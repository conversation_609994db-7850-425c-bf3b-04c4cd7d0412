{"timestamp": "2025-07-31T02:34:39.439Z", "totalTests": 18, "totalPassed": 15, "passRate": "83.3", "testResults": {"vestingTests": {"passed": 5, "total": 6, "details": ["✅ 轮次信息查询: 通过", "❌ 解锁进度查询: 错误 - VM Exception while processing transaction: reverted with reason string 'Price oracle failed'", "✅ 价格历史查询: 通过", "✅ 紧急签名者管理: 通过", "✅ 暂停功能: 通过"]}, "aggregatorTests": {"passed": 5, "total": 6, "details": ["✅ 聚合器状态查询: 通过", "✅ 价格源添加: 通过", "✅ 价格源查询: 通过", "❌ 紧急模式: 错误 - VM Exception while processing transaction: reverted with reason string 'Insufficient sources'", "✅ 暂停功能: 通过"]}, "integrationTests": {"passed": 5, "total": 6, "details": ["✅ 权限控制: 通过", "✅ 时间锁验证: 通过", "✅ 金额限制验证: 通过", "✅ 价格偏差检测: 通过", "❌ Vesting查询Gas效率: 错误 - VM Exception while processing transaction: reverted with reason string 'Price oracle failed'", "✅ 聚合器查询Gas效率: 通过"]}}, "summary": {"allPassed": false, "readyForDeployment": false}}