# ===========================================
# SocioMint 安全环境变量配置模板
# ===========================================
# 
# ⚠️  重要安全说明：
# 1. 复制此文件为 .env.local
# 2. 填入您的真实配置信息
# 3. 绝不要将包含真实私钥的 .env.local 提交到代码仓库
# 4. 私钥信息极其重要，请妥善保管
# 5. 建议使用硬件钱包或多签钱包管理大额资金
#
# ===========================================

# ===========================================
# 🔑 钱包配置（极其重要！）
# ===========================================

# 主部署钱包（当前拥有5亿HAOX代币的钱包）
DEPLOYER_PRIVATE_KEY=请填入您的部署者钱包私钥_64位十六进制字符串
DEPLOYER_WALLET_ADDRESS=请填入您的部署者钱包地址_0x开头

# 项目钱包（用于接收项目资金）
PROJECT_WALLET_ADDRESS=请填入您的项目钱包地址_0x开头
PROJECT_WALLET_PRIVATE_KEY=请填入您的项目钱包私钥_64位十六进制字符串

# 社区钱包（用于社区治理）
COMMUNITY_WALLET_ADDRESS=请填入您的社区钱包地址_0x开头
COMMUNITY_WALLET_PRIVATE_KEY=请填入您的社区钱包私钥_64位十六进制字符串

# 测试钱包（用于功能测试）
TEST_WALLET_ADDRESS=请填入您的测试钱包地址_0x开头
TEST_WALLET_PRIVATE_KEY=请填入您的测试钱包私钥_64位十六进制字符串

# ===========================================
# 🌐 区块链网络配置
# ===========================================
NEXT_PUBLIC_CHAIN_ID=97
NEXT_PUBLIC_NETWORK_NAME="BSC Testnet"
NEXT_PUBLIC_BSC_RPC_URL="https://bsc-testnet.public.blastapi.io"
NEXT_PUBLIC_BSC_EXPLORER_URL="https://testnet.bscscan.com"

# ===========================================
# 📄 HAOX 智能合约地址 V2 (BSC测试网)
# ===========================================

# 正式版合约地址（24小时时间锁）
NEXT_PUBLIC_HAOX_TOKEN_ADDRESS=******************************************
NEXT_PUBLIC_HAOX_PRESALE_ADDRESS=******************************************
NEXT_PUBLIC_HAOX_INVITATION_ADDRESS=******************************************
NEXT_PUBLIC_HAOX_PRICE_ORACLE_ADDRESS=******************************************
NEXT_PUBLIC_HAOX_VESTING_ADDRESS=******************************************

# 测试版合约地址（5分钟时间锁，用于快速测试）
NEXT_PUBLIC_HAOX_TOKEN_TEST_ADDRESS=0x1417865851b114bEC19976f7a9911822D949Ce3b

# 兼容性地址
NEXT_PUBLIC_HAOX_CONTRACT_ADDRESS_BSC=******************************************
NEXT_PUBLIC_HAOX_CONTRACT_ADDRESS=******************************************

# ===========================================
# 💰 代币转移配置
# ===========================================
PRESALE_TOKEN_ALLOCATION=200000000000000000000000000
INVITATION_TOKEN_ALLOCATION=300000000000000000000000000
PRESALE_CONTRACT_ADDRESS=******************************************
INVITATION_CONTRACT_ADDRESS=******************************************

# ===========================================
# ⛽ Gas 配置
# ===========================================
DEFAULT_GAS_LIMIT=500000
DEFAULT_GAS_PRICE=10000000000

# ===========================================
# 🔧 开发配置（兼容性保持）
# ===========================================
PRIVATE_KEY=请填入开发用私钥_与DEPLOYER_PRIVATE_KEY相同
WALLET_MASTER_SEED=请填入钱包种子_用于开发环境

# ===========================================
# 📝 填写示例（请替换为真实信息）
# ===========================================
# 
# DEPLOYER_PRIVATE_KEY=1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef
# DEPLOYER_WALLET_ADDRESS=******************************************
# PROJECT_WALLET_ADDRESS=******************************************
# PROJECT_WALLET_PRIVATE_KEY=abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890
#
# ⚠️  以上仅为格式示例，请使用您的真实钱包信息！
# ===========================================
