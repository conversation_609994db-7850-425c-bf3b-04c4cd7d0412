# 合约版本分析和清理计划

## 📋 当前合约文件分析

### 核心合约（需要保留的7个）✅
1. **HAOXTokenV2.sol** - ERC20代币合约 ✅
2. **HAOXPresaleV2.sol** - 预售逻辑合约 ✅
3. **HAOXInvitationV2.sol** - 邀请奖励合约 ✅
4. **HAOXVestingV2Minimal.sol** - 精简版解锁合约 ✅
5. **HAOXVestingV2Ultra.sol** - 超精简版解锁合约 ✅
6. **HAOXPriceOracleV2.sol** - 价格预言机合约 ✅
7. **HAOXPriceAggregatorMinimal.sol** - 精简版价格聚合器 ✅

### 过时/重复合约（需要清理）❌
1. **HAOXTokenV2Test.sol** - 测试版本，应移除
2. **HAOXVestingV2.sol** - 原始版本，已被Minimal版本替代
3. **HAOXVestingV2Fixed.sol** - 修复版本，已被Minimal版本替代
4. **HAOXVestingV2FixedSecure.sol** - 安全版本，已被Minimal版本替代
5. **HAOXPriceAggregatorV2.sol** - 完整版本，已被Minimal版本替代

## 🔄 版本演进历史

### HAOXVesting合约演进路径：
```
HAOXVestingV2.sol (原始版本)
    ↓
HAOXVestingV2Fixed.sol (修复版本)
    ↓
HAOXVestingV2FixedSecure.sol (安全加固版本)
    ↓
HAOXVestingV2Minimal.sol (精简优化版本) ← 最终版本
    ↓
HAOXVestingV2Ultra.sol (超精简版本) ← 最终版本
```

### HAOXPriceAggregator合约演进路径：
```
HAOXPriceAggregatorV2.sol (完整版本)
    ↓
HAOXPriceAggregatorMinimal.sol (精简版本) ← 最终版本
```

## 📊 成本优化对比

| 合约类型 | 原版本 | 最终版本 | 成本节省 |
|----------|--------|----------|----------|
| Vesting | HAOXVestingV2FixedSecure | HAOXVestingV2Minimal | 45% |
| Vesting Ultra | - | HAOXVestingV2Ultra | 更多节省 |
| Price Aggregator | HAOXPriceAggregatorV2 | HAOXPriceAggregatorMinimal | 43% |

## 🗂️ 清理操作计划

### 第1步：创建归档目录
```bash
mkdir -p contracts/archive/deprecated-versions
```

### 第2步：移动过时合约到归档
- HAOXTokenV2Test.sol → archive/deprecated-versions/
- HAOXVestingV2.sol → archive/deprecated-versions/
- HAOXVestingV2Fixed.sol → archive/deprecated-versions/
- HAOXVestingV2FixedSecure.sol → archive/deprecated-versions/
- HAOXPriceAggregatorV2.sol → archive/deprecated-versions/

### 第3步：更新部署脚本
- 确保所有部署脚本引用正确的最终版本
- 移除对过时合约的引用

### 第4步：验证依赖关系
- 检查测试文件中的引用
- 检查前端代码中的ABI引用
- 更新文档中的合约列表

## ✅ 最终合约清单

部署到生产环境的7个核心合约：

1. **HAOXTokenV2.sol** - 代币合约
2. **HAOXPresaleV2.sol** - 预售合约
3. **HAOXInvitationV2.sol** - 邀请合约
4. **HAOXVestingV2Minimal.sol** - 精简解锁合约（推荐）
5. **HAOXVestingV2Ultra.sol** - 超精简解锁合约（备选）
6. **HAOXPriceOracleV2.sol** - 价格预言机
7. **HAOXPriceAggregatorMinimal.sol** - 价格聚合器

## 🔍 清理后的好处

1. **降低混淆** - 移除重复版本，明确最终版本
2. **减少维护成本** - 只需维护7个核心合约
3. **提高安全性** - 避免部署错误版本的风险
4. **优化成本** - 使用最优化的合约版本
5. **简化测试** - 专注于最终版本的测试

## ⚠️ 注意事项

1. **备份重要** - 所有移动的文件都保留在archive目录
2. **脚本更新** - 确保部署脚本指向正确版本
3. **测试验证** - 清理后重新运行所有测试
4. **文档同步** - 更新所有相关文档
