openapi: 3.0.3
info:
  title: SocioMint API
  description: |
    SocioMint 是一个基于区块链的社交挖矿平台 API。
    
    ## 认证
    使用 Telegram 认证获取 JWT Token，然后通过 Cookie 或 Authorization Header 传递。
    
    ## 限流
    - 认证端点: 10 次/分钟
    - 一般 API: 100 次/分钟
    - 敏感操作: 3 次/分钟
    
    ## 错误处理
    所有错误响应都包含标准化的错误信息和错误代码。
  version: 1.0.0
  contact:
    name: SocioMint API Support
    email: <EMAIL>
    url: https://sociomint.app/support
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://sociomint.app/api
    description: 生产环境
  - url: https://staging.sociomint.app/api
    description: 测试环境
  - url: http://localhost:3000/api
    description: 开发环境

paths:
  /health:
    get:
      tags:
        - System
      summary: 系统健康检查
      description: 检查系统各组件的健康状态
      operationId: getHealth
      responses:
        '200':
          description: 系统健康
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthResponse'
        '503':
          description: 系统不健康
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /auth/telegram:
    post:
      tags:
        - Authentication
      summary: Telegram 用户认证
      description: 使用 Telegram 认证数据进行用户登录
      operationId: telegramAuth
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TelegramAuthRequest'
      responses:
        '200':
          description: 认证成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '401':
          description: 认证失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /auth/verify:
    get:
      tags:
        - Authentication
      summary: 验证认证状态
      description: 验证当前用户的认证状态
      operationId: verifyAuth
      security:
        - cookieAuth: []
        - bearerAuth: []
      responses:
        '200':
          description: 认证有效
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserResponse'
        '401':
          description: 认证无效
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /user/profile:
    get:
      tags:
        - User
      summary: 获取用户资料
      description: 获取当前用户的详细资料
      operationId: getUserProfile
      security:
        - cookieAuth: []
        - bearerAuth: []
      responses:
        '200':
          description: 用户资料
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfileResponse'
        '401':
          description: 未授权
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    put:
      tags:
        - User
      summary: 更新用户资料
      description: 更新当前用户的资料信息
      operationId: updateUserProfile
      security:
        - cookieAuth: []
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateProfileRequest'
      responses:
        '200':
          description: 更新成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfileResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /wallet/balance:
    get:
      tags:
        - Wallet
      summary: 获取钱包余额
      description: 获取用户的钱包余额信息
      operationId: getWalletBalance
      security:
        - cookieAuth: []
        - bearerAuth: []
      responses:
        '200':
          description: 钱包余额
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WalletBalanceResponse'
        '401':
          description: 未授权
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /wallet/transactions:
    get:
      tags:
        - Wallet
      summary: 获取交易记录
      description: 获取用户的交易历史记录
      operationId: getTransactions
      security:
        - cookieAuth: []
        - bearerAuth: []
      parameters:
        - name: page
          in: query
          description: 页码
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          description: 每页数量
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: type
          in: query
          description: 交易类型
          schema:
            type: string
            enum: [deposit, withdraw, transfer, trade]
        - name: status
          in: query
          description: 交易状态
          schema:
            type: string
            enum: [pending, completed, failed]
      responses:
        '200':
          description: 交易记录
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TransactionsResponse'

  /price-data:
    get:
      tags:
        - Market
      summary: 获取价格数据
      description: 获取代币的实时价格数据
      operationId: getPriceData
      parameters:
        - name: symbol
          in: query
          description: 交易对符号
          schema:
            type: string
            example: "HAOX/BNB"
        - name: interval
          in: query
          description: 时间间隔
          schema:
            type: string
            enum: [1m, 5m, 1h, 1d]
            default: "1h"
      responses:
        '200':
          description: 价格数据
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PriceDataResponse'

    post:
      tags:
        - Market
      summary: 设置价格预警
      description: 为指定代币设置价格预警
      operationId: setPriceAlert
      security:
        - cookieAuth: []
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PriceAlertRequest'
      responses:
        '200':
          description: 预警设置成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PriceAlertResponse'

components:
  securitySchemes:
    cookieAuth:
      type: apiKey
      in: cookie
      name: telegram-auth-token
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    # 基础响应结构
    BaseResponse:
      type: object
      required:
        - success
        - timestamp
      properties:
        success:
          type: boolean
        timestamp:
          type: string
          format: date-time

    SuccessResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          required:
            - data
          properties:
            data:
              type: object

    ErrorResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          required:
            - error
          properties:
            success:
              type: boolean
              example: false
            error:
              type: object
              required:
                - code
                - message
              properties:
                code:
                  type: string
                  example: "VALIDATION_ERROR"
                message:
                  type: string
                  example: "请求参数错误"
                details:
                  type: object
            requestId:
              type: string
              example: "req_123456789"

    # 分页结构
    Pagination:
      type: object
      required:
        - page
        - limit
        - total
        - totalPages
        - hasNext
        - hasPrev
      properties:
        page:
          type: integer
          example: 1
        limit:
          type: integer
          example: 20
        total:
          type: integer
          example: 100
        totalPages:
          type: integer
          example: 5
        hasNext:
          type: boolean
          example: true
        hasPrev:
          type: boolean
          example: false

    # 认证相关
    TelegramAuthRequest:
      type: object
      required:
        - id
        - first_name
        - auth_date
        - hash
      properties:
        id:
          type: integer
          format: int64
          example: 123456789
        first_name:
          type: string
          example: "John"
        last_name:
          type: string
          example: "Doe"
        username:
          type: string
          example: "johndoe"
        photo_url:
          type: string
          format: uri
          example: "https://t.me/i/userpic/320/johndoe.jpg"
        auth_date:
          type: integer
          format: int64
          example: 1640995200
        hash:
          type: string
          example: "a1b2c3d4e5f6..."

    AuthResponse:
      allOf:
        - $ref: '#/components/schemas/SuccessResponse'
        - type: object
          properties:
            data:
              type: object
              required:
                - id
                - firstName
              properties:
                id:
                  type: integer
                  format: int64
                  example: 123456789
                username:
                  type: string
                  example: "johndoe"
                firstName:
                  type: string
                  example: "John"
                lastName:
                  type: string
                  example: "Doe"
                photoUrl:
                  type: string
                  format: uri
                  example: "https://t.me/i/userpic/320/johndoe.jpg"

    # 用户相关
    UserResponse:
      allOf:
        - $ref: '#/components/schemas/SuccessResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/User'

    UserProfileResponse:
      allOf:
        - $ref: '#/components/schemas/SuccessResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/UserProfile'

    User:
      type: object
      required:
        - id
        - firstName
      properties:
        id:
          type: integer
          format: int64
          example: 123456789
        username:
          type: string
          example: "johndoe"
        firstName:
          type: string
          example: "John"
        lastName:
          type: string
          example: "Doe"
        photoUrl:
          type: string
          format: uri

    UserProfile:
      allOf:
        - $ref: '#/components/schemas/User'
        - type: object
          properties:
            email:
              type: string
              format: email
              example: "<EMAIL>"
            createdAt:
              type: string
              format: date-time
            updatedAt:
              type: string
              format: date-time

    UpdateProfileRequest:
      type: object
      properties:
        firstName:
          type: string
          example: "John"
        lastName:
          type: string
          example: "Doe"
        email:
          type: string
          format: email
          example: "<EMAIL>"

    # 钱包相关
    WalletBalanceResponse:
      allOf:
        - $ref: '#/components/schemas/SuccessResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/WalletBalance'

    WalletBalance:
      type: object
      required:
        - haoxBalance
        - bnbBalance
        - usdValue
        - lastUpdated
      properties:
        haoxBalance:
          $ref: '#/components/schemas/TokenBalance'
        bnbBalance:
          $ref: '#/components/schemas/TokenBalance'
        usdValue:
          type: object
          properties:
            haox:
              type: number
              format: float
              example: 246.91
            bnb:
              type: number
              format: float
              example: 108.64
            total:
              type: number
              format: float
              example: 355.55
        lastUpdated:
          type: string
          format: date-time

    TokenBalance:
      type: object
      required:
        - available
        - locked
        - total
      properties:
        available:
          type: string
          example: "1234.56"
        locked:
          type: string
          example: "100.00"
        total:
          type: string
          example: "1334.56"

    # 交易相关
    TransactionsResponse:
      allOf:
        - $ref: '#/components/schemas/SuccessResponse'
        - type: object
          properties:
            data:
              type: object
              required:
                - items
                - pagination
              properties:
                items:
                  type: array
                  items:
                    $ref: '#/components/schemas/Transaction'
                pagination:
                  $ref: '#/components/schemas/Pagination'

    Transaction:
      type: object
      required:
        - id
        - type
        - amount
        - currency
        - status
        - createdAt
      properties:
        id:
          type: string
          example: "tx_123456789"
        type:
          type: string
          enum: [deposit, withdraw, transfer, trade]
          example: "deposit"
        amount:
          type: string
          example: "100.00"
        currency:
          type: string
          example: "HAOX"
        status:
          type: string
          enum: [pending, completed, failed]
          example: "completed"
        hash:
          type: string
          example: "0x1234567890abcdef..."
        createdAt:
          type: string
          format: date-time
        completedAt:
          type: string
          format: date-time

    # 价格数据相关
    PriceDataResponse:
      allOf:
        - $ref: '#/components/schemas/SuccessResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/PriceData'

    PriceData:
      type: object
      required:
        - symbol
        - price
        - lastUpdated
      properties:
        symbol:
          type: string
          example: "HAOX/BNB"
        price:
          type: string
          example: "0.001234"
        change24h:
          type: string
          example: "+5.67%"
        volume24h:
          type: string
          example: "123456.78"
        high24h:
          type: string
          example: "0.001300"
        low24h:
          type: string
          example: "0.001100"
        lastUpdated:
          type: string
          format: date-time

    PriceAlertRequest:
      type: object
      required:
        - symbol
        - targetPrice
        - alertType
      properties:
        symbol:
          type: string
          example: "HAOX/BNB"
        targetPrice:
          type: string
          example: "0.002000"
        alertType:
          type: string
          enum: [above, below]
          example: "above"

    PriceAlertResponse:
      allOf:
        - $ref: '#/components/schemas/SuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                symbol:
                  type: string
                  example: "HAOX/BNB"
                targetPrice:
                  type: string
                  example: "0.002000"
                alertType:
                  type: string
                  example: "above"
                createdAt:
                  type: string
                  format: date-time

    # 系统健康检查
    HealthResponse:
      allOf:
        - $ref: '#/components/schemas/SuccessResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/HealthData'

    HealthData:
      type: object
      required:
        - status
        - timestamp
        - version
        - environment
        - responseTime
      properties:
        status:
          type: string
          enum: [healthy, unhealthy, degraded]
          example: "healthy"
        timestamp:
          type: string
          format: date-time
        uptime:
          type: number
          example: 86400
        version:
          type: string
          example: "1.0.0"
        environment:
          type: string
          example: "production"
        responseTime:
          type: number
          example: 45
        checks:
          type: object
          properties:
            database:
              $ref: '#/components/schemas/HealthCheck'
            blockchain:
              $ref: '#/components/schemas/HealthCheck'
            supabase:
              $ref: '#/components/schemas/HealthCheck'

    HealthCheck:
      type: object
      required:
        - status
      properties:
        status:
          type: string
          enum: [healthy, unhealthy, degraded]
          example: "healthy"
        responseTime:
          type: number
          example: 12
        error:
          type: string
        details:
          type: object

tags:
  - name: System
    description: 系统相关接口
  - name: Authentication
    description: 认证相关接口
  - name: User
    description: 用户相关接口
  - name: Wallet
    description: 钱包相关接口
  - name: Market
    description: 市场数据接口
