# Social Bet 功能需求文档 (PRD)

## 一、产品概述

### 1.1 功能定位
**Social Bet** 是一个基于Telegram的社交预测工具，将日常争论转化为有趣的赌约，通过HAOX代币创造真实的价值流转。采用链下托管机制，确保低成本、高效率的用户体验。

### 1.2 核心价值
- **社交属性**：将群聊争论变成财富机会
- **简单易用**：标准化模板，三选项设计
- **公平透明**：DAO裁定机制，全程可追溯，裁定信息公开
- **低成本高效**：链下实现，极低Gas费用
- **病毒传播**：转发激励，快速裂变
- **安全可靠**：完善的申诉机制和责任分担体系

### 1.3 目标用户
- Telegram活跃用户
- 加密货币爱好者
- 体育/电竞粉丝
- 预测市场参与者

## 二、功能架构

### 2.1 核心功能模块

```
HAOX Social Bet
├── 赌约系统
│   ├── 1v1模式（快速对赌）
│   ├── 1vN模式（多人预测）
│   └── 标准化模板（80%）+ 自定义（20%）
├── 裁定系统
│   ├── 三轮DAO裁定
│   ├── 信誉积分系统
│   └── 奖惩机制
├── 激励系统
│   ├── 转发奖励
│   ├── 裁判奖励
│   └── 排行榜奖励
└── 数据系统
    ├── 实时动态
    ├── 排行榜单
    └── 个人中心
```

## 三、详细功能设计

### 3.1 赌约创建与参与

#### 3.1.1 赌约模式

**1v1模式**
```javascript
创建流程：
1. 发起：@bot bet 1000 → 支付1000 HAOX + 1%手续费
2. 接受：任意用户接受 → 可选追加赌注（仅一次机会）
3. 确认：发起者选择匹配追加或放弃
4. 生效：双方赌注相等则生效，否则退款（扣除手续费）

示例：
- A发起1000 HAOX
- B接受并追加5000 HAOX
- A选择匹配5000 HAOX
- 基础奖池：12000 HAOX
```

**1vN模式**
```javascript
创建要求：
- 最低投入：10,000 HAOX
- 发起费用：2%
- 转发奖励：10-50%（可选，从最终奖池扣除）
- 选项数量：2-3个
- 有效时间：最长72小时

费用计算：
投入10万HAOX = 支付102,000 HAOX（含2%手续费）
设置20%转发奖励 = 从最终奖池扣除，无需预付
```

#### 3.1.2 标准化模板系统

```javascript
模板类型：
1. 体育竞技
   模板："{队伍A} vs {队伍B} - {日期}"
   选项：[A胜] [B胜] [平局/取消]
   
2. 价格预测
   模板："{币种}在{时间}前达到{价格}"
   选项：[达到] [未达到] [无效]
   
3. 事件预测
   模板："{事件}在{时间}前发生"
   选项：[发生] [未发生] [无法判定]
```

### 3.2 DAO裁定系统

#### 3.2.1 认证等级

```javascript
持币认证等级（系统自动认证，每小时更新）：
┌─────────┬──────────────┬────────────┬─────────────┬──────────────┐
│ 等级    │ 持币要求      │ 手续费折扣 │ 每日裁定次数 │ 裁定轮次权限  │
├─────────┼──────────────┼────────────┼─────────────┼──────────────┤
│ X1      │ 10 HAOX      │ 0%         │ 1次         │ 第一轮       │
│ X2      │ 1万 HAOX     │ 20%        │ 5次         │ 第一、二轮   │
│ X3      │ 100万 HAOX   │ 50%        │ 20次        │ 第一、二轮   │
│ X4      │ 1000万 HAOX  │ 70%        │ 50次        │ 第二、三轮   │
│ X5      │ 1亿 HAOX     │ 90%        │ 无限制      │ 第二、三轮   │
└─────────┴──────────────┴────────────┴─────────────┴──────────────┘

注：裁定次数每日UTC 0点重置，认证等级每小时根据钱包余额自动更新
```

#### 3.2.2 三轮裁定机制

```javascript
裁定流程：
第一轮：大众评审
- 参与要求：所有认证用户（X1-X5）
- 通过标准：11/20票（有11位用户投票即通过）
- 时限：24小时
- 信息公开：显示裁定用户的Telegram ID、平台分数、参与次数、认证等级

第二轮：专业评审
- 参与要求：X1、X2、X3认证用户
- 通过标准：6/10票
- 时限：12小时
- 信息公开：同第一轮

第三轮：终审裁定
- 参与要求：X4、X5认证用户
- 通过标准：3/5票
- 时限：6小时
- 信息公开：同第一轮

申诉机制：
- 申诉时限：裁定结果公布后24小时内
- 申诉质押：总奖池金额的10%
- 申诉流程：进入终审DAO仲裁（复用第三轮机制）
- 责任分担：申诉成功后，原裁定人员承担60%，DAO基金池承担40%
```

#### 3.2.3 信誉积分系统

```javascript
积分规则：
- 初始分数：90分
- 裁定门槛：70分
- 正确裁定：+1分
- 错误裁定：-5分
- 违规操作：-10分
- 连续正确奖励：
  - 5连胜：+5分
  - 10连胜：+15分
  - 20连胜：+35分

权益等级：
- 新手裁判(70-99分)：基础权益
- 资深裁判(100-199分)：10%手续费折扣
- 专家裁判(200-499分)：30%折扣+优先分配
- 大师裁判(500-999分)：50%折扣+跳过第一轮
- 传奇裁判(1000+分)：80%折扣+最终仲裁权
```

### 3.3 费用与分配

#### 3.3.1 手续费结构

```javascript
5%手续费分配：
├── 3% → 裁判奖励池（按参与轮次和准确率分配，赌约结束后立即发放）
├── 1% → 平台运营费用
├── 0.5% → DAO基金池（用于申诉赔偿等）
├── 0.3% → 系统维护基金
└── 0.2% → 争议处理基金
```

#### 3.3.2 奖池分配规则

```javascript
1v1模式：
总奖池 = 双方投注总和
手续费 = 总奖池 × 5%
转发奖励 = 总奖池 × 转发奖励比例（0-5%）
赢家获得 = 总奖池 - 手续费 - 转发奖励

1vN模式：
总奖池 = 所有投注总和
手续费 = 总奖池 × 5%
转发奖励 = 总奖池 × 转发奖励比例（10-50%）
赢家瓜分 = 总奖池 - 手续费 - 转发奖励
个人获得 = 赢家奖池 × (个人投注/赢方总投注)
```

### 3.4 页面设计

#### 3.4.1 主页面布局

```
┌─────────────────────────────────────────┐
│         🔥 热门赌约轮播（最近24小时）    │
│   [最高奖池] [最多参与] [即将结束]      │
├─────────────────────────────────────────┤
│  📊 实时动态  │  🏆 排行榜  │ 🎯 我的   │
├───────────────┼─────────────┼───────────┤
│               │             │           │
│ • 24h赌约流水 │ • 胜率榜    │ • 进行中  │
│ • 热度排序    │ • 收益榜    │ • 历史记录│
│ • 金额筛选    │ • 活跃榜    │ • 数据统计│
│ • 裁定记录    │ • 裁判榜    │ • 认证等级│
│               │             │           │
└───────────────┴─────────────┴───────────┘
│      [发起1v1赌约]  [发起1vN赌约]      │
└─────────────────────────────────────────┘

注：主页面仅显示最近24小时的赌约记录，个人页面可查看完整历史
```

#### 3.4.2 赌约卡片展示

```
┌─────────────────────────────────┐
│ ⚔️ 社交赌约 #12345              │
│ 🔥 热度：8,234人围观            │
├─────────────────────────────────┤
│ 📝 TES vs AL - LPL夏季赛        │
│                                 │
│ 💰 基础奖池：12,000 HAOX        │
│ 🎯 围观奖池：88,000 HAOX        │
│ ⏰ 剩余时间：02:45:30           │
├─────────────────────────────────┤
│ 选项投注分布：                  │
│ TES胜：65% ████████             │
│ AL胜： 30% ████                 │
│ 取消： 5%  █                    │
├─────────────────────────────────┤
│ [参与投注] [分享赚钱] [查看详情]│
└─────────────────────────────────┘
```

#### 3.4.3 排行榜设计

```javascript
榜单类型：
1. 胜率榜（月榜/总榜）
   - 最少参与20场
   - 显示胜率、战绩、连胜
   
2. 收益榜（月榜/总榜）
   - 显示总收益、收益率
   - 最大单笔收益
   
3. 活跃榜
   - 参与次数
   - 发起次数
   - 裁定次数

称号系统：
月榜：
🥇 月度预言家 / 财富之王
🥈 智慧贤者 / 黄金猎手
🥉 洞察大师 / 幸运之星

总榜：
🏆 永恒先知 / 赌神
🏆 命运之眼 / 财富传奇
🏆 时空智者 / 幸运天选
```

## 四、技术实现

### 4.1 技术架构

```
前端：
├── Telegram Bot (Node.js)
├── Mini App (Next.js + TypeScript)
└── 管理后台 (React)

后端：
├── API服务 (Next.js API Routes)
├── 数据库 (Supabase/PostgreSQL)
├── 缓存 (Redis)
├── 定时任务 (Node.js Cron)
└── 自动结算服务

区块链交互：
├── HAOX代币合约 (现有)
├── 托管钱包管理
└── 批量转账服务 (Ethers.js)
```

### 4.2 核心系统模块

```typescript
主要模块：
1. 赌约管理系统 - 创建、参与、状态管理
2. 裁定投票系统 - 三轮投票、结果统计
3. 信誉积分系统 - 积分计算、等级管理
4. 自动结算系统 - 奖励计算、批量转账
5. 托管钱包服务 - 资金安全、自动分配
```

### 4.3 数据库设计

#### 4.3.1 核心表结构

```sql
-- 赌约主表
CREATE TABLE social_bets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    creator_id UUID REFERENCES users(id) NOT NULL,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    bet_type VARCHAR(10) CHECK (bet_type IN ('1v1', '1vN')),

    -- 赌约配置
    options JSONB NOT NULL, -- 选项配置 ["选项1", "选项2", "选项3"]
    min_amount DECIMAL(20,8) NOT NULL,
    max_amount DECIMAL(20,8),
    forward_reward_rate DECIMAL(5,2) DEFAULT 0, -- 转发奖励比例

    -- 状态管理
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'judging', 'judged', 'settled', 'cancelled')),
    winning_option INTEGER, -- 获胜选项

    -- 时间管理
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    judging_started_at TIMESTAMP WITH TIME ZONE,
    settled_at TIMESTAMP WITH TIME ZONE,

    -- 资金统计
    total_pool DECIMAL(20,8) DEFAULT 0,
    fee_amount DECIMAL(20,8) DEFAULT 0,
    forward_reward_amount DECIMAL(20,8) DEFAULT 0,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 投注记录表
CREATE TABLE bet_participants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bet_id UUID REFERENCES social_bets(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) NOT NULL,

    option_choice INTEGER NOT NULL, -- 选择的选项 (0, 1, 2)
    amount DECIMAL(20,8) NOT NULL,

    -- 交易记录
    deposit_tx_hash VARCHAR(66), -- 投注转账哈希
    refund_tx_hash VARCHAR(66), -- 退款转账哈希（如果有）
    reward_tx_hash VARCHAR(66), -- 奖励转账哈希（如果获胜）

    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(bet_id, user_id) -- 每个用户每个赌约只能参与一次
);

-- 裁定投票表
CREATE TABLE bet_judgments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bet_id UUID REFERENCES social_bets(id) ON DELETE CASCADE,
    judge_id UUID REFERENCES users(id) NOT NULL,

    round_number INTEGER NOT NULL CHECK (round_number IN (1, 2, 3)),
    vote_option INTEGER NOT NULL, -- 投票选项

    -- 裁判资格
    reputation_score INTEGER NOT NULL, -- 投票时的信誉分数
    certification_level VARCHAR(10) NOT NULL, -- X1-X5认证等级

    -- 奖励记录
    reward_amount DECIMAL(20,8) DEFAULT 0,
    reward_tx_hash VARCHAR(66),

    voted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(bet_id, judge_id, round_number) -- 每轮每个裁判只能投票一次
);

-- 用户信誉积分表
CREATE TABLE user_reputation (
    user_id UUID REFERENCES users(id) PRIMARY KEY,

    -- 积分统计
    current_score INTEGER DEFAULT 90 NOT NULL,
    total_judgments INTEGER DEFAULT 0,
    correct_judgments INTEGER DEFAULT 0,
    consecutive_correct INTEGER DEFAULT 0,
    max_consecutive_correct INTEGER DEFAULT 0,

    -- 等级信息
    reputation_level VARCHAR(20) DEFAULT 'newbie' CHECK (
        reputation_level IN ('newbie', 'experienced', 'expert', 'master', 'legend')
    ),

    -- 认证等级（系统自动更新）
    certification_level VARCHAR(10) DEFAULT 'X1' CHECK (
        certification_level IN ('X1', 'X2', 'X3', 'X4', 'X5')
    ),
    haox_holdings DECIMAL(20,8) DEFAULT 0, -- 持币数量
    last_balance_check TIMESTAMP WITH TIME ZONE DEFAULT NOW(), -- 上次余额检查时间

    -- 裁定限制
    daily_judgments_used INTEGER DEFAULT 0, -- 当日已使用的裁定次数
    last_judgment_reset TIMESTAMP WITH TIME ZONE DEFAULT NOW(), -- 上次重置时间

    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 申诉记录表
CREATE TABLE bet_appeals (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bet_id UUID REFERENCES social_bets(id) ON DELETE CASCADE,
    appellant_id UUID REFERENCES users(id) NOT NULL,

    -- 申诉信息
    reason TEXT NOT NULL,
    evidence JSONB, -- 申诉证据
    stake_amount DECIMAL(20,8) NOT NULL, -- 质押金额（总奖池的10%）
    stake_tx_hash VARCHAR(66), -- 质押转账哈希

    -- 状态管理
    status VARCHAR(20) DEFAULT 'pending' CHECK (
        status IN ('pending', 'reviewing', 'approved', 'rejected', 'settled')
    ),

    -- 处理结果
    final_decision INTEGER, -- 最终裁定结果
    compensation_amount DECIMAL(20,8) DEFAULT 0, -- 赔偿金额

    -- 时间管理
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    reviewed_at TIMESTAMP WITH TIME ZONE,
    settled_at TIMESTAMP WITH TIME ZONE
);

-- 用户提现记录表
CREATE TABLE withdrawal_requests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) NOT NULL,

    amount DECIMAL(20,8) NOT NULL,
    gas_fee_level VARCHAR(10) DEFAULT 'standard' CHECK (
        gas_fee_level IN ('economy', 'standard', 'fast')
    ),
    estimated_gas_fee DECIMAL(20,8),
    actual_gas_fee DECIMAL(20,8),

    status VARCHAR(20) DEFAULT 'pending' CHECK (
        status IN ('pending', 'processing', 'completed', 'failed')
    ),

    tx_hash VARCHAR(66),
    error_message TEXT,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processed_at TIMESTAMP WITH TIME ZONE
);
```

### 4.4 API设计

#### 4.4.1 赌约管理API

```typescript
// 创建赌约
POST /api/social-bet/create
{
  "title": "TES vs AL - LPL夏季赛",
  "description": "2024年8月5日 TES对阵AL的比赛结果",
  "betType": "1vN",
  "options": ["TES胜", "AL胜", "取消/平局"],
  "minAmount": "1000",
  "maxAmount": "100000",
  "forwardRewardRate": 20, // 20%转发奖励
  "expiresAt": "2024-08-05T20:00:00Z"
}

// 参与赌约
POST /api/social-bet/{betId}/join
{
  "optionChoice": 0, // 选择选项0: TES胜
  "amount": "5000"
}

// 获取赌约详情
GET /api/social-bet/{betId}

// 获取赌约列表
GET /api/social-bet/list?status=active&page=1&limit=20
```

#### 4.4.2 裁定系统API

```typescript
// 提交裁定投票
POST /api/social-bet/{betId}/judge
{
  "round": 1,
  "voteOption": 0
}

// 获取裁定状态
GET /api/social-bet/{betId}/judgment-status

// 获取可裁定的赌约列表
GET /api/social-bet/judgeable?round=1
```

#### 4.4.3 用户信誉API

```typescript
// 获取用户信誉信息
GET /api/user/reputation

// 系统自动更新认证等级（每小时执行）
POST /api/system/update-certifications

// 检查用户裁定次数限制
GET /api/user/judgment-quota
```

#### 4.4.4 申诉系统API

```typescript
// 提交申诉
POST /api/social-bet/{betId}/appeal
{
  "reason": "裁定结果明显错误，证据如下...",
  "evidence": {
    "screenshots": ["url1", "url2"],
    "links": ["proof_link1"]
  }
}

// 获取申诉状态
GET /api/social-bet/{betId}/appeal-status

// 处理申诉（管理员）
POST /api/admin/appeals/{appealId}/process
{
  "decision": "approved", // approved | rejected
  "finalResult": 1
}
```

#### 4.4.5 提现系统API

```typescript
// 发起提现请求
POST /api/user/withdraw
{
  "amount": "1000",
  "gasFeeLevel": "standard" // economy | standard | fast
}

// 获取Gas费预估
GET /api/user/estimate-gas?amount=1000&level=standard

// 获取提现记录
GET /api/user/withdrawals?page=1&limit=20
```

### 4.5 自动化流程

#### 4.5.1 赌约状态自动流转

```typescript
// 定时任务：检查赌约状态
async function checkBetStatus() {
  // 1. 检查过期的赌约，启动裁定流程
  const expiredBets = await getExpiredActiveBets();
  for (const bet of expiredBets) {
    await startJudgmentProcess(bet.id);
  }

  // 2. 检查裁定超时的赌约
  const timeoutJudgments = await getTimeoutJudgments();
  for (const judgment of timeoutJudgments) {
    await handleJudgmentTimeout(judgment.betId, judgment.round);
  }

  // 3. 检查可结算的赌约
  const settleableBets = await getSettleableBets();
  for (const bet of settleableBets) {
    await autoSettleBet(bet.id);
  }

  // 4. 重置每日裁定次数（UTC 0点）
  await resetDailyJudgmentQuotas();
}

// 每分钟执行一次
setInterval(checkBetStatus, 60000);

// 每小时更新用户认证等级
setInterval(updateUserCertifications, 3600000);
```

#### 4.5.2 认证等级自动更新

```typescript
async function updateUserCertifications() {
  try {
    const users = await getAllActiveUsers();

    for (const user of users) {
      // 获取用户当前钱包余额
      const balance = await getHAOXBalance(user.walletAddress);

      // 计算新的认证等级
      const newLevel = calculateCertificationLevel(balance);

      // 更新数据库
      if (newLevel !== user.certificationLevel) {
        await updateUserCertification(user.id, newLevel, balance);
        console.log(`用户 ${user.id} 认证等级更新: ${user.certificationLevel} → ${newLevel}`);
      }
    }

    console.log('认证等级更新完成');
  } catch (error) {
    console.error('认证等级更新失败:', error);
  }
}

function calculateCertificationLevel(balance: string): string {
  const amount = parseFloat(balance);

  if (amount >= 100000000) return 'X5'; // 1亿
  if (amount >= 10000000) return 'X4';  // 1000万
  if (amount >= 1000000) return 'X3';   // 100万
  if (amount >= 10000) return 'X2';     // 1万
  return 'X1';                          // 10+
}
```

#### 4.5.2 自动结算流程

```typescript
async function autoSettleBet(betId: string) {
  try {
    // 1. 获取赌约和投注信息
    const bet = await getBetDetails(betId);
    const participants = await getBetParticipants(betId);

    // 2. 计算奖励分配
    const distribution = calculateRewardDistribution(bet, participants);

    // 3. 执行批量转账
    const txHashes = await batchTransferRewards(distribution);

    // 4. 更新数据库记录
    await updateSettlementRecords(betId, distribution, txHashes);

    // 5. 更新裁判信誉积分
    await updateJudgeReputations(betId, bet.winningOption);

    console.log(`赌约 ${betId} 自动结算完成`);

  } catch (error) {
    console.error(`赌约 ${betId} 结算失败:`, error);
    // 记录错误，等待人工处理
    await logSettlementError(betId, error);
  }
}
```

#### 4.5.3 托管钱包管理

```typescript
class EscrowWalletService {
  private wallet: ethers.Wallet;
  private haoxContract: ethers.Contract;
  private emergencyPaused: boolean = false;

  constructor() {
    // 私钥安全存储方案
    const privateKey = this.getSecurePrivateKey();
    this.wallet = new ethers.Wallet(privateKey);
    this.haoxContract = new ethers.Contract(HAOX_ADDRESS, HAOX_ABI, this.wallet);
  }

  // 安全获取私钥
  private getSecurePrivateKey(): string {
    // 方案A：环境变量 + 加密存储
    const encryptedKey = process.env.ESCROW_PRIVATE_KEY_ENCRYPTED;
    const decryptionKey = process.env.DECRYPTION_KEY;

    if (!encryptedKey || !decryptionKey) {
      throw new Error('私钥配置缺失');
    }

    // 解密私钥（使用AES加密）
    return this.decryptPrivateKey(encryptedKey, decryptionKey);
  }

  // 批量转账给获胜者
  async batchTransferRewards(distributions: RewardDistribution[]) {
    // 检查紧急暂停状态
    if (this.emergencyPaused) {
      throw new Error('系统处于紧急暂停状态');
    }

    // 验证转账总额
    const totalAmount = distributions.reduce((sum, dist) =>
      sum + parseFloat(dist.amount), 0
    );

    const balance = await this.checkBalance();
    if (parseFloat(balance) < totalAmount * 1.1) { // 保留10%缓冲
      throw new Error('托管钱包余额不足');
    }

    const txHashes: string[] = [];

    for (const dist of distributions) {
      try {
        // Gas费优化
        const gasPrice = await this.getOptimalGasPrice();

        const tx = await this.haoxContract.transfer(
          dist.userAddress,
          ethers.parseUnits(dist.amount, 18),
          { gasPrice }
        );

        await tx.wait();
        txHashes.push(tx.hash);

        console.log(`转账成功: ${dist.amount} HAOX → ${dist.userAddress}`);

        // 记录转账日志
        await this.logTransaction(dist, tx.hash);

      } catch (error) {
        console.error(`转账失败: ${dist.userAddress}`, error);
        await this.handleTransferError(dist, error);
        throw error;
      }
    }

    return txHashes;
  }

  // 检查钱包余额
  async checkBalance(): Promise<string> {
    const balance = await this.haoxContract.balanceOf(this.wallet.address);
    return ethers.formatUnits(balance, 18);
  }

  // 紧急暂停机制
  async emergencyPause(reason: string) {
    this.emergencyPaused = true;
    await this.notifyEmergencyPause(reason);
    console.log(`紧急暂停激活: ${reason}`);
  }

  // 恢复正常运行
  async resumeOperations() {
    this.emergencyPaused = false;
    console.log('系统恢复正常运行');
  }

  // 获取最优Gas价格
  private async getOptimalGasPrice(): Promise<bigint> {
    const provider = this.wallet.provider;
    const gasPrice = await provider.getFeeData();

    // 使用标准Gas价格
    return gasPrice.gasPrice || ethers.parseUnits('5', 'gwei');
  }
}
```

### 4.6 安全机制

#### 4.6.1 资金安全保障

```typescript
// 1. 多重验证机制
class SecurityValidator {
  // 验证用户投注资格
  async validateBetParticipation(userId: string, betId: string, amount: string) {
    // 检查是否已参与
    const existing = await checkExistingParticipation(userId, betId);
    if (existing) throw new Error('用户已参与此赌约');

    // 检查余额充足
    const balance = await getHAOXBalance(userId);
    if (parseFloat(balance) < parseFloat(amount)) {
      throw new Error('余额不足');
    }

    // 检查投注金额范围
    const bet = await getBetDetails(betId);
    if (parseFloat(amount) < parseFloat(bet.minAmount) ||
        parseFloat(amount) > parseFloat(bet.maxAmount)) {
      throw new Error('投注金额超出范围');
    }
  }

  // 验证裁判资格
  async validateJudgeEligibility(userId: string, betId: string, round: number) {
    const user = await getUserWithReputation(userId);

    // 检查认证等级权限
    const allowedLevels = this.getAllowedLevelsForRound(round);
    if (!allowedLevels.includes(user.certificationLevel)) {
      throw new Error('认证等级不足');
    }

    // 检查每日裁定次数限制
    const dailyQuota = this.getDailyQuotaForLevel(user.certificationLevel);
    if (user.dailyJudgmentsUsed >= dailyQuota && dailyQuota !== -1) {
      throw new Error('今日裁定次数已用完');
    }

    // 检查是否已在此轮投票
    const existingVote = await getExistingVote(userId, betId, round);
    if (existingVote) throw new Error('已在此轮投票');

    // 检查是否为赌约参与者（避免利益冲突）
    const isParticipant = await checkBetParticipation(userId, betId);
    if (isParticipant) throw new Error('赌约参与者不能担任裁判');
  }

  // 获取轮次允许的认证等级
  private getAllowedLevelsForRound(round: number): string[] {
    switch (round) {
      case 1: return ['X1', 'X2', 'X3', 'X4', 'X5'];
      case 2: return ['X1', 'X2', 'X3'];
      case 3: return ['X4', 'X5'];
      default: return [];
    }
  }

  // 获取等级的每日裁定配额
  private getDailyQuotaForLevel(level: string): number {
    const quotas = { X1: 1, X2: 5, X3: 20, X4: 50, X5: -1 }; // -1表示无限制
    return quotas[level] || 0;
  }
}

// 2. 托管钱包安全
class EscrowSecurity {
  // 钱包余额监控
  async monitorWalletBalance() {
    const balance = await this.escrowWallet.checkBalance();
    const requiredBalance = await calculateRequiredBalance();

    if (parseFloat(balance) < parseFloat(requiredBalance) * 1.1) {
      await sendLowBalanceAlert(balance, requiredBalance);
    }
  }

  // 异常交易检测
  async detectAnomalousTransactions() {
    const recentTxs = await getRecentTransactions(24); // 24小时内

    for (const tx of recentTxs) {
      // 检查大额转账
      if (parseFloat(tx.amount) > 1000000) {
        await flagLargeTransaction(tx);
      }

      // 检查频繁转账
      const userTxCount = await getUserTransactionCount(tx.userId, 1); // 1小时内
      if (userTxCount > 10) {
        await flagFrequentTransactions(tx.userId);
      }
    }
  }
}
```

#### 4.6.2 防作弊机制

```typescript
// 1. 投票操纵检测
class AntiManipulation {
  // 检测协调投票
  async detectCoordinatedVoting(betId: string, round: number) {
    const votes = await getRoundVotes(betId, round);

    // 检查投票时间聚集
    const timeWindows = groupVotesByTimeWindow(votes, 300); // 5分钟窗口
    for (const window of timeWindows) {
      if (window.votes.length > 5 && window.unanimity > 0.9) {
        await flagSuspiciousVoting(betId, round, window);
      }
    }

    // 检查IP地址聚集
    const ipGroups = groupVotesByIP(votes);
    for (const group of ipGroups) {
      if (group.votes.length > 3) {
        await flagIPClustering(betId, round, group);
      }
    }
  }

  // 检测刷分行为
  async detectReputationFarming(userId: string) {
    const recentJudgments = await getUserRecentJudgments(userId, 30); // 30天内

    // 检查判断频率
    if (recentJudgments.length > 100) {
      await flagHighFrequencyJudging(userId);
    }

    // 检查准确率异常
    const accuracy = calculateAccuracy(recentJudgments);
    if (accuracy > 0.95 && recentJudgments.length > 50) {
      await flagSuspiciousAccuracy(userId);
    }
  }
}
```

### 4.7 部署和运维

#### 4.7.1 环境配置

```bash
# 托管钱包安全配置
ESCROW_PRIVATE_KEY_ENCRYPTED=encrypted_private_key_string # 加密后的私钥
DECRYPTION_KEY=your_decryption_key # 解密密钥
ESCROW_WALLET_ADDRESS=0x... # 托管钱包地址（用于验证）

# 区块链配置
HAOX_CONTRACT_ADDRESS=0x... # HAOX代币合约地址
BSC_RPC_URL=https://bsc-dataseed.binance.org/
BSC_TESTNET_RPC_URL=https://data-seed-prebsc-1-s1.binance.org:8545/

# 数据库配置
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# 安全配置
JWT_SECRET=your-jwt-secret
RATE_LIMIT_WINDOW=60000 # 1分钟
RATE_LIMIT_MAX_REQUESTS=100
EMERGENCY_PAUSE_ENABLED=true

# 监控配置
ALERT_WEBHOOK_URL=https://hooks.slack.com/...
LOW_BALANCE_THRESHOLD=100000 # 10万HAOX
ABNORMAL_TX_THRESHOLD=1000000 # 100万HAOX异常交易阈值

# Gas费配置
GAS_PRICE_ECONOMY=********** # 3 Gwei
GAS_PRICE_STANDARD=********** # 5 Gwei
GAS_PRICE_FAST=*********** # 10 Gwei
```

#### 4.7.2 监控和告警

```typescript
// 系统健康监控
class SystemMonitor {
  async runHealthChecks() {
    const checks = [
      this.checkDatabaseConnection(),
      this.checkEscrowWalletBalance(),
      this.checkPendingSettlements(),
      this.checkAPIResponseTime(),
      this.checkPrivateKeySecurity(),
      this.checkEmergencyPauseStatus(),
    ];

    const results = await Promise.allSettled(checks);

    for (const [index, result] of results.entries()) {
      if (result.status === 'rejected') {
        await sendAlert(`健康检查失败: ${checks[index].name}`, result.reason);

        // 关键错误触发紧急暂停
        if (this.isCriticalError(checks[index].name)) {
          await this.triggerEmergencyPause(result.reason);
        }
      }
    }
  }

  // 检查私钥安全性
  async checkPrivateKeySecurity() {
    // 验证私钥配置是否正确
    const wallet = new EscrowWalletService();
    const address = await wallet.getAddress();

    if (address !== process.env.ESCROW_WALLET_ADDRESS) {
      throw new Error('私钥与预期地址不匹配');
    }
  }

  // 异常交易监控
  async monitorAbnormalTransactions() {
    const recentTxs = await getRecentTransactions(1); // 1小时内

    for (const tx of recentTxs) {
      // 检查大额转账
      if (parseFloat(tx.amount) > parseFloat(process.env.ABNORMAL_TX_THRESHOLD)) {
        await sendAlert(`检测到大额转账: ${tx.amount} HAOX`, tx);
      }

      // 检查频繁转账
      const userTxCount = await getUserTransactionCount(tx.userId, 1);
      if (userTxCount > 20) {
        await sendAlert(`用户频繁转账: ${tx.userId}`, { count: userTxCount });
      }
    }
  }

  // 业务指标监控
  async trackBusinessMetrics() {
    const metrics = {
      activeBets: await countActiveBets(),
      dailyVolume: await getDailyTradingVolume(),
      pendingJudgments: await countPendingJudgments(),
      averageSettlementTime: await getAverageSettlementTime(),
      escrowBalance: await getEscrowWalletBalance(),
      pendingAppeals: await countPendingAppeals(),
      dailyNewUsers: await countDailyNewUsers(),
    };

    // 发送到监控系统
    await sendMetrics(metrics);

    // 检查异常指标
    await this.checkAbnormalMetrics(metrics);
  }

  // 触发紧急暂停
  async triggerEmergencyPause(reason: string) {
    const escrowService = new EscrowWalletService();
    await escrowService.emergencyPause(reason);

    // 通知所有相关人员
    await sendCriticalAlert(`系统紧急暂停: ${reason}`);
  }
}

// 每5分钟执行健康检查
setInterval(() => new SystemMonitor().runHealthChecks(), 300000);

// 每小时执行异常交易监控
setInterval(() => new SystemMonitor().monitorAbnormalTransactions(), 3600000);
```

## 五、实施计划

### 5.1 开发阶段

**第一阶段（2周）：基础功能**
- 数据库表结构设计和创建
- 基础API端点实现
- 赌约创建和参与功能
- 简单的前端界面

**第二阶段（1周）：裁定系统**
- 三轮裁定逻辑实现
- 信誉积分系统
- 裁定界面开发

**第三阶段（1周）：自动化和优化**
- 自动结算系统
- 安全机制完善
- 性能优化和测试

### 5.2 测试计划

**单元测试**：覆盖所有核心业务逻辑
**集成测试**：测试API端点和数据库交互
**压力测试**：模拟高并发投注场景
**安全测试**：验证防作弊和资金安全机制

### 5.3 上线部署

**测试网部署**：先在BSC测试网验证功能
**小规模内测**：邀请少量用户参与测试
**逐步开放**：根据测试结果逐步扩大用户范围
**正式上线**：完整功能上线，开始正式运营
```
