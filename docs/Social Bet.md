# Social Bet 功能需求文档 (PRD)

## 一、产品概述

### 1.1 功能定位
**Social Bet** 是一个基于Telegram的社交预测工具，将日常争论转化为有趣的赌约，通过"福气"系统创造真实的价值流转。福气与HAOX代币1:1兑换，寓意"好运无限"，让用户在预测中积攒福气，收获好运。

### 1.2 核心价值
- **社交属性**：将群聊争论变成财富机会
- **简单易用**：标准化模板，三选项设计
- **公平透明**：DAO裁定机制，全程可追溯，裁定信息公开
- **福气满满**：创新的福气系统，瞬时结算，零Gas费用
- **病毒传播**：转发激励，快速裂变
- **好运无限**：福气与HAOX完美结合，寓意美好

### 1.3 目标用户
- Telegram活跃用户
- 加密货币爱好者
- 体育/电竞粉丝
- 预测市场参与者
- 寻求好运的用户群体

### 1.4 福气系统概述
**福气**是平台的核心虚拟资产，寓意"好运无限"：
- **1:1兑换**：1福气 = 1 HAOX，随时可兑换
- **瞬时操作**：福气转账无需区块链确认，秒级到账
- **零手续费**：福气系统内操作无Gas费用
- **安全存储**：HAOX代币安全存储在多签钱包
- **多元获取**：投注获胜、邀请好友、每日签到等多种方式获得福气

## 二、功能架构

### 2.1 核心功能模块

```
HAOX Social Bet
├── 赌约系统
│   ├── 1v1模式（快速对赌）
│   ├── 1vN模式（多人预测）
│   └── 标准化模板（80%）+ 自定义（20%）
├── 裁定系统
│   ├── 三轮DAO裁定
│   ├── 信誉积分系统
│   └── 奖惩机制
├── 激励系统
│   ├── 转发奖励
│   ├── 裁判奖励
│   └── 排行榜奖励
└── 数据系统
    ├── 实时动态
    ├── 排行榜单
    └── 个人中心
```

## 三、详细功能设计

### 3.1 赌约创建与参与

#### 3.1.1 赌约模式

**1v1模式**
```javascript
创建流程：
1. 发起：@bot bet 1000 → 消耗1000福气 + 1%手续费
2. 接受：任意用户接受 → 可选追加赌注（仅一次机会）
3. 确认：发起者选择匹配追加或放弃
4. 生效：双方赌注相等则生效，否则退还福气（扣除手续费）

示例：
- A发起1000福气
- B接受并追加5000福气
- A选择匹配5000福气
- 基础奖池：12000福气
```

**1vN模式**
```javascript
创建要求：
- 最低投入：10,000福气
- 发起费用：2%
- 转发奖励：10-50%（可选，从最终奖池扣除）
- 选项数量：2-3个
- 有效时间：最长72小时

费用计算：
投入10万福气 = 消耗102,000福气（含2%手续费）
设置20%转发奖励 = 从最终奖池扣除，无需预付
```

#### 3.1.2 标准化模板系统

```javascript
模板类型：
1. 体育竞技
   模板："{队伍A} vs {队伍B} - {日期}"
   选项：[A胜] [B胜] [平局/取消]
   
2. 价格预测
   模板："{币种}在{时间}前达到{价格}"
   选项：[达到] [未达到] [无效]
   
3. 事件预测
   模板："{事件}在{时间}前发生"
   选项：[发生] [未发生] [无法判定]
```

### 3.2 DAO裁定系统

#### 3.2.1 认证等级

```javascript
持币认证等级（系统自动认证，每小时更新）：
┌─────────┬──────────────┬────────────┬─────────────┬──────────────┐
│ 等级    │ 持币要求      │ 手续费折扣 │ 每日裁定次数 │ 裁定轮次权限  │
├─────────┼──────────────┼────────────┼─────────────┼──────────────┤
│ X1      │ 10 HAOX      │ 0%         │ 1次         │ 第一轮       │
│ X2      │ 1万 HAOX     │ 20%        │ 5次         │ 第一、二轮   │
│ X3      │ 100万 HAOX   │ 50%        │ 20次        │ 第一、二轮   │
│ X4      │ 1000万 HAOX  │ 70%        │ 50次        │ 第二、三轮   │
│ X5      │ 1亿 HAOX     │ 90%        │ 无限制      │ 第二、三轮   │
└─────────┴──────────────┴────────────┴─────────────┴──────────────┘

注：裁定次数每日UTC 0点重置，认证等级每小时根据钱包余额自动更新
```

#### 3.2.2 三轮裁定机制

```javascript
裁定流程：
第一轮：大众评审
- 参与要求：所有认证用户（X1-X5）
- 通过标准：11/20票（有11位用户投票即通过）
- 时限：24小时
- 信息公开：显示裁定用户的Telegram ID、平台分数、参与次数、认证等级

第二轮：专业评审
- 参与要求：X1、X2、X3认证用户
- 通过标准：6/10票
- 时限：12小时
- 信息公开：同第一轮

第三轮：终审裁定
- 参与要求：X4、X5认证用户
- 通过标准：3/5票
- 时限：6小时
- 信息公开：同第一轮

申诉机制：
- 申诉时限：裁定结果公布后24小时内
- 申诉质押：总奖池金额的10%
- 申诉流程：进入终审DAO仲裁（复用第三轮机制）
- 责任分担：申诉成功后，原裁定人员承担60%，DAO基金池承担40%
```

#### 3.2.3 信誉积分系统

```javascript
积分规则：
- 初始分数：90分
- 裁定门槛：70分
- 正确裁定：+1分
- 错误裁定：-5分
- 违规操作：-10分
- 连续正确奖励：
  - 5连胜：+5分
  - 10连胜：+15分
  - 20连胜：+35分

权益等级：
- 新手裁判(70-99分)：基础权益
- 资深裁判(100-199分)：10%手续费折扣
- 专家裁判(200-499分)：30%折扣+优先分配
- 大师裁判(500-999分)：50%折扣+跳过第一轮
- 传奇裁判(1000+分)：80%折扣+最终仲裁权
```

### 3.3 福气流转与分配

#### 3.3.1 手续费结构

```javascript
5%手续费分配（福气）：
├── 3% → 裁判奖励池（按参与轮次和准确率分配，赌约结束后立即发放）
└── 2% → DAO基金池（用于平台运营、系统维护、争议处理等）
```

#### 3.3.2 福气奖池分配规则

```javascript
1v1模式：
总奖池 = 双方投注福气总和
手续费 = 总奖池 × 5%
转发奖励 = 总奖池 × 转发奖励比例（0-5%）
赢家获得 = 总奖池 - 手续费 - 转发奖励

1vN模式：
总奖池 = 所有投注福气总和
手续费 = 总奖池 × 5%
转发奖励 = 总奖池 × 转发奖励比例（10-50%）
赢家瓜分 = 总奖池 - 手续费 - 转发奖励
个人获得 = 赢家奖池 × (个人投注/赢方总投注)
```

#### 3.3.3 福气兑换规则

```javascript
充值福气：
- 汇率：1 HAOX = 1 福气（固定汇率）
- 最小充值：无限制
- 手续费：用户承担Gas费
- 到账时间：区块链确认后立即到账

提现福气：
- 汇率：1 福气 = 1 HAOX（固定汇率）
- 每日额度：100万HAOX（防止挤兑，剩余滚动到次日）
- 小额提现：≤50万福气，热钱包自动处理，5分钟内到账
- 大额提现：>50万福气，多签钱包处理，24小时内到账
- 手续费：用户承担Gas费
- 钱包地址：Telegram登录自动生成BSC钱包地址
```

#### 3.3.4 福气奖励体系

```javascript
邀请奖励：
├── 基础奖励：每邀请1人 = 1,000福气
├── 里程碑奖励：邀请满5人 = 10,000福气
└── 里程碑奖励：邀请满10人 = 50,000福气

日常奖励：
├── 注册奖励：新用户注册 = 50福气
├── 首充奖励：首次充值 = 100福气
├── 每日签到：基础 = 10福气
├── 连续签到：额外奖励 = 5福气
├── 分享赌约：每次分享 = 20福气
└── 参与讨论：评论互动 = 5福气

Social Bet奖励：
├── 投注获胜：根据奖池比例分配福气
├── 裁定奖励：从手续费3%中按准确率分配
└── 转发奖励：根据设置比例分配福气
```

### 3.4 页面设计

#### 3.4.1 主页面布局

```
┌─────────────────────────────────────────┐
│  🍀 我的福气: 12,888 福气  [充值] [提现] │
│  � 今日收获: +288 福气   等级: 福气满满 │
├─────────────────────────────────────────┤
│         �🔥 热门赌约轮播（最近24小时）    │
│   [最高奖池] [最多参与] [即将结束]      │
├─────────────────────────────────────────┤
│  📊 实时动态  │  🏆 排行榜  │ 🎯 我的   │
├───────────────┼─────────────┼───────────┤
│               │             │           │
│ • 24h赌约流水 │ • 胜率榜    │ • 进行中  │
│ • 热度排序    │ • 福气榜    │ • 历史记录│
│ • 金额筛选    │ • 活跃榜    │ • 福气统计│
│ • 裁定记录    │ • 裁判榜    │ • 认证等级│
│               │             │ • 每日签到│
└───────────────┴─────────────┴───────────┘
│      [发起1v1赌约]  [发起1vN赌约]      │
│      [每日签到]    [邀请好友]          │
└─────────────────────────────────────────┘

注：主页面仅显示最近24小时的赌约记录，个人页面可查看完整历史
```

#### 3.4.2 福气系统界面

```
┌─────────────────────────────────────────┐
│              🍀 福气中心                │
├─────────────────────────────────────────┤
│  总福气: 12,888    可用: 11,888        │
│  锁定中: 1,000     等级: 福气满满 ⭐⭐⭐  │
├─────────────────────────────────────────┤
│  📈 福气获取方式                        │
│  • 投注获胜 💰  • 邀请好友 👥          │
│  • 每日签到 📅  • 裁定奖励 ⚖️          │
│  • 分享赌约 📤  • 参与讨论 💬          │
├─────────────────────────────────────────┤
│  🔄 福气兑换                            │
│  [充值福气] 1 HAOX = 1 福气             │
│  [提现福气] 1 福气 = 1 HAOX             │
│  每日提现额度: 100万 HAOX               │
└─────────────────────────────────────────┘
```

#### 3.4.2 赌约卡片展示

```
┌─────────────────────────────────┐
│ ⚔️ 社交赌约 #12345              │
│ 🔥 热度：8,234人围观            │
├─────────────────────────────────┤
│ 📝 TES vs AL - LPL夏季赛        │
│                                 │
│ 💰 基础奖池：12,000 HAOX        │
│ 🎯 围观奖池：88,000 HAOX        │
│ ⏰ 剩余时间：02:45:30           │
├─────────────────────────────────┤
│ 选项投注分布：                  │
│ TES胜：65% ████████             │
│ AL胜： 30% ████                 │
│ 取消： 5%  █                    │
├─────────────────────────────────┤
│ [参与投注] [分享赚钱] [查看详情]│
└─────────────────────────────────┘
```

#### 3.4.3 排行榜设计

```javascript
榜单类型：
1. 胜率榜（月榜/总榜）
   - 最少参与20场
   - 显示胜率、战绩、连胜
   
2. 收益榜（月榜/总榜）
   - 显示总收益、收益率
   - 最大单笔收益
   
3. 活跃榜
   - 参与次数
   - 发起次数
   - 裁定次数

称号系统：
月榜：
🥇 月度预言家 / 财富之王
🥈 智慧贤者 / 黄金猎手
🥉 洞察大师 / 幸运之星

总榜：
🏆 永恒先知 / 赌神
🏆 命运之眼 / 财富传奇
🏆 时空智者 / 幸运天选
```

## 四、技术实现

### 4.1 技术架构

```
前端：
├── Telegram Bot (Node.js)
├── Mini App (Next.js + TypeScript)
└── 管理后台 (React)

后端：
├── API服务 (Next.js API Routes)
├── 数据库 (Supabase/PostgreSQL)
├── 缓存 (Redis)
├── 定时任务 (Node.js Cron)
└── 自动结算服务

区块链交互：
├── HAOX代币合约 (现有)
├── 托管钱包管理
└── 批量转账服务 (Ethers.js)
```

### 4.2 核心系统模块

```typescript
主要模块：
1. 赌约管理系统 - 创建、参与、状态管理
2. 裁定投票系统 - 三轮投票、结果统计
3. 信誉积分系统 - 积分计算、等级管理
4. 自动结算系统 - 奖励计算、批量转账
5. 托管钱包服务 - 资金安全、自动分配
```

### 4.3 数据库设计

#### 4.3.1 核心表结构

```sql
-- 赌约主表
CREATE TABLE social_bets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    creator_id UUID REFERENCES users(id) NOT NULL,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    bet_type VARCHAR(10) CHECK (bet_type IN ('1v1', '1vN')),

    -- 赌约配置
    options JSONB NOT NULL, -- 选项配置 ["选项1", "选项2", "选项3"]
    min_fortune DECIMAL(20,8) NOT NULL, -- 最小福气投注
    max_fortune DECIMAL(20,8), -- 最大福气投注
    forward_reward_rate DECIMAL(5,2) DEFAULT 0, -- 转发奖励比例

    -- 状态管理
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'judging', 'confirming', 'settled', 'cancelled')),
    winning_option INTEGER, -- 获胜选项

    -- 确认机制
    creator_confirmed BOOLEAN DEFAULT FALSE, -- 发起人确认
    participants_confirmed BOOLEAN DEFAULT FALSE, -- 参与者确认
    dispute_raised BOOLEAN DEFAULT FALSE, -- 是否有争议

    -- 时间管理
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    judging_started_at TIMESTAMP WITH TIME ZONE,
    confirmation_deadline TIMESTAMP WITH TIME ZONE, -- 确认截止时间
    settled_at TIMESTAMP WITH TIME ZONE,

    -- 福气统计
    total_fortune_pool DECIMAL(20,8) DEFAULT 0, -- 总福气奖池
    fee_fortune DECIMAL(20,8) DEFAULT 0, -- 手续费福气
    forward_reward_fortune DECIMAL(20,8) DEFAULT 0, -- 转发奖励福气

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 投注记录表
CREATE TABLE bet_participants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bet_id UUID REFERENCES social_bets(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) NOT NULL,

    option_choice INTEGER NOT NULL, -- 选择的选项 (0, 1, 2)
    fortune_amount DECIMAL(20,8) NOT NULL, -- 投注福气数量

    -- 状态管理
    is_winner BOOLEAN DEFAULT FALSE, -- 是否获胜
    reward_fortune DECIMAL(20,8) DEFAULT 0, -- 获得的奖励福气

    -- 确认状态
    result_confirmed BOOLEAN DEFAULT FALSE, -- 是否确认结果
    confirmed_at TIMESTAMP WITH TIME ZONE, -- 确认时间

    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(bet_id, user_id) -- 每个用户每个赌约只能参与一次
);

-- 裁定投票表
CREATE TABLE bet_judgments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bet_id UUID REFERENCES social_bets(id) ON DELETE CASCADE,
    judge_id UUID REFERENCES users(id) NOT NULL,

    round_number INTEGER NOT NULL CHECK (round_number IN (1, 2, 3)),
    vote_option INTEGER NOT NULL, -- 投票选项

    -- 裁判资格
    reputation_score INTEGER NOT NULL, -- 投票时的信誉分数
    certification_level VARCHAR(10) NOT NULL, -- X1-X5认证等级

    -- 奖励记录
    reward_fortune DECIMAL(20,8) DEFAULT 0, -- 获得的福气奖励
    is_correct BOOLEAN DEFAULT NULL, -- 是否投票正确（结算后更新）

    voted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(bet_id, judge_id, round_number) -- 每轮每个裁判只能投票一次
);

-- 用户福气账户表
CREATE TABLE user_fortune (
    user_id UUID REFERENCES users(id) PRIMARY KEY,

    -- 福气余额
    available_fortune DECIMAL(20,8) DEFAULT 0 NOT NULL, -- 可用福气
    locked_fortune DECIMAL(20,8) DEFAULT 0 NOT NULL, -- 锁定福气（投注中）
    total_fortune DECIMAL(20,8) DEFAULT 0 NOT NULL, -- 总福气 = 可用 + 锁定

    -- 统计数据
    total_earned DECIMAL(20,8) DEFAULT 0 NOT NULL, -- 累计获得福气
    total_spent DECIMAL(20,8) DEFAULT 0 NOT NULL, -- 累计消费福气
    total_deposited DECIMAL(20,8) DEFAULT 0 NOT NULL, -- 累计充值福气
    total_withdrawn DECIMAL(20,8) DEFAULT 0 NOT NULL, -- 累计提现福气

    -- 福气等级
    fortune_level INTEGER DEFAULT 1 NOT NULL, -- 福气等级
    fortune_level_name VARCHAR(20) DEFAULT '初来乍到' NOT NULL, -- 等级名称

    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 福气交易记录表
CREATE TABLE fortune_transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) NOT NULL,

    -- 交易信息
    transaction_type VARCHAR(20) NOT NULL CHECK (transaction_type IN (
        'deposit', 'withdraw', 'bet', 'win', 'refund',
        'judge_reward', 'invite_reward', 'daily_checkin', 'admin_adjust'
    )),
    amount DECIMAL(20,8) NOT NULL, -- 正数为收入，负数为支出
    balance_before DECIMAL(20,8) NOT NULL, -- 交易前余额
    balance_after DECIMAL(20,8) NOT NULL, -- 交易后余额

    -- 关联信息
    reference_id UUID, -- 关联的业务ID（赌约ID、邀请ID等）
    reference_type VARCHAR(20), -- 关联类型
    description TEXT, -- 交易描述

    -- 区块链信息（仅充值/提现）
    haox_tx_hash VARCHAR(66), -- HAOX交易哈希
    haox_amount DECIMAL(20,8), -- HAOX数量
    exchange_rate DECIMAL(10,6) DEFAULT 1.0, -- 汇率

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 用户信誉积分表
CREATE TABLE user_reputation (
    user_id UUID REFERENCES users(id) PRIMARY KEY,

    -- 积分统计
    current_score INTEGER DEFAULT 90 NOT NULL,
    total_judgments INTEGER DEFAULT 0,
    correct_judgments INTEGER DEFAULT 0,
    consecutive_correct INTEGER DEFAULT 0,
    max_consecutive_correct INTEGER DEFAULT 0,

    -- 等级信息
    reputation_level VARCHAR(20) DEFAULT 'newbie' CHECK (
        reputation_level IN ('newbie', 'experienced', 'expert', 'master', 'legend')
    ),

    -- 认证等级（系统自动更新）
    certification_level VARCHAR(10) DEFAULT 'X1' CHECK (
        certification_level IN ('X1', 'X2', 'X3', 'X4', 'X5')
    ),
    haox_holdings DECIMAL(20,8) DEFAULT 0, -- 持币数量
    last_balance_check TIMESTAMP WITH TIME ZONE DEFAULT NOW(), -- 上次余额检查时间

    -- 裁定限制
    daily_judgments_used INTEGER DEFAULT 0, -- 当日已使用的裁定次数
    last_judgment_reset TIMESTAMP WITH TIME ZONE DEFAULT NOW(), -- 上次重置时间

    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 申诉记录表
CREATE TABLE bet_appeals (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bet_id UUID REFERENCES social_bets(id) ON DELETE CASCADE,
    appellant_id UUID REFERENCES users(id) NOT NULL,

    -- 申诉信息
    reason TEXT NOT NULL,
    evidence JSONB, -- 申诉证据
    stake_amount DECIMAL(20,8) NOT NULL, -- 质押金额（总奖池的10%）
    stake_tx_hash VARCHAR(66), -- 质押转账哈希

    -- 状态管理
    status VARCHAR(20) DEFAULT 'pending' CHECK (
        status IN ('pending', 'reviewing', 'approved', 'rejected', 'settled')
    ),

    -- 处理结果
    final_decision INTEGER, -- 最终裁定结果
    compensation_amount DECIMAL(20,8) DEFAULT 0, -- 赔偿金额

    -- 时间管理
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    reviewed_at TIMESTAMP WITH TIME ZONE,
    settled_at TIMESTAMP WITH TIME ZONE
);

-- HAOX充值提现记录表
CREATE TABLE haox_transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) NOT NULL,

    -- 交易类型
    transaction_type VARCHAR(10) NOT NULL CHECK (transaction_type IN ('deposit', 'withdraw')),

    -- 金额信息
    haox_amount DECIMAL(20,8) NOT NULL, -- HAOX数量
    fortune_amount DECIMAL(20,8) NOT NULL, -- 对应福气数量
    exchange_rate DECIMAL(10,6) DEFAULT 1.0 NOT NULL, -- 汇率

    -- Gas费信息（仅提现）
    gas_fee_level VARCHAR(10) CHECK (gas_fee_level IN ('economy', 'standard', 'fast')),
    estimated_gas_fee DECIMAL(20,8), -- 预估Gas费
    actual_gas_fee DECIMAL(20,8), -- 实际Gas费

    -- 状态管理
    status VARCHAR(20) DEFAULT 'pending' CHECK (
        status IN ('pending', 'confirming', 'processing', 'completed', 'failed', 'cancelled')
    ),

    -- 区块链信息
    tx_hash VARCHAR(66), -- 交易哈希
    block_number BIGINT, -- 区块号
    confirmations INTEGER DEFAULT 0, -- 确认数

    -- 多签信息（仅提现）
    multisig_tx_hash VARCHAR(66), -- 多签交易哈希
    required_signatures INTEGER, -- 需要的签名数
    current_signatures INTEGER DEFAULT 0, -- 当前签名数

    -- 错误信息
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,

    -- 时间信息
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    confirmed_at TIMESTAMP WITH TIME ZONE, -- 区块链确认时间
    processed_at TIMESTAMP WITH TIME ZONE, -- 处理完成时间

    -- 关联福气交易
    fortune_transaction_id UUID REFERENCES fortune_transactions(id)
);
```

### 4.4 API设计

#### 4.4.1 福气管理API

```typescript
// 获取用户福气信息
GET /api/fortune/balance
Response: {
  "availableFortune": "12888.50",
  "lockedFortune": "1000.00",
  "totalFortune": "13888.50",
  "fortuneLevel": 3,
  "fortuneLevelName": "福气满满",
  "totalEarned": "50000.00",
  "totalSpent": "36111.50"
}

// 充值福气（HAOX → 福气）
POST /api/fortune/deposit
{
  "haoxAmount": "1000.00",
  "txHash": "0x..." // HAOX转账交易哈希
}

// 提现福气（福气 → HAOX）
POST /api/fortune/withdraw
{
  "fortuneAmount": "1000.00",
  "gasFeeLevel": "standard" // economy | standard | fast
}

// 获取福气交易记录
GET /api/fortune/transactions?page=1&limit=20&type=all
```

#### 4.4.2 赌约管理API

```typescript
// 创建赌约
POST /api/social-bet/create
{
  "title": "TES vs AL - LPL夏季赛",
  "description": "2024年8月5日 TES对阵AL的比赛结果",
  "betType": "1vN",
  "options": ["TES胜", "AL胜", "取消/平局"],
  "minFortune": "1000", // 最小福气投注
  "maxFortune": "100000", // 最大福气投注
  "forwardRewardRate": 20, // 20%转发奖励
  "expiresAt": "2024-08-05T20:00:00Z"
}

// 参与赌约（使用福气）
POST /api/social-bet/{betId}/join
{
  "optionChoice": 0, // 选择选项0: TES胜
  "fortuneAmount": "5000" // 投注5000福气
}

// 确认赌约结果
POST /api/social-bet/{betId}/confirm
{
  "confirmed": true // true确认，false有异议
}

// 获取赌约详情
GET /api/social-bet/{betId}

// 获取赌约列表（24小时内）
GET /api/social-bet/list?status=active&page=1&limit=20
```

#### 4.4.3 裁定系统API

```typescript
// 提交裁定投票
POST /api/social-bet/{betId}/judge
{
  "round": 1,
  "voteOption": 0
}

// 获取裁定状态
GET /api/social-bet/{betId}/judgment-status

// 获取可裁定的赌约列表
GET /api/social-bet/judgeable?round=1
```

#### 4.4.4 用户信誉API

```typescript
// 获取用户信誉信息
GET /api/user/reputation

// 系统自动更新认证等级（每小时执行）
POST /api/system/update-certifications

// 检查用户裁定次数限制
GET /api/user/judgment-quota
```

#### 4.4.5 奖励系统API

```typescript
// 每日签到获得福气
POST /api/fortune/daily-checkin
Response: {
  "fortuneReward": "10",
  "consecutiveDays": 5,
  "bonusReward": "5", // 连续签到奖励
  "totalReward": "15",
  "nextCheckIn": "2024-08-07T00:00:00Z"
}

// 邀请奖励处理
POST /api/fortune/invite-reward
{
  "inviteeId": "user_uuid",
  "inviteCode": "ABC123"
}
Response: {
  "baseReward": "1000", // 基础邀请奖励
  "milestoneReward": "0", // 里程碑奖励（如果达成）
  "totalInvitations": 6,
  "nextMilestone": {
    "target": 10,
    "reward": "50000"
  }
}

// 分享赌约奖励
POST /api/fortune/share-reward
{
  "betId": "bet_uuid",
  "platform": "telegram" // telegram, twitter, etc.
}

// 参与讨论奖励
POST /api/fortune/comment-reward
{
  "betId": "bet_uuid",
  "commentId": "comment_uuid"
}

// 获取福气等级信息
GET /api/fortune/levels
Response: {
  "currentLevel": 3,
  "currentLevelName": "福气满满",
  "nextLevel": 4,
  "nextLevelName": "福星高照",
  "progressToNext": 0.65,
  "requiredFortune": 100000,
  "levelBenefits": [
    "投注手续费8折",
    "可发起1vN赌约",
    "优先客服支持"
  ]
}

// 获取福气获取历史
GET /api/fortune/earning-history?page=1&limit=20&type=all
Response: {
  "transactions": [
    {
      "type": "daily_checkin",
      "amount": "15",
      "description": "每日签到+连续奖励",
      "createdAt": "2024-08-06T08:00:00Z"
    },
    {
      "type": "invite_reward",
      "amount": "1000",
      "description": "邀请用户@username",
      "createdAt": "2024-08-05T15:30:00Z"
    }
  ],
  "totalEarned": "50000",
  "pagination": {...}
}
```

#### 4.4.6 管理员API

```typescript
// 人工处理争议赌约
POST /api/admin/social-bet/{betId}/resolve
{
  "finalResult": 1, // 最终结果
  "reason": "经过调查，证据显示..."
}

// 调整用户福气（紧急情况）
POST /api/admin/fortune/adjust
{
  "userId": "user_uuid",
  "amount": "1000", // 正数增加，负数扣除
  "reason": "系统错误补偿"
}

// 获取系统统计
GET /api/admin/statistics
Response: {
  "totalUsers": 10000,
  "totalFortune": "50000000",
  "activeBets": 156,
  "dailyVolume": "1000000"
}
```

### 4.5 自动化流程

#### 4.5.1 赌约状态自动流转

```typescript
// 定时任务：检查赌约状态
async function checkBetStatus() {
  // 1. 检查过期的赌约，启动裁定流程
  const expiredBets = await getExpiredActiveBets();
  for (const bet of expiredBets) {
    await startJudgmentProcess(bet.id);
  }

  // 2. 检查裁定超时的赌约
  const timeoutJudgments = await getTimeoutJudgments();
  for (const judgment of timeoutJudgments) {
    await handleJudgmentTimeout(judgment.betId, judgment.round);
  }

  // 3. 检查可结算的赌约
  const settleableBets = await getSettleableBets();
  for (const bet of settleableBets) {
    await autoSettleBet(bet.id);
  }

  // 4. 重置每日裁定次数（UTC 0点）
  await resetDailyJudgmentQuotas();
}

// 每分钟执行一次
setInterval(checkBetStatus, 60000);

// 每小时更新用户认证等级
setInterval(updateUserCertifications, 3600000);
```

#### 4.5.2 认证等级自动更新

```typescript
async function updateUserCertifications() {
  try {
    const users = await getAllActiveUsers();

    for (const user of users) {
      // 获取用户当前钱包余额
      const balance = await getHAOXBalance(user.walletAddress);

      // 计算新的认证等级
      const newLevel = calculateCertificationLevel(balance);

      // 更新数据库
      if (newLevel !== user.certificationLevel) {
        await updateUserCertification(user.id, newLevel, balance);
        console.log(`用户 ${user.id} 认证等级更新: ${user.certificationLevel} → ${newLevel}`);
      }
    }

    console.log('认证等级更新完成');
  } catch (error) {
    console.error('认证等级更新失败:', error);
  }
}

function calculateCertificationLevel(balance: string): string {
  const amount = parseFloat(balance);

  if (amount >= 100000000) return 'X5'; // 1亿
  if (amount >= 10000000) return 'X4';  // 1000万
  if (amount >= 1000000) return 'X3';   // 100万
  if (amount >= 10000) return 'X2';     // 1万
  return 'X1';                          // 10+
}
```

#### 4.5.3 福气奖励自动化系统

```typescript
class FortuneRewardSystem {
  // 每日签到奖励
  async processDailyCheckIn(userId: string): Promise<number> {
    const user = await getUserById(userId);
    const today = new Date().toDateString();

    // 检查今日是否已签到
    const todayCheckIn = await getCheckInRecord(userId, today);
    if (todayCheckIn) {
      throw new Error('今日已签到');
    }

    // 计算连续签到天数
    const consecutiveDays = await getConsecutiveCheckInDays(userId);

    // 基础奖励 + 连续奖励
    const baseReward = 10;
    const bonusReward = consecutiveDays >= 7 ? 5 : 0;
    const totalReward = baseReward + bonusReward;

    // 发放福气奖励
    await this.addUserFortune(userId, totalReward, 'daily_checkin');

    // 记录签到
    await recordCheckIn(userId, today, consecutiveDays + 1, totalReward);

    return totalReward;
  }

  // 邀请奖励处理
  async processInviteReward(inviterId: string, inviteeId: string): Promise<object> {
    const inviter = await getUserById(inviterId);

    // 基础邀请奖励
    const baseReward = 1000;
    await this.addUserFortune(inviterId, baseReward, 'invite_reward', inviteeId);

    // 更新邀请统计
    const newInviteCount = await incrementInviteCount(inviterId);

    // 检查里程碑奖励
    let milestoneReward = 0;
    if (newInviteCount === 5) {
      milestoneReward = 10000;
      await this.addUserFortune(inviterId, milestoneReward, 'invite_milestone_5');
    } else if (newInviteCount === 10) {
      milestoneReward = 50000;
      await this.addUserFortune(inviterId, milestoneReward, 'invite_milestone_10');
    }

    return {
      baseReward,
      milestoneReward,
      totalInvitations: newInviteCount,
      nextMilestone: this.getNextMilestone(newInviteCount)
    };
  }

  // 分享奖励
  async processShareReward(userId: string, betId: string, platform: string): Promise<number> {
    // 检查是否已分享过此赌约
    const existingShare = await getShareRecord(userId, betId, platform);
    if (existingShare) {
      throw new Error('已分享过此赌约');
    }

    const shareReward = 20;
    await this.addUserFortune(userId, shareReward, 'share_reward', betId);
    await recordShare(userId, betId, platform, shareReward);

    return shareReward;
  }

  // 评论奖励
  async processCommentReward(userId: string, betId: string, commentId: string): Promise<number> {
    // 检查评论是否有效（长度、内容质量等）
    const comment = await getComment(commentId);
    if (!this.isValidComment(comment)) {
      throw new Error('评论不符合奖励标准');
    }

    const commentReward = 5;
    await this.addUserFortune(userId, commentReward, 'comment_reward', commentId);

    return commentReward;
  }

  // 统一的福气发放方法
  private async addUserFortune(
    userId: string,
    amount: number,
    type: string,
    referenceId?: string
  ): Promise<void> {
    await supabase.rpc('add_user_fortune', {
      p_user_id: userId,
      p_amount: amount,
      p_transaction_type: type,
      p_reference_id: referenceId,
      p_description: this.getTransactionDescription(type, amount)
    });
  }

  private getTransactionDescription(type: string, amount: number): string {
    const descriptions = {
      'daily_checkin': `每日签到奖励 +${amount}福气`,
      'invite_reward': `邀请好友奖励 +${amount}福气`,
      'invite_milestone_5': `邀请5人里程碑奖励 +${amount}福气`,
      'invite_milestone_10': `邀请10人里程碑奖励 +${amount}福气`,
      'share_reward': `分享赌约奖励 +${amount}福气`,
      'comment_reward': `参与讨论奖励 +${amount}福气`
    };

    return descriptions[type] || `${type} +${amount}福气`;
  }
}
```

#### 4.5.2 自动结算流程

```typescript
async function autoSettleBet(betId: string) {
  try {
    // 1. 获取赌约和投注信息
    const bet = await getBetDetails(betId);
    const participants = await getBetParticipants(betId);

    // 2. 计算奖励分配
    const distribution = calculateRewardDistribution(bet, participants);

    // 3. 执行批量转账
    const txHashes = await batchTransferRewards(distribution);

    // 4. 更新数据库记录
    await updateSettlementRecords(betId, distribution, txHashes);

    // 5. 更新裁判信誉积分
    await updateJudgeReputations(betId, bet.winningOption);

    console.log(`赌约 ${betId} 自动结算完成`);

  } catch (error) {
    console.error(`赌约 ${betId} 结算失败:`, error);
    // 记录错误，等待人工处理
    await logSettlementError(betId, error);
  }
}
```

#### 4.5.3 混合钱包管理系统

```typescript
class HybridWalletSystem {
  private hotWallet: ethers.Wallet;        // 热钱包（小额提现）
  private multiSigWallet: GnosisSafe;      // 多签钱包（大额存储）
  private haoxContract: ethers.Contract;
  private emergencyPaused: boolean = false;

  // 配置参数
  private readonly SMALL_WITHDRAWAL_LIMIT = 500000; // 50万福气
  private readonly HOT_WALLET_MAX_BALANCE = 10000000; // 热钱包最大余额1000万HAOX
  private readonly HOT_WALLET_MIN_BALANCE = 2000000;  // 热钱包最小余额200万HAOX
  private readonly DAILY_WITHDRAWAL_LIMIT = 1000000;  // 每日提现额度100万HAOX

  constructor() {
    // 热钱包私钥安全存储
    const hotPrivateKey = this.getSecurePrivateKey('HOT_WALLET');
    this.hotWallet = new ethers.Wallet(hotPrivateKey);

    // 多签钱包初始化
    this.multiSigWallet = new GnosisSafe(process.env.MULTISIG_ADDRESS);

    this.haoxContract = new ethers.Contract(HAOX_ADDRESS, HAOX_ABI, this.hotWallet);
  }

  // 安全获取私钥
  private getSecurePrivateKey(walletType: 'HOT_WALLET' | 'MULTISIG'): string {
    const encryptedKey = process.env[`${walletType}_PRIVATE_KEY_ENCRYPTED`];
    const decryptionKey = process.env.DECRYPTION_KEY;

    if (!encryptedKey || !decryptionKey) {
      throw new Error(`${walletType}私钥配置缺失`);
    }

    // 解密私钥（使用AES加密）
    return this.decryptPrivateKey(encryptedKey, decryptionKey);
  }

  // 小额提现（全自动）
  async processSmallWithdrawal(userId: string, fortuneAmount: string, userAddress: string) {
    const amount = parseFloat(fortuneAmount);

    if (amount > this.SMALL_WITHDRAWAL_LIMIT) {
      throw new Error('超出小额提现限额，转入人工处理队列');
    }

    // 检查每日提现额度
    const todayWithdrawn = await this.getTodayWithdrawnAmount();
    if (todayWithdrawn + amount > this.DAILY_WITHDRAWAL_LIMIT) {
      throw new Error('超出每日提现额度，请明日再试');
    }

    // 检查热钱包余额
    const hotBalance = await this.getHotWalletBalance();
    if (hotBalance < amount) {
      await this.requestHotWalletReplenishment();
      throw new Error('热钱包余额不足，已请求补充，请稍后重试');
    }

    // 立即转账
    const tx = await this.haoxContract.transfer(
      userAddress,
      ethers.parseUnits(fortuneAmount, 18)
    );

    await tx.wait();

    // 扣除用户福气
    await this.deductUserFortune(userId, fortuneAmount);

    // 记录提现
    await this.recordWithdrawal(userId, fortuneAmount, tx.hash, 'completed');

    return tx.hash;
  }

  // 大额提现（多签处理）
  async processLargeWithdrawal(userId: string, fortuneAmount: string, userAddress: string) {
    const amount = parseFloat(fortuneAmount);

    // 创建多签交易
    const multiSigTx = await this.multiSigWallet.createTransaction({
      to: userAddress,
      value: 0,
      data: this.haoxContract.interface.encodeFunctionData('transfer', [
        userAddress,
        ethers.parseUnits(fortuneAmount, 18)
      ])
    });

    // 记录待处理提现
    await this.recordWithdrawal(userId, fortuneAmount, multiSigTx.hash, 'pending');

    // 通知管理员签名
    await this.notifyAdminForSigning(multiSigTx.hash, amount);

    return multiSigTx.hash;
  }

  // 批量转账给获胜者
  async batchTransferRewards(distributions: RewardDistribution[]) {
    // 检查紧急暂停状态
    if (this.emergencyPaused) {
      throw new Error('系统处于紧急暂停状态');
    }

    // 验证转账总额
    const totalAmount = distributions.reduce((sum, dist) =>
      sum + parseFloat(dist.amount), 0
    );

    const balance = await this.checkBalance();
    if (parseFloat(balance) < totalAmount * 1.1) { // 保留10%缓冲
      throw new Error('托管钱包余额不足');
    }

    const txHashes: string[] = [];

    for (const dist of distributions) {
      try {
        // Gas费优化
        const gasPrice = await this.getOptimalGasPrice();

        const tx = await this.haoxContract.transfer(
          dist.userAddress,
          ethers.parseUnits(dist.amount, 18),
          { gasPrice }
        );

        await tx.wait();
        txHashes.push(tx.hash);

        console.log(`转账成功: ${dist.amount} HAOX → ${dist.userAddress}`);

        // 记录转账日志
        await this.logTransaction(dist, tx.hash);

      } catch (error) {
        console.error(`转账失败: ${dist.userAddress}`, error);
        await this.handleTransferError(dist, error);
        throw error;
      }
    }

    return txHashes;
  }

  // 检查钱包余额
  async checkBalance(): Promise<string> {
    const balance = await this.haoxContract.balanceOf(this.wallet.address);
    return ethers.formatUnits(balance, 18);
  }

  // 紧急暂停机制
  async emergencyPause(reason: string) {
    this.emergencyPaused = true;
    await this.notifyEmergencyPause(reason);
    console.log(`紧急暂停激活: ${reason}`);
  }

  // 恢复正常运行
  async resumeOperations() {
    this.emergencyPaused = false;
    console.log('系统恢复正常运行');
  }

  // 获取最优Gas价格
  private async getOptimalGasPrice(): Promise<bigint> {
    const provider = this.wallet.provider;
    const gasPrice = await provider.getFeeData();

    // 使用标准Gas价格
    return gasPrice.gasPrice || ethers.parseUnits('5', 'gwei');
  }
}
```

### 4.6 安全机制

#### 4.6.1 资金安全保障

```typescript
// 1. 多重验证机制
class SecurityValidator {
  // 验证用户投注资格
  async validateBetParticipation(userId: string, betId: string, amount: string) {
    // 检查是否已参与
    const existing = await checkExistingParticipation(userId, betId);
    if (existing) throw new Error('用户已参与此赌约');

    // 检查余额充足
    const balance = await getHAOXBalance(userId);
    if (parseFloat(balance) < parseFloat(amount)) {
      throw new Error('余额不足');
    }

    // 检查投注金额范围
    const bet = await getBetDetails(betId);
    if (parseFloat(amount) < parseFloat(bet.minAmount) ||
        parseFloat(amount) > parseFloat(bet.maxAmount)) {
      throw new Error('投注金额超出范围');
    }
  }

  // 验证裁判资格
  async validateJudgeEligibility(userId: string, betId: string, round: number) {
    const user = await getUserWithReputation(userId);

    // 检查认证等级权限
    const allowedLevels = this.getAllowedLevelsForRound(round);
    if (!allowedLevels.includes(user.certificationLevel)) {
      throw new Error('认证等级不足');
    }

    // 检查每日裁定次数限制
    const dailyQuota = this.getDailyQuotaForLevel(user.certificationLevel);
    if (user.dailyJudgmentsUsed >= dailyQuota && dailyQuota !== -1) {
      throw new Error('今日裁定次数已用完');
    }

    // 检查是否已在此轮投票
    const existingVote = await getExistingVote(userId, betId, round);
    if (existingVote) throw new Error('已在此轮投票');

    // 检查是否为赌约参与者（避免利益冲突）
    const isParticipant = await checkBetParticipation(userId, betId);
    if (isParticipant) throw new Error('赌约参与者不能担任裁判');
  }

  // 获取轮次允许的认证等级
  private getAllowedLevelsForRound(round: number): string[] {
    switch (round) {
      case 1: return ['X1', 'X2', 'X3', 'X4', 'X5'];
      case 2: return ['X1', 'X2', 'X3'];
      case 3: return ['X4', 'X5'];
      default: return [];
    }
  }

  // 获取等级的每日裁定配额
  private getDailyQuotaForLevel(level: string): number {
    const quotas = { X1: 1, X2: 5, X3: 20, X4: 50, X5: -1 }; // -1表示无限制
    return quotas[level] || 0;
  }
}

// 2. 托管钱包安全
class EscrowSecurity {
  // 钱包余额监控
  async monitorWalletBalance() {
    const balance = await this.escrowWallet.checkBalance();
    const requiredBalance = await calculateRequiredBalance();

    if (parseFloat(balance) < parseFloat(requiredBalance) * 1.1) {
      await sendLowBalanceAlert(balance, requiredBalance);
    }
  }

  // 异常交易检测
  async detectAnomalousTransactions() {
    const recentTxs = await getRecentTransactions(24); // 24小时内

    for (const tx of recentTxs) {
      // 检查大额转账
      if (parseFloat(tx.amount) > 1000000) {
        await flagLargeTransaction(tx);
      }

      // 检查频繁转账
      const userTxCount = await getUserTransactionCount(tx.userId, 1); // 1小时内
      if (userTxCount > 10) {
        await flagFrequentTransactions(tx.userId);
      }
    }
  }
}
```

#### 4.6.2 防作弊机制

```typescript
// 1. 投票操纵检测
class AntiManipulation {
  // 检测协调投票
  async detectCoordinatedVoting(betId: string, round: number) {
    const votes = await getRoundVotes(betId, round);

    // 检查投票时间聚集
    const timeWindows = groupVotesByTimeWindow(votes, 300); // 5分钟窗口
    for (const window of timeWindows) {
      if (window.votes.length > 5 && window.unanimity > 0.9) {
        await flagSuspiciousVoting(betId, round, window);
      }
    }

    // 检查IP地址聚集
    const ipGroups = groupVotesByIP(votes);
    for (const group of ipGroups) {
      if (group.votes.length > 3) {
        await flagIPClustering(betId, round, group);
      }
    }
  }

  // 检测刷分行为
  async detectReputationFarming(userId: string) {
    const recentJudgments = await getUserRecentJudgments(userId, 30); // 30天内

    // 检查判断频率
    if (recentJudgments.length > 100) {
      await flagHighFrequencyJudging(userId);
    }

    // 检查准确率异常
    const accuracy = calculateAccuracy(recentJudgments);
    if (accuracy > 0.95 && recentJudgments.length > 50) {
      await flagSuspiciousAccuracy(userId);
    }
  }
}
```

### 4.7 部署和运维

#### 4.7.1 环境配置

```bash
# 托管钱包安全配置
ESCROW_PRIVATE_KEY_ENCRYPTED=encrypted_private_key_string # 加密后的私钥
DECRYPTION_KEY=your_decryption_key # 解密密钥
ESCROW_WALLET_ADDRESS=0x... # 托管钱包地址（用于验证）

# 区块链配置
HAOX_CONTRACT_ADDRESS=0x... # HAOX代币合约地址
BSC_RPC_URL=https://bsc-dataseed.binance.org/
BSC_TESTNET_RPC_URL=https://data-seed-prebsc-1-s1.binance.org:8545/

# 数据库配置
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# 安全配置
JWT_SECRET=your-jwt-secret
RATE_LIMIT_WINDOW=60000 # 1分钟
RATE_LIMIT_MAX_REQUESTS=100
EMERGENCY_PAUSE_ENABLED=true

# 监控配置
ALERT_WEBHOOK_URL=https://hooks.slack.com/...
LOW_BALANCE_THRESHOLD=100000 # 10万HAOX
ABNORMAL_TX_THRESHOLD=1000000 # 100万HAOX异常交易阈值

# Gas费配置
GAS_PRICE_ECONOMY=********** # 3 Gwei
GAS_PRICE_STANDARD=********** # 5 Gwei
GAS_PRICE_FAST=*********** # 10 Gwei
```

#### 4.7.2 监控和告警

```typescript
// 系统健康监控
class SystemMonitor {
  async runHealthChecks() {
    const checks = [
      this.checkDatabaseConnection(),
      this.checkEscrowWalletBalance(),
      this.checkPendingSettlements(),
      this.checkAPIResponseTime(),
      this.checkPrivateKeySecurity(),
      this.checkEmergencyPauseStatus(),
    ];

    const results = await Promise.allSettled(checks);

    for (const [index, result] of results.entries()) {
      if (result.status === 'rejected') {
        await sendAlert(`健康检查失败: ${checks[index].name}`, result.reason);

        // 关键错误触发紧急暂停
        if (this.isCriticalError(checks[index].name)) {
          await this.triggerEmergencyPause(result.reason);
        }
      }
    }
  }

  // 检查私钥安全性
  async checkPrivateKeySecurity() {
    // 验证私钥配置是否正确
    const wallet = new EscrowWalletService();
    const address = await wallet.getAddress();

    if (address !== process.env.ESCROW_WALLET_ADDRESS) {
      throw new Error('私钥与预期地址不匹配');
    }
  }

  // 异常交易监控
  async monitorAbnormalTransactions() {
    const recentTxs = await getRecentTransactions(1); // 1小时内

    for (const tx of recentTxs) {
      // 检查大额转账
      if (parseFloat(tx.amount) > parseFloat(process.env.ABNORMAL_TX_THRESHOLD)) {
        await sendAlert(`检测到大额转账: ${tx.amount} HAOX`, tx);
      }

      // 检查频繁转账
      const userTxCount = await getUserTransactionCount(tx.userId, 1);
      if (userTxCount > 20) {
        await sendAlert(`用户频繁转账: ${tx.userId}`, { count: userTxCount });
      }
    }
  }

  // 业务指标监控
  async trackBusinessMetrics() {
    const metrics = {
      activeBets: await countActiveBets(),
      dailyVolume: await getDailyTradingVolume(),
      pendingJudgments: await countPendingJudgments(),
      averageSettlementTime: await getAverageSettlementTime(),
      escrowBalance: await getEscrowWalletBalance(),
      pendingAppeals: await countPendingAppeals(),
      dailyNewUsers: await countDailyNewUsers(),
    };

    // 发送到监控系统
    await sendMetrics(metrics);

    // 检查异常指标
    await this.checkAbnormalMetrics(metrics);
  }

  // 触发紧急暂停
  async triggerEmergencyPause(reason: string) {
    const escrowService = new EscrowWalletService();
    await escrowService.emergencyPause(reason);

    // 通知所有相关人员
    await sendCriticalAlert(`系统紧急暂停: ${reason}`);
  }
}

// 每5分钟执行健康检查
setInterval(() => new SystemMonitor().runHealthChecks(), 300000);

// 每小时执行异常交易监控
setInterval(() => new SystemMonitor().monitorAbnormalTransactions(), 3600000);
```

## 五、实施计划

### 5.1 开发阶段

**第一阶段（2周）：福气系统和基础功能**
- 福气系统数据库设计（用户福气账户、交易记录、HAOX兑换记录）
- 福气核心API实现（充值、提现、转账、余额查询）
- 混合钱包系统搭建（热钱包+多签钱包）
- 自动提现系统（小额实时、大额人工）
- Telegram登录自动生成BSC钱包地址
- 基础的前端福气界面

**第二阶段（1.5周）：Social Bet核心功能**
- 赌约创建和参与（使用福气）
- 三轮裁定系统（包含认证等级限制）
- 确认机制替代申诉系统
- 自动结算和福气分配
- 24小时赌约展示界面

**第三阶段（1.5周）：福气奖励生态**
- 邀请奖励系统（1000福气基础+里程碑奖励）
- 每日签到系统（10福气+连续奖励）
- 分享和评论奖励系统
- 福气等级系统和权益
- 认证等级自动更新（每小时检查HAOX余额）

**第四阶段（1周）：安全和优化**
- 安全监控和异常检测
- 紧急暂停机制
- 热钱包自动补充系统
- 每日提现额度控制
- 完整的测试和性能优化

### 5.2 测试计划

**单元测试**：
- 认证等级计算逻辑
- 裁定次数限制验证
- 申诉流程测试
- 奖励分配计算

**集成测试**：
- API端点和数据库交互
- 托管钱包转账功能
- 自动化任务执行
- 实时数据更新

**安全测试**：
- 私钥安全性验证
- 防作弊机制测试
- 异常情况处理
- 紧急暂停功能

**压力测试**：
- 高并发投注场景
- 大量用户同时裁定
- 批量结算性能
- 数据库并发写入

### 5.3 上线部署

**测试网部署（1周）**：
- BSC测试网环境搭建
- 测试代币发放
- 功能完整性验证
- 安全机制测试

**内测阶段（1周）**：
- 邀请20-50名用户参与内测
- 收集用户反馈
- 修复发现的问题
- 优化用户体验

**公测阶段（1周）**：
- 逐步开放更多用户
- 监控系统稳定性
- 验证资金安全
- 完善监控告警

**正式上线**：
- 完整功能上线
- 24小时监控
- 用户支持服务
- 持续优化迭代

### 5.4 风险控制

**技术风险**：
- 私钥泄露：多重加密存储，定期轮换
- 系统故障：完善的监控和自动恢复机制
- 数据丢失：多地备份，定期恢复演练

**业务风险**：
- 用户争议：完善的申诉和仲裁机制
- 资金安全：实时监控，异常自动暂停
- 合规风险：持续关注法规变化，及时调整

**运营风险**：
- 用户流失：优化用户体验，增加激励机制
- 竞争压力：持续创新，保持技术领先
- 成本控制：优化Gas费用，提高系统效率
```
