# Social Bet 功能需求文档 (PRD)

## 一、产品概述

### 1.1 功能定位
**Social Bet** 是一个基于Telegram的去中心化社交预测工具，将日常争论转化为有趣的赌约，通过HAOX代币创造真实的价值流转。

### 1.2 核心价值
- **社交属性**：将群聊争论变成财富机会
- **简单易用**：标准化模板，三选项设计
- **公平透明**：DAO裁定机制，链上可查
- **病毒传播**：转发激励，快速裂变

### 1.3 目标用户
- Telegram活跃用户
- 加密货币爱好者
- 体育/电竞粉丝
- 预测市场参与者

## 二、功能架构

### 2.1 核心功能模块

```
HAOX Social Bet
├── 赌约系统
│   ├── 1v1模式（快速对赌）
│   ├── 1vN模式（多人预测）
│   └── 标准化模板（80%）+ 自定义（20%）
├── 裁定系统
│   ├── 三轮DAO裁定
│   ├── 信誉积分系统
│   └── 奖惩机制
├── 激励系统
│   ├── 转发奖励
│   ├── 裁判奖励
│   └── 排行榜奖励
└── 数据系统
    ├── 实时动态
    ├── 排行榜单
    └── 个人中心
```

## 三、详细功能设计

### 3.1 赌约创建与参与

#### 3.1.1 赌约模式

**1v1模式**
```javascript
创建流程：
1. 发起：@bot bet 1000 → 支付1000 HAOX + 1%手续费
2. 接受：任意用户接受 → 可选追加赌注（仅一次机会）
3. 确认：发起者选择匹配追加或放弃
4. 生效：双方赌注相等则生效，否则退款（扣除手续费）

示例：
- A发起1000 HAOX
- B接受并追加5000 HAOX
- A选择匹配5000 HAOX
- 基础奖池：12000 HAOX
```

**1vN模式**
```javascript
创建要求：
- 最低投入：10,000 HAOX
- 发起费用：2%
- 转发奖励：10-50%（可选，从最终奖池扣除）
- 选项数量：2-3个
- 有效时间：最长72小时

费用计算：
投入10万HAOX = 支付102,000 HAOX（含2%手续费）
设置20%转发奖励 = 从最终奖池扣除，无需预付
```

#### 3.1.2 标准化模板系统

```javascript
模板类型：
1. 体育竞技
   模板："{队伍A} vs {队伍B} - {日期}"
   选项：[A胜] [B胜] [平局/取消]
   
2. 价格预测
   模板："{币种}在{时间}前达到{价格}"
   选项：[达到] [未达到] [无效]
   
3. 事件预测
   模板："{事件}在{时间}前发生"
   选项：[发生] [未发生] [无法判定]
```

### 3.2 DAO裁定系统

#### 3.2.1 认证等级

```javascript
持币认证等级：
┌─────────┬──────────────┬────────────┬─────────────┐
│ 等级    │ 持币要求      │ 手续费折扣 │ DAO分红比例  │
├─────────┼──────────────┼────────────┼─────────────┤
│ X1      │ 10 HAOX      │ 0%         │ 10%         │
│ X2      │ 1万 HAOX     │ 20%        │ 30%         │
│ X3      │ 100万 HAOX   │ 50%        │ 25%         │
│ X4      │ 1000万 HAOX  │ 70%        │ 25%         │
│ X5      │ 1亿 HAOX     │ 90%        │ 10%         │
└─────────┴──────────────┴────────────┴─────────────┘
```

#### 3.2.2 三轮裁定机制

```javascript
裁定流程：
第一轮：大众评审
- 参与要求：70分以上所有认证用户
- 通过标准：11/20票
- 时限：24小时

第二轮：专业评审  
- 参与要求：100分以上 + X1-X3认证
- 通过标准：6/10票
- 时限：12小时

第三轮：终审裁定
- 参与要求：500分以上 + X4-X5认证
- 通过标准：3/5票
- 时限：6小时
```

#### 3.2.3 信誉积分系统

```javascript
积分规则：
- 初始分数：90分
- 裁定门槛：70分
- 正确裁定：+1分
- 错误裁定：-5分
- 违规操作：-10分
- 连续正确奖励：
  - 5连胜：+5分
  - 10连胜：+15分
  - 20连胜：+35分

权益等级：
- 新手裁判(70-99分)：基础权益
- 资深裁判(100-199分)：10%手续费折扣
- 专家裁判(200-499分)：30%折扣+优先分配
- 大师裁判(500-999分)：50%折扣+跳过第一轮
- 传奇裁判(1000+分)：80%折扣+最终仲裁权
```

### 3.3 费用与分配

#### 3.3.1 手续费结构

```javascript
5%手续费分配（更正版）：
├── 1.5% → 第一轮裁判平分
├── 1.5% → 第二轮裁判平分
├── 1.5% → 第三轮裁判平分
├── 0.3% → DAO基金池
└── 0.2% → 争议保险基金
```

#### 3.3.2 奖池分配规则

```javascript
1v1模式：
总奖池 = 双方投注总和
赢家获得 = 总奖池 × 90%（扣除5%手续费+5%转发奖励）

1vN模式：
总奖池 = 所有投注总和
赢家瓜分 = 总奖池 × (100% - 手续费% - 转发奖励%)
个人获得 = 赢家奖池 × (个人投注/赢方总投注)
```

### 3.4 页面设计

#### 3.4.1 主页面布局

```
┌─────────────────────────────────────────┐
│         🔥 热门赌约轮播                 │
│   [最高奖池] [最多参与] [即将结束]      │
├─────────────────────────────────────────┤
│  📊 实时动态  │  🏆 排行榜  │ 🎯 我的   │
├───────────────┼─────────────┼───────────┤
│               │             │           │
│ • 赌约流水    │ • 胜率榜    │ • 进行中  │
│ • 热度排序    │ • 收益榜    │ • 已结束  │
│ • 金额筛选    │ • 活跃榜    │ • 数据统计│
│               │             │           │
└───────────────┴─────────────┴───────────┘
│      [发起1v1赌约]  [发起1vN赌约]      │
└─────────────────────────────────────────┘
```

#### 3.4.2 赌约卡片展示

```
┌─────────────────────────────────┐
│ ⚔️ 社交赌约 #12345              │
│ 🔥 热度：8,234人围观            │
├─────────────────────────────────┤
│ 📝 TES vs AL - LPL夏季赛        │
│                                 │
│ 💰 基础奖池：12,000 HAOX        │
│ 🎯 围观奖池：88,000 HAOX        │
│ ⏰ 剩余时间：02:45:30           │
├─────────────────────────────────┤
│ 选项投注分布：                  │
│ TES胜：65% ████████             │
│ AL胜： 30% ████                 │
│ 取消： 5%  █                    │
├─────────────────────────────────┤
│ [参与投注] [分享赚钱] [查看详情]│
└─────────────────────────────────┘
```

#### 3.4.3 排行榜设计

```javascript
榜单类型：
1. 胜率榜（月榜/总榜）
   - 最少参与20场
   - 显示胜率、战绩、连胜
   
2. 收益榜（月榜/总榜）
   - 显示总收益、收益率
   - 最大单笔收益
   
3. 活跃榜
   - 参与次数
   - 发起次数
   - 裁定次数

称号系统：
月榜：
🥇 月度预言家 / 财富之王
🥈 智慧贤者 / 黄金猎手
🥉 洞察大师 / 幸运之星

总榜：
🏆 永恒先知 / 赌神
🏆 命运之眼 / 财富传奇
🏆 时空智者 / 幸运天选
```

## 四、技术实现

### 4.1 技术架构

```
前端：
├── Telegram Bot (Node.js)
├── Mini App (Next.js + TypeScript)
└── 管理后台 (React)

后端：
├── API服务 (Node.js + Express)
├── 数据库 (Supabase/PostgreSQL)
├── 缓存 (Redis)
└── 任务队列 (Bull)

区块链：
├── 智能合约 (Solidity)
├── 部署网络 (BSC)
└── 合约交互 (Ethers.js)
```

### 4.2 核心合约

```solidity
主要合约：
1. SocialBet.sol - 赌约核心逻辑
2. JudgeDAO.sol - 裁定系统
3. ReputationSystem.sol - 信誉积分
4. RewardDistribution.sol - 奖励分配
```
