# Cloudflare 部署配置指南

本文档说明如何配置 Cloudflare Pages 部署所需的 API 凭证和环境设置。

## 1. 获取 Cloudflare API 凭证

### 1.1 获取 Account ID

1. 登录 [Cloudflare Dashboard](https://dash.cloudflare.com/)
2. 在右侧边栏找到 **Account ID**
3. 复制 Account ID（格式类似：`1234567890abcdef1234567890abcdef`）

### 1.2 创建 API Token

1. 访问 [Cloudflare API Tokens](https://dash.cloudflare.com/profile/api-tokens)
2. 点击 **Create Token**
3. 选择 **Custom token** 模板
4. 配置权限：
   - **Account** - `Cloudflare Pages:Edit`
   - **Zone** - `Zone:Read` (如果使用自定义域名)
   - **Zone Resources** - `Include All zones` 或选择特定域名
5. 点击 **Continue to summary**
6. 点击 **Create Token**
7. 复制生成的 API Token（格式类似：`1234567890abcdef1234567890abcdef12345678`）

## 2. 配置 GitHub Secrets

### 2.1 添加 Repository Secrets

1. 访问 GitHub 仓库页面
2. 点击 **Settings** 标签
3. 在左侧菜单中选择 **Secrets and variables** > **Actions**
4. 点击 **New repository secret**
5. 添加以下 secrets：

```
CLOUDFLARE_API_TOKEN=your_api_token_here
CLOUDFLARE_ACCOUNT_ID=your_account_id_here
```

### 2.2 可选的额外 Secrets

```
# Supabase 配置
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# 认证密钥
JWT_SECRET=your_jwt_secret
NEXTAUTH_SECRET=your_nextauth_secret

# Telegram 配置
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_BOT_USERNAME=your_telegram_bot_username

# 区块链配置
NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID=your_walletconnect_project_id
NEXT_PUBLIC_ALCHEMY_API_KEY=your_alchemy_api_key

# 监控配置
SENTRY_DSN=your_sentry_dsn
```

## 3. 创建 Cloudflare Pages 项目

### 3.1 通过 Dashboard 创建

1. 访问 [Cloudflare Pages](https://pages.cloudflare.com/)
2. 点击 **Create a project**
3. 选择 **Connect to Git**
4. 选择 GitHub 仓库
5. 配置构建设置：
   - **Framework preset**: Next.js
   - **Build command**: `npm run build`
   - **Build output directory**: `out`
   - **Root directory**: `/`

### 3.2 通过 Wrangler CLI 创建

```bash
# 安装 Wrangler CLI
npm install -g wrangler

# 登录 Cloudflare
wrangler login

# 创建 Pages 项目
wrangler pages project create sociomint-production
wrangler pages project create sociomint-staging
```

## 4. 配置环境变量

### 4.1 通过 Dashboard 配置

1. 访问 Cloudflare Pages 项目
2. 点击 **Settings** 标签
3. 选择 **Environment variables**
4. 为 **Production** 和 **Preview** 环境分别添加变量

### 4.2 通过 Wrangler CLI 配置

```bash
# 设置生产环境变量
wrangler pages secret put NODE_ENV --env=production
wrangler pages secret put NEXT_PUBLIC_APP_URL --env=production
wrangler pages secret put JWT_SECRET --env=production

# 设置 staging 环境变量
wrangler pages secret put NODE_ENV --env=staging
wrangler pages secret put NEXT_PUBLIC_APP_URL --env=staging
wrangler pages secret put JWT_SECRET --env=staging
```

### 4.3 使用脚本批量配置

```bash
# 使用项目提供的脚本
chmod +x scripts/setup-cloudflare-env.sh

# 交互式设置生产环境
./scripts/setup-cloudflare-env.sh setup production

# 从文件设置环境变量
./scripts/setup-cloudflare-env.sh setup-from-file production .env.production
```

## 5. 配置自定义域名

### 5.1 添加域名

1. 在 Cloudflare Pages 项目中点击 **Custom domains**
2. 点击 **Set up a custom domain**
3. 输入域名：`sociomint.app`
4. 按照提示配置 DNS 记录

### 5.2 配置 DNS 记录

在 Cloudflare DNS 中添加以下记录：

```
Type: CNAME
Name: @
Content: sociomint-production.pages.dev
Proxy status: Proxied

Type: CNAME
Name: www
Content: sociomint-production.pages.dev
Proxy status: Proxied

Type: CNAME
Name: staging
Content: sociomint-staging.pages.dev
Proxy status: Proxied
```

## 6. 验证部署

### 6.1 检查构建状态

1. 访问 GitHub Actions 页面
2. 查看最新的工作流运行状态
3. 检查构建和部署日志

### 6.2 测试部署的应用

```bash
# 测试生产环境
curl -I https://sociomint.app/api/health

# 测试 staging 环境
curl -I https://staging.sociomint.app/api/health
```

### 6.3 监控部署

1. 设置 Cloudflare Analytics
2. 配置 Uptime 监控
3. 设置错误告警

## 7. 故障排除

### 7.1 常见问题

**构建失败**
- 检查 Node.js 版本兼容性
- 验证环境变量配置
- 查看构建日志中的错误信息

**部署失败**
- 验证 API Token 权限
- 检查 Account ID 是否正确
- 确认项目名称匹配

**运行时错误**
- 检查环境变量是否正确设置
- 验证 Edge Runtime 兼容性
- 查看 Cloudflare 函数日志

### 7.2 调试命令

```bash
# 本地测试 Cloudflare Pages
wrangler pages dev out

# 查看项目信息
wrangler pages project list

# 查看环境变量
wrangler pages secret list --env=production

# 查看部署历史
wrangler pages deployment list
```

## 8. 安全最佳实践

1. **API Token 权限最小化**：只授予必要的权限
2. **定期轮换密钥**：定期更新 API Token 和其他敏感信息
3. **环境隔离**：生产和测试环境使用不同的凭证
4. **监控访问**：启用 Cloudflare 安全监控
5. **备份配置**：定期备份环境变量和配置

## 9. 相关链接

- [Cloudflare Pages 文档](https://developers.cloudflare.com/pages/)
- [Wrangler CLI 文档](https://developers.cloudflare.com/workers/wrangler/)
- [GitHub Actions 文档](https://docs.github.com/en/actions)
- [Next.js 部署文档](https://nextjs.org/docs/deployment)
