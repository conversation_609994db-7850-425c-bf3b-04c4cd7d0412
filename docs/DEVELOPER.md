# SocioMint 开发者文档

本文档为 SocioMint 项目的开发者提供详细的技术指南、架构说明和开发规范。

## 📋 目录

1. [项目概述](#项目概述)
2. [技术栈](#技术栈)
3. [项目架构](#项目架构)
4. [开发环境设置](#开发环境设置)
5. [代码规范](#代码规范)
6. [API 开发](#api-开发)
7. [前端开发](#前端开发)
8. [智能合约](#智能合约)
9. [测试指南](#测试指南)
10. [部署流程](#部署流程)

## 🎯 项目概述

SocioMint 是一个基于区块链的社交挖矿平台，允许用户通过完成社交任务来赚取代币奖励。

### 核心功能

- **用户认证**: 基于 Telegram 的无密码认证
- **钱包管理**: 多链钱包集成和资产管理
- **社交任务**: 任务发布、完成和奖励分发
- **代币预售**: 代币销售和分发机制
- **商户系统**: 商户申请和管理功能

### 技术特点

- **全栈 TypeScript**: 类型安全的开发体验
- **无服务器架构**: 基于 Cloudflare Pages 的边缘计算
- **区块链集成**: 支持 BSC 和其他 EVM 兼容链
- **实时数据**: WebSocket 和 Server-Sent Events
- **高性能**: 优化的构建和缓存策略

## 🛠 技术栈

### 前端技术

```typescript
// 核心框架
- Next.js 14 (App Router)
- React 18
- TypeScript 5.x

// UI 组件
- Tailwind CSS
- Radix UI
- Framer Motion
- Lucide Icons

// 状态管理
- Zustand
- React Query (TanStack Query)

// 区块链集成
- Wagmi
- Viem
- WalletConnect
```

### 后端技术

```typescript
// 运行时
- Cloudflare Workers (Edge Runtime)
- Node.js 18+

// 数据库
- Supabase (PostgreSQL)
- Redis (缓存)

// 认证
- JWT
- Telegram Bot API

// 外部服务
- Alchemy (区块链 RPC)
- Telegram Bot
- SendGrid (邮件)
```

### 开发工具

```bash
# 包管理
npm / yarn / pnpm

# 代码质量
ESLint
Prettier
Husky (Git hooks)

# 测试
Jest
Testing Library
Playwright (E2E)

# 构建和部署
Wrangler CLI
GitHub Actions
```

## 🏗 项目架构

### 目录结构

```
sociomint/
├── src/
│   ├── app/                 # Next.js App Router
│   │   ├── (auth)/         # 认证相关页面
│   │   ├── api/            # API 路由
│   │   ├── dashboard/      # 仪表板页面
│   │   └── globals.css     # 全局样式
│   ├── components/         # React 组件
│   │   ├── ui/            # 基础 UI 组件
│   │   ├── forms/         # 表单组件
│   │   └── layout/        # 布局组件
│   ├── lib/               # 工具库
│   │   ├── auth.ts        # 认证逻辑
│   │   ├── db.ts          # 数据库连接
│   │   └── utils.ts       # 工具函数
│   ├── hooks/             # React Hooks
│   ├── services/          # 业务服务
│   ├── types/             # TypeScript 类型
│   └── config/            # 配置文件
├── docs/                  # 文档
├── scripts/               # 脚本文件
├── tests/                 # 测试文件
└── public/                # 静态资源
```

### 架构图

```mermaid
graph TB
    A[用户] --> B[Next.js 前端]
    B --> C[Cloudflare Pages]
    C --> D[API Routes]
    D --> E[Supabase 数据库]
    D --> F[区块链网络]
    D --> G[Telegram Bot API]
    D --> H[外部服务]
    
    I[管理员] --> J[管理后台]
    J --> D
    
    K[智能合约] --> F
    L[钱包] --> F
```

## 💻 开发环境设置

### 前置要求

```bash
# Node.js 18+
node --version

# npm 9+
npm --version

# Git
git --version
```

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/sociomint/sociomint.git
cd sociomint
```

2. **安装依赖**
```bash
npm install
```

3. **环境配置**
```bash
# 复制环境变量模板
cp .env.example .env.local

# 编辑环境变量
nano .env.local
```

4. **启动开发服务器**
```bash
npm run dev
```

5. **访问应用**
```
http://localhost:3000
```

### 必需的环境变量

```env
# 基础配置
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Supabase
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Telegram Bot
TELEGRAM_BOT_TOKEN=your_bot_token
TELEGRAM_BOT_USERNAME=your_bot_username

# 区块链
NEXT_PUBLIC_CHAIN_ID=97
NEXT_PUBLIC_RPC_URL=https://data-seed-prebsc-1-s1.binance.org:8545/
NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID=your_project_id

# JWT
JWT_SECRET=your_jwt_secret
```

## 📝 代码规范

### TypeScript 规范

```typescript
// 使用严格的类型定义
interface User {
  id: number;
  username: string;
  email?: string;
  createdAt: Date;
}

// 使用泛型提高复用性
interface ApiResponse<T> {
  success: boolean;
  data: T;
  timestamp: string;
}

// 使用枚举定义常量
enum TaskStatus {
  PENDING = 'pending',
  COMPLETED = 'completed',
  FAILED = 'failed',
}
```

### React 组件规范

```typescript
// 使用函数组件和 TypeScript
interface ButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary';
  onClick?: () => void;
  disabled?: boolean;
}

export function Button({ 
  children, 
  variant = 'primary', 
  onClick, 
  disabled = false 
}: ButtonProps) {
  return (
    <button
      className={`btn btn-${variant}`}
      onClick={onClick}
      disabled={disabled}
    >
      {children}
    </button>
  );
}
```

### API 路由规范

```typescript
// 使用统一的错误处理
import { withErrorHandler } from '@/lib/api-error-handler';

const handler = async (request: NextRequest) => {
  // 验证请求
  const body = await request.json();
  
  // 业务逻辑
  const result = await processData(body);
  
  // 返回响应
  return createSuccessResponse(result);
};

export const POST = withErrorHandler(handler);
```

### 命名规范

```typescript
// 文件命名: kebab-case
user-profile.tsx
api-error-handler.ts

// 组件命名: PascalCase
UserProfile
ApiErrorHandler

// 函数命名: camelCase
getUserProfile()
handleApiError()

// 常量命名: SCREAMING_SNAKE_CASE
const API_BASE_URL = 'https://api.example.com';
const MAX_RETRY_COUNT = 3;

// 类型命名: PascalCase
interface UserProfile {}
type ApiResponse<T> = {};
```

## 🔌 API 开发

### API 路由结构

```typescript
// src/app/api/users/route.ts
export async function GET(request: NextRequest) {
  // 获取用户列表
}

export async function POST(request: NextRequest) {
  // 创建新用户
}

// src/app/api/users/[id]/route.ts
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  // 获取特定用户
}
```

### 中间件使用

```typescript
import { 
  withAuth, 
  withRateLimit, 
  withValidation,
  compose 
} from '@/lib/middleware';

const handler = async (request: NextRequest) => {
  // 业务逻辑
};

export const POST = compose(
  withAuth,
  withRateLimit(10, 60000),
  withValidation(userSchema)
)(handler);
```

### 数据验证

```typescript
import { z } from 'zod';

const userSchema = z.object({
  username: z.string().min(3).max(20),
  email: z.string().email().optional(),
  age: z.number().min(18).max(120),
});

type User = z.infer<typeof userSchema>;
```

## 🎨 前端开发

### 组件开发

```typescript
// 使用 Tailwind CSS 和 Radix UI
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';

export function UserCard({ user }: { user: User }) {
  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <h3 className="text-lg font-semibold">{user.username}</h3>
      </CardHeader>
      <CardContent>
        <p className="text-gray-600">{user.email}</p>
        <Button className="mt-4">
          View Profile
        </Button>
      </CardContent>
    </Card>
  );
}
```

### 状态管理

```typescript
// 使用 Zustand
import { create } from 'zustand';

interface UserStore {
  user: User | null;
  setUser: (user: User) => void;
  clearUser: () => void;
}

export const useUserStore = create<UserStore>((set) => ({
  user: null,
  setUser: (user) => set({ user }),
  clearUser: () => set({ user: null }),
}));
```

### 数据获取

```typescript
// 使用 React Query
import { useQuery } from '@tanstack/react-query';

export function useUser(id: string) {
  return useQuery({
    queryKey: ['user', id],
    queryFn: () => fetchUser(id),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}
```

## ⛓ 智能合约

### 合约架构

```solidity
// HAOX Token Contract
contract HAOXToken is ERC20, Ownable {
    uint256 public constant TOTAL_SUPPLY = 1_000_000_000 * 10**18;
    
    constructor() ERC20("HAOX", "HAOX") {
        _mint(msg.sender, TOTAL_SUPPLY);
    }
}

// Presale Contract
contract HAOXPresale {
    HAOXToken public token;
    uint256 public price;
    uint256 public totalSold;
    
    function buyTokens() external payable {
        // 预售逻辑
    }
}
```

### 合约交互

```typescript
// 使用 Wagmi 和 Viem
import { useWriteContract } from 'wagmi';

export function useBuyTokens() {
  const { writeContract } = useWriteContract();
  
  return (amount: bigint) => {
    writeContract({
      address: PRESALE_CONTRACT_ADDRESS,
      abi: presaleAbi,
      functionName: 'buyTokens',
      value: amount,
    });
  };
}
```

## 🧪 测试指南

### 单元测试

```typescript
// 组件测试
import { render, screen } from '@testing-library/react';
import { Button } from '@/components/ui/button';

describe('Button', () => {
  it('renders correctly', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByRole('button')).toBeInTheDocument();
  });
});
```

### API 测试

```typescript
// API 路由测试
import { createMocks } from 'node-mocks-http';
import { GET } from '@/app/api/users/route';

describe('/api/users', () => {
  it('returns users list', async () => {
    const { req } = createMocks({ method: 'GET' });
    const response = await GET(req);
    const data = await response.json();
    
    expect(data.success).toBe(true);
    expect(Array.isArray(data.data)).toBe(true);
  });
});
```

### E2E 测试

```typescript
// Playwright 测试
import { test, expect } from '@playwright/test';

test('user can login with Telegram', async ({ page }) => {
  await page.goto('/login');
  await page.click('[data-testid="telegram-login"]');
  
  // 模拟 Telegram 认证
  await page.waitForURL('/dashboard');
  await expect(page.locator('h1')).toContainText('Dashboard');
});
```

## 🚀 部署流程

### 本地构建

```bash
# 构建应用
npm run build

# 预览构建结果
npm run start
```

### Cloudflare 部署

```bash
# 安装 Wrangler CLI
npm install -g wrangler

# 登录 Cloudflare
wrangler login

# 部署到 staging
wrangler pages deploy out --project-name=sociomint-staging

# 部署到 production
wrangler pages deploy out --project-name=sociomint-production
```

### CI/CD 流程

```yaml
# .github/workflows/deploy.yml
name: Deploy
on:
  push:
    branches: [main, develop]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run build
      - uses: cloudflare/pages-action@v1
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          projectName: sociomint-production
```

## 📚 相关资源

### 官方文档

- [Next.js 文档](https://nextjs.org/docs)
- [React 文档](https://react.dev)
- [TypeScript 文档](https://www.typescriptlang.org/docs)
- [Tailwind CSS 文档](https://tailwindcss.com/docs)

### 工具和库

- [Radix UI](https://www.radix-ui.com)
- [Framer Motion](https://www.framer.com/motion)
- [Wagmi](https://wagmi.sh)
- [Supabase](https://supabase.com/docs)

### 社区资源

- [GitHub 仓库](https://github.com/sociomint/sociomint)
- [开发者论坛](https://forum.sociomint.app)
- [Discord 社区](https://discord.gg/sociomint)

## 🤝 贡献指南

### 提交代码

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

### 代码审查

- 确保所有测试通过
- 遵循代码规范
- 添加必要的文档
- 获得至少一个审查者的批准

### 问题报告

- 使用 GitHub Issues
- 提供详细的重现步骤
- 包含环境信息
- 添加相关标签

---

**版本**: 1.0.0  
**更新时间**: 2024-07-28  
**维护者**: SocioMint 开发团队
