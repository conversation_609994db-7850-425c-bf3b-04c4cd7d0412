# SocioMint 备份和恢复策略

本文档详细说明了 SocioMint 项目的备份策略、恢复流程和灾难恢复计划。

## 📋 目录

1. [备份策略概述](#备份策略概述)
2. [数据分类](#数据分类)
3. [备份方案](#备份方案)
4. [恢复流程](#恢复流程)
5. [灾难恢复](#灾难恢复)
6. [监控和测试](#监控和测试)
7. [安全考虑](#安全考虑)

## 🎯 备份策略概述

### 备份目标

- **RTO (Recovery Time Objective)**: 4小时
- **RPO (Recovery Point Objective)**: 1小时
- **数据保留期**: 90天
- **备份频率**: 每日增量，每周全量
- **异地备份**: 是

### 备份原则

1. **3-2-1 原则**: 3份副本，2种介质，1份异地
2. **自动化**: 所有备份过程自动化
3. **加密**: 所有备份数据加密存储
4. **验证**: 定期验证备份完整性
5. **测试**: 定期进行恢复测试

## 📊 数据分类

### 关键数据 (Critical)

- **用户数据**: 用户账户、认证信息、个人资料
- **交易数据**: 钱包余额、交易记录、预售记录
- **智能合约**: 合约代码、部署记录、配置
- **系统配置**: 环境变量、密钥、证书

**备份频率**: 实时同步 + 每小时快照
**保留期**: 永久保留

### 重要数据 (Important)

- **业务数据**: 商户信息、任务记录、奖励记录
- **用户内容**: 上传文件、头像、文档
- **日志数据**: 应用日志、审计日志
- **监控数据**: 性能指标、错误记录

**备份频率**: 每日备份
**保留期**: 90天

### 一般数据 (Normal)

- **缓存数据**: Redis 缓存、会话数据
- **临时文件**: 上传临时文件、处理中文件
- **统计数据**: 分析报告、统计图表

**备份频率**: 每周备份
**保留期**: 30天

## 🔄 备份方案

### 1. 数据库备份

#### Supabase 数据库

```bash
# 每日全量备份
pg_dump -h db.supabase.co -U postgres -d sociomint_prod > backup_$(date +%Y%m%d).sql

# 每小时增量备份 (WAL)
pg_receivewal -h db.supabase.co -U postgres -D /backup/wal/
```

#### 备份脚本

```bash
#!/bin/bash
# scripts/backup-database.sh

set -e

BACKUP_DIR="/backup/database"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="sociomint_backup_${DATE}.sql"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 执行备份
pg_dump $DATABASE_URL > $BACKUP_DIR/$BACKUP_FILE

# 压缩备份
gzip $BACKUP_DIR/$BACKUP_FILE

# 加密备份
gpg --cipher-algo AES256 --compress-algo 1 --s2k-cipher-algo AES256 \
    --s2k-digest-algo SHA512 --s2k-mode 3 --s2k-count 65536 \
    --symmetric $BACKUP_DIR/$BACKUP_FILE.gz

# 上传到云存储
aws s3 cp $BACKUP_DIR/$BACKUP_FILE.gz.gpg s3://sociomint-backups/database/

# 清理本地文件
rm $BACKUP_DIR/$BACKUP_FILE.gz*

echo "Database backup completed: $BACKUP_FILE"
```

### 2. 文件备份

#### 静态资源备份

```bash
#!/bin/bash
# scripts/backup-assets.sh

ASSETS_DIR="/var/www/sociomint/public"
BACKUP_DIR="/backup/assets"
DATE=$(date +%Y%m%d)

# 同步到备份目录
rsync -av --delete $ASSETS_DIR/ $BACKUP_DIR/current/

# 创建快照
cp -al $BACKUP_DIR/current/ $BACKUP_DIR/snapshots/$DATE/

# 上传到云存储
aws s3 sync $BACKUP_DIR/snapshots/$DATE/ s3://sociomint-backups/assets/$DATE/
```

#### 用户上传文件备份

```bash
#!/bin/bash
# scripts/backup-uploads.sh

UPLOADS_BUCKET="sociomint-uploads"
BACKUP_BUCKET="sociomint-backups"
DATE=$(date +%Y%m%d)

# 同步 S3 存储桶
aws s3 sync s3://$UPLOADS_BUCKET/ s3://$BACKUP_BUCKET/uploads/$DATE/ \
    --storage-class GLACIER
```

### 3. 配置备份

#### 环境变量和配置

```bash
#!/bin/bash
# scripts/backup-config.sh

CONFIG_DIR="/backup/config"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $CONFIG_DIR

# 备份环境变量
echo "# Environment backup - $DATE" > $CONFIG_DIR/env_$DATE.txt
env | grep -E '^(NEXT_|SUPABASE_|TELEGRAM_|JWT_)' >> $CONFIG_DIR/env_$DATE.txt

# 备份 Cloudflare 配置
wrangler pages secret list --env=production > $CONFIG_DIR/cf_secrets_$DATE.txt

# 备份 GitHub Secrets (需要 API token)
# gh secret list --repo sociomint/sociomint > $CONFIG_DIR/gh_secrets_$DATE.txt

# 加密并上传
tar -czf - $CONFIG_DIR | gpg --symmetric | \
    aws s3 cp - s3://sociomint-backups/config/config_$DATE.tar.gz.gpg
```

### 4. 代码备份

#### Git 仓库备份

```bash
#!/bin/bash
# scripts/backup-code.sh

REPO_URL="https://github.com/sociomint/sociomint.git"
BACKUP_DIR="/backup/code"
DATE=$(date +%Y%m%d)

# 克隆完整仓库
git clone --mirror $REPO_URL $BACKUP_DIR/sociomint_$DATE.git

# 打包并上传
tar -czf $BACKUP_DIR/sociomint_$DATE.tar.gz -C $BACKUP_DIR sociomint_$DATE.git
aws s3 cp $BACKUP_DIR/sociomint_$DATE.tar.gz s3://sociomint-backups/code/

# 清理本地文件
rm -rf $BACKUP_DIR/sociomint_$DATE.*
```

## 🔧 恢复流程

### 1. 数据库恢复

#### 完全恢复

```bash
#!/bin/bash
# scripts/restore-database.sh

BACKUP_FILE=$1
RESTORE_DB="sociomint_restore"

if [ -z "$BACKUP_FILE" ]; then
    echo "Usage: $0 <backup_file>"
    exit 1
fi

# 下载备份文件
aws s3 cp s3://sociomint-backups/database/$BACKUP_FILE /tmp/

# 解密备份
gpg --decrypt /tmp/$BACKUP_FILE > /tmp/backup.sql.gz

# 解压备份
gunzip /tmp/backup.sql.gz

# 创建恢复数据库
createdb $RESTORE_DB

# 恢复数据
psql $RESTORE_DB < /tmp/backup.sql

echo "Database restored to: $RESTORE_DB"
```

#### 时间点恢复 (PITR)

```bash
#!/bin/bash
# scripts/restore-pitr.sh

TARGET_TIME=$1
BACKUP_BASE=$2

# 恢复基础备份
pg_basebackup -h localhost -D /var/lib/postgresql/restore

# 配置恢复
cat > /var/lib/postgresql/restore/recovery.conf << EOF
restore_command = 'cp /backup/wal/%f %p'
recovery_target_time = '$TARGET_TIME'
recovery_target_action = 'promote'
EOF

# 启动恢复
pg_ctl -D /var/lib/postgresql/restore start
```

### 2. 应用恢复

#### 快速恢复流程

```bash
#!/bin/bash
# scripts/restore-application.sh

set -e

echo "Starting application recovery..."

# 1. 恢复代码
git clone https://github.com/sociomint/sociomint.git /tmp/sociomint
cd /tmp/sociomint

# 2. 安装依赖
npm ci

# 3. 恢复配置
aws s3 cp s3://sociomint-backups/config/latest.env .env.production

# 4. 构建应用
npm run build

# 5. 部署到 Cloudflare
wrangler pages deploy out --project-name=sociomint-production

echo "Application recovery completed"
```

### 3. 数据验证

```bash
#!/bin/bash
# scripts/validate-restore.sh

# 检查数据库连接
psql $DATABASE_URL -c "SELECT COUNT(*) FROM users;"

# 检查关键表
psql $DATABASE_URL -c "SELECT COUNT(*) FROM transactions WHERE created_at > NOW() - INTERVAL '24 hours';"

# 检查应用健康
curl -f https://sociomint.app/api/health

echo "Restore validation completed"
```

## 🚨 灾难恢复

### 灾难场景分类

#### 1. 数据中心故障

**影响**: 完全服务中断
**RTO**: 4小时
**RPO**: 1小时

**恢复步骤**:
1. 激活备用数据中心
2. 恢复最新数据库备份
3. 重新部署应用
4. 更新 DNS 指向
5. 验证服务功能

#### 2. 数据库损坏

**影响**: 数据访问异常
**RTO**: 2小时
**RPO**: 15分钟

**恢复步骤**:
1. 停止应用服务
2. 评估损坏程度
3. 执行时间点恢复
4. 验证数据完整性
5. 重启应用服务

#### 3. 安全事件

**影响**: 数据泄露风险
**RTO**: 1小时
**RPO**: 0

**恢复步骤**:
1. 立即隔离受影响系统
2. 评估安全影响范围
3. 从干净备份恢复
4. 更新所有密钥和证书
5. 加强安全监控

### 应急联系人

- **技术负责人**: [联系方式]
- **运维团队**: [联系方式]
- **安全团队**: [联系方式]
- **业务负责人**: [联系方式]

## 📈 监控和测试

### 备份监控

```bash
#!/bin/bash
# scripts/monitor-backups.sh

# 检查最新备份时间
LATEST_BACKUP=$(aws s3 ls s3://sociomint-backups/database/ | tail -1 | awk '{print $1" "$2}')
BACKUP_AGE=$(( $(date +%s) - $(date -d "$LATEST_BACKUP" +%s) ))

if [ $BACKUP_AGE -gt 86400 ]; then
    echo "ALERT: Latest backup is older than 24 hours"
    # 发送告警
fi

# 检查备份完整性
aws s3 ls s3://sociomint-backups/ --recursive | wc -l
```

### 恢复测试

```bash
#!/bin/bash
# scripts/test-restore.sh

# 每月执行恢复测试
TEST_DB="sociomint_test_restore"
LATEST_BACKUP=$(aws s3 ls s3://sociomint-backups/database/ | tail -1 | awk '{print $4}')

# 执行测试恢复
./scripts/restore-database.sh $LATEST_BACKUP

# 验证数据
psql $TEST_DB -c "SELECT COUNT(*) FROM users;" > /tmp/test_result.txt

# 清理测试数据库
dropdb $TEST_DB

echo "Restore test completed"
```

## 🔒 安全考虑

### 备份加密

- **传输加密**: 使用 TLS/SSL
- **存储加密**: AES-256 加密
- **密钥管理**: 使用 KMS 或 HSM
- **访问控制**: 最小权限原则

### 合规要求

- **数据保护法规**: GDPR, CCPA
- **金融监管**: 反洗钱、KYC
- **审计要求**: SOX, ISO 27001
- **数据驻留**: 符合当地法规

### 备份审计

```bash
#!/bin/bash
# scripts/audit-backups.sh

# 生成备份审计报告
echo "Backup Audit Report - $(date)" > /tmp/backup_audit.txt
echo "================================" >> /tmp/backup_audit.txt

# 统计备份数量
aws s3 ls s3://sociomint-backups/ --recursive | wc -l >> /tmp/backup_audit.txt

# 检查加密状态
aws s3api head-object --bucket sociomint-backups --key database/latest.sql.gz.gpg | \
    grep ServerSideEncryption >> /tmp/backup_audit.txt

# 发送审计报告
mail -s "Backup Audit Report" <EMAIL> < /tmp/backup_audit.txt
```

---

**最后更新**: 2024-07-28
**文档版本**: 1.0.0
**负责人**: DevOps Team
