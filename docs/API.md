# SocioMint API 文档

本文档详细说明了 SocioMint 项目的 API 接口规范、使用方法和示例。

## 📋 目录

1. [API 概述](#api-概述)
2. [认证机制](#认证机制)
3. [响应格式](#响应格式)
4. [错误处理](#错误处理)
5. [API 端点](#api-端点)
6. [SDK 和工具](#sdk-和工具)

## 🌐 API 概述

### 基础信息

- **Base URL**: `https://sociomint.app/api`
- **Staging URL**: `https://staging.sociomint.app/api`
- **协议**: HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8
- **API 版本**: v1

### 特性

- ✅ RESTful 设计
- ✅ JWT 认证
- ✅ 限流保护
- ✅ 错误处理
- ✅ 请求验证
- ✅ 响应缓存
- ✅ CORS 支持

## 🔐 认证机制

### Telegram 认证

SocioMint 使用 Telegram 作为主要认证方式。

#### 认证流程

```mermaid
sequenceDiagram
    participant C as Client
    participant T as Telegram
    participant A as API
    
    C->>T: 用户点击 Telegram 登录
    T->>C: 返回认证数据
    C->>A: POST /api/auth/telegram
    A->>A: 验证 Telegram 数据
    A->>C: 返回 JWT Token
    C->>A: 使用 Token 访问 API
```

#### 获取认证 Token

```http
POST /api/auth/telegram
Content-Type: application/json

{
  "id": 123456789,
  "first_name": "John",
  "last_name": "Doe",
  "username": "johndoe",
  "photo_url": "https://t.me/i/userpic/320/johndoe.jpg",
  "auth_date": 1640995200,
  "hash": "a1b2c3d4e5f6..."
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "id": 123456789,
    "username": "johndoe",
    "firstName": "John",
    "lastName": "Doe",
    "photoUrl": "https://t.me/i/userpic/320/johndoe.jpg"
  },
  "timestamp": "2024-07-28T10:30:00.000Z"
}
```

#### 验证 Token

```http
GET /api/auth/verify
Cookie: telegram-auth-token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

或

```http
POST /api/auth/verify
Content-Type: application/json

{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### 使用 Token

在需要认证的请求中，通过以下方式之一传递 Token：

1. **Cookie** (推荐):
```http
Cookie: telegram-auth-token=your_jwt_token_here
```

2. **Authorization Header**:
```http
Authorization: Bearer your_jwt_token_here
```

## 📊 响应格式

### 标准响应结构

所有 API 响应都遵循统一的格式：

#### 成功响应

```json
{
  "success": true,
  "data": {
    // 响应数据
  },
  "timestamp": "2024-07-28T10:30:00.000Z"
}
```

#### 错误响应

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": {
      // 错误详情（可选）
    }
  },
  "timestamp": "2024-07-28T10:30:00.000Z",
  "requestId": "req_123456789"
}
```

### 分页响应

对于列表数据，使用分页格式：

```json
{
  "success": true,
  "data": {
    "items": [
      // 数据项
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "totalPages": 5,
      "hasNext": true,
      "hasPrev": false
    }
  },
  "timestamp": "2024-07-28T10:30:00.000Z"
}
```

## ❌ 错误处理

### 错误代码

| 代码 | HTTP状态 | 描述 |
|------|----------|------|
| `UNAUTHORIZED` | 401 | 未授权访问 |
| `TOKEN_EXPIRED` | 401 | Token 已过期 |
| `INVALID_TOKEN` | 401 | 无效的 Token |
| `FORBIDDEN` | 403 | 禁止访问 |
| `BAD_REQUEST` | 400 | 请求参数错误 |
| `VALIDATION_ERROR` | 400 | 数据验证失败 |
| `NOT_FOUND` | 404 | 资源不存在 |
| `METHOD_NOT_ALLOWED` | 405 | 方法不允许 |
| `RATE_LIMIT_EXCEEDED` | 429 | 请求频率超限 |
| `INTERNAL_SERVER_ERROR` | 500 | 服务器内部错误 |
| `DATABASE_ERROR` | 500 | 数据库错误 |
| `EXTERNAL_SERVICE_ERROR` | 503 | 外部服务错误 |

### 错误示例

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "缺少必需字段: email",
    "details": {
      "missingFields": ["email"],
      "field": "email",
      "value": null
    }
  },
  "timestamp": "2024-07-28T10:30:00.000Z",
  "requestId": "req_123456789"
}
```

## 🔗 API 端点

### 认证相关

#### POST /api/auth/telegram
Telegram 用户认证

**请求体**:
```json
{
  "id": 123456789,
  "first_name": "John",
  "username": "johndoe",
  "auth_date": 1640995200,
  "hash": "verification_hash"
}
```

#### GET /api/auth/verify
验证当前认证状态

#### POST /api/auth/logout
用户登出

### 用户相关

#### GET /api/user/profile
获取用户资料

**响应**:
```json
{
  "success": true,
  "data": {
    "id": 123456789,
    "username": "johndoe",
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "photoUrl": "https://example.com/avatar.jpg",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-07-28T10:30:00.000Z"
  }
}
```

#### PUT /api/user/profile
更新用户资料

**请求体**:
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>"
}
```

### 钱包相关

#### GET /api/wallet/balance
获取钱包余额

**响应**:
```json
{
  "success": true,
  "data": {
    "haoxBalance": {
      "available": "1234.56",
      "locked": "100.00",
      "total": "1334.56"
    },
    "bnbBalance": {
      "available": "0.5432",
      "locked": "0.0000",
      "total": "0.5432"
    },
    "usdValue": {
      "haox": 246.91,
      "bnb": 108.64,
      "total": 355.55
    },
    "lastUpdated": "2024-07-28T10:30:00.000Z"
  }
}
```

#### GET /api/wallet/transactions
获取交易记录

**查询参数**:
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 20, 最大: 100)
- `type`: 交易类型 (`deposit`, `withdraw`, `transfer`, `trade`)
- `status`: 交易状态 (`pending`, `completed`, `failed`)
- `startDate`: 开始日期 (ISO 8601)
- `endDate`: 结束日期 (ISO 8601)

**响应**:
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "tx_123456789",
        "type": "deposit",
        "amount": "100.00",
        "currency": "HAOX",
        "status": "completed",
        "hash": "0x1234567890abcdef...",
        "createdAt": "2024-07-28T10:30:00.000Z",
        "completedAt": "2024-07-28T10:35:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 50,
      "totalPages": 3,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

### 预售相关

#### GET /api/presale/info
获取预售信息

**响应**:
```json
{
  "success": true,
  "data": {
    "isActive": true,
    "currentPhase": 1,
    "totalPhases": 5,
    "currentPrice": "0.001",
    "nextPrice": "0.002",
    "tokensRemaining": "1000000",
    "totalRaised": "50000",
    "targetAmount": "100000",
    "startTime": "2024-01-01T00:00:00.000Z",
    "endTime": "2024-12-31T23:59:59.000Z"
  }
}
```

#### POST /api/presale/purchase
参与预售

**请求体**:
```json
{
  "amount": "100.00",
  "currency": "BNB",
  "paymentMethod": "wallet"
}
```

### 任务相关

#### GET /api/tasks
获取任务列表

**查询参数**:
- `status`: 任务状态 (`available`, `completed`, `expired`)
- `category`: 任务分类 (`social`, `trading`, `referral`)
- `page`: 页码
- `limit`: 每页数量

#### POST /api/tasks/:id/complete
完成任务

**请求体**:
```json
{
  "proof": "任务完成证明",
  "metadata": {
    "additional": "data"
  }
}
```


```

### 价格数据

#### GET /api/price-data
获取价格数据

**查询参数**:
- `symbol`: 交易对符号 (如: `HAOX/BNB`)
- `interval`: 时间间隔 (`1m`, `5m`, `1h`, `1d`)
- `limit`: 数据点数量

**响应**:
```json
{
  "success": true,
  "data": {
    "symbol": "HAOX/BNB",
    "price": "0.001234",
    "change24h": "+5.67%",
    "volume24h": "123456.78",
    "high24h": "0.001300",
    "low24h": "0.001100",
    "lastUpdated": "2024-07-28T10:30:00.000Z"
  }
}
```

#### POST /api/price-data
设置价格预警

**请求体**:
```json
{
  "symbol": "HAOX/BNB",
  "targetPrice": "0.002000",
  "alertType": "above"
}
```

### 系统相关

#### GET /api/health
系统健康检查

**响应**:
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": "2024-07-28T10:30:00.000Z",
    "uptime": 86400,
    "version": "1.0.0",
    "environment": "production",
    "responseTime": 45,
    "checks": {
      "database": {
        "status": "healthy",
        "responseTime": 12
      },
      "blockchain": {
        "status": "healthy",
        "responseTime": 234,
        "blockNumber": "0x1234567"
      }
    }
  }
}
```

## 🛠 SDK 和工具

### JavaScript/TypeScript SDK

```bash
npm install @sociomint/sdk
```

```typescript
import { SocioMintSDK } from '@sociomint/sdk';

const sdk = new SocioMintSDK({
  baseURL: 'https://sociomint.app/api',
  apiKey: 'your-api-key'
});

// 获取用户资料
const profile = await sdk.user.getProfile();

// 获取钱包余额
const balance = await sdk.wallet.getBalance();

// 参与预售
const purchase = await sdk.presale.purchase({
  amount: '100.00',
  currency: 'BNB'
});
```

### cURL 示例

```bash
# 获取价格数据
curl -X GET "https://sociomint.app/api/price-data?symbol=HAOX/BNB" \
  -H "Accept: application/json"

# 认证后获取余额
curl -X GET "https://sociomint.app/api/wallet/balance" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Accept: application/json"
```

### Postman 集合

我们提供了完整的 Postman 集合，包含所有 API 端点的示例请求。

[下载 Postman 集合](./postman/SocioMint-API.postman_collection.json)

## 📝 更新日志

### v1.0.0 (2024-07-28)
- 初始 API 版本发布
- 支持 Telegram 认证
- 钱包和交易功能
- 预售和任务系统
- 商户申请功能

---

**最后更新**: 2024-07-28
**API 版本**: v1.0.0
**文档版本**: 1.0.0
