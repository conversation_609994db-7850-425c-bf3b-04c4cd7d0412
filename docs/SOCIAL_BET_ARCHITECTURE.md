# 🎲 Social Bet系统架构文档

## 📋 概述

Social Bet是SocioMint平台的核心功能，基于福气系统实现的去中心化社交赌约平台。支持1v1和1vN两种模式，采用三轮DAO裁定机制，确保公平性和透明度。

## 🏗️ 系统架构

### 核心组件

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   赌约管理系统   │    │   三轮裁定系统   │    │   确认机制系统   │
│                │    │                │    │                │
│ • 创建赌约      │    │ • 资格验证      │    │ • 双方确认      │
│ • 参与投注      │    │ • 投票统计      │    │ • 争议处理      │
│ • 状态管理      │    │ • 奖励分配      │    │ • 自动确认      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   福气结算系统   │
                    │                │
                    │ • 奖池计算      │
                    │ • 批量转账      │
                    │ • 结算记录      │
                    └─────────────────┘
```

## 🗄️ 数据库设计

### 核心表结构

#### 1. social_bets（赌约主表）
```sql
CREATE TABLE social_bets (
    id UUID PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    category VARCHAR(50) NOT NULL,
    bet_type VARCHAR(10) CHECK (bet_type IN ('1v1', '1vN')),
    creator_id UUID REFERENCES users(id),
    options JSONB NOT NULL,
    min_bet_amount DECIMAL(20,8) DEFAULT 10,
    max_bet_amount DECIMAL(20,8),
    total_pool DECIMAL(20,8) DEFAULT 0,
    betting_deadline TIMESTAMP WITH TIME ZONE NOT NULL,
    result_deadline TIMESTAMP WITH TIME ZONE NOT NULL,
    status VARCHAR(20) DEFAULT 'open',
    winning_option VARCHAR(10),
    platform_fee_rate DECIMAL(5,4) DEFAULT 0.05,
    referral_reward_rate DECIMAL(5,4) DEFAULT 0.10,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 2. bet_participants（投注参与记录表）
```sql
CREATE TABLE bet_participants (
    id UUID PRIMARY KEY,
    bet_id UUID REFERENCES social_bets(id),
    user_id UUID REFERENCES users(id),
    selected_option VARCHAR(10) NOT NULL,
    bet_amount DECIMAL(20,8) NOT NULL,
    status VARCHAR(20) DEFAULT 'active',
    payout_amount DECIMAL(20,8) DEFAULT 0,
    is_winner BOOLEAN,
    referrer_id UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(bet_id, user_id)
);
```

#### 3. bet_judgments（裁定投票表）
```sql
CREATE TABLE bet_judgments (
    id UUID PRIMARY KEY,
    bet_id UUID REFERENCES social_bets(id),
    judge_id UUID REFERENCES users(id),
    judgment_round INTEGER CHECK (judgment_round BETWEEN 1 AND 3),
    selected_option VARCHAR(10) NOT NULL,
    confidence_level INTEGER CHECK (confidence_level BETWEEN 1 AND 5),
    reasoning TEXT,
    judge_certification_level INTEGER NOT NULL,
    judge_reputation_score INTEGER NOT NULL,
    reward_amount DECIMAL(20,8) DEFAULT 0,
    is_correct_judgment BOOLEAN,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(bet_id, judge_id, judgment_round)
);
```

#### 4. user_reputation（用户信誉表）
```sql
CREATE TABLE user_reputation (
    user_id UUID PRIMARY KEY REFERENCES users(id),
    certification_level INTEGER DEFAULT 0,
    haox_balance DECIMAL(20,8) DEFAULT 0,
    reputation_score INTEGER DEFAULT 0,
    total_judgments INTEGER DEFAULT 0,
    correct_judgments INTEGER DEFAULT 0,
    accuracy_rate DECIMAL(5,4) DEFAULT 0,
    daily_judgment_count INTEGER DEFAULT 0,
    daily_judgment_limit INTEGER DEFAULT 3,
    current_streak INTEGER DEFAULT 0,
    best_streak INTEGER DEFAULT 0,
    fee_discount_rate DECIMAL(5,4) DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## 🔄 业务流程

### 1. 赌约生命周期

```
创建赌约 → 开放投注 → 投注截止 → 结果公布 → 三轮裁定 → 双方确认 → 福气结算
   ↓           ↓           ↓           ↓           ↓           ↓           ↓
  open    betting_closed  judging   confirming   settled   completed
```

### 2. 三轮裁定机制

| 轮次 | 认证要求 | 每日限额 | 时长 | 共识阈值 |
|------|----------|----------|------|----------|
| 第1轮 | 无要求 | 3-30次 | 24小时 | 60% |
| 第2轮 | 1级以上 | 5-30次 | 24小时 | 60% |
| 第3轮 | 2级以上 | 8-30次 | 24小时 | 最终裁定 |

### 3. 福气流转

```
投注锁定 → 奖池累积 → 手续费扣除 → 获胜者分配
    ↓           ↓           ↓           ↓
  用户福气   总奖池     平台收入    获胜奖励
    ↓           ↓           ↓           ↓
  转发奖励   裁判奖励   信誉积分    等级提升
```

## 🎯 核心功能

### 1. 赌约管理
- **创建赌约**：支持1v1和1vN模式，自定义选项和时间
- **参与投注**：福气投注，实时统计，防重复参与
- **状态管理**：自动状态流转，过期处理

### 2. 三轮裁定
- **资格验证**：认证等级检查，每日次数限制
- **投票机制**：信心等级权重，共识阈值判断
- **奖励分配**：准确性奖励，连胜奖励

### 3. 确认机制
- **双方确认**：创建者和参与者确认结果
- **争议处理**：延长确认期，记录争议理由
- **自动确认**：48小时后自动确认

### 4. 福气结算
- **奖池计算**：扣除手续费、裁判奖励、转发奖励
- **批量转账**：获胜者分配、奖励发放
- **记录管理**：完整的结算日志

## 🔧 技术实现

### API端点

#### 赌约管理
- `POST /api/social-bet/create` - 创建赌约
- `GET /api/social-bet/create/templates` - 获取模板
- `POST /api/social-bet/participate` - 参与投注
- `GET /api/social-bet/bets` - 赌约列表
- `GET /api/social-bet/bets/[id]` - 赌约详情

#### 裁定系统
- `POST /api/social-bet/judgment` - 提交裁定
- `GET /api/social-bet/judgment/eligibility` - 检查资格
- `GET /api/social-bet/judgment/history` - 裁定历史

#### 确认机制
- `POST /api/social-bet/confirm` - 确认结果
- `GET /api/social-bet/confirm/status` - 确认状态

#### 结算系统
- `POST /api/social-bet/settlement` - 手动结算
- `GET /api/social-bet/settlement/stats` - 结算统计

### 核心服务类

#### 1. SocialBetService
- 赌约创建和管理
- 参与投注处理
- 状态更新和统计

#### 2. JudgmentService
- 裁定资格验证
- 投票提交和统计
- 轮次管理和结果计算

#### 3. ConfirmationService
- 确认权限检查
- 争议处理
- 自动确认机制

#### 4. SettlementService
- 奖池计算和分配
- 批量福气转账
- 结算记录管理

## 🛡️ 安全机制

### 1. 防作弊措施
- **利益冲突检测**：参与者不能裁定自己的赌约
- **认证等级要求**：高轮次需要更高认证
- **每日限额控制**：防止刷分行为

### 2. 数据安全
- **行级安全策略**：用户只能访问相关数据
- **事务完整性**：福气转账原子操作
- **审计日志**：完整的操作记录

### 3. 业务规则
- **时间控制**：严格的截止时间管理
- **状态验证**：状态转换规则检查
- **余额验证**：福气余额充足性检查

## 📊 监控指标

### 业务指标
- 活跃赌约数量
- 日投注金额
- 平均结算时间
- 用户参与率

### 技术指标
- API响应时间
- 数据库性能
- 错误率统计
- 系统可用性

### 用户指标
- 新用户注册率
- 用户留存率
- 平均投注金额
- 裁判参与度

## 🚀 部署和维护

### 部署步骤
1. 执行数据库迁移：`005_create_social_bet_system.sql`
2. 部署API服务
3. 更新前端组件
4. 执行系统测试
5. 监控运行状态

### 维护要点
- 定期清理过期数据
- 监控福气流转异常
- 优化查询性能
- 更新裁定规则

## 📝 总结

Social Bet系统通过完整的赌约生命周期管理、公平的三轮裁定机制、灵活的确认流程和自动化的福气结算，为用户提供了一个安全、透明、有趣的社交赌约平台。系统设计充分考虑了安全性、可扩展性和用户体验，为SocioMint平台的核心竞争力奠定了坚实基础。
