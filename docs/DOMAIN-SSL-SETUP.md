# 域名和SSL配置指南

本文档详细说明如何为SocioMint项目配置自定义域名和SSL证书。

## 📋 目录

1. [域名配置](#域名配置)
2. [SSL证书设置](#ssl证书设置)
3. [Cloudflare配置](#cloudflare配置)
4. [DNS设置](#dns设置)
5. [验证和测试](#验证和测试)
6. [故障排除](#故障排除)

## 🌐 域名配置

### 1. 域名选择建议

**推荐域名选项：**
- `sociomint.app` (主域名)
- `app.sociomint.com` (应用子域名)
- `api.sociomint.com` (API子域名)
- `staging.sociomint.com` (测试环境)

### 2. 域名注册

如果还没有域名，推荐的域名注册商：

1. **Cloudflare Registrar** (推荐)
   - 成本价注册
   - 自动集成Cloudflare服务
   - 免费WHOIS隐私保护

2. **Namecheap**
   - 价格合理
   - 良好的客户支持
   - 免费WHOIS隐私保护

3. **Google Domains**
   - 简单易用
   - 与Google服务集成

### 3. 子域名规划

```
主域名: sociomint.app
├── www.sociomint.app (重定向到主域名)
├── app.sociomint.app (主应用)
├── api.sociomint.app (API服务)
├── staging.sociomint.app (测试环境)
├── admin.sociomint.app (管理后台)
└── docs.sociomint.app (文档站点)
```

## 🔒 SSL证书设置

### 1. Cloudflare SSL (推荐)

Cloudflare提供免费的SSL证书，支持通配符证书。

**配置步骤：**

1. 登录Cloudflare控制台
2. 选择你的域名
3. 进入 SSL/TLS 设置
4. 选择加密模式：**Full (strict)**

```yaml
# Cloudflare SSL配置
SSL/TLS:
  加密模式: Full (strict)
  最小TLS版本: 1.2
  TLS 1.3: 启用
  自动HTTPS重写: 启用
  始终使用HTTPS: 启用
```

### 2. Let's Encrypt (备选方案)

如果不使用Cloudflare，可以使用Let's Encrypt免费证书。

**使用Certbot获取证书：**

```bash
# 安装Certbot
sudo apt-get update
sudo apt-get install certbot

# 获取证书
sudo certbot certonly --standalone -d sociomint.app -d www.sociomint.app

# 自动续期
sudo crontab -e
# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

### 3. 通配符证书

对于多个子域名，推荐使用通配符证书：

```bash
# 使用DNS验证获取通配符证书
sudo certbot certonly \
  --manual \
  --preferred-challenges=dns \
  --email <EMAIL> \
  --server https://acme-v02.api.letsencrypt.org/directory \
  --agree-tos \
  -d *.sociomint.app \
  -d sociomint.app
```

## ☁️ Cloudflare配置

### 1. 添加站点到Cloudflare

1. 登录 [Cloudflare控制台](https://dash.cloudflare.com)
2. 点击 "添加站点"
3. 输入域名：`sociomint.app`
4. 选择计划（免费计划足够）
5. 等待DNS扫描完成

### 2. 更新域名服务器

将域名的DNS服务器更改为Cloudflare提供的服务器：

```
示例Cloudflare DNS服务器：
- ava.ns.cloudflare.com
- bob.ns.cloudflare.com
```

### 3. Cloudflare Pages配置

**连接GitHub仓库：**

1. 进入Cloudflare Pages
2. 点击 "创建项目"
3. 连接GitHub账户
4. 选择SocioMint仓库
5. 配置构建设置：

```yaml
构建配置:
  框架预设: Next.js
  构建命令: npm run build
  构建输出目录: .next
  根目录: /
  环境变量: 
    - NODE_VERSION: 18
    - NEXT_PUBLIC_SITE_URL: https://sociomint.app
```

### 4. 自定义域名设置

1. 在Cloudflare Pages项目中
2. 进入 "自定义域名" 选项卡
3. 添加域名：`sociomint.app`
4. 添加www重定向：`www.sociomint.app`

## 🌍 DNS设置

### 1. DNS记录配置

在Cloudflare DNS管理中添加以下记录：

```dns
# A记录 - 主域名
Type: A
Name: @
Content: [Cloudflare Pages IP]
Proxy: 已代理 (橙色云朵)

# CNAME记录 - www重定向
Type: CNAME
Name: www
Content: sociomint.app
Proxy: 已代理

# CNAME记录 - API子域名
Type: CNAME
Name: api
Content: sociomint.app
Proxy: 已代理

# CNAME记录 - 测试环境
Type: CNAME
Name: staging
Content: sociomint.app
Proxy: 已代理

# MX记录 - 邮件服务（可选）
Type: MX
Name: @
Content: mx1.forwardemail.net
Priority: 10
```

### 2. 页面规则设置

在Cloudflare中设置页面规则：

```yaml
规则1 - HTTPS重定向:
  URL: http://*sociomint.app/*
  设置: 始终使用HTTPS

规则2 - WWW重定向:
  URL: www.sociomint.app/*
  设置: 转发URL (301重定向)
  目标: https://sociomint.app/$1

规则3 - 缓存设置:
  URL: sociomint.app/_next/static/*
  设置: 
    - 缓存级别: 缓存所有内容
    - 边缘缓存TTL: 1年
```

## ✅ 验证和测试

### 1. SSL证书验证

使用在线工具验证SSL配置：

```bash
# 命令行验证
openssl s_client -connect sociomint.app:443 -servername sociomint.app

# 或使用curl
curl -I https://sociomint.app
```

**在线验证工具：**
- [SSL Labs](https://www.ssllabs.com/ssltest/)
- [SSL Checker](https://www.sslshopper.com/ssl-checker.html)

### 2. DNS传播检查

验证DNS记录是否全球传播：

```bash
# 检查DNS记录
nslookup sociomint.app
dig sociomint.app

# 检查不同地区的DNS
dig @8.8.8.8 sociomint.app
dig @1.1.1.1 sociomint.app
```

**在线检查工具：**
- [DNS Checker](https://dnschecker.org/)
- [What's My DNS](https://www.whatsmydns.net/)

### 3. 性能测试

测试网站性能和可用性：

```bash
# 使用curl测试响应时间
curl -w "@curl-format.txt" -o /dev/null -s https://sociomint.app

# curl-format.txt内容：
#      time_namelookup:  %{time_namelookup}\n
#         time_connect:  %{time_connect}\n
#      time_appconnect:  %{time_appconnect}\n
#     time_pretransfer:  %{time_pretransfer}\n
#        time_redirect:  %{time_redirect}\n
#   time_starttransfer:  %{time_starttransfer}\n
#                     ----------\n
#           time_total:  %{time_total}\n
```

**在线性能测试：**
- [GTmetrix](https://gtmetrix.com/)
- [PageSpeed Insights](https://pagespeed.web.dev/)
- [WebPageTest](https://www.webpagetest.org/)

## 🔧 故障排除

### 1. 常见问题

**问题1：SSL证书错误**
```
错误：NET::ERR_CERT_AUTHORITY_INVALID
解决：检查Cloudflare SSL模式，确保设置为"Full (strict)"
```

**问题2：DNS解析失败**
```
错误：DNS_PROBE_FINISHED_NXDOMAIN
解决：检查DNS记录配置，等待DNS传播完成（最多48小时）
```

**问题3：重定向循环**
```
错误：ERR_TOO_MANY_REDIRECTS
解决：检查Cloudflare SSL模式和页面规则配置
```

### 2. 调试命令

```bash
# 检查域名解析
nslookup sociomint.app

# 检查SSL证书
openssl s_client -connect sociomint.app:443 -servername sociomint.app

# 检查HTTP头
curl -I https://sociomint.app

# 跟踪路由
traceroute sociomint.app

# 检查端口连通性
telnet sociomint.app 443
```

### 3. 监控设置

设置域名和SSL监控：

```yaml
监控项目:
  - SSL证书到期时间
  - 域名解析状态
  - 网站可用性
  - 响应时间
  - SSL评级

推荐监控服务:
  - UptimeRobot (免费)
  - Pingdom
  - StatusCake
  - Cloudflare Analytics
```

## 📝 配置清单

完成以下检查项确保配置正确：

- [ ] 域名已注册并指向Cloudflare DNS
- [ ] Cloudflare Pages项目已创建并连接GitHub
- [ ] 自定义域名已添加到Cloudflare Pages
- [ ] SSL证书已配置（Full strict模式）
- [ ] DNS记录已正确设置
- [ ] 页面规则已配置（HTTPS重定向、WWW重定向）
- [ ] SSL证书验证通过（A+评级）
- [ ] DNS传播已完成
- [ ] 网站可正常访问
- [ ] 性能测试通过
- [ ] 监控已设置

## 🚀 部署后验证

域名和SSL配置完成后，进行最终验证：

1. **访问测试**
   - https://sociomint.app ✅
   - https://www.sociomint.app (应重定向到主域名) ✅
   - http://sociomint.app (应重定向到HTTPS) ✅

2. **功能测试**
   - 用户注册/登录 ✅
   - 钱包连接 ✅
   - API调用 ✅
   - 页面加载速度 ✅

3. **安全测试**
   - SSL评级 A+ ✅
   - 安全头检查 ✅
   - 混合内容检查 ✅

配置完成后，你的SocioMint应用将拥有专业的域名和企业级的SSL安全保护！
