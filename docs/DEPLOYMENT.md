# SocioMint 部署指南

本文档详细说明了 SocioMint 项目的部署流程，包括开发、测试和生产环境的配置。

## 📋 目录

1. [环境概述](#环境概述)
2. [前置要求](#前置要求)
3. [本地开发环境](#本地开发环境)
4. [Staging 环境部署](#staging-环境部署)
5. [生产环境部署](#生产环境部署)
6. [CI/CD 流程](#cicd-流程)
7. [监控和维护](#监控和维护)
8. [故障排除](#故障排除)

## 🌍 环境概述

### 环境架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Development   │    │     Staging     │    │   Production    │
│  localhost:3000 │    │ staging.*.app   │    │  sociomint.app  │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ • 本地开发      │    │ • 预发布测试    │    │ • 正式生产      │
│ • 热重载       │    │ • 集成测试      │    │ • 高可用性      │
│ • 调试工具     │    │ • 性能测试      │    │ • 监控告警      │
│ • Mock 数据    │    │ • 用户验收测试  │    │ • 备份恢复      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 环境特性对比

| 特性 | Development | Staging | Production |
|------|-------------|---------|------------|
| 数据库 | 本地/测试 | 测试数据库 | 生产数据库 |
| 区块链网络 | 本地/测试网 | BSC 测试网 | BSC 主网 |
| 缓存 | 内存 | Redis | Redis 集群 |
| 监控 | 基础 | 完整 | 完整+告警 |
| 日志级别 | Debug | Info | Warn/Error |
| 性能优化 | 否 | 部分 | 完整 |

## 🔧 前置要求

### 系统要求

- **Node.js**: 18.x 或更高版本
- **npm**: 9.x 或更高版本
- **Git**: 最新版本
- **Wrangler CLI**: 3.x 或更高版本

### 账户和服务

- [x] GitHub 账户（代码托管）
- [x] Cloudflare 账户（部署和 CDN）
- [x] Supabase 账户（数据库）
- [x] Telegram Bot（用户认证）
- [x] WalletConnect 项目（钱包连接）
- [x] Alchemy 账户（区块链 RPC）

### 安装工具

```bash
# 安装 Node.js (使用 nvm)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 18
nvm use 18

# 安装 Wrangler CLI
npm install -g wrangler

# 验证安装
node --version
npm --version
wrangler --version
```

## 💻 本地开发环境

### 1. 克隆项目

```bash
git clone https://github.com/your-org/sociomint.git
cd sociomint
```

### 2. 安装依赖

```bash
npm install
```

### 3. 配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env.local

# 编辑环境变量
nano .env.local
```

必需的环境变量：
```env
# 基础配置
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Supabase 配置
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# 区块链配置
NEXT_PUBLIC_CHAIN_ID=97
NEXT_PUBLIC_RPC_URL=https://data-seed-prebsc-1-s1.binance.org:8545/

# Telegram Bot
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
```

### 4. 启动开发服务器

```bash
npm run dev
```

访问 http://localhost:3000 查看应用。

### 5. 开发工具

```bash
# 代码检查
npm run lint

# 类型检查
npm run type-check

# 运行测试
npm run test

# 性能测试
npm run perf:test
```

## 🧪 Staging 环境部署

### 1. 准备 Staging 配置

```bash
# 使用环境管理脚本
node scripts/manage-environments.js validate staging

# 或手动检查配置文件
cat .env.staging
```

### 2. 构建应用

```bash
# 设置环境
export NODE_ENV=staging

# 构建
npm run build
```

### 3. 部署到 Cloudflare Pages

#### 方法 1: 自动部署（推荐）

推送到 `develop` 分支会自动触发 staging 部署：

```bash
git checkout develop
git add .
git commit -m "feat: 新功能"
git push origin develop
```

#### 方法 2: 手动部署

```bash
# 使用 Wrangler CLI
wrangler pages deploy out --project-name=sociomint-staging

# 或使用环境管理脚本
node scripts/manage-environments.js deploy staging
```

### 4. 验证部署

```bash
# 健康检查
curl -I https://staging.sociomint.app/api/health

# 功能测试
npm run test:e2e -- --baseUrl=https://staging.sociomint.app
```

## 🚀 生产环境部署

### 1. 预部署检查

```bash
# 验证生产配置
node scripts/manage-environments.js validate production

# 比较 staging 和 production 配置
node scripts/manage-environments.js compare staging production

# 运行完整测试套件
npm run test:ci
npm run test:e2e
npm run perf:test
```

### 2. 安全检查

```bash
# 依赖安全审计
npm audit --audit-level=moderate

# 代码安全扫描
npm run security:scan

# 环境变量检查
node scripts/validate-env.js production
```

### 3. 构建生产版本

```bash
# 设置生产环境
export NODE_ENV=production

# 清理缓存
npm run clean

# 构建
npm run build:production

# 验证构建
npm run build:analyze
```

### 4. 部署到生产环境

#### 方法 1: 自动部署（推荐）

合并到 `main` 分支会自动触发生产部署：

```bash
# 创建发布分支
git checkout -b release/v1.0.0

# 更新版本号
npm version patch

# 合并到 main
git checkout main
git merge release/v1.0.0
git push origin main

# 创建 GitHub Release
gh release create v1.0.0 --title "Release v1.0.0" --notes "发布说明"
```

#### 方法 2: 手动部署

```bash
# 使用 Wrangler CLI
wrangler pages deploy out --project-name=sociomint-production

# 或使用环境管理脚本
node scripts/manage-environments.js deploy production
```

### 5. 部署后验证

```bash
# 健康检查
curl -I https://sociomint.app/api/health

# 功能验证
npm run test:smoke -- --baseUrl=https://sociomint.app

# 性能检查
npm run perf:check -- --url=https://sociomint.app
```

## 🔄 CI/CD 流程

### GitHub Actions 工作流

```yaml
# .github/workflows/deploy.yml
name: Deploy to Cloudflare Pages

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
      - run: npm ci
      - run: npm run lint
      - run: npm run test:ci
      - run: npm run build

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.event_name == 'push'
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
      - run: npm ci
      - run: npm run build
      - uses: cloudflare/pages-action@v1
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          projectName: ${{ github.ref == 'refs/heads/main' && 'sociomint-production' || 'sociomint-staging' }}
          directory: out
```

### 部署策略

1. **功能分支** → 开发环境（本地）
2. **develop 分支** → Staging 环境
3. **main 分支** → 生产环境

### 发布流程

```bash
# 1. 功能开发
git checkout -b feature/new-feature
# ... 开发和测试
git push origin feature/new-feature

# 2. 合并到 develop
git checkout develop
git merge feature/new-feature
git push origin develop  # 自动部署到 staging

# 3. 测试验收
# 在 staging 环境进行测试

# 4. 发布到生产
git checkout main
git merge develop
git push origin main     # 自动部署到 production
```

## 📊 监控和维护

### 监控指标

- **性能指标**: 页面加载时间、API 响应时间
- **错误监控**: 错误率、异常追踪
- **业务指标**: 用户活跃度、交易量
- **基础设施**: CPU、内存、网络使用率

### 日志管理

```bash
# 查看 Cloudflare 日志
wrangler pages deployment tail --project-name=sociomint-production

# 查看错误日志
wrangler pages functions logs --project-name=sociomint-production
```

### 备份策略

- **数据库备份**: 每日自动备份
- **配置备份**: 版本控制管理
- **静态资源**: CDN 多地域备份

## 🔧 故障排除

### 常见问题

#### 构建失败

```bash
# 清理缓存
npm run clean
rm -rf .next node_modules
npm install

# 检查依赖
npm audit fix
npm update
```

#### 部署失败

```bash
# 检查 Cloudflare 配置
wrangler pages project list
wrangler pages deployment list

# 验证环境变量
node scripts/manage-environments.js validate production
```

#### 运行时错误

```bash
# 查看日志
wrangler pages functions logs --project-name=sociomint-production

# 检查健康状态
curl -v https://sociomint.app/api/health
```

### 回滚策略

```bash
# 回滚到上一个版本
wrangler pages deployment list --project-name=sociomint-production
wrangler pages deployment rollback <deployment-id> --project-name=sociomint-production
```

### 紧急联系

- **技术负责人**: [联系方式]
- **运维团队**: [联系方式]
- **Cloudflare 支持**: [支持渠道]

## 📚 相关文档

- [Cloudflare 配置指南](./CLOUDFLARE-SETUP.md)
- [环境变量配置](./ENVIRONMENT-VARIABLES.md)
- [安全配置指南](./SECURITY.md)
- [性能优化指南](./PERFORMANCE.md)
- [API 文档](./API.md)

---

**最后更新**: 2024-07-28
**文档版本**: 1.0.0
