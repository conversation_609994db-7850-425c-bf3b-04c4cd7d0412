# 组件开发最佳实践指南

## 🎯 目标

确保组件的TypeScript接口定义与实际实现完全一致，避免声明了属性但未实现的问题。

## 🚨 常见问题

### 问题1：接口与实现不匹配

```typescript
// ❌ 错误示例
interface ButtonProps {
  href?: string;    // 声明了但未实现
  target?: string;  // 声明了但未实现
  onClick?: () => void;
}

const Button = ({ onClick }: ButtonProps) => {
  // href和target被忽略！
  return <button onClick={onClick}>Click me</button>;
};
```

```typescript
// ✅ 正确示例
interface ButtonProps {
  href?: string;
  target?: string;
  onClick?: () => void;
}

const Button = ({ href, target, onClick }: ButtonProps) => {
  if (href) {
    return <a href={href} target={target}>Click me</a>;
  }
  return <button onClick={onClick}>Click me</button>;
};
```

## 📋 开发检查清单

### 1. 组件设计阶段

- [ ] 明确组件的所有功能需求
- [ ] 设计完整的属性接口
- [ ] 考虑属性的可选性和默认值
- [ ] 规划属性的类型定义

### 2. 实现阶段

- [ ] 确保所有声明的属性都在组件中被使用
- [ ] 为可选属性提供合理的默认值
- [ ] 处理属性的边界情况
- [ ] 添加适当的类型检查

### 3. 验证阶段

- [ ] 使用开发工具验证属性一致性
- [ ] 编写单元测试覆盖所有属性
- [ ] 进行代码审查检查
- [ ] 运行ESLint规则检查

## 🛠️ 开发工具

### 1. 属性验证装饰器

```typescript
import { validateComponentProps } from '@/lib/dev-tools/component-validator';

interface ButtonProps {
  variant?: string;
  href?: string;
  onClick?: () => void;
}

// 在开发环境中自动验证属性一致性
const Button = validateComponentProps<ButtonProps>(
  'Button',
  ['variant', 'href', 'onClick']
)(({ variant, href, onClick }: ButtonProps) => {
  // 组件实现
});
```

### 2. 运行时检查Hook

```typescript
import { usePropsValidation } from '@/lib/dev-tools/component-validator';

const Button = (props: ButtonProps) => {
  // 开发环境中自动检查属性
  usePropsValidation('Button', props, ['variant', 'href', 'onClick']);
  
  // 组件实现
};
```

### 3. ESLint规则

```javascript
// .eslintrc.js
module.exports = {
  rules: {
    'custom/component-props-consistency': ['error', {
      ignoredProps: ['children', 'className', 'key', 'ref']
    }]
  }
};
```

## 📝 组件模板

### 基础组件模板

```typescript
'use client';

import React, { memo, useMemo } from 'react';
import { cn } from '@/lib/utils';

// 1. 定义完整的属性接口
interface ComponentProps {
  // 必需属性
  children: React.ReactNode;
  
  // 可选属性
  variant?: 'primary' | 'secondary';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  className?: string;
  
  // 事件处理
  onClick?: () => void;
  
  // 特殊属性
  href?: string;
  target?: string;
}

// 2. 实现组件，确保处理所有声明的属性
const Component: React.FC<ComponentProps> = memo(({
  children,
  variant = 'primary',
  size = 'md',
  disabled = false,
  className,
  onClick,
  href,
  target,
  ...props
}) => {
  // 3. 计算样式
  const classes = useMemo(() => cn(
    'base-classes',
    `variant-${variant}`,
    `size-${size}`,
    { 'disabled': disabled },
    className
  ), [variant, size, disabled, className]);

  // 4. 处理不同的渲染逻辑
  if (href) {
    return (
      <a
        href={href}
        target={target}
        className={classes}
        {...props}
      >
        {children}
      </a>
    );
  }

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={classes}
      {...props}
    >
      {children}
    </button>
  );
});

// 5. 设置显示名称
Component.displayName = 'Component';

export default Component;
```

## 🧪 测试策略

### 1. 属性覆盖测试

```typescript
describe('Component Props', () => {
  test('should handle all declared props', () => {
    const props: ComponentProps = {
      children: 'Test',
      variant: 'primary',
      size: 'md',
      disabled: false,
      href: '/test',
      target: '_blank',
      onClick: jest.fn()
    };

    render(<Component {...props} />);
    
    // 验证每个属性都被正确处理
    expect(screen.getByRole('link')).toHaveAttribute('href', '/test');
    expect(screen.getByRole('link')).toHaveAttribute('target', '_blank');
  });

  test('should render as button when no href', () => {
    render(
      <Component onClick={jest.fn()}>
        Test Button
      </Component>
    );
    
    expect(screen.getByRole('button')).toBeInTheDocument();
  });
});
```

### 2. 类型安全测试

```typescript
// 编译时类型检查
const validProps: ComponentProps = {
  children: 'Test',
  variant: 'primary', // ✅ 有效值
  // href: 123,       // ❌ 类型错误
};
```

## 🔄 持续改进

### 1. 定期审查

- 每月检查组件属性一致性
- 更新开发工具和规则
- 收集开发者反馈

### 2. 自动化检查

- CI/CD中集成属性检查
- 提交前运行验证脚本
- 自动生成属性一致性报告

### 3. 团队培训

- 定期分享最佳实践
- 代码审查重点关注属性一致性
- 建立组件开发标准

## 📚 相关资源

- [TypeScript接口设计指南](./TYPESCRIPT_INTERFACES.md)
- [组件测试最佳实践](./COMPONENT_TESTING.md)
- [ESLint规则配置](./ESLINT_SETUP.md)
- [开发工具使用指南](./DEV_TOOLS.md)
