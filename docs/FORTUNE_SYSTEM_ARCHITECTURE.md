# 🍀 福气系统架构文档

## 📋 概述

福气系统是SocioMint平台的核心奖励机制，将原有的HAOX代币奖励系统升级为更具中国文化特色的"福气"概念。本系统与现有Supabase数据库结构完全兼容，采用扩展而非重建的方式实现。

## 🏗️ 数据库架构

### 现有表扩展

#### 1. users表扩展
在现有的`users`表中添加福气相关字段：

```sql
-- 福气余额字段
available_fortune DECIMAL(20,8) DEFAULT 0 NOT NULL
locked_fortune DECIMAL(20,8) DEFAULT 0 NOT NULL

-- 福气统计字段  
total_fortune_earned DECIMAL(20,8) DEFAULT 0 NOT NULL
total_fortune_spent DECIMAL(20,8) DEFAULT 0 NOT NULL

-- 福气等级字段
fortune_level INTEGER DEFAULT 1 NOT NULL
fortune_level_name VARCHAR(20) DEFAULT '初来乍到' NOT NULL

-- 签到相关字段
consecutive_checkin_days INTEGER DEFAULT 0 NOT NULL
last_checkin_date DATE
total_checkin_days INTEGER DEFAULT 0 NOT NULL
```

#### 2. invitation_stats表扩展
在现有的邀请统计表中添加福气奖励处理标记：

```sql
-- 福气奖励处理标记
fortune_reward_processed BOOLEAN DEFAULT FALSE
fortune_reward_amount DECIMAL(20,8) DEFAULT 0
```

### 新增表结构

#### 1. fortune_transactions（福气交易记录表）
```sql
CREATE TABLE fortune_transactions (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES users(id),
    transaction_type VARCHAR(30) NOT NULL,
    amount DECIMAL(20,8) NOT NULL,
    balance_before DECIMAL(20,8) NOT NULL,
    balance_after DECIMAL(20,8) NOT NULL,
    reference_id UUID,
    reference_type VARCHAR(30),
    description TEXT NOT NULL,
    haox_tx_hash VARCHAR(66),
    haox_amount DECIMAL(20,8),
    exchange_rate DECIMAL(10,6) DEFAULT 1.0,
    created_at TIMESTAMP DEFAULT NOW()
);
```

#### 2. daily_checkins（每日签到记录表）
```sql
CREATE TABLE daily_checkins (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES users(id),
    checkin_date DATE NOT NULL,
    consecutive_days INTEGER NOT NULL,
    base_reward DECIMAL(20,8) NOT NULL,
    bonus_reward DECIMAL(20,8) DEFAULT 0,
    total_reward DECIMAL(20,8) NOT NULL,
    fortune_transaction_id UUID REFERENCES fortune_transactions(id),
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(user_id, checkin_date)
);
```

#### 3. share_rewards（分享奖励记录表）
```sql
CREATE TABLE share_rewards (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES users(id),
    shared_content_type VARCHAR(30) NOT NULL,
    shared_content_id UUID NOT NULL,
    platform VARCHAR(20) NOT NULL,
    reward_amount DECIMAL(20,8) NOT NULL,
    fortune_transaction_id UUID REFERENCES fortune_transactions(id),
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(user_id, shared_content_id, platform)
);
```

#### 4. fortune_levels（福气等级配置表）
```sql
CREATE TABLE fortune_levels (
    level INTEGER PRIMARY KEY,
    level_name VARCHAR(20) NOT NULL,
    min_fortune DECIMAL(20,8) NOT NULL,
    max_fortune DECIMAL(20,8),
    benefits JSONB,
    created_at TIMESTAMP DEFAULT NOW()
);
```

## 🎯 福气奖励机制

### 奖励类型和数值

| 奖励类型 | 福气数量 | 说明 |
|---------|---------|------|
| 邀请好友 | 1000福气 | 每成功邀请1人 |
| 邀请里程碑5人 | 10000福气 | 累计邀请5人奖励 |
| 邀请里程碑10人 | 50000福气 | 累计邀请10人奖励 |
| 每日签到 | 10福气 | 基础签到奖励 |
| 连续签到奖励 | +5福气 | 连续7天额外奖励 |
| 分享内容 | 20福气 | 每次分享到社交平台 |
| 参与讨论 | 5福气 | 发表有效评论 |
| 新用户注册 | 50福气 | 一次性注册奖励 |
| 首次充值 | 100福气 | 一次性首充奖励 |

### 福气等级系统

| 等级 | 名称 | 福气要求 | 权益 |
|-----|------|---------|------|
| 1 | 初来乍到 | 0-999 | 基础功能使用权 |
| 2 | 小有福气 | 1000-9999 | 基础功能 + 优先客服支持 |
| 3 | 福气满满 | 10000-99999 | 上述权益 + 可发起1vN赌约 |
| 4 | 福星高照 | 100000-999999 | 上述权益 + 专属客服通道 |
| 5 | 福气无边 | 1000000+ | 上述权益 + 内测新功能权限 |

## 🔧 技术实现

### 核心服务类

#### 1. FortuneService
- 福气账户管理
- 福气余额操作
- 福气等级计算
- 邀请奖励处理

#### 2. FortuneRewardService  
- 每日签到处理
- 分享奖励处理
- 评论奖励处理
- 新用户奖励处理

### API端点

#### 1. 每日签到API
- `POST /api/fortune/daily-checkin` - 执行签到
- `GET /api/fortune/daily-checkin` - 获取签到状态

#### 2. 邀请奖励API
- `POST /api/fortune/invite-reward` - 处理邀请奖励
- `GET /api/fortune/invite-reward` - 获取邀请统计

#### 3. 分享奖励API
- `POST /api/fortune/share-reward` - 处理分享奖励
- `GET /api/fortune/share-reward` - 获取分享统计

### 前端组件

#### 1. FortuneCenter
- 福气账户概览
- 多标签页设计（概览、签到、邀请、分享）
- 福气等级展示
- 奖励历史记录

#### 2. React Hooks
- `useFortune` - 福气账户管理
- `useFortuneRewards` - 福气奖励操作
- `useFortuneRewardsCompat` - 兼容性Hook

## 🔄 数据迁移策略

### 兼容性设计
1. **扩展现有表** - 不删除或重建现有表结构
2. **保持API兼容** - 原有接口继续工作
3. **渐进式迁移** - 新功能逐步替换旧功能
4. **数据一致性** - 确保HAOX奖励和福气奖励的一致性

### 迁移脚本
- `scripts/migrate-to-fortune-system.ts` - 数据迁移脚本
- `scripts/test-fortune-system.ts` - 功能测试脚本

## 🛡️ 安全和性能

### 数据安全
- 行级安全策略（RLS）
- 用户只能访问自己的福气数据
- 防止重复奖励的唯一约束

### 性能优化
- 适当的数据库索引
- 批量操作优化
- 缓存策略

### 防刷机制
- 每日签到限制
- 分享内容去重
- 评论质量检查
- 邀请关系验证

## 📊 监控和分析

### 关键指标
- 用户福气余额分布
- 每日签到率
- 邀请转化率
- 分享活跃度
- 福气等级分布

### 数据分析
- 用户行为分析
- 奖励效果评估
- 系统健康监控

## 🚀 部署和维护

### 部署步骤
1. 运行数据库迁移脚本
2. 部署新的API端点
3. 更新前端组件
4. 执行功能测试
5. 监控系统运行

### 维护要点
- 定期备份福气数据
- 监控奖励发放异常
- 优化查询性能
- 更新福气等级配置

## 📝 总结

福气系统通过扩展现有数据库结构，实现了与原有系统的完全兼容，同时提供了更丰富的奖励机制和用户体验。系统设计考虑了安全性、性能和可维护性，为SocioMint平台的长期发展奠定了坚实基础。
