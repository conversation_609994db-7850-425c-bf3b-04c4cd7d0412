# Staging环境部署指南

本文档详细说明如何部署SocioMint项目到staging环境进行测试。

## 🚨 重要通知: V2.1安全版本已发布 (2025-01-30)

### 🛡️ 强烈建议升级到V2.1安全版本
V2.1安全版本已完成5个中危漏洞的修复，将系统安全等级从中等风险提升到低风险。

**V2.1安全版本部署文档**: [`docs/STAGING-DEPLOYMENT-V2.1-SECURE.md`](./STAGING-DEPLOYMENT-V2.1-SECURE.md)

### 🔄 升级路径
- **当前V2用户**: 可继续使用，但建议尽快升级到V2.1
- **新部署**: 直接使用V2.1安全版本
- **生产环境**: 必须使用V2.1安全版本

---

## 📋 V2合约重构更新 (2025-01-29) - 历史版本

### 重要变更
- 完成了智能合约系统的全面重构，从V1升级到V2
- 新增动态定价算法、邀请系统优化、解锁机制改进
- 清理了所有V1合约文件和相关代码
- 更新了环境变量配置

### V2合约地址 (BSC测试网) - ✅ 全部部署完成
- **HAOXTokenV2**: `0x220C8116EC93D7894968d7CC662ab80Db80E7aF3` ✅
- **HAOXPresaleV2**: `0x232Cb2986CB0B7FE8f4f4329Eb47B78df4cBeF22` ✅
- **HAOXInvitationV2**: `0x330E44Fa889F8AD493437d4854c2e1A6545CdE03` ✅
- **HAOXPriceOracleV2**: `0xe6940f9FE1948FCE67862F818a490d398843b632` ✅
- **HAOXVestingV2**: `0x254024A7388f9812dA8B40847234ccE6A9E81788` ✅

### 部署成本统计
- **HAOXTokenV2**: 1,408,423 gas (0.01408423 BNB)
- **HAOXPriceOracleV2**: 1,130,300 gas (0.011303 BNB)
- **HAOXInvitationV2**: 1,992,232 gas (0.01992232 BNB)
- **HAOXPresaleV2**: 1,184,825 gas (0.01184825 BNB)
- **HAOXVestingV2**: 4,694,926 gas (0.04694926 BNB)
- **总计**: 10,410,706 gas (0.10410706 BNB ≈ $88.5 USD)

### 功能验证状态
- ✅ 代币合约基础功能正常
- ✅ 预售合约状态查询正常
- ✅ 邀请奖励参数正确
- ⚠️ 价格预言机需要设置HAOX/BNB交易对
- ✅ 解锁合约参数正确
- ⏳ 合约关系配置等待24小时时间锁

## �📋 目录

1. [部署前准备](#部署前准备)
2. [Cloudflare Pages部署](#cloudflare-pages部署)
3. [环境变量配置](#环境变量配置)
4. [数据库设置](#数据库设置)
5. [功能测试](#功能测试)
6. [性能验证](#性能验证)
7. [故障排除](#故障排除)

## 🚀 部署前准备

### 1. 运行部署检查脚本

```bash
# 运行最终部署验证
node scripts/final-deployment-check.js

# 确保所有检查通过
echo "检查结果: $?"
```

### 2. 提交代码到GitHub

```bash
# 确保所有更改已提交
git add .
git commit -m "feat: 准备staging环境部署"
git push origin main
```

### 3. 创建staging分支

```bash
# 创建staging分支
git checkout -b staging
git push origin staging
```

## ☁️ Cloudflare Pages部署

### 1. 创建Cloudflare Pages项目

1. 登录 [Cloudflare控制台](https://dash.cloudflare.com)
2. 进入 **Pages** 部分
3. 点击 **创建项目**
4. 选择 **连接到Git**

### 2. 连接GitHub仓库

1. 授权Cloudflare访问GitHub
2. 选择SocioMint仓库
3. 配置项目设置：

```yaml
项目名称: sociomint-staging
生产分支: staging
构建配置:
  框架预设: Next.js
  构建命令: npm run build
  构建输出目录: .next
  根目录: /
  Node.js版本: 18
```

### 3. 配置构建设置

```yaml
构建命令: npm run build
构建输出目录: .next
安装命令: npm ci
根目录: /

环境变量:
  NODE_VERSION: "18"
  NEXT_TELEMETRY_DISABLED: "1"
  SKIP_ENV_VALIDATION: "true"
```

## 🔧 环境变量配置

### 1. 在Cloudflare Pages中设置环境变量

进入项目设置 → 环境变量，添加以下变量：

```bash
# 基础配置
NODE_ENV=production
NEXT_PUBLIC_SITE_URL=https://sociomint-staging.pages.dev

# Supabase配置
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# NextAuth配置
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=https://sociomint-staging.pages.dev

# Telegram配置
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
NEXT_PUBLIC_TELEGRAM_BOT_USERNAME=your_bot_username

# 区块链配置
NEXT_PUBLIC_CHAIN_ID=97
NEXT_PUBLIC_HAOX_TOKEN_ADDRESS=0xe8f5D853F689e7798f7d8F4E0ACF3601191e6670
NEXT_PUBLIC_HAOX_PRESALE_ADDRESS=0x440F1E265eF64b9150746b6E41E4276bc27E182a

# 监控配置（可选）
NEXT_PUBLIC_GA_MEASUREMENT_ID=your_ga_id
NEXT_PUBLIC_SENTRY_DSN=your_sentry_dsn
```

### 2. 创建staging专用环境文件

```bash
# 创建.env.staging文件
cp .env.production .env.staging

# 修改staging特定配置
sed -i 's/sociomint.app/sociomint-staging.pages.dev/g' .env.staging
```

## 🗄️ 数据库设置

### 1. 创建Staging数据库

在Supabase控制台：

1. 创建新项目：`sociomint-staging`
2. 等待数据库初始化完成
3. 获取连接信息

### 2. 运行数据库迁移

```bash
# 设置Supabase CLI
npm install -g @supabase/cli

# 登录Supabase
supabase login

# 链接到staging项目
supabase link --project-ref your-staging-project-ref

# 运行迁移
supabase db push

# 或手动执行SQL文件
psql -h your-staging-host -U postgres -d postgres -f database/migrations/001_create_invitation_stats.sql
```

### 3. 初始化测试数据

```sql
-- 创建测试用户
INSERT INTO auth.users (id, email, email_confirmed_at, created_at, updated_at)
VALUES (
  gen_random_uuid(),
  '<EMAIL>',
  now(),
  now(),
  now()
);

-- 创建测试邀请统计
INSERT INTO invitation_stats (user_id, total_invitations, successful_invitations)
VALUES (
  (SELECT id FROM auth.users WHERE email = '<EMAIL>'),
  5,
  3
);
```

## 🧪 功能测试

### 1. 自动化测试

```bash
# 运行staging环境测试
node scripts/test-staging.js

# 或使用curl进行基础测试
curl -I https://sociomint-staging.pages.dev
curl https://sociomint-staging.pages.dev/api/health
```

### 2. 手动功能测试

**测试清单：**

- [ ] 页面加载
  - [ ] 首页正常显示
  - [ ] 登录页面可访问
  - [ ] 仪表板页面可访问
  - [ ] 钱包页面可访问

- [ ] 用户认证
  - [ ] Telegram登录功能
  - [ ] 用户会话管理
  - [ ] 登出功能

- [ ] 钱包功能
  - [ ] 钱包连接
  - [ ] 余额显示
  - [ ] 交易记录

- [ ] API端点
  - [ ] `/api/health` 健康检查
  - [ ] `/api/auth/*` 认证端点
  - [ ] `/api/wallet/*` 钱包端点

### 3. 端到端测试脚本

```javascript
// tests/e2e/staging.test.js
const { test, expect } = require('@playwright/test');

test('staging environment basic functionality', async ({ page }) => {
  // 访问首页
  await page.goto('https://sociomint-staging.pages.dev');
  await expect(page).toHaveTitle(/SocioMint/);
  
  // 检查导航
  await page.click('text=登录');
  await expect(page).toHaveURL(/.*login/);
  
  // 检查API健康状态
  const response = await page.request.get('/api/health');
  expect(response.status()).toBe(200);
});
```

## 📊 性能验证

### 1. 性能指标检查

```bash
# 使用Lighthouse进行性能测试
npx lighthouse https://sociomint-staging.pages.dev --output=json --output-path=./staging-performance.json

# 检查Core Web Vitals
npx @lhci/cli autorun --upload.target=temporary-public-storage
```

### 2. 负载测试

```bash
# 使用Apache Bench进行简单负载测试
ab -n 100 -c 10 https://sociomint-staging.pages.dev/

# 使用Artillery进行更复杂的负载测试
npm install -g artillery
artillery quick --count 10 --num 5 https://sociomint-staging.pages.dev/
```

### 3. 监控设置

```javascript
// 在staging环境启用详细监控
if (process.env.NODE_ENV === 'production' && window.location.hostname.includes('staging')) {
  // 启用详细日志
  console.log('Staging environment detected, enabling detailed monitoring');
  
  // 性能监控
  new PerformanceObserver((list) => {
    list.getEntries().forEach((entry) => {
      console.log('Performance:', entry.name, entry.duration);
    });
  }).observe({ entryTypes: ['navigation', 'resource'] });
}
```

## 🔍 故障排除

### 1. 常见部署问题

**问题1：构建失败**
```bash
# 检查构建日志
# 在Cloudflare Pages控制台查看详细构建日志

# 本地复现构建问题
npm run build
```

**问题2：环境变量未生效**
```bash
# 检查环境变量设置
# 确保在Cloudflare Pages项目设置中正确配置

# 验证环境变量
curl https://sociomint-staging.pages.dev/api/health
```

**问题3：数据库连接失败**
```bash
# 检查Supabase连接
# 验证URL和密钥是否正确

# 测试数据库连接
psql "postgresql://postgres:[password]@[host]:5432/postgres"
```

### 2. 调试工具

```bash
# 查看实时日志
npx wrangler pages deployment tail

# 检查函数日志
npx wrangler pages functions tail

# 本地调试
npm run dev
```

### 3. 回滚策略

```bash
# 如果staging部署有问题，快速回滚
git checkout main
git push origin staging --force

# 或在Cloudflare Pages控制台选择之前的部署版本
```

## ✅ 部署验证清单

完成staging部署后，确认以下项目：

- [ ] **基础功能**
  - [ ] 网站可正常访问
  - [ ] 所有页面加载正常
  - [ ] API端点响应正常
  - [ ] 数据库连接正常

- [ ] **用户功能**
  - [ ] 用户注册/登录
  - [ ] 钱包连接
  - [ ] 基础交互功能

- [ ] **性能指标**
  - [ ] 页面加载时间 < 3秒
  - [ ] API响应时间 < 1秒
  - [ ] Lighthouse评分 > 80

- [ ] **安全检查**
  - [ ] HTTPS正常工作
  - [ ] 环境变量安全
  - [ ] 无敏感信息泄露

- [ ] **监控设置**
  - [ ] 错误监控正常
  - [ ] 性能监控正常
  - [ ] 日志记录正常

## 🎯 下一步

Staging环境验证通过后：

1. **记录测试结果**
   - 性能指标
   - 功能测试结果
   - 发现的问题

2. **修复发现的问题**
   - 在开发环境修复
   - 重新部署到staging验证

3. **准备生产部署**
   - 更新生产环境配置
   - 准备域名和SSL
   - 制定部署计划

4. **升级到V2.1安全版本** ⭐ **强烈推荐**
   - 参考 [`docs/STAGING-DEPLOYMENT-V2.1-SECURE.md`](./STAGING-DEPLOYMENT-V2.1-SECURE.md)
   - 执行安全漏洞修复
   - 提升系统安全等级到低风险

5. **团队评审**
   - 功能验收
   - 性能评审
   - 安全评审

---

## 🔄 V2到V2.1升级快速指南

### 为什么要升级到V2.1？
- ✅ 修复5个中危安全漏洞
- ✅ 提升安全等级从中等风险到低风险
- ✅ 增强系统稳定性和性能
- ✅ 满足主网部署安全要求
- ✅ 企业级密钥管理系统
- ✅ 多预言机价格聚合

### 快速升级步骤

1. **备份当前配置**
   ```bash
   cp .env.local .env.v2.backup
   cp -r contracts/deployments contracts/deployments.v2.backup
   ```

2. **部署V2.1安全合约**
   ```bash
   # 详细步骤请参考V2.1部署文档
   cd contracts
   npx hardhat run scripts/deploy-secure-vesting.js --network bscTestnet
   npx hardhat run scripts/deploy-price-aggregator.js --network bscTestnet
   ```

3. **更新环境变量**
   ```bash
   # 添加V2.1安全合约地址
   NEXT_PUBLIC_HAOX_VESTING_ADDRESS_V2_FIXED_SECURE=NEW_SECURE_ADDRESS
   NEXT_PUBLIC_HAOX_PRICE_AGGREGATOR_ADDRESS=NEW_AGGREGATOR_ADDRESS

   # 启用安全功能
   SECURE_KEY_ENABLED=true
   ENABLE_MULTI_ORACLE=true
   ENABLE_TIME_LOCK=true
   ```

4. **设置安全密钥管理**
   ```bash
   chmod +x scripts/setup-secure-keys.sh
   ./scripts/setup-secure-keys.sh
   ```

5. **验证升级**
   ```bash
   npx hardhat run scripts/test-security-fixes.js --network bscTestnet
   ```

### 详细升级文档
完整的V2.1安全版本部署指南请参考：
**[`docs/STAGING-DEPLOYMENT-V2.1-SECURE.md`](./STAGING-DEPLOYMENT-V2.1-SECURE.md)**

### 升级支持
如需升级支持，请联系：
- **技术团队**: <EMAIL>
- **安全团队**: <EMAIL>

---

Staging环境部署成功后，强烈建议升级到V2.1安全版本，然后进行生产环境部署！
