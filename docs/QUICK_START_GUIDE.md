# SocioMint V2.1 快速启动指南

**目标**: 帮助新接手的开发者在30分钟内搭建完整的开发环境并运行项目

---

## 🚀 快速启动 (30分钟)

### 第1步: 环境准备 (5分钟)

#### 必需软件
```bash
# 检查Node.js版本 (需要 >= 18.0.0)
node --version

# 检查npm版本
npm --version

# 检查Git版本
git --version
```

如果缺少任何软件，请先安装：
- [Node.js](https://nodejs.org/) (推荐LTS版本)
- [Git](https://git-scm.com/)

### 第2步: 克隆项目 (2分钟)

```bash
# 克隆仓库
git clone [仓库地址] sociomint-v2.1
cd sociomint-v2.1

# 查看项目结构
ls -la
```

### 第3步: 安装依赖 (5分钟)

```bash
# 安装前端依赖
npm install

# 安装合约依赖
cd contracts
npm install
cd ..
```

### 第4步: 环境配置 (8分钟)

#### 4.1 复制环境变量模板
```bash
cp .env.example .env.local
```

#### 4.2 编辑环境变量
```bash
# 编辑 .env.local 文件
nano .env.local
```

**最小配置** (用于本地开发):
```bash
# 基础配置
NEXT_PUBLIC_APP_ENV=development
NEXT_PUBLIC_APP_NAME=SocioMint

# 区块链配置 (使用测试网)
NEXT_PUBLIC_BSC_RPC_URL=https://data-seed-prebsc-1-s1.binance.org:8545/
NEXT_PUBLIC_CHAIN_ID=97

# 认证配置
NEXTAUTH_SECRET=your-secret-key-here
NEXTAUTH_URL=http://localhost:3000

# 数据库配置 (可选，用于完整功能)
SUPABASE_URL=your-supabase-url
SUPABASE_ANON_KEY=your-supabase-anon-key
```

#### 4.3 合约环境配置
```bash
# 进入合约目录
cd contracts

# 复制合约环境变量
cp .env.example .env

# 编辑合约环境变量
nano .env
```

**合约最小配置**:
```bash
# BSC测试网配置
BSC_TESTNET_RPC_URL=https://data-seed-prebsc-1-s1.binance.org:8545/
BSC_TESTNET_PRIVATE_KEY=your-test-private-key

# 可选：区块链浏览器API (用于合约验证)
BSCSCAN_API_KEY=your-bscscan-api-key
```

### 第5步: 启动项目 (5分钟)

#### 5.1 编译智能合约
```bash
cd contracts
npx hardhat compile
cd ..
```

#### 5.2 启动前端开发服务器
```bash
npm run dev
```

#### 5.3 验证启动成功
打开浏览器访问: http://localhost:3000

你应该看到SocioMint的主页面。

### 第6步: 验证功能 (5分钟)

#### 6.1 检查页面功能
- [ ] 主页正常显示
- [ ] 钱包连接按钮可点击
- [ ] 价格图表区域显示
- [ ] 解锁进度组件加载

#### 6.2 检查控制台
按F12打开开发者工具，确保没有严重错误。

---

## 🔧 开发环境详细配置

### 数据库配置 (可选)

如果需要完整功能，需要配置Supabase数据库：

#### 1. 创建Supabase项目
1. 访问 https://supabase.com/
2. 创建新项目
3. 获取项目URL和API密钥

#### 2. 导入数据库Schema
```bash
# 使用Supabase CLI (推荐)
npx supabase init
npx supabase start
npx supabase db reset

# 或手动导入
# 在Supabase控制台的SQL编辑器中执行 supabase/schema.sql
```

#### 3. 更新环境变量
```bash
# 在 .env.local 中添加
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

### 钱包配置

#### 测试网钱包设置
1. 安装MetaMask浏览器扩展
2. 添加BSC测试网络：
   - 网络名称: BSC Testnet
   - RPC URL: https://data-seed-prebsc-1-s1.binance.org:8545/
   - 链ID: 97
   - 符号: tBNB
   - 区块浏览器: https://testnet.bscscan.com/

3. 获取测试币：
   - 访问 https://testnet.binance.org/faucet-smart
   - 输入钱包地址获取测试BNB

### IDE配置推荐

#### VS Code扩展
```json
{
  "recommendations": [
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-typescript-next",
    "solidity.solidity",
    "nomicfoundation.hardhat-solidity"
  ]
}
```

#### VS Code设置
```json
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "typescript.preferences.importModuleSpecifier": "relative",
  "solidity.defaultCompiler": "remote"
}
```

---

## 🧪 测试运行

### 前端测试
```bash
# 运行单元测试
npm run test

# 运行测试覆盖率
npm run test:coverage

# 运行E2E测试
npm run test:e2e
```

### 智能合约测试
```bash
cd contracts

# 运行合约测试
npx hardhat test

# 运行特定测试文件
npx hardhat test test/HAOXVestingV2Minimal.test.js

# 运行测试覆盖率
npx hardhat coverage
```

### 集成测试
```bash
# 启动本地区块链
cd contracts
npx hardhat node

# 在新终端部署合约到本地网络
npx hardhat run scripts/deploy-minimal-contracts.js --network localhost

# 运行集成测试
cd ..
npm run test:integration
```

---

## 🐛 常见问题解决

### 问题1: npm install失败
```bash
# 清理缓存
npm cache clean --force

# 删除node_modules重新安装
rm -rf node_modules package-lock.json
npm install
```

### 问题2: 合约编译失败
```bash
# 检查Solidity版本
npx hardhat --version

# 清理编译缓存
npx hardhat clean
npx hardhat compile
```

### 问题3: 前端启动失败
```bash
# 检查端口占用
lsof -i :3000

# 使用其他端口
npm run dev -- -p 3001
```

### 问题4: 钱包连接失败
1. 确保MetaMask已安装并解锁
2. 检查网络配置是否正确
3. 清除浏览器缓存和MetaMask缓存

### 问题5: 环境变量不生效
```bash
# 确保文件名正确
ls -la .env*

# 重启开发服务器
# Ctrl+C 停止，然后重新运行 npm run dev
```

---

## 📚 下一步学习

### 必读文档
1. **项目交接文档**: `docs/PROJECT_HANDOVER_DOCUMENT_V2.1.md`
2. **技术架构文档**: `docs/TECHNICAL_ARCHITECTURE.md`
3. **API文档**: `docs/openapi.yaml`
4. **部署指南**: `docs/DEPLOYMENT_GUIDE.md`

### 推荐学习路径
1. **第1天**: 熟悉项目结构和核心功能
2. **第2天**: 深入理解智能合约逻辑
3. **第3天**: 掌握前端组件和状态管理
4. **第4天**: 学习部署和监控流程
5. **第5天**: 实践测试和调试技巧

### 技术栈学习资源
- **Next.js**: https://nextjs.org/learn
- **Solidity**: https://docs.soliditylang.org/
- **Hardhat**: https://hardhat.org/tutorial
- **Tailwind CSS**: https://tailwindcss.com/docs
- **TypeScript**: https://www.typescriptlang.org/docs

---

## 🆘 获取帮助

### 内部支持
- **技术问题**: <EMAIL>
- **项目问题**: <EMAIL>
- **紧急问题**: <EMAIL>

### 社区资源
- **GitHub Issues**: 项目相关问题
- **技术群组**: [内部技术交流群]
- **文档反馈**: <EMAIL>

### 外部资源
- **Stack Overflow**: 通用技术问题
- **GitHub Discussions**: 开源项目讨论
- **Discord/Telegram**: 区块链开发社区

---

## ✅ 启动检查清单

完成以下检查确保环境配置正确：

### 环境检查
- [ ] Node.js版本 >= 18.0.0
- [ ] npm或yarn已安装
- [ ] Git已配置
- [ ] VS Code已安装 (推荐)

### 项目检查
- [ ] 项目已克隆到本地
- [ ] 前端依赖已安装
- [ ] 合约依赖已安装
- [ ] 环境变量已配置
- [ ] 合约编译成功

### 功能检查
- [ ] 前端开发服务器启动成功
- [ ] 主页面正常显示
- [ ] 钱包连接功能正常
- [ ] 控制台无严重错误
- [ ] 测试用例运行通过

### 可选检查
- [ ] Supabase数据库已配置
- [ ] 测试网钱包已设置
- [ ] 测试币已获取
- [ ] IDE扩展已安装

**全部完成后，您就可以开始SocioMint V2.1的开发工作了！** 🎉

---

*如果在启动过程中遇到任何问题，请参考常见问题解决部分，或联系技术团队获取支持。*
