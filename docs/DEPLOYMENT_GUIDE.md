# HAOX代币解锁系统部署指南

## 📋 系统概览

HAOX代币解锁系统包含以下组件：
- **HAOXVestingV2Fixed.sol** - 智能合约（已部署）
- **前端价格显示系统** - React组件
- **价格监控服务** - Node.js后台服务
- **管理面板** - Web管理界面

## 🚀 已完成部署

### 智能合约部署
- **合约地址**: `******************************************`
- **网络**: BSC测试网
- **交易哈希**: `0x601b19b52104cdc0e930d7050e86da6923f205356cd7a8c7bd5ccf7e1c700524`
- **Gas使用**: 4,545,332
- **部署成本**: 0.04545332 BNB

### 合约验证结果
✅ **基础参数正确**：
- 总轮次：31轮
- 每轮代币：1.5亿 HAOX
- 第1轮代币：5亿 HAOX（已解锁）
- 基准价格：$0.003041 USD

✅ **价格计算正确**：
- 第2-11轮：每轮+100%
- 第12-21轮：每轮+50%
- 第22-31轮：每轮+20%

✅ **代币分配正确**：
- 项目钱包：40%
- 社区钱包：60%

## 🔧 前端系统部署

### 1. 环境变量配置

更新 `.env.local` 文件：
```bash
# 合约地址
NEXT_PUBLIC_HAOX_VESTING_ADDRESS_V2_FIXED=******************************************
NEXT_PUBLIC_HAOX_TOKEN_ADDRESS=******************************************
NEXT_PUBLIC_HAOX_PRICE_ORACLE_ADDRESS=******************************************

# 网络配置
NEXT_PUBLIC_BSC_RPC_URL=https://data-seed-prebsc-1-s1.binance.org:8545/
NEXT_PUBLIC_CHAIN_ID=97

# 监控服务
DEPLOYER_PRIVATE_KEY=your_private_key_here
```

### 2. 安装依赖

```bash
npm install ethers@^6.0.0
```

### 3. 集成前端组件

在您的页面中导入并使用组件：

```jsx
// pages/price-monitor.js
import PriceDisplay from '../components/PriceDisplay';

export default function PriceMonitorPage() {
    return (
        <div>
            <PriceDisplay />
        </div>
    );
}
```

```jsx
// pages/admin.js
import AdminPanel from '../components/AdminPanel';

export default function AdminPage() {
    return (
        <div>
            <AdminPanel />
        </div>
    );
}
```

### 4. 添加CSS样式

创建样式文件 `styles/price-display.css`：

```css
/* 价格显示组件样式 */
.price-display {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.price-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.tab-navigation {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    border-bottom: 1px solid #e5e7eb;
}

.tab {
    padding: 10px 20px;
    border: none;
    background: none;
    cursor: pointer;
    border-bottom: 2px solid transparent;
}

.tab.active {
    border-bottom-color: #3b82f6;
    color: #3b82f6;
}

.price-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.current-price {
    font-size: 2.5rem;
    font-weight: bold;
    color: #1f2937;
}

.price-change.text-green-500 {
    color: #10b981;
}

.price-change.text-red-500 {
    color: #ef4444;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 20px;
}

.stat-card {
    background: white;
    padding: 16px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-bar-container {
    position: relative;
    background: #e5e7eb;
    height: 8px;
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar-fill {
    background: #3b82f6;
    height: 100%;
    transition: width 0.3s ease;
}

/* 管理面板样式 */
.admin-panel {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.connect-btn {
    background: #3b82f6;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
}

.stats-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.control-panel {
    background: white;
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 30px;
}

.control-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.control-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
}

.control-btn.primary {
    background: #3b82f6;
    color: white;
}

.control-btn.secondary {
    background: #6b7280;
    color: white;
}

.rounds-table {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 30px;
}

.table-container {
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
}

th {
    background: #f9fafb;
    font-weight: 600;
}

.status-completed {
    background: #f0fdf4;
}

.status-waiting {
    background: #fef3c7;
}

.status-pending {
    background: #f3f4f6;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .price-header {
        flex-direction: column;
        gap: 10px;
    }
    
    .tab-navigation {
        overflow-x: auto;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .control-buttons {
        flex-direction: column;
    }
}
```

## 🔍 监控服务部署

### 1. 启动监控服务

```bash
# 基本启动
node scripts/start-monitoring.js

# 使用自定义配置
node scripts/start-monitoring.js -c config/monitoring.json

# 详细模式
node scripts/start-monitoring.js -v

# 试运行（不执行实际交易）
node scripts/start-monitoring.js -d
```

### 2. 使用PM2管理服务

安装PM2：
```bash
npm install -g pm2
```

创建PM2配置文件 `ecosystem.config.js`：
```javascript
module.exports = {
  apps: [{
    name: 'haox-monitor',
    script: 'scripts/start-monitoring.js',
    args: '-v -l logs/monitoring.log',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production'
    }
  }]
};
```

启动服务：
```bash
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### 3. 监控服务功能

✅ **自动价格检查**：每30秒检查一次价格条件
✅ **事件监听**：实时监听合约事件
✅ **故障转移**：自动重试和错误处理
✅ **通知系统**：支持多种通知方式
✅ **日志记录**：详细的操作日志

## 📊 系统监控

### 1. 健康检查

监控服务提供健康检查端点：
```bash
curl http://localhost:3001/health
```

### 2. 日志监控

查看实时日志：
```bash
# PM2日志
pm2 logs haox-monitor

# 文件日志
tail -f logs/monitoring.log
```

### 3. 性能指标

监控关键指标：
- 价格检查成功率
- 交易Gas使用量
- 响应时间
- 错误率

## 🔒 安全考虑

### 1. 私钥管理
- 使用环境变量存储私钥
- 定期轮换私钥
- 限制私钥权限

### 2. 网络安全
- 使用HTTPS连接
- 配置防火墙规则
- 监控异常访问

### 3. 合约安全
- 定期审计合约代码
- 监控合约余额
- 设置紧急暂停机制

## 🚨 故障排除

### 常见问题

1. **合约连接失败**
   - 检查RPC URL是否正确
   - 验证网络连接
   - 确认合约地址

2. **交易失败**
   - 检查钱包余额
   - 调整Gas价格
   - 验证私钥权限

3. **价格获取失败**
   - 检查Chainlink预言机状态
   - 验证Binance API连接
   - 查看网络延迟

### 紧急操作

如需紧急停止监控：
```bash
pm2 stop haox-monitor
```

如需紧急暂停合约：
```bash
# 连接管理员钱包调用pause()函数
```

## 📈 性能优化

### 1. RPC优化
- 使用多个RPC端点
- 实现负载均衡
- 缓存查询结果

### 2. 监控优化
- 调整检查频率
- 优化Gas使用
- 减少不必要的查询

### 3. 通知优化
- 设置通知冷却期
- 批量发送通知
- 过滤重复消息

## 🔄 维护计划

### 日常维护
- 检查服务状态
- 监控日志文件
- 验证价格数据

### 周期维护
- 更新依赖包
- 备份配置文件
- 性能分析

### 紧急维护
- 合约升级
- 安全补丁
- 故障恢复

---

## 📞 技术支持

如遇到问题，请联系技术团队：
- 邮箱：<EMAIL>
- 电话：+86-xxx-xxxx-xxxx
- 文档：https://docs.sociomint.com
