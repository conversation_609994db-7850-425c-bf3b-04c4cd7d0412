# 🚀 SocioMint - 社交化加密货币交易平台

<div align="center">
  <img src="https://img.shields.io/badge/Next.js-14.2.30-black?style=for-the-badge&logo=next.js" alt="Next.js" />
  <img src="https://img.shields.io/badge/TypeScript-5.0-blue?style=for-the-badge&logo=typescript" alt="TypeScript" />
  <img src="https://img.shields.io/badge/Tailwind_CSS-3.4-38B2AC?style=for-the-badge&logo=tailwind-css" alt="Tailwind CSS" />
  <img src="https://img.shields.io/badge/Supabase-Database-green?style=for-the-badge&logo=supabase" alt="Supabase" />
  <img src="https://img.shields.io/badge/Web3-Wagmi-orange?style=for-the-badge&logo=ethereum" alt="Web3" />
  <img src="https://img.shields.io/badge/Cloudflare-F38020?style=for-the-badge&logo=cloudflare&logoColor=white" alt="Cloudflare" />
  <img src="https://img.shields.io/badge/License-MIT-yellow?style=for-the-badge" alt="License" />
</div>

<div align="center">
  <h3>🌟 基于区块链的社交挖矿平台 - 通过社交活动赚取代币奖励 🌟</h3>
</div>

## 📖 项目简介

SocioMint 是一个创新的社交化加密货币交易平台，专注于 HAOX 代币的交易和社交任务系统。平台集成了主流社交媒体平台，用户可以通过完成社交任务获得代币奖励，同时支持法币和加密货币的便捷交易。

### ✨ 核心功能

- 🔐 **Telegram 认证** - 无密码安全登录，一键连接
- 💰 **多链钱包支持** - 支持 BSC 和其他 EVM 兼容链
- 📋 **社交任务系统** - 多样化任务类型，丰富奖励机制
- 🛒 **代币预售功能** - 参与 HAOX 代币早期销售
- 🏪 **商户申请系统** - 企业用户认证，享受专属特权
- 📊 **实时价格数据** - 价格追踪和市场分析
- 🔒 **企业级安全** - 多重安全保护机制
- 🌐 **全球化部署** - 基于 Cloudflare 的边缘计算

### 🛠 技术栈

**前端框架**
- Next.js 14 (App Router)
- TypeScript
- Tailwind CSS
- Framer Motion

**Web3 集成**
- Wagmi v2
- Viem
- WalletConnect v3
- Alchemy RPC

**后端服务**
- Supabase (数据库 + 认证)
- Next.js API Routes

**开发工具**
- ESLint + Prettier
- Jest + Testing Library
- Husky (Git Hooks)

## 🔗 HAOX智能合约 (BSC测试网)

### 合约地址
- **HAOXToken**: `******************************************`
- **HAOXPresale**: `******************************************`
- **HAOXInvitation**: `******************************************`
- **HAOXPriceOracle**: `******************************************`
- **HAOXVesting**: `******************************************`

### 部署信息
- **网络**: BSC测试网 (Chain ID: 97)
- **部署日期**: 2025年1月24日
- **总Gas消耗**: 8,647,896 gas
- **部署成本**: $0.52 USD

### BSCScan链接
- [HAOXToken](https://testnet.bscscan.com/address/******************************************)
- [HAOXPresale](https://testnet.bscscan.com/address/******************************************)
- [HAOXInvitation](https://testnet.bscscan.com/address/******************************************)
- [HAOXPriceOracle](https://testnet.bscscan.com/address/******************************************)
- [HAOXVesting](https://testnet.bscscan.com/address/******************************************)

## 🚀 快速开始

### 环境要求

- Node.js 18.0+
- npm 或 yarn
- Git

### 安装步骤

1. **克隆项目**
   ```bash
   git clone https://github.com/your-username/sociomint.git
   cd sociomint
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **配置环境变量**
   ```bash
   cp .env.example .env.local
   ```

   编辑 `.env.local` 文件，填入以下必需的配置：
   ```env
   # Supabase
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

   # WalletConnect
   NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID=your_project_id

   # Alchemy
   NEXT_PUBLIC_ALCHEMY_API_KEY=your_alchemy_key
   ```

4. **启动开发服务器**
   ```bash
   npm run dev
   ```

5. **访问应用**
   打开 [http://localhost:3000](http://localhost:3000)

### 数据库设置

1. 在 [Supabase](https://supabase.com) 创建新项目
2. 运行数据库迁移脚本创建表结构
3. 配置行级安全策略 (RLS)

详细的数据库设置请参考 [部署指南](./docs/DEPLOYMENT.md)

## 📱 功能演示

### 主要页面

- **首页** (`/`) - 平台介绍和统计数据
- **交易页面** (`/trade`) - HAOX 代币交易界面
- **用户中心** (`/dashboard`) - 个人资料和交易记录
- **商家认证** (`/merchants`) - 商家申请和认证流程
- **社交任务** (`/tasks`) - 任务列表和奖励系统

## 🧪 测试

```bash
# 运行所有测试
npm run test

# 运行测试并生成覆盖率报告
npm run test:coverage
```

## 📦 构建和部署

### 本地构建

```bash
npm run build
npm run start
```

### Vercel 部署

1. 推送代码到 GitHub
2. 在 [Vercel](https://vercel.com) 导入项目
3. 配置环境变量
4. 自动部署

详细部署指南请参考 [DEPLOYMENT.md](./docs/DEPLOYMENT.md)

## 📚 项目文档

### 用户文档
- [用户手册](./docs/USER_MANUAL.md) - 完整的用户使用指南
- [常见问题](./docs/FAQ.md) - 用户常见问题解答

### 开发文档
- [开发者指南](./docs/DEVELOPER_GUIDE.md) - 开发环境搭建和开发规范
- [API 文档](./docs/API.md) - 完整的 API 接口文档
- [智能合约文档](./docs/SMART_CONTRACTS.md) - HAOX 合约详细说明
- [配置指南](./docs/CONFIGURATION.md) - 环境配置和第三方服务设置

### 运维文档
- [部署指南](./docs/DEPLOYMENT.md) - 生产环境部署流程
- [故障排除](./docs/TROUBLESHOOTING.md) - 常见问题诊断和解决

## 🏗 项目结构

```
src/
├── app/                    # Next.js App Router 页面
├── components/            # React 组件
│   ├── ui/                # 基础 UI 组件
│   ├── wallet/            # 钱包相关组件
│   ├── social/            # 社交功能组件
│   └── trading/           # 交易组件
├── hooks/                 # 自定义 React Hooks
├── lib/                   # 工具库和配置
├── types/                 # TypeScript 类型定义
└── constants/             # 常量定义
```

## 📚 文档

- [📖 用户手册](./docs/USER-MANUAL.md) - 详细的用户使用指南
- [👨‍💻 开发者文档](./docs/DEVELOPER.md) - 技术架构和开发指南
- [🚀 部署指南](./docs/DEPLOYMENT.md) - 部署和运维文档
- [🔌 API 文档](./docs/API.md) - 完整的 API 接口文档
- [☁️ Cloudflare 配置](./docs/CLOUDFLARE-SETUP.md) - Cloudflare 部署配置
- [🔒 备份恢复](./docs/BACKUP-RECOVERY.md) - 备份和恢复策略

## 🧪 测试

```bash
# 运行所有测试
npm test

# 运行测试并监听文件变化
npm run test:watch

# 运行测试覆盖率报告
npm run test:coverage

# 运行 E2E 测试
npm run test:e2e
```

## 🤝 贡献指南

我们欢迎社区贡献！请遵循以下步骤：

1. **Fork 项目**
2. **创建功能分支** (`git checkout -b feature/amazing-feature`)
3. **提交更改** (`git commit -m 'Add some amazing feature'`)
4. **推送到分支** (`git push origin feature/amazing-feature`)
5. **创建 Pull Request**

### 代码规范

- 使用 TypeScript 进行类型安全开发
- 遵循 ESLint 和 Prettier 配置
- 编写测试覆盖新功能
- 更新相关文档

## 🔗 相关链接

- **官网**: [https://sociomint.app](https://sociomint.app)
- **测试环境**: [https://staging.sociomint.app](https://staging.sociomint.app)
- **文档**: [https://docs.sociomint.app](https://docs.sociomint.app)
- **Telegram**: [https://t.me/sociomint](https://t.me/sociomint)
- **Discord**: [https://discord.gg/sociomint](https://discord.gg/sociomint)
- **Twitter**: [https://twitter.com/sociomint](https://twitter.com/sociomint)

## 📊 项目状态

![GitHub stars](https://img.shields.io/github/stars/sociomint/sociomint?style=social)
![GitHub forks](https://img.shields.io/github/forks/sociomint/sociomint?style=social)
![GitHub issues](https://img.shields.io/github/issues/sociomint/sociomint)
![GitHub pull requests](https://img.shields.io/github/issues-pr/sociomint/sociomint)

## 🙏 致谢

感谢所有为 SocioMint 项目做出贡献的开发者和社区成员！

特别感谢以下开源项目：
- [Next.js](https://nextjs.org/) - 强大的 React 框架
- [Supabase](https://supabase.com/) - 开源的后端即服务
- [Tailwind CSS](https://tailwindcss.com/) - 实用优先的 CSS 框架
- [Radix UI](https://www.radix-ui.com/) - 高质量的 UI 组件
- [Cloudflare](https://www.cloudflare.com/) - 全球边缘网络

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 支持

如果您遇到问题或有疑问：

- 📧 邮箱: <EMAIL>
- 💬 Telegram: @SocioMintSupport
- 🐛 问题报告: [GitHub Issues](https://github.com/sociomint/sociomint/issues)
- 💡 功能建议: [GitHub Discussions](https://github.com/sociomint/sociomint/discussions)

---

<div align="center">
  <p>用 ❤️ 构建，为了更好的 Web3 社交体验</p>
  <p>© 2024 SocioMint Team. All rights reserved.</p>
</div>
