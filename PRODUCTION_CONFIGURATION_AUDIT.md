# 🔍 SocioMint生产环境配置审计报告

## 📋 概述

本报告详细列出了SocioMint项目中所有需要在生产环境中配置的API端点、环境变量、合约地址和占位符数据。这些配置项必须在部署到生产环境之前进行正确设置。

---

## 🔑 关键环境变量配置

### ✅ **必需配置项**

#### **1. 数据库配置 (Supabase)**
```bash
# Supabase项目配置 - 从 https://supabase.com/dashboard 获取
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### **2. 区块链网络配置**
```bash
# BSC主网配置 (生产环境)
NEXT_PUBLIC_CHAIN_ID=56
NEXT_PUBLIC_NETWORK_NAME="BSC Mainnet"
NEXT_PUBLIC_RPC_URL="https://bsc-dataseed1.binance.org/"
NEXT_PUBLIC_BLOCK_EXPLORER="https://bscscan.com"

# 或 BSC测试网配置 (测试环境)
NEXT_PUBLIC_CHAIN_ID=97
NEXT_PUBLIC_NETWORK_NAME="BSC Testnet"
NEXT_PUBLIC_RPC_URL="https://data-seed-prebsc-1-s1.binance.org:8545/"
NEXT_PUBLIC_BLOCK_EXPLORER="https://testnet.bscscan.com"
```

#### **3. HAOX智能合约地址 (需要实际部署地址)**
```bash
# 主要合约地址 - 需要替换为实际部署的合约地址
NEXT_PUBLIC_HAOX_TOKEN_ADDRESS=0x... # HAOX代币合约
NEXT_PUBLIC_HAOX_PRESALE_ADDRESS=0x... # 预售合约
NEXT_PUBLIC_HAOX_INVITATION_ADDRESS=0x... # 邀请奖励合约
NEXT_PUBLIC_HAOX_PRICE_ORACLE_ADDRESS=0x... # 价格预言机合约
NEXT_PUBLIC_HAOX_VESTING_ADDRESS=0x... # 代币解锁合约

# 兼容性地址
NEXT_PUBLIC_HAOX_CONTRACT_ADDRESS_BSC=0x... # 与TOKEN_ADDRESS相同
NEXT_PUBLIC_HAOX_CONTRACT_ADDRESS=0x... # 与TOKEN_ADDRESS相同

# Chainlink价格预言机配置
NEXT_PUBLIC_CHAINLINK_BNB_USD_FEED=0x0567F2323251f0Aab15c8dFb1967E4e8A7D42aeE  # BSC主网
# 或测试网: NEXT_PUBLIC_CHAINLINK_BNB_USD_FEED=0x2514895c72f50D8bd4B4F9b1110F0D6bD2c97526

# HAOX代币解锁配置
NEXT_PUBLIC_HAOX_INITIAL_PRICE_RATIO=263111  # 1 BNB = 263,111 HAOX (预售结束价格)
NEXT_PUBLIC_HAOX_VESTING_ROUNDS=19           # 总解锁轮次
NEXT_PUBLIC_HAOX_PRICE_MAINTAIN_HOURS=168    # 价格维持时间(7天)

# PancakeSwap交易对配置 (备用价格数据源)
NEXT_PUBLIC_PANCAKESWAP_HAOX_BNB_PAIR=0x...  # HAOX/BNB交易对地址
NEXT_PUBLIC_PANCAKESWAP_FACTORY=******************************************  # PancakeSwap V2 Factory
```

#### **4. Web3钱包配置**
```bash
# WalletConnect项目ID - 从 https://cloud.walletconnect.com 获取
NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID=a1b2c3d4e5f6g7h8i9j0

# Alchemy API密钥 - 从 https://dashboard.alchemy.com 获取
NEXT_PUBLIC_ALCHEMY_API_KEY=your_alchemy_api_key
```

#### **5. 应用基础配置**
```bash
# 应用URL配置
NEXT_PUBLIC_APP_URL=https://your-domain.com
NEXTAUTH_URL=https://your-domain.com

# NextAuth密钥 - 生成随机字符串
NEXTAUTH_SECRET=your_nextauth_secret_32_characters_or_more

# 运行环境
NODE_ENV=production
```

### ⚠️ **敏感配置项 (需要安全管理)**

#### **6. 钱包私钥配置**
```bash
# ⚠️ 极其重要！请妥善保管这些私钥
DEPLOYER_PRIVATE_KEY=64位十六进制私钥字符串
DEPLOYER_WALLET_ADDRESS=0x...

PROJECT_WALLET_ADDRESS=0x...
PROJECT_WALLET_PRIVATE_KEY=64位十六进制私钥字符串

COMMUNITY_WALLET_ADDRESS=0x...
COMMUNITY_WALLET_PRIVATE_KEY=64位十六进制私钥字符串

TEST_WALLET_ADDRESS=0x...
TEST_WALLET_PRIVATE_KEY=64位十六进制私钥字符串
```

#### **7. 社交平台API密钥**
```bash
# Twitter API - 从 https://developer.twitter.com 获取
TWITTER_API_KEY=your_twitter_api_key
TWITTER_API_SECRET=your_twitter_api_secret
TWITTER_BEARER_TOKEN=your_twitter_bearer_token

# Discord API - 从 https://discord.com/developers/applications 获取
DISCORD_BOT_TOKEN=your_discord_bot_token
DISCORD_CLIENT_ID=your_discord_client_id

# Telegram Bot - 从 @BotFather 获取
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_BOT_USERNAME=your_bot_username
TELEGRAM_WEBHOOK_SECRET=your_webhook_secret_token
```

### 🔧 **可选配置项**

#### **8. 支付配置 (如需要)**
```bash
# 支付宝配置
ALIPAY_APP_ID=your_alipay_app_id
ALIPAY_PRIVATE_KEY=your_alipay_private_key

# 微信支付配置
WECHAT_PAY_MERCHANT_ID=your_wechat_pay_merchant_id
WECHAT_PAY_API_KEY=your_wechat_pay_api_key
```

#### **9. 分析和监控配置**
```bash
# Google Analytics
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX

# Vercel Analytics
NEXT_PUBLIC_VERCEL_ANALYTICS_ID=your_vercel_analytics_id

# Sentry错误追踪
SENTRY_DSN=https://your_sentry_dsn
```

---

## 🌐 API端点配置

### ✅ **内部API端点 (已实现)**

#### **1. 白皮书API**
- **端点**: `/api/whitepaper`
- **状态**: ✅ 已实现
- **配置**: 读取`docs/HAOX_WHITEPAPER_V2.md`文件
- **需要**: 确保白皮书文件存在且内容最新

#### **2. 健康检查API**
- **端点**: `/api/health`
- **状态**: ✅ 已实现
- **配置**: 检查数据库连接和环境变量
- **需要**: 配置正确的环境变量

#### **3. 数据库修复API**
- **端点**: `/api/database/fix-relations`
- **状态**: ✅ 已实现
- **配置**: 使用Supabase服务角色密钥
- **需要**: 正确的数据库权限

### ⚠️ **外部API依赖**

#### **4. Supabase数据库API**
- **服务**: Supabase PostgreSQL
- **状态**: 需要配置
- **配置**: 
  - 项目URL和API密钥
  - 数据库表结构
  - 行级安全策略
- **需要**: 创建生产环境Supabase项目

#### **5. BSC区块链RPC API**
- **服务**: Binance Smart Chain
- **状态**: 需要配置
- **配置**: 
  - RPC端点URL
  - 区块浏览器URL
  - 链ID设置
- **需要**: 选择可靠的RPC提供商

#### **6. WalletConnect API**
- **服务**: WalletConnect Cloud
- **状态**: 需要配置
- **配置**: 项目ID和域名白名单
- **需要**: 注册WalletConnect项目

---

## 📄 智能合约部署清单

### 🔄 **需要部署的合约**

#### **1. HAOXTokenV2.sol**
- **功能**: ERC20代币合约
- **当前地址**: `******************************************` (测试网)
- **需要**: 部署到BSC主网
- **配置**: 总供应量、代币名称、符号

#### **2. HAOXPresaleV2.sol**
- **功能**: 动态定价预售合约
- **当前地址**: `******************************************` (测试网)
- **需要**: 部署到BSC主网
- **配置**: 预售参数、价格阶梯

#### **3. HAOXInvitationV2.sol**
- **功能**: 邀请奖励系统
- **当前地址**: `******************************************` (测试网)
- **需要**: 部署到BSC主网
- **配置**: 奖励比例、邀请层级

#### **4. HAOXPriceOracleV2.sol**
- **功能**: 多源价格聚合器
- **当前地址**: `******************************************` (测试网)
- **需要**: 部署到BSC主网
- **配置**: 价格源、更新频率

#### **5. HAOXVestingV2.sol**
- **功能**: 19轮价格触发代币解锁合约
- **当前地址**: `0xfB069d009d4c219B2D7d6aeCe9BbA13bC75cD41A` (测试网)
- **需要**: 部署到BSC主网
- **配置**:
  - **解锁轮次**: 19轮总计（第1轮5亿枚已完成，第2-19轮每轮2.5亿枚）
  - **价格数据源**: Chainlink BNB/USD价格预言机 + PancakeSwap HAOX/BNB交易对
  - **初始价格**: 1/263,111 * BNB实时价格（基于预售结束价格）
  - **触发条件**:
    * 第2-7轮: 前一轮价格上涨100%
    * 第8-13轮: 前一轮价格上涨50%
    * 第14-19轮: 前一轮价格上涨20%
  - **维持要求**: 价格连续168小时(7天)保持在触发价格以上
  - **备案机制**: 多数据源价格聚合，流动性不足时降级处理

---

## 🗄️ 数据库配置清单

### ✅ **Supabase表结构**

#### **1. 用户相关表**
```sql
-- users表 (用户基础信息)
-- user_activities表 (用户活动记录)
-- social_accounts表 (社交账户绑定)
```

#### **2. 邀请系统表**
```sql
-- invitation_stats表 (邀请统计)
-- invitation_rewards表 (邀请奖励记录)
```

#### **3. 交易记录表**
```sql
-- transactions表 (交易记录)
-- presale_purchases表 (预售购买记录)
```

### ⚠️ **需要配置的数据库功能**

#### **4. 行级安全策略 (RLS)**
- **状态**: 需要配置
- **需要**: 为每个表设置适当的安全策略

#### **5. 数据库函数和触发器**
- **状态**: 需要配置
- **需要**: 创建自动化数据处理函数

#### **6. 实时订阅配置**
- **状态**: 需要配置
- **需要**: 配置实时数据更新

---

## 📱 前端配置清单

### ✅ **已配置项**

#### **1. 设计系统**
- **状态**: ✅ 完成
- **包含**: 颜色、字体、组件库
- **需要**: 无需额外配置

#### **2. 国际化支持**
- **状态**: ✅ 完成
- **支持**: 中文、英文
- **需要**: 无需额外配置

#### **3. 响应式设计**
- **状态**: ✅ 完成
- **支持**: 桌面端、移动端
- **需要**: 无需额外配置

### ⚠️ **需要配置项**

#### **4. HAOX代币解锁系统配置**
```bash
# 解锁轮次配置
HAOX_VESTING_ROUND_1_AMOUNT=500000000000000000000000000   # 5亿枚 (已完成)
HAOX_VESTING_ROUND_2_7_AMOUNT=250000000000000000000000000 # 2.5亿枚/轮 (第2-7轮)
HAOX_VESTING_ROUND_8_13_AMOUNT=250000000000000000000000000 # 2.5亿枚/轮 (第8-13轮)
HAOX_VESTING_ROUND_14_19_AMOUNT=250000000000000000000000000 # 2.5亿枚/轮 (第14-19轮)

# 价格触发条件配置
HAOX_VESTING_ROUND_2_7_TRIGGER=100   # 第2-7轮: 前一轮价格上涨100%
HAOX_VESTING_ROUND_8_13_TRIGGER=50   # 第8-13轮: 前一轮价格上涨50%
HAOX_VESTING_ROUND_14_19_TRIGGER=20  # 第14-19轮: 前一轮价格上涨20%

# 价格维持验证配置
HAOX_PRICE_MAINTAIN_DURATION=604800  # 7天 (168小时 * 3600秒)
HAOX_PRICE_CHECK_INTERVAL=3600       # 每小时检查一次价格

# 多数据源配置权重
CHAINLINK_PRICE_WEIGHT=70            # Chainlink数据源权重70%
PANCAKESWAP_PRICE_WEIGHT=30          # PancakeSwap数据源权重30%
MIN_LIQUIDITY_THRESHOLD=1000000000000000000  # 最小流动性阈值(1 BNB)
```

#### **5. 域名和SSL证书**
- **状态**: 需要配置
- **需要**: 
  - 购买域名
  - 配置DNS解析
  - 申请SSL证书

#### **5. CDN和缓存配置**
- **状态**: 需要配置
- **需要**: 
  - 配置Cloudflare或其他CDN
  - 设置缓存策略
  - 优化静态资源

#### **6. SEO配置**
- **状态**: 需要配置
- **需要**: 
  - 设置meta标签
  - 配置sitemap
  - 添加结构化数据

---

## 🔒 安全配置清单

### ⚠️ **关键安全项**

#### **1. 私钥管理**
- **状态**: 极其重要
- **需要**: 
  - 使用硬件钱包存储主要私钥
  - 设置多重签名钱包
  - 定期轮换API密钥

#### **2. 环境变量安全**
- **状态**: 需要配置
- **需要**: 
  - 使用安全的密钥管理服务
  - 避免在代码中硬编码敏感信息
  - 设置适当的访问权限

#### **3. API安全**
- **状态**: 需要配置
- **需要**: 
  - 实施速率限制
  - 添加API认证
  - 配置CORS策略

#### **4. 智能合约安全**
- **状态**: 需要审计
- **需要**: 
  - 进行专业安全审计
  - 实施时间锁机制
  - 设置紧急暂停功能

---

## 📊 监控和分析配置

### ⚠️ **需要配置项**

#### **1. 应用性能监控**
- **工具**: Sentry、Vercel Analytics
- **状态**: 需要配置
- **需要**: 设置错误追踪和性能监控

#### **2. 区块链监控**
- **工具**: BSCScan API、自定义监控
- **状态**: 需要配置
- **需要**: 监控合约交易和状态

#### **3. 用户行为分析**
- **工具**: Google Analytics
- **状态**: 需要配置
- **需要**: 设置事件追踪和转化分析

---

## ✅ 部署前检查清单

### 🔍 **必须完成项**

- [ ] **环境变量配置**: 所有必需的环境变量已设置
- [ ] **智能合约部署**: 所有合约已部署到目标网络
- [ ] **Chainlink集成**: BNB/USD价格预言机已配置并测试
- [ ] **解锁合约配置**: 19轮解锁机制已正确配置
- [ ] **价格触发测试**: 价格触发和维持机制已验证
- [ ] **多数据源验证**: 备用价格数据源已配置并测试
- [ ] **数据库设置**: Supabase项目已创建并配置
- [ ] **域名配置**: 域名已购买并配置DNS
- [ ] **SSL证书**: HTTPS已启用
- [ ] **API密钥**: 所有第三方服务API密钥已获取
- [ ] **安全审计**: 智能合约已通过安全审计
- [ ] **测试验证**: 所有功能已在测试环境验证
- [ ] **监控设置**: 错误追踪和性能监控已配置
- [ ] **备份策略**: 数据备份和恢复策略已制定

### 📋 **推荐完成项**

- [ ] **CDN配置**: 静态资源CDN已配置
- [ ] **缓存策略**: 页面和API缓存已优化
- [ ] **SEO优化**: 搜索引擎优化已完成
- [ ] **社交媒体**: 官方社交媒体账号已创建
- [ ] **客服系统**: 用户支持系统已建立
- [ ] **法律合规**: 相关法律文件已准备

---

## 🚨 重要提醒

### ⚠️ **安全警告**

1. **私钥安全**: 绝不要在代码仓库中提交包含真实私钥的文件
2. **环境隔离**: 生产环境和测试环境必须完全隔离
3. **访问控制**: 限制对生产环境的访问权限
4. **定期审计**: 定期检查和更新安全配置
5. **备份策略**: 确保关键数据有可靠的备份

### 📞 **技术支持**

如需技术支持或有疑问，请联系：
- **邮箱**: <EMAIL>
- **文档**: 参考项目README.md和相关技术文档
- **紧急联系**: 保留开发团队联系方式

---

**最后更新**: 2025年2月2日  
**文档版本**: V1.0  
**审计范围**: 完整项目配置
