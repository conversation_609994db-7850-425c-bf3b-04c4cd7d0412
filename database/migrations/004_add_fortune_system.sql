-- 在现有数据库基础上添加福气系统
-- 扩展现有表结构，不重复创建

-- 1. 扩展现有users表，添加福气相关字段
DO $$
BEGIN
    -- 添加福气余额字段
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'available_fortune') THEN
        ALTER TABLE public.users ADD COLUMN available_fortune DECIMAL(20,8) DEFAULT 0 NOT NULL;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'locked_fortune') THEN
        ALTER TABLE public.users ADD COLUMN locked_fortune DECIMAL(20,8) DEFAULT 0 NOT NULL;
    END IF;
    
    -- 添加福气统计字段
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'total_fortune_earned') THEN
        ALTER TABLE public.users ADD COLUMN total_fortune_earned DECIMAL(20,8) DEFAULT 0 NOT NULL;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'total_fortune_spent') THEN
        ALTER TABLE public.users ADD COLUMN total_fortune_spent DECIMAL(20,8) DEFAULT 0 NOT NULL;
    END IF;
    
    -- 添加福气等级字段
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'fortune_level') THEN
        ALTER TABLE public.users ADD COLUMN fortune_level INTEGER DEFAULT 1 NOT NULL;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'fortune_level_name') THEN
        ALTER TABLE public.users ADD COLUMN fortune_level_name VARCHAR(20) DEFAULT '初来乍到' NOT NULL;
    END IF;
    
    -- 添加签到相关字段
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'consecutive_checkin_days') THEN
        ALTER TABLE public.users ADD COLUMN consecutive_checkin_days INTEGER DEFAULT 0 NOT NULL;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'last_checkin_date') THEN
        ALTER TABLE public.users ADD COLUMN last_checkin_date DATE;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'total_checkin_days') THEN
        ALTER TABLE public.users ADD COLUMN total_checkin_days INTEGER DEFAULT 0 NOT NULL;
    END IF;
END $$;

-- 2. 创建福气交易记录表
CREATE TABLE IF NOT EXISTS public.fortune_transactions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    
    -- 交易信息
    transaction_type VARCHAR(30) NOT NULL,
    amount DECIMAL(20,8) NOT NULL, -- 正数为收入，负数为支出
    balance_before DECIMAL(20,8) NOT NULL,
    balance_after DECIMAL(20,8) NOT NULL,
    
    -- 关联信息
    reference_id UUID, -- 关联的业务ID（赌约ID、邀请ID等）
    reference_type VARCHAR(30), -- 关联类型
    description TEXT NOT NULL, -- 交易描述
    
    -- 区块链信息（仅充值/提现）
    haox_tx_hash VARCHAR(66), -- HAOX交易哈希
    haox_amount DECIMAL(20,8), -- HAOX数量
    exchange_rate DECIMAL(10,6) DEFAULT 1.0 NOT NULL, -- 汇率
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- 3. 创建每日签到记录表
CREATE TABLE IF NOT EXISTS public.daily_checkins (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    
    checkin_date DATE NOT NULL,
    consecutive_days INTEGER NOT NULL,
    base_reward DECIMAL(20,8) NOT NULL,
    bonus_reward DECIMAL(20,8) DEFAULT 0 NOT NULL,
    total_reward DECIMAL(20,8) NOT NULL,
    
    fortune_transaction_id UUID REFERENCES public.fortune_transactions(id),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    
    -- 确保每个用户每天只能签到一次
    UNIQUE(user_id, checkin_date)
);

-- 4. 创建分享奖励记录表
CREATE TABLE IF NOT EXISTS public.share_rewards (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    
    shared_content_type VARCHAR(30) NOT NULL, -- 'bet', 'invitation', 'achievement'
    shared_content_id UUID NOT NULL,
    platform VARCHAR(20) NOT NULL, -- 'telegram', 'twitter', 'facebook'
    
    reward_amount DECIMAL(20,8) NOT NULL,
    fortune_transaction_id UUID REFERENCES public.fortune_transactions(id),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    
    -- 确保同一内容在同一平台只能分享一次获得奖励
    UNIQUE(user_id, shared_content_id, platform)
);

-- 5. 创建HAOX充值提现记录表（与福气系统的兑换）
CREATE TABLE IF NOT EXISTS public.haox_fortune_exchanges (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    
    -- 交易类型
    exchange_type VARCHAR(10) NOT NULL, -- 'deposit' or 'withdraw'
    
    -- 金额信息
    haox_amount DECIMAL(20,8) NOT NULL,
    fortune_amount DECIMAL(20,8) NOT NULL,
    exchange_rate DECIMAL(10,6) DEFAULT 1.0 NOT NULL,
    
    -- 状态管理
    status VARCHAR(20) DEFAULT 'pending' NOT NULL,
    
    -- 区块链信息
    tx_hash VARCHAR(66), -- 交易哈希
    block_number BIGINT,
    confirmations INTEGER DEFAULT 0,
    
    -- 错误信息
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    
    -- 时间信息
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    confirmed_at TIMESTAMP WITH TIME ZONE,
    processed_at TIMESTAMP WITH TIME ZONE,
    
    -- 关联福气交易
    fortune_transaction_id UUID REFERENCES public.fortune_transactions(id)
);

-- 6. 创建福气等级配置表
CREATE TABLE IF NOT EXISTS public.fortune_levels (
    level INTEGER PRIMARY KEY,
    level_name VARCHAR(20) NOT NULL,
    min_fortune DECIMAL(20,8) NOT NULL,
    max_fortune DECIMAL(20,8),
    benefits JSONB, -- JSON格式的权益列表
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- 7. 扩展现有的邀请系统，添加福气奖励处理标记
DO $$
BEGIN
    -- 在invitation_stats表中添加福气奖励处理标记
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'invitation_stats') THEN
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'invitation_stats' AND column_name = 'fortune_reward_processed') THEN
            ALTER TABLE public.invitation_stats ADD COLUMN fortune_reward_processed BOOLEAN DEFAULT FALSE;
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'invitation_stats' AND column_name = 'fortune_reward_amount') THEN
            ALTER TABLE public.invitation_stats ADD COLUMN fortune_reward_amount DECIMAL(20,8) DEFAULT 0;
        END IF;
    END IF;
END $$;

-- 插入福气等级配置
INSERT INTO public.fortune_levels (level, level_name, min_fortune, max_fortune, benefits) VALUES
(1, '初来乍到', 0, 999.99999999, '["基础功能使用权"]'),
(2, '小有福气', 1000, 9999.99999999, '["基础功能使用权", "优先客服支持"]'),
(3, '福气满满', 10000, 99999.99999999, '["基础功能使用权", "优先客服支持", "可发起1vN赌约"]'),
(4, '福星高照', 100000, 999999.99999999, '["基础功能使用权", "优先客服支持", "可发起1vN赌约", "专属客服通道"]'),
(5, '福气无边', 1000000, NULL, '["基础功能使用权", "优先客服支持", "可发起1vN赌约", "专属客服通道", "内测新功能权限"]')
ON CONFLICT (level) DO NOTHING;

-- 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_users_fortune_level ON public.users(fortune_level);
CREATE INDEX IF NOT EXISTS idx_fortune_transactions_user_id ON public.fortune_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_fortune_transactions_type ON public.fortune_transactions(transaction_type);
CREATE INDEX IF NOT EXISTS idx_fortune_transactions_created_at ON public.fortune_transactions(created_at);
CREATE INDEX IF NOT EXISTS idx_daily_checkins_user_date ON public.daily_checkins(user_id, checkin_date);
CREATE INDEX IF NOT EXISTS idx_share_rewards_user_id ON public.share_rewards(user_id);
CREATE INDEX IF NOT EXISTS idx_haox_fortune_exchanges_user_id ON public.haox_fortune_exchanges(user_id);
CREATE INDEX IF NOT EXISTS idx_haox_fortune_exchanges_status ON public.haox_fortune_exchanges(status);

-- 启用行级安全策略
ALTER TABLE public.fortune_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.daily_checkins ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.share_rewards ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.haox_fortune_exchanges ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.fortune_levels ENABLE ROW LEVEL SECURITY;

-- 创建RLS策略
-- 用户只能查看自己的福气交易记录
CREATE POLICY "Users can view own fortune transactions" ON public.fortune_transactions
    FOR SELECT USING (auth.uid() = user_id);

-- 用户只能查看自己的签到记录
CREATE POLICY "Users can view own checkin records" ON public.daily_checkins
    FOR SELECT USING (auth.uid() = user_id);

-- 用户只能查看自己的分享奖励记录
CREATE POLICY "Users can view own share rewards" ON public.share_rewards
    FOR SELECT USING (auth.uid() = user_id);

-- 用户只能查看自己的HAOX兑换记录
CREATE POLICY "Users can view own haox exchanges" ON public.haox_fortune_exchanges
    FOR SELECT USING (auth.uid() = user_id);

-- 所有人都可以查看福气等级配置
CREATE POLICY "Anyone can view fortune levels" ON public.fortune_levels
    FOR SELECT USING (true);

-- 添加注释
COMMENT ON TABLE public.fortune_transactions IS '福气交易记录表';
COMMENT ON TABLE public.daily_checkins IS '每日签到记录表';
COMMENT ON TABLE public.share_rewards IS '分享奖励记录表';
COMMENT ON TABLE public.haox_fortune_exchanges IS 'HAOX与福气兑换记录表';
COMMENT ON TABLE public.fortune_levels IS '福气等级配置表';

-- 验证表创建
DO $$
DECLARE
    table_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO table_count
    FROM information_schema.tables
    WHERE table_schema = 'public'
    AND table_name IN ('fortune_transactions', 'daily_checkins', 'share_rewards', 'haox_fortune_exchanges', 'fortune_levels');
    
    RAISE NOTICE '福气系统已创建 % 个新表', table_count;
END $$;
