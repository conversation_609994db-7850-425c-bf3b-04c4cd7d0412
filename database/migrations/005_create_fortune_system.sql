-- 创建福气系统数据库表
-- 将现有HAOX奖励系统改造为福气系统

-- 1. 创建用户福气账户表
CREATE TABLE user_fortune (
    user_id UUID PRIMARY KEY,
    
    -- 福气余额
    available_fortune DECIMAL(20,8) DEFAULT 0 NOT NULL,
    locked_fortune DECIMAL(20,8) DEFAULT 0 NOT NULL,
    
    -- 统计数据
    total_earned DECIMAL(20,8) DEFAULT 0 NOT NULL,
    total_spent DECIMAL(20,8) DEFAULT 0 NOT NULL,
    total_deposited DECIMAL(20,8) DEFAULT 0 NOT NULL,
    total_withdrawn DECIMAL(20,8) DEFAULT 0 NOT NULL,
    
    -- 福气等级
    fortune_level INTEGER DEFAULT 1 NOT NULL,
    fortune_level_name VARCHAR(20) DEFAULT '初来乍到' NOT NULL,
    
    -- 每日签到统计
    consecutive_checkin_days INTEGER DEFAULT 0 NOT NULL,
    last_checkin_date DATE,
    total_checkin_days INTEGER DEFAULT 0 NOT NULL,
    
    -- 邀请统计
    total_invitations INTEGER DEFAULT 0 NOT NULL,
    successful_invitations INTEGER DEFAULT 0 NOT NULL,
    
    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- 2. 创建福气交易记录表
CREATE TABLE fortune_transactions (
    id UUID PRIMARY KEY,
    user_id UUID NOT NULL,
    
    -- 交易信息
    transaction_type VARCHAR(30) NOT NULL,
    amount DECIMAL(20,8) NOT NULL, -- 正数为收入，负数为支出
    balance_before DECIMAL(20,8) NOT NULL,
    balance_after DECIMAL(20,8) NOT NULL,
    
    -- 关联信息
    reference_id UUID, -- 关联的业务ID（赌约ID、邀请ID等）
    reference_type VARCHAR(30), -- 关联类型
    description TEXT NOT NULL, -- 交易描述
    
    -- 区块链信息（仅充值/提现）
    haox_tx_hash VARCHAR(66), -- HAOX交易哈希
    haox_amount DECIMAL(20,8), -- HAOX数量
    exchange_rate DECIMAL(10,6) DEFAULT 1.0 NOT NULL, -- 汇率
    
    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- 3. 创建HAOX充值提现记录表
CREATE TABLE haox_transactions (
    id UUID PRIMARY KEY,
    user_id UUID NOT NULL,
    
    -- 交易类型
    transaction_type VARCHAR(10) NOT NULL, -- 'deposit' or 'withdraw'
    
    -- 金额信息
    haox_amount DECIMAL(20,8) NOT NULL,
    fortune_amount DECIMAL(20,8) NOT NULL,
    exchange_rate DECIMAL(10,6) DEFAULT 1.0 NOT NULL,
    
    -- Gas费信息（仅提现）
    gas_fee_level VARCHAR(10), -- 'economy', 'standard', 'fast'
    estimated_gas_fee DECIMAL(20,8),
    actual_gas_fee DECIMAL(20,8),
    
    -- 状态管理
    status VARCHAR(20) DEFAULT 'pending' NOT NULL,
    
    -- 区块链信息
    tx_hash VARCHAR(66), -- 交易哈希
    block_number BIGINT,
    confirmations INTEGER DEFAULT 0,
    
    -- 多签信息（仅提现）
    multisig_tx_hash VARCHAR(66),
    required_signatures INTEGER,
    current_signatures INTEGER DEFAULT 0,
    
    -- 错误信息
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    
    -- 时间信息
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    confirmed_at TIMESTAMP,
    processed_at TIMESTAMP,
    
    -- 关联福气交易
    fortune_transaction_id UUID
);

-- 4. 创建每日签到记录表
CREATE TABLE daily_checkins (
    id UUID PRIMARY KEY,
    user_id UUID NOT NULL,
    
    checkin_date DATE NOT NULL,
    consecutive_days INTEGER NOT NULL,
    base_reward DECIMAL(20,8) NOT NULL,
    bonus_reward DECIMAL(20,8) DEFAULT 0 NOT NULL,
    total_reward DECIMAL(20,8) NOT NULL,
    
    fortune_transaction_id UUID,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- 5. 创建分享奖励记录表
CREATE TABLE share_rewards (
    id UUID PRIMARY KEY,
    user_id UUID NOT NULL,
    
    shared_content_type VARCHAR(30) NOT NULL, -- 'bet', 'invitation', 'achievement'
    shared_content_id UUID NOT NULL,
    platform VARCHAR(20) NOT NULL, -- 'telegram', 'twitter', 'facebook'
    
    reward_amount DECIMAL(20,8) NOT NULL,
    fortune_transaction_id UUID,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- 6. 创建福气等级配置表
CREATE TABLE fortune_levels (
    level INTEGER PRIMARY KEY,
    level_name VARCHAR(20) NOT NULL,
    min_fortune DECIMAL(20,8) NOT NULL,
    max_fortune DECIMAL(20,8),
    benefits TEXT, -- JSON格式的权益列表
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- 插入福气等级配置
INSERT INTO fortune_levels (level, level_name, min_fortune, max_fortune, benefits) VALUES
(1, '初来乍到', 0, 999.99999999, '["基础功能使用权"]'),
(2, '小有福气', 1000, 9999.99999999, '["基础功能使用权", "优先客服支持"]'),
(3, '福气满满', 10000, 99999.99999999, '["基础功能使用权", "优先客服支持", "可发起1vN赌约"]'),
(4, '福星高照', 100000, 999999.99999999, '["基础功能使用权", "优先客服支持", "可发起1vN赌约", "专属客服通道"]'),
(5, '福气无边', 1000000, NULL, '["基础功能使用权", "优先客服支持", "可发起1vN赌约", "专属客服通道", "内测新功能权限"]');

-- 创建索引优化查询性能
CREATE INDEX idx_user_fortune_level ON user_fortune(fortune_level);
CREATE INDEX idx_fortune_transactions_user_id ON fortune_transactions(user_id);
CREATE INDEX idx_fortune_transactions_type ON fortune_transactions(transaction_type);
CREATE INDEX idx_fortune_transactions_created_at ON fortune_transactions(created_at);
CREATE INDEX idx_haox_transactions_user_id ON haox_transactions(user_id);
CREATE INDEX idx_haox_transactions_status ON haox_transactions(status);
CREATE INDEX idx_daily_checkins_user_date ON daily_checkins(user_id, checkin_date);
CREATE INDEX idx_share_rewards_user_id ON share_rewards(user_id);
CREATE INDEX idx_user_invitations_inviter ON user_invitations(inviter_id);
CREATE INDEX idx_user_invitations_invitee ON user_invitations(invitee_id);
CREATE INDEX idx_user_invitations_status ON user_invitations(status);

-- 7. 创建用户邀请关系表
CREATE TABLE user_invitations (
    id UUID PRIMARY KEY,
    inviter_id UUID NOT NULL,
    invitee_id UUID NOT NULL,
    invite_code VARCHAR(50),
    status VARCHAR(20) DEFAULT 'pending' NOT NULL,
    reward_processed BOOLEAN DEFAULT FALSE,
    processed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- 创建唯一约束
ALTER TABLE daily_checkins ADD CONSTRAINT unique_user_checkin_date UNIQUE(user_id, checkin_date);
ALTER TABLE share_rewards ADD CONSTRAINT unique_user_share_content UNIQUE(user_id, shared_content_id, platform);
ALTER TABLE user_invitations ADD CONSTRAINT unique_invitation_pair UNIQUE(inviter_id, invitee_id);
