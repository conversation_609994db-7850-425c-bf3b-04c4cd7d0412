-- 创建用户活动记录表
-- 用于跟踪用户在平台上的各种活动

-- 创建用户活动表
CREATE TABLE IF NOT EXISTS user_activities (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    
    -- 活动信息
    activity_type VARCHAR(50) NOT NULL,
    activity_category VARCHAR(30) NOT NULL,
    activity_description TEXT,
    
    -- 活动详情
    target_type VARCHAR(50), -- 目标类型：task, transaction, wallet, profile等
    target_id VARCHAR(100),  -- 目标ID
    
    -- 活动结果
    status VARCHAR(20) DEFAULT 'completed' NOT NULL,
    -- completed: 已完成, failed: 失败, pending: 进行中, cancelled: 已取消
    
    -- 奖励信息
    reward_amount DECIMAL(20, 8) DEFAULT 0,
    reward_currency VARCHAR(10) DEFAULT 'HAOX',
    
    -- 元数据
    metadata JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    
    -- 时间信息
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    completed_at TIMESTAMP WITH TIME ZONE,
    
    -- 系统字段
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    
    -- 约束
    CONSTRAINT user_activities_status_check CHECK (
        status IN ('completed', 'failed', 'pending', 'cancelled')
    ),
    CONSTRAINT user_activities_reward_check CHECK (reward_amount >= 0)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_user_activities_user_id ON user_activities(user_id);
CREATE INDEX IF NOT EXISTS idx_user_activities_type ON user_activities(activity_type);
CREATE INDEX IF NOT EXISTS idx_user_activities_category ON user_activities(activity_category);
CREATE INDEX IF NOT EXISTS idx_user_activities_status ON user_activities(status);
CREATE INDEX IF NOT EXISTS idx_user_activities_target ON user_activities(target_type, target_id);
CREATE INDEX IF NOT EXISTS idx_user_activities_created_at ON user_activities(created_at);
CREATE INDEX IF NOT EXISTS idx_user_activities_completed_at ON user_activities(completed_at);
CREATE INDEX IF NOT EXISTS idx_user_activities_reward ON user_activities(reward_amount) WHERE reward_amount > 0;

-- 创建复合索引
CREATE INDEX IF NOT EXISTS idx_user_activities_user_type_date ON user_activities(user_id, activity_type, created_at);
CREATE INDEX IF NOT EXISTS idx_user_activities_user_category_date ON user_activities(user_id, activity_category, created_at);

-- 创建用户活动统计表
CREATE TABLE IF NOT EXISTS user_activity_stats (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    
    -- 活动统计
    total_activities INTEGER DEFAULT 0 NOT NULL,
    completed_activities INTEGER DEFAULT 0 NOT NULL,
    failed_activities INTEGER DEFAULT 0 NOT NULL,
    pending_activities INTEGER DEFAULT 0 NOT NULL,
    
    -- 分类统计
    auth_activities INTEGER DEFAULT 0 NOT NULL,
    wallet_activities INTEGER DEFAULT 0 NOT NULL,
    task_activities INTEGER DEFAULT 0 NOT NULL,
    social_activities INTEGER DEFAULT 0 NOT NULL,
    trading_activities INTEGER DEFAULT 0 NOT NULL,
    
    -- 奖励统计
    total_rewards DECIMAL(20, 8) DEFAULT 0 NOT NULL,
    total_reward_activities INTEGER DEFAULT 0 NOT NULL,
    
    -- 时间统计
    first_activity_at TIMESTAMP WITH TIME ZONE,
    last_activity_at TIMESTAMP WITH TIME ZONE,
    most_active_hour INTEGER, -- 0-23，最活跃的小时
    most_active_day INTEGER,  -- 0-6，最活跃的星期几（0=周日）
    
    -- 连续活动统计
    current_streak_days INTEGER DEFAULT 0 NOT NULL,
    longest_streak_days INTEGER DEFAULT 0 NOT NULL,
    last_active_date DATE,
    
    -- 系统字段
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    
    -- 约束
    CONSTRAINT user_activity_stats_user_id_unique UNIQUE (user_id),
    CONSTRAINT user_activity_stats_activities_check CHECK (
        completed_activities + failed_activities + pending_activities <= total_activities
    ),
    CONSTRAINT user_activity_stats_hour_check CHECK (
        most_active_hour IS NULL OR (most_active_hour >= 0 AND most_active_hour <= 23)
    ),
    CONSTRAINT user_activity_stats_day_check CHECK (
        most_active_day IS NULL OR (most_active_day >= 0 AND most_active_day <= 6)
    ),
    CONSTRAINT user_activity_stats_streak_check CHECK (
        current_streak_days >= 0 AND longest_streak_days >= 0 AND
        current_streak_days <= longest_streak_days
    )
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_user_activity_stats_user_id ON user_activity_stats(user_id);
CREATE INDEX IF NOT EXISTS idx_user_activity_stats_total_activities ON user_activity_stats(total_activities);
CREATE INDEX IF NOT EXISTS idx_user_activity_stats_total_rewards ON user_activity_stats(total_rewards);
CREATE INDEX IF NOT EXISTS idx_user_activity_stats_streak ON user_activity_stats(current_streak_days);
CREATE INDEX IF NOT EXISTS idx_user_activity_stats_last_active ON user_activity_stats(last_active_date);
CREATE INDEX IF NOT EXISTS idx_user_activity_stats_updated_at ON user_activity_stats(updated_at);

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_user_activity_stats_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_user_activity_stats_updated_at
    BEFORE UPDATE ON user_activity_stats
    FOR EACH ROW
    EXECUTE FUNCTION update_user_activity_stats_updated_at();

-- 创建活动类型配置表
CREATE TABLE IF NOT EXISTS activity_type_config (
    id BIGSERIAL PRIMARY KEY,
    activity_type VARCHAR(50) NOT NULL,
    activity_category VARCHAR(30) NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    icon VARCHAR(50),
    color VARCHAR(20),
    
    -- 奖励配置
    default_reward DECIMAL(20, 8) DEFAULT 0,
    max_daily_rewards INTEGER DEFAULT 0, -- 0表示无限制
    
    -- 配置选项
    is_active BOOLEAN DEFAULT true NOT NULL,
    requires_verification BOOLEAN DEFAULT false NOT NULL,
    auto_complete BOOLEAN DEFAULT true NOT NULL,
    
    -- 系统字段
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    
    -- 约束
    CONSTRAINT activity_type_config_type_unique UNIQUE (activity_type),
    CONSTRAINT activity_type_config_reward_check CHECK (default_reward >= 0),
    CONSTRAINT activity_type_config_daily_check CHECK (max_daily_rewards >= 0)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_activity_type_config_type ON activity_type_config(activity_type);
CREATE INDEX IF NOT EXISTS idx_activity_type_config_category ON activity_type_config(activity_category);
CREATE INDEX IF NOT EXISTS idx_activity_type_config_active ON activity_type_config(is_active);

-- 创建更新时间触发器
CREATE TRIGGER trigger_update_activity_type_config_updated_at
    BEFORE UPDATE ON activity_type_config
    FOR EACH ROW
    EXECUTE FUNCTION update_user_activity_stats_updated_at();

-- 插入默认活动类型配置
INSERT INTO activity_type_config (activity_type, activity_category, display_name, description, icon, color, default_reward, max_daily_rewards) VALUES
-- 认证相关
('user_login', 'auth', '用户登录', '用户通过Telegram登录系统', 'LogIn', 'blue', 1.0, 1),
('user_logout', 'auth', '用户登出', '用户退出系统', 'LogOut', 'gray', 0, 0),
('profile_update', 'auth', '更新资料', '用户更新个人资料信息', 'User', 'green', 5.0, 1),

-- 钱包相关
('wallet_connect', 'wallet', '连接钱包', '用户连接Web3钱包', 'Wallet', 'purple', 10.0, 1),
('wallet_disconnect', 'wallet', '断开钱包', '用户断开Web3钱包连接', 'WalletX', 'gray', 0, 0),
('token_deposit', 'wallet', '代币充值', '用户向钱包充值代币', 'ArrowDownToLine', 'green', 0, 0),
('token_withdraw', 'wallet', '代币提现', '用户从钱包提现代币', 'ArrowUpFromLine', 'orange', 0, 0),

-- 任务相关
('task_start', 'task', '开始任务', '用户开始执行任务', 'Play', 'blue', 0, 0),
('task_complete', 'task', '完成任务', '用户成功完成任务', 'CheckCircle', 'green', 0, 0),
('task_fail', 'task', '任务失败', '用户任务执行失败', 'XCircle', 'red', 0, 0),
('task_submit', 'task', '提交任务', '用户提交任务完成证明', 'Upload', 'yellow', 0, 0),

-- 社交相关
('social_share', 'social', '社交分享', '用户分享内容到社交媒体', 'Share2', 'blue', 2.0, 5),
('social_follow', 'social', '关注账号', '用户关注官方社交媒体账号', 'UserPlus', 'green', 5.0, 3),
('social_like', 'social', '点赞内容', '用户点赞社交媒体内容', 'Heart', 'red', 1.0, 10),
('social_comment', 'social', '评论内容', '用户评论社交媒体内容', 'MessageCircle', 'blue', 3.0, 5),
('invite_friend', 'social', '邀请朋友', '用户邀请朋友加入平台', 'UserPlus', 'purple', 20.0, 0),

-- 交易相关
('token_purchase', 'trading', '购买代币', '用户购买HAOX代币', 'ShoppingCart', 'green', 0, 0),
('token_sale', 'trading', '出售代币', '用户出售HAOX代币', 'DollarSign', 'orange', 0, 0),
('presale_participate', 'trading', '参与预售', '用户参与代币预售活动', 'Zap', 'yellow', 0, 0),

-- 系统相关
('system_notification', 'system', '系统通知', '系统发送通知给用户', 'Bell', 'gray', 0, 0),
('error_report', 'system', '错误报告', '用户报告系统错误', 'AlertTriangle', 'red', 10.0, 3),
('feedback_submit', 'system', '提交反馈', '用户提交意见反馈', 'MessageSquare', 'blue', 5.0, 1)

ON CONFLICT (activity_type) DO NOTHING;

-- 创建统计更新函数
CREATE OR REPLACE FUNCTION update_user_activity_stats_for_user(p_user_id BIGINT)
RETURNS VOID AS $$
DECLARE
    v_total_activities INTEGER;
    v_completed_activities INTEGER;
    v_failed_activities INTEGER;
    v_pending_activities INTEGER;
    v_auth_activities INTEGER;
    v_wallet_activities INTEGER;
    v_task_activities INTEGER;
    v_social_activities INTEGER;
    v_trading_activities INTEGER;
    v_total_rewards DECIMAL(20, 8);
    v_total_reward_activities INTEGER;
    v_first_activity_at TIMESTAMP WITH TIME ZONE;
    v_last_activity_at TIMESTAMP WITH TIME ZONE;
    v_most_active_hour INTEGER;
    v_most_active_day INTEGER;
    v_last_active_date DATE;
    v_current_streak INTEGER;
    v_longest_streak INTEGER;
BEGIN
    -- 计算基本统计
    SELECT 
        COUNT(*),
        COUNT(*) FILTER (WHERE status = 'completed'),
        COUNT(*) FILTER (WHERE status = 'failed'),
        COUNT(*) FILTER (WHERE status = 'pending'),
        COUNT(*) FILTER (WHERE activity_category = 'auth'),
        COUNT(*) FILTER (WHERE activity_category = 'wallet'),
        COUNT(*) FILTER (WHERE activity_category = 'task'),
        COUNT(*) FILTER (WHERE activity_category = 'social'),
        COUNT(*) FILTER (WHERE activity_category = 'trading'),
        COALESCE(SUM(reward_amount), 0),
        COUNT(*) FILTER (WHERE reward_amount > 0),
        MIN(created_at),
        MAX(created_at)
    INTO 
        v_total_activities,
        v_completed_activities,
        v_failed_activities,
        v_pending_activities,
        v_auth_activities,
        v_wallet_activities,
        v_task_activities,
        v_social_activities,
        v_trading_activities,
        v_total_rewards,
        v_total_reward_activities,
        v_first_activity_at,
        v_last_activity_at
    FROM user_activities
    WHERE user_id = p_user_id;

    -- 计算最活跃时间
    SELECT EXTRACT(HOUR FROM created_at)::INTEGER
    INTO v_most_active_hour
    FROM user_activities
    WHERE user_id = p_user_id
    GROUP BY EXTRACT(HOUR FROM created_at)
    ORDER BY COUNT(*) DESC
    LIMIT 1;

    SELECT EXTRACT(DOW FROM created_at)::INTEGER
    INTO v_most_active_day
    FROM user_activities
    WHERE user_id = p_user_id
    GROUP BY EXTRACT(DOW FROM created_at)
    ORDER BY COUNT(*) DESC
    LIMIT 1;

    -- 计算最后活跃日期
    SELECT DATE(MAX(created_at))
    INTO v_last_active_date
    FROM user_activities
    WHERE user_id = p_user_id;

    -- 计算连续活跃天数（简化版本）
    WITH daily_activities AS (
        SELECT DISTINCT DATE(created_at) as activity_date
        FROM user_activities
        WHERE user_id = p_user_id
        ORDER BY activity_date DESC
    ),
    streak_calc AS (
        SELECT 
            activity_date,
            ROW_NUMBER() OVER (ORDER BY activity_date DESC) as rn,
            activity_date - INTERVAL '1 day' * (ROW_NUMBER() OVER (ORDER BY activity_date DESC) - 1) as streak_date
        FROM daily_activities
    )
    SELECT 
        COUNT(*) FILTER (WHERE streak_date = CURRENT_DATE - INTERVAL '1 day' * (rn - 1)),
        COUNT(*)
    INTO v_current_streak, v_longest_streak
    FROM streak_calc;

    -- 更新或插入统计数据
    INSERT INTO user_activity_stats (
        user_id, total_activities, completed_activities, failed_activities, pending_activities,
        auth_activities, wallet_activities, task_activities, social_activities, trading_activities,
        total_rewards, total_reward_activities,
        first_activity_at, last_activity_at, most_active_hour, most_active_day,
        current_streak_days, longest_streak_days, last_active_date
    ) VALUES (
        p_user_id, v_total_activities, v_completed_activities, v_failed_activities, v_pending_activities,
        v_auth_activities, v_wallet_activities, v_task_activities, v_social_activities, v_trading_activities,
        v_total_rewards, v_total_reward_activities,
        v_first_activity_at, v_last_activity_at, v_most_active_hour, v_most_active_day,
        COALESCE(v_current_streak, 0), GREATEST(COALESCE(v_current_streak, 0), COALESCE(v_longest_streak, 0)), v_last_active_date
    )
    ON CONFLICT (user_id) DO UPDATE SET
        total_activities = EXCLUDED.total_activities,
        completed_activities = EXCLUDED.completed_activities,
        failed_activities = EXCLUDED.failed_activities,
        pending_activities = EXCLUDED.pending_activities,
        auth_activities = EXCLUDED.auth_activities,
        wallet_activities = EXCLUDED.wallet_activities,
        task_activities = EXCLUDED.task_activities,
        social_activities = EXCLUDED.social_activities,
        trading_activities = EXCLUDED.trading_activities,
        total_rewards = EXCLUDED.total_rewards,
        total_reward_activities = EXCLUDED.total_reward_activities,
        first_activity_at = EXCLUDED.first_activity_at,
        last_activity_at = EXCLUDED.last_activity_at,
        most_active_hour = EXCLUDED.most_active_hour,
        most_active_day = EXCLUDED.most_active_day,
        current_streak_days = EXCLUDED.current_streak_days,
        longest_streak_days = EXCLUDED.longest_streak_days,
        last_active_date = EXCLUDED.last_active_date,
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql;

-- 创建触发器自动更新统计
CREATE OR REPLACE FUNCTION trigger_update_user_activity_stats()
RETURNS TRIGGER AS $$
BEGIN
    -- 更新用户的活动统计
    PERFORM update_user_activity_stats_for_user(NEW.user_id);
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_user_activities_stats_update
    AFTER INSERT OR UPDATE ON user_activities
    FOR EACH ROW
    EXECUTE FUNCTION trigger_update_user_activity_stats();

-- 添加注释
COMMENT ON TABLE user_activities IS '用户活动记录表';
COMMENT ON TABLE user_activity_stats IS '用户活动统计表';
COMMENT ON TABLE activity_type_config IS '活动类型配置表';
COMMENT ON FUNCTION update_user_activity_stats_for_user(BIGINT) IS '更新指定用户的活动统计数据';
