-- 创建邀请统计表
-- 用于跟踪用户邀请活动和统计数据

-- 创建邀请统计表
CREATE TABLE IF NOT EXISTS invitation_stats (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    
    -- 邀请统计
    total_invitations INTEGER DEFAULT 0 NOT NULL,
    successful_invitations INTEGER DEFAULT 0 NOT NULL,
    pending_invitations INTEGER DEFAULT 0 NOT NULL,
    
    -- 奖励统计
    total_rewards DECIMAL(20, 8) DEFAULT 0 NOT NULL,
    pending_rewards DECIMAL(20, 8) DEFAULT 0 NOT NULL,
    claimed_rewards DECIMAL(20, 8) DEFAULT 0 NOT NULL,
    
    -- 等级信息
    invitation_level INTEGER DEFAULT 1 NOT NULL,
    level_progress DECIMAL(5, 2) DEFAULT 0 NOT NULL,
    
    -- 时间统计
    first_invitation_at TIMESTAMP WITH TIME ZONE,
    last_invitation_at TIMESTAMP WITH TIME ZONE,
    last_reward_at TIMESTAMP WITH TIME ZONE,
    
    -- 系统字段
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    
    -- 约束
    CONSTRAINT invitation_stats_user_id_unique UNIQUE (user_id),
    CONSTRAINT invitation_stats_totals_check CHECK (
        successful_invitations <= total_invitations AND
        pending_invitations <= total_invitations AND
        claimed_rewards <= total_rewards AND
        pending_rewards <= total_rewards
    ),
    CONSTRAINT invitation_stats_level_check CHECK (invitation_level >= 1),
    CONSTRAINT invitation_stats_progress_check CHECK (level_progress >= 0 AND level_progress <= 100)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_invitation_stats_user_id ON invitation_stats(user_id);
CREATE INDEX IF NOT EXISTS idx_invitation_stats_level ON invitation_stats(invitation_level);
CREATE INDEX IF NOT EXISTS idx_invitation_stats_total_invitations ON invitation_stats(total_invitations);
CREATE INDEX IF NOT EXISTS idx_invitation_stats_total_rewards ON invitation_stats(total_rewards);
CREATE INDEX IF NOT EXISTS idx_invitation_stats_updated_at ON invitation_stats(updated_at);

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_invitation_stats_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_invitation_stats_updated_at
    BEFORE UPDATE ON invitation_stats
    FOR EACH ROW
    EXECUTE FUNCTION update_invitation_stats_updated_at();

-- 创建邀请详情表
CREATE TABLE IF NOT EXISTS invitation_details (
    id BIGSERIAL PRIMARY KEY,
    inviter_id BIGINT NOT NULL,
    invitee_id BIGINT,
    invitation_code VARCHAR(50) NOT NULL,
    
    -- 邀请状态
    status VARCHAR(20) DEFAULT 'pending' NOT NULL,
    -- pending: 待接受, accepted: 已接受, expired: 已过期, cancelled: 已取消
    
    -- 奖励信息
    reward_amount DECIMAL(20, 8) DEFAULT 0 NOT NULL,
    reward_status VARCHAR(20) DEFAULT 'pending' NOT NULL,
    -- pending: 待发放, processing: 处理中, completed: 已发放, failed: 发放失败
    
    -- 时间信息
    invited_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    accepted_at TIMESTAMP WITH TIME ZONE,
    expired_at TIMESTAMP WITH TIME ZONE,
    reward_processed_at TIMESTAMP WITH TIME ZONE,
    
    -- 元数据
    invitation_source VARCHAR(50), -- 邀请来源：telegram, link, qr_code等
    metadata JSONB DEFAULT '{}',
    
    -- 系统字段
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    
    -- 约束
    CONSTRAINT invitation_details_code_unique UNIQUE (invitation_code),
    CONSTRAINT invitation_details_status_check CHECK (
        status IN ('pending', 'accepted', 'expired', 'cancelled')
    ),
    CONSTRAINT invitation_details_reward_status_check CHECK (
        reward_status IN ('pending', 'processing', 'completed', 'failed')
    )
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_invitation_details_inviter_id ON invitation_details(inviter_id);
CREATE INDEX IF NOT EXISTS idx_invitation_details_invitee_id ON invitation_details(invitee_id);
CREATE INDEX IF NOT EXISTS idx_invitation_details_code ON invitation_details(invitation_code);
CREATE INDEX IF NOT EXISTS idx_invitation_details_status ON invitation_details(status);
CREATE INDEX IF NOT EXISTS idx_invitation_details_reward_status ON invitation_details(reward_status);
CREATE INDEX IF NOT EXISTS idx_invitation_details_invited_at ON invitation_details(invited_at);
CREATE INDEX IF NOT EXISTS idx_invitation_details_source ON invitation_details(invitation_source);

-- 创建更新时间触发器
CREATE TRIGGER trigger_update_invitation_details_updated_at
    BEFORE UPDATE ON invitation_details
    FOR EACH ROW
    EXECUTE FUNCTION update_invitation_stats_updated_at();

-- 创建邀请奖励配置表
CREATE TABLE IF NOT EXISTS invitation_reward_config (
    id BIGSERIAL PRIMARY KEY,
    level INTEGER NOT NULL,
    min_invitations INTEGER NOT NULL,
    max_invitations INTEGER,
    reward_per_invitation DECIMAL(20, 8) NOT NULL,
    bonus_reward DECIMAL(20, 8) DEFAULT 0 NOT NULL,
    level_name VARCHAR(50) NOT NULL,
    level_description TEXT,
    
    -- 系统字段
    is_active BOOLEAN DEFAULT true NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    
    -- 约束
    CONSTRAINT invitation_reward_config_level_unique UNIQUE (level),
    CONSTRAINT invitation_reward_config_level_check CHECK (level >= 1),
    CONSTRAINT invitation_reward_config_invitations_check CHECK (
        min_invitations >= 0 AND 
        (max_invitations IS NULL OR max_invitations >= min_invitations)
    ),
    CONSTRAINT invitation_reward_config_rewards_check CHECK (
        reward_per_invitation >= 0 AND bonus_reward >= 0
    )
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_invitation_reward_config_level ON invitation_reward_config(level);
CREATE INDEX IF NOT EXISTS idx_invitation_reward_config_active ON invitation_reward_config(is_active);

-- 创建更新时间触发器
CREATE TRIGGER trigger_update_invitation_reward_config_updated_at
    BEFORE UPDATE ON invitation_reward_config
    FOR EACH ROW
    EXECUTE FUNCTION update_invitation_stats_updated_at();

-- 插入默认奖励配置
INSERT INTO invitation_reward_config (level, min_invitations, max_invitations, reward_per_invitation, bonus_reward, level_name, level_description) VALUES
(1, 0, 9, 10.0, 0, '新手邀请者', '刚开始邀请朋友的用户'),
(2, 10, 24, 12.0, 50.0, '活跃邀请者', '已邀请10-24位朋友的用户'),
(3, 25, 49, 15.0, 100.0, '超级邀请者', '已邀请25-49位朋友的用户'),
(4, 50, 99, 20.0, 200.0, '邀请大师', '已邀请50-99位朋友的用户'),
(5, 100, NULL, 25.0, 500.0, '邀请传奇', '已邀请100位以上朋友的用户')
ON CONFLICT (level) DO NOTHING;

-- 创建统计更新函数
CREATE OR REPLACE FUNCTION update_invitation_stats_for_user(p_user_id BIGINT)
RETURNS VOID AS $$
DECLARE
    v_total_invitations INTEGER;
    v_successful_invitations INTEGER;
    v_pending_invitations INTEGER;
    v_total_rewards DECIMAL(20, 8);
    v_pending_rewards DECIMAL(20, 8);
    v_claimed_rewards DECIMAL(20, 8);
    v_first_invitation_at TIMESTAMP WITH TIME ZONE;
    v_last_invitation_at TIMESTAMP WITH TIME ZONE;
    v_last_reward_at TIMESTAMP WITH TIME ZONE;
    v_invitation_level INTEGER;
    v_level_progress DECIMAL(5, 2);
    v_next_level_min INTEGER;
BEGIN
    -- 计算邀请统计
    SELECT 
        COUNT(*),
        COUNT(*) FILTER (WHERE status = 'accepted'),
        COUNT(*) FILTER (WHERE status = 'pending'),
        COALESCE(SUM(reward_amount), 0),
        COALESCE(SUM(reward_amount) FILTER (WHERE reward_status = 'pending'), 0),
        COALESCE(SUM(reward_amount) FILTER (WHERE reward_status = 'completed'), 0),
        MIN(invited_at),
        MAX(invited_at),
        MAX(reward_processed_at)
    INTO 
        v_total_invitations,
        v_successful_invitations,
        v_pending_invitations,
        v_total_rewards,
        v_pending_rewards,
        v_claimed_rewards,
        v_first_invitation_at,
        v_last_invitation_at,
        v_last_reward_at
    FROM invitation_details
    WHERE inviter_id = p_user_id;

    -- 计算邀请等级
    SELECT level INTO v_invitation_level
    FROM invitation_reward_config
    WHERE is_active = true
      AND min_invitations <= v_successful_invitations
      AND (max_invitations IS NULL OR max_invitations >= v_successful_invitations)
    ORDER BY level DESC
    LIMIT 1;

    -- 如果没有找到等级，设为1级
    IF v_invitation_level IS NULL THEN
        v_invitation_level := 1;
    END IF;

    -- 计算等级进度
    SELECT min_invitations INTO v_next_level_min
    FROM invitation_reward_config
    WHERE is_active = true AND level = v_invitation_level + 1;

    IF v_next_level_min IS NOT NULL THEN
        v_level_progress := LEAST(100.0, (v_successful_invitations::DECIMAL / v_next_level_min::DECIMAL) * 100.0);
    ELSE
        v_level_progress := 100.0; -- 已达到最高等级
    END IF;

    -- 更新或插入统计数据
    INSERT INTO invitation_stats (
        user_id, total_invitations, successful_invitations, pending_invitations,
        total_rewards, pending_rewards, claimed_rewards,
        invitation_level, level_progress,
        first_invitation_at, last_invitation_at, last_reward_at
    ) VALUES (
        p_user_id, v_total_invitations, v_successful_invitations, v_pending_invitations,
        v_total_rewards, v_pending_rewards, v_claimed_rewards,
        v_invitation_level, v_level_progress,
        v_first_invitation_at, v_last_invitation_at, v_last_reward_at
    )
    ON CONFLICT (user_id) DO UPDATE SET
        total_invitations = EXCLUDED.total_invitations,
        successful_invitations = EXCLUDED.successful_invitations,
        pending_invitations = EXCLUDED.pending_invitations,
        total_rewards = EXCLUDED.total_rewards,
        pending_rewards = EXCLUDED.pending_rewards,
        claimed_rewards = EXCLUDED.claimed_rewards,
        invitation_level = EXCLUDED.invitation_level,
        level_progress = EXCLUDED.level_progress,
        first_invitation_at = EXCLUDED.first_invitation_at,
        last_invitation_at = EXCLUDED.last_invitation_at,
        last_reward_at = EXCLUDED.last_reward_at,
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql;

-- 创建触发器自动更新统计
CREATE OR REPLACE FUNCTION trigger_update_invitation_stats()
RETURNS TRIGGER AS $$
BEGIN
    -- 更新邀请者的统计
    PERFORM update_invitation_stats_for_user(NEW.inviter_id);
    
    -- 如果是更新操作且邀请者发生变化，也更新旧邀请者的统计
    IF TG_OP = 'UPDATE' AND OLD.inviter_id != NEW.inviter_id THEN
        PERFORM update_invitation_stats_for_user(OLD.inviter_id);
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_invitation_details_stats_update
    AFTER INSERT OR UPDATE ON invitation_details
    FOR EACH ROW
    EXECUTE FUNCTION trigger_update_invitation_stats();

-- 添加注释
COMMENT ON TABLE invitation_stats IS '用户邀请统计表';
COMMENT ON TABLE invitation_details IS '邀请详情表';
COMMENT ON TABLE invitation_reward_config IS '邀请奖励配置表';
COMMENT ON FUNCTION update_invitation_stats_for_user(BIGINT) IS '更新指定用户的邀请统计数据';
