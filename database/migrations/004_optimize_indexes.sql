-- 优化数据库索引
-- 为查询性能添加必要的索引，基于实际使用场景

-- 分析现有索引并优化
DO $$
BEGIN
    RAISE NOTICE '开始优化数据库索引...';
END $$;

-- 1. 用户表索引优化
-- 基于常见查询模式添加复合索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_telegram_lookup 
    ON public.users(telegram_id, telegram_username) 
    WHERE telegram_id IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_merchant_active 
    ON public.users(is_merchant, merchant_status) 
    WHERE is_merchant = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_created_at_desc 
    ON public.users(created_at DESC);

-- 2. 交易表索引优化（如果存在）
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'transactions' AND table_schema = 'public') THEN
        -- 为交易查询优化索引
        EXECUTE 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transactions_user_status_date 
                 ON public.transactions(user_id, status, created_at DESC)';
        
        EXECUTE 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transactions_type_amount 
                 ON public.transactions(type, token_amount) 
                 WHERE status = ''completed''';
        
        EXECUTE 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transactions_hash 
                 ON public.transactions(tx_hash) 
                 WHERE tx_hash IS NOT NULL';
    END IF;
END $$;

-- 3. 社交账户表索引优化（如果存在）
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'social_accounts' AND table_schema = 'public') THEN
        -- 为社交账户查询优化索引
        EXECUTE 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_social_accounts_user_platform 
                 ON public.social_accounts(user_id, platform)';
        
        EXECUTE 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_social_accounts_platform_verified 
                 ON public.social_accounts(platform, verified) 
                 WHERE verified = true';
    END IF;
END $$;

-- 4. 任务相关表索引优化（如果存在）
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'social_tasks' AND table_schema = 'public') THEN
        -- 为任务查询优化索引
        EXECUTE 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_social_tasks_active 
                 ON public.social_tasks(is_active, created_at DESC) 
                 WHERE is_active = true';
        
        EXECUTE 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_social_tasks_type_platform 
                 ON public.social_tasks(task_type, platform)';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_task_completions' AND table_schema = 'public') THEN
        -- 为任务完成记录优化索引
        EXECUTE 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_task_completions_user_date 
                 ON public.user_task_completions(user_id, completed_at DESC)';
        
        EXECUTE 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_task_completions_task_date 
                 ON public.user_task_completions(task_id, completed_at DESC)';
        
        EXECUTE 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_task_completions_reward_claimed 
                 ON public.user_task_completions(reward_claimed, completed_at) 
                 WHERE reward_claimed = false';
    END IF;
END $$;

-- 5. 邀请系统索引优化
-- 为邀请统计查询优化
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_invitation_stats_level_rewards 
    ON public.invitation_stats(invitation_level, total_rewards DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_invitation_stats_successful_invitations 
    ON public.invitation_stats(successful_invitations DESC) 
    WHERE successful_invitations > 0;

-- 为邀请详情查询优化
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_invitation_details_inviter_status_date 
    ON public.invitation_details(inviter_id, status, invited_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_invitation_details_invitee_accepted 
    ON public.invitation_details(invitee_id, accepted_at) 
    WHERE status = 'accepted';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_invitation_details_reward_pending 
    ON public.invitation_details(reward_status, reward_amount) 
    WHERE reward_status = 'pending' AND reward_amount > 0;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_invitation_details_source_date 
    ON public.invitation_details(invitation_source, invited_at DESC) 
    WHERE invitation_source IS NOT NULL;

-- 6. 用户活动索引优化
-- 为活动记录查询优化
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_activities_user_category_date 
    ON public.user_activities(user_id, activity_category, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_activities_type_status_date 
    ON public.user_activities(activity_type, status, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_activities_reward_date 
    ON public.user_activities(reward_amount, created_at DESC) 
    WHERE reward_amount > 0;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_activities_target_lookup 
    ON public.user_activities(target_type, target_id) 
    WHERE target_type IS NOT NULL AND target_id IS NOT NULL;

-- 为活动统计查询优化
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_activity_stats_total_activities 
    ON public.user_activity_stats(total_activities DESC) 
    WHERE total_activities > 0;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_activity_stats_streak_active 
    ON public.user_activity_stats(current_streak_days DESC, last_active_date DESC) 
    WHERE current_streak_days > 0;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_activity_stats_rewards_ranking 
    ON public.user_activity_stats(total_rewards DESC) 
    WHERE total_rewards > 0;

-- 7. 时间范围查询优化
-- 为常见的时间范围查询添加索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_activities_daily_stats 
    ON public.user_activities(user_id, DATE(created_at), activity_category);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_invitation_details_daily_stats 
    ON public.invitation_details(inviter_id, DATE(invited_at), status);

-- 8. 全文搜索索引（如果需要）
-- 为用户搜索添加GIN索引
DO $$
BEGIN
    -- 检查是否支持全文搜索
    IF EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'pg_trgm') THEN
        -- 为用户名搜索添加三元组索引
        EXECUTE 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_username_trgm 
                 ON public.users USING gin(username gin_trgm_ops) 
                 WHERE username IS NOT NULL';
        
        EXECUTE 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_telegram_username_trgm 
                 ON public.users USING gin(telegram_username gin_trgm_ops) 
                 WHERE telegram_username IS NOT NULL';
    ELSE
        RAISE NOTICE 'pg_trgm extension not available, skipping trigram indexes';
    END IF;
END $$;

-- 9. 部分索引优化
-- 只为活跃数据创建索引，减少索引大小
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_active_merchants 
    ON public.users(id, username, created_at) 
    WHERE is_merchant = true AND merchant_status = 'approved';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_invitation_details_recent_active 
    ON public.invitation_details(inviter_id, invitee_id, status) 
    WHERE invited_at > NOW() - INTERVAL '30 days';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_activities_recent_rewards 
    ON public.user_activities(user_id, reward_amount, created_at) 
    WHERE created_at > NOW() - INTERVAL '7 days' AND reward_amount > 0;

-- 10. 统计和分析索引
-- 为管理后台统计查询优化
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_registration_stats 
    ON public.users(DATE(created_at), is_merchant);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_invitation_stats_leaderboard 
    ON public.invitation_stats(successful_invitations DESC, total_rewards DESC, updated_at DESC) 
    WHERE successful_invitations > 0;

-- 11. 外键性能优化
-- 确保所有外键都有对应的索引
DO $$
DECLARE
    fk_record RECORD;
    index_exists BOOLEAN;
BEGIN
    -- 检查所有外键是否有对应索引
    FOR fk_record IN 
        SELECT 
            tc.table_name,
            kcu.column_name,
            ccu.table_name AS foreign_table_name,
            ccu.column_name AS foreign_column_name
        FROM information_schema.table_constraints AS tc 
        JOIN information_schema.key_column_usage AS kcu
            ON tc.constraint_name = kcu.constraint_name
            AND tc.table_schema = kcu.table_schema
        JOIN information_schema.constraint_column_usage AS ccu
            ON ccu.constraint_name = tc.constraint_name
            AND ccu.table_schema = tc.table_schema
        WHERE tc.constraint_type = 'FOREIGN KEY' 
        AND tc.table_schema = 'public'
    LOOP
        -- 检查是否存在对应的索引
        SELECT EXISTS (
            SELECT 1 FROM pg_indexes 
            WHERE schemaname = 'public' 
            AND tablename = fk_record.table_name 
            AND indexdef LIKE '%' || fk_record.column_name || '%'
        ) INTO index_exists;
        
        IF NOT index_exists THEN
            RAISE NOTICE '为外键创建索引: %.% -> %.%', 
                fk_record.table_name, fk_record.column_name,
                fk_record.foreign_table_name, fk_record.foreign_column_name;
            
            EXECUTE format('CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_%s_%s 
                           ON public.%I(%I)', 
                           fk_record.table_name, fk_record.column_name,
                           fk_record.table_name, fk_record.column_name);
        END IF;
    END LOOP;
END $$;

-- 12. 清理无用索引
-- 检查并报告可能的重复索引
DO $$
DECLARE
    duplicate_indexes TEXT;
BEGIN
    SELECT string_agg(indexname, ', ') INTO duplicate_indexes
    FROM (
        SELECT indexname, COUNT(*) as cnt
        FROM pg_indexes 
        WHERE schemaname = 'public'
        GROUP BY replace(indexname, '_idx', ''), tablename
        HAVING COUNT(*) > 1
    ) duplicates;
    
    IF duplicate_indexes IS NOT NULL THEN
        RAISE NOTICE '发现可能的重复索引: %', duplicate_indexes;
    END IF;
END $$;

-- 13. 索引使用统计
-- 创建视图来监控索引使用情况
CREATE OR REPLACE VIEW public.index_usage_stats AS
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_tup_read,
    idx_tup_fetch,
    idx_scan,
    CASE 
        WHEN idx_scan = 0 THEN 'Never used'
        WHEN idx_scan < 10 THEN 'Rarely used'
        WHEN idx_scan < 100 THEN 'Moderately used'
        ELSE 'Frequently used'
    END as usage_level
FROM pg_stat_user_indexes 
WHERE schemaname = 'public'
ORDER BY idx_scan DESC;

-- 14. 索引维护建议
-- 创建函数来分析索引效果
CREATE OR REPLACE FUNCTION analyze_index_performance()
RETURNS TABLE(
    table_name TEXT,
    index_name TEXT,
    index_size TEXT,
    usage_count BIGINT,
    recommendation TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        t.tablename::TEXT,
        t.indexname::TEXT,
        pg_size_pretty(pg_relation_size(t.indexname::regclass))::TEXT,
        s.idx_scan,
        CASE 
            WHEN s.idx_scan = 0 THEN 'Consider dropping - never used'
            WHEN s.idx_scan < 10 THEN 'Monitor usage - rarely used'
            WHEN s.idx_scan > 1000 THEN 'High value index - keep'
            ELSE 'Normal usage'
        END::TEXT
    FROM pg_indexes t
    LEFT JOIN pg_stat_user_indexes s ON t.indexname = s.indexname
    WHERE t.schemaname = 'public'
    AND t.indexname NOT LIKE '%_pkey'  -- 排除主键
    ORDER BY s.idx_scan DESC NULLS LAST;
END;
$$ LANGUAGE plpgsql;

-- 添加注释
COMMENT ON VIEW public.index_usage_stats IS '索引使用情况统计视图';
COMMENT ON FUNCTION analyze_index_performance() IS '分析索引性能和使用建议';

-- 完成通知
DO $$
BEGIN
    RAISE NOTICE '数据库索引优化完成！';
    RAISE NOTICE '建议定期运行 SELECT * FROM analyze_index_performance(); 来监控索引效果';
END $$;
