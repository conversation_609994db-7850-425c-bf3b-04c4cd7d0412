-- Social Bet系统数据库表结构
-- 基于docs/Social Bet.md的详细需求设计

-- 1. 赌约主表 (social_bets)
CREATE TABLE IF NOT EXISTS public.social_bets (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    
    -- 基本信息
    title VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    category VARCHAR(50) NOT NULL, -- 'sports', 'politics', 'entertainment', 'crypto', 'other'
    
    -- 赌约类型和模式
    bet_type VARCHAR(10) NOT NULL CHECK (bet_type IN ('1v1', '1vN')),
    template_type VARCHAR(20), -- 'custom', 'sports_match', 'price_prediction', etc.
    
    -- 创建者信息
    creator_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    
    -- 选项设置 (JSON格式存储选项)
    options JSONB NOT NULL, -- [{"id": "A", "text": "选项A", "participants": 0}, {"id": "B", "text": "选项B", "participants": 0}]
    
    -- 投注设置
    min_bet_amount DECIMAL(20,8) NOT NULL DEFAULT 10, -- 最小投注金额(福气)
    max_bet_amount DECIMAL(20,8), -- 最大投注金额(福气)，NULL表示无限制
    total_pool DECIMAL(20,8) DEFAULT 0 NOT NULL, -- 总奖池金额
    
    -- 1v1模式专用字段
    target_user_id UUID REFERENCES public.users(id), -- 1v1模式的目标用户
    creator_option VARCHAR(10), -- 创建者选择的选项
    target_option VARCHAR(10), -- 目标用户选择的选项
    
    -- 时间设置
    betting_deadline TIMESTAMP WITH TIME ZONE NOT NULL, -- 投注截止时间
    result_deadline TIMESTAMP WITH TIME ZONE NOT NULL, -- 结果公布截止时间
    
    -- 状态管理
    status VARCHAR(20) DEFAULT 'open' NOT NULL CHECK (status IN (
        'open',           -- 开放投注
        'betting_closed', -- 投注截止，等待结果
        'judging',        -- 裁定中
        'confirming',     -- 确认中
        'settled',        -- 已结算
        'cancelled',      -- 已取消
        'expired'         -- 已过期
    )),
    
    -- 裁定设置
    requires_judgment BOOLEAN DEFAULT true NOT NULL, -- 是否需要裁定
    judgment_start_time TIMESTAMP WITH TIME ZONE, -- 裁定开始时间
    current_judgment_round INTEGER DEFAULT 0, -- 当前裁定轮次 (0-3)
    
    -- 结果信息
    winning_option VARCHAR(10), -- 获胜选项
    result_confirmed_by_creator BOOLEAN DEFAULT false,
    result_confirmed_by_participants BOOLEAN DEFAULT false,
    result_confirmation_deadline TIMESTAMP WITH TIME ZONE,
    
    -- 手续费和奖励设置
    platform_fee_rate DECIMAL(5,4) DEFAULT 0.05 NOT NULL, -- 平台手续费率 (5%)
    referral_reward_rate DECIMAL(5,4) DEFAULT 0.10 NOT NULL, -- 转发奖励率 (10%)
    
    -- 元数据
    tags TEXT[], -- 标签数组
    is_featured BOOLEAN DEFAULT false, -- 是否精选
    view_count INTEGER DEFAULT 0,
    participant_count INTEGER DEFAULT 0,
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- 2. 投注参与记录表 (bet_participants)
CREATE TABLE IF NOT EXISTS public.bet_participants (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    
    -- 关联信息
    bet_id UUID REFERENCES public.social_bets(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    
    -- 投注信息
    selected_option VARCHAR(10) NOT NULL, -- 选择的选项
    bet_amount DECIMAL(20,8) NOT NULL, -- 投注金额(福气)
    
    -- 状态
    status VARCHAR(20) DEFAULT 'active' NOT NULL CHECK (status IN (
        'active',    -- 活跃投注
        'withdrawn', -- 已撤回
        'settled',   -- 已结算
        'refunded'   -- 已退款
    )),
    
    -- 结算信息
    payout_amount DECIMAL(20,8) DEFAULT 0, -- 获得的奖励金额
    is_winner BOOLEAN, -- 是否获胜
    
    -- 转发信息
    referrer_id UUID REFERENCES public.users(id), -- 转发人ID
    referral_reward DECIMAL(20,8) DEFAULT 0, -- 转发奖励金额
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    
    -- 确保用户在同一赌约中只能投注一次
    UNIQUE(bet_id, user_id)
);

-- 3. 裁定投票表 (bet_judgments)
CREATE TABLE IF NOT EXISTS public.bet_judgments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    
    -- 关联信息
    bet_id UUID REFERENCES public.social_bets(id) ON DELETE CASCADE NOT NULL,
    judge_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    
    -- 裁定信息
    judgment_round INTEGER NOT NULL CHECK (judgment_round BETWEEN 1 AND 3), -- 裁定轮次
    selected_option VARCHAR(10) NOT NULL, -- 裁定选择的获胜选项
    confidence_level INTEGER CHECK (confidence_level BETWEEN 1 AND 5), -- 信心等级 1-5
    reasoning TEXT, -- 裁定理由
    
    -- 裁判资格信息
    judge_certification_level INTEGER NOT NULL, -- 裁判认证等级
    judge_reputation_score INTEGER NOT NULL, -- 裁判信誉积分
    
    -- 奖励信息
    reward_amount DECIMAL(20,8) DEFAULT 0, -- 裁定奖励金额
    is_correct_judgment BOOLEAN, -- 是否正确裁定（最终确定后更新）
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    
    -- 确保裁判在同一轮次中只能投票一次
    UNIQUE(bet_id, judge_id, judgment_round)
);

-- 4. 用户信誉和认证表 (user_reputation)
CREATE TABLE IF NOT EXISTS public.user_reputation (
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE PRIMARY KEY,
    
    -- 认证等级 (基于HAOX持币量)
    certification_level INTEGER DEFAULT 0 NOT NULL CHECK (certification_level BETWEEN 0 AND 5),
    haox_balance DECIMAL(20,8) DEFAULT 0 NOT NULL, -- 当前HAOX余额
    last_balance_check TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- 信誉积分系统
    reputation_score INTEGER DEFAULT 0 NOT NULL, -- 信誉积分
    total_judgments INTEGER DEFAULT 0 NOT NULL, -- 总裁定次数
    correct_judgments INTEGER DEFAULT 0 NOT NULL, -- 正确裁定次数
    accuracy_rate DECIMAL(5,4) DEFAULT 0 NOT NULL, -- 准确率
    
    -- 每日限制
    daily_judgment_count INTEGER DEFAULT 0 NOT NULL, -- 今日裁定次数
    last_judgment_date DATE, -- 最后裁定日期
    
    -- 连胜记录
    current_streak INTEGER DEFAULT 0 NOT NULL, -- 当前连胜
    best_streak INTEGER DEFAULT 0 NOT NULL, -- 最佳连胜
    
    -- 权益信息
    fee_discount_rate DECIMAL(5,4) DEFAULT 0 NOT NULL, -- 手续费折扣率
    daily_judgment_limit INTEGER DEFAULT 3 NOT NULL, -- 每日裁定限额
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- 5. 申诉记录表 (bet_appeals) - 保留用于特殊情况
CREATE TABLE IF NOT EXISTS public.bet_appeals (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    
    -- 关联信息
    bet_id UUID REFERENCES public.social_bets(id) ON DELETE CASCADE NOT NULL,
    appellant_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    
    -- 申诉信息
    appeal_type VARCHAR(20) NOT NULL CHECK (appeal_type IN (
        'wrong_result',    -- 结果错误
        'unfair_judgment', -- 裁定不公
        'technical_issue', -- 技术问题
        'rule_violation'   -- 违规行为
    )),
    
    reason TEXT NOT NULL, -- 申诉理由
    evidence_urls TEXT[], -- 证据链接
    
    -- 质押信息
    stake_amount DECIMAL(20,8) NOT NULL, -- 质押金额
    stake_returned BOOLEAN DEFAULT false, -- 质押是否已返还
    
    -- 状态管理
    status VARCHAR(20) DEFAULT 'pending' NOT NULL CHECK (status IN (
        'pending',   -- 待处理
        'reviewing', -- 审核中
        'approved',  -- 申诉成功
        'rejected',  -- 申诉失败
        'withdrawn'  -- 撤回申诉
    )),
    
    -- 处理信息
    admin_response TEXT, -- 管理员回复
    processed_by UUID REFERENCES public.users(id), -- 处理人
    processed_at TIMESTAMP WITH TIME ZONE, -- 处理时间
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- 6. 赌约模板表 (bet_templates)
CREATE TABLE IF NOT EXISTS public.bet_templates (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    
    -- 模板信息
    name VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    category VARCHAR(50) NOT NULL,
    
    -- 模板配置
    template_config JSONB NOT NULL, -- 模板配置JSON
    default_options JSONB NOT NULL, -- 默认选项
    
    -- 使用统计
    usage_count INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_social_bets_status ON public.social_bets(status);
CREATE INDEX IF NOT EXISTS idx_social_bets_creator ON public.social_bets(creator_id);
CREATE INDEX IF NOT EXISTS idx_social_bets_category ON public.social_bets(category);
CREATE INDEX IF NOT EXISTS idx_social_bets_betting_deadline ON public.social_bets(betting_deadline);
CREATE INDEX IF NOT EXISTS idx_social_bets_created_at ON public.social_bets(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_bet_participants_bet_id ON public.bet_participants(bet_id);
CREATE INDEX IF NOT EXISTS idx_bet_participants_user_id ON public.bet_participants(user_id);
CREATE INDEX IF NOT EXISTS idx_bet_participants_status ON public.bet_participants(status);

CREATE INDEX IF NOT EXISTS idx_bet_judgments_bet_id ON public.bet_judgments(bet_id);
CREATE INDEX IF NOT EXISTS idx_bet_judgments_judge_id ON public.bet_judgments(judge_id);
CREATE INDEX IF NOT EXISTS idx_bet_judgments_round ON public.bet_judgments(judgment_round);

CREATE INDEX IF NOT EXISTS idx_user_reputation_cert_level ON public.user_reputation(certification_level);
CREATE INDEX IF NOT EXISTS idx_user_reputation_score ON public.user_reputation(reputation_score DESC);

-- 启用行级安全策略
ALTER TABLE public.social_bets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bet_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bet_judgments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_reputation ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bet_appeals ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bet_templates ENABLE ROW LEVEL SECURITY;

-- 创建RLS策略
-- 赌约表：所有人可查看，只有创建者可修改
CREATE POLICY "Anyone can view social bets" ON public.social_bets
    FOR SELECT USING (true);

CREATE POLICY "Users can create social bets" ON public.social_bets
    FOR INSERT WITH CHECK (auth.uid() = creator_id);

CREATE POLICY "Creators can update their bets" ON public.social_bets
    FOR UPDATE USING (auth.uid() = creator_id);

-- 投注记录：用户只能查看和创建自己的投注
CREATE POLICY "Users can view own bet participations" ON public.bet_participants
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create bet participations" ON public.bet_participants
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- 裁定记录：裁判只能查看和创建自己的裁定
CREATE POLICY "Judges can view own judgments" ON public.bet_judgments
    FOR SELECT USING (auth.uid() = judge_id);

CREATE POLICY "Judges can create judgments" ON public.bet_judgments
    FOR INSERT WITH CHECK (auth.uid() = judge_id);

-- 用户信誉：用户只能查看自己的信誉信息
CREATE POLICY "Users can view own reputation" ON public.user_reputation
    FOR SELECT USING (auth.uid() = user_id);

-- 申诉记录：用户只能查看和创建自己的申诉
CREATE POLICY "Users can view own appeals" ON public.bet_appeals
    FOR SELECT USING (auth.uid() = appellant_id);

CREATE POLICY "Users can create appeals" ON public.bet_appeals
    FOR INSERT WITH CHECK (auth.uid() = appellant_id);

-- 模板：所有人可查看
CREATE POLICY "Anyone can view bet templates" ON public.bet_templates
    FOR SELECT USING (true);

-- 添加表注释
COMMENT ON TABLE public.social_bets IS 'Social Bet赌约主表';
COMMENT ON TABLE public.bet_participants IS '投注参与记录表';
COMMENT ON TABLE public.bet_judgments IS '裁定投票记录表';
COMMENT ON TABLE public.user_reputation IS '用户信誉和认证等级表';
COMMENT ON TABLE public.bet_appeals IS '申诉记录表';
COMMENT ON TABLE public.bet_templates IS '赌约模板表';
