-- 修复数据库外键约束和关系
-- 统一数据类型，确保数据完整性

-- 首先检查并修复用户表的结构
DO $$
BEGIN
    -- 检查users表是否存在，如果不存在则创建
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'users' AND table_schema = 'public') THEN
        -- 创建users表（基于Supabase auth.users）
        CREATE TABLE public.users (
            id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
            email TEXT UNIQUE,
            username TEXT UNIQUE,
            first_name TEXT,
            last_name TEXT,
            avatar_url TEXT,
            wallet_address TEXT,
            telegram_id BIGINT UNIQUE,
            telegram_username TEXT,
            is_merchant BOOLEAN DEFAULT FALSE,
            merchant_status TEXT DEFAULT 'none' CHECK (merchant_status IN ('none', 'pending', 'approved', 'rejected')),
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- 创建索引
        CREATE INDEX IF NOT EXISTS idx_users_wallet_address ON public.users(wallet_address);
        CREATE INDEX IF NOT EXISTS idx_users_telegram_id ON public.users(telegram_id);
        CREATE INDEX IF NOT EXISTS idx_users_telegram_username ON public.users(telegram_username);
        CREATE INDEX IF NOT EXISTS idx_users_merchant_status ON public.users(merchant_status);
        
        -- 启用RLS
        ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
        
        -- 创建RLS策略
        CREATE POLICY "Users can view own profile" ON public.users FOR SELECT USING (auth.uid() = id);
        CREATE POLICY "Users can update own profile" ON public.users FOR UPDATE USING (auth.uid() = id);
        CREATE POLICY "Enable insert for authenticated users" ON public.users FOR INSERT WITH CHECK (auth.uid() = id);
    END IF;
END $$;

-- 修复invitation_stats表的外键关系
DO $$
BEGIN
    -- 如果invitation_stats表存在但user_id是BIGINT，需要修复
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'invitation_stats' 
        AND column_name = 'user_id' 
        AND data_type = 'bigint'
        AND table_schema = 'public'
    ) THEN
        -- 删除现有的invitation_stats表（如果有数据，需要先备份）
        DROP TABLE IF EXISTS public.invitation_stats CASCADE;
    END IF;
    
    -- 重新创建invitation_stats表，使用正确的UUID类型
    CREATE TABLE IF NOT EXISTS public.invitation_stats (
        id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
        user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
        
        -- 邀请统计
        total_invitations INTEGER DEFAULT 0 NOT NULL,
        successful_invitations INTEGER DEFAULT 0 NOT NULL,
        pending_invitations INTEGER DEFAULT 0 NOT NULL,
        
        -- 奖励统计
        total_rewards DECIMAL(20, 8) DEFAULT 0 NOT NULL,
        pending_rewards DECIMAL(20, 8) DEFAULT 0 NOT NULL,
        claimed_rewards DECIMAL(20, 8) DEFAULT 0 NOT NULL,
        
        -- 等级信息
        invitation_level INTEGER DEFAULT 1 NOT NULL,
        level_progress DECIMAL(5, 2) DEFAULT 0 NOT NULL,
        
        -- 时间统计
        first_invitation_at TIMESTAMP WITH TIME ZONE,
        last_invitation_at TIMESTAMP WITH TIME ZONE,
        last_reward_at TIMESTAMP WITH TIME ZONE,
        
        -- 系统字段
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
        
        -- 约束
        CONSTRAINT invitation_stats_user_id_unique UNIQUE (user_id),
        CONSTRAINT invitation_stats_totals_check CHECK (
            successful_invitations <= total_invitations AND
            pending_invitations <= total_invitations AND
            claimed_rewards <= total_rewards AND
            pending_rewards <= total_rewards
        ),
        CONSTRAINT invitation_stats_level_check CHECK (invitation_level >= 1),
        CONSTRAINT invitation_stats_progress_check CHECK (level_progress >= 0 AND level_progress <= 100)
    );
    
    -- 创建索引
    CREATE INDEX IF NOT EXISTS idx_invitation_stats_user_id ON public.invitation_stats(user_id);
    CREATE INDEX IF NOT EXISTS idx_invitation_stats_level ON public.invitation_stats(invitation_level);
    CREATE INDEX IF NOT EXISTS idx_invitation_stats_total_invitations ON public.invitation_stats(total_invitations);
    CREATE INDEX IF NOT EXISTS idx_invitation_stats_total_rewards ON public.invitation_stats(total_rewards);
    CREATE INDEX IF NOT EXISTS idx_invitation_stats_updated_at ON public.invitation_stats(updated_at);
    
    -- 启用RLS
    ALTER TABLE public.invitation_stats ENABLE ROW LEVEL SECURITY;
    
    -- 创建RLS策略
    CREATE POLICY "Users can view own invitation stats" ON public.invitation_stats FOR SELECT USING (auth.uid() = user_id);
    CREATE POLICY "Users can update own invitation stats" ON public.invitation_stats FOR UPDATE USING (auth.uid() = user_id);
    CREATE POLICY "Enable insert for authenticated users" ON public.invitation_stats FOR INSERT WITH CHECK (auth.uid() = user_id);
END $$;

-- 修复invitation_details表
DO $$
BEGIN
    -- 如果invitation_details表存在但外键类型不正确，需要修复
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'invitation_details' 
        AND column_name = 'inviter_id' 
        AND data_type = 'bigint'
        AND table_schema = 'public'
    ) THEN
        DROP TABLE IF EXISTS public.invitation_details CASCADE;
    END IF;
    
    -- 重新创建invitation_details表
    CREATE TABLE IF NOT EXISTS public.invitation_details (
        id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
        inviter_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
        invitee_id UUID REFERENCES public.users(id) ON DELETE SET NULL,
        invitation_code VARCHAR(50) NOT NULL,
        
        -- 邀请状态
        status VARCHAR(20) DEFAULT 'pending' NOT NULL,
        -- pending: 待接受, accepted: 已接受, expired: 已过期, cancelled: 已取消
        
        -- 奖励信息
        reward_amount DECIMAL(20, 8) DEFAULT 0 NOT NULL,
        reward_status VARCHAR(20) DEFAULT 'pending' NOT NULL,
        -- pending: 待发放, processing: 处理中, completed: 已发放, failed: 发放失败
        
        -- 时间信息
        invited_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
        accepted_at TIMESTAMP WITH TIME ZONE,
        expired_at TIMESTAMP WITH TIME ZONE,
        reward_processed_at TIMESTAMP WITH TIME ZONE,
        
        -- 元数据
        invitation_source VARCHAR(50), -- 邀请来源：telegram, link, qr_code等
        metadata JSONB DEFAULT '{}',
        
        -- 系统字段
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
        
        -- 约束
        CONSTRAINT invitation_details_code_unique UNIQUE (invitation_code),
        CONSTRAINT invitation_details_status_check CHECK (
            status IN ('pending', 'accepted', 'expired', 'cancelled')
        ),
        CONSTRAINT invitation_details_reward_status_check CHECK (
            reward_status IN ('pending', 'processing', 'completed', 'failed')
        )
    );
    
    -- 创建索引
    CREATE INDEX IF NOT EXISTS idx_invitation_details_inviter_id ON public.invitation_details(inviter_id);
    CREATE INDEX IF NOT EXISTS idx_invitation_details_invitee_id ON public.invitation_details(invitee_id);
    CREATE INDEX IF NOT EXISTS idx_invitation_details_code ON public.invitation_details(invitation_code);
    CREATE INDEX IF NOT EXISTS idx_invitation_details_status ON public.invitation_details(status);
    CREATE INDEX IF NOT EXISTS idx_invitation_details_reward_status ON public.invitation_details(reward_status);
    CREATE INDEX IF NOT EXISTS idx_invitation_details_invited_at ON public.invitation_details(invited_at);
    CREATE INDEX IF NOT EXISTS idx_invitation_details_source ON public.invitation_details(invitation_source);
    
    -- 启用RLS
    ALTER TABLE public.invitation_details ENABLE ROW LEVEL SECURITY;
    
    -- 创建RLS策略
    CREATE POLICY "Users can view own invitations" ON public.invitation_details 
        FOR SELECT USING (auth.uid() = inviter_id OR auth.uid() = invitee_id);
    CREATE POLICY "Users can update own invitations" ON public.invitation_details 
        FOR UPDATE USING (auth.uid() = inviter_id);
    CREATE POLICY "Users can create invitations" ON public.invitation_details 
        FOR INSERT WITH CHECK (auth.uid() = inviter_id);
END $$;

-- 修复user_activities表
DO $$
BEGIN
    -- 如果user_activities表存在但user_id是BIGINT，需要修复
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_activities' 
        AND column_name = 'user_id' 
        AND data_type = 'bigint'
        AND table_schema = 'public'
    ) THEN
        DROP TABLE IF EXISTS public.user_activities CASCADE;
    END IF;
    
    -- 重新创建user_activities表
    CREATE TABLE IF NOT EXISTS public.user_activities (
        id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
        user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
        
        -- 活动信息
        activity_type VARCHAR(50) NOT NULL,
        activity_category VARCHAR(30) NOT NULL,
        activity_description TEXT,
        
        -- 活动详情
        target_type VARCHAR(50), -- 目标类型：task, transaction, wallet, profile等
        target_id VARCHAR(100),  -- 目标ID
        
        -- 活动结果
        status VARCHAR(20) DEFAULT 'completed' NOT NULL,
        -- completed: 已完成, failed: 失败, pending: 进行中, cancelled: 已取消
        
        -- 奖励信息
        reward_amount DECIMAL(20, 8) DEFAULT 0,
        reward_currency VARCHAR(10) DEFAULT 'HAOX',
        
        -- 元数据
        metadata JSONB DEFAULT '{}',
        ip_address INET,
        user_agent TEXT,
        
        -- 时间信息
        started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
        completed_at TIMESTAMP WITH TIME ZONE,
        
        -- 系统字段
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
        
        -- 约束
        CONSTRAINT user_activities_status_check CHECK (
            status IN ('completed', 'failed', 'pending', 'cancelled')
        ),
        CONSTRAINT user_activities_reward_check CHECK (reward_amount >= 0)
    );
    
    -- 创建索引
    CREATE INDEX IF NOT EXISTS idx_user_activities_user_id ON public.user_activities(user_id);
    CREATE INDEX IF NOT EXISTS idx_user_activities_type ON public.user_activities(activity_type);
    CREATE INDEX IF NOT EXISTS idx_user_activities_category ON public.user_activities(activity_category);
    CREATE INDEX IF NOT EXISTS idx_user_activities_status ON public.user_activities(status);
    CREATE INDEX IF NOT EXISTS idx_user_activities_target ON public.user_activities(target_type, target_id);
    CREATE INDEX IF NOT EXISTS idx_user_activities_created_at ON public.user_activities(created_at);
    CREATE INDEX IF NOT EXISTS idx_user_activities_completed_at ON public.user_activities(completed_at);
    CREATE INDEX IF NOT EXISTS idx_user_activities_reward ON public.user_activities(reward_amount) WHERE reward_amount > 0;
    
    -- 复合索引
    CREATE INDEX IF NOT EXISTS idx_user_activities_user_type_date ON public.user_activities(user_id, activity_type, created_at);
    CREATE INDEX IF NOT EXISTS idx_user_activities_user_category_date ON public.user_activities(user_id, activity_category, created_at);
    
    -- 启用RLS
    ALTER TABLE public.user_activities ENABLE ROW LEVEL SECURITY;
    
    -- 创建RLS策略
    CREATE POLICY "Users can view own activities" ON public.user_activities FOR SELECT USING (auth.uid() = user_id);
    CREATE POLICY "Users can insert own activities" ON public.user_activities FOR INSERT WITH CHECK (auth.uid() = user_id);
    CREATE POLICY "Users can update own activities" ON public.user_activities FOR UPDATE USING (auth.uid() = user_id);
END $$;

-- 修复user_activity_stats表
DO $$
BEGIN
    -- 如果user_activity_stats表存在但user_id是BIGINT，需要修复
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_activity_stats' 
        AND column_name = 'user_id' 
        AND data_type = 'bigint'
        AND table_schema = 'public'
    ) THEN
        DROP TABLE IF EXISTS public.user_activity_stats CASCADE;
    END IF;
    
    -- 重新创建user_activity_stats表
    CREATE TABLE IF NOT EXISTS public.user_activity_stats (
        id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
        user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
        
        -- 活动统计
        total_activities INTEGER DEFAULT 0 NOT NULL,
        completed_activities INTEGER DEFAULT 0 NOT NULL,
        failed_activities INTEGER DEFAULT 0 NOT NULL,
        pending_activities INTEGER DEFAULT 0 NOT NULL,
        
        -- 分类统计
        auth_activities INTEGER DEFAULT 0 NOT NULL,
        wallet_activities INTEGER DEFAULT 0 NOT NULL,
        task_activities INTEGER DEFAULT 0 NOT NULL,
        social_activities INTEGER DEFAULT 0 NOT NULL,
        trading_activities INTEGER DEFAULT 0 NOT NULL,
        
        -- 奖励统计
        total_rewards DECIMAL(20, 8) DEFAULT 0 NOT NULL,
        total_reward_activities INTEGER DEFAULT 0 NOT NULL,
        
        -- 时间统计
        first_activity_at TIMESTAMP WITH TIME ZONE,
        last_activity_at TIMESTAMP WITH TIME ZONE,
        most_active_hour INTEGER, -- 0-23，最活跃的小时
        most_active_day INTEGER,  -- 0-6，最活跃的星期几（0=周日）
        
        -- 连续活动统计
        current_streak_days INTEGER DEFAULT 0 NOT NULL,
        longest_streak_days INTEGER DEFAULT 0 NOT NULL,
        last_active_date DATE,
        
        -- 系统字段
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
        
        -- 约束
        CONSTRAINT user_activity_stats_user_id_unique UNIQUE (user_id),
        CONSTRAINT user_activity_stats_activities_check CHECK (
            completed_activities + failed_activities + pending_activities <= total_activities
        ),
        CONSTRAINT user_activity_stats_hour_check CHECK (
            most_active_hour IS NULL OR (most_active_hour >= 0 AND most_active_hour <= 23)
        ),
        CONSTRAINT user_activity_stats_day_check CHECK (
            most_active_day IS NULL OR (most_active_day >= 0 AND most_active_day <= 6)
        ),
        CONSTRAINT user_activity_stats_streak_check CHECK (
            current_streak_days >= 0 AND longest_streak_days >= 0 AND
            current_streak_days <= longest_streak_days
        )
    );
    
    -- 创建索引
    CREATE INDEX IF NOT EXISTS idx_user_activity_stats_user_id ON public.user_activity_stats(user_id);
    CREATE INDEX IF NOT EXISTS idx_user_activity_stats_total_activities ON public.user_activity_stats(total_activities);
    CREATE INDEX IF NOT EXISTS idx_user_activity_stats_total_rewards ON public.user_activity_stats(total_rewards);
    CREATE INDEX IF NOT EXISTS idx_user_activity_stats_streak ON public.user_activity_stats(current_streak_days);
    CREATE INDEX IF NOT EXISTS idx_user_activity_stats_last_active ON public.user_activity_stats(last_active_date);
    CREATE INDEX IF NOT EXISTS idx_user_activity_stats_updated_at ON public.user_activity_stats(updated_at);
    
    -- 启用RLS
    ALTER TABLE public.user_activity_stats ENABLE ROW LEVEL SECURITY;
    
    -- 创建RLS策略
    CREATE POLICY "Users can view own activity stats" ON public.user_activity_stats FOR SELECT USING (auth.uid() = user_id);
    CREATE POLICY "Users can update own activity stats" ON public.user_activity_stats FOR UPDATE USING (auth.uid() = user_id);
    CREATE POLICY "Enable insert for authenticated users" ON public.user_activity_stats FOR INSERT WITH CHECK (auth.uid() = user_id);
END $$;

-- 创建或更新触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为所有相关表添加updated_at触发器
DO $$
BEGIN
    -- users表
    DROP TRIGGER IF EXISTS trigger_update_users_updated_at ON public.users;
    CREATE TRIGGER trigger_update_users_updated_at
        BEFORE UPDATE ON public.users
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
    
    -- invitation_stats表
    DROP TRIGGER IF EXISTS trigger_update_invitation_stats_updated_at ON public.invitation_stats;
    CREATE TRIGGER trigger_update_invitation_stats_updated_at
        BEFORE UPDATE ON public.invitation_stats
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
    
    -- invitation_details表
    DROP TRIGGER IF EXISTS trigger_update_invitation_details_updated_at ON public.invitation_details;
    CREATE TRIGGER trigger_update_invitation_details_updated_at
        BEFORE UPDATE ON public.invitation_details
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
    
    -- user_activity_stats表
    DROP TRIGGER IF EXISTS trigger_update_user_activity_stats_updated_at ON public.user_activity_stats;
    CREATE TRIGGER trigger_update_user_activity_stats_updated_at
        BEFORE UPDATE ON public.user_activity_stats
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
END $$;

-- 添加注释
COMMENT ON TABLE public.users IS '用户基础信息表，扩展auth.users';
COMMENT ON TABLE public.invitation_stats IS '用户邀请统计表';
COMMENT ON TABLE public.invitation_details IS '邀请详情表';
COMMENT ON TABLE public.user_activities IS '用户活动记录表';
COMMENT ON TABLE public.user_activity_stats IS '用户活动统计表';

-- 验证外键约束
DO $$
DECLARE
    constraint_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO constraint_count
    FROM information_schema.table_constraints
    WHERE table_schema = 'public'
    AND constraint_type = 'FOREIGN KEY'
    AND table_name IN ('invitation_stats', 'invitation_details', 'user_activities', 'user_activity_stats');
    
    RAISE NOTICE '已创建 % 个外键约束', constraint_count;
END $$;
