{"name": "sociomint222", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "perf:baseline": "node scripts/simple-performance-test.js test", "perf:check": "node scripts/simple-performance-test.js check", "perf:test": "node scripts/performance-test.js test", "perf:compare": "node scripts/performance-test.js compare", "build:analyze": "ANALYZE=true npm run build", "build:production": "NODE_ENV=production npm run build", "deploy:staging": "bash scripts/deploy-cloudflare.sh staging", "deploy:production": "bash scripts/deploy-cloudflare.sh production", "deploy:setup": "wrangler pages project create sociomint-staging && wrangler pages project create sociomint-production"}, "dependencies": {"@heroicons/react": "^2.2.0", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.52.0", "@tanstack/react-query": "^5.83.0", "@types/jsonwebtoken": "^9.0.10", "@types/qrcode": "^1.5.5", "@wagmi/connectors": "^5.9.0", "@wagmi/core": "^2.18.0", "clsx": "^2.1.1", "framer-motion": "^11.18.2", "hardhat-contract-sizer": "^2.10.0", "isomorphic-dompurify": "^2.26.0", "jsonwebtoken": "^9.0.2", "lightweight-charts": "^5.0.8", "lucide-react": "^0.460.0", "next": "14.2.30", "next-auth": "^4.24.11", "qrcode": "^1.5.4", "react": "^18", "react-dom": "^18", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "viem": "^2.33.0", "wagmi": "^2.16.0"}, "devDependencies": {"@nomicfoundation/hardhat-ignition": "^0.15.13", "@nomicfoundation/hardhat-ignition-ethers": "^0.15.14", "@nomicfoundation/hardhat-network-helpers": "^1.1.0", "@nomicfoundation/hardhat-toolbox": "^6.1.0", "@nomicfoundation/ignition-core": "^0.15.13", "@openzeppelin/contracts": "^5.4.0", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@typechain/ethers-v6": "^0.5.1", "@types/mocha": "^10.0.10", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "chrome-launcher": "^1.2.0", "dotenv": "^17.2.0", "eslint": "^8", "eslint-config-next": "14.2.30", "hardhat": "^2.26.1", "hardhat-gas-reporter": "^2.3.0", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "jest-watch-typeahead": "^3.0.1", "lighthouse": "^12.8.0", "node-mocks-http": "^1.17.2", "pino-pretty": "^13.0.0", "postcss": "^8", "solhint": "^6.0.0", "solidity-coverage": "^0.8.16", "tailwindcss": "^3.4.1", "typechain": "^8.3.2", "typescript": "^5"}}