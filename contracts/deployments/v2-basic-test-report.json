{"timestamp": "2025-07-30T09:40:38.223Z", "network": "BSC Testnet", "summary": {"total": 6, "passed": 4, "failed": 1, "pending": 1, "success_rate": "66.7%"}, "contracts": {"haoxToken": "0x220C8116EC93D7894968d7CC662ab80Db80E7aF3", "presale": "0x232Cb2986CB0B7FE8f4f4329Eb47B78df4cBeF22", "invitation": "0x330E44Fa889F8AD493437d4854c2e1A6545CdE03", "priceOracle": "0xe6940f9FE1948FCE67862F818a490d398843b632", "vesting": "0x254024A7388f9812dA8B40847234ccE6A9E81788"}, "results": [{"test": "HAOXTokenV2基础功能", "status": "PASS", "details": "所有基础功能正常"}, {"test": "HAOXPresaleV2状态查询", "status": "PASS", "details": "预售状态查询正常"}, {"test": "HAOXInvitationV2参数", "status": "PASS", "details": "邀请奖励参数正确"}, {"test": "HAOXPriceOracleV2参数", "status": "FAIL", "details": "Cannot mix BigInt and other types, use explicit conversions"}, {"test": "HAOXVestingV2参数", "status": "PASS", "details": "解锁合约参数正确"}, {"test": "合约关系", "status": "PENDING", "details": "等待24小时时间锁到期"}]}