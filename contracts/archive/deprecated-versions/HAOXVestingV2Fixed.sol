// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "./HAOXTokenV2.sol";
import "./HAOXPriceOracleV2.sol";

/**
 * @title HAOXVestingV2Fixed
 * @dev 修正版HAOX代币解锁合约
 * 
 * 解锁机制：
 * - 第1轮：5亿HAOX（已完成，预售结束）
 * - 第2-31轮：每轮1.5亿HAOX，共45亿HAOX
 * - 价格触发：第2-11轮(+100%)，第12-21轮(+50%)，第22-31轮(+20%)
 * - 维持期：价格需连续7天保持在触发价格以上
 * - 分配：项目钱包40%，社区钱包60%
 */
contract HAOXVestingV2Fixed is ReentrancyGuard, Pausable, Ownable {
    
    HAOXTokenV2 public immutable haoxToken;
    HAOXPriceOracleV2 public priceOracle;
    
    // 解锁参数
    uint256 public constant TOTAL_ROUNDS = 31;
    uint256 public constant TOKENS_PER_ROUND = 150_000_000 * 10**18; // 1.5亿HAOX
    uint256 public constant FIRST_ROUND_TOKENS = 500_000_000 * 10**18; // 5亿HAOX（第1轮）
    uint256 public constant PROJECT_SHARE = 40; // 40%
    uint256 public constant COMMUNITY_SHARE = 60; // 60%
    uint256 public constant PRICE_MAINTENANCE_PERIOD = 7 days; // 价格维持期
    
    // 基准价格和增长率（以8位小数存储，$0.003041 = 304100）
    uint256 public constant BASE_PRICE = 304100; // $0.003041 (8位小数)
    uint256 public constant ROUND_2_11_INCREASE = 200; // 100% increase (200% of previous)
    uint256 public constant ROUND_12_21_INCREASE = 150; // 50% increase (150% of previous)
    uint256 public constant ROUND_22_31_INCREASE = 120; // 20% increase (120% of previous)
    
    // 解锁轮次结构
    struct UnlockRound {
        uint256 roundNumber;
        uint256 triggerPrice; // 8位小数
        uint256 priceReachedTime; // 价格首次达到时间
        bool priceConditionMet; // 价格条件是否达成
        bool unlocked; // 是否已解锁
        uint256 unlockTime; // 解锁时间
        uint256 projectTokens; // 项目钱包代币数量
        uint256 communityTokens; // 社区钱包代币数量
    }
    
    mapping(uint256 => UnlockRound) public unlockRounds;
    uint256 public currentRound = 1; // 第1轮已完成
    
    address public projectWallet;
    address public communityWallet;
    
    // 价格检查历史记录
    struct PriceCheck {
        uint256 timestamp;
        uint256 price;
        uint256 targetPrice;
        bool conditionMet;
    }
    
    mapping(uint256 => PriceCheck[]) public priceCheckHistory;
    
    // 事件
    event RoundUnlocked(
        uint256 indexed roundNumber,
        uint256 triggerPrice,
        uint256 projectTokens,
        uint256 communityTokens,
        uint256 timestamp
    );
    event PriceConditionMet(
        uint256 indexed roundNumber, 
        uint256 price, 
        uint256 timestamp
    );
    event PriceConditionReset(
        uint256 indexed roundNumber, 
        uint256 price, 
        uint256 timestamp
    );
    event WalletUpdated(string walletType, address oldWallet, address newWallet);
    event PriceChecked(
        uint256 indexed roundNumber,
        uint256 currentPrice,
        uint256 targetPrice,
        bool conditionMet,
        uint256 timestamp
    );
    
    modifier validRound(uint256 roundNumber) {
        require(roundNumber >= 1 && roundNumber <= TOTAL_ROUNDS, "Invalid round number");
        _;
    }
    
    constructor(
        address _haoxToken,
        address _priceOracle,
        address _projectWallet,
        address _communityWallet
    ) Ownable(msg.sender) {
        require(_haoxToken != address(0), "Invalid token address");
        require(_priceOracle != address(0), "Invalid oracle address");
        require(_projectWallet != address(0), "Invalid project wallet");
        require(_communityWallet != address(0), "Invalid community wallet");
        
        haoxToken = HAOXTokenV2(_haoxToken);
        priceOracle = HAOXPriceOracleV2(_priceOracle);
        projectWallet = _projectWallet;
        communityWallet = _communityWallet;
        
        // 初始化所有解锁轮次
        _initializeRounds();
    }
    
    /**
     * @dev 初始化所有解锁轮次
     */
    function _initializeRounds() internal {
        uint256 triggerPrice = BASE_PRICE;
        
        // 第1轮（已完成，预售结束）
        unlockRounds[1] = UnlockRound({
            roundNumber: 1,
            triggerPrice: triggerPrice,
            priceReachedTime: block.timestamp,
            priceConditionMet: true,
            unlocked: true,
            unlockTime: block.timestamp,
            projectTokens: (FIRST_ROUND_TOKENS * PROJECT_SHARE) / 100,
            communityTokens: (FIRST_ROUND_TOKENS * COMMUNITY_SHARE) / 100
        });
        
        // 第2-11轮：每轮价格上涨100%
        for (uint256 i = 2; i <= 11; i++) {
            triggerPrice = (triggerPrice * ROUND_2_11_INCREASE) / 100;
            _createRound(i, triggerPrice);
        }
        
        // 第12-21轮：每轮价格上涨50%
        for (uint256 i = 12; i <= 21; i++) {
            triggerPrice = (triggerPrice * ROUND_12_21_INCREASE) / 100;
            _createRound(i, triggerPrice);
        }
        
        // 第22-31轮：每轮价格上涨20%
        for (uint256 i = 22; i <= 31; i++) {
            triggerPrice = (triggerPrice * ROUND_22_31_INCREASE) / 100;
            _createRound(i, triggerPrice);
        }
    }
    
    /**
     * @dev 创建解锁轮次
     */
    function _createRound(uint256 roundNumber, uint256 triggerPrice) internal {
        uint256 projectTokens = (TOKENS_PER_ROUND * PROJECT_SHARE) / 100;
        uint256 communityTokens = (TOKENS_PER_ROUND * COMMUNITY_SHARE) / 100;
        
        unlockRounds[roundNumber] = UnlockRound({
            roundNumber: roundNumber,
            triggerPrice: triggerPrice,
            priceReachedTime: 0,
            priceConditionMet: false,
            unlocked: false,
            unlockTime: 0,
            projectTokens: projectTokens,
            communityTokens: communityTokens
        });
    }
    
    /**
     * @dev 检查价格条件并更新状态
     */
    function checkPriceCondition() public virtual whenNotPaused {
        uint256 nextRound = currentRound + 1;
        if (nextRound > TOTAL_ROUNDS) return;
        
        UnlockRound storage round = unlockRounds[nextRound];
        if (round.unlocked) return;
        
        uint256 currentPrice = priceOracle.getLatestPrice();
        bool conditionMet = currentPrice >= round.triggerPrice;
        
        // 记录价格检查历史
        priceCheckHistory[nextRound].push(PriceCheck({
            timestamp: block.timestamp,
            price: currentPrice,
            targetPrice: round.triggerPrice,
            conditionMet: conditionMet
        }));
        
        emit PriceChecked(nextRound, currentPrice, round.triggerPrice, conditionMet, block.timestamp);
        
        if (conditionMet) {
            if (!round.priceConditionMet) {
                // 首次达到价格条件
                round.priceConditionMet = true;
                round.priceReachedTime = block.timestamp;
                emit PriceConditionMet(nextRound, currentPrice, block.timestamp);
            } else {
                // 检查是否维持了足够长时间
                if (block.timestamp >= round.priceReachedTime + PRICE_MAINTENANCE_PERIOD) {
                    _unlockRound(nextRound);
                }
            }
        } else {
            // 价格跌破，重置条件
            if (round.priceConditionMet) {
                round.priceConditionMet = false;
                round.priceReachedTime = 0;
                emit PriceConditionReset(nextRound, currentPrice, block.timestamp);
            }
        }
    }
    
    /**
     * @dev 解锁指定轮次
     */
    function _unlockRound(uint256 roundNumber) internal {
        UnlockRound storage round = unlockRounds[roundNumber];
        require(!round.unlocked, "Round already unlocked");
        require(round.priceConditionMet, "Price condition not met");
        require(
            block.timestamp >= round.priceReachedTime + PRICE_MAINTENANCE_PERIOD,
            "Price maintenance period not completed"
        );
        
        round.unlocked = true;
        round.unlockTime = block.timestamp;
        currentRound = roundNumber;
        
        // 转移代币
        require(
            haoxToken.transfer(projectWallet, round.projectTokens),
            "Project transfer failed"
        );
        require(
            haoxToken.transfer(communityWallet, round.communityTokens),
            "Community transfer failed"
        );
        
        emit RoundUnlocked(
            roundNumber,
            round.triggerPrice,
            round.projectTokens,
            round.communityTokens,
            block.timestamp
        );
    }
    
    /**
     * @dev 获取轮次信息
     */
    function getRoundInfo(uint256 roundNumber) external view validRound(roundNumber) returns (
        uint256 triggerPrice,
        bool priceConditionMet,
        bool unlocked,
        uint256 priceReachedTime,
        uint256 unlockTime,
        uint256 projectTokens,
        uint256 communityTokens
    ) {
        UnlockRound memory round = unlockRounds[roundNumber];
        return (
            round.triggerPrice,
            round.priceConditionMet,
            round.unlocked,
            round.priceReachedTime,
            round.unlockTime,
            round.projectTokens,
            round.communityTokens
        );
    }
    
    /**
     * @dev 获取当前价格和下一轮解锁进度
     */
    function getUnlockProgress() external view returns (
        uint256 currentPrice,
        uint256 nextRoundNumber,
        uint256 nextRoundTriggerPrice,
        bool nextRoundPriceConditionMet,
        uint256 timeRemaining,
        uint256 priceReachedTime
    ) {
        currentPrice = priceOracle.getLatestPrice();
        nextRoundNumber = currentRound + 1;
        
        if (nextRoundNumber <= TOTAL_ROUNDS) {
            UnlockRound memory nextRound = unlockRounds[nextRoundNumber];
            nextRoundTriggerPrice = nextRound.triggerPrice;
            nextRoundPriceConditionMet = nextRound.priceConditionMet;
            priceReachedTime = nextRound.priceReachedTime;
            
            if (nextRound.priceConditionMet && nextRound.priceReachedTime > 0) {
                uint256 elapsed = block.timestamp - nextRound.priceReachedTime;
                timeRemaining = elapsed >= PRICE_MAINTENANCE_PERIOD ? 0 : 
                               PRICE_MAINTENANCE_PERIOD - elapsed;
            }
        }
    }
    
    /**
     * @dev 获取价格检查历史
     */
    function getPriceCheckHistory(uint256 roundNumber, uint256 limit)
        external view virtual validRound(roundNumber) returns (PriceCheck[] memory) {
        PriceCheck[] memory history = priceCheckHistory[roundNumber];
        uint256 length = history.length;
        
        if (limit == 0 || limit > length) {
            limit = length;
        }
        
        PriceCheck[] memory result = new PriceCheck[](limit);
        for (uint256 i = 0; i < limit; i++) {
            result[i] = history[length - limit + i];
        }
        
        return result;
    }
    
    /**
     * @dev 获取解锁统计信息
     */
    function getUnlockStatistics() external view returns (
        uint256 totalUnlockedRounds,
        uint256 totalUnlockedTokens,
        uint256 totalProjectTokens,
        uint256 totalCommunityTokens,
        uint256 remainingTokens
    ) {
        for (uint256 i = 1; i <= TOTAL_ROUNDS; i++) {
            if (unlockRounds[i].unlocked) {
                totalUnlockedRounds++;
                if (i == 1) {
                    totalUnlockedTokens += FIRST_ROUND_TOKENS;
                } else {
                    totalUnlockedTokens += TOKENS_PER_ROUND;
                }
                totalProjectTokens += unlockRounds[i].projectTokens;
                totalCommunityTokens += unlockRounds[i].communityTokens;
            }
        }
        
        uint256 totalSupply = FIRST_ROUND_TOKENS + (TOKENS_PER_ROUND * (TOTAL_ROUNDS - 1));
        remainingTokens = totalSupply - totalUnlockedTokens;
    }
    
    /**
     * @dev 更新价格预言机地址
     */
    function updatePriceOracle(address _newOracle) external onlyOwner {
        require(_newOracle != address(0), "Invalid oracle address");
        address oldOracle = address(priceOracle);
        priceOracle = HAOXPriceOracleV2(_newOracle);
        emit WalletUpdated("priceOracle", oldOracle, _newOracle);
    }
    
    /**
     * @dev 更新项目钱包地址
     */
    function updateProjectWallet(address _newWallet) external onlyOwner {
        require(_newWallet != address(0), "Invalid wallet address");
        address oldWallet = projectWallet;
        projectWallet = _newWallet;
        emit WalletUpdated("project", oldWallet, _newWallet);
    }
    
    /**
     * @dev 更新社区钱包地址
     */
    function updateCommunityWallet(address _newWallet) external onlyOwner {
        require(_newWallet != address(0), "Invalid wallet address");
        address oldWallet = communityWallet;
        communityWallet = _newWallet;
        emit WalletUpdated("community", oldWallet, _newWallet);
    }
    
    /**
     * @dev 紧急暂停
     */
    function pause() external onlyOwner {
        _pause();
    }
    
    /**
     * @dev 恢复运行
     */
    function unpause() external onlyOwner {
        _unpause();
    }
    
    /**
     * @dev 紧急提取代币（仅限所有者）
     */
    function emergencyWithdraw(address token, uint256 amount) external virtual onlyOwner {
        require(token != address(0), "Invalid token address");
        require(IERC20(token).transfer(owner(), amount), "Transfer failed");
    }
}
