// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "./HAOXVestingV2FixedSecure.sol";

/**
 * @title HAOXVestingV2Optimized
 * @dev 优化版本的HAOX代币解锁合约
 * 实现存储优化和Gas优化，限制历史记录大小
 */
contract HAOXVestingV2Optimized is HAOXVestingV2FixedSecure {
    
    // 优化的价格检查结构（紧凑存储）
    struct OptimizedPriceCheck {
        uint64 timestamp;      // 时间戳（足够到2554年）
        uint64 price;          // 价格（8位小数，最大1844万）
        uint64 targetPrice;    // 目标价格
        bool conditionMet;     // 条件是否满足
    }
    
    // 存储优化配置
    uint256 public constant MAX_PRICE_HISTORY_SIZE = 100;
    uint256 public constant CLEANUP_BATCH_SIZE = 10;
    uint256 public constant GAS_OPTIMIZATION_THRESHOLD = 200000;
    uint256 public constant PRICE_MAINTAIN_DURATION = 7 days;
    
    // 优化的存储映射
    mapping(uint256 => OptimizedPriceCheck[]) private optimizedPriceHistory;
    mapping(uint256 => uint256) public priceHistoryCount;
    mapping(uint256 => uint256) public lastCleanupIndex;
    
    // Gas使用统计
    mapping(bytes4 => uint256) public functionGasUsage;
    mapping(bytes4 => uint256) public functionCallCount;
    
    // 批量操作状态
    struct BatchState {
        uint256 currentBatch;
        uint256 totalBatches;
        bool inProgress;
    }
    
    BatchState public cleanupState;
    
    // 事件定义
    event PriceHistoryOptimized(
        uint256 indexed roundNumber,
        uint256 oldSize,
        uint256 newSize,
        uint256 gasUsed
    );
    
    event GasOptimizationTriggered(
        bytes4 indexed functionSelector,
        uint256 gasUsed,
        uint256 threshold
    );
    
    event BatchCleanupStarted(uint256 totalBatches);
    event BatchCleanupCompleted(uint256 batchNumber, uint256 itemsProcessed);
    event BatchCleanupFinished();

    // 修饰符
    modifier gasOptimized() {
        uint256 gasStart = gasleft();
        _;
        uint256 gasUsed = gasStart - gasleft();
        bytes4 selector = msg.sig;
        
        functionGasUsage[selector] += gasUsed;
        functionCallCount[selector]++;
        
        // 如果Gas使用超过阈值，触发优化
        if (gasUsed > GAS_OPTIMIZATION_THRESHOLD) {
            emit GasOptimizationTriggered(selector, gasUsed, GAS_OPTIMIZATION_THRESHOLD);
            _triggerOptimization(selector);
        }
    }

    /**
     * @dev 构造函数
     */
    constructor(
        address _haoxToken,
        address _priceOracle,
        address _projectWallet,
        address _communityWallet
    ) HAOXVestingV2FixedSecure(_haoxToken, _priceOracle, _projectWallet, _communityWallet) {
        // 初始化批量清理状态
        cleanupState = BatchState({
            currentBatch: 0,
            totalBatches: 0,
            inProgress: false
        });
    }

    /**
     * @dev 优化的价格检查函数
     */
    function checkPriceCondition() public override nonReentrant whenNotPaused gasOptimized {
        uint256 currentRoundNumber = currentRound;
        
        // 检查是否所有轮次都已完成
        if (currentRoundNumber > TOTAL_ROUNDS) {
            return;
        }
        
        // 获取当前价格（使用优化的调用）
        uint256 currentPrice = _getOptimizedPrice();
        
        // 使用基类的轮次访问方式
        (uint256 triggerPrice, bool priceConditionMet, bool unlocked, uint256 priceReachedTime,,,) = this.getRoundInfo(currentRoundNumber);
        bool conditionCurrentlyMet = currentPrice >= triggerPrice;
        
        // 优化的价格检查记录
        _addOptimizedPriceCheck(currentRoundNumber, currentPrice, triggerPrice, conditionCurrentlyMet);
        
        // 调用基类的价格检查逻辑
        super.checkPriceCondition();
        
        // 触发自动清理（如果需要）
        _autoCleanupIfNeeded(currentRoundNumber);
    }

    /**
     * @dev 优化的价格获取
     */
    function _getOptimizedPrice() internal view returns (uint256) {
        // 使用静态调用减少Gas消耗
        (bool success, bytes memory data) = address(priceOracle).staticcall(
            abi.encodeWithSignature("getLatestPrice()")
        );
        
        require(success && data.length >= 32, "Price oracle call failed");
        return abi.decode(data, (uint256));
    }

    /**
     * @dev 添加优化的价格检查记录
     */
    function _addOptimizedPriceCheck(
        uint256 roundNumber,
        uint256 price,
        uint256 targetPrice,
        bool conditionMet
    ) internal {
        OptimizedPriceCheck memory newCheck = OptimizedPriceCheck({
            timestamp: uint64(block.timestamp),
            price: uint64(price),
            targetPrice: uint64(targetPrice),
            conditionMet: conditionMet
        });
        
        OptimizedPriceCheck[] storage history = optimizedPriceHistory[roundNumber];
        
        // 如果历史记录已满，使用循环覆盖
        if (history.length >= MAX_PRICE_HISTORY_SIZE) {
            uint256 oldestIndex = priceHistoryCount[roundNumber] % MAX_PRICE_HISTORY_SIZE;
            history[oldestIndex] = newCheck;
        } else {
            history.push(newCheck);
        }
        
        priceHistoryCount[roundNumber]++;
    }

    /**
     * @dev 获取优化的价格检查历史
     */
    function getOptimizedPriceHistory(uint256 roundNumber, uint256 limit) 
        external 
        view 
        returns (OptimizedPriceCheck[] memory) 
    {
        OptimizedPriceCheck[] storage history = optimizedPriceHistory[roundNumber];
        uint256 length = history.length;
        
        if (limit == 0 || limit > length) {
            limit = length;
        }
        
        OptimizedPriceCheck[] memory result = new OptimizedPriceCheck[](limit);
        
        // 返回最新的记录
        for (uint256 i = 0; i < limit; i++) {
            uint256 index = length > limit ? length - limit + i : i;
            result[i] = history[index];
        }
        
        return result;
    }

    /**
     * @dev 兼容性函数：转换为原始格式
     */
    function getPriceCheckHistory(uint256 roundNumber, uint256 limit) 
        external 
        view 
        override
        returns (PriceCheck[] memory) 
    {
        OptimizedPriceCheck[] memory optimizedHistory = this.getOptimizedPriceHistory(roundNumber, limit);
        PriceCheck[] memory result = new PriceCheck[](optimizedHistory.length);
        
        for (uint256 i = 0; i < optimizedHistory.length; i++) {
            result[i] = PriceCheck({
                timestamp: optimizedHistory[i].timestamp,
                price: optimizedHistory[i].price,
                targetPrice: optimizedHistory[i].targetPrice,
                conditionMet: optimizedHistory[i].conditionMet
            });
        }
        
        return result;
    }

    /**
     * @dev 自动清理（如果需要）
     */
    function _autoCleanupIfNeeded(uint256 roundNumber) internal {
        // 每100次检查触发一次清理
        if (priceHistoryCount[roundNumber] % 100 == 0) {
            _scheduleCleanup(roundNumber);
        }
    }

    /**
     * @dev 安排清理任务
     */
    function _scheduleCleanup(uint256 roundNumber) internal {
        if (!cleanupState.inProgress) {
            cleanupState.inProgress = true;
            cleanupState.currentBatch = 0;
            cleanupState.totalBatches = (roundNumber / CLEANUP_BATCH_SIZE) + 1;
            
            emit BatchCleanupStarted(cleanupState.totalBatches);
        }
    }

    /**
     * @dev 执行批量清理
     */
    function executeBatchCleanup() external gasOptimized {
        require(cleanupState.inProgress, "No cleanup in progress");
        require(cleanupState.currentBatch < cleanupState.totalBatches, "Cleanup already finished");
        
        uint256 startRound = cleanupState.currentBatch * CLEANUP_BATCH_SIZE + 1;
        uint256 endRound = startRound + CLEANUP_BATCH_SIZE - 1;
        uint256 itemsProcessed = 0;
        
        for (uint256 i = startRound; i <= endRound && i <= TOTAL_ROUNDS; i++) {
            if (_cleanupRoundHistory(i)) {
                itemsProcessed++;
            }
        }
        
        cleanupState.currentBatch++;
        emit BatchCleanupCompleted(cleanupState.currentBatch, itemsProcessed);
        
        // 检查是否完成
        if (cleanupState.currentBatch >= cleanupState.totalBatches) {
            cleanupState.inProgress = false;
            cleanupState.currentBatch = 0;
            cleanupState.totalBatches = 0;
            
            emit BatchCleanupFinished();
        }
    }

    /**
     * @dev 清理单个轮次的历史记录
     */
    function _cleanupRoundHistory(uint256 roundNumber) internal returns (bool) {
        OptimizedPriceCheck[] storage history = optimizedPriceHistory[roundNumber];
        
        if (history.length <= MAX_PRICE_HISTORY_SIZE) {
            return false; // 不需要清理
        }
        
        uint256 oldSize = history.length;
        
        // 保留最新的记录
        OptimizedPriceCheck[] memory latestRecords = new OptimizedPriceCheck[](MAX_PRICE_HISTORY_SIZE);
        
        for (uint256 i = 0; i < MAX_PRICE_HISTORY_SIZE; i++) {
            latestRecords[i] = history[oldSize - MAX_PRICE_HISTORY_SIZE + i];
        }
        
        // 清空并重新填充
        delete optimizedPriceHistory[roundNumber];
        
        for (uint256 i = 0; i < MAX_PRICE_HISTORY_SIZE; i++) {
            optimizedPriceHistory[roundNumber].push(latestRecords[i]);
        }
        
        emit PriceHistoryOptimized(roundNumber, oldSize, MAX_PRICE_HISTORY_SIZE, gasleft());
        
        return true;
    }

    /**
     * @dev 触发优化
     */
    function _triggerOptimization(bytes4 functionSelector) internal {
        // 根据函数选择器执行特定优化
        if (functionSelector == this.checkPriceCondition.selector) {
            _optimizePriceCheck();
        } else if (functionSelector == this.getPriceCheckHistory.selector) {
            _optimizeHistoryAccess();
        }
    }

    /**
     * @dev 优化价格检查
     */
    function _optimizePriceCheck() internal {
        // 实现价格检查的特定优化
        // 例如：缓存频繁访问的数据
    }

    /**
     * @dev 优化历史访问
     */
    function _optimizeHistoryAccess() internal {
        // 实现历史访问的特定优化
        // 例如：预计算常用查询
    }

    /**
     * @dev 手动触发清理
     */
    function manualCleanup(uint256 roundNumber) external onlyOwner gasOptimized {
        require(roundNumber <= TOTAL_ROUNDS, "Invalid round number");
        
        bool cleaned = _cleanupRoundHistory(roundNumber);
        require(cleaned, "No cleanup needed");
    }

    /**
     * @dev 批量清理多个轮次
     */
    function batchCleanup(uint256[] calldata roundNumbers) external onlyOwner gasOptimized {
        require(roundNumbers.length <= 10, "Too many rounds");
        
        for (uint256 i = 0; i < roundNumbers.length; i++) {
            if (roundNumbers[i] <= TOTAL_ROUNDS) {
                _cleanupRoundHistory(roundNumbers[i]);
            }
        }
    }

    /**
     * @dev 获取Gas使用统计
     */
    function getGasStatistics(bytes4 functionSelector) external view returns (
        uint256 totalGasUsed,
        uint256 callCount,
        uint256 averageGasUsed
    ) {
        totalGasUsed = functionGasUsage[functionSelector];
        callCount = functionCallCount[functionSelector];
        averageGasUsed = callCount > 0 ? totalGasUsed / callCount : 0;
    }

    /**
     * @dev 获取所有函数的Gas统计
     */
    function getAllGasStatistics() external view returns (
        bytes4[] memory selectors,
        uint256[] memory totalGasUsed,
        uint256[] memory callCounts,
        uint256[] memory averageGasUsed
    ) {
        // 预定义的函数选择器列表
        bytes4[] memory knownSelectors = new bytes4[](4);
        knownSelectors[0] = this.checkPriceCondition.selector;
        knownSelectors[1] = this.getPriceCheckHistory.selector;
        knownSelectors[2] = this.manualCleanup.selector;
        knownSelectors[3] = this.batchCleanup.selector;
        
        selectors = new bytes4[](knownSelectors.length);
        totalGasUsed = new uint256[](knownSelectors.length);
        callCounts = new uint256[](knownSelectors.length);
        averageGasUsed = new uint256[](knownSelectors.length);
        
        for (uint256 i = 0; i < knownSelectors.length; i++) {
            bytes4 selector = knownSelectors[i];
            selectors[i] = selector;
            totalGasUsed[i] = functionGasUsage[selector];
            callCounts[i] = functionCallCount[selector];
            averageGasUsed[i] = callCounts[i] > 0 ? totalGasUsed[i] / callCounts[i] : 0;
        }
    }

    /**
     * @dev 获取存储优化统计
     */
    function getStorageStatistics() external view returns (
        uint256 totalRounds,
        uint256 totalHistoryEntries,
        uint256 averageHistorySize,
        uint256 maxHistorySize,
        bool cleanupInProgress
    ) {
        totalRounds = TOTAL_ROUNDS;
        totalHistoryEntries = 0;
        uint256 maxSize = 0;
        
        for (uint256 i = 1; i <= TOTAL_ROUNDS; i++) {
            uint256 size = optimizedPriceHistory[i].length;
            totalHistoryEntries += size;
            if (size > maxSize) {
                maxSize = size;
            }
        }
        
        averageHistorySize = totalRounds > 0 ? totalHistoryEntries / totalRounds : 0;
        maxHistorySize = maxSize;
        cleanupInProgress = cleanupState.inProgress;
    }

    /**
     * @dev 重置Gas统计
     */
    function resetGasStatistics() external onlyOwner {
        bytes4[] memory selectors = new bytes4[](4);
        selectors[0] = this.checkPriceCondition.selector;
        selectors[1] = this.getPriceCheckHistory.selector;
        selectors[2] = this.manualCleanup.selector;
        selectors[3] = this.batchCleanup.selector;
        
        for (uint256 i = 0; i < selectors.length; i++) {
            functionGasUsage[selectors[i]] = 0;
            functionCallCount[selectors[i]] = 0;
        }
    }
}
