{"contracts/HAOXInvitationV2.sol": {"l": {"103": 76, "104": 76, "115": 0, "116": 0, "117": 0, "118": 0, "119": 0, "122": 0, "123": 0, "126": 0, "127": 0, "136": 0, "137": 0, "140": 0, "141": 0, "144": 0, "145": 0, "146": 0, "147": 0, "150": 0, "151": 0, "152": 0, "153": 0, "156": 0, "163": 2, "164": 2, "166": 2, "167": 0, "168": 0, "174": 0, "175": 0, "176": 0, "179": 0, "181": 0, "188": 2, "189": 2, "191": 1, "192": 1, "193": 0, "195": 0, "196": 0, "197": 0, "198": 0, "200": 0, "202": 0, "203": 0, "210": 18, "211": 17, "218": 8, "219": 7, "222": 6, "225": 6, "226": 0, "227": 0, "228": 0, "229": 0, "230": 0, "236": 6, "237": 6, "239": 6, "240": 0, "241": 0, "242": 0, "243": 0, "246": 6, "247": 6, "256": 6, "257": 6, "263": 6, "264": 6, "265": 0, "268": 6, "283": 6, "284": 6, "299": 8, "306": 4, "313": 6, "320": 6, "327": 5}, "path": "/Users/<USER>/Desktop/sociomint222/contracts/contracts/HAOXInvitationV2.sol", "s": {"1": 76, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 2, "17": 2, "18": 2, "19": 0, "20": 0, "21": 0, "22": 0, "23": 2, "24": 2, "25": 1, "26": 1, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 18, "33": 8, "34": 7, "35": 6, "36": 6, "37": 0, "38": 0, "39": 0, "40": 6, "41": 6, "42": 0, "43": 6, "44": 6, "45": 6, "46": 6, "47": 6, "48": 6, "49": 6, "50": 6, "51": 8, "52": 4, "53": 6, "54": 6, "55": 5}, "b": {"1": [76, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [3, 0], "10": [2, 1], "11": [0, 2], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [2, 0], "16": [2, 0], "17": [2, 0], "18": [1, 1], "19": [0, 1], "20": [0, 0], "21": [0, 0], "22": [18, 1], "23": [17, 1], "24": [8, 1], "25": [7, 1], "26": [6, 1], "27": [0, 0], "28": [0, 6], "29": [6, 1], "30": [6, 0], "31": [5, 1], "32": [4, 1]}, "f": {"1": 76, "2": 0, "3": 2, "4": 2, "5": 18, "6": 8, "7": 6, "8": 6, "9": 8, "10": 4, "11": 6, "12": 6, "13": 5}, "fnMap": {"1": {"name": "constructor", "line": 102, "loc": {"start": {"line": 102, "column": 4}, "end": {"line": 105, "column": 4}}}, "2": {"name": "recordInvitation", "line": 110, "loc": {"start": {"line": 110, "column": 4}, "end": {"line": 157, "column": 4}}}, "3": {"name": "claimRewards", "line": 162, "loc": {"start": {"line": 162, "column": 4}, "end": {"line": 182, "column": 4}}}, "4": {"name": "claimLeaderboardReward", "line": 187, "loc": {"start": {"line": 187, "column": 4}, "end": {"line": 204, "column": 4}}}, "5": {"name": "endPresale", "line": 209, "loc": {"start": {"line": 209, "column": 4}, "end": {"line": 212, "column": 4}}}, "6": {"name": "finalizeLeaderboard", "line": 217, "loc": {"start": {"line": 217, "column": 4}, "end": {"line": 248, "column": 4}}}, "7": {"name": "_getAllInviters", "line": 253, "loc": {"start": {"line": 253, "column": 4}, "end": {"line": 269, "column": 4}}}, "8": {"name": "getUserStats", "line": 274, "loc": {"start": {"line": 274, "column": 4}, "end": {"line": 293, "column": 4}}}, "9": {"name": "getUserInvitees", "line": 298, "loc": {"start": {"line": 298, "column": 4}, "end": {"line": 300, "column": 4}}}, "10": {"name": "getLeaderboard", "line": 305, "loc": {"start": {"line": 305, "column": 4}, "end": {"line": 307, "column": 4}}}, "11": {"name": "pause", "line": 312, "loc": {"start": {"line": 312, "column": 4}, "end": {"line": 314, "column": 4}}}, "12": {"name": "unpause", "line": 319, "loc": {"start": {"line": 319, "column": 4}, "end": {"line": 321, "column": 4}}}, "13": {"name": "emergencyWithdraw", "line": 326, "loc": {"start": {"line": 326, "column": 4}, "end": {"line": 328, "column": 4}}}}, "statementMap": {"1": {"start": {"line": 103, "column": 8}, "end": {"line": 103, "column": 65}}, "2": {"start": {"line": 115, "column": 8}, "end": {"line": 115, "column": 91}}, "3": {"start": {"line": 116, "column": 8}, "end": {"line": 116, "column": 46}}, "4": {"start": {"line": 117, "column": 8}, "end": {"line": 117, "column": 79}}, "5": {"start": {"line": 118, "column": 8}, "end": {"line": 118, "column": 56}}, "6": {"start": {"line": 119, "column": 8}, "end": {"line": 119, "column": 71}}, "7": {"start": {"line": 123, "column": 8}, "end": {"line": 123, "column": 40}}, "8": {"start": {"line": 126, "column": 8}, "end": {"line": 126, "column": 89}}, "9": {"start": {"line": 136, "column": 8}, "end": {"line": 136, "column": 52}}, "10": {"start": {"line": 140, "column": 8}, "end": {"line": 140, "column": 40}}, "11": {"start": {"line": 144, "column": 8}, "end": {"line": 144, "column": 4802}}, "12": {"start": {"line": 147, "column": 12}, "end": {"line": 147, "column": 65}}, "13": {"start": {"line": 150, "column": 8}, "end": {"line": 150, "column": 5066}}, "14": {"start": {"line": 153, "column": 12}, "end": {"line": 153, "column": 67}}, "15": {"start": {"line": 156, "column": 8}, "end": {"line": 156, "column": 77}}, "16": {"start": {"line": 163, "column": 8}, "end": {"line": 163, "column": 55}}, "17": {"start": {"line": 164, "column": 8}, "end": {"line": 164, "column": 56}}, "18": {"start": {"line": 166, "column": 8}, "end": {"line": 166, "column": 58}}, "19": {"start": {"line": 167, "column": 8}, "end": {"line": 167, "column": 53}}, "20": {"start": {"line": 168, "column": 8}, "end": {"line": 168, "column": 5770}}, "21": {"start": {"line": 179, "column": 8}, "end": {"line": 179, "column": 82}}, "22": {"start": {"line": 181, "column": 8}, "end": {"line": 181, "column": 55}}, "23": {"start": {"line": 188, "column": 8}, "end": {"line": 188, "column": 49}}, "24": {"start": {"line": 189, "column": 8}, "end": {"line": 189, "column": 65}}, "25": {"start": {"line": 191, "column": 8}, "end": {"line": 191, "column": 55}}, "26": {"start": {"line": 192, "column": 8}, "end": {"line": 192, "column": 68}}, "27": {"start": {"line": 193, "column": 8}, "end": {"line": 193, "column": 60}}, "28": {"start": {"line": 195, "column": 8}, "end": {"line": 195, "column": 48}}, "29": {"start": {"line": 200, "column": 8}, "end": {"line": 200, "column": 73}}, "30": {"start": {"line": 202, "column": 8}, "end": {"line": 202, "column": 58}}, "31": {"start": {"line": 203, "column": 8}, "end": {"line": 203, "column": 67}}, "32": {"start": {"line": 210, "column": 8}, "end": {"line": 210, "column": 46}}, "33": {"start": {"line": 218, "column": 8}, "end": {"line": 218, "column": 49}}, "34": {"start": {"line": 219, "column": 8}, "end": {"line": 219, "column": 58}}, "35": {"start": {"line": 222, "column": 8}, "end": {"line": 222, "column": 53}}, "36": {"start": {"line": 225, "column": 8}, "end": {"line": 225, "column": 7595}}, "37": {"start": {"line": 226, "column": 12}, "end": {"line": 226, "column": 7659}}, "38": {"start": {"line": 227, "column": 16}, "end": {"line": 227, "column": 7731}}, "39": {"start": {"line": 228, "column": 20}, "end": {"line": 228, "column": 46}}, "40": {"start": {"line": 236, "column": 8}, "end": {"line": 236, "column": 70}}, "41": {"start": {"line": 239, "column": 8}, "end": {"line": 239, "column": 8152}}, "42": {"start": {"line": 240, "column": 12}, "end": {"line": 240, "column": 38}}, "43": {"start": {"line": 247, "column": 8}, "end": {"line": 247, "column": 46}}, "44": {"start": {"line": 256, "column": 8}, "end": {"line": 256, "column": 54}}, "45": {"start": {"line": 257, "column": 8}, "end": {"line": 257, "column": 25}}, "46": {"start": {"line": 263, "column": 8}, "end": {"line": 263, "column": 54}}, "47": {"start": {"line": 264, "column": 8}, "end": {"line": 264, "column": 8936}}, "48": {"start": {"line": 268, "column": 8}, "end": {"line": 268, "column": 21}}, "49": {"start": {"line": 283, "column": 8}, "end": {"line": 283, "column": 48}}, "50": {"start": {"line": 284, "column": 8}, "end": {"line": 284, "column": 9455}}, "51": {"start": {"line": 299, "column": 8}, "end": {"line": 299, "column": 31}}, "52": {"start": {"line": 306, "column": 8}, "end": {"line": 306, "column": 26}}, "53": {"start": {"line": 313, "column": 8}, "end": {"line": 313, "column": 15}}, "54": {"start": {"line": 320, "column": 8}, "end": {"line": 320, "column": 17}}, "55": {"start": {"line": 327, "column": 8}, "end": {"line": 327, "column": 70}}}, "branchMap": {"1": {"line": 103, "type": "if", "locations": [{"start": {"line": 103, "column": 8}, "end": {"line": 103, "column": 8}}, {"start": {"line": 103, "column": 8}, "end": {"line": 103, "column": 8}}]}, "2": {"line": 115, "type": "if", "locations": [{"start": {"line": 115, "column": 8}, "end": {"line": 115, "column": 8}}, {"start": {"line": 115, "column": 8}, "end": {"line": 115, "column": 8}}]}, "3": {"line": 116, "type": "if", "locations": [{"start": {"line": 116, "column": 8}, "end": {"line": 116, "column": 8}}, {"start": {"line": 116, "column": 8}, "end": {"line": 116, "column": 8}}]}, "4": {"line": 117, "type": "if", "locations": [{"start": {"line": 117, "column": 8}, "end": {"line": 117, "column": 8}}, {"start": {"line": 117, "column": 8}, "end": {"line": 117, "column": 8}}]}, "5": {"line": 118, "type": "if", "locations": [{"start": {"line": 118, "column": 8}, "end": {"line": 118, "column": 8}}, {"start": {"line": 118, "column": 8}, "end": {"line": 118, "column": 8}}]}, "6": {"line": 119, "type": "if", "locations": [{"start": {"line": 119, "column": 8}, "end": {"line": 119, "column": 8}}, {"start": {"line": 119, "column": 8}, "end": {"line": 119, "column": 8}}]}, "7": {"line": 144, "type": "if", "locations": [{"start": {"line": 144, "column": 8}, "end": {"line": 144, "column": 8}}, {"start": {"line": 144, "column": 8}, "end": {"line": 144, "column": 8}}]}, "8": {"line": 150, "type": "if", "locations": [{"start": {"line": 150, "column": 8}, "end": {"line": 150, "column": 8}}, {"start": {"line": 150, "column": 8}, "end": {"line": 150, "column": 8}}]}, "9": {"line": 162, "type": "if", "locations": [{"start": {"line": 162, "column": 37}, "end": {"line": 162, "column": 37}}, {"start": {"line": 162, "column": 37}, "end": {"line": 162, "column": 37}}]}, "10": {"line": 162, "type": "if", "locations": [{"start": {"line": 162, "column": 50}, "end": {"line": 162, "column": 50}}, {"start": {"line": 162, "column": 50}, "end": {"line": 162, "column": 50}}]}, "11": {"line": 166, "type": "if", "locations": [{"start": {"line": 166, "column": 8}, "end": {"line": 166, "column": 8}}, {"start": {"line": 166, "column": 8}, "end": {"line": 166, "column": 8}}]}, "12": {"line": 167, "type": "if", "locations": [{"start": {"line": 167, "column": 8}, "end": {"line": 167, "column": 8}}, {"start": {"line": 167, "column": 8}, "end": {"line": 167, "column": 8}}]}, "13": {"line": 168, "type": "if", "locations": [{"start": {"line": 168, "column": 8}, "end": {"line": 168, "column": 8}}, {"start": {"line": 168, "column": 8}, "end": {"line": 168, "column": 8}}]}, "14": {"line": 179, "type": "if", "locations": [{"start": {"line": 179, "column": 8}, "end": {"line": 179, "column": 8}}, {"start": {"line": 179, "column": 8}, "end": {"line": 179, "column": 8}}]}, "15": {"line": 187, "type": "if", "locations": [{"start": {"line": 187, "column": 47}, "end": {"line": 187, "column": 47}}, {"start": {"line": 187, "column": 47}, "end": {"line": 187, "column": 47}}]}, "16": {"line": 187, "type": "if", "locations": [{"start": {"line": 187, "column": 60}, "end": {"line": 187, "column": 60}}, {"start": {"line": 187, "column": 60}, "end": {"line": 187, "column": 60}}]}, "17": {"line": 188, "type": "if", "locations": [{"start": {"line": 188, "column": 8}, "end": {"line": 188, "column": 8}}, {"start": {"line": 188, "column": 8}, "end": {"line": 188, "column": 8}}]}, "18": {"line": 189, "type": "if", "locations": [{"start": {"line": 189, "column": 8}, "end": {"line": 189, "column": 8}}, {"start": {"line": 189, "column": 8}, "end": {"line": 189, "column": 8}}]}, "19": {"line": 192, "type": "if", "locations": [{"start": {"line": 192, "column": 8}, "end": {"line": 192, "column": 8}}, {"start": {"line": 192, "column": 8}, "end": {"line": 192, "column": 8}}]}, "20": {"line": 193, "type": "if", "locations": [{"start": {"line": 193, "column": 8}, "end": {"line": 193, "column": 8}}, {"start": {"line": 193, "column": 8}, "end": {"line": 193, "column": 8}}]}, "21": {"line": 200, "type": "if", "locations": [{"start": {"line": 200, "column": 8}, "end": {"line": 200, "column": 8}}, {"start": {"line": 200, "column": 8}, "end": {"line": 200, "column": 8}}]}, "22": {"line": 209, "type": "if", "locations": [{"start": {"line": 209, "column": 35}, "end": {"line": 209, "column": 35}}, {"start": {"line": 209, "column": 35}, "end": {"line": 209, "column": 35}}]}, "23": {"line": 210, "type": "if", "locations": [{"start": {"line": 210, "column": 8}, "end": {"line": 210, "column": 8}}, {"start": {"line": 210, "column": 8}, "end": {"line": 210, "column": 8}}]}, "24": {"line": 217, "type": "if", "locations": [{"start": {"line": 217, "column": 44}, "end": {"line": 217, "column": 44}}, {"start": {"line": 217, "column": 44}, "end": {"line": 217, "column": 44}}]}, "25": {"line": 218, "type": "if", "locations": [{"start": {"line": 218, "column": 8}, "end": {"line": 218, "column": 8}}, {"start": {"line": 218, "column": 8}, "end": {"line": 218, "column": 8}}]}, "26": {"line": 219, "type": "if", "locations": [{"start": {"line": 219, "column": 8}, "end": {"line": 219, "column": 8}}, {"start": {"line": 219, "column": 8}, "end": {"line": 219, "column": 8}}]}, "27": {"line": 227, "type": "if", "locations": [{"start": {"line": 227, "column": 16}, "end": {"line": 227, "column": 16}}, {"start": {"line": 227, "column": 16}, "end": {"line": 227, "column": 16}}]}, "28": {"line": 236, "type": "if", "locations": [{"start": {"line": 236, "column": 50}, "end": {"line": 236, "column": 51}}, {"start": {"line": 236, "column": 55}, "end": {"line": 236, "column": 69}}]}, "29": {"line": 312, "type": "if", "locations": [{"start": {"line": 312, "column": 30}, "end": {"line": 312, "column": 30}}, {"start": {"line": 312, "column": 30}, "end": {"line": 312, "column": 30}}]}, "30": {"line": 319, "type": "if", "locations": [{"start": {"line": 319, "column": 32}, "end": {"line": 319, "column": 32}}, {"start": {"line": 319, "column": 32}, "end": {"line": 319, "column": 32}}]}, "31": {"line": 326, "type": "if", "locations": [{"start": {"line": 326, "column": 56}, "end": {"line": 326, "column": 56}}, {"start": {"line": 326, "column": 56}, "end": {"line": 326, "column": 56}}]}, "32": {"line": 327, "type": "if", "locations": [{"start": {"line": 327, "column": 8}, "end": {"line": 327, "column": 8}}, {"start": {"line": 327, "column": 8}, "end": {"line": 327, "column": 8}}]}}}, "contracts/HAOXPresaleV2.sol": {"l": {"62": 21, "63": 21, "64": 21, "66": 21, "67": 21, "68": 21, "70": 21, "71": 21, "72": 21, "83": 21, "85": 21, "86": 21, "88": 21, "89": 2079, "90": 2079, "93": 21, "100": 5, "102": 5, "103": 5, "106": 0, "107": 0, "108": 0, "110": 0, "126": 3, "127": 3, "128": 3, "129": 3, "131": 3, "132": 3, "133": 3, "135": 3, "137": 3, "138": 3, "141": 0, "142": 0, "143": 0, "146": 0, "147": 0, "151": 3, "154": 3, "155": 3, "156": 3, "163": 6, "164": 6, "165": 5, "166": 4, "170": 3, "175": 3, "178": 3, "181": 3, "182": 3, "188": 3, "191": 3, "192": 3, "195": 3, "196": 0, "203": 3, "213": 3, "214": 0, "216": 0, "221": 0, "233": 3, "234": 3, "236": 3, "237": 3, "239": 3, "240": 3, "243": 0, "245": 0, "247": 0, "248": 0, "253": 3, "254": 3, "261": 1, "268": 21, "269": 21, "277": 0, "284": 0, "291": 0, "292": 0, "306": 1}, "path": "/Users/<USER>/Desktop/sociomint222/contracts/contracts/HAOXPresaleV2.sol", "s": {"1": 21, "2": 21, "3": 21, "4": 21, "5": 21, "6": 21, "7": 5, "8": 5, "9": 5, "10": 0, "11": 0, "12": 0, "13": 3, "14": 3, "15": 3, "16": 3, "17": 3, "18": 3, "19": 3, "20": 3, "21": 0, "22": 3, "23": 6, "24": 6, "25": 5, "26": 4, "27": 3, "28": 3, "29": 3, "30": 3, "31": 3, "32": 3, "33": 3, "34": 0, "35": 3, "36": 3, "37": 0, "38": 0, "39": 3, "40": 3, "41": 3, "42": 3, "43": 0, "44": 1, "45": 21, "46": 0, "47": 0, "48": 0, "49": 0, "50": 1}, "b": {"1": [21, 0], "2": [21, 0], "3": [21, 0], "4": [21, 0], "5": [21, 0], "6": [5, 0], "7": [5, 0], "8": [3, 0], "9": [3, 0], "10": [6, 0], "11": [6, 0], "12": [6, 0], "13": [5, 1], "14": [4, 1], "15": [3, 1], "16": [3, 0], "17": [3, 0], "18": [3, 0], "19": [0, 3], "20": [0, 3], "21": [0, 0], "22": [3, 0], "23": [21, 0], "24": [0, 0], "25": [0, 0], "26": [0, 0], "27": [0, 0]}, "f": {"1": 21, "2": 21, "3": 5, "4": 3, "5": 6, "6": 3, "7": 1, "8": 21, "9": 0, "10": 0, "11": 0, "12": 1}, "fnMap": {"1": {"name": "constructor", "line": 61, "loc": {"start": {"line": 57, "column": 4}, "end": {"line": 73, "column": 4}}}, "2": {"name": "initializeRates", "line": 82, "loc": {"start": {"line": 82, "column": 4}, "end": {"line": 94, "column": 4}}}, "3": {"name": "getStageRate", "line": 99, "loc": {"start": {"line": 99, "column": 4}, "end": {"line": 112, "column": 4}}}, "4": {"name": "calculateCrossStageTokens", "line": 121, "loc": {"start": {"line": 121, "column": 4}, "end": {"line": 157, "column": 4}}}, "5": {"name": "buyTokens", "line": 162, "loc": {"start": {"line": 162, "column": 4}, "end": {"line": 223, "column": 4}}}, "6": {"name": "_updateStageProgress", "line": 228, "loc": {"start": {"line": 228, "column": 4}, "end": {"line": 255, "column": 4}}}, "7": {"name": "getCurrentRate", "line": 260, "loc": {"start": {"line": 260, "column": 4}, "end": {"line": 262, "column": 4}}}, "8": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "line": 267, "loc": {"start": {"line": 267, "column": 4}, "end": {"line": 271, "column": 4}}}, "9": {"name": "pause", "line": 276, "loc": {"start": {"line": 276, "column": 4}, "end": {"line": 278, "column": 4}}}, "10": {"name": "unpause", "line": 283, "loc": {"start": {"line": 283, "column": 4}, "end": {"line": 285, "column": 4}}}, "11": {"name": "withdrawBNB", "line": 290, "loc": {"start": {"line": 290, "column": 4}, "end": {"line": 293, "column": 4}}}, "12": {"name": "getPresaleStatus", "line": 298, "loc": {"start": {"line": 298, "column": 4}, "end": {"line": 314, "column": 4}}}}, "statementMap": {"1": {"start": {"line": 62, "column": 8}, "end": {"line": 62, "column": 65}}, "2": {"start": {"line": 63, "column": 8}, "end": {"line": 63, "column": 79}}, "3": {"start": {"line": 64, "column": 8}, "end": {"line": 64, "column": 68}}, "4": {"start": {"line": 83, "column": 8}, "end": {"line": 83, "column": 63}}, "5": {"start": {"line": 85, "column": 8}, "end": {"line": 85, "column": 35}}, "6": {"start": {"line": 88, "column": 8}, "end": {"line": 88, "column": 2940}}, "7": {"start": {"line": 100, "column": 8}, "end": {"line": 100, "column": 53}}, "8": {"start": {"line": 102, "column": 8}, "end": {"line": 102, "column": 3311}}, "9": {"start": {"line": 103, "column": 12}, "end": {"line": 103, "column": 37}}, "10": {"start": {"line": 106, "column": 12}, "end": {"line": 106, "column": 39}}, "11": {"start": {"line": 107, "column": 12}, "end": {"line": 107, "column": 3472}}, "12": {"start": {"line": 110, "column": 12}, "end": {"line": 110, "column": 23}}, "13": {"start": {"line": 126, "column": 8}, "end": {"line": 126, "column": 40}}, "14": {"start": {"line": 127, "column": 8}, "end": {"line": 127, "column": 31}}, "15": {"start": {"line": 128, "column": 8}, "end": {"line": 128, "column": 36}}, "16": {"start": {"line": 129, "column": 8}, "end": {"line": 129, "column": 68}}, "17": {"start": {"line": 131, "column": 8}, "end": {"line": 131, "column": 4160}}, "18": {"start": {"line": 132, "column": 12}, "end": {"line": 132, "column": 51}}, "19": {"start": {"line": 133, "column": 12}, "end": {"line": 133, "column": 70}}, "20": {"start": {"line": 135, "column": 12}, "end": {"line": 135, "column": 4365}}, "21": {"start": {"line": 141, "column": 16}, "end": {"line": 141, "column": 83}}, "22": {"start": {"line": 151, "column": 8}, "end": {"line": 151, "column": 66}}, "23": {"start": {"line": 163, "column": 8}, "end": {"line": 163, "column": 51}}, "24": {"start": {"line": 164, "column": 8}, "end": {"line": 164, "column": 56}}, "25": {"start": {"line": 165, "column": 8}, "end": {"line": 165, "column": 71}}, "26": {"start": {"line": 166, "column": 8}, "end": {"line": 166, "column": 5462}}, "27": {"start": {"line": 170, "column": 8}, "end": {"line": 170, "column": 5598}}, "28": {"start": {"line": 175, "column": 8}, "end": {"line": 175, "column": 41}}, "29": {"start": {"line": 178, "column": 8}, "end": {"line": 178, "column": 5833}}, "30": {"start": {"line": 181, "column": 8}, "end": {"line": 181, "column": 55}}, "31": {"start": {"line": 182, "column": 8}, "end": {"line": 182, "column": 5978}}, "32": {"start": {"line": 188, "column": 8}, "end": {"line": 188, "column": 61}}, "33": {"start": {"line": 195, "column": 8}, "end": {"line": 195, "column": 6349}}, "34": {"start": {"line": 196, "column": 12}, "end": {"line": 196, "column": 6419}}, "35": {"start": {"line": 203, "column": 8}, "end": {"line": 203, "column": 6603}}, "36": {"start": {"line": 213, "column": 8}, "end": {"line": 213, "column": 6802}}, "37": {"start": {"line": 216, "column": 12}, "end": {"line": 216, "column": 6958}}, "38": {"start": {"line": 221, "column": 12}, "end": {"line": 221, "column": 62}}, "39": {"start": {"line": 233, "column": 8}, "end": {"line": 233, "column": 40}}, "40": {"start": {"line": 234, "column": 8}, "end": {"line": 234, "column": 45}}, "41": {"start": {"line": 236, "column": 8}, "end": {"line": 236, "column": 7453}}, "42": {"start": {"line": 237, "column": 12}, "end": {"line": 237, "column": 7530}}, "43": {"start": {"line": 245, "column": 16}, "end": {"line": 245, "column": 100}}, "44": {"start": {"line": 261, "column": 8}, "end": {"line": 261, "column": 41}}, "45": {"start": {"line": 268, "column": 8}, "end": {"line": 268, "column": 8476}}, "46": {"start": {"line": 277, "column": 8}, "end": {"line": 277, "column": 15}}, "47": {"start": {"line": 284, "column": 8}, "end": {"line": 284, "column": 17}}, "48": {"start": {"line": 291, "column": 8}, "end": {"line": 291, "column": 54}}, "49": {"start": {"line": 292, "column": 8}, "end": {"line": 292, "column": 55}}, "50": {"start": {"line": 306, "column": 8}, "end": {"line": 306, "column": 9309}}}, "branchMap": {"1": {"line": 62, "type": "if", "locations": [{"start": {"line": 62, "column": 8}, "end": {"line": 62, "column": 8}}, {"start": {"line": 62, "column": 8}, "end": {"line": 62, "column": 8}}]}, "2": {"line": 63, "type": "if", "locations": [{"start": {"line": 63, "column": 8}, "end": {"line": 63, "column": 8}}, {"start": {"line": 63, "column": 8}, "end": {"line": 63, "column": 8}}]}, "3": {"line": 64, "type": "if", "locations": [{"start": {"line": 64, "column": 8}, "end": {"line": 64, "column": 8}}, {"start": {"line": 64, "column": 8}, "end": {"line": 64, "column": 8}}]}, "4": {"line": 82, "type": "if", "locations": [{"start": {"line": 82, "column": 40}, "end": {"line": 82, "column": 40}}, {"start": {"line": 82, "column": 40}, "end": {"line": 82, "column": 40}}]}, "5": {"line": 83, "type": "if", "locations": [{"start": {"line": 83, "column": 8}, "end": {"line": 83, "column": 8}}, {"start": {"line": 83, "column": 8}, "end": {"line": 83, "column": 8}}]}, "6": {"line": 100, "type": "if", "locations": [{"start": {"line": 100, "column": 8}, "end": {"line": 100, "column": 8}}, {"start": {"line": 100, "column": 8}, "end": {"line": 100, "column": 8}}]}, "7": {"line": 102, "type": "if", "locations": [{"start": {"line": 102, "column": 8}, "end": {"line": 102, "column": 8}}, {"start": {"line": 102, "column": 8}, "end": {"line": 102, "column": 8}}]}, "8": {"line": 135, "type": "if", "locations": [{"start": {"line": 135, "column": 12}, "end": {"line": 135, "column": 12}}, {"start": {"line": 135, "column": 12}, "end": {"line": 135, "column": 12}}]}, "9": {"line": 151, "type": "if", "locations": [{"start": {"line": 151, "column": 8}, "end": {"line": 151, "column": 8}}, {"start": {"line": 151, "column": 8}, "end": {"line": 151, "column": 8}}]}, "10": {"line": 162, "type": "if", "locations": [{"start": {"line": 162, "column": 57}, "end": {"line": 162, "column": 57}}, {"start": {"line": 162, "column": 57}, "end": {"line": 162, "column": 57}}]}, "11": {"line": 162, "type": "if", "locations": [{"start": {"line": 162, "column": 70}, "end": {"line": 162, "column": 70}}, {"start": {"line": 162, "column": 70}, "end": {"line": 162, "column": 70}}]}, "12": {"line": 163, "type": "if", "locations": [{"start": {"line": 163, "column": 8}, "end": {"line": 163, "column": 8}}, {"start": {"line": 163, "column": 8}, "end": {"line": 163, "column": 8}}]}, "13": {"line": 164, "type": "if", "locations": [{"start": {"line": 164, "column": 8}, "end": {"line": 164, "column": 8}}, {"start": {"line": 164, "column": 8}, "end": {"line": 164, "column": 8}}]}, "14": {"line": 165, "type": "if", "locations": [{"start": {"line": 165, "column": 8}, "end": {"line": 165, "column": 8}}, {"start": {"line": 165, "column": 8}, "end": {"line": 165, "column": 8}}]}, "15": {"line": 166, "type": "if", "locations": [{"start": {"line": 166, "column": 8}, "end": {"line": 166, "column": 8}}, {"start": {"line": 166, "column": 8}, "end": {"line": 166, "column": 8}}]}, "16": {"line": 170, "type": "if", "locations": [{"start": {"line": 170, "column": 8}, "end": {"line": 170, "column": 8}}, {"start": {"line": 170, "column": 8}, "end": {"line": 170, "column": 8}}]}, "17": {"line": 181, "type": "if", "locations": [{"start": {"line": 181, "column": 8}, "end": {"line": 181, "column": 8}}, {"start": {"line": 181, "column": 8}, "end": {"line": 181, "column": 8}}]}, "18": {"line": 182, "type": "if", "locations": [{"start": {"line": 182, "column": 8}, "end": {"line": 182, "column": 8}}, {"start": {"line": 182, "column": 8}, "end": {"line": 182, "column": 8}}]}, "19": {"line": 195, "type": "if", "locations": [{"start": {"line": 195, "column": 8}, "end": {"line": 195, "column": 8}}, {"start": {"line": 195, "column": 8}, "end": {"line": 195, "column": 8}}]}, "20": {"line": 213, "type": "if", "locations": [{"start": {"line": 213, "column": 8}, "end": {"line": 213, "column": 8}}, {"start": {"line": 213, "column": 8}, "end": {"line": 213, "column": 8}}]}, "21": {"line": 213, "type": "cond-expr", "locations": [{"start": {"line": 213, "column": 12}, "end": {"line": 213, "column": 39}}, {"start": {"line": 213, "column": 44}, "end": {"line": 213, "column": 82}}]}, "22": {"line": 237, "type": "if", "locations": [{"start": {"line": 237, "column": 12}, "end": {"line": 237, "column": 12}}, {"start": {"line": 237, "column": 12}, "end": {"line": 237, "column": 12}}]}, "23": {"line": 267, "type": "if", "locations": [{"start": {"line": 267, "column": 63}, "end": {"line": 267, "column": 63}}, {"start": {"line": 267, "column": 63}, "end": {"line": 267, "column": 63}}]}, "24": {"line": 276, "type": "if", "locations": [{"start": {"line": 276, "column": 30}, "end": {"line": 276, "column": 30}}, {"start": {"line": 276, "column": 30}, "end": {"line": 276, "column": 30}}]}, "25": {"line": 283, "type": "if", "locations": [{"start": {"line": 283, "column": 32}, "end": {"line": 283, "column": 32}}, {"start": {"line": 283, "column": 32}, "end": {"line": 283, "column": 32}}]}, "26": {"line": 290, "type": "if", "locations": [{"start": {"line": 290, "column": 36}, "end": {"line": 290, "column": 36}}, {"start": {"line": 290, "column": 36}, "end": {"line": 290, "column": 36}}]}, "27": {"line": 291, "type": "if", "locations": [{"start": {"line": 291, "column": 8}, "end": {"line": 291, "column": 8}}, {"start": {"line": 291, "column": 8}, "end": {"line": 291, "column": 8}}]}}}, "contracts/HAOXPriceAggregatorMinimal.sol": {"l": {"53": 21, "60": 63, "61": 63, "62": 63, "65": 63, "66": 63, "69": 63, "76": 63, "77": 63, "84": 2, "85": 2, "87": 2, "88": 2, "90": 2, "97": 3, "98": 0, "102": 0, "105": 3, "106": 2, "111": 2, "118": 0, "125": 4, "127": 4, "128": 4, "129": 4, "132": 4, "133": 12, "136": 10, "137": 10, "140": 10, "142": 10, "143": 10, "144": 10, "146": 10, "149": 4, "151": 3, "154": 3, "156": 3, "157": 3, "158": 3, "160": 3, "167": 0, "169": 0, "173": 0, "174": 0, "181": 10, "183": 10, "187": 10, "188": 10, "189": 10, "191": 0, "192": 0, "200": 3, "202": 0, "206": 0, "213": 4, "215": 3, "216": 3, "217": 3, "219": 3, "226": 1, "228": 1, "229": 1, "230": 3, "232": 1, "234": 1, "235": 1, "236": 1, "238": 1, "251": 0, "252": 0, "253": 0, "256": 0, "274": 1, "275": 1, "277": 1, "284": 0, "286": 0, "287": 0, "288": 0, "289": 0, "298": 2, "302": 1, "310": 0, "311": 0}, "path": "/Users/<USER>/Desktop/sociomint222/contracts/contracts/HAOXPriceAggregatorMinimal.sol", "s": {"1": 63, "2": 63, "3": 63, "4": 63, "5": 63, "6": 63, "7": 2, "8": 2, "9": 2, "10": 3, "11": 0, "12": 0, "13": 3, "14": 2, "15": 2, "16": 0, "17": 4, "18": 4, "19": 4, "20": 4, "21": 4, "22": 12, "23": 10, "24": 10, "25": 10, "26": 4, "27": 3, "28": 3, "29": 3, "30": 0, "31": 0, "32": 0, "33": 0, "34": 10, "35": 10, "36": 10, "37": 3, "38": 3, "39": 0, "40": 0, "41": 4, "42": 3, "43": 1, "44": 1, "45": 1, "46": 3, "47": 1, "48": 1, "49": 0, "50": 0, "51": 0, "52": 0, "53": 1, "54": 1, "55": 1, "56": 0, "57": 0, "58": 0, "59": 0, "60": 2, "61": 1, "62": 0, "63": 0}, "b": {"1": [63, 0], "2": [63, 0], "3": [63, 0], "4": [63, 0], "5": [63, 0], "6": [2, 0], "7": [2, 0], "8": [2, 0], "9": [0, 3], "10": [0, 0], "11": [2, 1], "12": [2, 0], "13": [0, 0], "14": [4, 0], "15": [4, 0], "16": [2, 10], "17": [0, 10], "18": [0, 10], "19": [0, 0], "20": [3, 1], "21": [0, 0], "22": [10, 0], "23": [3, 0], "24": [0, 0], "25": [0, 0], "26": [4, 1], "27": [3, 1], "28": [1, 0], "29": [1, 0], "30": [3, 0], "31": [1, 0], "32": [0, 0], "33": [1, 0], "34": [0, 0], "35": [0, 0], "36": [0, 0], "37": [2, 1], "38": [1, 0], "39": [0, 0]}, "f": {"1": 21, "2": 63, "3": 2, "4": 3, "5": 0, "6": 4, "7": 0, "8": 10, "9": 3, "10": 4, "11": 1, "12": 0, "13": 1, "14": 0, "15": 2, "16": 1, "17": 0}, "fnMap": {"1": {"name": "constructor", "line": 52, "loc": {"start": {"line": 52, "column": 4}, "end": {"line": 54, "column": 4}}}, "2": {"name": "addPriceSource", "line": 59, "loc": {"start": {"line": 59, "column": 4}, "end": {"line": 78, "column": 4}}}, "3": {"name": "updatePriceSource", "line": 83, "loc": {"start": {"line": 83, "column": 4}, "end": {"line": 91, "column": 4}}}, "4": {"name": "getLatestPrice", "line": 96, "loc": {"start": {"line": 96, "column": 4}, "end": {"line": 112, "column": 4}}}, "5": {"name": "getLastUpdateTime", "line": 117, "loc": {"start": {"line": 117, "column": 4}, "end": {"line": 119, "column": 4}}}, "6": {"name": "updateAggregatedPrice", "line": 124, "loc": {"start": {"line": 124, "column": 4}, "end": {"line": 161, "column": 4}}}, "7": {"name": "_getPriceFromSource", "line": 166, "loc": {"start": {"line": 166, "column": 4}, "end": {"line": 175, "column": 4}}}, "8": {"name": "_tryGetPriceFromSource", "line": 180, "loc": {"start": {"line": 180, "column": 4}, "end": {"line": 194, "column": 4}}}, "9": {"name": "_validatePriceDeviation", "line": 199, "loc": {"start": {"line": 199, "column": 4}, "end": {"line": 207, "column": 4}}}, "10": {"name": "activateEmergencyMode", "line": 212, "loc": {"start": {"line": 212, "column": 4}, "end": {"line": 220, "column": 4}}}, "11": {"name": "deactivateEmergencyMode", "line": 225, "loc": {"start": {"line": 225, "column": 4}, "end": {"line": 239, "column": 4}}}, "12": {"name": "getAggregatorStatus", "line": 244, "loc": {"start": {"line": 244, "column": 4}, "end": {"line": 263, "column": 4}}}, "13": {"name": "getPriceSource", "line": 268, "loc": {"start": {"line": 268, "column": 4}, "end": {"line": 278, "column": 4}}}, "14": {"name": "batchUpdateSources", "line": 283, "loc": {"start": {"line": 283, "column": 4}, "end": {"line": 292, "column": 4}}}, "15": {"name": "pause", "line": 297, "loc": {"start": {"line": 297, "column": 4}, "end": {"line": 299, "column": 4}}}, "16": {"name": "unpause", "line": 301, "loc": {"start": {"line": 301, "column": 4}, "end": {"line": 303, "column": 4}}}, "17": {"name": "emergencyPause", "line": 308, "loc": {"start": {"line": 308, "column": 4}, "end": {"line": 312, "column": 4}}}}, "statementMap": {"1": {"start": {"line": 60, "column": 8}, "end": {"line": 60, "column": 54}}, "2": {"start": {"line": 61, "column": 8}, "end": {"line": 61, "column": 61}}, "3": {"start": {"line": 62, "column": 8}, "end": {"line": 62, "column": 61}}, "4": {"start": {"line": 65, "column": 8}, "end": {"line": 65, "column": 1907}}, "5": {"start": {"line": 66, "column": 12}, "end": {"line": 66, "column": 69}}, "6": {"start": {"line": 76, "column": 8}, "end": {"line": 76, "column": 58}}, "7": {"start": {"line": 84, "column": 8}, "end": {"line": 84, "column": 56}}, "8": {"start": {"line": 85, "column": 8}, "end": {"line": 85, "column": 61}}, "9": {"start": {"line": 90, "column": 8}, "end": {"line": 90, "column": 57}}, "10": {"start": {"line": 97, "column": 8}, "end": {"line": 97, "column": 2850}}, "11": {"start": {"line": 98, "column": 12}, "end": {"line": 98, "column": 2887}}, "12": {"start": {"line": 102, "column": 12}, "end": {"line": 102, "column": 33}}, "13": {"start": {"line": 105, "column": 8}, "end": {"line": 105, "column": 56}}, "14": {"start": {"line": 106, "column": 8}, "end": {"line": 106, "column": 3160}}, "15": {"start": {"line": 111, "column": 8}, "end": {"line": 111, "column": 26}}, "16": {"start": {"line": 118, "column": 8}, "end": {"line": 118, "column": 71}}, "17": {"start": {"line": 125, "column": 8}, "end": {"line": 125, "column": 55}}, "18": {"start": {"line": 127, "column": 8}, "end": {"line": 127, "column": 31}}, "19": {"start": {"line": 128, "column": 8}, "end": {"line": 128, "column": 31}}, "20": {"start": {"line": 129, "column": 8}, "end": {"line": 129, "column": 30}}, "21": {"start": {"line": 132, "column": 8}, "end": {"line": 132, "column": 3811}}, "22": {"start": {"line": 133, "column": 12}, "end": {"line": 133, "column": 49}}, "23": {"start": {"line": 136, "column": 12}, "end": {"line": 136, "column": 69}}, "24": {"start": {"line": 137, "column": 12}, "end": {"line": 137, "column": 34}}, "25": {"start": {"line": 140, "column": 12}, "end": {"line": 140, "column": 52}}, "26": {"start": {"line": 149, "column": 8}, "end": {"line": 149, "column": 76}}, "27": {"start": {"line": 151, "column": 8}, "end": {"line": 151, "column": 59}}, "28": {"start": {"line": 154, "column": 8}, "end": {"line": 154, "column": 47}}, "29": {"start": {"line": 160, "column": 8}, "end": {"line": 160, "column": 76}}, "30": {"start": {"line": 167, "column": 8}, "end": {"line": 167, "column": 54}}, "31": {"start": {"line": 169, "column": 8}, "end": {"line": 169, "column": 4984}}, "32": {"start": {"line": 173, "column": 8}, "end": {"line": 173, "column": 66}}, "33": {"start": {"line": 174, "column": 8}, "end": {"line": 174, "column": 42}}, "34": {"start": {"line": 181, "column": 8}, "end": {"line": 181, "column": 54}}, "35": {"start": {"line": 183, "column": 8}, "end": {"line": 183, "column": 5443}}, "36": {"start": {"line": 187, "column": 8}, "end": {"line": 187, "column": 5577}}, "37": {"start": {"line": 200, "column": 8}, "end": {"line": 200, "column": 36}}, "38": {"start": {"line": 200, "column": 30}, "end": {"line": 200, "column": 36}}, "39": {"start": {"line": 202, "column": 8}, "end": {"line": 202, "column": 5998}}, "40": {"start": {"line": 206, "column": 8}, "end": {"line": 206, "column": 80}}, "41": {"start": {"line": 213, "column": 8}, "end": {"line": 213, "column": 42}}, "42": {"start": {"line": 219, "column": 8}, "end": {"line": 219, "column": 59}}, "43": {"start": {"line": 226, "column": 8}, "end": {"line": 226, "column": 54}}, "44": {"start": {"line": 228, "column": 8}, "end": {"line": 228, "column": 29}}, "45": {"start": {"line": 229, "column": 8}, "end": {"line": 229, "column": 6765}}, "46": {"start": {"line": 230, "column": 12}, "end": {"line": 230, "column": 53}}, "47": {"start": {"line": 232, "column": 8}, "end": {"line": 232, "column": 75}}, "48": {"start": {"line": 238, "column": 8}, "end": {"line": 238, "column": 39}}, "49": {"start": {"line": 251, "column": 8}, "end": {"line": 251, "column": 24}}, "50": {"start": {"line": 252, "column": 8}, "end": {"line": 252, "column": 7393}}, "51": {"start": {"line": 253, "column": 12}, "end": {"line": 253, "column": 48}}, "52": {"start": {"line": 256, "column": 8}, "end": {"line": 256, "column": 7513}}, "53": {"start": {"line": 274, "column": 8}, "end": {"line": 274, "column": 56}}, "54": {"start": {"line": 275, "column": 8}, "end": {"line": 275, "column": 58}}, "55": {"start": {"line": 277, "column": 8}, "end": {"line": 277, "column": 79}}, "56": {"start": {"line": 284, "column": 8}, "end": {"line": 284, "column": 74}}, "57": {"start": {"line": 286, "column": 8}, "end": {"line": 286, "column": 8327}}, "58": {"start": {"line": 287, "column": 12}, "end": {"line": 287, "column": 8392}}, "59": {"start": {"line": 289, "column": 16}, "end": {"line": 289, "column": 105}}, "60": {"start": {"line": 298, "column": 8}, "end": {"line": 298, "column": 15}}, "61": {"start": {"line": 302, "column": 8}, "end": {"line": 302, "column": 17}}, "62": {"start": {"line": 310, "column": 8}, "end": {"line": 310, "column": 55}}, "63": {"start": {"line": 311, "column": 8}, "end": {"line": 311, "column": 15}}}, "branchMap": {"1": {"line": 59, "type": "if", "locations": [{"start": {"line": 59, "column": 67}, "end": {"line": 59, "column": 67}}, {"start": {"line": 59, "column": 67}, "end": {"line": 59, "column": 67}}]}, "2": {"line": 60, "type": "if", "locations": [{"start": {"line": 60, "column": 8}, "end": {"line": 60, "column": 8}}, {"start": {"line": 60, "column": 8}, "end": {"line": 60, "column": 8}}]}, "3": {"line": 61, "type": "if", "locations": [{"start": {"line": 61, "column": 8}, "end": {"line": 61, "column": 8}}, {"start": {"line": 61, "column": 8}, "end": {"line": 61, "column": 8}}]}, "4": {"line": 62, "type": "if", "locations": [{"start": {"line": 62, "column": 8}, "end": {"line": 62, "column": 8}}, {"start": {"line": 62, "column": 8}, "end": {"line": 62, "column": 8}}]}, "5": {"line": 66, "type": "if", "locations": [{"start": {"line": 66, "column": 12}, "end": {"line": 66, "column": 12}}, {"start": {"line": 66, "column": 12}, "end": {"line": 66, "column": 12}}]}, "6": {"line": 83, "type": "if", "locations": [{"start": {"line": 83, "column": 83}, "end": {"line": 83, "column": 83}}, {"start": {"line": 83, "column": 83}, "end": {"line": 83, "column": 83}}]}, "7": {"line": 84, "type": "if", "locations": [{"start": {"line": 84, "column": 8}, "end": {"line": 84, "column": 8}}, {"start": {"line": 84, "column": 8}, "end": {"line": 84, "column": 8}}]}, "8": {"line": 85, "type": "if", "locations": [{"start": {"line": 85, "column": 8}, "end": {"line": 85, "column": 8}}, {"start": {"line": 85, "column": 8}, "end": {"line": 85, "column": 8}}]}, "9": {"line": 97, "type": "if", "locations": [{"start": {"line": 97, "column": 8}, "end": {"line": 97, "column": 8}}, {"start": {"line": 97, "column": 8}, "end": {"line": 97, "column": 8}}]}, "10": {"line": 98, "type": "if", "locations": [{"start": {"line": 98, "column": 12}, "end": {"line": 98, "column": 12}}, {"start": {"line": 98, "column": 12}, "end": {"line": 98, "column": 12}}]}, "11": {"line": 105, "type": "if", "locations": [{"start": {"line": 105, "column": 8}, "end": {"line": 105, "column": 8}}, {"start": {"line": 105, "column": 8}, "end": {"line": 105, "column": 8}}]}, "12": {"line": 106, "type": "if", "locations": [{"start": {"line": 106, "column": 8}, "end": {"line": 106, "column": 8}}, {"start": {"line": 106, "column": 8}, "end": {"line": 106, "column": 8}}]}, "13": {"line": 118, "type": "if", "locations": [{"start": {"line": 118, "column": 31}, "end": {"line": 118, "column": 53}}, {"start": {"line": 118, "column": 57}, "end": {"line": 118, "column": 70}}]}, "14": {"line": 124, "type": "if", "locations": [{"start": {"line": 124, "column": 46}, "end": {"line": 124, "column": 46}}, {"start": {"line": 124, "column": 46}, "end": {"line": 124, "column": 46}}]}, "15": {"line": 125, "type": "if", "locations": [{"start": {"line": 125, "column": 8}, "end": {"line": 125, "column": 8}}, {"start": {"line": 125, "column": 8}, "end": {"line": 125, "column": 8}}]}, "16": {"line": 133, "type": "if", "locations": [{"start": {"line": 133, "column": 12}, "end": {"line": 133, "column": 12}}, {"start": {"line": 133, "column": 12}, "end": {"line": 133, "column": 12}}]}, "17": {"line": 137, "type": "if", "locations": [{"start": {"line": 137, "column": 12}, "end": {"line": 137, "column": 12}}, {"start": {"line": 137, "column": 12}, "end": {"line": 137, "column": 12}}]}, "18": {"line": 140, "type": "if", "locations": [{"start": {"line": 140, "column": 12}, "end": {"line": 140, "column": 12}}, {"start": {"line": 140, "column": 12}, "end": {"line": 140, "column": 12}}]}, "19": {"line": 140, "type": "cond-expr", "locations": [{"start": {"line": 140, "column": 16}, "end": {"line": 140, "column": 25}}, {"start": {"line": 140, "column": 30}, "end": {"line": 140, "column": 41}}]}, "20": {"line": 149, "type": "if", "locations": [{"start": {"line": 149, "column": 8}, "end": {"line": 149, "column": 8}}, {"start": {"line": 149, "column": 8}, "end": {"line": 149, "column": 8}}]}, "21": {"line": 173, "type": "if", "locations": [{"start": {"line": 173, "column": 8}, "end": {"line": 173, "column": 8}}, {"start": {"line": 173, "column": 8}, "end": {"line": 173, "column": 8}}]}, "22": {"line": 187, "type": "if", "locations": [{"start": {"line": 187, "column": 8}, "end": {"line": 187, "column": 8}}, {"start": {"line": 187, "column": 8}, "end": {"line": 187, "column": 8}}]}, "23": {"line": 200, "type": "if", "locations": [{"start": {"line": 200, "column": 8}, "end": {"line": 200, "column": 8}}, {"start": {"line": 200, "column": 8}, "end": {"line": 200, "column": 8}}]}, "24": {"line": 203, "type": "if", "locations": [{"start": {"line": 203, "column": 14}, "end": {"line": 203, "column": 59}}, {"start": {"line": 204, "column": 14}, "end": {"line": 204, "column": 59}}]}, "25": {"line": 206, "type": "if", "locations": [{"start": {"line": 206, "column": 8}, "end": {"line": 206, "column": 8}}, {"start": {"line": 206, "column": 8}, "end": {"line": 206, "column": 8}}]}, "26": {"line": 212, "type": "if", "locations": [{"start": {"line": 212, "column": 59}, "end": {"line": 212, "column": 59}}, {"start": {"line": 212, "column": 59}, "end": {"line": 212, "column": 59}}]}, "27": {"line": 213, "type": "if", "locations": [{"start": {"line": 213, "column": 8}, "end": {"line": 213, "column": 8}}, {"start": {"line": 213, "column": 8}, "end": {"line": 213, "column": 8}}]}, "28": {"line": 225, "type": "if", "locations": [{"start": {"line": 225, "column": 48}, "end": {"line": 225, "column": 48}}, {"start": {"line": 225, "column": 48}, "end": {"line": 225, "column": 48}}]}, "29": {"line": 226, "type": "if", "locations": [{"start": {"line": 226, "column": 8}, "end": {"line": 226, "column": 8}}, {"start": {"line": 226, "column": 8}, "end": {"line": 226, "column": 8}}]}, "30": {"line": 230, "type": "if", "locations": [{"start": {"line": 230, "column": 12}, "end": {"line": 230, "column": 12}}, {"start": {"line": 230, "column": 12}, "end": {"line": 230, "column": 12}}]}, "31": {"line": 232, "type": "if", "locations": [{"start": {"line": 232, "column": 8}, "end": {"line": 232, "column": 8}}, {"start": {"line": 232, "column": 8}, "end": {"line": 232, "column": 8}}]}, "32": {"line": 253, "type": "if", "locations": [{"start": {"line": 253, "column": 12}, "end": {"line": 253, "column": 12}}, {"start": {"line": 253, "column": 12}, "end": {"line": 253, "column": 12}}]}, "33": {"line": 274, "type": "if", "locations": [{"start": {"line": 274, "column": 8}, "end": {"line": 274, "column": 8}}, {"start": {"line": 274, "column": 8}, "end": {"line": 274, "column": 8}}]}, "34": {"line": 283, "type": "if", "locations": [{"start": {"line": 283, "column": 99}, "end": {"line": 283, "column": 99}}, {"start": {"line": 283, "column": 99}, "end": {"line": 283, "column": 99}}]}, "35": {"line": 284, "type": "if", "locations": [{"start": {"line": 284, "column": 8}, "end": {"line": 284, "column": 8}}, {"start": {"line": 284, "column": 8}, "end": {"line": 284, "column": 8}}]}, "36": {"line": 287, "type": "if", "locations": [{"start": {"line": 287, "column": 12}, "end": {"line": 287, "column": 12}}, {"start": {"line": 287, "column": 12}, "end": {"line": 287, "column": 12}}]}, "37": {"line": 297, "type": "if", "locations": [{"start": {"line": 297, "column": 30}, "end": {"line": 297, "column": 30}}, {"start": {"line": 297, "column": 30}, "end": {"line": 297, "column": 30}}]}, "38": {"line": 301, "type": "if", "locations": [{"start": {"line": 301, "column": 32}, "end": {"line": 301, "column": 32}}, {"start": {"line": 301, "column": 32}, "end": {"line": 301, "column": 32}}]}, "39": {"line": 310, "type": "if", "locations": [{"start": {"line": 310, "column": 8}, "end": {"line": 310, "column": 8}}, {"start": {"line": 310, "column": 8}, "end": {"line": 310, "column": 8}}]}}}, "contracts/HAOXPriceOracleV2.sol": {"l": {"86": 37, "87": 35, "88": 35, "89": 35, "91": 35, "92": 35, "93": 35, "94": 35, "97": 35, "104": 97, "111": 0, "112": 0, "114": 0, "115": 0, "118": 0, "119": 0, "120": 0, "123": 97, "124": 97, "132": 62, "137": 0, "140": 0, "142": 0, "143": 0, "146": 0, "149": 0, "150": 0, "152": 0, "153": 0, "154": 0, "156": 0, "159": 62, "160": 62, "168": 63, "169": 1, "172": 62, "173": 62, "175": 62, "176": 62, "180": 0, "181": 0, "188": 0, "192": 0, "199": 35, "200": 35, "203": 35, "205": 0, "206": 0, "210": 0, "212": 0, "217": 0, "224": 0, "232": 0, "233": 0, "235": 0, "243": 5, "244": 5, "245": 5, "252": 1, "253": 1, "254": 1, "261": 28, "262": 28, "269": 0, "276": 2, "283": 1}, "path": "/Users/<USER>/Desktop/sociomint222/contracts/contracts/HAOXPriceOracleV2.sol", "s": {"1": 37, "2": 35, "3": 35, "4": 35, "5": 35, "6": 97, "7": 0, "8": 0, "9": 0, "10": 0, "11": 62, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 63, "20": 1, "21": 62, "22": 62, "23": 62, "24": 62, "25": 0, "26": 0, "27": 35, "28": 35, "29": 35, "30": 0, "31": 0, "32": 0, "33": 0, "34": 5, "35": 1, "36": 28, "37": 28, "38": 0, "39": 2, "40": 1}, "b": {"1": [35, 2], "2": [35, 0], "3": [35, 0], "4": [35, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [1, 62], "16": [62, 0], "17": [62, 0], "18": [0, 0], "19": [0, 0], "20": [0, 35], "21": [0, 0], "22": [0, 0], "23": [0, 0], "24": [5, 1], "25": [1, 0], "26": [2, 1], "27": [1, 0]}, "f": {"1": 37, "2": 97, "3": 62, "4": 63, "5": 0, "6": 35, "7": 5, "8": 1, "9": 28, "10": 0, "11": 2, "12": 1}, "fnMap": {"1": {"name": "constructor", "line": 85, "loc": {"start": {"line": 80, "column": 4}, "end": {"line": 98, "column": 4}}}, "2": {"name": "getBnbUsdPrice", "line": 103, "loc": {"start": {"line": 103, "column": 4}, "end": {"line": 126, "column": 4}}}, "3": {"name": "getHaoxBnbPrice", "line": 131, "loc": {"start": {"line": 131, "column": 4}, "end": {"line": 162, "column": 4}}}, "4": {"name": "getHaoxUsdPrice", "line": 167, "loc": {"start": {"line": 167, "column": 4}, "end": {"line": 182, "column": 4}}}, "5": {"name": "updatePrices", "line": 187, "loc": {"start": {"line": 187, "column": 4}, "end": {"line": 193, "column": 4}}}, "6": {"name": "_updatePrices", "line": 198, "loc": {"start": {"line": 198, "column": 4}, "end": {"line": 237, "column": 4}}}, "7": {"name": "activateEmergencyMode", "line": 242, "loc": {"start": {"line": 242, "column": 4}, "end": {"line": 246, "column": 4}}}, "8": {"name": "deactivateEmergencyMode", "line": 251, "loc": {"start": {"line": 251, "column": 4}, "end": {"line": 255, "column": 4}}}, "9": {"name": "getLatestPrice", "line": 260, "loc": {"start": {"line": 260, "column": 4}, "end": {"line": 263, "column": 4}}}, "10": {"name": "getPriceWithConfidence", "line": 268, "loc": {"start": {"line": 268, "column": 4}, "end": {"line": 270, "column": 4}}}, "11": {"name": "pause", "line": 275, "loc": {"start": {"line": 275, "column": 4}, "end": {"line": 277, "column": 4}}}, "12": {"name": "unpause", "line": 282, "loc": {"start": {"line": 282, "column": 4}, "end": {"line": 284, "column": 4}}}}, "statementMap": {"1": {"start": {"line": 86, "column": 8}, "end": {"line": 86, "column": 75}}, "2": {"start": {"line": 87, "column": 8}, "end": {"line": 87, "column": 66}}, "3": {"start": {"line": 88, "column": 8}, "end": {"line": 88, "column": 64}}, "4": {"start": {"line": 89, "column": 8}, "end": {"line": 89, "column": 59}}, "5": {"start": {"line": 97, "column": 8}, "end": {"line": 97, "column": 22}}, "6": {"start": {"line": 104, "column": 8}, "end": {"line": 104, "column": 3028}}, "7": {"start": {"line": 111, "column": 12}, "end": {"line": 111, "column": 47}}, "8": {"start": {"line": 112, "column": 12}, "end": {"line": 112, "column": 95}}, "9": {"start": {"line": 114, "column": 12}, "end": {"line": 114, "column": 55}}, "10": {"start": {"line": 118, "column": 12}, "end": {"line": 118, "column": 53}}, "11": {"start": {"line": 132, "column": 8}, "end": {"line": 132, "column": 3995}}, "12": {"start": {"line": 137, "column": 12}, "end": {"line": 137, "column": 68}}, "13": {"start": {"line": 140, "column": 12}, "end": {"line": 140, "column": 65}}, "14": {"start": {"line": 142, "column": 12}, "end": {"line": 142, "column": 86}}, "15": {"start": {"line": 143, "column": 12}, "end": {"line": 143, "column": 85}}, "16": {"start": {"line": 149, "column": 12}, "end": {"line": 149, "column": 61}}, "17": {"start": {"line": 150, "column": 12}, "end": {"line": 150, "column": 71}}, "18": {"start": {"line": 153, "column": 12}, "end": {"line": 153, "column": 4916}}, "19": {"start": {"line": 168, "column": 8}, "end": {"line": 168, "column": 5325}}, "20": {"start": {"line": 169, "column": 12}, "end": {"line": 169, "column": 40}}, "21": {"start": {"line": 172, "column": 8}, "end": {"line": 172, "column": 71}}, "22": {"start": {"line": 173, "column": 8}, "end": {"line": 173, "column": 74}}, "23": {"start": {"line": 175, "column": 8}, "end": {"line": 175, "column": 5573}}, "24": {"start": {"line": 176, "column": 12}, "end": {"line": 176, "column": 72}}, "25": {"start": {"line": 188, "column": 8}, "end": {"line": 188, "column": 5987}}, "26": {"start": {"line": 192, "column": 8}, "end": {"line": 192, "column": 22}}, "27": {"start": {"line": 199, "column": 8}, "end": {"line": 199, "column": 71}}, "28": {"start": {"line": 200, "column": 8}, "end": {"line": 200, "column": 68}}, "29": {"start": {"line": 203, "column": 8}, "end": {"line": 203, "column": 6398}}, "30": {"start": {"line": 205, "column": 12}, "end": {"line": 205, "column": 6511}}, "31": {"start": {"line": 206, "column": 16}, "end": {"line": 206, "column": 6640}}, "32": {"start": {"line": 210, "column": 16}, "end": {"line": 210, "column": 6840}}, "33": {"start": {"line": 235, "column": 12}, "end": {"line": 235, "column": 91}}, "34": {"start": {"line": 245, "column": 8}, "end": {"line": 245, "column": 48}}, "35": {"start": {"line": 254, "column": 8}, "end": {"line": 254, "column": 39}}, "36": {"start": {"line": 261, "column": 8}, "end": {"line": 261, "column": 44}}, "37": {"start": {"line": 262, "column": 8}, "end": {"line": 262, "column": 20}}, "38": {"start": {"line": 269, "column": 8}, "end": {"line": 269, "column": 32}}, "39": {"start": {"line": 276, "column": 8}, "end": {"line": 276, "column": 15}}, "40": {"start": {"line": 283, "column": 8}, "end": {"line": 283, "column": 17}}}, "branchMap": {"1": {"line": 86, "type": "if", "locations": [{"start": {"line": 86, "column": 8}, "end": {"line": 86, "column": 8}}, {"start": {"line": 86, "column": 8}, "end": {"line": 86, "column": 8}}]}, "2": {"line": 87, "type": "if", "locations": [{"start": {"line": 87, "column": 8}, "end": {"line": 87, "column": 8}}, {"start": {"line": 87, "column": 8}, "end": {"line": 87, "column": 8}}]}, "3": {"line": 88, "type": "if", "locations": [{"start": {"line": 88, "column": 8}, "end": {"line": 88, "column": 8}}, {"start": {"line": 88, "column": 8}, "end": {"line": 88, "column": 8}}]}, "4": {"line": 89, "type": "if", "locations": [{"start": {"line": 89, "column": 8}, "end": {"line": 89, "column": 8}}, {"start": {"line": 89, "column": 8}, "end": {"line": 89, "column": 8}}]}, "5": {"line": 111, "type": "if", "locations": [{"start": {"line": 111, "column": 12}, "end": {"line": 111, "column": 12}}, {"start": {"line": 111, "column": 12}, "end": {"line": 111, "column": 12}}]}, "6": {"line": 112, "type": "if", "locations": [{"start": {"line": 112, "column": 12}, "end": {"line": 112, "column": 12}}, {"start": {"line": 112, "column": 12}, "end": {"line": 112, "column": 12}}]}, "7": {"line": 119, "type": "if", "locations": [{"start": {"line": 119, "column": 49}, "end": {"line": 119, "column": 51}}, {"start": {"line": 119, "column": 55}, "end": {"line": 119, "column": 83}}]}, "8": {"line": 120, "type": "if", "locations": [{"start": {"line": 120, "column": 44}, "end": {"line": 120, "column": 46}}, {"start": {"line": 120, "column": 50}, "end": {"line": 120, "column": 59}}]}, "9": {"line": 137, "type": "if", "locations": [{"start": {"line": 137, "column": 12}, "end": {"line": 137, "column": 12}}, {"start": {"line": 137, "column": 12}, "end": {"line": 137, "column": 12}}]}, "10": {"line": 142, "type": "if", "locations": [{"start": {"line": 142, "column": 49}, "end": {"line": 142, "column": 65}}, {"start": {"line": 142, "column": 69}, "end": {"line": 142, "column": 85}}]}, "11": {"line": 143, "type": "if", "locations": [{"start": {"line": 143, "column": 48}, "end": {"line": 143, "column": 64}}, {"start": {"line": 143, "column": 68}, "end": {"line": 143, "column": 84}}]}, "12": {"line": 152, "type": "if", "locations": [{"start": {"line": 152, "column": 61}, "end": {"line": 152, "column": 62}}, {"start": {"line": 152, "column": 66}, "end": {"line": 152, "column": 67}}]}, "13": {"line": 153, "type": "if", "locations": [{"start": {"line": 153, "column": 12}, "end": {"line": 153, "column": 12}}, {"start": {"line": 153, "column": 12}, "end": {"line": 153, "column": 12}}]}, "14": {"line": 156, "type": "if", "locations": [{"start": {"line": 156, "column": 44}, "end": {"line": 156, "column": 46}}, {"start": {"line": 156, "column": 50}, "end": {"line": 156, "column": 59}}]}, "15": {"line": 168, "type": "if", "locations": [{"start": {"line": 168, "column": 8}, "end": {"line": 168, "column": 8}}, {"start": {"line": 168, "column": 8}, "end": {"line": 168, "column": 8}}]}, "16": {"line": 175, "type": "if", "locations": [{"start": {"line": 175, "column": 8}, "end": {"line": 175, "column": 8}}, {"start": {"line": 175, "column": 8}, "end": {"line": 175, "column": 8}}]}, "17": {"line": 175, "type": "cond-expr", "locations": [{"start": {"line": 175, "column": 12}, "end": {"line": 175, "column": 27}}, {"start": {"line": 175, "column": 32}, "end": {"line": 175, "column": 48}}]}, "18": {"line": 187, "type": "if", "locations": [{"start": {"line": 187, "column": 37}, "end": {"line": 187, "column": 37}}, {"start": {"line": 187, "column": 37}, "end": {"line": 187, "column": 37}}]}, "19": {"line": 188, "type": "if", "locations": [{"start": {"line": 188, "column": 8}, "end": {"line": 188, "column": 8}}, {"start": {"line": 188, "column": 8}, "end": {"line": 188, "column": 8}}]}, "20": {"line": 203, "type": "if", "locations": [{"start": {"line": 203, "column": 8}, "end": {"line": 203, "column": 8}}, {"start": {"line": 203, "column": 8}, "end": {"line": 203, "column": 8}}]}, "21": {"line": 205, "type": "if", "locations": [{"start": {"line": 205, "column": 12}, "end": {"line": 205, "column": 12}}, {"start": {"line": 205, "column": 12}, "end": {"line": 205, "column": 12}}]}, "22": {"line": 207, "type": "if", "locations": [{"start": {"line": 207, "column": 22}, "end": {"line": 207, "column": 92}}, {"start": {"line": 208, "column": 22}, "end": {"line": 208, "column": 92}}]}, "23": {"line": 210, "type": "if", "locations": [{"start": {"line": 210, "column": 16}, "end": {"line": 210, "column": 16}}, {"start": {"line": 210, "column": 16}, "end": {"line": 210, "column": 16}}]}, "24": {"line": 242, "type": "if", "locations": [{"start": {"line": 242, "column": 65}, "end": {"line": 242, "column": 65}}, {"start": {"line": 242, "column": 65}, "end": {"line": 242, "column": 65}}]}, "25": {"line": 251, "type": "if", "locations": [{"start": {"line": 251, "column": 48}, "end": {"line": 251, "column": 48}}, {"start": {"line": 251, "column": 48}, "end": {"line": 251, "column": 48}}]}, "26": {"line": 275, "type": "if", "locations": [{"start": {"line": 275, "column": 30}, "end": {"line": 275, "column": 30}}, {"start": {"line": 275, "column": 30}, "end": {"line": 275, "column": 30}}]}, "27": {"line": 282, "type": "if", "locations": [{"start": {"line": 282, "column": 32}, "end": {"line": 282, "column": 32}}, {"start": {"line": 282, "column": 32}, "end": {"line": 282, "column": 32}}]}}}, "contracts/HAOXTokenV2.sol": {"l": {"57": 230, "64": 6, "66": 6, "68": 6, "72": 6, "75": 0, "76": 0, "78": 0, "79": 0, "80": 0, "82": 0, "83": 0, "91": 4, "93": 4, "94": 4, "98": 4, "100": 0, "101": 0, "103": 0, "104": 0, "105": 0, "107": 0, "108": 0, "116": 4, "118": 4, "119": 4, "123": 4, "125": 0, "126": 0, "128": 0, "129": 0, "130": 0, "132": 0, "133": 0, "141": 4, "143": 4, "144": 4, "148": 4, "150": 0, "151": 0, "153": 0, "154": 0, "155": 0, "157": 0, "158": 0, "166": 12, "167": 11, "168": 11, "169": 11, "176": 5, "177": 4, "178": 4, "185": 3, "186": 2, "187": 2, "189": 1, "190": 1, "197": 1, "204": 179, "211": 2, "226": 3, "227": 3, "228": 1, "229": 1, "232": 3}, "path": "/Users/<USER>/Desktop/sociomint222/contracts/contracts/HAOXTokenV2.sol", "s": {"1": 230, "2": 6, "3": 6, "4": 6, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 4, "11": 4, "12": 4, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 4, "19": 4, "20": 4, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 4, "27": 4, "28": 4, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 12, "35": 11, "36": 11, "37": 5, "38": 4, "39": 3, "40": 2, "41": 2, "42": 1, "43": 1, "44": 179, "45": 2, "46": 3, "47": 3, "48": 1, "49": 3}, "b": {"1": [6, 1], "2": [6, 0], "3": [0, 0], "4": [0, 0], "5": [4, 0], "6": [4, 0], "7": [0, 0], "8": [0, 0], "9": [4, 0], "10": [4, 0], "11": [0, 0], "12": [0, 0], "13": [4, 0], "14": [4, 0], "15": [0, 0], "16": [0, 0], "17": [12, 1], "18": [11, 1], "19": [5, 0], "20": [4, 1], "21": [2, 1], "22": [2, 0], "23": [1, 1], "24": [1, 0], "25": [179, 2], "26": [2, 1], "27": [1, 2], "28": [1, 0], "29": [1, 2]}, "f": {"1": 230, "2": 6, "3": 4, "4": 4, "5": 4, "6": 12, "7": 5, "8": 3, "9": 1, "10": 179, "11": 2, "12": 3}, "fnMap": {"1": {"name": "constructor", "line": 55, "loc": {"start": {"line": 55, "column": 4}, "end": {"line": 58, "column": 4}}}, "2": {"name": "setPresaleContract", "line": 63, "loc": {"start": {"line": 63, "column": 4}, "end": {"line": 85, "column": 4}}}, "3": {"name": "setInvitationContract", "line": 90, "loc": {"start": {"line": 90, "column": 4}, "end": {"line": 110, "column": 4}}}, "4": {"name": "setVestingContract", "line": 115, "loc": {"start": {"line": 115, "column": 4}, "end": {"line": 135, "column": 4}}}, "5": {"name": "setPriceO<PERSON>le", "line": 140, "loc": {"start": {"line": 140, "column": 4}, "end": {"line": 160, "column": 4}}}, "6": {"name": "emergencyPause", "line": 165, "loc": {"start": {"line": 165, "column": 4}, "end": {"line": 170, "column": 4}}}, "7": {"name": "unpause", "line": 175, "loc": {"start": {"line": 175, "column": 4}, "end": {"line": 179, "column": 4}}}, "8": {"name": "autoUnpause", "line": 184, "loc": {"start": {"line": 184, "column": 4}, "end": {"line": 191, "column": 4}}}, "9": {"name": "renounceOwnership", "line": 196, "loc": {"start": {"line": 196, "column": 4}, "end": {"line": 198, "column": 4}}}, "10": {"name": "transfer", "line": 203, "loc": {"start": {"line": 203, "column": 4}, "end": {"line": 205, "column": 4}}}, "11": {"name": "transferFrom", "line": 210, "loc": {"start": {"line": 210, "column": 4}, "end": {"line": 212, "column": 4}}}, "12": {"name": "getContractStatus", "line": 217, "loc": {"start": {"line": 217, "column": 4}, "end": {"line": 241, "column": 4}}}}, "statementMap": {"1": {"start": {"line": 57, "column": 8}, "end": {"line": 57, "column": 40}}, "2": {"start": {"line": 64, "column": 8}, "end": {"line": 64, "column": 109}}, "3": {"start": {"line": 66, "column": 8}, "end": {"line": 66, "column": 2083}}, "4": {"start": {"line": 72, "column": 12}, "end": {"line": 72, "column": 70}}, "5": {"start": {"line": 75, "column": 12}, "end": {"line": 75, "column": 91}}, "6": {"start": {"line": 76, "column": 12}, "end": {"line": 76, "column": 67}}, "7": {"start": {"line": 78, "column": 12}, "end": {"line": 78, "column": 48}}, "8": {"start": {"line": 82, "column": 12}, "end": {"line": 82, "column": 80}}, "9": {"start": {"line": 83, "column": 12}, "end": {"line": 83, "column": 41}}, "10": {"start": {"line": 91, "column": 8}, "end": {"line": 91, "column": 115}}, "11": {"start": {"line": 93, "column": 8}, "end": {"line": 93, "column": 3151}}, "12": {"start": {"line": 98, "column": 12}, "end": {"line": 98, "column": 70}}, "13": {"start": {"line": 100, "column": 12}, "end": {"line": 100, "column": 91}}, "14": {"start": {"line": 101, "column": 12}, "end": {"line": 101, "column": 67}}, "15": {"start": {"line": 103, "column": 12}, "end": {"line": 103, "column": 51}}, "16": {"start": {"line": 107, "column": 12}, "end": {"line": 107, "column": 86}}, "17": {"start": {"line": 108, "column": 12}, "end": {"line": 108, "column": 41}}, "18": {"start": {"line": 116, "column": 8}, "end": {"line": 116, "column": 109}}, "19": {"start": {"line": 118, "column": 8}, "end": {"line": 118, "column": 4180}}, "20": {"start": {"line": 123, "column": 12}, "end": {"line": 123, "column": 70}}, "21": {"start": {"line": 125, "column": 12}, "end": {"line": 125, "column": 91}}, "22": {"start": {"line": 126, "column": 12}, "end": {"line": 126, "column": 67}}, "23": {"start": {"line": 128, "column": 12}, "end": {"line": 128, "column": 48}}, "24": {"start": {"line": 132, "column": 12}, "end": {"line": 132, "column": 80}}, "25": {"start": {"line": 133, "column": 12}, "end": {"line": 133, "column": 41}}, "26": {"start": {"line": 141, "column": 8}, "end": {"line": 141, "column": 101}}, "27": {"start": {"line": 143, "column": 8}, "end": {"line": 143, "column": 5179}}, "28": {"start": {"line": 148, "column": 12}, "end": {"line": 148, "column": 70}}, "29": {"start": {"line": 150, "column": 12}, "end": {"line": 150, "column": 91}}, "30": {"start": {"line": 151, "column": 12}, "end": {"line": 151, "column": 67}}, "31": {"start": {"line": 153, "column": 12}, "end": {"line": 153, "column": 44}}, "32": {"start": {"line": 157, "column": 12}, "end": {"line": 157, "column": 80}}, "33": {"start": {"line": 158, "column": 12}, "end": {"line": 158, "column": 41}}, "34": {"start": {"line": 166, "column": 8}, "end": {"line": 166, "column": 43}}, "35": {"start": {"line": 168, "column": 8}, "end": {"line": 168, "column": 15}}, "36": {"start": {"line": 169, "column": 8}, "end": {"line": 169, "column": 56}}, "37": {"start": {"line": 176, "column": 8}, "end": {"line": 176, "column": 38}}, "38": {"start": {"line": 177, "column": 8}, "end": {"line": 177, "column": 17}}, "39": {"start": {"line": 185, "column": 8}, "end": {"line": 185, "column": 38}}, "40": {"start": {"line": 186, "column": 8}, "end": {"line": 186, "column": 57}}, "41": {"start": {"line": 187, "column": 8}, "end": {"line": 187, "column": 101}}, "42": {"start": {"line": 189, "column": 8}, "end": {"line": 189, "column": 17}}, "43": {"start": {"line": 197, "column": 8}, "end": {"line": 197, "column": 32}}, "44": {"start": {"line": 204, "column": 8}, "end": {"line": 204, "column": 41}}, "45": {"start": {"line": 211, "column": 8}, "end": {"line": 211, "column": 51}}, "46": {"start": {"line": 226, "column": 8}, "end": {"line": 226, "column": 29}}, "47": {"start": {"line": 227, "column": 8}, "end": {"line": 227, "column": 7628}}, "48": {"start": {"line": 228, "column": 12}, "end": {"line": 228, "column": 62}}, "49": {"start": {"line": 232, "column": 8}, "end": {"line": 232, "column": 7847}}}, "branchMap": {"1": {"line": 63, "type": "if", "locations": [{"start": {"line": 63, "column": 67}, "end": {"line": 63, "column": 67}}, {"start": {"line": 63, "column": 67}, "end": {"line": 63, "column": 67}}]}, "2": {"line": 66, "type": "if", "locations": [{"start": {"line": 66, "column": 8}, "end": {"line": 66, "column": 8}}, {"start": {"line": 66, "column": 8}, "end": {"line": 66, "column": 8}}]}, "3": {"line": 75, "type": "if", "locations": [{"start": {"line": 75, "column": 12}, "end": {"line": 75, "column": 12}}, {"start": {"line": 75, "column": 12}, "end": {"line": 75, "column": 12}}]}, "4": {"line": 76, "type": "if", "locations": [{"start": {"line": 76, "column": 12}, "end": {"line": 76, "column": 12}}, {"start": {"line": 76, "column": 12}, "end": {"line": 76, "column": 12}}]}, "5": {"line": 90, "type": "if", "locations": [{"start": {"line": 90, "column": 73}, "end": {"line": 90, "column": 73}}, {"start": {"line": 90, "column": 73}, "end": {"line": 90, "column": 73}}]}, "6": {"line": 93, "type": "if", "locations": [{"start": {"line": 93, "column": 8}, "end": {"line": 93, "column": 8}}, {"start": {"line": 93, "column": 8}, "end": {"line": 93, "column": 8}}]}, "7": {"line": 100, "type": "if", "locations": [{"start": {"line": 100, "column": 12}, "end": {"line": 100, "column": 12}}, {"start": {"line": 100, "column": 12}, "end": {"line": 100, "column": 12}}]}, "8": {"line": 101, "type": "if", "locations": [{"start": {"line": 101, "column": 12}, "end": {"line": 101, "column": 12}}, {"start": {"line": 101, "column": 12}, "end": {"line": 101, "column": 12}}]}, "9": {"line": 115, "type": "if", "locations": [{"start": {"line": 115, "column": 67}, "end": {"line": 115, "column": 67}}, {"start": {"line": 115, "column": 67}, "end": {"line": 115, "column": 67}}]}, "10": {"line": 118, "type": "if", "locations": [{"start": {"line": 118, "column": 8}, "end": {"line": 118, "column": 8}}, {"start": {"line": 118, "column": 8}, "end": {"line": 118, "column": 8}}]}, "11": {"line": 125, "type": "if", "locations": [{"start": {"line": 125, "column": 12}, "end": {"line": 125, "column": 12}}, {"start": {"line": 125, "column": 12}, "end": {"line": 125, "column": 12}}]}, "12": {"line": 126, "type": "if", "locations": [{"start": {"line": 126, "column": 12}, "end": {"line": 126, "column": 12}}, {"start": {"line": 126, "column": 12}, "end": {"line": 126, "column": 12}}]}, "13": {"line": 140, "type": "if", "locations": [{"start": {"line": 140, "column": 59}, "end": {"line": 140, "column": 59}}, {"start": {"line": 140, "column": 59}, "end": {"line": 140, "column": 59}}]}, "14": {"line": 143, "type": "if", "locations": [{"start": {"line": 143, "column": 8}, "end": {"line": 143, "column": 8}}, {"start": {"line": 143, "column": 8}, "end": {"line": 143, "column": 8}}]}, "15": {"line": 150, "type": "if", "locations": [{"start": {"line": 150, "column": 12}, "end": {"line": 150, "column": 12}}, {"start": {"line": 150, "column": 12}, "end": {"line": 150, "column": 12}}]}, "16": {"line": 151, "type": "if", "locations": [{"start": {"line": 151, "column": 12}, "end": {"line": 151, "column": 12}}, {"start": {"line": 151, "column": 12}, "end": {"line": 151, "column": 12}}]}, "17": {"line": 165, "type": "if", "locations": [{"start": {"line": 165, "column": 39}, "end": {"line": 165, "column": 39}}, {"start": {"line": 165, "column": 39}, "end": {"line": 165, "column": 39}}]}, "18": {"line": 166, "type": "if", "locations": [{"start": {"line": 166, "column": 8}, "end": {"line": 166, "column": 8}}, {"start": {"line": 166, "column": 8}, "end": {"line": 166, "column": 8}}]}, "19": {"line": 175, "type": "if", "locations": [{"start": {"line": 175, "column": 32}, "end": {"line": 175, "column": 32}}, {"start": {"line": 175, "column": 32}, "end": {"line": 175, "column": 32}}]}, "20": {"line": 176, "type": "if", "locations": [{"start": {"line": 176, "column": 8}, "end": {"line": 176, "column": 8}}, {"start": {"line": 176, "column": 8}, "end": {"line": 176, "column": 8}}]}, "21": {"line": 185, "type": "if", "locations": [{"start": {"line": 185, "column": 8}, "end": {"line": 185, "column": 8}}, {"start": {"line": 185, "column": 8}, "end": {"line": 185, "column": 8}}]}, "22": {"line": 186, "type": "if", "locations": [{"start": {"line": 186, "column": 8}, "end": {"line": 186, "column": 8}}, {"start": {"line": 186, "column": 8}, "end": {"line": 186, "column": 8}}]}, "23": {"line": 187, "type": "if", "locations": [{"start": {"line": 187, "column": 8}, "end": {"line": 187, "column": 8}}, {"start": {"line": 187, "column": 8}, "end": {"line": 187, "column": 8}}]}, "24": {"line": 196, "type": "if", "locations": [{"start": {"line": 196, "column": 49}, "end": {"line": 196, "column": 49}}, {"start": {"line": 196, "column": 49}, "end": {"line": 196, "column": 49}}]}, "25": {"line": 203, "type": "if", "locations": [{"start": {"line": 203, "column": 66}, "end": {"line": 203, "column": 66}}, {"start": {"line": 203, "column": 66}, "end": {"line": 203, "column": 66}}]}, "26": {"line": 210, "type": "if", "locations": [{"start": {"line": 210, "column": 84}, "end": {"line": 210, "column": 84}}, {"start": {"line": 210, "column": 84}, "end": {"line": 210, "column": 84}}]}, "27": {"line": 227, "type": "if", "locations": [{"start": {"line": 227, "column": 8}, "end": {"line": 227, "column": 8}}, {"start": {"line": 227, "column": 8}, "end": {"line": 227, "column": 8}}]}, "28": {"line": 229, "type": "if", "locations": [{"start": {"line": 229, "column": 55}, "end": {"line": 229, "column": 82}}, {"start": {"line": 229, "column": 86}, "end": {"line": 229, "column": 86}}]}, "29": {"line": 234, "type": "if", "locations": [{"start": {"line": 234, "column": 33}, "end": {"line": 234, "column": 64}}, {"start": {"line": 234, "column": 68}, "end": {"line": 234, "column": 68}}]}}}, "contracts/HAOXVestingV2Minimal.sol": {"l": {"131": 38, "132": 37, "133": 37, "134": 37, "136": 37, "137": 37, "138": 37, "139": 37, "142": 37, "145": 37, "153": 37, "163": 37, "164": 37, "172": 22, "174": 22, "175": 0, "179": 22, "180": 22, "181": 22, "184": 22, "185": 22, "190": 22, "192": 22, "193": 19, "194": 11, "195": 11, "196": 11, "198": 8, "199": 8, "200": 7, "204": 3, "205": 1, "206": 1, "207": 1, "216": 7, "217": 7, "218": 7, "221": 7, "222": 7, "225": 7, "226": 7, "228": 6, "231": 6, "238": 22, "241": 22, "242": 22, "249": 4, "250": 3, "252": 2, "254": 2, "261": 2, "268": 1, "269": 1, "270": 1, "271": 1, "272": 1, "274": 1, "275": 1, "277": 1, "290": 0, "291": 0, "293": 0, "294": 0, "295": 0, "296": 0, "298": 0, "299": 0, "300": 0, "315": 0, "316": 0, "318": 0, "331": 0, "338": 4, "342": 2, "346": 8, "350": 2, "357": 0, "358": 0}, "path": "/Users/<USER>/Desktop/sociomint222/contracts/contracts/HAOXVestingV2Minimal.sol", "s": {"1": 38, "2": 37, "3": 37, "4": 37, "5": 37, "6": 37, "7": 37, "8": 22, "9": 22, "10": 0, "11": 22, "12": 22, "13": 22, "14": 22, "15": 22, "16": 19, "17": 11, "18": 8, "19": 8, "20": 7, "21": 3, "22": 1, "23": 7, "24": 7, "25": 7, "26": 7, "27": 7, "28": 6, "29": 22, "30": 22, "31": 22, "32": 4, "33": 3, "34": 2, "35": 2, "36": 1, "37": 1, "38": 1, "39": 1, "40": 1, "41": 1, "42": 1, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 8, "52": 2, "53": 0, "54": 0}, "b": {"1": [37, 1], "2": [37, 0], "3": [37, 0], "4": [37, 0], "5": [24, 0], "6": [22, 2], "7": [0, 22], "8": [19, 3], "9": [11, 8], "10": [7, 1], "11": [1, 2], "12": [7, 0], "13": [6, 1], "14": [22, 0], "15": [4, 0], "16": [3, 1], "17": [2, 1], "18": [1, 0], "19": [1, 0], "20": [1, 0], "21": [1, 0], "22": [1, 0], "23": [0, 0], "24": [0, 0], "25": [0, 0], "26": [0, 0], "27": [4, 1], "28": [2, 0], "29": [8, 1], "30": [2, 0], "31": [0, 0]}, "f": {"1": 38, "2": 37, "3": 22, "4": 7, "5": 22, "6": 4, "7": 1, "8": 0, "9": 0, "10": 0, "11": 4, "12": 2, "13": 8, "14": 2, "15": 0}, "fnMap": {"1": {"name": "constructor", "line": 130, "loc": {"start": {"line": 125, "column": 4}, "end": {"line": 146, "column": 4}}}, "2": {"name": "_initializeRounds", "line": 151, "loc": {"start": {"line": 151, "column": 4}, "end": {"line": 166, "column": 4}}}, "3": {"name": "checkPriceCondition", "line": 171, "loc": {"start": {"line": 171, "column": 4}, "end": {"line": 210, "column": 4}}}, "4": {"name": "_unlockRound", "line": 215, "loc": {"start": {"line": 215, "column": 4}, "end": {"line": 232, "column": 4}}}, "5": {"name": "_getCurrentPrice", "line": 237, "loc": {"start": {"line": 237, "column": 4}, "end": {"line": 243, "column": 4}}}, "6": {"name": "requestEmergencyWithdraw", "line": 248, "loc": {"start": {"line": 248, "column": 4}, "end": {"line": 262, "column": 4}}}, "7": {"name": "executeEmergencyWithdraw", "line": 267, "loc": {"start": {"line": 267, "column": 4}, "end": {"line": 278, "column": 4}}}, "8": {"name": "getUnlockProgress", "line": 283, "loc": {"start": {"line": 283, "column": 4}, "end": {"line": 303, "column": 4}}}, "9": {"name": "getRoundInfo", "line": 308, "loc": {"start": {"line": 308, "column": 4}, "end": {"line": 325, "column": 4}}}, "10": {"name": "getPriceHistory", "line": 330, "loc": {"start": {"line": 330, "column": 4}, "end": {"line": 332, "column": 4}}}, "11": {"name": "addEmergencySigner", "line": 337, "loc": {"start": {"line": 337, "column": 4}, "end": {"line": 339, "column": 4}}}, "12": {"name": "remove<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "line": 341, "loc": {"start": {"line": 341, "column": 4}, "end": {"line": 343, "column": 4}}}, "13": {"name": "pause", "line": 345, "loc": {"start": {"line": 345, "column": 4}, "end": {"line": 347, "column": 4}}}, "14": {"name": "unpause", "line": 349, "loc": {"start": {"line": 349, "column": 4}, "end": {"line": 351, "column": 4}}}, "15": {"name": "emergencyPause", "line": 356, "loc": {"start": {"line": 356, "column": 4}, "end": {"line": 359, "column": 4}}}}, "statementMap": {"1": {"start": {"line": 131, "column": 8}, "end": {"line": 131, "column": 65}}, "2": {"start": {"line": 132, "column": 8}, "end": {"line": 132, "column": 68}}, "3": {"start": {"line": 133, "column": 8}, "end": {"line": 133, "column": 70}}, "4": {"start": {"line": 134, "column": 8}, "end": {"line": 134, "column": 74}}, "5": {"start": {"line": 145, "column": 8}, "end": {"line": 145, "column": 26}}, "6": {"start": {"line": 153, "column": 8}, "end": {"line": 153, "column": 4521}}, "7": {"start": {"line": 163, "column": 8}, "end": {"line": 163, "column": 5381}}, "8": {"start": {"line": 172, "column": 8}, "end": {"line": 172, "column": 42}}, "9": {"start": {"line": 174, "column": 8}, "end": {"line": 174, "column": 5663}}, "10": {"start": {"line": 175, "column": 12}, "end": {"line": 175, "column": 18}}, "11": {"start": {"line": 179, "column": 8}, "end": {"line": 179, "column": 49}}, "12": {"start": {"line": 180, "column": 8}, "end": {"line": 180, "column": 49}}, "13": {"start": {"line": 181, "column": 8}, "end": {"line": 181, "column": 62}}, "14": {"start": {"line": 184, "column": 8}, "end": {"line": 184, "column": 47}}, "15": {"start": {"line": 192, "column": 8}, "end": {"line": 192, "column": 6270}}, "16": {"start": {"line": 193, "column": 12}, "end": {"line": 193, "column": 6306}}, "17": {"start": {"line": 196, "column": 16}, "end": {"line": 196, "column": 82}}, "18": {"start": {"line": 198, "column": 16}, "end": {"line": 198, "column": 85}}, "19": {"start": {"line": 199, "column": 16}, "end": {"line": 199, "column": 6664}}, "20": {"start": {"line": 200, "column": 20}, "end": {"line": 200, "column": 58}}, "21": {"start": {"line": 204, "column": 12}, "end": {"line": 204, "column": 6854}}, "22": {"start": {"line": 207, "column": 16}, "end": {"line": 207, "column": 84}}, "23": {"start": {"line": 216, "column": 8}, "end": {"line": 216, "column": 49}}, "24": {"start": {"line": 221, "column": 8}, "end": {"line": 221, "column": 49}}, "25": {"start": {"line": 222, "column": 8}, "end": {"line": 222, "column": 51}}, "26": {"start": {"line": 225, "column": 8}, "end": {"line": 225, "column": 93}}, "27": {"start": {"line": 226, "column": 8}, "end": {"line": 226, "column": 99}}, "28": {"start": {"line": 228, "column": 8}, "end": {"line": 228, "column": 102}}, "29": {"start": {"line": 238, "column": 8}, "end": {"line": 238, "column": 8050}}, "30": {"start": {"line": 241, "column": 8}, "end": {"line": 241, "column": 67}}, "31": {"start": {"line": 242, "column": 8}, "end": {"line": 242, "column": 42}}, "32": {"start": {"line": 249, "column": 8}, "end": {"line": 249, "column": 62}}, "33": {"start": {"line": 250, "column": 8}, "end": {"line": 250, "column": 66}}, "34": {"start": {"line": 252, "column": 8}, "end": {"line": 252, "column": 92}}, "35": {"start": {"line": 261, "column": 8}, "end": {"line": 261, "column": 75}}, "36": {"start": {"line": 268, "column": 8}, "end": {"line": 268, "column": 71}}, "37": {"start": {"line": 269, "column": 8}, "end": {"line": 269, "column": 60}}, "38": {"start": {"line": 270, "column": 8}, "end": {"line": 270, "column": 53}}, "39": {"start": {"line": 271, "column": 8}, "end": {"line": 271, "column": 92}}, "40": {"start": {"line": 272, "column": 8}, "end": {"line": 272, "column": 62}}, "41": {"start": {"line": 275, "column": 8}, "end": {"line": 275, "column": 79}}, "42": {"start": {"line": 277, "column": 8}, "end": {"line": 277, "column": 65}}, "43": {"start": {"line": 293, "column": 8}, "end": {"line": 293, "column": 9982}}, "44": {"start": {"line": 294, "column": 12}, "end": {"line": 294, "column": 50}}, "45": {"start": {"line": 298, "column": 12}, "end": {"line": 298, "column": 10194}}, "46": {"start": {"line": 299, "column": 16}, "end": {"line": 299, "column": 74}}, "47": {"start": {"line": 315, "column": 8}, "end": {"line": 315, "column": 79}}, "48": {"start": {"line": 316, "column": 8}, "end": {"line": 316, "column": 48}}, "49": {"start": {"line": 318, "column": 8}, "end": {"line": 318, "column": 10865}}, "50": {"start": {"line": 331, "column": 8}, "end": {"line": 331, "column": 40}}, "51": {"start": {"line": 346, "column": 8}, "end": {"line": 346, "column": 15}}, "52": {"start": {"line": 350, "column": 8}, "end": {"line": 350, "column": 17}}, "53": {"start": {"line": 357, "column": 8}, "end": {"line": 357, "column": 62}}, "54": {"start": {"line": 358, "column": 8}, "end": {"line": 358, "column": 15}}}, "branchMap": {"1": {"line": 131, "type": "if", "locations": [{"start": {"line": 131, "column": 8}, "end": {"line": 131, "column": 8}}, {"start": {"line": 131, "column": 8}, "end": {"line": 131, "column": 8}}]}, "2": {"line": 132, "type": "if", "locations": [{"start": {"line": 132, "column": 8}, "end": {"line": 132, "column": 8}}, {"start": {"line": 132, "column": 8}, "end": {"line": 132, "column": 8}}]}, "3": {"line": 133, "type": "if", "locations": [{"start": {"line": 133, "column": 8}, "end": {"line": 133, "column": 8}}, {"start": {"line": 133, "column": 8}, "end": {"line": 133, "column": 8}}]}, "4": {"line": 134, "type": "if", "locations": [{"start": {"line": 134, "column": 8}, "end": {"line": 134, "column": 8}}, {"start": {"line": 134, "column": 8}, "end": {"line": 134, "column": 8}}]}, "5": {"line": 171, "type": "if", "locations": [{"start": {"line": 171, "column": 44}, "end": {"line": 171, "column": 44}}, {"start": {"line": 171, "column": 44}, "end": {"line": 171, "column": 44}}]}, "6": {"line": 171, "type": "if", "locations": [{"start": {"line": 171, "column": 57}, "end": {"line": 171, "column": 57}}, {"start": {"line": 171, "column": 57}, "end": {"line": 171, "column": 57}}]}, "7": {"line": 174, "type": "if", "locations": [{"start": {"line": 174, "column": 8}, "end": {"line": 174, "column": 8}}, {"start": {"line": 174, "column": 8}, "end": {"line": 174, "column": 8}}]}, "8": {"line": 192, "type": "if", "locations": [{"start": {"line": 192, "column": 8}, "end": {"line": 192, "column": 8}}, {"start": {"line": 192, "column": 8}, "end": {"line": 192, "column": 8}}]}, "9": {"line": 193, "type": "if", "locations": [{"start": {"line": 193, "column": 12}, "end": {"line": 193, "column": 12}}, {"start": {"line": 193, "column": 12}, "end": {"line": 193, "column": 12}}]}, "10": {"line": 199, "type": "if", "locations": [{"start": {"line": 199, "column": 16}, "end": {"line": 199, "column": 16}}, {"start": {"line": 199, "column": 16}, "end": {"line": 199, "column": 16}}]}, "11": {"line": 204, "type": "if", "locations": [{"start": {"line": 204, "column": 12}, "end": {"line": 204, "column": 12}}, {"start": {"line": 204, "column": 12}, "end": {"line": 204, "column": 12}}]}, "12": {"line": 225, "type": "if", "locations": [{"start": {"line": 225, "column": 8}, "end": {"line": 225, "column": 8}}, {"start": {"line": 225, "column": 8}, "end": {"line": 225, "column": 8}}]}, "13": {"line": 226, "type": "if", "locations": [{"start": {"line": 226, "column": 8}, "end": {"line": 226, "column": 8}}, {"start": {"line": 226, "column": 8}, "end": {"line": 226, "column": 8}}]}, "14": {"line": 241, "type": "if", "locations": [{"start": {"line": 241, "column": 8}, "end": {"line": 241, "column": 8}}, {"start": {"line": 241, "column": 8}, "end": {"line": 241, "column": 8}}]}, "15": {"line": 248, "type": "if", "locations": [{"start": {"line": 248, "column": 63}, "end": {"line": 248, "column": 63}}, {"start": {"line": 248, "column": 63}, "end": {"line": 248, "column": 63}}]}, "16": {"line": 249, "type": "if", "locations": [{"start": {"line": 249, "column": 8}, "end": {"line": 249, "column": 8}}, {"start": {"line": 249, "column": 8}, "end": {"line": 249, "column": 8}}]}, "17": {"line": 250, "type": "if", "locations": [{"start": {"line": 250, "column": 8}, "end": {"line": 250, "column": 8}}, {"start": {"line": 250, "column": 8}, "end": {"line": 250, "column": 8}}]}, "18": {"line": 269, "type": "if", "locations": [{"start": {"line": 269, "column": 8}, "end": {"line": 269, "column": 8}}, {"start": {"line": 269, "column": 8}, "end": {"line": 269, "column": 8}}]}, "19": {"line": 270, "type": "if", "locations": [{"start": {"line": 270, "column": 8}, "end": {"line": 270, "column": 8}}, {"start": {"line": 270, "column": 8}, "end": {"line": 270, "column": 8}}]}, "20": {"line": 271, "type": "if", "locations": [{"start": {"line": 271, "column": 8}, "end": {"line": 271, "column": 8}}, {"start": {"line": 271, "column": 8}, "end": {"line": 271, "column": 8}}]}, "21": {"line": 272, "type": "if", "locations": [{"start": {"line": 272, "column": 8}, "end": {"line": 272, "column": 8}}, {"start": {"line": 272, "column": 8}, "end": {"line": 272, "column": 8}}]}, "22": {"line": 275, "type": "if", "locations": [{"start": {"line": 275, "column": 8}, "end": {"line": 275, "column": 8}}, {"start": {"line": 275, "column": 8}, "end": {"line": 275, "column": 8}}]}, "23": {"line": 293, "type": "if", "locations": [{"start": {"line": 293, "column": 8}, "end": {"line": 293, "column": 8}}, {"start": {"line": 293, "column": 8}, "end": {"line": 293, "column": 8}}]}, "24": {"line": 298, "type": "if", "locations": [{"start": {"line": 298, "column": 12}, "end": {"line": 298, "column": 12}}, {"start": {"line": 298, "column": 12}, "end": {"line": 298, "column": 12}}]}, "25": {"line": 300, "type": "if", "locations": [{"start": {"line": 300, "column": 69}, "end": {"line": 300, "column": 69}}, {"start": {"line": 300, "column": 73}, "end": {"line": 300, "column": 105}}]}, "26": {"line": 315, "type": "if", "locations": [{"start": {"line": 315, "column": 8}, "end": {"line": 315, "column": 8}}, {"start": {"line": 315, "column": 8}, "end": {"line": 315, "column": 8}}]}, "27": {"line": 337, "type": "if", "locations": [{"start": {"line": 337, "column": 57}, "end": {"line": 337, "column": 57}}, {"start": {"line": 337, "column": 57}, "end": {"line": 337, "column": 57}}]}, "28": {"line": 341, "type": "if", "locations": [{"start": {"line": 341, "column": 60}, "end": {"line": 341, "column": 60}}, {"start": {"line": 341, "column": 60}, "end": {"line": 341, "column": 60}}]}, "29": {"line": 345, "type": "if", "locations": [{"start": {"line": 345, "column": 30}, "end": {"line": 345, "column": 30}}, {"start": {"line": 345, "column": 30}, "end": {"line": 345, "column": 30}}]}, "30": {"line": 349, "type": "if", "locations": [{"start": {"line": 349, "column": 32}, "end": {"line": 349, "column": 32}}, {"start": {"line": 349, "column": 32}, "end": {"line": 349, "column": 32}}]}, "31": {"line": 357, "type": "if", "locations": [{"start": {"line": 357, "column": 8}, "end": {"line": 357, "column": 8}}, {"start": {"line": 357, "column": 8}, "end": {"line": 357, "column": 8}}]}}}, "contracts/HAOXVestingV2Ultra.sol": {"l": {"64": 38, "65": 38, "66": 38, "67": 38, "69": 38, "70": 38, "71": 38, "72": 38, "74": 38, "77": 38, "84": 38, "90": 38, "91": 38, "104": 31, "105": 31, "107": 31, "108": 31, "110": 31, "111": 31, "113": 31, "114": 12, "115": 12, "116": 12, "118": 2, "119": 2, "123": 31, "125": 5, "133": 5, "134": 5, "136": 5, "138": 5, "139": 5, "147": 31, "150": 31, "151": 31, "161": 7, "163": 6, "167": 6, "173": 6, "174": 6, "184": 4, "186": 4, "187": 4, "188": 3, "193": 2, "195": 2, "196": 2, "198": 0, "201": 2, "208": 0, "215": 0, "216": 0, "223": 8, "227": 0, "239": 0, "240": 0, "256": 0, "257": 0, "258": 0, "261": 0, "268": 0}, "path": "/Users/<USER>/Desktop/sociomint222/contracts/contracts/HAOXVestingV2Ultra.sol", "s": {"1": 38, "2": 38, "3": 38, "4": 38, "5": 38, "6": 38, "7": 38, "8": 31, "9": 31, "10": 0, "11": 31, "12": 31, "13": 0, "14": 31, "15": 31, "16": 31, "17": 12, "18": 19, "19": 31, "20": 5, "21": 5, "22": 5, "23": 5, "24": 31, "25": 31, "26": 31, "27": 7, "28": 6, "29": 6, "30": 6, "31": 4, "32": 4, "33": 4, "34": 3, "35": 2, "36": 2, "37": 0, "38": 2, "39": 0, "40": 8, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0}, "b": {"1": [38, 0], "2": [38, 0], "3": [38, 0], "4": [38, 0], "5": [31, 1], "6": [0, 31], "7": [0, 31], "8": [12, 19], "9": [2, 17], "10": [5, 26], "11": [5, 0], "12": [31, 0], "13": [7, 1], "14": [6, 1], "15": [4, 0], "16": [4, 0], "17": [3, 1], "18": [2, 1], "19": [2, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0], "23": [8, 0], "24": [0, 0], "25": [0, 0]}, "f": {"1": 38, "2": 38, "3": 31, "4": 5, "5": 31, "6": 7, "7": 4, "8": 0, "9": 0, "10": 8, "11": 0, "12": 0, "13": 0, "14": 0}, "fnMap": {"1": {"name": "constructor", "line": 63, "loc": {"start": {"line": 58, "column": 4}, "end": {"line": 78, "column": 4}}}, "2": {"name": "_initializeRounds", "line": 83, "loc": {"start": {"line": 83, "column": 4}, "end": {"line": 98, "column": 4}}}, "3": {"name": "checkPriceCondition", "line": 103, "loc": {"start": {"line": 103, "column": 4}, "end": {"line": 127, "column": 4}}}, "4": {"name": "_unlockRound", "line": 132, "loc": {"start": {"line": 132, "column": 4}, "end": {"line": 141, "column": 4}}}, "5": {"name": "_getCurrentPrice", "line": 146, "loc": {"start": {"line": 146, "column": 4}, "end": {"line": 152, "column": 4}}}, "6": {"name": "requestEmergencyWithdraw", "line": 160, "loc": {"start": {"line": 157, "column": 4}, "end": {"line": 175, "column": 4}}}, "7": {"name": "executeEmergencyWithdraw", "line": 183, "loc": {"start": {"line": 180, "column": 4}, "end": {"line": 202, "column": 4}}}, "8": {"name": "setEmergency<PERSON><PERSON>er", "line": 207, "loc": {"start": {"line": 207, "column": 4}, "end": {"line": 209, "column": 4}}}, "9": {"name": "setRequiredSignatures", "line": 214, "loc": {"start": {"line": 214, "column": 4}, "end": {"line": 217, "column": 4}}}, "10": {"name": "pause", "line": 222, "loc": {"start": {"line": 222, "column": 4}, "end": {"line": 224, "column": 4}}}, "11": {"name": "unpause", "line": 226, "loc": {"start": {"line": 226, "column": 4}, "end": {"line": 228, "column": 4}}}, "12": {"name": "getRoundInfo", "line": 233, "loc": {"start": {"line": 233, "column": 4}, "end": {"line": 246, "column": 4}}}, "13": {"name": "getUnlockProgress", "line": 251, "loc": {"start": {"line": 251, "column": 4}, "end": {"line": 262, "column": 4}}}, "14": {"name": "getCurrentPrice", "line": 267, "loc": {"start": {"line": 267, "column": 4}, "end": {"line": 269, "column": 4}}}}, "statementMap": {"1": {"start": {"line": 64, "column": 8}, "end": {"line": 64, "column": 65}}, "2": {"start": {"line": 65, "column": 8}, "end": {"line": 65, "column": 68}}, "3": {"start": {"line": 66, "column": 8}, "end": {"line": 66, "column": 70}}, "4": {"start": {"line": 67, "column": 8}, "end": {"line": 67, "column": 74}}, "5": {"start": {"line": 77, "column": 8}, "end": {"line": 77, "column": 26}}, "6": {"start": {"line": 84, "column": 8}, "end": {"line": 84, "column": 2728}}, "7": {"start": {"line": 90, "column": 8}, "end": {"line": 90, "column": 3077}}, "8": {"start": {"line": 104, "column": 8}, "end": {"line": 104, "column": 42}}, "9": {"start": {"line": 105, "column": 8}, "end": {"line": 105, "column": 46}}, "10": {"start": {"line": 105, "column": 40}, "end": {"line": 105, "column": 46}}, "11": {"start": {"line": 107, "column": 8}, "end": {"line": 107, "column": 49}}, "12": {"start": {"line": 108, "column": 8}, "end": {"line": 108, "column": 34}}, "13": {"start": {"line": 108, "column": 28}, "end": {"line": 108, "column": 34}}, "14": {"start": {"line": 110, "column": 8}, "end": {"line": 110, "column": 49}}, "15": {"start": {"line": 111, "column": 8}, "end": {"line": 111, "column": 62}}, "16": {"start": {"line": 113, "column": 8}, "end": {"line": 113, "column": 3786}}, "17": {"start": {"line": 116, "column": 12}, "end": {"line": 116, "column": 61}}, "18": {"start": {"line": 117, "column": 15}, "end": {"line": 117, "column": 4018}}, "19": {"start": {"line": 123, "column": 8}, "end": {"line": 123, "column": 4198}}, "20": {"start": {"line": 125, "column": 12}, "end": {"line": 125, "column": 36}}, "21": {"start": {"line": 133, "column": 8}, "end": {"line": 133, "column": 49}}, "22": {"start": {"line": 136, "column": 8}, "end": {"line": 136, "column": 56}}, "23": {"start": {"line": 138, "column": 8}, "end": {"line": 138, "column": 4630}}, "24": {"start": {"line": 147, "column": 8}, "end": {"line": 147, "column": 4839}}, "25": {"start": {"line": 150, "column": 8}, "end": {"line": 150, "column": 72}}, "26": {"start": {"line": 151, "column": 8}, "end": {"line": 151, "column": 42}}, "27": {"start": {"line": 161, "column": 8}, "end": {"line": 161, "column": 72}}, "28": {"start": {"line": 163, "column": 8}, "end": {"line": 163, "column": 5354}}, "29": {"start": {"line": 173, "column": 8}, "end": {"line": 173, "column": 58}}, "30": {"start": {"line": 174, "column": 8}, "end": {"line": 174, "column": 24}}, "31": {"start": {"line": 184, "column": 8}, "end": {"line": 184, "column": 69}}, "32": {"start": {"line": 186, "column": 8}, "end": {"line": 186, "column": 71}}, "33": {"start": {"line": 187, "column": 8}, "end": {"line": 187, "column": 53}}, "34": {"start": {"line": 188, "column": 8}, "end": {"line": 188, "column": 6138}}, "35": {"start": {"line": 195, "column": 8}, "end": {"line": 195, "column": 6323}}, "36": {"start": {"line": 196, "column": 12}, "end": {"line": 196, "column": 60}}, "37": {"start": {"line": 198, "column": 12}, "end": {"line": 198, "column": 64}}, "38": {"start": {"line": 201, "column": 8}, "end": {"line": 201, "column": 65}}, "39": {"start": {"line": 215, "column": 8}, "end": {"line": 215, "column": 56}}, "40": {"start": {"line": 223, "column": 8}, "end": {"line": 223, "column": 15}}, "41": {"start": {"line": 227, "column": 8}, "end": {"line": 227, "column": 17}}, "42": {"start": {"line": 239, "column": 8}, "end": {"line": 239, "column": 48}}, "43": {"start": {"line": 240, "column": 8}, "end": {"line": 240, "column": 7469}}, "44": {"start": {"line": 256, "column": 8}, "end": {"line": 256, "column": 28}}, "45": {"start": {"line": 257, "column": 8}, "end": {"line": 257, "column": 7865}}, "46": {"start": {"line": 258, "column": 12}, "end": {"line": 258, "column": 46}}, "47": {"start": {"line": 261, "column": 8}, "end": {"line": 261, "column": 53}}, "48": {"start": {"line": 268, "column": 8}, "end": {"line": 268, "column": 33}}}, "branchMap": {"1": {"line": 64, "type": "if", "locations": [{"start": {"line": 64, "column": 8}, "end": {"line": 64, "column": 8}}, {"start": {"line": 64, "column": 8}, "end": {"line": 64, "column": 8}}]}, "2": {"line": 65, "type": "if", "locations": [{"start": {"line": 65, "column": 8}, "end": {"line": 65, "column": 8}}, {"start": {"line": 65, "column": 8}, "end": {"line": 65, "column": 8}}]}, "3": {"line": 66, "type": "if", "locations": [{"start": {"line": 66, "column": 8}, "end": {"line": 66, "column": 8}}, {"start": {"line": 66, "column": 8}, "end": {"line": 66, "column": 8}}]}, "4": {"line": 67, "type": "if", "locations": [{"start": {"line": 67, "column": 8}, "end": {"line": 67, "column": 8}}, {"start": {"line": 67, "column": 8}, "end": {"line": 67, "column": 8}}]}, "5": {"line": 103, "type": "if", "locations": [{"start": {"line": 103, "column": 44}, "end": {"line": 103, "column": 44}}, {"start": {"line": 103, "column": 44}, "end": {"line": 103, "column": 44}}]}, "6": {"line": 105, "type": "if", "locations": [{"start": {"line": 105, "column": 8}, "end": {"line": 105, "column": 8}}, {"start": {"line": 105, "column": 8}, "end": {"line": 105, "column": 8}}]}, "7": {"line": 108, "type": "if", "locations": [{"start": {"line": 108, "column": 8}, "end": {"line": 108, "column": 8}}, {"start": {"line": 108, "column": 8}, "end": {"line": 108, "column": 8}}]}, "8": {"line": 113, "type": "if", "locations": [{"start": {"line": 113, "column": 8}, "end": {"line": 113, "column": 8}}, {"start": {"line": 113, "column": 8}, "end": {"line": 113, "column": 8}}]}, "9": {"line": 117, "type": "if", "locations": [{"start": {"line": 117, "column": 15}, "end": {"line": 117, "column": 15}}, {"start": {"line": 117, "column": 15}, "end": {"line": 117, "column": 15}}]}, "10": {"line": 123, "type": "if", "locations": [{"start": {"line": 123, "column": 8}, "end": {"line": 123, "column": 8}}, {"start": {"line": 123, "column": 8}, "end": {"line": 123, "column": 8}}]}, "11": {"line": 138, "type": "if", "locations": [{"start": {"line": 138, "column": 8}, "end": {"line": 138, "column": 8}}, {"start": {"line": 138, "column": 8}, "end": {"line": 138, "column": 8}}]}, "12": {"line": 150, "type": "if", "locations": [{"start": {"line": 150, "column": 8}, "end": {"line": 150, "column": 8}}, {"start": {"line": 150, "column": 8}, "end": {"line": 150, "column": 8}}]}, "13": {"line": 160, "type": "if", "locations": [{"start": {"line": 160, "column": 15}, "end": {"line": 160, "column": 15}}, {"start": {"line": 160, "column": 15}, "end": {"line": 160, "column": 15}}]}, "14": {"line": 161, "type": "if", "locations": [{"start": {"line": 161, "column": 8}, "end": {"line": 161, "column": 8}}, {"start": {"line": 161, "column": 8}, "end": {"line": 161, "column": 8}}]}, "15": {"line": 183, "type": "if", "locations": [{"start": {"line": 183, "column": 15}, "end": {"line": 183, "column": 15}}, {"start": {"line": 183, "column": 15}, "end": {"line": 183, "column": 15}}]}, "16": {"line": 184, "type": "if", "locations": [{"start": {"line": 184, "column": 8}, "end": {"line": 184, "column": 8}}, {"start": {"line": 184, "column": 8}, "end": {"line": 184, "column": 8}}]}, "17": {"line": 187, "type": "if", "locations": [{"start": {"line": 187, "column": 8}, "end": {"line": 187, "column": 8}}, {"start": {"line": 187, "column": 8}, "end": {"line": 187, "column": 8}}]}, "18": {"line": 188, "type": "if", "locations": [{"start": {"line": 188, "column": 8}, "end": {"line": 188, "column": 8}}, {"start": {"line": 188, "column": 8}, "end": {"line": 188, "column": 8}}]}, "19": {"line": 195, "type": "if", "locations": [{"start": {"line": 195, "column": 8}, "end": {"line": 195, "column": 8}}, {"start": {"line": 195, "column": 8}, "end": {"line": 195, "column": 8}}]}, "20": {"line": 207, "type": "if", "locations": [{"start": {"line": 207, "column": 70}, "end": {"line": 207, "column": 70}}, {"start": {"line": 207, "column": 70}, "end": {"line": 207, "column": 70}}]}, "21": {"line": 214, "type": "if", "locations": [{"start": {"line": 214, "column": 63}, "end": {"line": 214, "column": 63}}, {"start": {"line": 214, "column": 63}, "end": {"line": 214, "column": 63}}]}, "22": {"line": 215, "type": "if", "locations": [{"start": {"line": 215, "column": 8}, "end": {"line": 215, "column": 8}}, {"start": {"line": 215, "column": 8}, "end": {"line": 215, "column": 8}}]}, "23": {"line": 222, "type": "if", "locations": [{"start": {"line": 222, "column": 30}, "end": {"line": 222, "column": 30}}, {"start": {"line": 222, "column": 30}, "end": {"line": 222, "column": 30}}]}, "24": {"line": 226, "type": "if", "locations": [{"start": {"line": 226, "column": 32}, "end": {"line": 226, "column": 32}}, {"start": {"line": 226, "column": 32}, "end": {"line": 226, "column": 32}}]}, "25": {"line": 258, "type": "if", "locations": [{"start": {"line": 258, "column": 12}, "end": {"line": 258, "column": 12}}, {"start": {"line": 258, "column": 12}, "end": {"line": 258, "column": 12}}]}}}, "contracts/MockPriceOracle.sol": {"l": {"16": 264, "17": 264, "25": 63, "33": 86, "34": 86, "42": 0, "43": 0, "44": 0, "52": 0, "59": 0}, "path": "/Users/<USER>/Desktop/sociomint222/contracts/contracts/MockPriceOracle.sol", "s": {"1": 63, "2": 86, "3": 0, "4": 0}, "b": {}, "f": {"1": 264, "2": 63, "3": 86, "4": 0, "5": 0, "6": 0}, "fnMap": {"1": {"name": "constructor", "line": 15, "loc": {"start": {"line": 15, "column": 4}, "end": {"line": 18, "column": 4}}}, "2": {"name": "getLatestPrice", "line": 24, "loc": {"start": {"line": 24, "column": 4}, "end": {"line": 26, "column": 4}}}, "3": {"name": "setPrice", "line": 32, "loc": {"start": {"line": 32, "column": 4}, "end": {"line": 35, "column": 4}}}, "4": {"name": "activateEmergencyMode", "line": 41, "loc": {"start": {"line": 41, "column": 4}, "end": {"line": 45, "column": 4}}}, "5": {"name": "isEmergencyMode", "line": 51, "loc": {"start": {"line": 51, "column": 4}, "end": {"line": 53, "column": 4}}}, "6": {"name": "deactivateEmergencyMode", "line": 58, "loc": {"start": {"line": 58, "column": 4}, "end": {"line": 60, "column": 4}}}}, "statementMap": {"1": {"start": {"line": 25, "column": 8}, "end": {"line": 25, "column": 21}}, "2": {"start": {"line": 34, "column": 8}, "end": {"line": 34, "column": 35}}, "3": {"start": {"line": 44, "column": 8}, "end": {"line": 44, "column": 47}}, "4": {"start": {"line": 52, "column": 8}, "end": {"line": 52, "column": 29}}}, "branchMap": {}}}