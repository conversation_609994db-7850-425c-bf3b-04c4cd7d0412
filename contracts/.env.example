# Private key for deployment (without 0x prefix)
PRIVATE_KEY=your_private_key_here

# BSC Scan API key for contract verification
BSCSCAN_API_KEY=your_bscscan_api_key_here

# CoinMarketCap API key for gas reporting
COINMARKETCAP_API_KEY=your_coinmarketcap_api_key_here

# Chainlink price feed addresses
CHAINLINK_BNB_USD_FEED=******************************************
CHAINLINK_ETH_USD_FEED=******************************************

# Multisig wallet addresses
MULTISIG_WALLET=******************************************
TREASURY_WALLET=******************************************
TEAM_WALLET=******************************************

# Contract deployment settings
INITIAL_SUPPLY=5000000000000000000000000000  # 5 billion tokens with 18 decimals
PRESALE_SUPPLY=180000000000000000000000000   # 180 million tokens
INVITATION_SUPPLY=20000000000000000000000000  # 20 million tokens

# Presale settings
PRESALE_TARGET_BNB=300000000000000000000       # 300 BNB
MIN_INVESTMENT=100000000000000000              # 0.1 BNB
MAX_INVESTMENT=20000000000000000000            # 20 BNB
INITIAL_RATE=2697000                           # 1 BNB = 2,697,000 HAOX
FINAL_RATE=379500                              # 1 BNB = 379,500 HAOX

# Backend API endpoints
BACKEND_API_URL=http://localhost:3001/api
WEBHOOK_SECRET=your_webhook_secret_here

# Database connection
SUPABASE_URL=your_supabase_url_here
SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_KEY=your_supabase_service_key_here
