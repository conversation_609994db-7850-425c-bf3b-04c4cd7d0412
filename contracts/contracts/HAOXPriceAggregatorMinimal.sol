// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";

/**
 * @title HAOXPriceAggregatorMinimal
 * @dev 精简版价格聚合器 - 成本优化版本
 * 保留核心价格聚合功能，移除复杂特性以降低部署成本
 */
contract HAOXPriceAggregatorMinimal is Ownable, Pausable {
    
    // 精简的价格源结构
    struct PriceSource {
        address oracle;
        uint8 weight;
        bool active;
        uint32 lastUpdate;
    }
    
    // 聚合配置常量
    uint256 public constant MAX_PRICE_DEVIATION = 500; // 5%
    uint256 public constant PRICE_STALENESS_THRESHOLD = 3600; // 1小时
    uint256 public constant MIN_SOURCES_REQUIRED = 2;
    uint256 public constant MAX_SOURCES = 5; // 减少到5个源
    
    // 状态变量
    PriceSource[MAX_SOURCES] public priceSources;
    uint8 public sourceCount;
    
    // 最新价格数据
    uint256 public latestPrice;
    uint256 public lastUpdateTime;
    uint8 public lastSourceCount;
    
    // 紧急模式
    uint256 public emergencyPrice;
    uint256 public emergencyPriceTimestamp;
    bool public emergencyMode;
    
    // 事件定义（精简版）
    event PriceAggregated(uint256 price, uint256 timestamp, uint8 sourceCount);
    event PriceSourceAdded(uint8 indexed sourceId, address indexed oracle, uint8 weight);
    event PriceSourceUpdated(uint8 indexed sourceId, bool active, uint8 weight);
    event EmergencyModeActivated(uint256 price, uint256 timestamp);
    event EmergencyModeDeactivated();

    /**
     * @dev 构造函数
     */
    constructor() Ownable(msg.sender) {
        emergencyMode = false;
    }

    /**
     * @dev 添加价格源（精简版）
     */
    function addPriceSource(address oracle, uint8 weight) external onlyOwner {
        require(oracle != address(0), "Invalid oracle");
        require(weight > 0 && weight <= 100, "Invalid weight");
        require(sourceCount < MAX_SOURCES, "Too many sources");
        
        // 检查重复
        for (uint8 i = 0; i < sourceCount; i++) {
            require(priceSources[i].oracle != oracle, "Oracle exists");
        }
        
        priceSources[sourceCount] = PriceSource({
            oracle: oracle,
            weight: weight,
            active: true,
            lastUpdate: 0
        });
        
        emit PriceSourceAdded(sourceCount, oracle, weight);
        sourceCount++;
    }

    /**
     * @dev 更新价格源状态
     */
    function updatePriceSource(uint8 sourceId, bool active, uint8 weight) external onlyOwner {
        require(sourceId < sourceCount, "Invalid source");
        require(weight > 0 && weight <= 100, "Invalid weight");
        
        priceSources[sourceId].active = active;
        priceSources[sourceId].weight = weight;
        
        emit PriceSourceUpdated(sourceId, active, weight);
    }

    /**
     * @dev 获取最新价格
     */
    function getLatestPrice() external view returns (uint256) {
        if (emergencyMode) {
            require(
                block.timestamp - emergencyPriceTimestamp <= PRICE_STALENESS_THRESHOLD * 2,
                "Emergency price stale"
            );
            return emergencyPrice;
        }
        
        require(lastUpdateTime > 0, "No price available");
        require(
            block.timestamp - lastUpdateTime <= PRICE_STALENESS_THRESHOLD,
            "Price stale"
        );
        
        return latestPrice;
    }

    /**
     * @dev 获取最后更新时间
     */
    function getLastUpdateTime() external view returns (uint256) {
        return emergencyMode ? emergencyPriceTimestamp : lastUpdateTime;
    }

    /**
     * @dev 更新聚合价格（精简版）
     */
    function updateAggregatedPrice() external whenNotPaused {
        require(!emergencyMode, "Emergency mode active");
        
        uint256 totalWeight = 0;
        uint256 weightedSum = 0;
        uint8 validSources = 0;
        
        // 收集有效价格
        for (uint8 i = 0; i < sourceCount; i++) {
            if (!priceSources[i].active) continue;

            // 使用低级调用替代try-catch
            (bool success, uint256 price) = _tryGetPriceFromSource(i);
            if (!success) continue;

            // 基本价格验证
            if (price == 0 || price > 1e18) continue;

            weightedSum += price * priceSources[i].weight;
            totalWeight += priceSources[i].weight;
            validSources++;

            priceSources[i].lastUpdate = uint32(block.timestamp);
        }
        
        require(validSources >= MIN_SOURCES_REQUIRED, "Insufficient sources");
        
        uint256 aggregatedPrice = weightedSum / totalWeight;
        
        // 简化的偏差检测
        _validatePriceDeviation(aggregatedPrice);
        
        latestPrice = aggregatedPrice;
        lastUpdateTime = block.timestamp;
        lastSourceCount = validSources;
        
        emit PriceAggregated(aggregatedPrice, block.timestamp, validSources);
    }

    /**
     * @dev 从价格源获取价格
     */
    function _getPriceFromSource(uint8 sourceId) internal view returns (uint256) {
        address oracle = priceSources[sourceId].oracle;

        (bool success, bytes memory data) = oracle.staticcall(
            abi.encodeWithSignature("getLatestPrice()")
        );

        require(success && data.length >= 32, "Oracle call failed");
        return abi.decode(data, (uint256));
    }

    /**
     * @dev 尝试从价格源获取价格（安全版本）
     */
    function _tryGetPriceFromSource(uint8 sourceId) internal view returns (bool success, uint256 price) {
        address oracle = priceSources[sourceId].oracle;

        (bool callSuccess, bytes memory data) = oracle.staticcall(
            abi.encodeWithSignature("getLatestPrice()")
        );

        if (callSuccess && data.length >= 32) {
            price = abi.decode(data, (uint256));
            success = true;
        } else {
            success = false;
            price = 0;
        }
    }

    /**
     * @dev 验证价格偏差（简化版）
     */
    function _validatePriceDeviation(uint256 newPrice) internal view {
        if (latestPrice == 0) return;
        
        uint256 deviation = newPrice > latestPrice
            ? (newPrice - latestPrice) * 10000 / latestPrice
            : (latestPrice - newPrice) * 10000 / latestPrice;
            
        require(deviation <= MAX_PRICE_DEVIATION * 3, "Price deviation too high");
    }

    /**
     * @dev 激活紧急模式
     */
    function activateEmergencyMode(uint256 price) external onlyOwner {
        require(price > 0, "Invalid price");
        
        emergencyMode = true;
        emergencyPrice = price;
        emergencyPriceTimestamp = block.timestamp;
        
        emit EmergencyModeActivated(price, block.timestamp);
    }

    /**
     * @dev 停用紧急模式
     */
    function deactivateEmergencyMode() external onlyOwner {
        require(emergencyMode, "Not in emergency mode");
        
        uint8 activeCount = 0;
        for (uint8 i = 0; i < sourceCount; i++) {
            if (priceSources[i].active) activeCount++;
        }
        require(activeCount >= MIN_SOURCES_REQUIRED, "Insufficient sources");
        
        emergencyMode = false;
        emergencyPrice = 0;
        emergencyPriceTimestamp = 0;
        
        emit EmergencyModeDeactivated();
    }

    /**
     * @dev 获取聚合器状态
     */
    function getAggregatorStatus() external view returns (
        uint8 totalSources,
        uint8 activeSources,
        bool isEmergencyMode,
        uint256 lastUpdate,
        uint256 currentPrice
    ) {
        uint8 active = 0;
        for (uint8 i = 0; i < sourceCount; i++) {
            if (priceSources[i].active) active++;
        }
        
        return (
            sourceCount,
            active,
            emergencyMode,
            lastUpdateTime,
            latestPrice
        );
    }

    /**
     * @dev 获取价格源信息
     */
    function getPriceSource(uint8 sourceId) external view returns (
        address oracle,
        uint8 weight,
        bool active,
        uint32 lastUpdate
    ) {
        require(sourceId < sourceCount, "Invalid source");
        PriceSource memory source = priceSources[sourceId];
        
        return (source.oracle, source.weight, source.active, source.lastUpdate);
    }

    /**
     * @dev 批量更新价格源状态
     */
    function batchUpdateSources(uint8[] calldata sourceIds, bool[] calldata activeStates) external onlyOwner {
        require(sourceIds.length == activeStates.length, "Length mismatch");
        
        for (uint256 i = 0; i < sourceIds.length; i++) {
            if (sourceIds[i] < sourceCount) {
                priceSources[sourceIds[i]].active = activeStates[i];
                emit PriceSourceUpdated(sourceIds[i], activeStates[i], priceSources[sourceIds[i]].weight);
            }
        }
    }

    /**
     * @dev 暂停/恢复合约
     */
    function pause() external onlyOwner {
        _pause();
    }

    function unpause() external onlyOwner {
        _unpause();
    }

    /**
     * @dev 紧急暂停
     */
    function emergencyPause() external {
        // 简化权限检查
        require(msg.sender == owner(), "Not authorized");
        _pause();
    }
}
