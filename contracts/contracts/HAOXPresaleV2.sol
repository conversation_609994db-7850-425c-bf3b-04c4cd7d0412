// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "./HAOXTokenV2.sol";
import "./HAOXInvitationV2.sol";
import "./HAOXPriceOracleV2.sol";

/**
 * @title HAOXPresaleV2
 * @dev 重构的预售合约，支持跨阶段购买和精确的动态定价
 */
contract HAOXPresaleV2 is ReentrancyGuard, Pausable, Ownable {

    HAOXTokenV2 public immutable haoxToken;
    HAOXInvitationV2 public invitationContract;
    HAOXPriceOracleV2 public priceOracle;

    // 预售参数
    uint256 public constant TOTAL_PRESALE_TOKENS = 200_000_000 * 10**18; // 2亿HAOX
    uint256 public constant TARGET_BNB = 320 * 10**18; // 320 BNB
    uint256 public constant MIN_INVESTMENT = 0.1 * 10**18; // 0.1 BNB
    uint256 public constant MAX_INVESTMENT = 20 * 10**18; // 20 BNB
    uint256 public constant TOTAL_STAGES = 100;
    uint256 public constant TOKENS_PER_STAGE = TOTAL_PRESALE_TOKENS / TOTAL_STAGES; // 200万HAOX每阶段

    // 动态定价参数
    uint256 public constant INITIAL_RATE = 1_912_125; // 初始汇率：1,912,125 HAOX/BNB
    uint256 public constant RATE_DECREASE_PERCENT = 98; // 每阶段汇率降低到98%

    // 状态变量
    uint256 public currentStage;
    uint256 public totalBNBRaised;
    uint256 public totalTokensSold;
    uint256 public tokensRemainingInCurrentStage;
    bool public presaleActive;
    
    // 用户投资记录
    mapping(address => uint256) public investments;
    mapping(address => uint256) public tokensPurchased;
    mapping(address => bool) public whitelist;
    
    // 事件
    event TokensPurchased(
        address indexed buyer,
        uint256 bnbAmount,
        uint256 tokenAmount,
        uint256 avgRate,
        uint256 startStage,
        uint256 endStage
    );
    event StageCompleted(uint256 stage, uint256 totalTokensSold);
    event PresaleEnded(uint256 totalBNBRaised, uint256 totalTokensSold);
    
    constructor(
        address _haoxToken,
        address _invitationContract,
        address _priceOracle
    ) Ownable(msg.sender) {
        require(_haoxToken != address(0), "Invalid token address");
        require(_invitationContract != address(0), "Invalid invitation address");
        require(_priceOracle != address(0), "Invalid oracle address");

        haoxToken = HAOXTokenV2(_haoxToken);
        invitationContract = HAOXInvitationV2(_invitationContract);
        priceOracle = HAOXPriceOracleV2(_priceOracle);

        currentStage = 0;
        tokensRemainingInCurrentStage = TOKENS_PER_STAGE;
        presaleActive = true;
    }
    
    // 预计算的汇率表（Gas优化）
    mapping(uint256 => uint256) private _stageRates;
    bool private _ratesInitialized;

    /**
     * @dev 初始化汇率表（仅调用一次）
     */
    function initializeRates() external onlyOwner {
        require(!_ratesInitialized, "Rates already initialized");

        uint256 rate = INITIAL_RATE;
        _stageRates[0] = rate;

        for (uint256 i = 1; i < TOTAL_STAGES; i++) {
            rate = (rate * RATE_DECREASE_PERCENT) / 100;
            _stageRates[i] = rate;
        }

        _ratesInitialized = true;
    }

    /**
     * @dev 获取指定阶段的汇率（优化版本）
     */
    function getStageRate(uint256 stage) public view returns (uint256) {
        require(stage < TOTAL_STAGES, "Invalid stage");

        if (_ratesInitialized) {
            return _stageRates[stage];
        } else {
            // 回退到实时计算
            uint256 rate = INITIAL_RATE;
            for (uint256 i = 0; i < stage; i++) {
                rate = (rate * RATE_DECREASE_PERCENT) / 100;
            }
            return rate;
        }
    }
    
    /**
     * @dev 跨阶段购买的核心算法
     * @param bnbAmount 投入的BNB数量
     * @return tokenAmount 获得的代币数量
     * @return avgRate 平均汇率
     * @return endStage 结束阶段
     */
    function calculateCrossStageTokens(uint256 bnbAmount) 
        public 
        view 
        returns (uint256 tokenAmount, uint256 avgRate, uint256 endStage) 
    {
        uint256 remainingBNB = bnbAmount;
        uint256 totalTokens = 0;
        uint256 stage = currentStage;
        uint256 stageTokensRemaining = tokensRemainingInCurrentStage;
        
        while (remainingBNB > 0 && stage < TOTAL_STAGES) {
            uint256 stageRate = getStageRate(stage);
            uint256 maxTokensFromBNB = remainingBNB * stageRate / 1e18;
            
            if (maxTokensFromBNB <= stageTokensRemaining) {
                // 当前阶段可以满足剩余需求
                totalTokens += maxTokensFromBNB;
                remainingBNB = 0;
            } else {
                // 需要进入下一阶段
                uint256 bnbForThisStage = (stageTokensRemaining * 1e18) / stageRate;
                totalTokens += stageTokensRemaining;
                remainingBNB -= bnbForThisStage;
                
                // 进入下一阶段
                stage++;
                stageTokensRemaining = TOKENS_PER_STAGE;
            }
        }
        
        require(remainingBNB == 0, "Insufficient tokens available");
        
        // 计算平均汇率
        avgRate = (totalTokens * 1e18) / bnbAmount;
        endStage = stage;
        tokenAmount = totalTokens;
    }
    
    /**
     * @dev 购买代币（支持跨阶段和邀请）
     */
    function buyTokens(address inviter) external payable nonReentrant whenNotPaused {
        require(presaleActive, "Presale not active");
        require(whitelist[msg.sender], "Not whitelisted");
        require(msg.value >= MIN_INVESTMENT, "Below minimum investment");
        require(
            investments[msg.sender] + msg.value <= MAX_INVESTMENT,
            "Exceeds maximum investment"
        );
        require(
            totalBNBRaised + msg.value <= TARGET_BNB,
            "Exceeds target BNB"
        );
        
        uint256 startStage = currentStage;
        
        // 计算跨阶段购买
        (uint256 tokenAmount, uint256 avgRate, uint256 endStage) = 
            calculateCrossStageTokens(msg.value);
        
        require(tokenAmount > 0, "Invalid token amount");
        require(
            totalTokensSold + tokenAmount <= TOTAL_PRESALE_TOKENS,
            "Exceeds total presale tokens"
        );
        
        // 更新状态
        _updateStageProgress(msg.value, tokenAmount, endStage);
        
        // 更新用户记录
        investments[msg.sender] += msg.value;
        tokensPurchased[msg.sender] += tokenAmount;

        // 处理邀请奖励
        if (inviter != address(0) && inviter != msg.sender) {
            try invitationContract.recordInvitation(inviter, msg.sender, msg.value) {
                // 邀请记录成功
            } catch {
                // 邀请记录失败，不影响购买
            }
        }

        emit TokensPurchased(
            msg.sender,
            msg.value,
            tokenAmount,
            avgRate,
            startStage,
            endStage
        );

        // 检查是否达到目标
        if (totalBNBRaised >= TARGET_BNB || totalTokensSold >= TOTAL_PRESALE_TOKENS) {
            presaleActive = false;
            // 通知邀请合约预售结束
            try invitationContract.endPresale() {
                // 预售结束通知成功
            } catch {
                // 通知失败，不影响预售结束
            }
            emit PresaleEnded(totalBNBRaised, totalTokensSold);
        }
    }
    
    /**
     * @dev 更新阶段进度（内部函数）
     */
    function _updateStageProgress(
        uint256 bnbAmount,
        uint256 tokenAmount,
        uint256 endStage
    ) internal {
        uint256 remainingBNB = bnbAmount;
        uint256 remainingTokens = tokenAmount;
        
        while (remainingTokens > 0 && currentStage < TOTAL_STAGES) {
            if (remainingTokens <= tokensRemainingInCurrentStage) {
                // 当前阶段可以满足
                tokensRemainingInCurrentStage -= remainingTokens;
                remainingTokens = 0;
            } else {
                // 完成当前阶段，进入下一阶段
                remainingTokens -= tokensRemainingInCurrentStage;
                
                emit StageCompleted(currentStage, totalTokensSold + (tokenAmount - remainingTokens));
                
                currentStage++;
                tokensRemainingInCurrentStage = TOKENS_PER_STAGE;
            }
        }
        
        // 更新总量
        totalBNBRaised += bnbAmount;
        totalTokensSold += tokenAmount;
    }
    
    /**
     * @dev 获取当前汇率
     */
    function getCurrentRate() external view returns (uint256) {
        return getStageRate(currentStage);
    }
    
    /**
     * @dev 批量添加白名单
     */
    function addToWhitelist(address[] calldata users) external onlyOwner {
        for (uint256 i = 0; i < users.length; i++) {
            whitelist[users[i]] = true;
        }
    }
    
    /**
     * @dev 紧急暂停
     */
    function pause() external onlyOwner {
        _pause();
    }
    
    /**
     * @dev 恢复运行
     */
    function unpause() external onlyOwner {
        _unpause();
    }
    
    /**
     * @dev 提取BNB
     */
    function withdrawBNB() external onlyOwner {
        require(!presaleActive, "Presale still active");
        payable(owner()).transfer(address(this).balance);
    }
    
    /**
     * @dev 获取预售状态
     */
    function getPresaleStatus() external view returns (
        uint256 _currentStage,
        uint256 _tokensRemainingInCurrentStage,
        uint256 _totalBNBRaised,
        uint256 _totalTokensSold,
        uint256 _currentRate,
        bool _isActive
    ) {
        return (
            currentStage,
            tokensRemainingInCurrentStage,
            totalBNBRaised,
            totalTokensSold,
            getStageRate(currentStage),
            presaleActive
        );
    }
}
