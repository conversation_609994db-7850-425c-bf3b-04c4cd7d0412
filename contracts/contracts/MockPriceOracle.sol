// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

/**
 * @title MockPriceOracle
 * @notice 用于测试的模拟价格预言机
 */
contract MockPriceOracle {
    uint256 private _price;
    bool private _emergencyMode;
    
    event PriceUpdated(uint256 newPrice);
    event EmergencyModeActivated(uint256 fixedPrice);
    
    constructor() {
        _price = 1e16; // 默认0.01 USD
        _emergencyMode = false;
    }
    
    /**
     * @notice 获取最新价格
     * @return price 当前价格（18位小数）
     */
    function getLatestPrice() external view returns (uint256 price) {
        return _price;
    }
    
    /**
     * @notice 设置价格（仅用于测试）
     * @param newPrice 新价格
     */
    function setPrice(uint256 newPrice) external {
        _price = newPrice;
        emit PriceUpdated(newPrice);
    }
    
    /**
     * @notice 激活紧急模式并设置固定价格
     * @param fixedPrice 固定价格
     */
    function activateEmergencyMode(uint256 fixedPrice) external {
        _emergencyMode = true;
        _price = fixedPrice;
        emit EmergencyModeActivated(fixedPrice);
    }
    
    /**
     * @notice 检查是否处于紧急模式
     * @return 是否处于紧急模式
     */
    function isEmergencyMode() external view returns (bool) {
        return _emergencyMode;
    }
    
    /**
     * @notice 退出紧急模式
     */
    function deactivateEmergencyMode() external {
        _emergencyMode = false;
    }
}
