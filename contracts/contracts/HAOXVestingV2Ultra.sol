// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";

/**
 * @title HAOXVestingV2Ultra
 * @dev 超精简版HAOX代币解锁合约 - 极致成本优化版本
 * 仅保留最核心的安全功能，最大化降低部署成本
 */
contract HAOXVestingV2Ultra is Ownable, ReentrancyGuard, Pausable {
    
    // 基础常量
    uint256 public constant TOTAL_ROUNDS = 31;
    uint256 public constant PRICE_MAINTAIN_DURATION = 7 days;
    uint256 public constant EMERGENCY_DELAY = 7 days;
    uint256 public constant MAX_EMERGENCY_AMOUNT = 1000000 * 10**18;
    
    // 核心状态变量
    IERC20 public immutable haoxToken;
    address public immutable priceOracle;
    address public immutable projectWallet;
    address public immutable communityWallet;
    
    uint256 public currentRound = 1;
    
    // 轮次信息结构（超紧凑存储）
    struct Round {
        uint128 triggerPrice;      // 触发价格 (8位小数)
        uint64 priceReachedTime;   // 价格达到时间
        bool priceConditionMet;    // 价格条件是否满足
        bool unlocked;             // 是否已解锁
    }
    
    // 紧急提取请求结构（超精简版）
    struct EmergencyRequest {
        uint128 amount;
        uint64 requestTime;
        bool executed;
    }
    
    // 状态映射（移除历史记录）
    mapping(uint256 => Round) public rounds;
    mapping(bytes32 => EmergencyRequest) public emergencyRequests;
    mapping(address => bool) public emergencySigners;
    
    uint256 public requiredSignatures = 1;
    
    // 事件定义（超精简版）
    event PriceConditionMet(uint256 indexed roundNumber, uint256 price);
    event RoundUnlocked(uint256 indexed roundNumber, uint256 timestamp);
    event EmergencyWithdrawRequested(bytes32 indexed requestId, uint256 amount);
    event EmergencyWithdrawExecuted(bytes32 indexed requestId, uint256 amount);
    
    constructor(
        address _haoxToken,
        address _priceOracle,
        address _projectWallet,
        address _communityWallet
    ) Ownable(msg.sender) {
        require(_haoxToken != address(0), "Invalid token address");
        require(_priceOracle != address(0), "Invalid oracle address");
        require(_projectWallet != address(0), "Invalid project wallet");
        require(_communityWallet != address(0), "Invalid community wallet");
        
        haoxToken = IERC20(_haoxToken);
        priceOracle = _priceOracle;
        projectWallet = _projectWallet;
        communityWallet = _communityWallet;
        
        emergencySigners[msg.sender] = true;
        
        // 初始化31轮价格阶梯
        _initializeRounds();
    }
    
    /**
     * @dev 初始化31轮价格阶梯
     */
    function _initializeRounds() internal {
        uint256[31] memory prices = [
            uint256(0.01e8), 0.02e8, 0.03e8, 0.04e8, 0.05e8, 0.06e8, 0.07e8, 0.08e8, 0.09e8, 0.10e8,
            0.11e8, 0.12e8, 0.13e8, 0.14e8, 0.15e8, 0.16e8, 0.17e8, 0.18e8, 0.19e8, 0.20e8,
            0.25e8, 0.30e8, 0.35e8, 0.40e8, 0.45e8, 0.50e8, 0.60e8, 0.70e8, 0.80e8, 0.90e8, 1.00e8
        ];
        
        for (uint256 i = 0; i < TOTAL_ROUNDS; i++) {
            rounds[i + 1] = Round({
                triggerPrice: uint128(prices[i]),
                priceReachedTime: 0,
                priceConditionMet: false,
                unlocked: false
            });
        }
    }
    
    /**
     * @dev 检查价格条件（超精简版）
     */
    function checkPriceCondition() external whenNotPaused {
        uint256 roundNumber = currentRound;
        if (roundNumber > TOTAL_ROUNDS) return;
        
        Round storage round = rounds[roundNumber];
        if (round.unlocked) return;
        
        uint256 currentPrice = _getCurrentPrice();
        bool conditionMet = currentPrice >= round.triggerPrice;
        
        if (conditionMet && !round.priceConditionMet) {
            round.priceConditionMet = true;
            round.priceReachedTime = uint64(block.timestamp);
            emit PriceConditionMet(roundNumber, currentPrice);
        } else if (!conditionMet && round.priceConditionMet) {
            round.priceConditionMet = false;
            round.priceReachedTime = 0;
        }
        
        // 检查是否可以解锁
        if (round.priceConditionMet && 
            block.timestamp >= round.priceReachedTime + PRICE_MAINTAIN_DURATION) {
            _unlockRound(roundNumber);
        }
    }
    
    /**
     * @dev 解锁轮次
     */
    function _unlockRound(uint256 roundNumber) internal {
        Round storage round = rounds[roundNumber];
        round.unlocked = true;
        
        emit RoundUnlocked(roundNumber, block.timestamp);
        
        if (roundNumber < TOTAL_ROUNDS) {
            currentRound = roundNumber + 1;
        }
    }
    
    /**
     * @dev 获取当前价格
     */
    function _getCurrentPrice() internal view returns (uint256) {
        (bool success, bytes memory data) = priceOracle.staticcall(
            abi.encodeWithSignature("getLatestPrice()")
        );
        require(success && data.length >= 32, "Price oracle call failed");
        return abi.decode(data, (uint256));
    }
    
    /**
     * @dev 紧急提取请求
     */
    function requestEmergencyWithdraw(
        address token,
        uint256 amount
    ) external onlyOwner returns (bytes32) {
        require(amount <= MAX_EMERGENCY_AMOUNT, "Amount exceeds maximum");
        
        bytes32 requestId = keccak256(abi.encodePacked(
            token, amount, block.timestamp, msg.sender
        ));
        
        emergencyRequests[requestId] = EmergencyRequest({
            amount: uint128(amount),
            requestTime: uint64(block.timestamp),
            executed: false
        });
        
        emit EmergencyWithdrawRequested(requestId, amount);
        return requestId;
    }
    
    /**
     * @dev 执行紧急提取
     */
    function executeEmergencyWithdraw(
        bytes32 requestId,
        address token
    ) external nonReentrant {
        require(emergencySigners[msg.sender], "Not authorized signer");
        
        EmergencyRequest storage request = emergencyRequests[requestId];
        require(!request.executed, "Already executed");
        require(
            block.timestamp >= request.requestTime + EMERGENCY_DELAY,
            "Time lock not expired"
        );
        
        request.executed = true;
        
        if (token == address(haoxToken)) {
            haoxToken.transfer(projectWallet, request.amount);
        } else {
            IERC20(token).transfer(projectWallet, request.amount);
        }
        
        emit EmergencyWithdrawExecuted(requestId, request.amount);
    }
    
    /**
     * @dev 管理紧急签名者
     */
    function setEmergencySigner(address signer, bool status) external onlyOwner {
        emergencySigners[signer] = status;
    }
    
    /**
     * @dev 设置所需签名数量
     */
    function setRequiredSignatures(uint256 _required) external onlyOwner {
        require(_required > 0, "Invalid signature count");
        requiredSignatures = _required;
    }
    
    /**
     * @dev 暂停/恢复合约
     */
    function pause() external onlyOwner {
        _pause();
    }
    
    function unpause() external onlyOwner {
        _unpause();
    }
    
    /**
     * @dev 获取轮次信息
     */
    function getRoundInfo(uint256 roundNumber) external view returns (
        uint256 triggerPrice,
        bool priceConditionMet,
        bool unlocked,
        uint256 priceReachedTime
    ) {
        Round memory round = rounds[roundNumber];
        return (
            round.triggerPrice,
            round.priceConditionMet,
            round.unlocked,
            round.priceReachedTime
        );
    }
    
    /**
     * @dev 获取解锁进度
     */
    function getUnlockProgress() external view returns (
        uint256 totalRounds,
        uint256 currentRoundNumber,
        uint256 unlockedRounds
    ) {
        uint256 unlocked = 0;
        for (uint256 i = 1; i <= TOTAL_ROUNDS; i++) {
            if (rounds[i].unlocked) unlocked++;
        }
        
        return (TOTAL_ROUNDS, currentRound, unlocked);
    }
    
    /**
     * @dev 获取当前价格（外部调用）
     */
    function getCurrentPrice() external view returns (uint256) {
        return _getCurrentPrice();
    }
}
