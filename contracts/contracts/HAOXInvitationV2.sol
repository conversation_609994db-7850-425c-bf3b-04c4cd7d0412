// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "./HAOXTokenV2.sol";

/**
 * @title HAOXInvitationV2
 * @dev 重构的邀请奖励合约
 * 
 * 奖励规则：
 * - 基础奖励：每成功邀请1人参与预售(≥0.1BNB) = 1,000 HAOX
 * - 里程碑奖励：5人 = 10,000 HAOX，10人 = 50,000 HAOX
 * - 排行榜奖励：预售结束后根据排名发放
 * - 总奖励池：300,000,000 HAOX
 */
contract HAOXInvitationV2 is ReentrancyGuard, Pausable, Ownable {
    
    HAOXTokenV2 public immutable haoxToken;
    
    // 奖励参数
    uint256 public constant BASE_REWARD = 1_000 * 10**18; // 1,000 HAOX per invitation
    uint256 public constant MILESTONE_5_REWARD = 10_000 * 10**18; // 10,000 HAOX at 5 invitations
    uint256 public constant MILESTONE_10_REWARD = 50_000 * 10**18; // 50,000 HAOX at 10 invitations
    uint256 public constant TOTAL_REWARD_POOL = 300_000_000 * 10**18; // 300M HAOX total
    uint256 public constant MIN_PURCHASE_AMOUNT = 0.1 * 10**18; // 0.1 BNB minimum
    
    // 排行榜奖励池
    uint256 public constant LEADERBOARD_POOL = 2_700_000 * 10**18; // 2.7M HAOX
    uint256[20] public LEADERBOARD_REWARDS = [
        1_000_000 * 10**18, // 1st place
        500_000 * 10**18,   // 2nd place
        200_000 * 10**18,   // 3rd place
        100_000 * 10**18,   // 4th-10th place (7 positions)
        100_000 * 10**18,
        100_000 * 10**18,
        100_000 * 10**18,
        100_000 * 10**18,
        100_000 * 10**18,
        100_000 * 10**18,
        50_000 * 10**18,    // 11th-20th place (10 positions)
        50_000 * 10**18,
        50_000 * 10**18,
        50_000 * 10**18,
        50_000 * 10**18,
        50_000 * 10**18,
        50_000 * 10**18,
        50_000 * 10**18,
        50_000 * 10**18,
        50_000 * 10**18
    ];
    
    // 状态变量
    uint256 public totalRewardsDistributed;
    bool public presaleEnded;
    bool public leaderboardFinalized;
    
    // 用户数据结构
    struct UserStats {
        uint256 successfulInvitations;
        uint256 totalRewards;
        uint256 claimableRewards;
        bool milestone5Claimed;
        bool milestone10Claimed;
        uint256 leaderboardReward;
        bool leaderboardClaimed;
    }
    
    // 邀请记录结构
    struct InvitationRecord {
        address inviter;
        address invitee;
        uint256 purchaseAmount;
        uint256 timestamp;
        bool rewardClaimed;
    }
    
    // 映射
    mapping(address => UserStats) public userStats;
    mapping(address => address) public inviterOf; // invitee => inviter
    mapping(address => address[]) public inviteesOf; // inviter => invitees[]
    mapping(bytes32 => InvitationRecord) public invitationRecords;
    
    // 排行榜
    address[] public leaderboard;
    mapping(address => uint256) public leaderboardPosition; // 1-based, 0 means not in top 20
    
    // 事件
    event InvitationRecorded(
        address indexed inviter,
        address indexed invitee,
        uint256 purchaseAmount,
        uint256 baseReward
    );
    event MilestoneReached(address indexed user, uint256 milestone, uint256 reward);
    event RewardClaimed(address indexed user, uint256 amount);
    event LeaderboardFinalized(address[] topUsers);
    event LeaderboardRewardClaimed(address indexed user, uint256 position, uint256 reward);
    
    constructor(address _haoxToken) Ownable(msg.sender) {
        require(_haoxToken != address(0), "Invalid token address");
        haoxToken = HAOXTokenV2(_haoxToken);
    }
    
    /**
     * @dev 记录邀请关系和购买（由预售合约调用）
     */
    function recordInvitation(
        address inviter,
        address invitee,
        uint256 purchaseAmount
    ) external {
        require(msg.sender == address(haoxToken.presaleContract()), "Only presale contract");
        require(!presaleEnded, "Presale ended");
        require(purchaseAmount >= MIN_PURCHASE_AMOUNT, "Purchase below minimum");
        require(inviter != invitee, "Cannot invite self");
        require(inviterOf[invitee] == address(0), "Already has inviter");
        
        // 记录邀请关系
        inviterOf[invitee] = inviter;
        inviteesOf[inviter].push(invitee);
        
        // 创建邀请记录
        bytes32 recordId = keccak256(abi.encodePacked(inviter, invitee, block.timestamp));
        invitationRecords[recordId] = InvitationRecord({
            inviter: inviter,
            invitee: invitee,
            purchaseAmount: purchaseAmount,
            timestamp: block.timestamp,
            rewardClaimed: false
        });
        
        // 更新邀请者统计
        UserStats storage stats = userStats[inviter];
        stats.successfulInvitations++;
        
        // 计算基础奖励
        uint256 baseReward = BASE_REWARD;
        stats.claimableRewards += baseReward;
        
        // 检查里程碑奖励
        if (stats.successfulInvitations == 5 && !stats.milestone5Claimed) {
            stats.claimableRewards += MILESTONE_5_REWARD;
            stats.milestone5Claimed = true;
            emit MilestoneReached(inviter, 5, MILESTONE_5_REWARD);
        }
        
        if (stats.successfulInvitations == 10 && !stats.milestone10Claimed) {
            stats.claimableRewards += MILESTONE_10_REWARD;
            stats.milestone10Claimed = true;
            emit MilestoneReached(inviter, 10, MILESTONE_10_REWARD);
        }
        
        emit InvitationRecorded(inviter, invitee, purchaseAmount, baseReward);
    }
    
    /**
     * @dev 用户主动领取奖励
     */
    function claimRewards() external nonReentrant whenNotPaused {
        UserStats storage stats = userStats[msg.sender];
        uint256 claimableAmount = stats.claimableRewards;
        
        require(claimableAmount > 0, "No rewards to claim");
        require(presaleEnded, "Presale not ended yet");
        require(
            totalRewardsDistributed + claimableAmount <= TOTAL_REWARD_POOL,
            "Exceeds reward pool"
        );
        
        // 更新状态
        stats.claimableRewards = 0;
        stats.totalRewards += claimableAmount;
        totalRewardsDistributed += claimableAmount;
        
        // 转账代币
        require(haoxToken.transfer(msg.sender, claimableAmount), "Transfer failed");
        
        emit RewardClaimed(msg.sender, claimableAmount);
    }
    
    /**
     * @dev 领取排行榜奖励
     */
    function claimLeaderboardReward() external nonReentrant whenNotPaused {
        require(presaleEnded, "Presale not ended");
        require(leaderboardFinalized, "Leaderboard not finalized");
        
        UserStats storage stats = userStats[msg.sender];
        require(stats.leaderboardReward > 0, "No leaderboard reward");
        require(!stats.leaderboardClaimed, "Already claimed");
        
        uint256 reward = stats.leaderboardReward;
        stats.leaderboardClaimed = true;
        stats.totalRewards += reward;
        totalRewardsDistributed += reward;
        
        require(haoxToken.transfer(msg.sender, reward), "Transfer failed");
        
        uint256 position = leaderboardPosition[msg.sender];
        emit LeaderboardRewardClaimed(msg.sender, position, reward);
    }

    /**
     * @dev 预售结束，管理员调用
     */
    function endPresale() external onlyOwner {
        require(!presaleEnded, "Already ended");
        presaleEnded = true;
    }

    /**
     * @dev 生成并确定排行榜（管理员调用）
     */
    function finalizeLeaderboard() external onlyOwner {
        require(presaleEnded, "Presale not ended");
        require(!leaderboardFinalized, "Already finalized");

        // 获取所有有邀请记录的用户
        address[] memory allUsers = _getAllInviters();

        // 按邀请数量排序（冒泡排序，适合小数据集）
        for (uint256 i = 0; i < allUsers.length; i++) {
            for (uint256 j = i + 1; j < allUsers.length; j++) {
                if (userStats[allUsers[i]].successfulInvitations < userStats[allUsers[j]].successfulInvitations) {
                    address temp = allUsers[i];
                    allUsers[i] = allUsers[j];
                    allUsers[j] = temp;
                }
            }
        }

        // 设置前20名的排行榜奖励
        uint256 topCount = allUsers.length > 20 ? 20 : allUsers.length;
        leaderboard = new address[](topCount);

        for (uint256 i = 0; i < topCount; i++) {
            address user = allUsers[i];
            leaderboard[i] = user;
            leaderboardPosition[user] = i + 1; // 1-based position
            userStats[user].leaderboardReward = LEADERBOARD_REWARDS[i];
        }

        leaderboardFinalized = true;
        emit LeaderboardFinalized(leaderboard);
    }

    /**
     * @dev 获取所有邀请者地址（内部函数）
     */
    function _getAllInviters() internal view returns (address[] memory) {
        // 这里简化实现，实际应该维护一个邀请者列表
        // 为了演示，返回一个固定大小的数组
        address[] memory inviters = new address[](100); // 假设最多100个邀请者
        uint256 count = 0;

        // 注意：这里需要根据实际情况实现
        // 可以通过事件日志或维护邀请者列表来获取

        // 创建一个临时数组返回实际数量
        address[] memory result = new address[](count);
        for (uint256 i = 0; i < count; i++) {
            result[i] = inviters[i];
        }

        return result;
    }

    /**
     * @dev 获取用户邀请统计
     */
    function getUserStats(address user) external view returns (
        uint256 successfulInvitations,
        uint256 totalRewards,
        uint256 claimableRewards,
        bool milestone5Claimed,
        bool milestone10Claimed,
        uint256 leaderboardReward,
        bool leaderboardClaimed
    ) {
        UserStats memory stats = userStats[user];
        return (
            stats.successfulInvitations,
            stats.totalRewards,
            stats.claimableRewards,
            stats.milestone5Claimed,
            stats.milestone10Claimed,
            stats.leaderboardReward,
            stats.leaderboardClaimed
        );
    }

    /**
     * @dev 获取用户邀请的所有人
     */
    function getUserInvitees(address user) external view returns (address[] memory) {
        return inviteesOf[user];
    }

    /**
     * @dev 获取排行榜
     */
    function getLeaderboard() external view returns (address[] memory) {
        return leaderboard;
    }

    /**
     * @dev 紧急暂停
     */
    function pause() external onlyOwner {
        _pause();
    }

    /**
     * @dev 恢复运行
     */
    function unpause() external onlyOwner {
        _unpause();
    }

    /**
     * @dev 紧急提取代币（仅限管理员）
     */
    function emergencyWithdraw(uint256 amount) external onlyOwner {
        require(haoxToken.transfer(owner(), amount), "Transfer failed");
    }
}
