// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";

// Chainlink价格接口
interface AggregatorV3Interface {
    function latestRoundData() external view returns (
        uint80 roundId,
        int256 price,
        uint256 startedAt,
        uint256 updatedAt,
        uint80 answeredInRound
    );
    function decimals() external view returns (uint8);
}

// PancakeSwap接口（简化）
interface IPancakeSwapPair {
    function getReserves() external view returns (uint112 reserve0, uint112 reserve1, uint32 blockTimestampLast);
    function token0() external view returns (address);
    function token1() external view returns (address);
}

/**
 * @title HAOXPriceOracleV2
 * @dev HAOX价格预言机合约
 *
 * 功能：
 * - 集成Chainlink获取BNB/USD价格
 * - 集成PancakeSwap TWAP作为备用
 * - 多源价格验证（偏差不超过5%）
 * - 每小时自动更新
 * - 异常处理机制
 */
contract HAOXPriceOracleV2 is Ownable, Pausable {
    
    // 价格数据结构
    struct PriceData {
        uint256 price; // 价格（8位小数）
        uint256 timestamp;
        uint256 confidence; // 置信度（0-100）
        bool isValid;
    }
    
    // 配置参数
    uint256 public constant MAX_PRICE_DEVIATION = 500; // 5% (basis points)
    uint256 public constant UPDATE_INTERVAL = 1 hours;
    uint256 public constant PRICE_STALENESS_THRESHOLD = 2 hours;
    uint256 public constant MIN_CONFIDENCE = 80; // 最低置信度80%
    
    // 价格源
    AggregatorV3Interface public bnbUsdChainlink;
    IPancakeSwapPair public haoxBnbPair;
    address public haoxToken;
    address public wbnb;
    
    // 价格数据
    PriceData public currentHaoxPrice;
    PriceData public currentBnbPrice;
    mapping(uint256 => PriceData) public priceHistory; // timestamp => price
    
    // 状态变量
    uint256 public lastUpdateTime;
    bool public emergencyMode;
    uint256 public emergencyPrice; // 紧急情况下的固定价格
    
    // 事件
    event PriceUpdated(
        uint256 haoxPrice,
        uint256 bnbPrice,
        uint256 confidence,
        string source
    );
    event EmergencyModeActivated(uint256 fixedPrice);
    event EmergencyModeDeactivated();
    event PriceSourceUpdated(string sourceType, address newAddress);
    
    constructor(
        address _bnbUsdChainlink,
        address _haoxBnbPair,
        address _haoxToken,
        address _wbnb
    ) Ownable(msg.sender) {
        require(_bnbUsdChainlink != address(0), "Invalid Chainlink address");
        require(_haoxBnbPair != address(0), "Invalid pair address");
        require(_haoxToken != address(0), "Invalid HAOX address");
        require(_wbnb != address(0), "Invalid WBNB address");
        
        bnbUsdChainlink = AggregatorV3Interface(_bnbUsdChainlink);
        haoxBnbPair = IPancakeSwapPair(_haoxBnbPair);
        haoxToken = _haoxToken;
        wbnb = _wbnb;
        
        // 初始化价格
        _updatePrices();
    }
    
    /**
     * @dev 获取BNB/USD价格（从Chainlink）
     */
    function getBnbUsdPrice() public view returns (uint256 price, uint256 confidence) {
        try bnbUsdChainlink.latestRoundData() returns (
            uint80,
            int256 _price,
            uint256,
            uint256 updatedAt,
            uint80
        ) {
            require(_price > 0, "Invalid price");
            require(block.timestamp - updatedAt <= PRICE_STALENESS_THRESHOLD, "Price too stale");
            
            uint8 decimals = bnbUsdChainlink.decimals();
            price = uint256(_price) * 10**(8 - decimals); // 标准化到8位小数
            
            // 计算置信度（基于数据新鲜度）
            uint256 age = block.timestamp - updatedAt;
            confidence = age < UPDATE_INTERVAL ? 100 : (100 * UPDATE_INTERVAL) / age;
            confidence = confidence > 100 ? 100 : confidence;
            
        } catch {
            price = 0;
            confidence = 0;
        }
    }
    
    /**
     * @dev 获取HAOX/BNB价格（从PancakeSwap）
     */
    function getHaoxBnbPrice() public view returns (uint256 price, uint256 confidence) {
        try haoxBnbPair.getReserves() returns (
            uint112 reserve0,
            uint112 reserve1,
            uint32 blockTimestampLast
        ) {
            require(reserve0 > 0 && reserve1 > 0, "Invalid reserves");
            
            // 确定哪个是HAOX，哪个是BNB
            bool haoxIsToken0 = haoxBnbPair.token0() == haoxToken;
            
            uint256 haoxReserve = haoxIsToken0 ? uint256(reserve0) : uint256(reserve1);
            uint256 bnbReserve = haoxIsToken0 ? uint256(reserve1) : uint256(reserve0);
            
            // 计算价格：1 HAOX = ? BNB
            price = (bnbReserve * 10**8) / haoxReserve; // 8位小数
            
            // 计算置信度（基于流动性和时间）
            uint256 totalLiquidity = haoxReserve + bnbReserve;
            uint256 age = block.timestamp - uint256(blockTimestampLast);
            
            confidence = totalLiquidity > 1000000 * 10**18 ? 90 : 70; // 基于流动性
            if (age > UPDATE_INTERVAL) {
                confidence = (confidence * UPDATE_INTERVAL) / age;
            }
            confidence = confidence > 100 ? 100 : confidence;
            
        } catch {
            price = 0;
            confidence = 0;
        }
    }
    
    /**
     * @dev 计算HAOX/USD价格
     */
    function getHaoxUsdPrice() public view returns (uint256 price, uint256 confidence) {
        if (emergencyMode) {
            return (emergencyPrice, 100);
        }
        
        (uint256 bnbUsdPrice, uint256 bnbConfidence) = getBnbUsdPrice();
        (uint256 haoxBnbPrice, uint256 haoxConfidence) = getHaoxBnbPrice();
        
        if (bnbUsdPrice == 0 || haoxBnbPrice == 0) {
            return (currentHaoxPrice.price, currentHaoxPrice.confidence);
        }
        
        // HAOX/USD = HAOX/BNB * BNB/USD
        price = (haoxBnbPrice * bnbUsdPrice) / 10**8;
        confidence = (bnbConfidence * haoxConfidence) / 100;
    }
    
    /**
     * @dev 更新价格（公开函数，任何人都可以调用）
     */
    function updatePrices() external whenNotPaused {
        require(
            block.timestamp >= lastUpdateTime + UPDATE_INTERVAL,
            "Update too frequent"
        );
        _updatePrices();
    }
    
    /**
     * @dev 内部价格更新逻辑
     */
    function _updatePrices() internal {
        (uint256 haoxPrice, uint256 haoxConfidence) = getHaoxUsdPrice();
        (uint256 bnbPrice, uint256 bnbConfidence) = getBnbUsdPrice();
        
        // 验证价格有效性
        if (haoxConfidence >= MIN_CONFIDENCE && bnbConfidence >= MIN_CONFIDENCE) {
            // 检查价格偏差
            if (currentHaoxPrice.isValid) {
                uint256 deviation = haoxPrice > currentHaoxPrice.price
                    ? ((haoxPrice - currentHaoxPrice.price) * 10000) / currentHaoxPrice.price
                    : ((currentHaoxPrice.price - haoxPrice) * 10000) / currentHaoxPrice.price;
                
                if (deviation > MAX_PRICE_DEVIATION) {
                    // 价格偏差过大，降低置信度
                    haoxConfidence = haoxConfidence / 2;
                }
            }
            
            // 更新价格
            currentHaoxPrice = PriceData({
                price: haoxPrice,
                timestamp: block.timestamp,
                confidence: haoxConfidence,
                isValid: true
            });
            
            currentBnbPrice = PriceData({
                price: bnbPrice,
                timestamp: block.timestamp,
                confidence: bnbConfidence,
                isValid: true
            });
            
            // 保存历史价格
            priceHistory[block.timestamp] = currentHaoxPrice;
            lastUpdateTime = block.timestamp;
            
            emit PriceUpdated(haoxPrice, bnbPrice, haoxConfidence, "chainlink_pancakeswap");
        }
    }
    
    /**
     * @dev 激活紧急模式（管理员）
     */
    function activateEmergencyMode(uint256 _fixedPrice) external onlyOwner {
        emergencyMode = true;
        emergencyPrice = _fixedPrice;
        emit EmergencyModeActivated(_fixedPrice);
    }
    
    /**
     * @dev 停用紧急模式（管理员）
     */
    function deactivateEmergencyMode() external onlyOwner {
        emergencyMode = false;
        emergencyPrice = 0;
        emit EmergencyModeDeactivated();
    }
    
    /**
     * @dev 获取最新价格（外部调用接口）
     */
    function getLatestPrice() external view returns (uint256) {
        (uint256 price,) = getHaoxUsdPrice();
        return price;
    }
    
    /**
     * @dev 获取价格和置信度
     */
    function getPriceWithConfidence() external view returns (uint256 price, uint256 confidence) {
        return getHaoxUsdPrice();
    }
    
    /**
     * @dev 暂停合约
     */
    function pause() external onlyOwner {
        _pause();
    }
    
    /**
     * @dev 恢复合约
     */
    function unpause() external onlyOwner {
        _unpause();
    }
}
