// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";
import "@openzeppelin/contracts/access/Ownable.sol";

/**
 * @title HAOXTokenV2
 * @dev 重构的HAOX代币合约
 * 
 * 特点：
 * - 移除铸币权限
 * - 移除余额修改权限
 * - 移除强制转账权限
 * - 保留紧急暂停权限（72小时限制）
 * - 保留参数更新权限（24小时时间锁）
 * - 总供应量：5,000,000,000 HAOX
 * - 初始解锁：500,000,000 HAOX（10%）
 */
contract HAOXTokenV2 is ERC20, Pausable, Ownable {
    
    // 供应量分配
    uint256 public constant TOTAL_SUPPLY = 5_000_000_000 * 10**18; // 50亿HAOX
    uint256 public constant INITIAL_UNLOCK = 500_000_000 * 10**18; // 5亿HAOX（10%）
    uint256 public constant PRESALE_ALLOCATION = 200_000_000 * 10**18; // 2亿HAOX
    uint256 public constant PROJECT_RESERVE = 300_000_000 * 10**18; // 3亿HAOX（邀请奖励等）
    uint256 public constant LOCKED_SUPPLY = 4_500_000_000 * 10**18; // 45亿HAOX待解锁
    
    // 合约地址
    address public presaleContract;
    address public invitationContract;
    address public vestingContract;
    address public priceOracle;
    
    // 时间锁机制
    struct TimeLock {
        uint256 unlockTime;
        bool executed;
    }
    
    mapping(bytes32 => TimeLock) public timeLocks;
    uint256 public constant TIMELOCK_DELAY = 24 hours;
    uint256 public constant MAX_PAUSE_DURATION = 72 hours;
    
    // 暂停相关
    uint256 public pauseStartTime;
    
    // 事件
    event ContractAddressUpdated(string contractType, address oldAddress, address newAddress);
    event TimeLockCreated(bytes32 indexed lockId, uint256 unlockTime);
    event TimeLockExecuted(bytes32 indexed lockId);
    event EmergencyPauseActivated(uint256 duration);
    
    constructor() ERC20("HAOX Token", "HAOX") Ownable(msg.sender) {
        // 铸造初始解锁的代币到合约部署者
        _mint(msg.sender, INITIAL_UNLOCK);
    }
    
    /**
     * @dev 设置预售合约地址（带时间锁）
     */
    function setPresaleContract(address _presaleContract) external onlyOwner {
        bytes32 lockId = keccak256(abi.encodePacked("setPresaleContract", _presaleContract, block.timestamp));
        
        if (timeLocks[lockId].unlockTime == 0) {
            // 创建时间锁
            timeLocks[lockId] = TimeLock({
                unlockTime: block.timestamp + TIMELOCK_DELAY,
                executed: false
            });
            emit TimeLockCreated(lockId, timeLocks[lockId].unlockTime);
        } else {
            // 执行时间锁
            require(block.timestamp >= timeLocks[lockId].unlockTime, "TimeLock not expired");
            require(!timeLocks[lockId].executed, "Already executed");
            
            address oldAddress = presaleContract;
            presaleContract = _presaleContract;
            timeLocks[lockId].executed = true;
            
            emit ContractAddressUpdated("presale", oldAddress, _presaleContract);
            emit TimeLockExecuted(lockId);
        }
    }
    
    /**
     * @dev 设置邀请合约地址（带时间锁）
     */
    function setInvitationContract(address _invitationContract) external onlyOwner {
        bytes32 lockId = keccak256(abi.encodePacked("setInvitationContract", _invitationContract, block.timestamp));
        
        if (timeLocks[lockId].unlockTime == 0) {
            timeLocks[lockId] = TimeLock({
                unlockTime: block.timestamp + TIMELOCK_DELAY,
                executed: false
            });
            emit TimeLockCreated(lockId, timeLocks[lockId].unlockTime);
        } else {
            require(block.timestamp >= timeLocks[lockId].unlockTime, "TimeLock not expired");
            require(!timeLocks[lockId].executed, "Already executed");
            
            address oldAddress = invitationContract;
            invitationContract = _invitationContract;
            timeLocks[lockId].executed = true;
            
            emit ContractAddressUpdated("invitation", oldAddress, _invitationContract);
            emit TimeLockExecuted(lockId);
        }
    }
    
    /**
     * @dev 设置解锁合约地址（带时间锁）
     */
    function setVestingContract(address _vestingContract) external onlyOwner {
        bytes32 lockId = keccak256(abi.encodePacked("setVestingContract", _vestingContract, block.timestamp));
        
        if (timeLocks[lockId].unlockTime == 0) {
            timeLocks[lockId] = TimeLock({
                unlockTime: block.timestamp + TIMELOCK_DELAY,
                executed: false
            });
            emit TimeLockCreated(lockId, timeLocks[lockId].unlockTime);
        } else {
            require(block.timestamp >= timeLocks[lockId].unlockTime, "TimeLock not expired");
            require(!timeLocks[lockId].executed, "Already executed");
            
            address oldAddress = vestingContract;
            vestingContract = _vestingContract;
            timeLocks[lockId].executed = true;
            
            emit ContractAddressUpdated("vesting", oldAddress, _vestingContract);
            emit TimeLockExecuted(lockId);
        }
    }
    
    /**
     * @dev 设置价格预言机地址（带时间锁）
     */
    function setPriceOracle(address _priceOracle) external onlyOwner {
        bytes32 lockId = keccak256(abi.encodePacked("setPriceOracle", _priceOracle, block.timestamp));
        
        if (timeLocks[lockId].unlockTime == 0) {
            timeLocks[lockId] = TimeLock({
                unlockTime: block.timestamp + TIMELOCK_DELAY,
                executed: false
            });
            emit TimeLockCreated(lockId, timeLocks[lockId].unlockTime);
        } else {
            require(block.timestamp >= timeLocks[lockId].unlockTime, "TimeLock not expired");
            require(!timeLocks[lockId].executed, "Already executed");
            
            address oldAddress = priceOracle;
            priceOracle = _priceOracle;
            timeLocks[lockId].executed = true;
            
            emit ContractAddressUpdated("priceOracle", oldAddress, _priceOracle);
            emit TimeLockExecuted(lockId);
        }
    }
    
    /**
     * @dev 紧急暂停（最长72小时）
     */
    function emergencyPause() external onlyOwner {
        require(!paused(), "Already paused");
        pauseStartTime = block.timestamp;
        _pause();
        emit EmergencyPauseActivated(MAX_PAUSE_DURATION);
    }
    
    /**
     * @dev 恢复运行
     */
    function unpause() external onlyOwner {
        require(paused(), "Not paused");
        _unpause();
        pauseStartTime = 0;
    }
    
    /**
     * @dev 自动解除暂停（72小时后任何人都可以调用）
     */
    function autoUnpause() external {
        require(paused(), "Not paused");
        require(pauseStartTime > 0, "Invalid pause state");
        require(block.timestamp >= pauseStartTime + MAX_PAUSE_DURATION, "Pause duration not exceeded");
        
        _unpause();
        pauseStartTime = 0;
    }
    
    /**
     * @dev 放弃所有权（不可逆）
     */
    function renounceOwnership() public override onlyOwner {
        super.renounceOwnership();
    }
    
    /**
     * @dev 重写转账函数以支持暂停机制
     */
    function transfer(address to, uint256 amount) public override whenNotPaused returns (bool) {
        return super.transfer(to, amount);
    }
    
    /**
     * @dev 重写授权转账函数以支持暂停机制
     */
    function transferFrom(address from, address to, uint256 amount) public override whenNotPaused returns (bool) {
        return super.transferFrom(from, to, amount);
    }
    
    /**
     * @dev 获取合约状态
     */
    function getContractStatus() external view returns (
        bool isPaused,
        uint256 pauseDuration,
        uint256 remainingPauseTime,
        address presale,
        address invitation,
        address vesting,
        address oracle
    ) {
        uint256 remaining = 0;
        if (paused() && pauseStartTime > 0) {
            uint256 elapsed = block.timestamp - pauseStartTime;
            remaining = elapsed < MAX_PAUSE_DURATION ? MAX_PAUSE_DURATION - elapsed : 0;
        }
        
        return (
            paused(),
            pauseStartTime > 0 ? block.timestamp - pauseStartTime : 0,
            remaining,
            presaleContract,
            invitationContract,
            vestingContract,
            priceOracle
        );
    }
}
