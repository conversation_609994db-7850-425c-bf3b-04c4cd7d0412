/**
 * HAOXVestingV2Minimal 合约测试
 * 验证修复后的合约功能是否正常
 */

const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("HAOXVestingV2Minimal - 修复后测试", function () {
    let haoxToken;
    let priceOracle;
    let vestingContract;
    let owner;
    let projectWallet;
    let communityWallet;
    let addr1;

    beforeEach(async function () {
        [owner, projectWallet, communityWallet, addr1] = await ethers.getSigners();

        // 部署模拟HAOX代币
        const MockERC20 = await ethers.getContractFactory("MockERC20");
        haoxToken = await MockERC20.deploy("HAOX Token", "HAOX", ethers.parseEther("5000000000"));

        // 部署模拟价格预言机
        const MockPriceOracle = await ethers.getContractFactory("MockPriceOracle");
        priceOracle = await MockPriceOracle.deploy();

        // 部署解锁合约
        const HAOXVestingV2Minimal = await ethers.getContractFactory("HAOXVestingV2Minimal");
        vestingContract = await HAOXVestingV2Minimal.deploy(
            await haoxToken.getAddress(),
            await priceOracle.getAddress(),
            projectWallet.address,
            communityWallet.address
        );

        // 向解锁合约转入代币
        await haoxToken.transfer(await vestingContract.getAddress(), ethers.parseEther("5000000000"));
    });

    describe("🔧 修复验证", function () {
        it("✅ 应该正确使用自定义错误而不是require", async function () {
            // 测试无效地址错误
            const HAOXVestingV2Minimal = await ethers.getContractFactory("HAOXVestingV2Minimal");
            
            await expect(
                HAOXVestingV2Minimal.deploy(
                    ethers.ZeroAddress, // 无效地址
                    await priceOracle.getAddress(),
                    projectWallet.address,
                    communityWallet.address
                )
            ).to.be.revertedWithCustomError(vestingContract, "InvalidAddress");
        });

        it("✅ 应该正确处理重入攻击防护", async function () {
            // 设置价格达到触发条件
            await priceOracle.setPrice(ethers.parseUnits("0.01", 8));
            
            // 检查价格条件
            await vestingContract.checkPriceCondition();
            
            // 验证状态已正确更新
            const roundInfo = await vestingContract.getRoundInfo(1);
            expect(roundInfo.priceConditionMet).to.be.true;
        });

        it("✅ 应该正确处理无效轮次", async function () {
            await expect(
                vestingContract.getRoundInfo(0)
            ).to.be.revertedWithCustomError(vestingContract, "InvalidRound");

            await expect(
                vestingContract.getRoundInfo(32)
            ).to.be.revertedWithCustomError(vestingContract, "InvalidRound");
        });

        it("✅ 应该正确处理紧急提取权限", async function () {
            // 暂停合约
            await vestingContract.pause();
            
            // 非授权用户尝试紧急提取
            await expect(
                vestingContract.connect(addr1).requestEmergencyWithdraw(ethers.parseEther("1000"))
            ).to.be.revertedWithCustomError(vestingContract, "NotAuthorized");
        });

        it("✅ 应该正确处理金额过大错误", async function () {
            // 暂停合约
            await vestingContract.pause();
            
            // 尝试提取超过最大金额
            await expect(
                vestingContract.requestEmergencyWithdraw(ethers.parseEther("2000000")) // 超过100万限制
            ).to.be.revertedWithCustomError(vestingContract, "AmountTooLarge");
        });
    });

    describe("🔍 功能完整性测试", function () {
        it("✅ 应该正确初始化轮次数据", async function () {
            const roundInfo = await vestingContract.getRoundInfo(1);
            expect(roundInfo.triggerPrice).to.equal(ethers.parseUnits("0.01", 8));
            expect(roundInfo.unlocked).to.be.false;
        });

        it("✅ 应该正确处理价格检查", async function () {
            // 设置价格低于触发价格
            await priceOracle.setPrice(ethers.parseUnits("0.005", 8));
            await vestingContract.checkPriceCondition();
            
            let roundInfo = await vestingContract.getRoundInfo(1);
            expect(roundInfo.priceConditionMet).to.be.false;

            // 设置价格达到触发价格
            await priceOracle.setPrice(ethers.parseUnits("0.01", 8));
            await vestingContract.checkPriceCondition();
            
            roundInfo = await vestingContract.getRoundInfo(1);
            expect(roundInfo.priceConditionMet).to.be.true;
        });

        it("✅ 应该正确获取解锁进度", async function () {
            await priceOracle.setPrice(ethers.parseUnits("0.01", 8));
            
            const progress = await vestingContract.getUnlockProgress();
            expect(progress.currentPrice).to.equal(ethers.parseUnits("0.01", 8));
            expect(progress.nextRound).to.equal(1);
            expect(progress.targetPrice).to.equal(ethers.parseUnits("0.01", 8));
        });
    });

    describe("🛡️ 安全性测试", function () {
        it("✅ 应该正确处理价格预言机失败", async function () {
            // 部署一个会失败的价格预言机
            const FailingOracle = await ethers.getContractFactory("FailingPriceOracle");
            const failingOracle = await FailingOracle.deploy();
            
            const HAOXVestingV2Minimal = await ethers.getContractFactory("HAOXVestingV2Minimal");
            const failingVesting = await HAOXVestingV2Minimal.deploy(
                await haoxToken.getAddress(),
                await failingOracle.getAddress(),
                projectWallet.address,
                communityWallet.address
            );

            await expect(
                failingVesting.checkPriceCondition()
            ).to.be.revertedWithCustomError(failingVesting, "PriceOracleFailed");
        });

        it("✅ 应该正确处理紧急暂停", async function () {
            // 添加紧急签名者
            await vestingContract.addEmergencySigner(addr1.address);
            
            // 紧急暂停
            await vestingContract.connect(addr1).emergencyPause();
            
            // 验证合约已暂停
            expect(await vestingContract.paused()).to.be.true;
        });

        it("✅ 应该正确处理时间锁", async function () {
            // 暂停合约
            await vestingContract.pause();
            
            // 请求紧急提取
            const amount = ethers.parseEther("1000");
            await vestingContract.requestEmergencyWithdraw(amount);
            
            // 立即尝试执行应该失败
            const requestId = ethers.keccak256(
                ethers.solidityPacked(
                    ["uint256", "uint256", "address"],
                    [amount, await ethers.provider.getBlock("latest").then(b => b.timestamp), owner.address]
                )
            );
            
            await expect(
                vestingContract.executeEmergencyWithdraw(requestId)
            ).to.be.revertedWithCustomError(vestingContract, "TimeLockActive");
        });
    });
});
