// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "forge-std/Test.sol";
import "../contracts/HAOXPresaleV2.sol";

/**
 * @title 预售并发测试
 * @dev 测试多用户同时购买的场景
 */
contract PresaleConcurrencyTest is Test {
    HAOXPresaleV2 public presale;
    
    address public user1 = address(0x1);
    address public user2 = address(0x2);
    address public user3 = address(0x3);
    
    function setUp() public {
        presale = new HAOXPresaleV2();
        
        // 添加用户到白名单
        address[] memory users = new address[](3);
        users[0] = user1;
        users[1] = user2;
        users[2] = user3;
        presale.addToWhitelist(users);
        
        // 给用户分配BNB
        vm.deal(user1, 100 ether);
        vm.deal(user2, 100 ether);
        vm.deal(user3, 100 ether);
    }
    
    /**
     * @dev 测试同一阶段内的并发购买
     */
    function testConcurrentPurchasesSameStage() public {
        // 用户1购买1 BNB
        vm.prank(user1);
        presale.buyTokens{value: 1 ether}();
        
        // 用户2购买1 BNB
        vm.prank(user2);
        presale.buyTokens{value: 1 ether}();
        
        // 验证状态一致性
        (uint256 currentStage, uint256 tokensRemaining, uint256 totalBNB, uint256 totalTokens,,) = 
            presale.getPresaleStatus();
        
        uint256 expectedTokens = 2 * 1 ether * presale.getStageRate(0) / 1e18;
        
        assertEq(totalBNB, 2 ether, "Total BNB should be 2");
        assertEq(totalTokens, expectedTokens, "Total tokens mismatch");
        assertEq(currentStage, 0, "Should still be in stage 0");
    }
    
    /**
     * @dev 测试跨阶段的并发购买
     */
    function testConcurrentCrossStage() public {
        // 先购买到接近阶段边界
        uint256 tokensPerStage = presale.TOKENS_PER_STAGE();
        uint256 rate0 = presale.getStageRate(0);
        uint256 bnbForMostOfStage = (tokensPerStage * 9 / 10) * 1e18 / rate0; // 90%的阶段
        
        vm.prank(user1);
        presale.buyTokens{value: bnbForMostOfStage}();
        
        // 现在两个用户同时购买大额，会跨越阶段
        vm.prank(user2);
        presale.buyTokens{value: 5 ether}();
        
        vm.prank(user3);
        presale.buyTokens{value: 5 ether}();
        
        // 验证最终状态
        (uint256 currentStage, uint256 tokensRemaining, uint256 totalBNB, uint256 totalTokens,,) = 
            presale.getPresaleStatus();
        
        assertTrue(currentStage > 0, "Should have progressed to next stage");
        assertEq(totalBNB, bnbForMostOfStage + 10 ether, "Total BNB mismatch");
    }
    
    /**
     * @dev 测试边界条件：阶段刚好用完
     */
    function testStageExactCompletion() public {
        uint256 tokensPerStage = presale.TOKENS_PER_STAGE();
        uint256 rate0 = presale.getStageRate(0);
        uint256 bnbForExactStage = tokensPerStage * 1e18 / rate0;
        
        vm.prank(user1);
        presale.buyTokens{value: bnbForExactStage}();
        
        (uint256 currentStage, uint256 tokensRemaining,,,, ) = 
            presale.getPresaleStatus();
        
        assertEq(currentStage, 1, "Should be in stage 1");
        assertEq(tokensRemaining, tokensPerStage, "New stage should be full");
    }
    
    /**
     * @dev 测试Gas消耗
     */
    function testGasConsumption() public {
        uint256 gasBefore = gasleft();
        
        vm.prank(user1);
        presale.buyTokens{value: 10 ether}(); // 大额购买，可能跨多个阶段
        
        uint256 gasUsed = gasBefore - gasleft();
        
        // Gas消耗应该在合理范围内（小于500k）
        assertTrue(gasUsed < 500000, "Gas consumption too high");
    }
    
    /**
     * @dev 测试最大投资限制
     */
    function testMaxInvestmentLimit() public {
        vm.prank(user1);
        presale.buyTokens{value: 20 ether}(); // 最大投资
        
        // 尝试再次投资应该失败
        vm.prank(user1);
        vm.expectRevert("Exceeds maximum investment");
        presale.buyTokens{value: 0.1 ether}();
    }
    
    /**
     * @dev 测试预售结束条件
     */
    function testPresaleEndConditions() public {
        // 模拟达到目标BNB
        uint256 targetBNB = presale.TARGET_BNB();
        
        // 多个用户购买直到接近目标
        uint256 remainingBNB = targetBNB;
        uint256 userIndex = 1;
        
        while (remainingBNB > 20 ether) {
            address currentUser = address(uint160(userIndex));
            vm.deal(currentUser, 25 ether);
            
            address[] memory singleUser = new address[](1);
            singleUser[0] = currentUser;
            presale.addToWhitelist(singleUser);
            
            vm.prank(currentUser);
            presale.buyTokens{value: 20 ether}();
            
            remainingBNB -= 20 ether;
            userIndex++;
        }
        
        // 最后一次购买
        if (remainingBNB > 0) {
            address finalUser = address(uint160(userIndex));
            vm.deal(finalUser, remainingBNB + 1 ether);
            
            address[] memory singleUser = new address[](1);
            singleUser[0] = finalUser;
            presale.addToWhitelist(singleUser);
            
            vm.prank(finalUser);
            presale.buyTokens{value: remainingBNB}();
        }
        
        (,,,,,bool isActive) = presale.getPresaleStatus();
        assertFalse(isActive, "Presale should be ended");
    }
}
