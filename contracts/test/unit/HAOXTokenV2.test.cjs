const { expect } = require("chai");
const { ethers } = require("hardhat");
const { time } = require("@nomicfoundation/hardhat-network-helpers");

describe("HAOXTokenV2", function () {
  let haoxToken;
  let owner;
  let addr1;
  let addr2;
  let addr3;
  let presaleContract;
  let invitationContract;
  let vestingContract;
  let priceOracle;
  let addrs;

  beforeEach(async function () {
    [owner, addr1, addr2, addr3, presaleContract, invitationContract, vestingContract, priceOracle, ...addrs] = await ethers.getSigners();

    const HAOXTokenV2 = await ethers.getContractFactory("HAOXTokenV2");
    haoxToken = await HAOXTokenV2.deploy();
    await haoxToken.waitForDeployment();
  });

  describe("Deployment", function () {
    it("Should set the right owner", async function () {
      expect(await haoxToken.owner()).to.equal(owner.address);
    });

    it("Should assign the total supply of tokens to the owner", async function () {
      const ownerBalance = await haoxToken.balanceOf(owner.address);
      expect(await haoxToken.totalSupply()).to.equal(ownerBalance);
    });

    it("Should have correct name and symbol", async function () {
      expect(await haoxToken.name()).to.equal("HAOX Token");
      expect(await haoxToken.symbol()).to.equal("HAOX");
    });

    it("Should have 18 decimals", async function () {
      expect(await haoxToken.decimals()).to.equal(18);
    });

    it("Should have correct total supply", async function () {
      const expectedSupply = ethers.parseEther("500000000"); // 5亿代币 (初始解锁)
      expect(await haoxToken.totalSupply()).to.equal(expectedSupply);
    });

    it("Should have correct supply constants", async function () {
      expect(await haoxToken.TOTAL_SUPPLY()).to.equal(ethers.parseEther("5000000000")); // 50亿
      expect(await haoxToken.INITIAL_UNLOCK()).to.equal(ethers.parseEther("500000000")); // 5亿
      expect(await haoxToken.PRESALE_ALLOCATION()).to.equal(ethers.parseEther("200000000")); // 2亿
      expect(await haoxToken.PROJECT_RESERVE()).to.equal(ethers.parseEther("300000000")); // 3亿
      expect(await haoxToken.LOCKED_SUPPLY()).to.equal(ethers.parseEther("4500000000")); // 45亿
    });

    it("Should have correct timelock delay", async function () {
      expect(await haoxToken.TIMELOCK_DELAY()).to.equal(7 * 24 * 3600); // 7天
    });

    it("Should have correct max pause duration", async function () {
      expect(await haoxToken.MAX_PAUSE_DURATION()).to.equal(72 * 3600); // 72小时
    });

    it("Should start unpaused", async function () {
      expect(await haoxToken.paused()).to.be.false;
      expect(await haoxToken.pauseStartTime()).to.equal(0);
    });
  });

  describe("Transactions", function () {
    it("Should transfer tokens between accounts", async function () {
      const transferAmount = ethers.parseEther("50");
      
      await haoxToken.transfer(addr1.address, transferAmount);
      const addr1Balance = await haoxToken.balanceOf(addr1.address);
      expect(addr1Balance).to.equal(transferAmount);

      await haoxToken.connect(addr1).transfer(addr2.address, transferAmount);
      const addr2Balance = await haoxToken.balanceOf(addr2.address);
      expect(addr2Balance).to.equal(transferAmount);
    });

    it("Should fail if sender doesn't have enough tokens", async function () {
      const initialOwnerBalance = await haoxToken.balanceOf(owner.address);
      const transferAmount = initialOwnerBalance + 1n;

      await expect(
        haoxToken.connect(addr1).transfer(owner.address, transferAmount)
      ).to.be.revertedWithCustomError(haoxToken, "ERC20InsufficientBalance");
    });

    it("Should update balances after transfers", async function () {
      const initialOwnerBalance = await haoxToken.balanceOf(owner.address);
      const transferAmount = ethers.parseEther("100");

      await haoxToken.transfer(addr1.address, transferAmount);
      await haoxToken.transfer(addr2.address, transferAmount);

      const finalOwnerBalance = await haoxToken.balanceOf(owner.address);
      expect(finalOwnerBalance).to.equal(initialOwnerBalance - transferAmount * 2n);

      const addr1Balance = await haoxToken.balanceOf(addr1.address);
      expect(addr1Balance).to.equal(transferAmount);

      const addr2Balance = await haoxToken.balanceOf(addr2.address);
      expect(addr2Balance).to.equal(transferAmount);
    });
  });

  describe("Allowances", function () {
    it("Should approve tokens for delegated transfer", async function () {
      const approveAmount = ethers.parseEther("100");
      
      await haoxToken.approve(addr1.address, approveAmount);
      expect(await haoxToken.allowance(owner.address, addr1.address)).to.equal(approveAmount);
    });

    it("Should allow delegated transfers", async function () {
      const approveAmount = ethers.parseEther("100");
      const transferAmount = ethers.parseEther("50");
      
      await haoxToken.approve(addr1.address, approveAmount);
      await haoxToken.connect(addr1).transferFrom(owner.address, addr2.address, transferAmount);
      
      expect(await haoxToken.balanceOf(addr2.address)).to.equal(transferAmount);
      expect(await haoxToken.allowance(owner.address, addr1.address)).to.equal(approveAmount - transferAmount);
    });

    it("Should fail delegated transfer if allowance is insufficient", async function () {
      const approveAmount = ethers.parseEther("50");
      const transferAmount = ethers.parseEther("100");
      
      await haoxToken.approve(addr1.address, approveAmount);
      
      await expect(
        haoxToken.connect(addr1).transferFrom(owner.address, addr2.address, transferAmount)
      ).to.be.revertedWithCustomError(haoxToken, "ERC20InsufficientAllowance");
    });
  });

  describe("Ownership", function () {
    it("Should allow owner to transfer ownership", async function () {
      await haoxToken.transferOwnership(addr1.address);
      expect(await haoxToken.owner()).to.equal(addr1.address);
    });

    it("Should prevent non-owner from transferring ownership", async function () {
      await expect(
        haoxToken.connect(addr1).transferOwnership(addr2.address)
      ).to.be.revertedWithCustomError(haoxToken, "OwnableUnauthorizedAccount");
    });
  });

  describe("Pausable", function () {
    it("Should allow owner to emergency pause and unpause", async function () {
      await haoxToken.emergencyPause();
      expect(await haoxToken.paused()).to.be.true;

      await haoxToken.unpause();
      expect(await haoxToken.paused()).to.be.false;
    });

    it("Should prevent transfers when paused", async function () {
      await haoxToken.emergencyPause();

      await expect(
        haoxToken.transfer(addr1.address, ethers.parseEther("50"))
      ).to.be.revertedWithCustomError(haoxToken, "EnforcedPause");
    });

    it("Should prevent non-owner from emergency pausing", async function () {
      await expect(
        haoxToken.connect(addr1).emergencyPause()
      ).to.be.revertedWithCustomError(haoxToken, "OwnableUnauthorizedAccount");
    });
  });

  describe("Time Lock Functions", function () {
    describe("Presale Contract Setting", function () {
      it("Should create timelock for presale contract setting", async function () {
        await expect(haoxToken.setPresaleContract(presaleContract.address))
          .to.emit(haoxToken, "TimeLockCreated");

        // 第二次调用应该执行设置
        await time.increase(7 * 24 * 3600 + 1); // 7天+1秒

        await expect(haoxToken.setPresaleContract(presaleContract.address))
          .to.emit(haoxToken, "TimeLockExecuted");

        expect(await haoxToken.presaleContract()).to.equal(presaleContract.address);
      });

      it("Should prevent setting presale contract before timelock", async function () {
        await haoxToken.setPresaleContract(presaleContract.address); // 创建时间锁

        // 立即尝试再次调用应该失败
        await expect(haoxToken.setPresaleContract(presaleContract.address))
          .to.be.revertedWith("Time lock active");
      });

      it("Should prevent non-owner from setting presale contract", async function () {
        await expect(haoxToken.connect(addr1).setPresaleContract(presaleContract.address))
          .to.be.revertedWithCustomError(haoxToken, "OwnableUnauthorizedAccount");
      });
    });

    describe("Invitation Contract Setting", function () {
      it("Should create timelock for invitation contract setting", async function () {
        await expect(haoxToken.setInvitationContract(invitationContract.address))
          .to.emit(haoxToken, "TimeLockCreated");

        await time.increase(7 * 24 * 3600 + 1);

        await expect(haoxToken.setInvitationContract(invitationContract.address))
          .to.emit(haoxToken, "TimeLockExecuted");

        expect(await haoxToken.invitationContract()).to.equal(invitationContract.address);
      });

      it("Should prevent setting invitation contract before timelock", async function () {
        await haoxToken.setInvitationContract(invitationContract.address);

        await expect(haoxToken.setInvitationContract(invitationContract.address))
          .to.be.revertedWith("Time lock active");
      });
    });

    describe("Vesting Contract Setting", function () {
      it("Should create timelock for vesting contract setting", async function () {
        await expect(haoxToken.setVestingContract(vestingContract.address))
          .to.emit(haoxToken, "TimeLockCreated");

        await time.increase(7 * 24 * 3600 + 1);

        await expect(haoxToken.setVestingContract(vestingContract.address))
          .to.emit(haoxToken, "TimeLockExecuted");

        expect(await haoxToken.vestingContract()).to.equal(vestingContract.address);
      });

      it("Should prevent setting vesting contract before timelock", async function () {
        await haoxToken.setVestingContract(vestingContract.address);

        await expect(haoxToken.setVestingContract(vestingContract.address))
          .to.be.revertedWith("Time lock active");
      });
    });

    describe("Price Oracle Setting", function () {
      it("Should create timelock for price oracle setting", async function () {
        await expect(haoxToken.setPriceOracle(priceOracle.address))
          .to.emit(haoxToken, "TimeLockCreated");

        await time.increase(7 * 24 * 3600 + 1);

        await expect(haoxToken.setPriceOracle(priceOracle.address))
          .to.emit(haoxToken, "TimeLockExecuted");

        expect(await haoxToken.priceOracle()).to.equal(priceOracle.address);
      });

      it("Should prevent setting price oracle before timelock", async function () {
        await haoxToken.setPriceOracle(priceOracle.address);

        await expect(haoxToken.setPriceOracle(priceOracle.address))
          .to.be.revertedWith("Time lock active");
      });
    });
  });

  describe("Emergency Pause System", function () {
    it("Should allow owner to emergency pause", async function () {
      await expect(haoxToken.emergencyPause())
        .to.emit(haoxToken, "EmergencyPauseActivated")
        .withArgs(72 * 3600); // 72小时

      expect(await haoxToken.paused()).to.be.true;
      expect(await haoxToken.pauseStartTime()).to.be.gt(0);
    });

    it("Should prevent double emergency pause", async function () {
      await haoxToken.emergencyPause();

      await expect(haoxToken.emergencyPause())
        .to.be.revertedWith("Already paused");
    });

    it("Should allow owner to unpause", async function () {
      await haoxToken.emergencyPause();

      await haoxToken.unpause();

      expect(await haoxToken.paused()).to.be.false;
      expect(await haoxToken.pauseStartTime()).to.equal(0);
    });

    it("Should prevent unpause when not paused", async function () {
      await expect(haoxToken.unpause())
        .to.be.revertedWith("Not paused");
    });

    it("Should allow auto unpause after 72 hours", async function () {
      await haoxToken.emergencyPause();

      // 快进72小时
      await time.increase(72 * 3600 + 1);

      // 任何人都可以调用autoUnpause
      await haoxToken.connect(addr1).autoUnpause();

      expect(await haoxToken.paused()).to.be.false;
      expect(await haoxToken.pauseStartTime()).to.equal(0);
    });

    it("Should prevent auto unpause before 72 hours", async function () {
      await haoxToken.emergencyPause();

      // 只快进1小时
      await time.increase(3600);

      await expect(haoxToken.connect(addr1).autoUnpause())
        .to.be.revertedWith("Pause duration not exceeded");
    });

    it("Should prevent auto unpause when not paused", async function () {
      await expect(haoxToken.connect(addr1).autoUnpause())
        .to.be.revertedWith("Not paused");
    });

    it("Should prevent transfers when paused", async function () {
      await haoxToken.emergencyPause();

      await expect(haoxToken.transfer(addr1.address, ethers.parseEther("50")))
        .to.be.revertedWithCustomError(haoxToken, "EnforcedPause");

      await expect(haoxToken.connect(addr1).transferFrom(owner.address, addr2.address, ethers.parseEther("50")))
        .to.be.revertedWithCustomError(haoxToken, "EnforcedPause");
    });
  });

  describe("Contract Status View", function () {
    it("Should return correct contract status when not paused", async function () {
      const status = await haoxToken.getContractStatus();

      expect(status.isPaused).to.be.false;
      expect(status.pauseDuration).to.equal(0);
      expect(status.remainingPauseTime).to.equal(0);
      expect(status.presale).to.equal(ethers.ZeroAddress);
      expect(status.invitation).to.equal(ethers.ZeroAddress);
    });

    it("Should return correct contract status when paused", async function () {
      await haoxToken.emergencyPause();

      const status = await haoxToken.getContractStatus();

      expect(status.isPaused).to.be.true;
      expect(status.pauseDuration).to.be.gt(0);
      expect(status.remainingPauseTime).to.be.gt(0);
    });

    it("Should return correct contract addresses after setting", async function () {
      // 设置预售合约
      await haoxToken.setPresaleContract(presaleContract.address);
      await time.increase(7 * 24 * 3600 + 1);
      await haoxToken.setPresaleContract(presaleContract.address);

      const status = await haoxToken.getContractStatus();
      expect(status.presale).to.equal(presaleContract.address);
    });
  });

  describe("Edge Cases", function () {
    it("Should handle zero transfers", async function () {
      await expect(haoxToken.transfer(addr1.address, 0)).to.not.be.reverted;
      expect(await haoxToken.balanceOf(addr1.address)).to.equal(0);
    });

    it("Should handle maximum approval", async function () {
      const maxApproval = ethers.MaxUint256;
      await haoxToken.approve(addr1.address, maxApproval);
      expect(await haoxToken.allowance(owner.address, addr1.address)).to.equal(maxApproval);
    });

    it("Should prevent transfer to zero address", async function () {
      await expect(
        haoxToken.transfer(ethers.ZeroAddress, ethers.parseEther("50"))
      ).to.be.revertedWithCustomError(haoxToken, "ERC20InvalidReceiver");
    });

    it("Should handle ownership renouncement", async function () {
      await haoxToken.renounceOwnership();
      expect(await haoxToken.owner()).to.equal(ethers.ZeroAddress);
    });

    it("Should handle very large amounts", async function () {
      const ownerBalance = await haoxToken.balanceOf(owner.address);
      const largeAmount = ownerBalance + ethers.parseEther("1000000");

      // 应该能处理大数值而不溢出
      await expect(haoxToken.transfer(addr1.address, largeAmount))
        .to.be.revertedWithCustomError(haoxToken, "ERC20InsufficientBalance");
    });

    it("Should handle rapid pause/unpause cycles", async function () {
      await haoxToken.emergencyPause();
      await haoxToken.unpause();
      await haoxToken.emergencyPause();
      await haoxToken.unpause();

      expect(await haoxToken.paused()).to.be.false;
    });
  });
});
