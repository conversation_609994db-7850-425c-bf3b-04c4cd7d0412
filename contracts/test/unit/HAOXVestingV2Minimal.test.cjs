const { expect } = require("chai");
const { ethers } = require("hardhat");
const { time } = require("@nomicfoundation/hardhat-network-helpers");

describe("HAOXVestingV2Minimal", function () {
  let haoxToken;
  let priceOracle;
  let vestingContract;
  let owner;
  let projectWallet;
  let communityWallet;
  let addr1;
  let addr2;

  beforeEach(async function () {
    [owner, projectWallet, communityWallet, addr1, addr2] = await ethers.getSigners();

    // 部署HAOX代币
    const HAOXTokenV2 = await ethers.getContractFactory("HAOXTokenV2");
    haoxToken = await HAOXTokenV2.deploy();
    await haoxToken.waitForDeployment();

    // 部署模拟价格预言机 (使用模拟地址)
    const MockPriceOracle = await ethers.getContractFactory("MockPriceOracle");
    priceOracle = await MockPriceOracle.deploy();
    await priceOracle.waitForDeployment();

    // 部署解锁合约
    const HAOXVestingV2Minimal = await ethers.getContractFactory("HAOXVestingV2Minimal");
    vestingContract = await HAOXVestingV2Minimal.deploy(
      await haoxToken.getAddress(),
      await priceOracle.getAddress(),
      projectWallet.address,
      communityWallet.address
    );
    await vestingContract.waitForDeployment();

    // 向解锁合约转入代币
    const totalTokens = ethers.parseEther("400000000"); // 4亿代币（足够前几轮解锁）
    await haoxToken.transfer(await vestingContract.getAddress(), totalTokens);
  });

  describe("Deployment", function () {
    it("Should set the correct initial values", async function () {
      expect(await vestingContract.HAOX_TOKEN()).to.equal(await haoxToken.getAddress());
      expect(await vestingContract.PRICE_ORACLE()).to.equal(await priceOracle.getAddress());
      expect(await vestingContract.PROJECT_WALLET()).to.equal(projectWallet.address);
      expect(await vestingContract.COMMUNITY_WALLET()).to.equal(communityWallet.address);
      expect(await vestingContract.currentRound()).to.equal(1);
    });

    it("Should initialize 31 rounds with correct prices", async function () {
      for (let i = 1; i <= 31; i++) {
        const round = await vestingContract.rounds(i);
        expect(round.triggerPrice).to.be.gt(0);
        expect(round.priceConditionMet).to.be.false;
        expect(round.unlocked).to.be.false;
      }
    });

    it("Should set owner as emergency signer", async function () {
      expect(await vestingContract.emergencySigners(owner.address)).to.be.true;
    });

    it("Should have correct constants", async function () {
      expect(await vestingContract.TOTAL_ROUNDS()).to.equal(31);
      expect(await vestingContract.PRICE_MAINTAIN_DURATION()).to.equal(7 * 24 * 3600); // 7天
      expect(await vestingContract.EMERGENCY_DELAY()).to.equal(7 * 24 * 3600); // 7天
      expect(await vestingContract.MAX_EMERGENCY_AMOUNT()).to.equal(ethers.parseEther("1000000"));
    });
  });

  describe("Price Condition Checking", function () {
    it("Should check price condition for current round", async function () {
      // 设置价格预言机返回足够高的价格（8位小数）
      await priceOracle.setPrice(1000000); // 0.01 USD (8位小数)

      await expect(vestingContract.checkPriceCondition())
        .to.emit(vestingContract, "PriceConditionMet");
    });

    it("Should not trigger if price is too low", async function () {
      // 设置价格预言机返回较低的价格（8位小数）
      await priceOracle.setPrice(500000); // 0.005 USD (8位小数，低于0.01触发价格)

      await vestingContract.checkPriceCondition();

      const round = await vestingContract.rounds(1);
      expect(round.priceConditionMet).to.be.false;
    });

    it("Should maintain price history", async function () {
      await priceOracle.setPrice(1000000); // 0.01 USD (8位小数)
      await vestingContract.checkPriceCondition();

      const historyIndex = await vestingContract.historyIndex(1);
      expect(historyIndex).to.be.gt(0);
    });

    it("Should reset price condition if price drops", async function () {
      // 首先设置高价格
      await priceOracle.setPrice(1000000); // 0.01 USD (8位小数)
      await vestingContract.checkPriceCondition();

      // 然后设置低价格
      await priceOracle.setPrice(500000); // 0.005 USD (8位小数)
      await vestingContract.checkPriceCondition();

      const round = await vestingContract.rounds(1);
      expect(round.priceConditionMet).to.be.false;
    });
  });

  describe("Round Unlocking", function () {
    it("Should unlock round after price maintained for 7 days", async function () {
      // 设置高价格并触发条件
      await priceOracle.setPrice(1000000); // 0.01 USD (8位小数)
      await vestingContract.checkPriceCondition();

      // 快进7天
      await time.increase(7 * 24 * 3600);

      // 再次检查价格条件，这时应该自动解锁
      await expect(vestingContract.checkPriceCondition())
        .to.emit(vestingContract, "RoundUnlocked");

      const round = await vestingContract.rounds(1);
      expect(round.unlocked).to.be.true;
      expect(await vestingContract.currentRound()).to.equal(2);
    });

    it("Should not unlock if price not maintained long enough", async function () {
      await priceOracle.setPrice(1000000); // 0.01 USD (8位小数)
      await vestingContract.checkPriceCondition();

      // 只快进3天
      await time.increase(3 * 24 * 3600);

      // 再次检查价格条件，不应该解锁
      await vestingContract.checkPriceCondition();

      const round = await vestingContract.rounds(1);
      expect(round.unlocked).to.be.false;
    });

    it("Should distribute tokens correctly on unlock", async function () {
      const initialProjectBalance = await haoxToken.balanceOf(projectWallet.address);
      const initialCommunityBalance = await haoxToken.balanceOf(communityWallet.address);

      // 触发解锁
      await priceOracle.setPrice(1000000); // 0.01 USD (8位小数)
      await vestingContract.checkPriceCondition();
      await time.increase(7 * 24 * 3600);
      await vestingContract.checkPriceCondition(); // 这会触发解锁

      const finalProjectBalance = await haoxToken.balanceOf(projectWallet.address);
      const finalCommunityBalance = await haoxToken.balanceOf(communityWallet.address);

      expect(finalProjectBalance).to.be.gt(initialProjectBalance);
      expect(finalCommunityBalance).to.be.gt(initialCommunityBalance);
    });
  });

  describe("Emergency Functions", function () {
    beforeEach(async function () {
      // 紧急提取需要合约处于暂停状态
      await vestingContract.pause();
    });

    it("Should allow emergency signer to request withdrawal", async function () {
      const amount = ethers.parseEther("1000");

      await expect(vestingContract.requestEmergencyWithdraw(amount))
        .to.emit(vestingContract, "EmergencyWithdrawRequested");
    });

    it("Should not allow non-signer to request withdrawal", async function () {
      const amount = ethers.parseEther("1000");
      
      await expect(vestingContract.connect(addr1).requestEmergencyWithdraw(amount))
        .to.be.revertedWith("Not authorized");
    });

    it("Should not allow withdrawal above maximum", async function () {
      const amount = ethers.parseEther("2000000"); // 超过最大限制
      
      await expect(vestingContract.requestEmergencyWithdraw(amount))
        .to.be.revertedWith("Amount too large");
    });

    it("Should execute emergency withdrawal after delay", async function () {
      const amount = ethers.parseEther("1000");
      
      // 请求紧急提取
      const tx = await vestingContract.requestEmergencyWithdraw(amount);
      const receipt = await tx.wait();
      const event = receipt.logs.find(log => log.fragment?.name === "EmergencyWithdrawRequested");
      const requestId = event.args[0];
      
      // 快进7天
      await time.increase(7 * 24 * 3600);
      
      const initialBalance = await haoxToken.balanceOf(owner.address);
      
      await expect(vestingContract.executeEmergencyWithdraw(requestId))
        .to.emit(vestingContract, "EmergencyWithdrawExecuted");
      
      const finalBalance = await haoxToken.balanceOf(owner.address);
      expect(finalBalance - initialBalance).to.equal(amount);
    });
  });

  describe("Access Control", function () {
    it("Should allow owner to add emergency signer", async function () {
      await vestingContract.addEmergencySigner(addr1.address);
      expect(await vestingContract.emergencySigners(addr1.address)).to.be.true;
    });

    it("Should allow owner to remove emergency signer", async function () {
      await vestingContract.addEmergencySigner(addr1.address);
      await vestingContract.removeEmergencySigner(addr1.address);
      expect(await vestingContract.emergencySigners(addr1.address)).to.be.false;
    });

    it("Should allow owner to pause and unpause", async function () {
      await vestingContract.pause();
      expect(await vestingContract.paused()).to.be.true;
      
      await vestingContract.unpause();
      expect(await vestingContract.paused()).to.be.false;
    });

    it("Should prevent operations when paused", async function () {
      await vestingContract.pause();
      
      await expect(vestingContract.checkPriceCondition())
        .to.be.revertedWithCustomError(vestingContract, "EnforcedPause");
    });
  });

  describe("Access Control", function () {
    it("Should allow owner to add emergency signer", async function () {
      await vestingContract.addEmergencySigner(addr1.address);
      expect(await vestingContract.emergencySigners(addr1.address)).to.be.true;
    });

    it("Should allow owner to remove emergency signer", async function () {
      await vestingContract.addEmergencySigner(addr1.address);
      await vestingContract.removeEmergencySigner(addr1.address);
      expect(await vestingContract.emergencySigners(addr1.address)).to.be.false;
    });

    it("Should allow owner to pause and unpause", async function () {
      await vestingContract.pause();
      expect(await vestingContract.paused()).to.be.true;

      await vestingContract.unpause();
      expect(await vestingContract.paused()).to.be.false;
    });

    it("Should prevent operations when paused", async function () {
      await vestingContract.pause();

      await expect(vestingContract.checkPriceCondition())
        .to.be.revertedWithCustomError(vestingContract, "EnforcedPause");
    });

    it("Should prevent non-owner from adding signers", async function () {
      await expect(vestingContract.connect(addr1).addEmergencySigner(addr2.address))
        .to.be.revertedWithCustomError(vestingContract, "OwnableUnauthorizedAccount");
    });

    it("Should prevent non-owner from pausing", async function () {
      await expect(vestingContract.connect(addr1).pause())
        .to.be.revertedWithCustomError(vestingContract, "OwnableUnauthorizedAccount");
    });
  });

  describe("Multi-Round Testing", function () {
    it("Should handle multiple round unlocks", async function () {
      // 解锁前3轮
      for (let round = 1; round <= 3; round++) {
        // 设置足够高的价格来触发每一轮
        const roundPrices = [1000000, 1500000, 2000000]; // 0.01, 0.015, 0.02 USD (8位小数)
        await priceOracle.setPrice(roundPrices[round - 1]);
        await vestingContract.checkPriceCondition();
        await time.increase(7 * 24 * 3600);
        await vestingContract.checkPriceCondition(); // 这会触发解锁

        expect(await vestingContract.currentRound()).to.equal(round + 1);

        const roundData = await vestingContract.rounds(round);
        expect(roundData.unlocked).to.be.true;
      }
    });

    it("Should calculate correct token amounts for different rounds", async function () {
      const round1 = await vestingContract.rounds(1);
      const round10 = await vestingContract.rounds(10);
      const round31 = await vestingContract.rounds(31);

      expect(round1.triggerPrice).to.be.lt(round10.triggerPrice);
      expect(round10.triggerPrice).to.be.lt(round31.triggerPrice);
    });

    it("Should handle price condition reset across rounds", async function () {
      // 第1轮设置价格条件
      await priceOracle.setPrice(1000000); // 0.01 USD (8位小数)
      await vestingContract.checkPriceCondition();

      // 解锁第1轮
      await time.increase(7 * 24 * 3600);
      await vestingContract.checkPriceCondition(); // 这会触发解锁

      // 第2轮价格不足，应该重置（第2轮需要0.015）
      await priceOracle.setPrice(1000000); // 0.01 USD，低于第2轮的0.015要求
      await vestingContract.checkPriceCondition();

      const round2 = await vestingContract.rounds(2);
      expect(round2.priceConditionMet).to.be.false;
    });
  });

  describe("Edge Cases and Error Handling", function () {
    it("Should handle all 31 rounds correctly", async function () {
      expect(await vestingContract.TOTAL_ROUNDS()).to.equal(31);

      // 验证最后一轮的价格设置
      const lastRound = await vestingContract.rounds(31);
      expect(lastRound.triggerPrice).to.be.gt(0);
    });

    it("Should handle zero address checks", async function () {
      const HAOXVestingV2Minimal = await ethers.getContractFactory("HAOXVestingV2Minimal");

      await expect(HAOXVestingV2Minimal.deploy(
        ethers.ZeroAddress, // 零地址代币
        await priceOracle.getAddress(),
        projectWallet.address,
        communityWallet.address
      )).to.be.reverted;
    });

    it("Should handle price oracle failures gracefully", async function () {
      // 部署一个会失败的价格预言机
      const MockFailingOracle = await ethers.getContractFactory("HAOXPriceOracleV2");
      const failingOracle = await MockFailingOracle.deploy(
        ethers.ZeroAddress,
        ethers.ZeroAddress,
        ethers.ZeroAddress,
        ethers.ZeroAddress
      );

      const HAOXVestingV2Minimal = await ethers.getContractFactory("HAOXVestingV2Minimal");
      const testVesting = await HAOXVestingV2Minimal.deploy(
        await haoxToken.getAddress(),
        await failingOracle.getAddress(),
        projectWallet.address,
        communityWallet.address
      );

      // 价格检查应该处理失败情况
      await testVesting.checkPriceCondition();

      const round = await testVesting.rounds(1);
      expect(round.priceConditionMet).to.be.false;
    });

    it("Should handle large token amounts correctly", async function () {
      const contractBalance = await haoxToken.balanceOf(await vestingContract.getAddress());
      expect(contractBalance).to.be.gt(ethers.parseEther("100000000")); // 超过1亿代币
    });

    it("Should prevent reentrancy attacks", async function () {
      // 这个测试验证ReentrancyGuard的工作
      await priceOracle.setPrice(1000000); // 0.01 USD (8位小数)
      await vestingContract.checkPriceCondition();
      await time.increase(7 * 24 * 3600);

      // 正常解锁应该成功
      await vestingContract.checkPriceCondition(); // 这会触发解锁

      // 验证轮次已解锁
      const round = await vestingContract.rounds(1);
      expect(round.unlocked).to.be.true;
    });
  });

  describe("View Functions", function () {
    it("Should return correct round information", async function () {
      const round1 = await vestingContract.rounds(1);
      expect(round1.triggerPrice).to.equal(1000000); // 0.01 USD (8位小数)
      expect(round1.priceConditionMet).to.be.false;
      expect(round1.unlocked).to.be.false;
      expect(round1.priceReachedTime).to.equal(0);
    });

    it("Should return correct current round", async function () {
      expect(await vestingContract.currentRound()).to.equal(1);
    });

    it("Should return correct emergency signer status", async function () {
      expect(await vestingContract.emergencySigners(owner.address)).to.be.true;
      expect(await vestingContract.emergencySigners(addr1.address)).to.be.false;
    });

    it("Should return correct required signatures", async function () {
      expect(await vestingContract.requiredSignatures()).to.equal(1);
    });
  });
});
