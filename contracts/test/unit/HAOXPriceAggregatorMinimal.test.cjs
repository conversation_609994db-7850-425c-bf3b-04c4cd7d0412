const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("HAOXPriceAggregatorMinimal", function () {
  let priceAggregator;
  let mockOracle1;
  let mockOracle2;
  let mockOracle3;
  let owner;
  let addr1;

  beforeEach(async function () {
    [owner, addr1] = await ethers.getSigners();

    // 部署模拟价格预言机
    const MockPriceOracle = await ethers.getContractFactory("MockPriceOracle");
    mockOracle1 = await MockPriceOracle.deploy();
    mockOracle2 = await MockPriceOracle.deploy();
    mockOracle3 = await MockPriceOracle.deploy();

    await mockOracle1.waitForDeployment();
    await mockOracle2.waitForDeployment();
    await mockOracle3.waitForDeployment();

    // 部署价格聚合器
    const HAOXPriceAggregatorMinimal = await ethers.getContractFactory("HAOXPriceAggregatorMinimal");
    priceAggregator = await HAOXPriceAggregatorMinimal.deploy();
    await priceAggregator.waitForDeployment();

    // 添加价格源
    await priceAggregator.addPriceSource(await mockOracle1.getAddress(), 40);
    await priceAggregator.addPriceSource(await mockOracle2.getAddress(), 30);
    await priceAggregator.addPriceSource(await mockOracle3.getAddress(), 30);
  });

  describe("Deployment", function () {
    it("Should set owner correctly", async function () {
      expect(await priceAggregator.owner()).to.equal(owner.address);
    });

    it("Should have correct constants", async function () {
      expect(await priceAggregator.MAX_PRICE_DEVIATION()).to.equal(500); // 5%
      expect(await priceAggregator.PRICE_STALENESS_THRESHOLD()).to.equal(3600); // 1小时
      expect(await priceAggregator.MIN_SOURCES_REQUIRED()).to.equal(2);
      expect(await priceAggregator.MAX_SOURCES()).to.equal(5);
    });

    it("Should initialize with correct state", async function () {
      expect(await priceAggregator.emergencyMode()).to.be.false;
      expect(await priceAggregator.sourceCount()).to.equal(3); // 我们添加了3个源
    });

    it("Should have added price sources correctly", async function () {
      const source0 = await priceAggregator.getPriceSource(0);
      expect(source0.oracle).to.equal(await mockOracle1.getAddress());
      expect(source0.weight).to.equal(40);
      expect(source0.active).to.be.true;
    });
  });

  describe("Price Aggregation", function () {
    it("Should update aggregated price correctly", async function () {
      const price = 1000000; // 0.01 USD (8位小数)

      await mockOracle1.setPrice(price);
      await mockOracle2.setPrice(price);
      await mockOracle3.setPrice(price);

      await priceAggregator.updateAggregatedPrice();

      const latestPrice = await priceAggregator.getLatestPrice();
      expect(latestPrice).to.be.gt(0);
    });

    it("Should handle price deviations correctly", async function () {
      await mockOracle1.setPrice(1000000); // 0.01 USD
      await mockOracle2.setPrice(1050000); // 0.0105 USD (5%偏差)
      await mockOracle3.setPrice(1000000); // 0.01 USD

      await priceAggregator.updateAggregatedPrice();

      const latestPrice = await priceAggregator.getLatestPrice();
      expect(latestPrice).to.be.gt(0);
    });

    it("Should require minimum sources", async function () {
      // 禁用两个源，只留一个
      await priceAggregator.updatePriceSource(1, false, 30);
      await priceAggregator.updatePriceSource(2, false, 30);

      await mockOracle1.setPrice(1000000);

      await expect(priceAggregator.updateAggregatedPrice())
        .to.be.revertedWith("Insufficient valid sources");
    });

    it("Should emit PriceAggregated event", async function () {
      await mockOracle1.setPrice(1000000);
      await mockOracle2.setPrice(1000000);
      await mockOracle3.setPrice(1000000);

      await expect(priceAggregator.updateAggregatedPrice())
        .to.emit(priceAggregator, "PriceAggregated");
    });
  });

  describe("Emergency Mode", function () {
    it("Should allow owner to activate emergency mode", async function () {
      const emergencyPrice = 1200000; // 0.012 USD
      
      await expect(priceAggregator.activateEmergencyMode(emergencyPrice))
        .to.emit(priceAggregator, "EmergencyModeActivated")
        .withArgs(emergencyPrice);
      
      expect(await priceAggregator.emergencyMode()).to.be.true;
      expect(await priceAggregator.emergencyPrice()).to.equal(emergencyPrice);
    });

    it("Should return emergency price when in emergency mode", async function () {
      const emergencyPrice = 1200000;
      
      await priceAggregator.activateEmergencyMode(emergencyPrice);
      
      const aggregatedPrice = await priceAggregator.getAggregatedPrice();
      expect(aggregatedPrice).to.equal(emergencyPrice);
    });

    it("Should allow owner to deactivate emergency mode", async function () {
      await priceAggregator.activateEmergencyMode(1200000);
      
      await expect(priceAggregator.deactivateEmergencyMode())
        .to.emit(priceAggregator, "EmergencyModeDeactivated");
      
      expect(await priceAggregator.emergencyMode()).to.be.false;
    });

    it("Should prevent non-owner from activating emergency mode", async function () {
      await expect(
        priceAggregator.connect(addr1).activateEmergencyMode(1200000)
      ).to.be.revertedWithCustomError(priceAggregator, "OwnableUnauthorizedAccount");
    });
  });

  describe("Access Control", function () {
    it("Should allow owner to pause and unpause", async function () {
      await priceAggregator.pause();
      expect(await priceAggregator.paused()).to.be.true;
      
      await priceAggregator.unpause();
      expect(await priceAggregator.paused()).to.be.false;
    });

    it("Should prevent operations when paused", async function () {
      await priceAggregator.pause();
      
      await expect(priceAggregator.getAggregatedPrice())
        .to.be.revertedWithCustomError(priceAggregator, "EnforcedPause");
    });

    it("Should prevent non-owner from pausing", async function () {
      await expect(priceAggregator.connect(addr1).pause())
        .to.be.revertedWithCustomError(priceAggregator, "OwnableUnauthorizedAccount");
    });
  });

  describe("View Functions", function () {
    it("Should return individual oracle prices", async function () {
      await mockOracle1.setPrice(1000000);
      await mockOracle2.setPrice(1050000);
      await mockOracle3.setPrice(1100000);
      
      const prices = await priceAggregator.getOraclePrices();
      expect(prices[0]).to.equal(1000000);
      expect(prices[1]).to.equal(1050000);
      expect(prices[2]).to.equal(1100000);
    });

    it("Should return price deviation status", async function () {
      await mockOracle1.setPrice(1000000);
      await mockOracle2.setPrice(1000000);
      await mockOracle3.setPrice(1000000);
      
      const hasDeviation = await priceAggregator.hasPriceDeviation();
      expect(hasDeviation).to.be.false;
    });

    it("Should detect price deviations", async function () {
      await mockOracle1.setPrice(1000000); // 0.01 USD
      await mockOracle2.setPrice(1100000); // 0.011 USD (10%偏差)
      await mockOracle3.setPrice(1000000); // 0.01 USD
      
      const hasDeviation = await priceAggregator.hasPriceDeviation();
      expect(hasDeviation).to.be.true;
    });
  });

  describe("Edge Cases", function () {
    it("Should handle zero prices", async function () {
      await mockOracle1.setPrice(0);
      await mockOracle2.setPrice(1000000);
      await mockOracle3.setPrice(1000000);
      
      const aggregatedPrice = await priceAggregator.getAggregatedPrice();
      expect(aggregatedPrice).to.be.gt(0);
    });

    it("Should handle very large prices", async function () {
      const largePrice = ethers.parseUnits("1000", 8); // 1000 USD
      
      await mockOracle1.setPrice(largePrice);
      await mockOracle2.setPrice(largePrice);
      await mockOracle3.setPrice(largePrice);
      
      const aggregatedPrice = await priceAggregator.getLatestPrice();
      expect(aggregatedPrice).to.equal(largePrice);
    });

    it("Should handle emergency price of zero", async function () {
      await expect(priceAggregator.activateEmergencyMode(0))
        .to.be.revertedWith("Invalid price");
    });
  });
});
