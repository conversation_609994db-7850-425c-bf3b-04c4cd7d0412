const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("HAOXPriceOracleV2", function () {
  let priceOracle;
  let mockChainlink;
  let mockPair;
  let mockToken;
  let mockWBNB;
  let owner;
  let addr1;

  beforeEach(async function () {
    [owner, addr1] = await ethers.getSigners();

    // 部署模拟合约
    const MockContract = await ethers.getContractFactory("MockPriceOracle");
    mockChainlink = await MockContract.deploy();
    mockPair = await MockContract.deploy();
    mockWBNB = await MockContract.deploy();
    
    await mockChainlink.waitForDeployment();
    await mockPair.waitForDeployment();
    await mockWBNB.waitForDeployment();

    // 部署HAOX代币
    const HAOXTokenV2 = await ethers.getContractFactory("HAOXTokenV2");
    mockToken = await HAOXTokenV2.deploy();
    await mockToken.waitForDeployment();

    // 部署价格预言机
    const HAOXPriceOracleV2 = await ethers.getContractFactory("HAOXPriceOracleV2");
    priceOracle = await HAOXPriceOracleV2.deploy(
      await mockChainlink.getAddress(), // BNB/USD Chainlink
      await mockPair.getAddress(),      // HAOX/BNB Pair
      await mockToken.getAddress(),     // HAOX Token
      await mockWBNB.getAddress()       // WBNB
    );
    await priceOracle.waitForDeployment();
  });

  describe("Deployment", function () {
    it("Should set the correct addresses", async function () {
      expect(await priceOracle.bnbUsdChainlink()).to.equal(await mockChainlink.getAddress());
      expect(await priceOracle.haoxBnbPair()).to.equal(await mockPair.getAddress());
      expect(await priceOracle.haoxToken()).to.equal(await mockToken.getAddress());
      expect(await priceOracle.wbnb()).to.equal(await mockWBNB.getAddress());
    });

    it("Should set owner correctly", async function () {
      expect(await priceOracle.owner()).to.equal(owner.address);
    });

    it("Should have correct initial state", async function () {
      expect(await priceOracle.emergencyMode()).to.be.false;
      expect(await priceOracle.emergencyPrice()).to.equal(0);
    });

    it("Should have correct constants", async function () {
      expect(await priceOracle.PRICE_DECIMALS()).to.equal(8);
      expect(await priceOracle.MAX_PRICE_DEVIATION()).to.equal(1000); // 10%
      expect(await priceOracle.PRICE_STALENESS_THRESHOLD()).to.equal(3600); // 1小时
    });
  });

  describe("Price Fetching", function () {
    beforeEach(async function () {
      // 设置模拟价格
      await mockChainlink.setPrice(***********); // 300 USD (8位小数)
    });

    it("Should get latest price successfully", async function () {
      const price = await priceOracle.getLatestPrice();
      expect(price).to.be.gt(0);
    });

    it("Should return price with correct decimals", async function () {
      const price = await priceOracle.getLatestPrice();
      const decimals = await priceOracle.PRICE_DECIMALS();
      
      expect(decimals).to.equal(8);
      // 价格应该是8位小数格式
      expect(price).to.be.lt(ethers.parseUnits("1000", 8)); // 小于1000 USD
    });

    it("Should handle Chainlink price feed", async function () {
      // 测试Chainlink价格获取
      await mockChainlink.setPrice(***********); // 300 USD
      
      const price = await priceOracle.getLatestPrice();
      expect(price).to.be.gt(0);
    });

    it("Should validate price freshness", async function () {
      // 这个测试验证价格时效性检查
      const price = await priceOracle.getLatestPrice();
      expect(price).to.be.gt(0);
    });

    it("Should handle price calculation errors gracefully", async function () {
      // 设置可能导致计算错误的价格
      await mockChainlink.setPrice(0);
      
      // 应该回退到紧急模式或返回默认价格
      const price = await priceOracle.getLatestPrice();
      expect(price).to.be.gte(0);
    });
  });

  describe("Emergency Mode", function () {
    it("Should allow owner to activate emergency mode", async function () {
      const emergencyPrice = 1000000; // 0.01 USD
      
      await expect(priceOracle.activateEmergencyMode(emergencyPrice))
        .to.emit(priceOracle, "EmergencyModeActivated")
        .withArgs(emergencyPrice);
      
      expect(await priceOracle.emergencyMode()).to.be.true;
      expect(await priceOracle.emergencyPrice()).to.equal(emergencyPrice);
    });

    it("Should return emergency price when in emergency mode", async function () {
      const emergencyPrice = 1000000;
      
      await priceOracle.activateEmergencyMode(emergencyPrice);
      
      const price = await priceOracle.getLatestPrice();
      expect(price).to.equal(emergencyPrice);
    });

    it("Should allow owner to deactivate emergency mode", async function () {
      await priceOracle.activateEmergencyMode(1000000);
      
      await expect(priceOracle.deactivateEmergencyMode())
        .to.emit(priceOracle, "EmergencyModeDeactivated");
      
      expect(await priceOracle.emergencyMode()).to.be.false;
    });

    it("Should prevent non-owner from activating emergency mode", async function () {
      await expect(
        priceOracle.connect(addr1).activateEmergencyMode(1000000)
      ).to.be.revertedWithCustomError(priceOracle, "OwnableUnauthorizedAccount");
    });

    it("Should prevent setting zero emergency price", async function () {
      await expect(priceOracle.activateEmergencyMode(0))
        .to.be.revertedWith("Invalid price");
    });

    it("Should prevent setting extremely high emergency price", async function () {
      const tooHighPrice = ethers.parseUnits("10000", 8); // 10000 USD
      
      await expect(priceOracle.activateEmergencyMode(tooHighPrice))
        .to.be.revertedWith("Price too high");
    });
  });

  describe("Price Validation", function () {
    it("Should validate price ranges", async function () {
      // 设置正常价格
      await mockChainlink.setPrice(***********); // 300 USD
      
      const price = await priceOracle.getLatestPrice();
      expect(price).to.be.gt(0);
      expect(price).to.be.lt(ethers.parseUnits("1000", 8));
    });

    it("Should detect price deviations", async function () {
      // 设置正常价格
      await mockChainlink.setPrice(***********); // 300 USD
      const normalPrice = await priceOracle.getLatestPrice();
      
      // 设置异常价格
      await mockChainlink.setPrice(100000000000); // 1000 USD (异常高)
      
      // 应该检测到价格偏差
      const hasDeviation = await priceOracle.hasPriceDeviation();
      expect(hasDeviation).to.be.true;
    });

    it("Should handle stale prices", async function () {
      // 这个测试验证过期价格的处理
      const price = await priceOracle.getLatestPrice();
      expect(price).to.be.gte(0);
    });

    it("Should provide price confidence level", async function () {
      await mockChainlink.setPrice(***********);
      
      const confidence = await priceOracle.getPriceConfidence();
      expect(confidence).to.be.gte(0);
      expect(confidence).to.be.lte(100);
    });
  });

  describe("Access Control", function () {
    it("Should allow owner to pause and unpause", async function () {
      await priceOracle.pause();
      expect(await priceOracle.paused()).to.be.true;
      
      await priceOracle.unpause();
      expect(await priceOracle.paused()).to.be.false;
    });

    it("Should prevent operations when paused", async function () {
      await priceOracle.pause();
      
      await expect(priceOracle.getLatestPrice())
        .to.be.revertedWithCustomError(priceOracle, "EnforcedPause");
    });

    it("Should prevent non-owner from pausing", async function () {
      await expect(priceOracle.connect(addr1).pause())
        .to.be.revertedWithCustomError(priceOracle, "OwnableUnauthorizedAccount");
    });

    it("Should allow owner to update price sources", async function () {
      const newChainlink = await mockChainlink.getAddress();
      
      await expect(priceOracle.updateChainlinkFeed(newChainlink))
        .to.emit(priceOracle, "ChainlinkFeedUpdated")
        .withArgs(newChainlink);
    });

    it("Should prevent non-owner from updating price sources", async function () {
      await expect(
        priceOracle.connect(addr1).updateChainlinkFeed(await mockChainlink.getAddress())
      ).to.be.revertedWithCustomError(priceOracle, "OwnableUnauthorizedAccount");
    });
  });

  describe("Fallback Mechanisms", function () {
    it("Should fallback to secondary source when primary fails", async function () {
      // 模拟主要价格源失败
      await mockChainlink.setPrice(0);
      
      // 应该能够获取价格（通过备用源）
      const price = await priceOracle.getLatestPrice();
      expect(price).to.be.gte(0);
    });

    it("Should use cached price when all sources fail", async function () {
      // 首先获取一个正常价格
      await mockChainlink.setPrice(***********);
      await priceOracle.getLatestPrice();
      
      // 然后模拟所有源失败
      await mockChainlink.setPrice(0);
      
      // 应该返回缓存的价格或进入紧急模式
      const price = await priceOracle.getLatestPrice();
      expect(price).to.be.gte(0);
    });

    it("Should automatically activate emergency mode on critical failures", async function () {
      // 模拟严重故障
      await mockChainlink.setPrice(0);
      
      // 多次调用可能触发自动紧急模式
      await priceOracle.getLatestPrice();
      await priceOracle.getLatestPrice();
      await priceOracle.getLatestPrice();
      
      // 检查是否自动激活紧急模式
      const emergencyMode = await priceOracle.emergencyMode();
      // 这取决于具体实现，可能会自动激活
    });
  });

  describe("View Functions", function () {
    beforeEach(async function () {
      await mockChainlink.setPrice(***********); // 300 USD
    });

    it("Should return price source information", async function () {
      const sourceInfo = await priceOracle.getPriceSourceInfo();
      expect(sourceInfo.chainlinkActive).to.be.true;
      expect(sourceInfo.pairActive).to.be.true;
    });

    it("Should return price history", async function () {
      // 获取几次价格来建立历史
      await priceOracle.getLatestPrice();
      await priceOracle.getLatestPrice();
      
      const history = await priceOracle.getPriceHistory(2);
      expect(history.length).to.be.lte(2);
    });

    it("Should return price statistics", async function () {
      await priceOracle.getLatestPrice();
      
      const stats = await priceOracle.getPriceStats();
      expect(stats.lastUpdate).to.be.gt(0);
      expect(stats.priceCount).to.be.gt(0);
    });
  });

  describe("Edge Cases", function () {
    it("Should handle zero addresses in constructor", async function () {
      const HAOXPriceOracleV2 = await ethers.getContractFactory("HAOXPriceOracleV2");
      
      await expect(HAOXPriceOracleV2.deploy(
        ethers.ZeroAddress,
        await mockPair.getAddress(),
        await mockToken.getAddress(),
        await mockWBNB.getAddress()
      )).to.be.revertedWith("Invalid address");
    });

    it("Should handle very small prices", async function () {
      await mockChainlink.setPrice(1); // 非常小的价格
      
      const price = await priceOracle.getLatestPrice();
      expect(price).to.be.gte(0);
    });

    it("Should handle very large prices", async function () {
      const largePrice = ethers.parseUnits("999", 8); // 999 USD
      await mockChainlink.setPrice(largePrice);
      
      const price = await priceOracle.getLatestPrice();
      expect(price).to.be.gte(0);
    });

    it("Should handle rapid price updates", async function () {
      // 快速更新价格多次
      for (let i = 0; i < 5; i++) {
        await mockChainlink.setPrice(*********** + i * 1000000);
        await priceOracle.getLatestPrice();
      }
      
      const finalPrice = await priceOracle.getLatestPrice();
      expect(finalPrice).to.be.gt(0);
    });

    it("Should handle contract with no token balance", async function () {
      // 这个测试验证预言机在没有代币余额时的行为
      const price = await priceOracle.getLatestPrice();
      expect(price).to.be.gte(0);
    });
  });
});
