{"_format": "hh-sol-cache-2", "files": {"/Users/<USER>/Desktop/sociomint222/node_modules/@openzeppelin/contracts/utils/Pausable.sol": {"lastModificationDate": 1753263243919, "contentHash": "0d47b53e10b1985efbb396f937626279", "sourceName": "@openzeppelin/contracts/utils/Pausable.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../utils/Context.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["Pausable"]}, "/Users/<USER>/Desktop/sociomint222/node_modules/@openzeppelin/contracts/utils/ReentrancyGuard.sol": {"lastModificationDate": 1753263243955, "contentHash": "190613e556d509d9e9a0ea43dc5d891d", "sourceName": "@openzeppelin/contracts/utils/ReentrancyGuard.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["Reentrancy<PERSON><PERSON>"]}, "/Users/<USER>/Desktop/sociomint222/node_modules/@openzeppelin/contracts/access/Ownable.sol": {"lastModificationDate": 1753263243859, "contentHash": "d3c790edc9ccf808a17c5a6cd13614fd", "sourceName": "@openzeppelin/contracts/access/Ownable.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../utils/Context.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["Ownable"]}, "/Users/<USER>/Desktop/sociomint222/node_modules/@openzeppelin/contracts/token/ERC20/ERC20.sol": {"lastModificationDate": 1753263234868, "contentHash": "59dfce11284f2636db261df9b6a18f81", "sourceName": "@openzeppelin/contracts/token/ERC20/ERC20.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./IERC20.sol", "./extensions/IERC20Metadata.sol", "../../utils/Context.sol", "../../interfaces/draft-IERC6093.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ERC20"]}, "/Users/<USER>/Desktop/sociomint222/node_modules/@openzeppelin/contracts/utils/Context.sol": {"lastModificationDate": 1753263227893, "contentHash": "67bfbc07588eb8683b3fd8f6f909563e", "sourceName": "@openzeppelin/contracts/utils/Context.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["Context"]}, "/Users/<USER>/Desktop/sociomint222/node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol": {"lastModificationDate": 1753263242395, "contentHash": "9261adf6457863de3e9892f51317ec89", "sourceName": "@openzeppelin/contracts/token/ERC20/IERC20.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": [">=0.4.16"], "artifacts": ["IERC20"]}, "/Users/<USER>/Desktop/sociomint222/node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"lastModificationDate": 1753263242413, "contentHash": "513778b30d2750f5d2b9b19bbcf748a5", "sourceName": "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../IERC20.sol"], "versionPragmas": [">=0.6.2"], "artifacts": ["IERC20Metadata"]}, "/Users/<USER>/Desktop/sociomint222/node_modules/@openzeppelin/contracts/interfaces/draft-IERC6093.sol": {"lastModificationDate": 1753263230986, "contentHash": "5041977bbe908de2e6ed0270447f79ad", "sourceName": "@openzeppelin/contracts/interfaces/draft-IERC6093.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": [">=0.8.4"], "artifacts": ["IERC1155Errors", "IERC20Errors", "IERC721Errors"]}, "/Users/<USER>/Desktop/sociomint222/contracts/contracts/FailingPriceOracle.sol": {"lastModificationDate": 1754114388094, "contentHash": "6c79907838419c11905daec83823fff1", "sourceName": "contracts/FailingPriceOracle.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["FailingPriceOracle"]}, "/Users/<USER>/Desktop/sociomint222/contracts/contracts/HAOXInvitationV2.sol": {"lastModificationDate": 1753775062716, "contentHash": "ed5bd248545dd977d0f15edbee865baf", "sourceName": "contracts/HAOXInvitationV2.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/utils/ReentrancyGuard.sol", "@openzeppelin/contracts/utils/Pausable.sol", "@openzeppelin/contracts/access/Ownable.sol", "./HAOXTokenV2.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["HAOXInvitationV2"]}, "/Users/<USER>/Desktop/sociomint222/contracts/contracts/HAOXTokenV2.sol": {"lastModificationDate": 1753775107216, "contentHash": "b364a00d3645714492e9212a85197780", "sourceName": "contracts/HAOXTokenV2.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/token/ERC20/ERC20.sol", "@openzeppelin/contracts/utils/Pausable.sol", "@openzeppelin/contracts/access/Ownable.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["HAOXTokenV2"]}, "/Users/<USER>/Desktop/sociomint222/contracts/contracts/HAOXPresaleV2.sol": {"lastModificationDate": 1753775419399, "contentHash": "7ba838f4e1da038f90312c0359de4372", "sourceName": "contracts/HAOXPresaleV2.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/utils/ReentrancyGuard.sol", "@openzeppelin/contracts/utils/Pausable.sol", "@openzeppelin/contracts/access/Ownable.sol", "./HAOXTokenV2.sol", "./HAOXInvitationV2.sol", "./HAOXPriceOracleV2.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["HAOXPresaleV2"]}, "/Users/<USER>/Desktop/sociomint222/contracts/contracts/HAOXPriceOracleV2.sol": {"lastModificationDate": 1753779606671, "contentHash": "f4e1ca100f5d3e24cc45058bf4bc0902", "sourceName": "contracts/HAOXPriceOracleV2.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/access/Ownable.sol", "@openzeppelin/contracts/utils/Pausable.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["AggregatorV3Interface", "HAOXPriceOracleV2", "IPancakeSwapPair"]}, "/Users/<USER>/Desktop/sociomint222/contracts/contracts/HAOXVestingV2Ultra.sol": {"lastModificationDate": 1753951701905, "contentHash": "1b81278048d49dd445745cc41fe02626", "sourceName": "contracts/HAOXVestingV2Ultra.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/token/ERC20/IERC20.sol", "@openzeppelin/contracts/access/Ownable.sol", "@openzeppelin/contracts/utils/ReentrancyGuard.sol", "@openzeppelin/contracts/utils/Pausable.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["HAOXVestingV2Ultra"]}, "/Users/<USER>/Desktop/sociomint222/contracts/contracts/HAOXVestingV2Minimal.sol": {"lastModificationDate": 1754117360846, "contentHash": "5089e0fb20b18c9f37f8f22125ed2bf5", "sourceName": "contracts/HAOXVestingV2Minimal.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/token/ERC20/IERC20.sol", "@openzeppelin/contracts/access/Ownable.sol", "@openzeppelin/contracts/utils/ReentrancyGuard.sol", "@openzeppelin/contracts/utils/Pausable.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["HAOXVestingV2Minimal"]}, "/Users/<USER>/Desktop/sociomint222/contracts/contracts/HAOXPriceAggregatorMinimal.sol": {"lastModificationDate": 1753928269431, "contentHash": "ecc1af07b20cb01696cb16495db2e62b", "sourceName": "contracts/HAOXPriceAggregatorMinimal.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/access/Ownable.sol", "@openzeppelin/contracts/utils/Pausable.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["HAOXPriceAggregatorMinimal"]}, "/Users/<USER>/Desktop/sociomint222/contracts/contracts/MockERC20.sol": {"lastModificationDate": 1754114377595, "contentHash": "1a839c18b43859e0dce63b6c33156941", "sourceName": "contracts/MockERC20.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/token/ERC20/ERC20.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["MockERC20"]}, "/Users/<USER>/Desktop/sociomint222/contracts/contracts/MockPriceOracle.sol": {"lastModificationDate": 1754054136529, "contentHash": "4786c1db11eb035b0aca921c772db102", "sourceName": "contracts/MockPriceOracle.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.19"], "artifacts": ["MockPriceO<PERSON>le"]}}}