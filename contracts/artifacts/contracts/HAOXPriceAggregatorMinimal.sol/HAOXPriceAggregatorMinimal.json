{"_format": "hh-sol-artifact-1", "contractName": "HAOXPriceAggregatorMinimal", "sourceName": "contracts/HAOXPriceAggregatorMinimal.sol", "abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "EnforcedPause", "type": "error"}, {"inputs": [], "name": "ExpectedPause", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "price", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "EmergencyModeActivated", "type": "event"}, {"anonymous": false, "inputs": [], "name": "EmergencyModeDeactivated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "price", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"indexed": false, "internalType": "uint8", "name": "sourceCount", "type": "uint8"}], "name": "PriceAggregated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint8", "name": "sourceId", "type": "uint8"}, {"indexed": true, "internalType": "address", "name": "oracle", "type": "address"}, {"indexed": false, "internalType": "uint8", "name": "weight", "type": "uint8"}], "name": "PriceSourceAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint8", "name": "sourceId", "type": "uint8"}, {"indexed": false, "internalType": "bool", "name": "active", "type": "bool"}, {"indexed": false, "internalType": "uint8", "name": "weight", "type": "uint8"}], "name": "PriceSourceUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"inputs": [], "name": "MAX_PRICE_DEVIATION", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_SOURCES", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MIN_SOURCES_REQUIRED", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PRICE_STALENESS_THRESHOLD", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "price", "type": "uint256"}], "name": "activateEmergencyMode", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "oracle", "type": "address"}, {"internalType": "uint8", "name": "weight", "type": "uint8"}], "name": "addPriceSource", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint8[]", "name": "sourceIds", "type": "uint8[]"}, {"internalType": "bool[]", "name": "activeStates", "type": "bool[]"}], "name": "batchUpdateSources", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "deactivateEmergencyMode", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "emergencyMode", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "emergencyPause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "emergencyPrice", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "emergencyPriceTimestamp", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getAggregatorStatus", "outputs": [{"internalType": "uint8", "name": "totalSources", "type": "uint8"}, {"internalType": "uint8", "name": "activeSources", "type": "uint8"}, {"internalType": "bool", "name": "isEmergencyMode", "type": "bool"}, {"internalType": "uint256", "name": "lastUpdate", "type": "uint256"}, {"internalType": "uint256", "name": "currentPrice", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getLastUpdateTime", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getLatestPrice", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint8", "name": "sourceId", "type": "uint8"}], "name": "getPriceSource", "outputs": [{"internalType": "address", "name": "oracle", "type": "address"}, {"internalType": "uint8", "name": "weight", "type": "uint8"}, {"internalType": "bool", "name": "active", "type": "bool"}, {"internalType": "uint32", "name": "lastUpdate", "type": "uint32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "lastSourceCount", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "lastUpdateTime", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "latestPrice", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "priceSources", "outputs": [{"internalType": "address", "name": "oracle", "type": "address"}, {"internalType": "uint8", "name": "weight", "type": "uint8"}, {"internalType": "bool", "name": "active", "type": "bool"}, {"internalType": "uint32", "name": "lastUpdate", "type": "uint32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "sourceCount", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "updateAggregatedPrice", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint8", "name": "sourceId", "type": "uint8"}, {"internalType": "bool", "name": "active", "type": "bool"}, {"internalType": "uint8", "name": "weight", "type": "uint8"}], "name": "updatePriceSource", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}