{"_format": "hh-sol-artifact-1", "contractName": "HAOXTokenV2", "sourceName": "contracts/HAOXTokenV2.sol", "abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientAllowance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "name": "ERC20InvalidApprover", "type": "error"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "name": "ERC20InvalidReceiver", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "ERC20InvalidSender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "name": "ERC20InvalidSpender", "type": "error"}, {"inputs": [], "name": "EnforcedPause", "type": "error"}, {"inputs": [], "name": "ExpectedPause", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "string", "name": "contractType", "type": "string"}, {"indexed": false, "internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"indexed": false, "internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "name": "ContractAddressUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "duration", "type": "uint256"}], "name": "EmergencyPauseActivated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "lockId", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "unlockTime", "type": "uint256"}], "name": "TimeLockCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "lockId", "type": "bytes32"}], "name": "TimeLockExecuted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"inputs": [], "name": "INITIAL_UNLOCK", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "LOCKED_SUPPLY", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_PAUSE_DURATION", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PRESALE_ALLOCATION", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PROJECT_RESERVE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "TIMELOCK_DELAY", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "TOTAL_SUPPLY", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "autoUnpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "emergencyPause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getContractStatus", "outputs": [{"internalType": "bool", "name": "isPaused", "type": "bool"}, {"internalType": "uint256", "name": "pauseDuration", "type": "uint256"}, {"internalType": "uint256", "name": "remainingPauseTime", "type": "uint256"}, {"internalType": "address", "name": "presale", "type": "address"}, {"internalType": "address", "name": "invitation", "type": "address"}, {"internalType": "address", "name": "vesting", "type": "address"}, {"internalType": "address", "name": "oracle", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "invitationContract", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pauseStartTime", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "presaleContract", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "priceOracle", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_invitationContract", "type": "address"}], "name": "setInvitationContract", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_presaleContract", "type": "address"}], "name": "setPresaleContract", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_priceOracle", "type": "address"}], "name": "setPriceO<PERSON>le", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_vestingContract", "type": "address"}], "name": "setVestingContract", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "name": "timeLocks", "outputs": [{"internalType": "uint256", "name": "unlockTime", "type": "uint256"}, {"internalType": "bool", "name": "executed", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "vestingContract", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}