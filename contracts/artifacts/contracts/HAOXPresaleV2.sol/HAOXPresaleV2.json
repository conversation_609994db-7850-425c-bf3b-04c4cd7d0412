{"_format": "hh-sol-artifact-1", "contractName": "HAOXPresaleV2", "sourceName": "contracts/HAOXPresaleV2.sol", "abi": [{"inputs": [{"internalType": "address", "name": "_haoxToken", "type": "address"}, {"internalType": "address", "name": "_invitationContract", "type": "address"}, {"internalType": "address", "name": "_priceOracle", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "EnforcedPause", "type": "error"}, {"inputs": [], "name": "ExpectedPause", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "totalBNBRaised", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "totalTokensSold", "type": "uint256"}], "name": "PresaleEnded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "stage", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "totalTokensSold", "type": "uint256"}], "name": "StageCompleted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "buyer", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "bnbAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "tokenAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "avgRate", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "startStage", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "endStage", "type": "uint256"}], "name": "TokensPurchased", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"inputs": [], "name": "INITIAL_RATE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_INVESTMENT", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MIN_INVESTMENT", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "RATE_DECREASE_PERCENT", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "TARGET_BNB", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "TOKENS_PER_STAGE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "TOTAL_PRESALE_TOKENS", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "TOTAL_STAGES", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "users", "type": "address[]"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "inviter", "type": "address"}], "name": "buyTokens", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "bnbAmount", "type": "uint256"}], "name": "calculateCrossStageTokens", "outputs": [{"internalType": "uint256", "name": "tokenAmount", "type": "uint256"}, {"internalType": "uint256", "name": "avgRate", "type": "uint256"}, {"internalType": "uint256", "name": "endStage", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "currentStage", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getCurrentRate", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getPresaleStatus", "outputs": [{"internalType": "uint256", "name": "_currentStage", "type": "uint256"}, {"internalType": "uint256", "name": "_tokensRemainingInCurrentStage", "type": "uint256"}, {"internalType": "uint256", "name": "_totalBNBRaised", "type": "uint256"}, {"internalType": "uint256", "name": "_totalTokensSold", "type": "uint256"}, {"internalType": "uint256", "name": "_currentRate", "type": "uint256"}, {"internalType": "bool", "name": "_isActive", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "stage", "type": "uint256"}], "name": "getStageRate", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "haoxToken", "outputs": [{"internalType": "contract HAOXTokenV2", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "initializeRates", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "investments", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "invitationContract", "outputs": [{"internalType": "contract HAOXInvitationV2", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "presaleActive", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "priceOracle", "outputs": [{"internalType": "contract HAOXPriceOracleV2", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "tokensPurchased", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "tokensRemainingInCurrentStage", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalBNBRaised", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalTokensSold", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "whitelist", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "withdrawBNB", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}