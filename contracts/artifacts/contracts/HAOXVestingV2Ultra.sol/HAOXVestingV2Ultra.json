{"_format": "hh-sol-artifact-1", "contractName": "HAOXVestingV2Ultra", "sourceName": "contracts/HAOXVestingV2Ultra.sol", "abi": [{"inputs": [{"internalType": "address", "name": "_haoxToken", "type": "address"}, {"internalType": "address", "name": "_priceOracle", "type": "address"}, {"internalType": "address", "name": "_projectWallet", "type": "address"}, {"internalType": "address", "name": "_communityWallet", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "EnforcedPause", "type": "error"}, {"inputs": [], "name": "ExpectedPause", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "requestId", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "EmergencyWithdrawExecuted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "requestId", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "EmergencyWithdrawRequested", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "roundNumber", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "price", "type": "uint256"}], "name": "PriceConditionMet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "roundNumber", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "RoundUnlocked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"inputs": [], "name": "EMERGENCY_DELAY", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_EMERGENCY_AMOUNT", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PRICE_MAINTAIN_DURATION", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "TOTAL_ROUNDS", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "checkPriceCondition", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "communityWallet", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "currentRound", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "name": "emergencyRequests", "outputs": [{"internalType": "uint128", "name": "amount", "type": "uint128"}, {"internalType": "uint64", "name": "requestTime", "type": "uint64"}, {"internalType": "bool", "name": "executed", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "emergencySigners", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "requestId", "type": "bytes32"}, {"internalType": "address", "name": "token", "type": "address"}], "name": "executeEmergencyWithdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getCurrentPrice", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "roundNumber", "type": "uint256"}], "name": "getRoundInfo", "outputs": [{"internalType": "uint256", "name": "triggerPrice", "type": "uint256"}, {"internalType": "bool", "name": "priceConditionMet", "type": "bool"}, {"internalType": "bool", "name": "unlocked", "type": "bool"}, {"internalType": "uint256", "name": "priceReachedTime", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getUnlockProgress", "outputs": [{"internalType": "uint256", "name": "totalRounds", "type": "uint256"}, {"internalType": "uint256", "name": "currentRoundNumber", "type": "uint256"}, {"internalType": "uint256", "name": "unlockedRounds", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "haoxToken", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "priceOracle", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "projectWallet", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "requestEmergencyWithdraw", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "requiredSignatures", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "rounds", "outputs": [{"internalType": "uint128", "name": "triggerPrice", "type": "uint128"}, {"internalType": "uint64", "name": "priceReachedTime", "type": "uint64"}, {"internalType": "bool", "name": "priceConditionMet", "type": "bool"}, {"internalType": "bool", "name": "unlocked", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "signer", "type": "address"}, {"internalType": "bool", "name": "status", "type": "bool"}], "name": "setEmergency<PERSON><PERSON>er", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_required", "type": "uint256"}], "name": "setRequiredSignatures", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}