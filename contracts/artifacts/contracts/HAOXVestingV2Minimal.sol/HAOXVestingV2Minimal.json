{"_format": "hh-sol-artifact-1", "contractName": "HAOXVestingV2Minimal", "sourceName": "contracts/HAOXVestingV2Minimal.sol", "abi": [{"inputs": [{"internalType": "address", "name": "_haoxToken", "type": "address"}, {"internalType": "address", "name": "_priceOracle", "type": "address"}, {"internalType": "address", "name": "_projectWallet", "type": "address"}, {"internalType": "address", "name": "_communityWallet", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "AlreadyExecuted", "type": "error"}, {"inputs": [], "name": "Amount<PERSON>ooLarge", "type": "error"}, {"inputs": [], "name": "EnforcedPause", "type": "error"}, {"inputs": [], "name": "ExpectedPause", "type": "error"}, {"inputs": [], "name": "InvalidAddress", "type": "error"}, {"inputs": [], "name": "InvalidRound", "type": "error"}, {"inputs": [], "name": "NotAuthorized", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "PriceOracleFailed", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"inputs": [], "name": "RequestNotFound", "type": "error"}, {"inputs": [], "name": "TimeLockActive", "type": "error"}, {"inputs": [], "name": "TransferFailed", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "requestId", "type": "bytes32"}, {"indexed": true, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "EmergencyWithdrawExecuted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "requestId", "type": "bytes32"}, {"indexed": true, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "requestTime", "type": "uint256"}], "name": "EmergencyWithdrawRequested", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "roundNumber", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "price", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "PriceConditionMet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "roundNumber", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "price", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "PriceConditionReset", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "roundNumber", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "triggerPrice", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "projectTokens", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "communityTokens", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "RoundUnlocked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"inputs": [], "name": "COMMUNITY_WALLET", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "EMERGENCY_DELAY", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "HAOX_TOKEN", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_EMERGENCY_AMOUNT", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PRICE_MAINTAIN_DURATION", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PRICE_ORACLE", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PROJECT_WALLET", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "TOTAL_ROUNDS", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "signer", "type": "address"}], "name": "addEmergencySigner", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "checkPriceCondition", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "currentRound", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "emergencyPause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "name": "emergencyRequests", "outputs": [{"internalType": "uint128", "name": "amount", "type": "uint128"}, {"internalType": "uint64", "name": "requestTime", "type": "uint64"}, {"internalType": "bool", "name": "executed", "type": "bool"}, {"internalType": "address", "name": "requester", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "emergencySigners", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "requestId", "type": "bytes32"}], "name": "executeEmergencyWithdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "roundNumber", "type": "uint256"}], "name": "getPriceHistory", "outputs": [{"components": [{"internalType": "uint64", "name": "timestamp", "type": "uint64"}, {"internalType": "uint64", "name": "price", "type": "uint64"}, {"internalType": "bool", "name": "conditionMet", "type": "bool"}], "internalType": "struct HAOXVestingV2Minimal.PriceCheck[10]", "name": "", "type": "tuple[10]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "roundNumber", "type": "uint256"}], "name": "getRoundInfo", "outputs": [{"internalType": "uint256", "name": "triggerPrice", "type": "uint256"}, {"internalType": "bool", "name": "priceConditionMet", "type": "bool"}, {"internalType": "bool", "name": "unlocked", "type": "bool"}, {"internalType": "uint256", "name": "priceReachedTime", "type": "uint256"}, {"internalType": "uint256", "name": "unlockTime", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getUnlockProgress", "outputs": [{"internalType": "uint256", "name": "currentPrice", "type": "uint256"}, {"internalType": "uint256", "name": "nextRound", "type": "uint256"}, {"internalType": "uint256", "name": "targetPrice", "type": "uint256"}, {"internalType": "bool", "name": "conditionMet", "type": "bool"}, {"internalType": "uint256", "name": "timeRemaining", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "historyIndex", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "name": "priceHistory", "outputs": [{"internalType": "uint64", "name": "timestamp", "type": "uint64"}, {"internalType": "uint64", "name": "price", "type": "uint64"}, {"internalType": "bool", "name": "conditionMet", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "signer", "type": "address"}], "name": "remove<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "requestEmergencyWithdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "requiredSignatures", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "rounds", "outputs": [{"internalType": "uint128", "name": "triggerPrice", "type": "uint128"}, {"internalType": "uint64", "name": "priceReachedTime", "type": "uint64"}, {"internalType": "uint64", "name": "unlockTime", "type": "uint64"}, {"internalType": "bool", "name": "priceConditionMet", "type": "bool"}, {"internalType": "bool", "name": "unlocked", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "0x60806040908082526004918236101561001757600080fd5b600091823560e01c9081630557ac4114610dad575080630a19399a14610d6a5780631766ef7e14610d255780633f4ba83a14610cce5780634660a19d14610b2d578063482f20a614610acb57806351858e2714610a6a578063540797a5146109a15780635c975abb14610a47578063687404b214610a00578063715018a6146109a657806382944e2d146109a15780638456cb591461094757806388c3ffb01461088e5780638a19c8bc146108705780638c65c81f146108065780638d068043146107e85780638da5cb5b146107c15780639ad3ec4814610613578063b6159606146105d0578063ce8dc388146105b5578063cf9d226e14610591578063d0f694cb14610552578063d379f9b914610527578063d491cdf3146104e4578063e0cad9b1146104a1578063e19df5c61461045f578063f2fde38b146103cc578063fd1a98a51461028e5763ff5f1e911461016f57600080fd5b3461028a578160031936011261028a57818290839261018c611385565b9460035490601f8211156101bb575b5060a0965081519586526020860152840152151560608301526080820152f35b81815260208890528281208351955093506101d585610e86565b83549360016001600160801b038616918288526001600160401b038760801c16968760208a015260c01c86890152015495608060ff80891615159889606085015260081c161515910152938580610281575b610232575b5061019b565b61023f9192965042610ef3565b62093a809081811061025a57505060a096505b93388061022c565b81039190821161026e575060a09650610252565b634e487b7160e01b815260118852602490fd5b50801515610227565b5080fd5b509190346103c957602090816003193601126103c95783516001600160401b039361014091828101868111828210176103b657875284845b848110610392575050508035835260078452858320908651928301908382108783111761037f575086528284835b600a831061034257505086519691508690505b600a8410610315576103c087f35b8460606001928486518a81511683528a858201511685840152015115158582015201930193019291610307565b60019182918a5161035281610eb7565b60ff8c8c8954809282821686521c168584015260801c1615158c82015281520193019101909185906102f4565b634e487b7160e01b855260419052602484fd5b885161039d81610eb7565b8681528683820152868a820152818401520185906102c6565b634e487b7160e01b855260418352602485fd5b80fd5b5082903461045b57602036600319011261045b576001600160a01b0382358181169391929084900361045757610400610e5a565b831561044157505082546001600160a01b0319811683178455167f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e08380a380f35b51631e4fbdf760e01b8152908101849052602490fd5b8480fd5b8280fd5b50823461045b57602036600319011261045b57356001600160a01b0381169081900361045b5761048d610e5a565b825260066020528120805460ff1916905580f35b503461028a578160031936011261028a57517f00000000000000000000000000000000000000000000000000000000000000006001600160a01b03168152602090f35b503461028a578160031936011261028a57517f00000000000000000000000000000000000000000000000000000000000000006001600160a01b03168152602090f35b50823461045b57602036600319011261045b578160209360ff92358152600885522054169051908152f35b50823461045b57602036600319011261045b57356001600160a01b0381169081900361045b57818360ff92602095526006855220541690519015158152f35b503461028a578160031936011261028a576020905169d3c21bcecceda10000008152f35b503461028a578160031936011261028a5760209051601f8152f35b503461028a578160031936011261028a57517f00000000000000000000000000000000000000000000000000000000000000006001600160a01b03168152602090f35b5082903461045b57602090816003193601126107bd5782359283855260058352818520918254936001600160401b038560801c1680156107ae5760ff8660c01c1661079f5762093a80810180911161078c57421061077e573387526006815260ff8288205416156107705760ff60c01b198516600160c01b1784558654825163a9059cbb60e01b81526001600160a01b03918216858201526001600160801b039687166024820152908290829060449082908c907f0000000000000000000000000000000000000000000000000000000000000000165af1918215610766578892610739575b50501561072c5750505416907f52dd3ccddd493d9fb871d4f3af402c398e59ac2e679c482008b36d4e84e89a1e8380a380f35b516312171d8360e31b8152fd5b6107589250803d1061075f575b6107508183610ed2565b81019061136d565b87806106f9565b503d610746565b83513d8a823e3d90fd5b505163ea8e4eb560e01b8152fd5b50516320b5c16d60e01b8152fd5b634e487b7160e01b885260118452602488fd5b505051630dc1019760e01b8152fd5b505051632589d98f60e11b8152fd5b8380fd5b503461028a578160031936011261028a57905490516001600160a01b039091168152602090f35b503461028a578160031936011261028a576020906009549051908152f35b50823461045b57602036600319011261045b578160a0938260ff93358252602052209160018354930154908051936001600160801b03811685526001600160401b038160801c16602086015260c01c908401528181161515606084015260081c1615156080820152f35b503461028a578160031936011261028a576020906003549051908152f35b5090346103c95760203660031901126103c95782358015801561093d575b61092e579060a09383928252602052208151916108c883610e86565b60808254916001600160801b0383169485815260016001600160401b0385851c169485602084015260c01c95868484015201549060ff80831615159283606084015260081c16151593849101528151958652602086015284015260608301526080820152f35b5050516328ad4a9560e21b8152fd5b50601f81116108ac565b503461028a578160031936011261028a5760207f62e78cea01bee320cd4e420270b5ea74000d11b0c9f74754ebdbfc544b05a25891610984610e5a565b61098c61134f565b600160ff19600254161760025551338152a180f35b610e37565b82346103c957806003193601126103c9576109bf610e5a565b80546001600160a01b03198116825581906001600160a01b03167f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e08280a380f35b5082903461045b578260031936011261045b57600260015414610a3a57826002600155610a2b61134f565b610a33610f16565b6001805580f35b51633ee5aeb560e01b8152fd5b503461028a578160031936011261028a5760209060ff6002541690519015158152f35b50823461045b578260031936011261045b57338352600660205260ff828420541615610abd575060207f62e78cea01bee320cd4e420270b5ea74000d11b0c9f74754ebdbfc544b05a2589161098c61134f565b905163ea8e4eb560e01b8152fd5b5090346103c957816003193601126103c95760243592358152600760205281812090600a8410156103c95750606092610b0391610e11565b50549060ff8151926001600160401b03808216855281841c16602085015260801c16151590820152f35b5082903461045b57602036600319011261045b57813590610b4c611468565b338452600660205260ff818520541615610cbf5769d3c21bcecceda10000008211610cb0578051602081019083825242838201523360601b60608201526054815260808101916001600160401b039082841082851117610c9d57838552825190209561010083019084821083831117610c8a575084526001600160801b03858116845242821660a0840190815260c084018981523360e0909501948552888a5260056020529589209451855491516001600160c01b0319909216921691909117911660801b67ffffffffffffffff60801b16178255916001919051815460ff60c01b191690151560c01b60ff60c01b161781559151910180546001600160a01b0319166001600160a01b039290921691909117905542917f41b48671772f7d8bc5ea523a9219ae16bd8e0f470629632de1a7c289357e95858480a480f35b634e487b7160e01b895260419052602488fd5b634e487b7160e01b885260418752602488fd5b51630625040160e01b81529050fd5b5163ea8e4eb560e01b81529050fd5b503461028a578160031936011261028a5760207f5db9ee0a495bf2e6ff9c91a7834c1ba4fdd244a5e8aa4e537bd38aeae4b073aa91610d0b610e5a565b610d13611468565b60ff196002541660025551338152a180f35b50823461045b57602036600319011261045b57356001600160a01b0381169081900361045b57610d53610e5a565b825260066020528120805460ff1916600117905580f35b503461028a578160031936011261028a57517f00000000000000000000000000000000000000000000000000000000000000006001600160a01b03168152602090f35b92939050346107bd5760203660031901126107bd5760809382913581526005602052209060ff8254926001808060a01b0391015416926001600160801b03811685526001600160401b0381871c16602086015260c01c161515908301526060820152f35b600a821015610e21570190600090565b634e487b7160e01b600052603260045260246000fd5b34610e55576000366003190112610e5557602060405162093a808152f35b600080fd5b6000546001600160a01b03163303610e6e57565b60405163118cdaa760e01b8152336004820152602490fd5b60a081019081106001600160401b03821117610ea157604052565b634e487b7160e01b600052604160045260246000fd5b606081019081106001600160401b03821117610ea157604052565b90601f801991011681019081106001600160401b03821117610ea157604052565b91908203918211610f0057565b634e487b7160e01b600052601160045260246000fd5b600354601f811161134c57610f29611385565b600091808352600460208181526040918291828720916001600160801b038354168710156008835260ff9283868b205416926001600160401b038b8b8b610f99888542169c8d95815196610f7c88610eb7565b8752878a880196168652818701948b8652815260078a5220610e11565b939093611339575183549251915170ffffffffffffffffffffffffffffffffff1990931690851617908c1b6fffffffffffffffff0000000000000000161790151560801b60ff60801b16179055600194850193868511611326578a8d5260088452898d2087600a8160ff1998160616868254161790556000146112aa57848701958654918183161560001461108657505050505061105f9550825416179055908154906001600160401b0360801b9060801b16906001600160401b0360801b1916179055565b7f7d3286e30b82fedc38ef2ba29d6d3e5ccc6dbd65373b1492379ea18eccc9258e429380a4565b91959650935062093a809297506110a391965460801c1642610ef3565b1015918261129c575b50506110bc575b50505050505050565b848752818352838720818101805461ff00191661010017905580546001600160c01b03164260c01b6001600160c01b031916179055840180851161128957600355825163a9059cbb60e01b8082526001600160a01b037f00000000000000000000000000000000000000000000000000000000000000008116848401526a422ca8b0a00a42500000006024840181905298927f000000000000000000000000000000000000000000000000000000000000000082169290868160448188885af190811561127f578591611262575b5015611252579060448692858951958694859384527f000000000000000000000000000000000000000000000000000000000000000016898401528d60248401525af1918215611247579161122a575b501561121c5750907f219e0c2358bbd0ac84dc3ea4ce7d0dc9cb1eac750ee2f1e15927a0627fa00b8b918151908682524290820152a4388080808080806110b3565b82516312171d8360e31b8152fd5b6112419150833d851161075f576107508183610ed2565b386111da565b8551903d90823e3d90fd5b86516312171d8360e31b81528590fd5b6112799150873d891161075f576107508183610ed2565b3861118a565b88513d87823e3d90fd5b634e487b7160e01b875260118252602487fd5b60081c1615905038806110ac565b5050509294509092508301918254908082169081611318575b506112d15750505050505050565b169055805467ffffffffffffffff60801b1916905542927f8a82bdd88cae1a98297cd0cc6e0ff3e71f88cbf11cc22b15fa0202e6443b4a2f9080a4388080808080806110b3565b90508160081c1615386112c3565b634e487b7160e01b8d526011835260248dfd5b50634e487b7160e01b8f528e855260248ffd5b50565b60ff6002541661135b57565b60405163d93c066560e01b8152600490fd5b90816020910312610e5557518015158103610e555790565b60408051638e15f47360e01b602080830191825260048352926001600160401b039290810183811182821017610ea15760405260008092819251907f00000000000000000000000000000000000000000000000000000000000000005afa3d1561145f573d92831161144b5760405192611408601f8201601f1916860185610ed2565b83523d828585013e5b158015611441575b61142f5782828051810103126103c95750015190565b60405163337d75ad60e11b8152600490fd5b5082825110611419565b634e487b7160e01b82526041600452602482fd5b60609250611411565b60ff600254161561147557565b604051638dfc202b60e01b8152600490fdfea26469706673582212203e28fd3c3eb4baa8a96b8fc9f5a852a78f03ed4636f4d3cd3f216bbe3149018264736f6c63430008140033", "linkReferences": {}, "deployedLinkReferences": {}}