{"_format": "hh-sol-artifact-1", "contractName": "MockERC20", "sourceName": "contracts/MockERC20.sol", "abi": [{"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint256", "name": "initialSupply", "type": "uint256"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientAllowance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "name": "ERC20InvalidApprover", "type": "error"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "name": "ERC20InvalidReceiver", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "ERC20InvalidSender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "name": "ERC20InvalidSpender", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "burn", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "mint", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}