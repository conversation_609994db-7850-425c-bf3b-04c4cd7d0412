{"_format": "hh-sol-artifact-1", "contractName": "MockPriceO<PERSON>le", "sourceName": "contracts/MockPriceOracle.sol", "abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "fixedPrice", "type": "uint256"}], "name": "EmergencyModeActivated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "newPrice", "type": "uint256"}], "name": "PriceUpdated", "type": "event"}, {"inputs": [{"internalType": "uint256", "name": "fixedPrice", "type": "uint256"}], "name": "activateEmergencyMode", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "deactivateEmergencyMode", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getLatestPrice", "outputs": [{"internalType": "uint256", "name": "price", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "isEmergencyMode", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "newPrice", "type": "uint256"}], "name": "setPrice", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x6080806040523461002b57662386f26fc1000060005560ff196001541660015561018690816100318239f35b600080fdfe608080604052600436101561001357600080fd5b600090813560e01c90816320a194b81461012b575080638e15f4731461010e57806391b7f5ed146100c957806399b20eaf146100ab5763d9c5e6d71461005857600080fd5b346100a85760203660031901126100a8577f5fa9abb83376468762d4533d9cd0a8f21edf0033fa06b47347578fb235a92edc6020600435600160ff1981541617600155808455604051908152a180f35b80fd5b50346100a857806003193601126100a85760ff196001541660015580f35b50346100a85760203660031901126100a8577f66cbca4f3c64fecf1dcb9ce094abcf7f68c3450a1d4e3a8e917dd621edb4ebe06020600435808455604051908152a180f35b50346100a857806003193601126100a85760209054604051908152f35b90503461014c578160031936011261014c5760209060ff6001541615158152f35b5080fdfea2646970667358221220c24db03009593b6633b6a90802c22a3304f3eef8ac704585d71ed7973384419164736f6c63430008140033", "deployedBytecode": "0x608080604052600436101561001357600080fd5b600090813560e01c90816320a194b81461012b575080638e15f4731461010e57806391b7f5ed146100c957806399b20eaf146100ab5763d9c5e6d71461005857600080fd5b346100a85760203660031901126100a8577f5fa9abb83376468762d4533d9cd0a8f21edf0033fa06b47347578fb235a92edc6020600435600160ff1981541617600155808455604051908152a180f35b80fd5b50346100a857806003193601126100a85760ff196001541660015580f35b50346100a85760203660031901126100a8577f66cbca4f3c64fecf1dcb9ce094abcf7f68c3450a1d4e3a8e917dd621edb4ebe06020600435808455604051908152a180f35b50346100a857806003193601126100a85760209054604051908152f35b90503461014c578160031936011261014c5760209060ff6001541615158152f35b5080fdfea2646970667358221220c24db03009593b6633b6a90802c22a3304f3eef8ac704585d71ed7973384419164736f6c63430008140033", "linkReferences": {}, "deployedLinkReferences": {}}