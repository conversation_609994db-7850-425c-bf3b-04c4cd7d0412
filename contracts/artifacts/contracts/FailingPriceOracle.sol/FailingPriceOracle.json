{"_format": "hh-sol-artifact-1", "contractName": "FailingPriceOracle", "sourceName": "contracts/FailingPriceOracle.sol", "abi": [{"inputs": [], "name": "getLatestPrice", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "pure", "type": "function"}], "bytecode": "0x6080806040523460145760a2908161001a8239f35b600080fdfe6080806040526004361015601257600080fd5b600090813560e01c638e15f47314602857600080fd5b346068578160031936011260685762461bcd60e51b815260206004820152600d60248201526c13dc9858db194819985a5b1959609a1b6044820152606490fd5b5080fdfea26469706673582212205a6890a4d7a079349e0a0af9d66d34983db4ed56e6a7706425b476406ee1186a64736f6c63430008140033", "deployedBytecode": "0x6080806040526004361015601257600080fd5b600090813560e01c638e15f47314602857600080fd5b346068578160031936011260685762461bcd60e51b815260206004820152600d60248201526c13dc9858db194819985a5b1959609a1b6044820152606490fd5b5080fdfea26469706673582212205a6890a4d7a079349e0a0af9d66d34983db4ed56e6a7706425b476406ee1186a64736f6c63430008140033", "linkReferences": {}, "deployedLinkReferences": {}}