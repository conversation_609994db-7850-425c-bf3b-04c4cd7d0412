{"_format": "hh-sol-artifact-1", "contractName": "HAOXPriceOracleV2", "sourceName": "contracts/HAOXPriceOracleV2.sol", "abi": [{"inputs": [{"internalType": "address", "name": "_bnbUsdChainlink", "type": "address"}, {"internalType": "address", "name": "_haoxBnbPair", "type": "address"}, {"internalType": "address", "name": "_haoxToken", "type": "address"}, {"internalType": "address", "name": "_wbnb", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "EnforcedPause", "type": "error"}, {"inputs": [], "name": "ExpectedPause", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "fixedPrice", "type": "uint256"}], "name": "EmergencyModeActivated", "type": "event"}, {"anonymous": false, "inputs": [], "name": "EmergencyModeDeactivated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "string", "name": "sourceType", "type": "string"}, {"indexed": false, "internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "name": "PriceSourceUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "haoxPrice", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "bnbPrice", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "confidence", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "source", "type": "string"}], "name": "PriceUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"inputs": [], "name": "MAX_PRICE_DEVIATION", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MIN_CONFIDENCE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PRICE_STALENESS_THRESHOLD", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "UPDATE_INTERVAL", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_fixedPrice", "type": "uint256"}], "name": "activateEmergencyMode", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "bnbUsdChainlink", "outputs": [{"internalType": "contract AggregatorV3Interface", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "currentBnbPrice", "outputs": [{"internalType": "uint256", "name": "price", "type": "uint256"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "uint256", "name": "confidence", "type": "uint256"}, {"internalType": "bool", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "currentHaoxPrice", "outputs": [{"internalType": "uint256", "name": "price", "type": "uint256"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "uint256", "name": "confidence", "type": "uint256"}, {"internalType": "bool", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "deactivateEmergencyMode", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "emergencyMode", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "emergencyPrice", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getBnbUsdPrice", "outputs": [{"internalType": "uint256", "name": "price", "type": "uint256"}, {"internalType": "uint256", "name": "confidence", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getHaoxBnbPrice", "outputs": [{"internalType": "uint256", "name": "price", "type": "uint256"}, {"internalType": "uint256", "name": "confidence", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getHaoxUsdPrice", "outputs": [{"internalType": "uint256", "name": "price", "type": "uint256"}, {"internalType": "uint256", "name": "confidence", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getLatestPrice", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getPriceWithConfidence", "outputs": [{"internalType": "uint256", "name": "price", "type": "uint256"}, {"internalType": "uint256", "name": "confidence", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "haoxBnbPair", "outputs": [{"internalType": "contract IPancakeSwapPair", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "haoxToken", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "lastUpdateTime", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "priceHistory", "outputs": [{"internalType": "uint256", "name": "price", "type": "uint256"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "uint256", "name": "confidence", "type": "uint256"}, {"internalType": "bool", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "updatePrices", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "wbnb", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}