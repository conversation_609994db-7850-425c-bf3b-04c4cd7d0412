import hre from "hardhat";

const { ethers } = hre;

async function main() {
  console.log("🚀 部署HAOXInvitationV2合约...\n");
  
  const [deployer] = await ethers.getSigners();
  console.log("部署账户:", deployer.address);
  
  const balance = await deployer.provider.getBalance(deployer.address);
  console.log("账户余额:", ethers.formatEther(balance), "BNB\n");

  // 已部署的代币合约地址
  const haoxTokenAddress = "******************************************";

  try {
    // 部署邀请合约
    console.log("🎁 部署HAOXInvitationV2合约...");
    console.log("使用代币合约:", haoxTokenAddress);
    
    const HAOXInvitationV2 = await ethers.getContractFactory("HAOXInvitationV2");
    const invitationContract = await HAOXInvitationV2.deploy(haoxTokenAddress);
    
    console.log("等待部署确认...");
    await invitationContract.waitForDeployment();
    
    const invitationAddress = await invitationContract.getAddress();
    const deployTx = invitationContract.deploymentTransaction();
    
    console.log("等待交易确认...");
    const receipt = await deployTx.wait();
    
    console.log("\n✅ HAOXInvitationV2 部署成功!");
    console.log("==========================================");
    console.log("合约地址:", invitationAddress);
    console.log("交易哈希:", deployTx.hash);
    console.log("Gas使用:", receipt.gasUsed.toString());
    console.log("Gas价格:", deployTx.gasPrice.toString());
    console.log("BNB消耗:", ethers.formatEther(receipt.gasUsed * deployTx.gasPrice));
    console.log("区块号:", receipt.blockNumber);
    console.log("==========================================");

    // 验证合约基本信息
    console.log("\n🔍 验证合约信息...");
    const baseReward = await invitationContract.BASE_REWARD();
    const milestone5 = await invitationContract.MILESTONE_5_REWARD();
    const milestone10 = await invitationContract.MILESTONE_10_REWARD();
    const totalPool = await invitationContract.TOTAL_REWARD_POOL();
    
    console.log("基础奖励:", ethers.formatEther(baseReward), "HAOX");
    console.log("5人里程碑:", ethers.formatEther(milestone5), "HAOX");
    console.log("10人里程碑:", ethers.formatEther(milestone10), "HAOX");
    console.log("总奖励池:", ethers.formatEther(totalPool), "HAOX");
    
    // 验证奖励数值
    if (ethers.formatEther(baseReward) === "1000.0" &&
        ethers.formatEther(milestone5) === "10000.0" &&
        ethers.formatEther(milestone10) === "50000.0" &&
        ethers.formatEther(totalPool) === "300000000.0") {
      console.log("✅ 邀请奖励参数验证通过");
    } else {
      console.log("❌ 邀请奖励参数验证失败");
    }

    // 检查合约状态
    console.log("\n📊 检查合约状态...");
    const presaleEnded = await invitationContract.presaleEnded();
    console.log("预售结束状态:", presaleEnded);

  } catch (error) {
    console.error("❌ 部署失败:", error);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
