import hre from "hardhat";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const { ethers } = hre;
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 部署配置
const DEPLOYMENT_CONFIG = {
  // BSC测试网Chainlink BNB/USD价格源
  CHAINLINK_BNB_USD: "******************************************",
  // 项目钱包地址
  PROJECT_WALLET: "******************************************",
  // 社区钱包地址  
  COMMUNITY_WALLET: "******************************************",
  // WBNB地址（BSC测试网）
  WBNB_ADDRESS: "******************************************",
};

async function main() {
  console.log("🚀 开始部署SocioMint V2智能合约系统...\n");
  
  const [deployer] = await ethers.getSigners();
  console.log("部署账户:", deployer.address);
  
  const balance = await deployer.provider.getBalance(deployer.address);
  console.log("账户余额:", ethers.formatEther(balance), "BNB\n");

  const deployedContracts = {};
  const deploymentDetails = [];

  try {
    // 1. 部署HAOX代币合约
    console.log("📄 1. 部署HAOXTokenV2合约...");
    const startTime1 = Date.now();
    const HAOXTokenV2 = await ethers.getContractFactory("HAOXTokenV2");
    const haoxToken = await HAOXTokenV2.deploy();
    await haoxToken.waitForDeployment();
    
    const haoxTokenAddress = await haoxToken.getAddress();
    const deployTx1 = haoxToken.deploymentTransaction();
    const receipt1 = await deployTx1.wait();
    
    deployedContracts.haoxToken = haoxTokenAddress;
    deploymentDetails.push({
      contract: "HAOXTokenV2",
      address: haoxTokenAddress,
      txHash: deployTx1.hash,
      gasUsed: receipt1.gasUsed.toString(),
      gasPrice: deployTx1.gasPrice.toString(),
      bnbCost: ethers.formatEther(receipt1.gasUsed * deployTx1.gasPrice),
      blockNumber: receipt1.blockNumber,
      timestamp: new Date().toISOString(),
      deployTime: `${Date.now() - startTime1}ms`
    });
    
    console.log("✅ HAOXTokenV2 部署成功:", haoxTokenAddress);
    console.log("   交易哈希:", deployTx1.hash);
    console.log("   Gas使用:", receipt1.gasUsed.toString());
    console.log("   BNB消耗:", ethers.formatEther(receipt1.gasUsed * deployTx1.gasPrice));

    // 2. 部署价格预言机合约
    console.log("\n📊 2. 部署HAOXPriceOracleV2合约...");
    const startTime2 = Date.now();
    const HAOXPriceOracleV2 = await ethers.getContractFactory("HAOXPriceOracleV2");
    
    const placeholderPairAddress = "******************************************";
    const priceOracle = await HAOXPriceOracleV2.deploy(
      DEPLOYMENT_CONFIG.CHAINLINK_BNB_USD,
      placeholderPairAddress,
      haoxTokenAddress,
      DEPLOYMENT_CONFIG.WBNB_ADDRESS
    );
    await priceOracle.waitForDeployment();
    
    const priceOracleAddress = await priceOracle.getAddress();
    const deployTx2 = priceOracle.deploymentTransaction();
    const receipt2 = await deployTx2.wait();
    
    deployedContracts.priceOracle = priceOracleAddress;
    deploymentDetails.push({
      contract: "HAOXPriceOracleV2",
      address: priceOracleAddress,
      txHash: deployTx2.hash,
      gasUsed: receipt2.gasUsed.toString(),
      gasPrice: deployTx2.gasPrice.toString(),
      bnbCost: ethers.formatEther(receipt2.gasUsed * deployTx2.gasPrice),
      blockNumber: receipt2.blockNumber,
      timestamp: new Date().toISOString(),
      deployTime: `${Date.now() - startTime2}ms`
    });
    
    console.log("✅ HAOXPriceOracleV2 部署成功:", priceOracleAddress);
    console.log("   交易哈希:", deployTx2.hash);
    console.log("   Gas使用:", receipt2.gasUsed.toString());
    console.log("   BNB消耗:", ethers.formatEther(receipt2.gasUsed * deployTx2.gasPrice));

    // 3. 部署邀请奖励合约
    console.log("\n🎁 3. 部署HAOXInvitationV2合约...");
    const startTime3 = Date.now();
    const HAOXInvitationV2 = await ethers.getContractFactory("HAOXInvitationV2");
    const invitationContract = await HAOXInvitationV2.deploy(haoxTokenAddress);
    await invitationContract.waitForDeployment();
    
    const invitationAddress = await invitationContract.getAddress();
    const deployTx3 = invitationContract.deploymentTransaction();
    const receipt3 = await deployTx3.wait();
    
    deployedContracts.invitation = invitationAddress;
    deploymentDetails.push({
      contract: "HAOXInvitationV2",
      address: invitationAddress,
      txHash: deployTx3.hash,
      gasUsed: receipt3.gasUsed.toString(),
      gasPrice: deployTx3.gasPrice.toString(),
      bnbCost: ethers.formatEther(receipt3.gasUsed * deployTx3.gasPrice),
      blockNumber: receipt3.blockNumber,
      timestamp: new Date().toISOString(),
      deployTime: `${Date.now() - startTime3}ms`
    });
    
    console.log("✅ HAOXInvitationV2 部署成功:", invitationAddress);
    console.log("   交易哈希:", deployTx3.hash);
    console.log("   Gas使用:", receipt3.gasUsed.toString());
    console.log("   BNB消耗:", ethers.formatEther(receipt3.gasUsed * deployTx3.gasPrice));

    // 4. 部署预售合约
    console.log("\n💰 4. 部署HAOXPresaleV2合约...");
    const startTime4 = Date.now();
    const HAOXPresaleV2 = await ethers.getContractFactory("HAOXPresaleV2");
    const presaleContract = await HAOXPresaleV2.deploy(
      haoxTokenAddress,
      invitationAddress,
      priceOracleAddress
    );
    await presaleContract.waitForDeployment();
    
    const presaleAddress = await presaleContract.getAddress();
    const deployTx4 = presaleContract.deploymentTransaction();
    const receipt4 = await deployTx4.wait();
    
    deployedContracts.presale = presaleAddress;
    deploymentDetails.push({
      contract: "HAOXPresaleV2",
      address: presaleAddress,
      txHash: deployTx4.hash,
      gasUsed: receipt4.gasUsed.toString(),
      gasPrice: deployTx4.gasPrice.toString(),
      bnbCost: ethers.formatEther(receipt4.gasUsed * deployTx4.gasPrice),
      blockNumber: receipt4.blockNumber,
      timestamp: new Date().toISOString(),
      deployTime: `${Date.now() - startTime4}ms`
    });
    
    console.log("✅ HAOXPresaleV2 部署成功:", presaleAddress);
    console.log("   交易哈希:", deployTx4.hash);
    console.log("   Gas使用:", receipt4.gasUsed.toString());
    console.log("   BNB消耗:", ethers.formatEther(receipt4.gasUsed * deployTx4.gasPrice));

    // 5. 部署解锁合约
    console.log("\n🔓 5. 部署HAOXVestingV2合约...");
    const startTime5 = Date.now();
    const HAOXVestingV2 = await ethers.getContractFactory("HAOXVestingV2");
    const vestingContract = await HAOXVestingV2.deploy(
      haoxTokenAddress,
      priceOracleAddress,
      DEPLOYMENT_CONFIG.PROJECT_WALLET,
      DEPLOYMENT_CONFIG.COMMUNITY_WALLET
    );
    await vestingContract.waitForDeployment();
    
    const vestingAddress = await vestingContract.getAddress();
    const deployTx5 = vestingContract.deploymentTransaction();
    const receipt5 = await deployTx5.wait();
    
    deployedContracts.vesting = vestingAddress;
    deploymentDetails.push({
      contract: "HAOXVestingV2",
      address: vestingAddress,
      txHash: deployTx5.hash,
      gasUsed: receipt5.gasUsed.toString(),
      gasPrice: deployTx5.gasPrice.toString(),
      bnbCost: ethers.formatEther(receipt5.gasUsed * deployTx5.gasPrice),
      blockNumber: receipt5.blockNumber,
      timestamp: new Date().toISOString(),
      deployTime: `${Date.now() - startTime5}ms`
    });
    
    console.log("✅ HAOXVestingV2 部署成功:", vestingAddress);
    console.log("   交易哈希:", deployTx5.hash);
    console.log("   Gas使用:", receipt5.gasUsed.toString());
    console.log("   BNB消耗:", ethers.formatEther(receipt5.gasUsed * deployTx5.gasPrice));

    // 计算总成本
    const totalGasUsed = deploymentDetails.reduce((sum, detail) => sum + BigInt(detail.gasUsed), 0n);
    const totalBnbCost = deploymentDetails.reduce((sum, detail) => sum + parseFloat(detail.bnbCost), 0);

    // 生成部署报告
    const deploymentReport = {
      network: "BSC Testnet",
      chainId: 97,
      timestamp: new Date().toISOString(),
      deployer: deployer.address,
      contracts: deployedContracts,
      deploymentDetails: deploymentDetails,
      summary: {
        totalContracts: deploymentDetails.length,
        totalGasUsed: totalGasUsed.toString(),
        totalBnbCost: totalBnbCost.toFixed(6),
        averageGasPrice: deploymentDetails[0]?.gasPrice || "0"
      }
    };

    // 保存部署报告
    const deploymentDir = path.join(__dirname, "../deployments");
    if (!fs.existsSync(deploymentDir)) {
      fs.mkdirSync(deploymentDir, { recursive: true });
    }
    
    const reportPath = path.join(deploymentDir, "v2-deployment-report.json");
    fs.writeFileSync(reportPath, JSON.stringify(deploymentReport, null, 2));

    // 生成环境变量文件
    const envContent = `
# SocioMint V2 智能合约地址 (BSC测试网 - ${new Date().toISOString().split('T')[0]} 部署)
NEXT_PUBLIC_HAOX_TOKEN_ADDRESS_V2=${deployedContracts.haoxToken}
NEXT_PUBLIC_HAOX_PRESALE_ADDRESS_V2=${deployedContracts.presale}
NEXT_PUBLIC_HAOX_INVITATION_ADDRESS_V2=${deployedContracts.invitation}
NEXT_PUBLIC_HAOX_PRICE_ORACLE_ADDRESS_V2=${deployedContracts.priceOracle}
NEXT_PUBLIC_HAOX_VESTING_ADDRESS_V2=${deployedContracts.vesting}

# 兼容性地址（更新现有变量）
NEXT_PUBLIC_HAOX_TOKEN_ADDRESS=${deployedContracts.haoxToken}
NEXT_PUBLIC_HAOX_PRESALE_ADDRESS=${deployedContracts.presale}
NEXT_PUBLIC_HAOX_INVITATION_ADDRESS=${deployedContracts.invitation}
NEXT_PUBLIC_HAOX_CONTRACT_ADDRESS_BSC=${deployedContracts.haoxToken}
NEXT_PUBLIC_HAOX_CONTRACT_ADDRESS=${deployedContracts.haoxToken}
`;

    const envPath = path.join(deploymentDir, "v2-env-variables.txt");
    fs.writeFileSync(envPath, envContent);

    // 打印部署总结
    console.log("\n🎉 部署完成！");
    console.log("==========================================");
    console.log("📋 合约地址总结:");
    Object.entries(deployedContracts).forEach(([name, address]) => {
      console.log(`   ${name}: ${address}`);
    });
    console.log("==========================================");
    console.log("💰 部署成本总结:");
    console.log(`   总Gas使用: ${totalGasUsed.toString()}`);
    console.log(`   总BNB消耗: ${totalBnbCost.toFixed(6)} BNB`);
    console.log("==========================================");
    console.log(`📄 部署报告已保存: ${reportPath}`);
    console.log(`🔧 环境变量已生成: ${envPath}`);

  } catch (error) {
    console.error("❌ 部署失败:", error);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
