import hre from "hardhat";

const { ethers } = hre;

async function main() {
  console.log("🚀 部署HAOXPriceOracleV2合约...\n");
  
  const [deployer] = await ethers.getSigners();
  console.log("部署账户:", deployer.address);
  
  const balance = await deployer.provider.getBalance(deployer.address);
  console.log("账户余额:", ethers.formatEther(balance), "BNB\n");

  // 已部署的代币合约地址
  const haoxTokenAddress = "******************************************";
  
  // 部署配置
  const CHAINLINK_BNB_USD = "******************************************";
  const WBNB_ADDRESS = "******************************************";
  const placeholderPairAddress = "******************************************";

  try {
    // 部署价格预言机合约
    console.log("📊 部署HAOXPriceOracleV2合约...");
    const HAOXPriceOracleV2 = await ethers.getContractFactory("HAOXPriceOracleV2");
    const priceOracle = await HAOXPriceOracleV2.deploy(
      CHAINLINK_BNB_USD,
      placeholderPairAddress,
      haoxTokenAddress,
      WBNB_ADDRESS
    );
    
    console.log("等待部署确认...");
    await priceOracle.waitForDeployment();
    
    const priceOracleAddress = await priceOracle.getAddress();
    const deployTx = priceOracle.deploymentTransaction();
    
    console.log("等待交易确认...");
    const receipt = await deployTx.wait();
    
    console.log("\n✅ HAOXPriceOracleV2 部署成功!");
    console.log("==========================================");
    console.log("合约地址:", priceOracleAddress);
    console.log("交易哈希:", deployTx.hash);
    console.log("Gas使用:", receipt.gasUsed.toString());
    console.log("Gas价格:", deployTx.gasPrice.toString());
    console.log("BNB消耗:", ethers.formatEther(receipt.gasUsed * deployTx.gasPrice));
    console.log("区块号:", receipt.blockNumber);
    console.log("==========================================");

    // 验证合约基本信息
    console.log("\n🔍 验证合约信息...");
    const maxDeviation = await priceOracle.MAX_PRICE_DEVIATION();
    const updateInterval = await priceOracle.UPDATE_INTERVAL();
    const minConfidence = await priceOracle.MIN_CONFIDENCE();
    const emergencyMode = await priceOracle.emergencyMode();
    
    console.log("最大价格偏差:", maxDeviation.toString(), "/ 10000 =", (Number(maxDeviation) / 100).toString() + "%");
    console.log("更新间隔:", updateInterval.toString(), "秒 =", (Number(updateInterval) / 3600).toString(), "小时");
    console.log("最低置信度:", minConfidence.toString() + "%");
    console.log("紧急模式:", emergencyMode);

  } catch (error) {
    console.error("❌ 部署失败:", error);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
