import hre from "hardhat";

const { ethers } = hre;

async function main() {
  console.log("🚀 部署HAOXVestingV2合约...\n");
  
  const [deployer] = await ethers.getSigners();
  console.log("部署账户:", deployer.address);
  
  const balance = await deployer.provider.getBalance(deployer.address);
  console.log("账户余额:", ethers.formatEther(balance), "BNB\n");

  // 已部署的合约地址
  const haoxTokenAddress = "******************************************";
  const priceOracleAddress = "******************************************";
  
  // 项目钱包和社区钱包地址
  const projectWallet = "******************************************";
  const communityWallet = "******************************************";

  try {
    // 部署解锁合约
    console.log("🔓 部署HAOXVestingV2合约...");
    console.log("使用代币合约:", haoxTokenAddress);
    console.log("使用价格预言机:", priceOracleAddress);
    console.log("项目钱包:", projectWallet);
    console.log("社区钱包:", communityWallet);
    
    const HAOXVestingV2 = await ethers.getContractFactory("HAOXVestingV2");
    const vestingContract = await HAOXVestingV2.deploy(
      haoxTokenAddress,
      priceOracleAddress,
      projectWallet,
      communityWallet
    );
    
    console.log("等待部署确认...");
    await vestingContract.waitForDeployment();
    
    const vestingAddress = await vestingContract.getAddress();
    const deployTx = vestingContract.deploymentTransaction();
    
    console.log("等待交易确认...");
    const receipt = await deployTx.wait();
    
    console.log("\n✅ HAOXVestingV2 部署成功!");
    console.log("==========================================");
    console.log("合约地址:", vestingAddress);
    console.log("交易哈希:", deployTx.hash);
    console.log("Gas使用:", receipt.gasUsed.toString());
    console.log("Gas价格:", deployTx.gasPrice.toString());
    console.log("BNB消耗:", ethers.formatEther(receipt.gasUsed * deployTx.gasPrice));
    console.log("区块号:", receipt.blockNumber);
    console.log("==========================================");

    // 验证合约基本信息
    console.log("\n🔍 验证合约信息...");
    const totalRounds = await vestingContract.TOTAL_ROUNDS();
    const tokensPerRound = await vestingContract.TOKENS_PER_ROUND();
    const initialTriggerPrice = await vestingContract.INITIAL_TRIGGER_PRICE();
    const currentRound = await vestingContract.currentRound();
    
    console.log("总轮次:", totalRounds.toString());
    console.log("每轮代币:", ethers.formatEther(tokensPerRound), "HAOX");
    console.log("初始触发价格: $", (Number(initialTriggerPrice) / 100000000).toFixed(8), "USD");
    console.log("当前轮次:", currentRound.toString());
    
    // 验证解锁参数
    if (totalRounds.toString() === "31" &&
        ethers.formatEther(tokensPerRound) === "150000000.0" &&
        initialTriggerPrice.toString() === "272000" &&
        currentRound.toString() === "1") {
      console.log("✅ 解锁参数验证通过");
    } else {
      console.log("❌ 解锁参数验证失败");
    }

    // 检查合约状态
    console.log("\n📊 检查合约状态...");
    const projectWalletAddr = await vestingContract.projectWallet();
    const communityWalletAddr = await vestingContract.communityWallet();
    
    console.log("项目钱包地址:", projectWalletAddr);
    console.log("社区钱包地址:", communityWalletAddr);
    
    // 检查第一轮解锁信息
    const round1Info = await vestingContract.rounds(1);
    console.log("第1轮解锁信息:");
    console.log("  代币数量:", ethers.formatEther(round1Info.tokenAmount), "HAOX");
    console.log("  触发价格: $", (Number(round1Info.triggerPrice) / 100000000).toFixed(8), "USD");
    console.log("  已解锁:", round1Info.unlocked);

  } catch (error) {
    console.error("❌ 部署失败:", error);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
