import hre from "hardhat";

const { ethers } = hre;

async function main() {
  console.log("🚀 部署HAOXPresaleV2合约...\n");
  
  const [deployer] = await ethers.getSigners();
  console.log("部署账户:", deployer.address);
  
  const balance = await deployer.provider.getBalance(deployer.address);
  console.log("账户余额:", ethers.formatEther(balance), "BNB\n");

  // 已部署的合约地址
  const haoxTokenAddress = "******************************************";
  const priceOracleAddress = "******************************************";
  const invitationAddress = "******************************************";

  try {
    // 部署预售合约
    console.log("💰 部署HAOXPresaleV2合约...");
    console.log("使用代币合约:", haoxTokenAddress);
    console.log("使用价格预言机:", priceOracleAddress);
    console.log("使用邀请合约:", invitationAddress);

    const HAOXPresaleV2 = await ethers.getContractFactory("HAOXPresaleV2");
    const presaleContract = await HAOXPresaleV2.deploy(
      haoxTokenAddress,
      invitationAddress,
      priceOracleAddress
    );
    
    console.log("等待部署确认...");
    await presaleContract.waitForDeployment();
    
    const presaleAddress = await presaleContract.getAddress();
    const deployTx = presaleContract.deploymentTransaction();
    
    console.log("等待交易确认...");
    const receipt = await deployTx.wait();
    
    console.log("\n✅ HAOXPresaleV2 部署成功!");
    console.log("==========================================");
    console.log("合约地址:", presaleAddress);
    console.log("交易哈希:", deployTx.hash);
    console.log("Gas使用:", receipt.gasUsed.toString());
    console.log("Gas价格:", deployTx.gasPrice.toString());
    console.log("BNB消耗:", ethers.formatEther(receipt.gasUsed * deployTx.gasPrice));
    console.log("区块号:", receipt.blockNumber);
    console.log("==========================================");

    // 验证合约基本信息
    console.log("\n🔍 验证合约信息...");
    const totalPresaleTokens = await presaleContract.TOTAL_PRESALE_TOKENS();
    const targetBNB = await presaleContract.TARGET_BNB();
    const totalStages = await presaleContract.TOTAL_STAGES();
    const tokensPerStage = await presaleContract.TOKENS_PER_STAGE();
    const initialRate = await presaleContract.INITIAL_RATE();
    
    console.log("预售代币总量:", ethers.formatEther(totalPresaleTokens), "HAOX");
    console.log("目标BNB:", ethers.formatEther(targetBNB), "BNB");
    console.log("总阶段数:", totalStages.toString());
    console.log("每阶段代币:", ethers.formatEther(tokensPerStage), "HAOX");
    console.log("初始汇率:", initialRate.toString(), "HAOX/BNB");
    
    // 验证预售参数是否正确
    const expectedTokens = ethers.parseEther("200000000"); // 2亿HAOX
    const expectedBNB = ethers.parseEther("320"); // 320 BNB
    
    if (totalPresaleTokens.toString() === expectedTokens.toString() && 
        targetBNB.toString() === expectedBNB.toString() &&
        totalStages.toString() === "100" &&
        initialRate.toString() === "1912125") {
      console.log("✅ 预售参数验证通过");
    } else {
      console.log("❌ 预售参数验证失败");
    }

    // 检查预售状态
    console.log("\n📊 检查预售状态...");
    const presaleStatus = await presaleContract.getPresaleStatus();
    console.log("当前阶段:", presaleStatus._currentStage.toString());
    console.log("当前阶段剩余代币:", ethers.formatEther(presaleStatus._tokensRemainingInCurrentStage));
    console.log("总募集BNB:", ethers.formatEther(presaleStatus._totalBNBRaised));
    console.log("总售出代币:", ethers.formatEther(presaleStatus._totalTokensSold));
    console.log("当前汇率:", presaleStatus._currentRate.toString(), "HAOX/BNB");
    console.log("预售激活:", presaleStatus._isActive);

  } catch (error) {
    console.error("❌ 部署失败:", error);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
