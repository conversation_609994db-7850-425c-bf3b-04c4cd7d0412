import hre from "hardhat";

const { ethers } = hre;

async function main() {
  console.log("🚀 部署HAOXTokenV2合约...\n");
  
  const [deployer] = await ethers.getSigners();
  console.log("部署账户:", deployer.address);
  
  const balance = await deployer.provider.getBalance(deployer.address);
  console.log("账户余额:", ethers.formatEther(balance), "BNB\n");

  try {
    // 部署HAOX代币合约
    console.log("📄 部署HAOXTokenV2合约...");
    const HAOXTokenV2 = await ethers.getContractFactory("HAOXTokenV2");
    const haoxToken = await HAOXTokenV2.deploy();
    
    console.log("等待部署确认...");
    await haoxToken.waitForDeployment();
    
    const haoxTokenAddress = await haoxToken.getAddress();
    const deployTx = haoxToken.deploymentTransaction();
    
    console.log("等待交易确认...");
    const receipt = await deployTx.wait();
    
    console.log("\n✅ HAOXTokenV2 部署成功!");
    console.log("==========================================");
    console.log("合约地址:", haoxTokenAddress);
    console.log("交易哈希:", deployTx.hash);
    console.log("Gas使用:", receipt.gasUsed.toString());
    console.log("Gas价格:", deployTx.gasPrice.toString());
    console.log("BNB消耗:", ethers.formatEther(receipt.gasUsed * deployTx.gasPrice));
    console.log("区块号:", receipt.blockNumber);
    console.log("==========================================");

    // 验证合约基本信息
    console.log("\n🔍 验证合约信息...");
    const name = await haoxToken.name();
    const symbol = await haoxToken.symbol();
    const totalSupply = await haoxToken.totalSupply();
    const decimals = await haoxToken.decimals();
    
    console.log("代币名称:", name);
    console.log("代币符号:", symbol);
    console.log("总供应量:", ethers.formatEther(totalSupply), "HAOX");
    console.log("精度:", decimals);

  } catch (error) {
    console.error("❌ 部署失败:", error);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
