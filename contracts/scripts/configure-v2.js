const { ethers } = require("hardhat");
const fs = require("fs");
const path = require("path");

// 从部署报告中读取合约地址
function loadDeploymentReport() {
  const reportPath = path.join(__dirname, "../deployments/v2-deployment-complete.json");
  if (!fs.existsSync(reportPath)) {
    throw new Error("部署报告不存在，请先运行部署脚本");
  }
  return JSON.parse(fs.readFileSync(reportPath, "utf8"));
}

async function main() {
  console.log("⚙️  开始配置SocioMint V2智能合约系统...\n");
  
  const [deployer] = await ethers.getSigners();
  console.log("配置账户:", deployer.address);
  
  // 加载部署信息
  const deploymentReport = loadDeploymentReport();
  const contracts = {
    haoxToken: deploymentReport.contracts.HAOXTokenV2.address,
    presale: deploymentReport.contracts.HAOXPresaleV2.address,
    invitation: deploymentReport.contracts.HAOXInvitationV2.address,
    priceOracle: deploymentReport.contracts.HAOXPriceOracleV2.address,
    vesting: deploymentReport.contracts.HAOXVestingV2.address
  };
  
  console.log("📋 加载的合约地址:");
  Object.entries(contracts).forEach(([name, address]) => {
    console.log(`   ${name}: ${address}`);
  });
  console.log();

  try {
    // 获取合约实例
    const haoxToken = await ethers.getContractAt("HAOXTokenV2", contracts.haoxToken);
    const presaleContract = await ethers.getContractAt("HAOXPresaleV2", contracts.presale);
    const invitationContract = await ethers.getContractAt("HAOXInvitationV2", contracts.invitation);
    const priceOracle = await ethers.getContractAt("HAOXPriceOracleV2", contracts.priceOracle);
    const vestingContract = await ethers.getContractAt("HAOXVestingV2", contracts.vesting);

    // 1. 执行时间锁设置（如果24小时已过）
    console.log("🔐 1. 检查并执行时间锁设置...");
    
    try {
      // 尝试执行预售合约设置
      console.log("尝试设置预售合约地址...");
      await haoxToken.setPresaleContract(contracts.presale);
      console.log("✅ 预售合约地址设置成功");
    } catch (error) {
      console.log("⏰ 预售合约地址时间锁未到期或已设置");
    }

    try {
      // 尝试执行邀请合约设置
      console.log("尝试设置邀请合约地址...");
      await haoxToken.setInvitationContract(contracts.invitation);
      console.log("✅ 邀请合约地址设置成功");
    } catch (error) {
      console.log("⏰ 邀请合约地址时间锁未到期或已设置");
    }

    try {
      // 尝试执行解锁合约设置
      console.log("尝试设置解锁合约地址...");
      await haoxToken.setVestingContract(contracts.vesting);
      console.log("✅ 解锁合约地址设置成功");
    } catch (error) {
      console.log("⏰ 解锁合约地址时间锁未到期或已设置");
    }

    try {
      // 尝试执行价格预言机设置
      console.log("尝试设置价格预言机地址...");
      await haoxToken.setPriceOracle(contracts.priceOracle);
      console.log("✅ 价格预言机地址设置成功");
    } catch (error) {
      console.log("⏰ 价格预言机地址时间锁未到期或已设置");
    }

    // 2. 配置白名单（添加一些测试地址）
    console.log("\n👥 2. 配置预售白名单...");
    
    const testAddresses = [
      deployer.address,
      "0x681E8E1921778A450930Bc1991c93981FD0B1F24", // 项目钱包
      // 可以添加更多测试地址
    ];
    
    try {
      await presaleContract.addToWhitelist(testAddresses);
      console.log("✅ 白名单地址添加成功");
      testAddresses.forEach(addr => console.log(`   ${addr}`));
    } catch (error) {
      console.log("❌ 白名单添加失败:", error.message);
    }

    // 3. 检查合约状态
    console.log("\n📊 3. 检查合约状态...");
    
    // 检查代币合约状态
    const tokenStatus = await haoxToken.getContractStatus();
    console.log("代币合约状态:");
    console.log(`   暂停状态: ${tokenStatus.isPaused}`);
    console.log(`   预售合约: ${tokenStatus.presale}`);
    console.log(`   邀请合约: ${tokenStatus.invitation}`);
    console.log(`   解锁合约: ${tokenStatus.vesting}`);
    console.log(`   价格预言机: ${tokenStatus.oracle}`);

    // 检查预售合约状态
    const presaleStatus = await presaleContract.getPresaleStatus();
    console.log("\n预售合约状态:");
    console.log(`   当前阶段: ${presaleStatus._currentStage}`);
    console.log(`   当前阶段剩余代币: ${ethers.formatEther(presaleStatus._tokensRemainingInCurrentStage)}`);
    console.log(`   总募集BNB: ${ethers.formatEther(presaleStatus._totalBNBRaised)}`);
    console.log(`   总售出代币: ${ethers.formatEther(presaleStatus._totalTokensSold)}`);
    console.log(`   当前汇率: ${presaleStatus._currentRate} HAOX/BNB`);
    console.log(`   预售激活: ${presaleStatus._isActive}`);

    // 检查代币余额
    const tokenBalance = await haoxToken.balanceOf(deployer.address);
    const presaleBalance = await haoxToken.balanceOf(contracts.presale);
    const invitationBalance = await haoxToken.balanceOf(contracts.invitation);
    
    console.log("\n💰 代币余额:");
    console.log(`   部署者: ${ethers.formatEther(tokenBalance)} HAOX`);
    console.log(`   预售合约: ${ethers.formatEther(presaleBalance)} HAOX`);
    console.log(`   邀请合约: ${ethers.formatEther(invitationBalance)} HAOX`);

    // 4. 测试基本功能
    console.log("\n🧪 4. 测试基本功能...");
    
    // 测试价格预言机
    try {
      const latestPrice = await priceOracle.getLatestPrice();
      console.log(`✅ 价格预言机工作正常，当前价格: ${latestPrice}`);
    } catch (error) {
      console.log("⚠️  价格预言机需要配置HAOX/BNB交易对");
    }

    // 测试预售汇率计算
    try {
      const stage0Rate = await presaleContract.getStageRate(0);
      const stage1Rate = await presaleContract.getStageRate(1);
      console.log(`✅ 预售汇率计算正常:`);
      console.log(`   阶段0汇率: ${stage0Rate} HAOX/BNB`);
      console.log(`   阶段1汇率: ${stage1Rate} HAOX/BNB`);
    } catch (error) {
      console.log("❌ 预售汇率计算失败:", error.message);
    }

    // 5. 生成配置报告
    console.log("\n📋 5. 生成配置报告...");
    
    const configReport = {
      timestamp: new Date().toISOString(),
      network: "BSC Testnet",
      contracts: contracts,
      configuration: {
        tokenStatus: {
          isPaused: tokenStatus.isPaused,
          presaleContract: tokenStatus.presale,
          invitationContract: tokenStatus.invitation,
          vestingContract: tokenStatus.vesting,
          priceOracle: tokenStatus.oracle
        },
        presaleStatus: {
          currentStage: presaleStatus._currentStage.toString(),
          tokensRemaining: ethers.formatEther(presaleStatus._tokensRemainingInCurrentStage),
          totalBNBRaised: ethers.formatEther(presaleStatus._totalBNBRaised),
          totalTokensSold: ethers.formatEther(presaleStatus._totalTokensSold),
          currentRate: presaleStatus._currentRate.toString(),
          isActive: presaleStatus._isActive
        },
        balances: {
          deployer: ethers.formatEther(tokenBalance),
          presaleContract: ethers.formatEther(presaleBalance),
          invitationContract: ethers.formatEther(invitationBalance)
        },
        whitelist: testAddresses
      },
      nextSteps: [
        "配置HAOX/BNB交易对地址",
        "测试预售购买功能",
        "测试邀请奖励功能",
        "更新前端环境变量",
        "进行全面测试"
      ]
    };

    // 保存配置报告
    const configPath = path.join(__dirname, "../deployments/v2-configuration-report.json");
    fs.writeFileSync(configPath, JSON.stringify(configReport, null, 2));

    console.log("\n🎉 配置完成！");
    console.log("==========================================");
    console.log(`📄 配置报告已保存: ${configPath}`);
    console.log("\n✅ 系统状态:");
    console.log(`   代币合约: ${tokenStatus.isPaused ? '暂停' : '正常'}`);
    console.log(`   预售合约: ${presaleStatus._isActive ? '激活' : '未激活'}`);
    console.log(`   白名单地址: ${testAddresses.length} 个`);
    console.log("\n📝 下一步操作:");
    configReport.nextSteps.forEach((step, index) => {
      console.log(`   ${index + 1}. ${step}`);
    });

  } catch (error) {
    console.error("❌ 配置失败:", error);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
