const { ethers } = require("hardhat");
const fs = require("fs");
const path = require("path");

// 从部署报告中读取合约地址
function loadDeploymentReport() {
  const reportPath = path.join(__dirname, "../deployments/v2-deployment-complete.json");
  if (!fs.existsSync(reportPath)) {
    throw new Error("部署报告不存在，请先运行部署脚本");
  }
  return JSON.parse(fs.readFileSync(reportPath, "utf8"));
}

async function main() {
  console.log("🔍 开始验证SocioMint V2智能合约系统...\n");
  
  const [deployer] = await ethers.getSigners();
  console.log("验证账户:", deployer.address);
  
  // 加载部署信息
  const deploymentReport = loadDeploymentReport();
  const contracts = {
    haoxToken: deploymentReport.contracts.HAOXTokenV2.address,
    presale: deploymentReport.contracts.HAOXPresaleV2.address,
    invitation: deploymentReport.contracts.HAOXInvitationV2.address,
    priceOracle: deploymentReport.contracts.HAOXPriceOracleV2.address,
    vesting: deploymentReport.contracts.HAOXVestingV2.address
  };
  
  console.log("📋 验证的合约地址:");
  Object.entries(contracts).forEach(([name, address]) => {
    console.log(`   ${name}: ${address}`);
  });
  console.log();

  const verificationResults = [];

  try {
    // 获取合约实例
    const haoxToken = await ethers.getContractAt("HAOXTokenV2", contracts.haoxToken);
    const presaleContract = await ethers.getContractAt("HAOXPresaleV2", contracts.presale);
    const invitationContract = await ethers.getContractAt("HAOXInvitationV2", contracts.invitation);
    const priceOracle = await ethers.getContractAt("HAOXPriceOracleV2", contracts.priceOracle);
    const vestingContract = await ethers.getContractAt("HAOXVestingV2", contracts.vesting);

    // 1. 验证代币合约
    console.log("🪙 1. 验证HAOXTokenV2合约...");
    try {
      const name = await haoxToken.name();
      const symbol = await haoxToken.symbol();
      const totalSupply = await haoxToken.totalSupply();
      const decimals = await haoxToken.decimals();
      
      console.log(`   名称: ${name}`);
      console.log(`   符号: ${symbol}`);
      console.log(`   总供应量: ${ethers.formatEther(totalSupply)} HAOX`);
      console.log(`   精度: ${decimals}`);
      
      // 验证常量
      const expectedTotalSupply = ethers.parseEther("500000000"); // 5亿初始解锁
      if (totalSupply.toString() === expectedTotalSupply.toString()) {
        console.log("✅ 代币合约验证通过");
        verificationResults.push({ contract: "HAOXTokenV2", status: "PASS", details: "所有参数正确" });
      } else {
        console.log("❌ 总供应量不匹配");
        verificationResults.push({ contract: "HAOXTokenV2", status: "FAIL", details: "总供应量不匹配" });
      }
    } catch (error) {
      console.log("❌ 代币合约验证失败:", error.message);
      verificationResults.push({ contract: "HAOXTokenV2", status: "FAIL", details: error.message });
    }

    // 2. 验证预售合约
    console.log("\n💰 2. 验证HAOXPresaleV2合约...");
    try {
      const totalPresaleTokens = await presaleContract.TOTAL_PRESALE_TOKENS();
      const targetBNB = await presaleContract.TARGET_BNB();
      const totalStages = await presaleContract.TOTAL_STAGES();
      const tokensPerStage = await presaleContract.TOKENS_PER_STAGE();
      const initialRate = await presaleContract.INITIAL_RATE();
      
      console.log(`   预售代币总量: ${ethers.formatEther(totalPresaleTokens)} HAOX`);
      console.log(`   目标BNB: ${ethers.formatEther(targetBNB)} BNB`);
      console.log(`   总阶段数: ${totalStages}`);
      console.log(`   每阶段代币: ${ethers.formatEther(tokensPerStage)} HAOX`);
      console.log(`   初始汇率: ${initialRate} HAOX/BNB`);
      
      // 验证常量
      const expectedTokens = ethers.parseEther("200000000"); // 2亿HAOX
      const expectedBNB = ethers.parseEther("320"); // 320 BNB
      
      if (totalPresaleTokens.toString() === expectedTokens.toString() && 
          targetBNB.toString() === expectedBNB.toString() &&
          totalStages.toString() === "100" &&
          initialRate.toString() === "1912125") {
        console.log("✅ 预售合约验证通过");
        verificationResults.push({ contract: "HAOXPresaleV2", status: "PASS", details: "所有参数正确" });
      } else {
        console.log("❌ 预售合约参数不匹配");
        verificationResults.push({ contract: "HAOXPresaleV2", status: "FAIL", details: "参数不匹配" });
      }
    } catch (error) {
      console.log("❌ 预售合约验证失败:", error.message);
      verificationResults.push({ contract: "HAOXPresaleV2", status: "FAIL", details: error.message });
    }

    // 3. 验证邀请合约
    console.log("\n🎁 3. 验证HAOXInvitationV2合约...");
    try {
      const baseReward = await invitationContract.BASE_REWARD();
      const milestone5 = await invitationContract.MILESTONE_5_REWARD();
      const milestone10 = await invitationContract.MILESTONE_10_REWARD();
      const totalPool = await invitationContract.TOTAL_REWARD_POOL();
      
      console.log(`   基础奖励: ${ethers.formatEther(baseReward)} HAOX`);
      console.log(`   5人里程碑: ${ethers.formatEther(milestone5)} HAOX`);
      console.log(`   10人里程碑: ${ethers.formatEther(milestone10)} HAOX`);
      console.log(`   总奖励池: ${ethers.formatEther(totalPool)} HAOX`);
      
      // 验证奖励数值
      if (ethers.formatEther(baseReward) === "1000.0" &&
          ethers.formatEther(milestone5) === "10000.0" &&
          ethers.formatEther(milestone10) === "50000.0" &&
          ethers.formatEther(totalPool) === "300000000.0") {
        console.log("✅ 邀请合约验证通过");
        verificationResults.push({ contract: "HAOXInvitationV2", status: "PASS", details: "所有奖励参数正确" });
      } else {
        console.log("❌ 邀请合约奖励参数不匹配");
        verificationResults.push({ contract: "HAOXInvitationV2", status: "FAIL", details: "奖励参数不匹配" });
      }
    } catch (error) {
      console.log("❌ 邀请合约验证失败:", error.message);
      verificationResults.push({ contract: "HAOXInvitationV2", status: "FAIL", details: error.message });
    }

    // 4. 验证解锁合约
    console.log("\n🔓 4. 验证HAOXVestingV2合约...");
    try {
      const totalRounds = await vestingContract.TOTAL_ROUNDS();
      const tokensPerRound = await vestingContract.TOKENS_PER_ROUND();
      const initialTriggerPrice = await vestingContract.INITIAL_TRIGGER_PRICE();
      const currentRound = await vestingContract.currentRound();
      
      console.log(`   总轮次: ${totalRounds}`);
      console.log(`   每轮代币: ${ethers.formatEther(tokensPerRound)} HAOX`);
      console.log(`   初始触发价格: $${initialTriggerPrice / 100000000} USD`);
      console.log(`   当前轮次: ${currentRound}`);
      
      // 验证解锁参数
      if (totalRounds.toString() === "31" &&
          ethers.formatEther(tokensPerRound) === "150000000.0" &&
          initialTriggerPrice.toString() === "272000" &&
          currentRound.toString() === "1") {
        console.log("✅ 解锁合约验证通过");
        verificationResults.push({ contract: "HAOXVestingV2", status: "PASS", details: "所有解锁参数正确" });
      } else {
        console.log("❌ 解锁合约参数不匹配");
        verificationResults.push({ contract: "HAOXVestingV2", status: "FAIL", details: "解锁参数不匹配" });
      }
    } catch (error) {
      console.log("❌ 解锁合约验证失败:", error.message);
      verificationResults.push({ contract: "HAOXVestingV2", status: "FAIL", details: error.message });
    }

    // 5. 验证价格预言机
    console.log("\n📊 5. 验证HAOXPriceOracleV2合约...");
    try {
      const maxDeviation = await priceOracle.MAX_PRICE_DEVIATION();
      const updateInterval = await priceOracle.UPDATE_INTERVAL();
      const minConfidence = await priceOracle.MIN_CONFIDENCE();
      const emergencyMode = await priceOracle.emergencyMode();
      
      console.log(`   最大价格偏差: ${maxDeviation / 100}%`);
      console.log(`   更新间隔: ${updateInterval / 3600} 小时`);
      console.log(`   最低置信度: ${minConfidence}%`);
      console.log(`   紧急模式: ${emergencyMode}`);
      
      // 验证预言机参数
      if (maxDeviation.toString() === "500" &&
          updateInterval.toString() === "3600" &&
          minConfidence.toString() === "80" &&
          !emergencyMode) {
        console.log("✅ 价格预言机验证通过");
        verificationResults.push({ contract: "HAOXPriceOracleV2", status: "PASS", details: "所有预言机参数正确" });
      } else {
        console.log("❌ 价格预言机参数不匹配");
        verificationResults.push({ contract: "HAOXPriceOracleV2", status: "FAIL", details: "预言机参数不匹配" });
      }
    } catch (error) {
      console.log("❌ 价格预言机验证失败:", error.message);
      verificationResults.push({ contract: "HAOXPriceOracleV2", status: "FAIL", details: error.message });
    }

    // 6. 验证合约关系
    console.log("\n🔗 6. 验证合约关系...");
    try {
      const tokenStatus = await haoxToken.getContractStatus();
      
      console.log("合约关系状态:");
      console.log(`   预售合约: ${tokenStatus.presale}`);
      console.log(`   邀请合约: ${tokenStatus.invitation}`);
      console.log(`   解锁合约: ${tokenStatus.vesting}`);
      console.log(`   价格预言机: ${tokenStatus.oracle}`);
      
      // 检查是否所有关系都已设置
      const allSet = tokenStatus.presale !== ethers.ZeroAddress &&
                     tokenStatus.invitation !== ethers.ZeroAddress &&
                     tokenStatus.vesting !== ethers.ZeroAddress &&
                     tokenStatus.oracle !== ethers.ZeroAddress;
      
      if (allSet) {
        console.log("✅ 合约关系验证通过");
        verificationResults.push({ contract: "ContractRelations", status: "PASS", details: "所有合约关系已设置" });
      } else {
        console.log("⚠️  部分合约关系未设置（可能需要等待时间锁）");
        verificationResults.push({ contract: "ContractRelations", status: "PARTIAL", details: "部分关系未设置，需要时间锁" });
      }
    } catch (error) {
      console.log("❌ 合约关系验证失败:", error.message);
      verificationResults.push({ contract: "ContractRelations", status: "FAIL", details: error.message });
    }

    // 7. 生成验证报告
    console.log("\n📋 7. 生成验证报告...");
    
    const passCount = verificationResults.filter(r => r.status === "PASS").length;
    const failCount = verificationResults.filter(r => r.status === "FAIL").length;
    const partialCount = verificationResults.filter(r => r.status === "PARTIAL").length;
    
    const verificationReport = {
      timestamp: new Date().toISOString(),
      network: "BSC Testnet",
      summary: {
        total: verificationResults.length,
        passed: passCount,
        failed: failCount,
        partial: partialCount,
        success_rate: `${((passCount + partialCount) / verificationResults.length * 100).toFixed(1)}%`
      },
      contracts: contracts,
      results: verificationResults,
      recommendations: []
    };

    // 添加建议
    if (failCount > 0) {
      verificationReport.recommendations.push("修复失败的合约验证问题");
    }
    if (partialCount > 0) {
      verificationReport.recommendations.push("等待时间锁到期后完成合约关系设置");
    }
    if (passCount === verificationResults.length) {
      verificationReport.recommendations.push("所有验证通过，可以开始测试");
    }

    // 保存验证报告
    const verifyPath = path.join(__dirname, "../deployments/v2-verification-report.json");
    fs.writeFileSync(verifyPath, JSON.stringify(verificationReport, null, 2));

    console.log("\n🎉 验证完成！");
    console.log("==========================================");
    console.log("📊 验证结果总结:");
    console.log(`   ✅ 通过: ${passCount}`);
    console.log(`   ⚠️  部分: ${partialCount}`);
    console.log(`   ❌ 失败: ${failCount}`);
    console.log(`   📈 成功率: ${verificationReport.summary.success_rate}`);
    console.log("==========================================");
    console.log(`📄 验证报告已保存: ${verifyPath}`);
    
    if (verificationReport.recommendations.length > 0) {
      console.log("\n📝 建议:");
      verificationReport.recommendations.forEach((rec, index) => {
        console.log(`   ${index + 1}. ${rec}`);
      });
    }

  } catch (error) {
    console.error("❌ 验证失败:", error);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
