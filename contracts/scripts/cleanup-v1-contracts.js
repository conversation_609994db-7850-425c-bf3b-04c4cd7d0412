import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 要删除的V1合约文件
const v1ContractsToDelete = [
  'contracts/HAOXToken.sol',
  'contracts/HAOXPresale.sol', 
  'contracts/HAOXInvitation.sol',
  'contracts/HAOXVesting.sol',
  'contracts/SimpleHAOX.sol'
];

// 要保留的V2合约文件
const v2ContractsToKeep = [
  'contracts/HAOXTokenV2.sol',
  'contracts/HAOXPresaleV2.sol',
  'contracts/HAOXInvitationV2.sol',
  'contracts/HAOXVestingV2.sol',
  'contracts/HAOXPriceOracleV2.sol'
];

console.log('🧹 开始清理V1合约文件...\n');

// 删除V1合约文件
v1ContractsToDelete.forEach(filePath => {
  const fullPath = path.join(__dirname, '..', filePath);
  if (fs.existsSync(fullPath)) {
    fs.unlinkSync(fullPath);
    console.log(`✅ 删除: ${filePath}`);
  } else {
    console.log(`⚠️  文件不存在: ${filePath}`);
  }
});

// 检查V2合约文件是否存在
console.log('\n📋 检查V2合约文件:');
v2ContractsToKeep.forEach(filePath => {
  const fullPath = path.join(__dirname, '..', filePath);
  if (fs.existsSync(fullPath)) {
    console.log(`✅ 存在: ${filePath}`);
  } else {
    console.log(`❌ 缺失: ${filePath}`);
  }
});

// 删除旧的HAOX.sol文件（如果存在）
const oldHaoxPath = path.join(__dirname, '..', 'HAOX.sol');
if (fs.existsSync(oldHaoxPath)) {
  fs.unlinkSync(oldHaoxPath);
  console.log(`✅ 删除: HAOX.sol`);
}

console.log('\n🎉 V1合约清理完成！');
