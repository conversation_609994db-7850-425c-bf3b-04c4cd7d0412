import hre from "hardhat";

const { ethers } = hre;

async function main() {
  console.log("🧪 部署HAOXTokenV2Test测试版合约...\n");
  
  const [deployer] = await ethers.getSigners();
  console.log("部署账户:", deployer.address);
  
  const balance = await deployer.provider.getBalance(deployer.address);
  console.log("账户余额:", ethers.formatEther(balance), "BNB\n");

  try {
    // 部署测试版代币合约
    console.log("📄 部署HAOXTokenV2Test合约...");
    const HAOXTokenV2Test = await ethers.getContractFactory("HAOXTokenV2Test");
    const haoxTokenTest = await HAOXTokenV2Test.deploy();
    
    console.log("等待部署确认...");
    await haoxTokenTest.waitForDeployment();
    
    const haoxTokenTestAddress = await haoxTokenTest.getAddress();
    const deployTx = haoxTokenTest.deploymentTransaction();
    
    console.log("等待交易确认...");
    const receipt = await deployTx.wait();
    
    console.log("\n✅ HAOXTokenV2Test 部署成功!");
    console.log("==========================================");
    console.log("合约地址:", haoxTokenTestAddress);
    console.log("交易哈希:", deployTx.hash);
    console.log("Gas使用:", receipt.gasUsed.toString());
    console.log("Gas价格:", deployTx.gasPrice.toString());
    console.log("BNB消耗:", ethers.formatEther(receipt.gasUsed * deployTx.gasPrice));
    console.log("区块号:", receipt.blockNumber);
    console.log("==========================================");

    // 验证合约基本信息
    console.log("\n🔍 验证合约信息...");
    const name = await haoxTokenTest.name();
    const symbol = await haoxTokenTest.symbol();
    const totalSupply = await haoxTokenTest.totalSupply();
    const decimals = await haoxTokenTest.decimals();
    const timelockDelay = await haoxTokenTest.TIMELOCK_DELAY();
    const maxPauseDuration = await haoxTokenTest.MAX_PAUSE_DURATION();
    
    console.log("代币名称:", name);
    console.log("代币符号:", symbol);
    console.log("总供应量:", ethers.formatEther(totalSupply), "HAOX");
    console.log("精度:", decimals);
    console.log("时间锁延迟:", (Number(timelockDelay) / 60).toString(), "分钟");
    console.log("最大暂停时间:", (Number(maxPauseDuration) / 60).toString(), "分钟");

    // 测试时间锁功能
    console.log("\n🧪 测试时间锁功能...");
    
    // 使用现有的预售合约地址进行测试
    const testPresaleAddress = "******************************************";
    
    console.log("第一次调用setPresaleContract（创建时间锁）...");
    const tx1 = await haoxTokenTest.setPresaleContract(testPresaleAddress);
    await tx1.wait();
    console.log("✅ 时间锁创建成功，5分钟后可执行");
    
    // 生成测试配置
    const testConfig = {
      network: "BSC Testnet",
      testTokenAddress: haoxTokenTestAddress,
      timelockDelay: "5 minutes",
      maxPauseDuration: "10 minutes",
      deploymentTime: new Date().toISOString(),
      instructions: [
        "1. 等待5分钟后可以执行时间锁操作",
        "2. 使用此测试合约进行快速迭代测试",
        "3. 测试完成后使用正式版合约进行生产部署",
        "4. 测试版合约仅用于开发测试，不要用于生产环境"
      ]
    };

    console.log("\n📋 测试版合约配置:");
    console.log("合约地址:", haoxTokenTestAddress);
    console.log("时间锁延迟:", "5分钟");
    console.log("暂停时间:", "10分钟");
    console.log("用途:", "快速测试和开发");

  } catch (error) {
    console.error("❌ 部署失败:", error);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
