import hre from "hardhat";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const { ethers } = hre;
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 从部署报告中读取合约地址
function loadDeploymentReport() {
  const reportPath = path.join(__dirname, "../deployments/v2-deployment-complete.json");
  if (!fs.existsSync(reportPath)) {
    throw new Error("部署报告不存在，请先运行部署脚本");
  }
  return JSON.parse(fs.readFileSync(reportPath, "utf8"));
}

async function main() {
  console.log("🧪 开始基础功能测试...\n");
  
  const [deployer] = await ethers.getSigners();
  console.log("测试账户:", deployer.address);
  
  // 加载部署信息
  const deploymentReport = loadDeploymentReport();
  const contracts = {
    haoxToken: deploymentReport.contracts.HAOXTokenV2.address,
    presale: deploymentReport.contracts.HAOXPresaleV2.address,
    invitation: deploymentReport.contracts.HAOXInvitationV2.address,
    priceOracle: deploymentReport.contracts.HAOXPriceOracleV2.address,
    vesting: deploymentReport.contracts.HAOXVestingV2.address
  };
  
  console.log("📋 测试的合约地址:");
  Object.entries(contracts).forEach(([name, address]) => {
    console.log(`   ${name}: ${address}`);
  });
  console.log();

  const testResults = [];

  try {
    // 获取合约实例
    const haoxToken = await ethers.getContractAt("HAOXTokenV2", contracts.haoxToken);
    const presaleContract = await ethers.getContractAt("HAOXPresaleV2", contracts.presale);
    const invitationContract = await ethers.getContractAt("HAOXInvitationV2", contracts.invitation);
    const priceOracle = await ethers.getContractAt("HAOXPriceOracleV2", contracts.priceOracle);
    const vestingContract = await ethers.getContractAt("HAOXVestingV2", contracts.vesting);

    // 1. 测试代币合约基础功能
    console.log("🪙 1. 测试HAOXTokenV2基础功能...");
    try {
      const name = await haoxToken.name();
      const symbol = await haoxToken.symbol();
      const totalSupply = await haoxToken.totalSupply();
      const decimals = await haoxToken.decimals();
      const balance = await haoxToken.balanceOf(deployer.address);
      
      console.log(`   名称: ${name}`);
      console.log(`   符号: ${symbol}`);
      console.log(`   总供应量: ${ethers.formatEther(totalSupply)} HAOX`);
      console.log(`   精度: ${decimals}`);
      console.log(`   部署者余额: ${ethers.formatEther(balance)} HAOX`);
      
      testResults.push({ test: "HAOXTokenV2基础功能", status: "PASS", details: "所有基础功能正常" });
    } catch (error) {
      console.log("❌ 代币合约测试失败:", error.message);
      testResults.push({ test: "HAOXTokenV2基础功能", status: "FAIL", details: error.message });
    }

    // 2. 测试预售合约状态
    console.log("\n💰 2. 测试HAOXPresaleV2状态...");
    try {
      const presaleStatus = await presaleContract.getPresaleStatus();
      const totalPresaleTokens = await presaleContract.TOTAL_PRESALE_TOKENS();
      const targetBNB = await presaleContract.TARGET_BNB();
      
      console.log(`   当前阶段: ${presaleStatus._currentStage}`);
      console.log(`   当前阶段剩余代币: ${ethers.formatEther(presaleStatus._tokensRemainingInCurrentStage)} HAOX`);
      console.log(`   总募集BNB: ${ethers.formatEther(presaleStatus._totalBNBRaised)} BNB`);
      console.log(`   总售出代币: ${ethers.formatEther(presaleStatus._totalTokensSold)} HAOX`);
      console.log(`   当前汇率: ${presaleStatus._currentRate} HAOX/BNB`);
      console.log(`   预售激活: ${presaleStatus._isActive}`);
      console.log(`   预售代币总量: ${ethers.formatEther(totalPresaleTokens)} HAOX`);
      console.log(`   目标BNB: ${ethers.formatEther(targetBNB)} BNB`);
      
      testResults.push({ test: "HAOXPresaleV2状态查询", status: "PASS", details: "预售状态查询正常" });
    } catch (error) {
      console.log("❌ 预售合约测试失败:", error.message);
      testResults.push({ test: "HAOXPresaleV2状态查询", status: "FAIL", details: error.message });
    }

    // 3. 测试邀请合约参数
    console.log("\n🎁 3. 测试HAOXInvitationV2参数...");
    try {
      const baseReward = await invitationContract.BASE_REWARD();
      const milestone5 = await invitationContract.MILESTONE_5_REWARD();
      const milestone10 = await invitationContract.MILESTONE_10_REWARD();
      const totalPool = await invitationContract.TOTAL_REWARD_POOL();
      
      console.log(`   基础奖励: ${ethers.formatEther(baseReward)} HAOX`);
      console.log(`   5人里程碑: ${ethers.formatEther(milestone5)} HAOX`);
      console.log(`   10人里程碑: ${ethers.formatEther(milestone10)} HAOX`);
      console.log(`   总奖励池: ${ethers.formatEther(totalPool)} HAOX`);
      
      testResults.push({ test: "HAOXInvitationV2参数", status: "PASS", details: "邀请奖励参数正确" });
    } catch (error) {
      console.log("❌ 邀请合约测试失败:", error.message);
      testResults.push({ test: "HAOXInvitationV2参数", status: "FAIL", details: error.message });
    }

    // 4. 测试价格预言机
    console.log("\n📊 4. 测试HAOXPriceOracleV2...");
    try {
      const maxDeviation = await priceOracle.MAX_PRICE_DEVIATION();
      const updateInterval = await priceOracle.UPDATE_INTERVAL();
      const minConfidence = await priceOracle.MIN_CONFIDENCE();
      const emergencyMode = await priceOracle.emergencyMode();
      
      console.log(`   最大价格偏差: ${maxDeviation / 100}%`);
      console.log(`   更新间隔: ${updateInterval / 3600} 小时`);
      console.log(`   最低置信度: ${minConfidence}%`);
      console.log(`   紧急模式: ${emergencyMode}`);
      
      // 尝试获取价格（可能会失败，因为还没有HAOX/BNB交易对）
      try {
        const latestPrice = await priceOracle.getLatestPrice();
        console.log(`   当前价格: ${latestPrice}`);
      } catch (priceError) {
        console.log("   ⚠️  价格获取失败（预期，因为HAOX/BNB交易对未设置）");
      }
      
      testResults.push({ test: "HAOXPriceOracleV2参数", status: "PASS", details: "价格预言机参数正确" });
    } catch (error) {
      console.log("❌ 价格预言机测试失败:", error.message);
      testResults.push({ test: "HAOXPriceOracleV2参数", status: "FAIL", details: error.message });
    }

    // 5. 测试解锁合约
    console.log("\n🔓 5. 测试HAOXVestingV2...");
    try {
      const totalRounds = await vestingContract.TOTAL_ROUNDS();
      const tokensPerRound = await vestingContract.TOKENS_PER_ROUND();
      const initialTriggerPrice = await vestingContract.INITIAL_TRIGGER_PRICE();
      const currentRound = await vestingContract.currentRound();
      
      console.log(`   总轮次: ${totalRounds}`);
      console.log(`   每轮代币: ${ethers.formatEther(tokensPerRound)} HAOX`);
      console.log(`   初始触发价格: $${(Number(initialTriggerPrice) / 100000000).toFixed(8)} USD`);
      console.log(`   当前轮次: ${currentRound}`);
      
      testResults.push({ test: "HAOXVestingV2参数", status: "PASS", details: "解锁合约参数正确" });
    } catch (error) {
      console.log("❌ 解锁合约测试失败:", error.message);
      testResults.push({ test: "HAOXVestingV2参数", status: "FAIL", details: error.message });
    }

    // 6. 测试合约关系（预期会失败，因为还没有配置）
    console.log("\n🔗 6. 测试合约关系...");
    try {
      const tokenStatus = await haoxToken.getContractStatus();
      
      console.log("合约关系状态:");
      console.log(`   预售合约: ${tokenStatus.presale}`);
      console.log(`   邀请合约: ${tokenStatus.invitation}`);
      console.log(`   解锁合约: ${tokenStatus.vesting}`);
      console.log(`   价格预言机: ${tokenStatus.oracle}`);
      
      const allSet = tokenStatus.presale !== ethers.ZeroAddress &&
                     tokenStatus.invitation !== ethers.ZeroAddress &&
                     tokenStatus.vesting !== ethers.ZeroAddress &&
                     tokenStatus.oracle !== ethers.ZeroAddress;
      
      if (allSet) {
        testResults.push({ test: "合约关系", status: "PASS", details: "所有合约关系已设置" });
      } else {
        testResults.push({ test: "合约关系", status: "PENDING", details: "等待24小时时间锁到期" });
      }
    } catch (error) {
      console.log("❌ 合约关系测试失败:", error.message);
      testResults.push({ test: "合约关系", status: "FAIL", details: error.message });
    }

    // 生成测试报告
    const passCount = testResults.filter(r => r.status === "PASS").length;
    const failCount = testResults.filter(r => r.status === "FAIL").length;
    const pendingCount = testResults.filter(r => r.status === "PENDING").length;
    
    const testReport = {
      timestamp: new Date().toISOString(),
      network: "BSC Testnet",
      summary: {
        total: testResults.length,
        passed: passCount,
        failed: failCount,
        pending: pendingCount,
        success_rate: `${((passCount / testResults.length) * 100).toFixed(1)}%`
      },
      contracts: contracts,
      results: testResults
    };

    // 保存测试报告
    const testPath = path.join(__dirname, "../deployments/v2-basic-test-report.json");
    fs.writeFileSync(testPath, JSON.stringify(testReport, null, 2));

    console.log("\n🎉 基础功能测试完成！");
    console.log("==========================================");
    console.log("📊 测试结果总结:");
    console.log(`   ✅ 通过: ${passCount}`);
    console.log(`   ⏳ 待定: ${pendingCount}`);
    console.log(`   ❌ 失败: ${failCount}`);
    console.log(`   📈 成功率: ${testReport.summary.success_rate}`);
    console.log("==========================================");
    console.log(`📄 测试报告已保存: ${testPath}`);

  } catch (error) {
    console.error("❌ 测试失败:", error);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
