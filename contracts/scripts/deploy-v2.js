import hre from "hardhat";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const { ethers } = hre;
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 部署配置
const DEPLOYMENT_CONFIG = {
  // BSC测试网Chainlink BNB/USD价格源
  CHAINLINK_BNB_USD: "******************************************",
  // 初始BNB价格（用于测试，8位小数）
  INITIAL_BNB_PRICE: ethers.parseUnits("850", 8), // $850
  // 项目钱包地址
  PROJECT_WALLET: "******************************************",
  // 社区钱包地址  
  COMMUNITY_WALLET: "******************************************", // 暂时使用同一个地址
  // WBNB地址（BSC测试网）
  WBNB_ADDRESS: "******************************************",
};

async function main() {
  console.log("🚀 开始部署SocioMint V2智能合约系统...\n");
  
  const [deployer] = await ethers.getSigners();
  console.log("部署账户:", deployer.address);
  console.log("账户余额:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)), "BNB\n");

  const deployedContracts = {};
  const deploymentLog = [];

  try {
    // 1. 部署HAOX代币合约
    console.log("📄 1. 部署HAOXTokenV2合约...");
    const HAOXTokenV2 = await ethers.getContractFactory("HAOXTokenV2");
    const haoxToken = await HAOXTokenV2.deploy();
    await haoxToken.waitForDeployment();
    
    const haoxTokenAddress = await haoxToken.getAddress();
    deployedContracts.haoxToken = haoxTokenAddress;
    deploymentLog.push(`HAOXTokenV2: ${haoxTokenAddress}`);
    console.log("✅ HAOXTokenV2 部署成功:", haoxTokenAddress);

    // 2. 部署价格预言机合约
    console.log("\n📊 2. 部署HAOXPriceOracleV2合约...");
    const HAOXPriceOracleV2 = await ethers.getContractFactory("HAOXPriceOracleV2");

    // 使用一个有效的地址作为占位符，避免零地址验证错误
    // 后续可以通过setHaoxBnbPair函数更新
    const placeholderPairAddress = "******************************************"; // 临时占位符
    const priceOracle = await HAOXPriceOracleV2.deploy(
      DEPLOYMENT_CONFIG.CHAINLINK_BNB_USD,
      placeholderPairAddress, // HAOX/BNB交易对地址占位符
      haoxTokenAddress,
      DEPLOYMENT_CONFIG.WBNB_ADDRESS
    );
    await priceOracle.waitForDeployment();
    
    const priceOracleAddress = await priceOracle.getAddress();
    deployedContracts.priceOracle = priceOracleAddress;
    deploymentLog.push(`HAOXPriceOracleV2: ${priceOracleAddress}`);
    console.log("✅ HAOXPriceOracleV2 部署成功:", priceOracleAddress);

    // 3. 部署邀请奖励合约
    console.log("\n🎁 3. 部署HAOXInvitationV2合约...");
    const HAOXInvitationV2 = await ethers.getContractFactory("HAOXInvitationV2");
    const invitationContract = await HAOXInvitationV2.deploy(haoxTokenAddress);
    await invitationContract.waitForDeployment();
    
    const invitationAddress = await invitationContract.getAddress();
    deployedContracts.invitation = invitationAddress;
    deploymentLog.push(`HAOXInvitationV2: ${invitationAddress}`);
    console.log("✅ HAOXInvitationV2 部署成功:", invitationAddress);

    // 4. 部署预售合约
    console.log("\n💰 4. 部署HAOXPresaleV2合约...");
    const HAOXPresaleV2 = await ethers.getContractFactory("HAOXPresaleV2");
    const presaleContract = await HAOXPresaleV2.deploy(
      haoxTokenAddress,
      invitationAddress,
      priceOracleAddress
    );
    await presaleContract.waitForDeployment();
    
    const presaleAddress = await presaleContract.getAddress();
    deployedContracts.presale = presaleAddress;
    deploymentLog.push(`HAOXPresaleV2: ${presaleAddress}`);
    console.log("✅ HAOXPresaleV2 部署成功:", presaleAddress);

    // 5. 部署解锁合约
    console.log("\n🔓 5. 部署HAOXVestingV2合约...");
    const HAOXVestingV2 = await ethers.getContractFactory("HAOXVestingV2");
    const vestingContract = await HAOXVestingV2.deploy(
      haoxTokenAddress,
      priceOracleAddress,
      DEPLOYMENT_CONFIG.PROJECT_WALLET,
      DEPLOYMENT_CONFIG.COMMUNITY_WALLET
    );
    await vestingContract.waitForDeployment();
    
    const vestingAddress = await vestingContract.getAddress();
    deployedContracts.vesting = vestingAddress;
    deploymentLog.push(`HAOXVestingV2: ${vestingAddress}`);
    console.log("✅ HAOXVestingV2 部署成功:", vestingAddress);

    // 6. 配置合约关系
    console.log("\n⚙️  6. 配置合约关系...");
    
    // 设置代币合约的关联地址（需要时间锁，先创建时间锁）
    console.log("创建预售合约地址时间锁...");
    await haoxToken.setPresaleContract(presaleAddress);
    console.log("等待24小时后可执行预售合约设置");
    
    console.log("创建邀请合约地址时间锁...");
    await haoxToken.setInvitationContract(invitationAddress);
    console.log("等待24小时后可执行邀请合约设置");
    
    console.log("创建解锁合约地址时间锁...");
    await haoxToken.setVestingContract(vestingAddress);
    console.log("等待24小时后可执行解锁合约设置");
    
    console.log("创建价格预言机地址时间锁...");
    await haoxToken.setPriceOracle(priceOracleAddress);
    console.log("等待24小时后可执行价格预言机设置");

    // 初始化预售合约的汇率表（Gas优化）
    console.log("初始化预售合约汇率表...");
    await presaleContract.initializeRates();
    console.log("✅ 汇率表初始化完成");

    // 7. 转移代币到相应合约
    console.log("\n💸 7. 分配代币...");
    
    // 获取代币余额
    const totalSupply = await haoxToken.totalSupply();
    console.log("代币总供应量:", ethers.formatEther(totalSupply), "HAOX");
    
    // 预售合约需要2亿HAOX
    const presaleAllocation = ethers.parseEther("200000000"); // 2亿HAOX
    await haoxToken.transfer(presaleAddress, presaleAllocation);
    console.log("✅ 转移2亿HAOX到预售合约");
    
    // 邀请合约需要3亿HAOX
    const invitationAllocation = ethers.parseEther("300000000"); // 3亿HAOX
    await haoxToken.transfer(invitationAddress, invitationAllocation);
    console.log("✅ 转移3亿HAOX到邀请合约");

    // 8. 生成部署报告
    console.log("\n📋 8. 生成部署报告...");
    
    const deploymentReport = {
      network: "BSC Testnet",
      chainId: 97,
      timestamp: new Date().toISOString(),
      deployer: deployer.address,
      contracts: deployedContracts,
      gasUsed: "待计算",
      notes: [
        "所有合约部署成功",
        "代币合约地址设置需要24小时时间锁",
        "预售合约汇率表已初始化",
        "代币已分配到相应合约",
        "需要手动设置HAOX/BNB交易对地址"
      ]
    };

    // 保存部署报告
    const reportPath = path.join(__dirname, "../deployments/v2-deployment-report.json");
    fs.mkdirSync(path.dirname(reportPath), { recursive: true });
    fs.writeFileSync(reportPath, JSON.stringify(deploymentReport, null, 2));

    // 生成环境变量文件
    const envContent = `
# SocioMint V2 智能合约地址 (BSC测试网 - ${new Date().toISOString().split('T')[0]} 部署)
NEXT_PUBLIC_HAOX_TOKEN_ADDRESS_V2=${deployedContracts.haoxToken}
NEXT_PUBLIC_HAOX_PRESALE_ADDRESS_V2=${deployedContracts.presale}
NEXT_PUBLIC_HAOX_INVITATION_ADDRESS_V2=${deployedContracts.invitation}
NEXT_PUBLIC_HAOX_PRICE_ORACLE_ADDRESS_V2=${deployedContracts.priceOracle}
NEXT_PUBLIC_HAOX_VESTING_ADDRESS_V2=${deployedContracts.vesting}

# 兼容性地址（更新现有变量）
NEXT_PUBLIC_HAOX_TOKEN_ADDRESS=${deployedContracts.haoxToken}
NEXT_PUBLIC_HAOX_PRESALE_ADDRESS=${deployedContracts.presale}
NEXT_PUBLIC_HAOX_INVITATION_ADDRESS=${deployedContracts.invitation}
NEXT_PUBLIC_HAOX_CONTRACT_ADDRESS_BSC=${deployedContracts.haoxToken}
NEXT_PUBLIC_HAOX_CONTRACT_ADDRESS=${deployedContracts.haoxToken}
`;

    const envPath = path.join(__dirname, "../deployments/v2-env-variables.txt");
    fs.writeFileSync(envPath, envContent);

    // 打印部署总结
    console.log("\n🎉 部署完成！");
    console.log("==========================================");
    console.log("📋 合约地址总结:");
    deploymentLog.forEach(log => console.log(`   ${log}`));
    console.log("==========================================");
    console.log(`📄 部署报告已保存: ${reportPath}`);
    console.log(`🔧 环境变量已生成: ${envPath}`);
    console.log("\n⚠️  重要提醒:");
    console.log("1. 代币合约地址设置需要等待24小时时间锁");
    console.log("2. 需要手动设置HAOX/BNB交易对地址");
    console.log("3. 建议在测试网上进行充分测试");
    console.log("4. 更新前端环境变量文件");

  } catch (error) {
    console.error("❌ 部署失败:", error);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
