import hre from "hardhat";
import dotenv from "dotenv";

// 加载环境变量
dotenv.config({ path: '../.env.local' });

const { ethers } = hre;

async function main() {
  console.log("💰 开始代币转移操作...\n");
  
  // 从环境变量读取配置
  const DEPLOYER_PRIVATE_KEY = process.env.DEPLOYER_PRIVATE_KEY;
  const HAOX_TOKEN_ADDRESS = process.env.NEXT_PUBLIC_HAOX_TOKEN_ADDRESS;
  const PRESALE_CONTRACT_ADDRESS = process.env.PRESALE_CONTRACT_ADDRESS;
  const INVITATION_CONTRACT_ADDRESS = process.env.INVITATION_CONTRACT_ADDRESS;
  const PRESALE_TOKEN_ALLOCATION = process.env.PRESALE_TOKEN_ALLOCATION;
  const INVITATION_TOKEN_ALLOCATION = process.env.INVITATION_TOKEN_ALLOCATION;

  // 验证环境变量
  if (!DEPLOYER_PRIVATE_KEY) {
    throw new Error("请在.env.local中设置DEPLOYER_PRIVATE_KEY");
  }
  if (!HAOX_TOKEN_ADDRESS) {
    throw new Error("请在.env.local中设置NEXT_PUBLIC_HAOX_TOKEN_ADDRESS");
  }

  // 创建钱包实例
  const provider = new ethers.JsonRpcProvider(process.env.NEXT_PUBLIC_BSC_RPC_URL);
  const wallet = new ethers.Wallet(DEPLOYER_PRIVATE_KEY, provider);
  
  console.log("部署者钱包地址:", wallet.address);
  
  const balance = await provider.getBalance(wallet.address);
  console.log("钱包BNB余额:", ethers.formatEther(balance), "BNB");

  // 获取代币合约实例
  const haoxToken = await ethers.getContractAt("HAOXTokenV2", HAOX_TOKEN_ADDRESS, wallet);
  
  // 检查代币余额
  const tokenBalance = await haoxToken.balanceOf(wallet.address);
  console.log("钱包HAOX余额:", ethers.formatEther(tokenBalance), "HAOX");
  console.log();

  const transferResults = [];

  try {
    // 1. 转移代币到预售合约
    console.log("📤 1. 转移代币到预售合约...");
    console.log("目标地址:", PRESALE_CONTRACT_ADDRESS);
    console.log("转移数量:", ethers.formatEther(PRESALE_TOKEN_ALLOCATION), "HAOX");
    
    // 检查预售合约当前余额
    const presaleCurrentBalance = await haoxToken.balanceOf(PRESALE_CONTRACT_ADDRESS);
    console.log("预售合约当前余额:", ethers.formatEther(presaleCurrentBalance), "HAOX");
    
    if (presaleCurrentBalance < ethers.parseEther("200000000")) {
      console.log("执行转移...");
      const tx1 = await haoxToken.transfer(PRESALE_CONTRACT_ADDRESS, PRESALE_TOKEN_ALLOCATION, {
        gasLimit: process.env.DEFAULT_GAS_LIMIT || 500000,
        gasPrice: process.env.DEFAULT_GAS_PRICE || ethers.parseUnits("10", "gwei")
      });
      
      console.log("交易哈希:", tx1.hash);
      console.log("等待确认...");
      const receipt1 = await tx1.wait();
      
      console.log("✅ 预售合约转移成功!");
      console.log("Gas使用:", receipt1.gasUsed.toString());
      console.log("区块号:", receipt1.blockNumber);
      
      transferResults.push({
        target: "预售合约",
        address: PRESALE_CONTRACT_ADDRESS,
        amount: ethers.formatEther(PRESALE_TOKEN_ALLOCATION),
        txHash: tx1.hash,
        gasUsed: receipt1.gasUsed.toString(),
        status: "SUCCESS"
      });
    } else {
      console.log("⚠️  预售合约已有足够代币，跳过转移");
      transferResults.push({
        target: "预售合约",
        address: PRESALE_CONTRACT_ADDRESS,
        amount: "0",
        status: "SKIPPED",
        reason: "已有足够代币"
      });
    }

    // 2. 转移代币到邀请合约
    console.log("\n📤 2. 转移代币到邀请合约...");
    console.log("目标地址:", INVITATION_CONTRACT_ADDRESS);
    console.log("转移数量:", ethers.formatEther(INVITATION_TOKEN_ALLOCATION), "HAOX");
    
    // 检查邀请合约当前余额
    const invitationCurrentBalance = await haoxToken.balanceOf(INVITATION_CONTRACT_ADDRESS);
    console.log("邀请合约当前余额:", ethers.formatEther(invitationCurrentBalance), "HAOX");
    
    if (invitationCurrentBalance < ethers.parseEther("300000000")) {
      console.log("执行转移...");
      const tx2 = await haoxToken.transfer(INVITATION_CONTRACT_ADDRESS, INVITATION_TOKEN_ALLOCATION, {
        gasLimit: process.env.DEFAULT_GAS_LIMIT || 500000,
        gasPrice: process.env.DEFAULT_GAS_PRICE || ethers.parseUnits("10", "gwei")
      });
      
      console.log("交易哈希:", tx2.hash);
      console.log("等待确认...");
      const receipt2 = await tx2.wait();
      
      console.log("✅ 邀请合约转移成功!");
      console.log("Gas使用:", receipt2.gasUsed.toString());
      console.log("区块号:", receipt2.blockNumber);
      
      transferResults.push({
        target: "邀请合约",
        address: INVITATION_CONTRACT_ADDRESS,
        amount: ethers.formatEther(INVITATION_TOKEN_ALLOCATION),
        txHash: tx2.hash,
        gasUsed: receipt2.gasUsed.toString(),
        status: "SUCCESS"
      });
    } else {
      console.log("⚠️  邀请合约已有足够代币，跳过转移");
      transferResults.push({
        target: "邀请合约",
        address: INVITATION_CONTRACT_ADDRESS,
        amount: "0",
        status: "SKIPPED",
        reason: "已有足够代币"
      });
    }

    // 3. 验证转移结果
    console.log("\n🔍 3. 验证转移结果...");
    
    const finalTokenBalance = await haoxToken.balanceOf(wallet.address);
    const finalPresaleBalance = await haoxToken.balanceOf(PRESALE_CONTRACT_ADDRESS);
    const finalInvitationBalance = await haoxToken.balanceOf(INVITATION_CONTRACT_ADDRESS);
    
    console.log("最终余额分布:");
    console.log("  部署者钱包:", ethers.formatEther(finalTokenBalance), "HAOX");
    console.log("  预售合约:", ethers.formatEther(finalPresaleBalance), "HAOX");
    console.log("  邀请合约:", ethers.formatEther(finalInvitationBalance), "HAOX");
    
    const totalDistributed = finalPresaleBalance + finalInvitationBalance;
    console.log("  已分配总计:", ethers.formatEther(totalDistributed), "HAOX");
    console.log("  剩余可分配:", ethers.formatEther(finalTokenBalance), "HAOX");

    // 4. 生成转移报告
    const transferReport = {
      timestamp: new Date().toISOString(),
      network: "BSC Testnet",
      tokenContract: HAOX_TOKEN_ADDRESS,
      deployerWallet: wallet.address,
      transfers: transferResults,
      finalBalances: {
        deployer: ethers.formatEther(finalTokenBalance),
        presaleContract: ethers.formatEther(finalPresaleBalance),
        invitationContract: ethers.formatEther(finalInvitationBalance),
        totalDistributed: ethers.formatEther(totalDistributed)
      },
      verification: {
        presaleTargetMet: finalPresaleBalance >= ethers.parseEther("200000000"),
        invitationTargetMet: finalInvitationBalance >= ethers.parseEther("300000000"),
        allTransfersCompleted: transferResults.every(r => r.status === "SUCCESS" || r.status === "SKIPPED")
      }
    };

    // 保存转移报告
    const fs = await import('fs');
    const path = await import('path');
    const reportPath = path.join(process.cwd(), "deployments", "token-transfer-report.json");
    
    // 确保目录存在
    const deploymentDir = path.dirname(reportPath);
    if (!fs.existsSync(deploymentDir)) {
      fs.mkdirSync(deploymentDir, { recursive: true });
    }
    
    fs.writeFileSync(reportPath, JSON.stringify(transferReport, null, 2));

    console.log("\n🎉 代币转移完成!");
    console.log("==========================================");
    console.log("📊 转移总结:");
    transferResults.forEach(result => {
      console.log(`   ${result.target}: ${result.status} (${result.amount} HAOX)`);
    });
    console.log("==========================================");
    console.log(`📄 转移报告已保存: ${reportPath}`);
    
    // 验证结果
    if (transferReport.verification.allTransfersCompleted) {
      console.log("✅ 所有代币转移操作完成");
    } else {
      console.log("⚠️  部分转移操作未完成，请检查报告");
    }

  } catch (error) {
    console.error("❌ 代币转移失败:", error);
    
    // 记录失败信息
    transferResults.push({
      target: "转移操作",
      status: "FAILED",
      error: error.message
    });
    
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
