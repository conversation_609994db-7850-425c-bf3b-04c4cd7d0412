<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for contracts/HAOXTokenV2Test.sol</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../index.html">all files</a> / <a href="index.html">contracts/</a> HAOXTokenV2Test.sol
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>0/49</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>0/58</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>0/12</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>0/65</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;
&nbsp;
import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";
&nbsp;
/**
 * @title HAOXTokenV2Test
 * @dev 测试版HAOX代币合约 - 缩短时间锁用于快速测试
 * 
 * 测试版特性：
 * - 时间锁延迟：5分钟（而非24小时）
 * - 暂停时间：10分钟（而非72小时）
 * - 其他功能与正式版完全相同
 */
contract HAOXTokenV2Test is ERC20, Ownable, Pausable {
    
    // 代币经济学参数
    uint256 public constant TOTAL_SUPPLY = 50_000_000_000 * 10**18; // 500亿代币
    uint256 public constant INITIAL_UNLOCK = 500_000_000 * 10**18;  // 首轮解锁5亿代币 (10%)
    
    // 关联合约地址
    address public presaleContract;
    address public invitationContract;
    address public vestingContract;
    address public priceOracle;
    
    // 时间锁机制 - 测试版缩短时间
    struct TimeLock {
        uint256 unlockTime;
        bool executed;
    }
    
    mapping(bytes32 =&gt; TimeLock) public timeLocks;
    uint256 public constant TIMELOCK_DELAY = 5 minutes; // 测试版：5分钟
    uint256 public constant MAX_PAUSE_DURATION = 10 minutes; // 测试版：10分钟
    
    // 暂停相关
    uint256 public pauseStartTime;
    
    // 事件
    event ContractAddressUpdated(string contractType, address oldAddress, address newAddress);
    event TimeLockCreated(bytes32 indexed lockId, uint256 unlockTime);
    event TimeLockExecuted(bytes32 indexed lockId);
    event EmergencyPauseActivated(uint256 duration);
    
<span class="fstat-no" title="function not covered" >    constructor() ERC20("HAOX Token Test", "HAOX-TEST") Ownable(msg.sender) {</span>
        // 铸造初始解锁的代币到合约部署者
<span class="cstat-no" title="statement not covered" >        _mint(msg.sender, INITIAL_UNLOCK)</span>;
    }
    
    /**
     * @dev 设置预售合约地址（带时间锁）
     */
<span class="fstat-no" title="function not covered" >    function setPresaleContract(address _presaleContract) external onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        bytes32 lockId = keccak256(abi.encodePacked("setPresaleContract", _presaleContract, block.timestamp));</span>
        
<span class="cstat-no" title="statement not covered" >        if (timeLocks[lockId].unlockTime == 0) {</span>
            // 创建时间锁
            timeLocks[lockId] = TimeLock({
                unlockTime: block.timestamp + TIMELOCK_DELAY,
                executed: false
            });
<span class="cstat-no" title="statement not covered" >            emit TimeLockCreated(lockId, timeLocks[lockId].unlockTime);</span>
        } else {
            // 执行时间锁
<span class="cstat-no" title="statement not covered" >            require(block.timestamp &gt;= timeLocks[lockId].unlockTime, "TimeLock not expired")</span>;
<span class="cstat-no" title="statement not covered" >            require(!timeLocks[lockId].executed, "Already executed")</span>;
            
<span class="cstat-no" title="statement not covered" >            address oldAddress = presaleContract;</span>
            presaleContract = _presaleContract;
            timeLocks[lockId].executed = true;
            
<span class="cstat-no" title="statement not covered" >            emit ContractAddressUpdated("presale", oldAddress, _presaleContract);</span>
<span class="cstat-no" title="statement not covered" >            emit TimeLockExecuted(lockId);</span>
        }
    }
    
    /**
     * @dev 设置邀请合约地址（带时间锁）
     */
<span class="fstat-no" title="function not covered" >    function setInvitationContract(address _invitationContract) external onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        bytes32 lockId = keccak256(abi.encodePacked("setInvitationContract", _invitationContract, block.timestamp));</span>
        
<span class="cstat-no" title="statement not covered" >        if (timeLocks[lockId].unlockTime == 0) {</span>
            timeLocks[lockId] = TimeLock({
                unlockTime: block.timestamp + TIMELOCK_DELAY,
                executed: false
            });
<span class="cstat-no" title="statement not covered" >            emit TimeLockCreated(lockId, timeLocks[lockId].unlockTime);</span>
        } else {
<span class="cstat-no" title="statement not covered" >            require(block.timestamp &gt;= timeLocks[lockId].unlockTime, "TimeLock not expired")</span>;
<span class="cstat-no" title="statement not covered" >            require(!timeLocks[lockId].executed, "Already executed")</span>;
            
<span class="cstat-no" title="statement not covered" >            address oldAddress = invitationContract;</span>
            invitationContract = _invitationContract;
            timeLocks[lockId].executed = true;
            
<span class="cstat-no" title="statement not covered" >            emit ContractAddressUpdated("invitation", oldAddress, _invitationContract);</span>
<span class="cstat-no" title="statement not covered" >            emit TimeLockExecuted(lockId);</span>
        }
    }
    
    /**
     * @dev 设置解锁合约地址（带时间锁）
     */
<span class="fstat-no" title="function not covered" >    function setVestingContract(address _vestingContract) external onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        bytes32 lockId = keccak256(abi.encodePacked("setVestingContract", _vestingContract, block.timestamp));</span>
        
<span class="cstat-no" title="statement not covered" >        if (timeLocks[lockId].unlockTime == 0) {</span>
            timeLocks[lockId] = TimeLock({
                unlockTime: block.timestamp + TIMELOCK_DELAY,
                executed: false
            });
<span class="cstat-no" title="statement not covered" >            emit TimeLockCreated(lockId, timeLocks[lockId].unlockTime);</span>
        } else {
<span class="cstat-no" title="statement not covered" >            require(block.timestamp &gt;= timeLocks[lockId].unlockTime, "TimeLock not expired")</span>;
<span class="cstat-no" title="statement not covered" >            require(!timeLocks[lockId].executed, "Already executed")</span>;
            
<span class="cstat-no" title="statement not covered" >            address oldAddress = vestingContract;</span>
            vestingContract = _vestingContract;
            timeLocks[lockId].executed = true;
            
<span class="cstat-no" title="statement not covered" >            emit ContractAddressUpdated("vesting", oldAddress, _vestingContract);</span>
<span class="cstat-no" title="statement not covered" >            emit TimeLockExecuted(lockId);</span>
        }
    }
    
    /**
     * @dev 设置价格预言机地址（带时间锁）
     */
<span class="fstat-no" title="function not covered" >    function setPriceOracle(address _priceOracle) external onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        bytes32 lockId = keccak256(abi.encodePacked("setPriceOracle", _priceOracle, block.timestamp));</span>
        
<span class="cstat-no" title="statement not covered" >        if (timeLocks[lockId].unlockTime == 0) {</span>
            timeLocks[lockId] = TimeLock({
                unlockTime: block.timestamp + TIMELOCK_DELAY,
                executed: false
            });
<span class="cstat-no" title="statement not covered" >            emit TimeLockCreated(lockId, timeLocks[lockId].unlockTime);</span>
        } else {
<span class="cstat-no" title="statement not covered" >            require(block.timestamp &gt;= timeLocks[lockId].unlockTime, "TimeLock not expired")</span>;
<span class="cstat-no" title="statement not covered" >            require(!timeLocks[lockId].executed, "Already executed")</span>;
            
<span class="cstat-no" title="statement not covered" >            address oldAddress = priceOracle;</span>
            priceOracle = _priceOracle;
            timeLocks[lockId].executed = true;
            
<span class="cstat-no" title="statement not covered" >            emit ContractAddressUpdated("priceOracle", oldAddress, _priceOracle);</span>
<span class="cstat-no" title="statement not covered" >            emit TimeLockExecuted(lockId);</span>
        }
    }
    
    /**
     * @dev 紧急暂停（最长10分钟 - 测试版）
     */
<span class="fstat-no" title="function not covered" >    function emergencyPause() external onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        require(!paused(), "Already paused")</span>;
        pauseStartTime = block.timestamp;
<span class="cstat-no" title="statement not covered" >        _pause()</span>;
<span class="cstat-no" title="statement not covered" >        emit EmergencyPauseActivated(MAX_PAUSE_DURATION);</span>
    }
    
    /**
     * @dev 恢复运行
     */
<span class="fstat-no" title="function not covered" >    function unpause() external onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        require(paused(), "Not paused")</span>;
<span class="cstat-no" title="statement not covered" >        _unpause()</span>;
        pauseStartTime = 0;
    }
    
    /**
     * @dev 自动解除暂停（10分钟后任何人都可以调用）
     */
<span class="fstat-no" title="function not covered" >    function autoUnpause() external {</span>
<span class="cstat-no" title="statement not covered" >        require(paused(), "Not paused")</span>;
<span class="cstat-no" title="statement not covered" >        require(pauseStartTime &gt; 0, "Invalid pause state")</span>;
<span class="cstat-no" title="statement not covered" >        require(block.timestamp &gt;= pauseStartTime + MAX_PAUSE_DURATION, "Pause duration not exceeded")</span>;
        
<span class="cstat-no" title="statement not covered" >        _unpause()</span>;
        pauseStartTime = 0;
    }
    
    /**
     * @dev 放弃所有权（不可逆）
     */
<span class="fstat-no" title="function not covered" >    function renounceOwnership() public override onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        super.renounceOwnership()</span>;
    }
    
    /**
     * @dev 重写转账函数以支持暂停机制
     */
<span class="fstat-no" title="function not covered" >    function transfer(address to, uint256 amount) public override whenNotPaused returns (bool) {</span>
<span class="cstat-no" title="statement not covered" >        return super.transfer(to, amount);</span>
    }
    
    /**
     * @dev 重写授权转账函数以支持暂停机制
     */
<span class="fstat-no" title="function not covered" >    function transferFrom(address from, address to, uint256 amount) public override whenNotPaused returns (bool) {</span>
<span class="cstat-no" title="statement not covered" >        return super.transferFrom(from, to, amount);</span>
    }
    
    /**
     * @dev 获取合约状态
     */
<span class="fstat-no" title="function not covered" >    function getContractStatus() external view returns (</span>
        bool isPaused,
        uint256 pauseDuration,
        uint256 remainingPauseTime,
        address presale,
        address invitation,
        address vesting,
        address oracle
    ) {
<span class="cstat-no" title="statement not covered" >        uint256 remaining = 0;</span>
<span class="cstat-no" title="statement not covered" >        if (paused() &amp;&amp; pauseStartTime &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >            uint256 elapsed = block.timestamp - pauseStartTime;</span>
            remaining = elapsed &lt; MAX_PAUSE_DURATION ? MAX_PAUSE_DURATION - elapsed : 0;
        }
        
<span class="cstat-no" title="statement not covered" >        return (</span>
            paused(),
            pauseStartTime &gt; 0 ? block.timestamp - pauseStartTime : 0,
            remaining,
            presaleContract,
            invitationContract,
            vestingContract,
            priceOracle
        );
    }
}
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Aug 01 2025 12:47:30 GMT+0800 (中国标准时间)
</div>
</div>
<script src="../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../sorter.js"></script>
</body>
</html>
