<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for contracts/HAOXPresaleV2.sol</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../index.html">all files</a> / <a href="index.html">contracts/</a> HAOXPresaleV2.sol
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">76% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>38/50</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">46.3% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>25/54</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">75% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>9/12</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">74.07% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>60/81</span>
      </div>
    </div>
  </div>
  <div class='status-line medium'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
272
273
274
275
276
277
278
279
280
281
282
283
284
285
286
287
288
289
290
291
292
293
294
295
296
297
298
299
300
301
302
303
304
305
306
307
308
309
310
311
312
313
314
315
316</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">21×</span>
<span class="cline-any cline-yes">21×</span>
<span class="cline-any cline-yes">21×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">21×</span>
<span class="cline-any cline-yes">21×</span>
<span class="cline-any cline-yes">21×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">21×</span>
<span class="cline-any cline-yes">21×</span>
<span class="cline-any cline-yes">21×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">21×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">21×</span>
<span class="cline-any cline-yes">21×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">21×</span>
<span class="cline-any cline-yes">2079×</span>
<span class="cline-any cline-yes">2079×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">21×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">5×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">5×</span>
<span class="cline-any cline-yes">5×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-yes">5×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">21×</span>
<span class="cline-any cline-yes">21×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;
&nbsp;
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "./HAOXTokenV2.sol";
import "./HAOXInvitationV2.sol";
import "./HAOXPriceOracleV2.sol";
&nbsp;
/**
 * @title HAOXPresaleV2
 * @dev 重构的预售合约，支持跨阶段购买和精确的动态定价
 */
contract HAOXPresaleV2 is ReentrancyGuard, Pausable, Ownable {
&nbsp;
    HAOXTokenV2 public immutable haoxToken;
    HAOXInvitationV2 public invitationContract;
    HAOXPriceOracleV2 public priceOracle;
&nbsp;
    // 预售参数
    uint256 public constant TOTAL_PRESALE_TOKENS = 200_000_000 * 10**18; // 2亿HAOX
    uint256 public constant TARGET_BNB = 320 * 10**18; // 320 BNB
    uint256 public constant MIN_INVESTMENT = 0.1 * 10**18; // 0.1 BNB
    uint256 public constant MAX_INVESTMENT = 20 * 10**18; // 20 BNB
    uint256 public constant TOTAL_STAGES = 100;
    uint256 public constant TOKENS_PER_STAGE = TOTAL_PRESALE_TOKENS / TOTAL_STAGES; // 200万HAOX每阶段
&nbsp;
    // 动态定价参数
    uint256 public constant INITIAL_RATE = 1_912_125; // 初始汇率：1,912,125 HAOX/BNB
    uint256 public constant RATE_DECREASE_PERCENT = 98; // 每阶段汇率降低到98%
&nbsp;
    // 状态变量
    uint256 public currentStage;
    uint256 public totalBNBRaised;
    uint256 public totalTokensSold;
    uint256 public tokensRemainingInCurrentStage;
    bool public presaleActive;
    
    // 用户投资记录
    mapping(address =&gt; uint256) public investments;
    mapping(address =&gt; uint256) public tokensPurchased;
    mapping(address =&gt; bool) public whitelist;
    
    // 事件
    event TokensPurchased(
        address indexed buyer,
        uint256 bnbAmount,
        uint256 tokenAmount,
        uint256 avgRate,
        uint256 startStage,
        uint256 endStage
    );
    event StageCompleted(uint256 stage, uint256 totalTokensSold);
    event PresaleEnded(uint256 totalBNBRaised, uint256 totalTokensSold);
    
    constructor(
        address _haoxToken,
        address _invitationContract,
        address _priceOracle
    ) Ownable(msg.sender) {
        <span class="missing-if-branch" title="else path not taken" >E</span>require(_haoxToken != address(0), "Invalid token address");
        <span class="missing-if-branch" title="else path not taken" >E</span>require(_invitationContract != address(0), "Invalid invitation address");
        <span class="missing-if-branch" title="else path not taken" >E</span>require(_priceOracle != address(0), "Invalid oracle address");
&nbsp;
        haoxToken = HAOXTokenV2(_haoxToken);
        invitationContract = HAOXInvitationV2(_invitationContract);
        priceOracle = HAOXPriceOracleV2(_priceOracle);
&nbsp;
        currentStage = 0;
        tokensRemainingInCurrentStage = TOKENS_PER_STAGE;
        presaleActive = true;
    }
    
    // 预计算的汇率表（Gas优化）
    mapping(uint256 =&gt; uint256) private _stageRates;
    bool private _ratesInitialized;
&nbsp;
    /**
     * @dev 初始化汇率表（仅调用一次）
     */
    function initializeRates() external <span class="missing-if-branch" title="else path not taken" >E</span>onlyOwner {
        <span class="missing-if-branch" title="else path not taken" >E</span>require(!_ratesInitialized, "Rates already initialized");
&nbsp;
        uint256 rate = INITIAL_RATE;
        _stageRates[0] = rate;
&nbsp;
        for (uint256 i = 1; i &lt; TOTAL_STAGES; i++) {
            rate = (rate * RATE_DECREASE_PERCENT) / 100;
            _stageRates[i] = rate;
        }
&nbsp;
        _ratesInitialized = true;
    }
&nbsp;
    /**
     * @dev 获取指定阶段的汇率（优化版本）
     */
    function getStageRate(uint256 stage) public view returns (uint256) {
        <span class="missing-if-branch" title="else path not taken" >E</span>require(stage &lt; TOTAL_STAGES, "Invalid stage");
&nbsp;
        <span class="missing-if-branch" title="else path not taken" >E</span>if (_ratesInitialized) {
            return _stageRates[stage];
        } else {
            // 回退到实时计算
<span class="cstat-no" title="statement not covered" >            uint256 rate = INITIAL_RATE;</span>
<span class="cstat-no" title="statement not covered" >            for (uint256 i = 0; i &lt; stage; i++) {</span>
                rate = (rate * RATE_DECREASE_PERCENT) / 100;
            }
<span class="cstat-no" title="statement not covered" >            return rate;</span>
        }
    }
    
    /**
     * @dev 跨阶段购买的核心算法
     * @param bnbAmount 投入的BNB数量
     * @return tokenAmount 获得的代币数量
     * @return avgRate 平均汇率
     * @return endStage 结束阶段
     */
    function calculateCrossStageTokens(uint256 bnbAmount) 
        public 
        view 
        returns (uint256 tokenAmount, uint256 avgRate, uint256 endStage) 
    {
        uint256 remainingBNB = bnbAmount;
        uint256 totalTokens = 0;
        uint256 stage = currentStage;
        uint256 stageTokensRemaining = tokensRemainingInCurrentStage;
        
        while (remainingBNB &gt; 0 &amp;&amp; stage &lt; TOTAL_STAGES) {
            uint256 stageRate = getStageRate(stage);
            uint256 maxTokensFromBNB = remainingBNB * stageRate / 1e18;
            
            <span class="missing-if-branch" title="else path not taken" >E</span>if (maxTokensFromBNB &lt;= stageTokensRemaining) {
                // 当前阶段可以满足剩余需求
                totalTokens += maxTokensFromBNB;
                remainingBNB = 0;
            } else {
                // 需要进入下一阶段
<span class="cstat-no" title="statement not covered" >                uint256 bnbForThisStage = (stageTokensRemaining * 1e18) / stageRate;</span>
                totalTokens += stageTokensRemaining;
                remainingBNB -= bnbForThisStage;
                
                // 进入下一阶段
                stage++;
                stageTokensRemaining = TOKENS_PER_STAGE;
            }
        }
        
        <span class="missing-if-branch" title="else path not taken" >E</span>require(remainingBNB == 0, "Insufficient tokens available");
        
        // 计算平均汇率
        avgRate = (totalTokens * 1e18) / bnbAmount;
        endStage = stage;
        tokenAmount = totalTokens;
    }
    
    /**
     * @dev 购买代币（支持跨阶段和邀请）
     */
    function buyTokens(address inviter) external payable <span class="missing-if-branch" title="else path not taken" >E</span>nonReentrant <span class="missing-if-branch" title="else path not taken" >E</span>whenNotPaused {
        <span class="missing-if-branch" title="else path not taken" >E</span>require(presaleActive, "Presale not active");
        require(whitelist[msg.sender], "Not whitelisted");
        require(msg.value &gt;= MIN_INVESTMENT, "Below minimum investment");
        require(
            investments[msg.sender] + msg.value &lt;= MAX_INVESTMENT,
            "Exceeds maximum investment"
        );
        <span class="missing-if-branch" title="else path not taken" >E</span>require(
            totalBNBRaised + msg.value &lt;= TARGET_BNB,
            "Exceeds target BNB"
        );
        
        uint256 startStage = currentStage;
        
        // 计算跨阶段购买
        (uint256 tokenAmount, uint256 avgRate, uint256 endStage) = 
            calculateCrossStageTokens(msg.value);
        
        <span class="missing-if-branch" title="else path not taken" >E</span>require(tokenAmount &gt; 0, "Invalid token amount");
        <span class="missing-if-branch" title="else path not taken" >E</span>require(
            totalTokensSold + tokenAmount &lt;= TOTAL_PRESALE_TOKENS,
            "Exceeds total presale tokens"
        );
        
        // 更新状态
        _updateStageProgress(msg.value, tokenAmount, endStage);
        
        // 更新用户记录
        investments[msg.sender] += msg.value;
        tokensPurchased[msg.sender] += tokenAmount;
&nbsp;
        // 处理邀请奖励
        <span class="missing-if-branch" title="if path not taken" >I</span>if (inviter != address(0) &amp;&amp; inviter != msg.sender) {
<span class="cstat-no" title="statement not covered" >            try invitationContract.recordInvitation(inviter, msg.sender, msg.value) {</span>
                // 邀请记录成功
            } catch {
                // 邀请记录失败，不影响购买
            }
        }
&nbsp;
        emit TokensPurchased(
            msg.sender,
            msg.value,
            tokenAmount,
            avgRate,
            startStage,
            endStage
        );
&nbsp;
        // 检查是否达到目标
        <span class="missing-if-branch" title="if path not taken" >I</span>if (totalBNBRaised &gt;= TARGET_BNB || totalTokensSold &gt;= TOTAL_PRESALE_TOKENS) {
            presaleActive = false;
            // 通知邀请合约预售结束
<span class="cstat-no" title="statement not covered" >            try invitationContract.endPresale() {</span>
                // 预售结束通知成功
            } catch {
                // 通知失败，不影响预售结束
            }
<span class="cstat-no" title="statement not covered" >            emit PresaleEnded(totalBNBRaised, totalTokensSold);</span>
        }
    }
    
    /**
     * @dev 更新阶段进度（内部函数）
     */
    function _updateStageProgress(
        uint256 bnbAmount,
        uint256 tokenAmount,
        uint256 endStage
    ) internal {
        uint256 remainingBNB = bnbAmount;
        uint256 remainingTokens = tokenAmount;
        
        while (remainingTokens &gt; 0 &amp;&amp; currentStage &lt; TOTAL_STAGES) {
            <span class="missing-if-branch" title="else path not taken" >E</span>if (remainingTokens &lt;= tokensRemainingInCurrentStage) {
                // 当前阶段可以满足
                tokensRemainingInCurrentStage -= remainingTokens;
                remainingTokens = 0;
            } else {
                // 完成当前阶段，进入下一阶段
                remainingTokens -= tokensRemainingInCurrentStage;
                
<span class="cstat-no" title="statement not covered" >                emit StageCompleted(currentStage, totalTokensSold + (tokenAmount - remainingTokens));</span>
                
                currentStage++;
                tokensRemainingInCurrentStage = TOKENS_PER_STAGE;
            }
        }
        
        // 更新总量
        totalBNBRaised += bnbAmount;
        totalTokensSold += tokenAmount;
    }
    
    /**
     * @dev 获取当前汇率
     */
    function getCurrentRate() external view returns (uint256) {
        return getStageRate(currentStage);
    }
    
    /**
     * @dev 批量添加白名单
     */
    function addToWhitelist(address[] calldata users) external <span class="missing-if-branch" title="else path not taken" >E</span>onlyOwner {
        for (uint256 i = 0; i &lt; users.length; i++) {
            whitelist[users[i]] = true;
        }
    }
    
    /**
     * @dev 紧急暂停
     */
<span class="fstat-no" title="function not covered" >    function pause() external onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        _pause()</span>;
    }
    
    /**
     * @dev 恢复运行
     */
<span class="fstat-no" title="function not covered" >    function unpause() external onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        _unpause()</span>;
    }
    
    /**
     * @dev 提取BNB
     */
<span class="fstat-no" title="function not covered" >    function withdrawBNB() external onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        require(!presaleActive, "Presale still active")</span>;
<span class="cstat-no" title="statement not covered" >        payable(owner()).transfer(address(this).balance)</span>;
    }
    
    /**
     * @dev 获取预售状态
     */
    function getPresaleStatus() external view returns (
        uint256 _currentStage,
        uint256 _tokensRemainingInCurrentStage,
        uint256 _totalBNBRaised,
        uint256 _totalTokensSold,
        uint256 _currentRate,
        bool _isActive
    ) {
        return (
            currentStage,
            tokensRemainingInCurrentStage,
            totalBNBRaised,
            totalTokensSold,
            getStageRate(currentStage),
            presaleActive
        );
    }
}
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Sat Aug 02 2025 00:11:34 GMT+0800 (中国标准时间)
</div>
</div>
<script src="../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../sorter.js"></script>
</body>
</html>
