<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for contracts/HAOXVestingV2.sol</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../index.html">all files</a> / <a href="index.html">contracts/</a> HAOXVestingV2.sol
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>0/46</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>0/56</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>0/13</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>0/70</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
272
273
274
275
276
277
278
279
280
281
282
283
284
285
286
287
288
289
290
291
292
293
294
295
296
297
298
299
300
301
302
303
304
305
306</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;
&nbsp;
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "./HAOXTokenV2.sol";
import "./HAOXPriceOracleV2.sol";
&nbsp;
/**
 * @title HAOXVestingV2
 * @dev 代币解锁合约
 * 
 * 解锁机制：
 * - 总共31轮解锁（第1轮已完成）
 * - 第2-11轮：价格达到$0.00272并维持7天
 * - 第12-21轮：在前一轮基础上价格上涨50%并维持7天
 * - 第22-31轮：在前一轮基础上价格上涨20%并维持7天
 * - 每轮解锁1.5亿HAOX（项目方40%，社区60%）
 */
contract HAOXVestingV2 is ReentrancyGuard, Pausable, Ownable {
    
    HAOXTokenV2 public immutable haoxToken;
    HAOXPriceOracleV2 public priceOracle;
    
    // 解锁参数
    uint256 public constant TOTAL_ROUNDS = 31;
    uint256 public constant TOKENS_PER_ROUND = 150_000_000 * 10**18; // 1.5亿HAOX
    uint256 public constant PROJECT_SHARE = 40; // 40%
    uint256 public constant COMMUNITY_SHARE = 60; // 60%
    uint256 public constant PRICE_MAINTENANCE_PERIOD = 7 days; // 价格维持期
    
    // 初始价格触发条件
    uint256 public constant INITIAL_TRIGGER_PRICE = 272000; // $0.00272 (8位小数)
    uint256 public constant ROUND_12_21_INCREASE = 150; // 50% increase (150% of previous)
    uint256 public constant ROUND_22_31_INCREASE = 120; // 20% increase (120% of previous)
    
    // 解锁轮次结构
    struct UnlockRound {
        uint256 roundNumber;
        uint256 triggerPrice; // 触发价格（8位小数）
        uint256 priceReachedTime; // 价格首次达到时间
        bool priceConditionMet; // 价格条件是否满足
        bool unlocked; // 是否已解锁
        uint256 unlockTime; // 解锁时间
        uint256 projectTokens; // 项目方代币数量
        uint256 communityTokens; // 社区代币数量
    }
    
    // 状态变量
    uint256 public currentRound = 1; // 当前轮次（第1轮已完成）
    uint256 public totalUnlockedTokens;
    address public projectWallet;
    address public communityWallet;
    
    // 映射
    mapping(uint256 =&gt; UnlockRound) public unlockRounds;
    mapping(uint256 =&gt; uint256) public roundTriggerPrices;
    
    // 事件
    event RoundInitialized(uint256 round, uint256 triggerPrice);
    event PriceConditionMet(uint256 round, uint256 price, uint256 timestamp);
    event RoundUnlocked(
        uint256 round,
        uint256 projectTokens,
        uint256 communityTokens,
        uint256 timestamp
    );
    event WalletUpdated(string walletType, address oldWallet, address newWallet);
    
<span class="fstat-no" title="function not covered" >    constructor(</span>
        address _haoxToken,
        address _priceOracle,
        address _projectWallet,
        address _communityWallet
    ) Ownable(msg.sender) {
<span class="cstat-no" title="statement not covered" >        require(_haoxToken != address(0), "Invalid token address")</span>;
<span class="cstat-no" title="statement not covered" >        require(_priceOracle != address(0), "Invalid oracle address")</span>;
<span class="cstat-no" title="statement not covered" >        require(_projectWallet != address(0), "Invalid project wallet")</span>;
<span class="cstat-no" title="statement not covered" >        require(_communityWallet != address(0), "Invalid community wallet")</span>;
        
        haoxToken = HAOXTokenV2(_haoxToken);
        priceOracle = HAOXPriceOracleV2(_priceOracle);
        projectWallet = _projectWallet;
        communityWallet = _communityWallet;
        
        // 初始化所有解锁轮次
<span class="cstat-no" title="statement not covered" >        _initializeRounds()</span>;
    }
    
    /**
     * @dev 初始化所有解锁轮次
     */
<span class="fstat-no" title="function not covered" >    function _initializeRounds() internal {</span>
<span class="cstat-no" title="statement not covered" >        uint256 triggerPrice = INITIAL_TRIGGER_PRICE;</span>
        
        // 第2-11轮：固定触发价格$0.00272
<span class="cstat-no" title="statement not covered" >        for (uint256 i = 2; i &lt;= 11; i++) {</span>
<span class="cstat-no" title="statement not covered" >            _createRound(i, triggerPrice)</span>;
        }
        
        // 第12-21轮：每轮价格上涨50%
<span class="cstat-no" title="statement not covered" >        for (uint256 i = 12; i &lt;= 21; i++) {</span>
            triggerPrice = (triggerPrice * ROUND_12_21_INCREASE) / 100;
<span class="cstat-no" title="statement not covered" >            _createRound(i, triggerPrice)</span>;
        }
        
        // 第22-31轮：每轮价格上涨20%
<span class="cstat-no" title="statement not covered" >        for (uint256 i = 22; i &lt;= 31; i++) {</span>
            triggerPrice = (triggerPrice * ROUND_22_31_INCREASE) / 100;
<span class="cstat-no" title="statement not covered" >            _createRound(i, triggerPrice)</span>;
        }
    }
    
    /**
     * @dev 创建解锁轮次
     */
<span class="fstat-no" title="function not covered" >    function _createRound(uint256 roundNumber, uint256 triggerPrice) internal {</span>
<span class="cstat-no" title="statement not covered" >        uint256 projectTokens = (TOKENS_PER_ROUND * PROJECT_SHARE) / 100;</span>
<span class="cstat-no" title="statement not covered" >        uint256 communityTokens = (TOKENS_PER_ROUND * COMMUNITY_SHARE) / 100;</span>
        
        unlockRounds[roundNumber] = UnlockRound({
            roundNumber: roundNumber,
            triggerPrice: triggerPrice,
            priceReachedTime: 0,
            priceConditionMet: false,
            unlocked: false,
            unlockTime: 0,
            projectTokens: projectTokens,
            communityTokens: communityTokens
        });
        
        roundTriggerPrices[roundNumber] = triggerPrice;
        
<span class="cstat-no" title="statement not covered" >        emit RoundInitialized(roundNumber, triggerPrice);</span>
    }
    
    /**
     * @dev 检查并更新价格条件
     */
<span class="fstat-no" title="function not covered" >    function checkPriceConditions() external whenNotPaused {</span>
<span class="cstat-no" title="statement not covered" >        (uint256 currentPrice, uint256 confidence) = priceOracle.getPriceWithConfidence();</span>
<span class="cstat-no" title="statement not covered" >        require(confidence &gt;= 80, "Price confidence too low")</span>;
        
        // 检查当前轮次的价格条件
<span class="cstat-no" title="statement not covered" >        if (currentRound &lt;= TOTAL_ROUNDS) {</span>
<span class="cstat-no" title="statement not covered" >            UnlockRound storage round = unlockRounds[currentRound];</span>
            
<span class="cstat-no" title="statement not covered" >            if (!round.priceConditionMet &amp;&amp; currentPrice &gt;= round.triggerPrice) {</span>
                // 价格首次达到触发条件
                round.priceReachedTime = block.timestamp;
                round.priceConditionMet = true;
                
<span class="cstat-no" title="statement not covered" >                emit PriceConditionMet(currentRound, currentPrice, block.timestamp);</span>
            }
            
            // 检查是否可以解锁
<span class="cstat-no" title="statement not covered" >            if (round.priceConditionMet &amp;&amp; !round.unlocked) {</span>
<span class="cstat-no" title="statement not covered" >                if (block.timestamp &gt;= round.priceReachedTime + PRICE_MAINTENANCE_PERIOD) {</span>
                    // 验证价格是否持续维持
<span class="cstat-no" title="statement not covered" >                    if (currentPrice &gt;= round.triggerPrice) {</span>
<span class="cstat-no" title="statement not covered" >                        _unlockRound(currentRound)</span>;
                    } else {
                        // 价格跌破，重置条件
                        round.priceConditionMet = false;
                        round.priceReachedTime = 0;
                    }
                }
            }
        }
    }
    
    /**
     * @dev 解锁指定轮次
     */
<span class="fstat-no" title="function not covered" >    function _unlockRound(uint256 roundNumber) internal {</span>
<span class="cstat-no" title="statement not covered" >        UnlockRound storage round = unlockRounds[roundNumber];</span>
<span class="cstat-no" title="statement not covered" >        require(!round.unlocked, "Round already unlocked")</span>;
<span class="cstat-no" title="statement not covered" >        require(round.priceConditionMet, "Price condition not met")</span>;
        
        // 标记为已解锁
        round.unlocked = true;
        round.unlockTime = block.timestamp;
        
        // 更新统计
        totalUnlockedTokens += TOKENS_PER_ROUND;
        
        // 转账代币
<span class="cstat-no" title="statement not covered" >        require(</span>
            haoxToken.transfer(projectWallet, round.projectTokens),
            "Project transfer failed"
        );
<span class="cstat-no" title="statement not covered" >        require(</span>
            haoxToken.transfer(communityWallet, round.communityTokens),
            "Community transfer failed"
        );
        
<span class="cstat-no" title="statement not covered" >        emit RoundUnlocked(</span>
            roundNumber,
            round.projectTokens,
            round.communityTokens,
            block.timestamp
        );
        
        // 移动到下一轮
<span class="cstat-no" title="statement not covered" >        if (currentRound &lt; TOTAL_ROUNDS) {</span>
            currentRound++;
        }
    }
    
    /**
     * @dev 获取轮次信息
     */
<span class="fstat-no" title="function not covered" >    function getRoundInfo(uint256 roundNumber) external view returns (</span>
        uint256 triggerPrice,
        uint256 priceReachedTime,
        bool priceConditionMet,
        bool unlocked,
        uint256 unlockTime,
        uint256 projectTokens,
        uint256 communityTokens
    ) {
<span class="cstat-no" title="statement not covered" >        require(roundNumber &gt;= 2 &amp;&amp; roundNumber &lt;= TOTAL_ROUNDS, "Invalid round")</span>;
        
<span class="cstat-no" title="statement not covered" >        UnlockRound memory round = unlockRounds[roundNumber];</span>
<span class="cstat-no" title="statement not covered" >        return (</span>
            round.triggerPrice,
            round.priceReachedTime,
            round.priceConditionMet,
            round.unlocked,
            round.unlockTime,
            round.projectTokens,
            round.communityTokens
        );
    }
    
    /**
     * @dev 获取当前状态
     */
<span class="fstat-no" title="function not covered" >    function getCurrentStatus() external view returns (</span>
        uint256 _currentRound,
        uint256 _totalUnlockedTokens,
        uint256 _remainingTokens,
        uint256 _nextTriggerPrice,
        bool _nextRoundConditionMet
    ) {
        _currentRound = currentRound;
        _totalUnlockedTokens = totalUnlockedTokens;
        _remainingTokens = (TOTAL_ROUNDS - currentRound) * TOKENS_PER_ROUND;
        
<span class="cstat-no" title="statement not covered" >        if (currentRound &lt;= TOTAL_ROUNDS) {</span>
            _nextTriggerPrice = unlockRounds[currentRound].triggerPrice;
            _nextRoundConditionMet = unlockRounds[currentRound].priceConditionMet;
        }
    }
    
    /**
     * @dev 更新项目钱包地址
     */
<span class="fstat-no" title="function not covered" >    function setProjectWallet(address _projectWallet) external onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        require(_projectWallet != address(0), "Invalid address")</span>;
<span class="cstat-no" title="statement not covered" >        address oldWallet = projectWallet;</span>
        projectWallet = _projectWallet;
<span class="cstat-no" title="statement not covered" >        emit WalletUpdated("project", oldWallet, _projectWallet);</span>
    }
    
    /**
     * @dev 更新社区钱包地址
     */
<span class="fstat-no" title="function not covered" >    function setCommunityWallet(address _communityWallet) external onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        require(_communityWallet != address(0), "Invalid address")</span>;
<span class="cstat-no" title="statement not covered" >        address oldWallet = communityWallet;</span>
        communityWallet = _communityWallet;
<span class="cstat-no" title="statement not covered" >        emit WalletUpdated("community", oldWallet, _communityWallet);</span>
    }
    
    /**
     * @dev 更新价格预言机地址
     */
<span class="fstat-no" title="function not covered" >    function setPriceOracle(address _priceOracle) external onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        require(_priceOracle != address(0), "Invalid address")</span>;
        priceOracle = HAOXPriceOracleV2(_priceOracle);
    }
    
    /**
     * @dev 紧急暂停
     */
<span class="fstat-no" title="function not covered" >    function pause() external onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        _pause()</span>;
    }
    
    /**
     * @dev 恢复运行
     */
<span class="fstat-no" title="function not covered" >    function unpause() external onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        _unpause()</span>;
    }
    
    /**
     * @dev 紧急提取代币（仅限管理员）
     */
<span class="fstat-no" title="function not covered" >    function emergencyWithdraw(uint256 amount) external onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        require(haoxToken.transfer(owner(), amount), "Transfer failed")</span>;
    }
}
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Aug 01 2025 12:47:30 GMT+0800 (中国标准时间)
</div>
</div>
<script src="../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../sorter.js"></script>
</body>
</html>
