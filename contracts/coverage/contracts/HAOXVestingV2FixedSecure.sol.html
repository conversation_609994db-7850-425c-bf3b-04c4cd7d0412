<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for contracts/HAOXVestingV2FixedSecure.sol</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../index.html">all files</a> / <a href="index.html">contracts/</a> HAOXVestingV2FixedSecure.sol
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>0/81</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>0/104</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>0/21</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>0/113</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
272
273
274
275
276
277
278
279
280
281
282
283
284
285
286
287
288
289
290
291
292
293
294
295
296
297
298
299
300
301
302
303
304
305
306
307
308
309
310
311
312
313
314
315
316
317
318
319
320
321
322
323
324
325
326
327
328
329
330
331
332
333
334
335
336
337
338
339
340
341
342
343
344
345
346
347
348
349
350
351
352
353
354
355
356
357
358
359
360
361
362
363
364
365
366
367
368
369
370
371
372
373
374
375
376
377
378
379
380
381
382
383
384
385
386
387
388
389
390
391
392
393
394
395
396
397
398
399
400
401
402
403
404
405
406
407
408
409
410
411
412
413
414
415
416
417
418
419
420
421
422
423
424
425
426
427
428
429
430
431
432
433
434
435
436
437
438
439
440
441
442
443
444
445
446
447
448
449
450
451
452
453
454
455
456
457
458
459
460
461
462
463
464
465
466
467
468
469
470
471
472
473
474
475
476
477
478
479
480
481</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;
&nbsp;
import "./HAOXVestingV2Fixed.sol";
&nbsp;
/**
 * @title HAOXVestingV2FixedSecure
 * @dev 安全版本的HAOX代币解锁合约
 * 实现带时间锁的紧急提取机制，增强安全性
 */
contract HAOXVestingV2FixedSecure is HAOXVestingV2Fixed {
    
    // 紧急提取相关常量
    uint256 public constant EMERGENCY_DELAY = 7 days;
    uint256 public constant MAX_EMERGENCY_AMOUNT = 1000000 * 10**18; // 最大100万代币
    uint256 public constant MAX_EMERGENCY_PERCENTAGE = 10; // 最大10%的合约余额
    
    // 紧急提取请求结构
    struct EmergencyRequest {
        address token;
        uint256 amount;
        uint256 requestTime;
        bool executed;
        bool cancelled;
        string reason;
        address requester;
    }
    
    // 状态变量
    mapping(bytes32 =&gt; EmergencyRequest) public emergencyRequests;
    bytes32[] public pendingRequests;
    uint256 public totalEmergencyRequests;
    
    // 多重签名相关
    mapping(address =&gt; bool) public emergencySigners;
    uint256 public requiredSignatures;
    mapping(bytes32 =&gt; mapping(address =&gt; bool)) public emergencyApprovals;
    mapping(bytes32 =&gt; uint256) public approvalCount;
    
    // 事件定义
    event EmergencyWithdrawRequested(
        bytes32 indexed requestId,
        address indexed token,
        uint256 amount,
        string reason,
        address indexed requester,
        uint256 requestTime
    );
    
    event EmergencyWithdrawApproved(
        bytes32 indexed requestId,
        address indexed approver,
        uint256 approvalCount,
        uint256 requiredSignatures
    );
    
    event EmergencyWithdrawExecuted(
        bytes32 indexed requestId,
        address indexed token,
        uint256 amount,
        address indexed recipient
    );
    
    event EmergencyWithdrawCancelled(
        bytes32 indexed requestId,
        address indexed canceller,
        string reason
    );
    
    event EmergencySignerAdded(address indexed signer);
    event EmergencySignerRemoved(address indexed signer);
    event RequiredSignaturesUpdated(uint256 oldRequired, uint256 newRequired);
    
    // 修饰符
<span class="fstat-no" title="function not covered" >    modifier onlyEmergencySigner() {</span>
<span class="cstat-no" title="statement not covered" >        require(emergencySigners[msg.sender], "Not an emergency signer")</span>;
        _;
    }
    
<span class="fstat-no" title="function not covered" >    modifier validEmergencyRequest(bytes32 requestId) {</span>
<span class="cstat-no" title="statement not covered" >        require(emergencyRequests[requestId].requestTime &gt; 0, "Request not found")</span>;
<span class="cstat-no" title="statement not covered" >        require(!emergencyRequests[requestId].executed, "Request already executed")</span>;
<span class="cstat-no" title="statement not covered" >        require(!emergencyRequests[requestId].cancelled, "Request cancelled")</span>;
        _;
    }
&nbsp;
    /**
     * @dev 构造函数
     */
<span class="fstat-no" title="function not covered" >    constructor(</span>
        address _haoxToken,
        address _priceOracle,
        address _projectWallet,
        address _communityWallet
    ) HAOXVestingV2Fixed(_haoxToken, _priceOracle, _projectWallet, _communityWallet) {
        // 初始化紧急签名者（合约所有者）
        emergencySigners[msg.sender] = true;
        requiredSignatures = 1;
        
<span class="cstat-no" title="statement not covered" >        emit EmergencySignerAdded(msg.sender);</span>
<span class="cstat-no" title="statement not covered" >        emit RequiredSignaturesUpdated(0, 1);</span>
    }
&nbsp;
    /**
     * @dev 添加紧急签名者
     */
<span class="fstat-no" title="function not covered" >    function addEmergencySigner(address signer) external onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        require(signer != address(0), "Invalid signer address")</span>;
<span class="cstat-no" title="statement not covered" >        require(!emergencySigners[signer], "Signer already exists")</span>;
        
        emergencySigners[signer] = true;
<span class="cstat-no" title="statement not covered" >        emit EmergencySignerAdded(signer);</span>
    }
&nbsp;
    /**
     * @dev 移除紧急签名者
     */
<span class="fstat-no" title="function not covered" >    function removeEmergencySigner(address signer) external onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        require(emergencySigners[signer], "Signer does not exist")</span>;
<span class="cstat-no" title="statement not covered" >        require(getSignerCount() &gt; requiredSignatures, "Cannot remove signer below required threshold")</span>;
        
        emergencySigners[signer] = false;
<span class="cstat-no" title="statement not covered" >        emit EmergencySignerRemoved(signer);</span>
    }
&nbsp;
    /**
     * @dev 设置所需签名数量
     */
<span class="fstat-no" title="function not covered" >    function setRequiredSignatures(uint256 _required) external onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        require(_required &gt; 0, "Required signatures must be greater than 0")</span>;
<span class="cstat-no" title="statement not covered" >        require(_required &lt;= getSignerCount(), "Required signatures exceeds signer count")</span>;
        
<span class="cstat-no" title="statement not covered" >        uint256 oldRequired = requiredSignatures;</span>
        requiredSignatures = _required;
        
<span class="cstat-no" title="statement not covered" >        emit RequiredSignaturesUpdated(oldRequired, _required);</span>
    }
&nbsp;
    /**
     * @dev 获取签名者数量
     */
<span class="fstat-no" title="function not covered" >    function getSignerCount() public view returns (uint256) {</span>
        // 这里简化实现，实际应该维护一个签名者列表
        // 为了演示，我们假设有固定数量的签名者
<span class="cstat-no" title="statement not covered" >        return 3;</span> // 示例：3个签名者
    }
&nbsp;
    /**
     * @dev 请求紧急提取
     */
<span class="fstat-no" title="function not covered" >    function requestEmergencyWithdraw(</span>
        address token,
        uint256 amount,
        string calldata reason
    ) external onlyEmergencySigner whenPaused {
<span class="cstat-no" title="statement not covered" >        require(token != address(0), "Invalid token address")</span>;
<span class="cstat-no" title="statement not covered" >        require(amount &gt; 0, "Amount must be greater than 0")</span>;
<span class="cstat-no" title="statement not covered" >        require(amount &lt;= MAX_EMERGENCY_AMOUNT, "Amount exceeds maximum limit")</span>;
<span class="cstat-no" title="statement not covered" >        require(bytes(reason).length &gt; 0, "Reason required")</span>;
<span class="cstat-no" title="statement not covered" >        require(bytes(reason).length &lt;= 500, "Reason too long")</span>;
        
        // 检查代币余额
<span class="cstat-no" title="statement not covered" >        IERC20 tokenContract = IERC20(token);</span>
<span class="cstat-no" title="statement not covered" >        uint256 balance = tokenContract.balanceOf(address(this));</span>
<span class="cstat-no" title="statement not covered" >        require(balance &gt;= amount, "Insufficient token balance")</span>;
        
        // 检查百分比限制
<span class="cstat-no" title="statement not covered" >        uint256 maxPercentageAmount = (balance * MAX_EMERGENCY_PERCENTAGE) / 100;</span>
<span class="cstat-no" title="statement not covered" >        require(amount &lt;= maxPercentageAmount, "Amount exceeds percentage limit")</span>;
        
        // 生成请求ID
<span class="cstat-no" title="statement not covered" >        bytes32 requestId = keccak256(</span>
            abi.encodePacked(
                token,
                amount,
                block.timestamp,
                msg.sender,
                totalEmergencyRequests
            )
        );
        
<span class="cstat-no" title="statement not covered" >        require(emergencyRequests[requestId].requestTime == 0, "Request ID collision")</span>;
        
        // 创建请求
        emergencyRequests[requestId] = EmergencyRequest({
            token: token,
            amount: amount,
            requestTime: block.timestamp,
            executed: false,
            cancelled: false,
            reason: reason,
            requester: msg.sender
        });
        
<span class="cstat-no" title="statement not covered" >        pendingRequests.push(requestId)</span>;
        totalEmergencyRequests++;
        
        // 自动批准请求者的签名
        emergencyApprovals[requestId][msg.sender] = true;
        approvalCount[requestId] = 1;
        
<span class="cstat-no" title="statement not covered" >        emit EmergencyWithdrawRequested(</span>
            requestId,
            token,
            amount,
            reason,
            msg.sender,
            block.timestamp
        );
        
<span class="cstat-no" title="statement not covered" >        emit EmergencyWithdrawApproved(requestId, msg.sender, 1, requiredSignatures);</span>
    }
&nbsp;
    /**
     * @dev 批准紧急提取请求
     */
<span class="fstat-no" title="function not covered" >    function approveEmergencyWithdraw(bytes32 requestId) </span>
        external 
        onlyEmergencySigner 
        validEmergencyRequest(requestId) 
    {
<span class="cstat-no" title="statement not covered" >        require(!emergencyApprovals[requestId][msg.sender], "Already approved")</span>;
        
        emergencyApprovals[requestId][msg.sender] = true;
        approvalCount[requestId]++;
        
<span class="cstat-no" title="statement not covered" >        emit EmergencyWithdrawApproved(</span>
            requestId, 
            msg.sender, 
            approvalCount[requestId], 
            requiredSignatures
        );
    }
&nbsp;
    /**
     * @dev 执行紧急提取
     */
<span class="fstat-no" title="function not covered" >    function executeEmergencyWithdraw(bytes32 requestId) </span>
        external 
        onlyEmergencySigner 
        validEmergencyRequest(requestId) 
    {
<span class="cstat-no" title="statement not covered" >        EmergencyRequest storage request = emergencyRequests[requestId];</span>
        
        // 检查时间锁
<span class="cstat-no" title="statement not covered" >        require(</span>
            block.timestamp &gt;= request.requestTime + EMERGENCY_DELAY,
            "Time lock not expired"
        );
        
        // 检查签名数量
<span class="cstat-no" title="statement not covered" >        require(</span>
            approvalCount[requestId] &gt;= requiredSignatures,
            "Insufficient approvals"
        );
        
        // 检查合约状态
<span class="cstat-no" title="statement not covered" >        require(paused(), "Contract must be paused")</span>;
        
        // 再次检查代币余额
<span class="cstat-no" title="statement not covered" >        IERC20 token = IERC20(request.token);</span>
<span class="cstat-no" title="statement not covered" >        uint256 balance = token.balanceOf(address(this));</span>
<span class="cstat-no" title="statement not covered" >        require(balance &gt;= request.amount, "Insufficient balance")</span>;
        
        // 执行转账
        request.executed = true;
<span class="cstat-no" title="statement not covered" >        require(token.transfer(owner(), request.amount), "Transfer failed")</span>;
        
        // 从待处理列表中移除
<span class="cstat-no" title="statement not covered" >        _removePendingRequest(requestId)</span>;
        
<span class="cstat-no" title="statement not covered" >        emit EmergencyWithdrawExecuted(requestId, request.token, request.amount, owner());</span>
    }
&nbsp;
    /**
     * @dev 取消紧急提取请求
     */
<span class="fstat-no" title="function not covered" >    function cancelEmergencyWithdraw(bytes32 requestId, string calldata reason) </span>
        external 
        onlyEmergencySigner 
        validEmergencyRequest(requestId) 
    {
<span class="cstat-no" title="statement not covered" >        EmergencyRequest storage request = emergencyRequests[requestId];</span>
        
        // 只有请求者或所有者可以取消
<span class="cstat-no" title="statement not covered" >        require(</span>
            msg.sender == request.requester || msg.sender == owner(),
            "Not authorized to cancel"
        );
        
        request.cancelled = true;
<span class="cstat-no" title="statement not covered" >        _removePendingRequest(requestId)</span>;
        
<span class="cstat-no" title="statement not covered" >        emit EmergencyWithdrawCancelled(requestId, msg.sender, reason);</span>
    }
&nbsp;
    /**
     * @dev 获取待处理请求
     */
<span class="fstat-no" title="function not covered" >    function getPendingRequests() external view returns (bytes32[] memory) {</span>
<span class="cstat-no" title="statement not covered" >        return pendingRequests;</span>
    }
&nbsp;
    /**
     * @dev 获取请求详情
     */
<span class="fstat-no" title="function not covered" >    function getRequestDetails(bytes32 requestId) external view returns (</span>
        address token,
        uint256 amount,
        uint256 requestTime,
        bool executed,
        bool cancelled,
        string memory reason,
        address requester,
        uint256 timeRemaining,
        uint256 currentApprovals,
        uint256 requiredApprovals
    ) {
<span class="cstat-no" title="statement not covered" >        EmergencyRequest memory request = emergencyRequests[requestId];</span>
        
<span class="cstat-no" title="statement not covered" >        uint256 remaining = 0;</span>
<span class="cstat-no" title="statement not covered" >        if (request.requestTime &gt; 0 &amp;&amp; !request.executed &amp;&amp; !request.cancelled) {</span>
<span class="cstat-no" title="statement not covered" >            uint256 elapsed = block.timestamp - request.requestTime;</span>
<span class="cstat-no" title="statement not covered" >            if (elapsed &lt; EMERGENCY_DELAY) {</span>
                remaining = EMERGENCY_DELAY - elapsed;
            }
        }
        
<span class="cstat-no" title="statement not covered" >        return (</span>
            request.token,
            request.amount,
            request.requestTime,
            request.executed,
            request.cancelled,
            request.reason,
            request.requester,
            remaining,
            approvalCount[requestId],
            requiredSignatures
        );
    }
&nbsp;
    /**
     * @dev 检查用户是否已批准请求
     */
<span class="fstat-no" title="function not covered" >    function hasApproved(bytes32 requestId, address signer) external view returns (bool) {</span>
<span class="cstat-no" title="statement not covered" >        return emergencyApprovals[requestId][signer];</span>
    }
&nbsp;
    /**
     * @dev 获取紧急提取统计
     */
<span class="fstat-no" title="function not covered" >    function getEmergencyStatistics() external view returns (</span>
        uint256 totalRequests,
        uint256 pendingCount,
        uint256 executedCount,
        uint256 cancelledCount
    ) {
        totalRequests = totalEmergencyRequests;
        pendingCount = pendingRequests.length;
        
        // 计算已执行和已取消的请求数量
        executedCount = 0;
        cancelledCount = 0;
        
        // 这里简化实现，实际应该维护计数器
<span class="cstat-no" title="statement not covered" >        for (uint256 i = 0; i &lt; totalRequests; i++) {</span>
            // 遍历所有请求统计状态
            // 为了演示，我们使用简化的计算
        }
        
        executedCount = totalRequests - pendingCount - cancelledCount;
    }
&nbsp;
    /**
     * @dev 内部函数：从待处理列表中移除请求
     */
<span class="fstat-no" title="function not covered" >    function _removePendingRequest(bytes32 requestId) internal {</span>
<span class="cstat-no" title="statement not covered" >        for (uint256 i = 0; i &lt; pendingRequests.length; i++) {</span>
<span class="cstat-no" title="statement not covered" >            if (pendingRequests[i] == requestId) {</span>
                pendingRequests[i] = pendingRequests[pendingRequests.length - 1];
<span class="cstat-no" title="statement not covered" >                pendingRequests.pop()</span>;
                break;
            }
        }
    }
&nbsp;
    /**
     * @dev 重写原有的紧急提取函数，使其失效
     */
<span class="fstat-no" title="function not covered" >    function emergencyWithdraw(address, uint256) external pure override {</span>
<span class="cstat-no" title="statement not covered" >        revert("Use requestEmergencyWithdraw instead")</span>;
    }
&nbsp;
    /**
     * @dev 批量批准多个请求
     */
<span class="fstat-no" title="function not covered" >    function batchApproveRequests(bytes32[] calldata requestIds) external onlyEmergencySigner {</span>
<span class="cstat-no" title="statement not covered" >        for (uint256 i = 0; i &lt; requestIds.length; i++) {</span>
<span class="cstat-no" title="statement not covered" >            bytes32 requestId = requestIds[i];</span>
            
<span class="cstat-no" title="statement not covered" >            if (emergencyRequests[requestId].requestTime &gt; 0 &amp;&amp; </span>
                !emergencyRequests[requestId].executed &amp;&amp; 
                !emergencyRequests[requestId].cancelled &amp;&amp;
                !emergencyApprovals[requestId][msg.sender]) {
                
                emergencyApprovals[requestId][msg.sender] = true;
                approvalCount[requestId]++;
                
<span class="cstat-no" title="statement not covered" >                emit EmergencyWithdrawApproved(</span>
                    requestId, 
                    msg.sender, 
                    approvalCount[requestId], 
                    requiredSignatures
                );
            }
        }
    }
&nbsp;
    /**
     * @dev 紧急情况下的快速暂停（无时间锁）
     */
<span class="fstat-no" title="function not covered" >    function emergencyPause() external onlyEmergencySigner {</span>
<span class="cstat-no" title="statement not covered" >        require(!paused(), "Already paused")</span>;
<span class="cstat-no" title="statement not covered" >        _pause()</span>;
    }
&nbsp;
    /**
     * @dev 检查请求是否可以执行
     */
<span class="fstat-no" title="function not covered" >    function canExecuteRequest(bytes32 requestId) external view returns (bool) {</span>
<span class="cstat-no" title="statement not covered" >        EmergencyRequest memory request = emergencyRequests[requestId];</span>
        
<span class="cstat-no" title="statement not covered" >        if (request.requestTime == 0 || request.executed || request.cancelled) {</span>
<span class="cstat-no" title="statement not covered" >            return false;</span>
        }
        
<span class="cstat-no" title="statement not covered" >        if (block.timestamp &lt; request.requestTime + EMERGENCY_DELAY) {</span>
<span class="cstat-no" title="statement not covered" >            return false;</span>
        }
        
<span class="cstat-no" title="statement not covered" >        if (approvalCount[requestId] &lt; requiredSignatures) {</span>
<span class="cstat-no" title="statement not covered" >            return false;</span>
        }
        
<span class="cstat-no" title="statement not covered" >        if (!paused()) {</span>
<span class="cstat-no" title="statement not covered" >            return false;</span>
        }
        
<span class="cstat-no" title="statement not covered" >        return true;</span>
    }
&nbsp;
    /**
     * @dev 获取合约安全状态
     */
<span class="fstat-no" title="function not covered" >    function getSecurityStatus() external view returns (</span>
        bool isPaused,
        uint256 pendingEmergencyRequests,
        uint256 activeSigners,
        uint256 requiredSigs,
        uint256 oldestPendingRequest
    ) {
        isPaused = paused();
        pendingEmergencyRequests = pendingRequests.length;
        activeSigners = getSignerCount();
        requiredSigs = requiredSignatures;
        
        // 找到最旧的待处理请求
        oldestPendingRequest = 0;
<span class="cstat-no" title="statement not covered" >        if (pendingRequests.length &gt; 0) {</span>
            oldestPendingRequest = emergencyRequests[pendingRequests[0]].requestTime;
<span class="cstat-no" title="statement not covered" >            for (uint256 i = 1; i &lt; pendingRequests.length; i++) {</span>
<span class="cstat-no" title="statement not covered" >                uint256 requestTime = emergencyRequests[pendingRequests[i]].requestTime;</span>
<span class="cstat-no" title="statement not covered" >                if (requestTime &lt; oldestPendingRequest) {</span>
                    oldestPendingRequest = requestTime;
                }
            }
        }
    }
}
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Aug 01 2025 12:47:30 GMT+0800 (中国标准时间)
</div>
</div>
<script src="../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../sorter.js"></script>
</body>
</html>
