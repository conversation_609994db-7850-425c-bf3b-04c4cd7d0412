<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for contracts/HAOXInvitationV2.sol</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../index.html">all files</a> / <a href="index.html">contracts/</a> HAOXInvitationV2.sol
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">50.91% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>28/55</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">45.31% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>29/64</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">92.31% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>12/13</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">40.51% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>32/79</span>
      </div>
    </div>
  </div>
  <div class='status-line medium'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
272
273
274
275
276
277
278
279
280
281
282
283
284
285
286
287
288
289
290
291
292
293
294
295
296
297
298
299
300
301
302
303
304
305
306
307
308
309
310
311
312
313
314
315
316
317
318
319
320
321
322
323
324
325
326
327
328
329
330</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">76×</span>
<span class="cline-any cline-yes">76×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">18×</span>
<span class="cline-any cline-yes">17×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-yes">7×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">5×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;
&nbsp;
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "./HAOXTokenV2.sol";
&nbsp;
/**
 * @title HAOXInvitationV2
 * @dev 重构的邀请奖励合约
 * 
 * 奖励规则：
 * - 基础奖励：每成功邀请1人参与预售(≥0.1BNB) = 1,000 HAOX
 * - 里程碑奖励：5人 = 10,000 HAOX，10人 = 50,000 HAOX
 * - 排行榜奖励：预售结束后根据排名发放
 * - 总奖励池：300,000,000 HAOX
 */
contract HAOXInvitationV2 is ReentrancyGuard, Pausable, Ownable {
    
    HAOXTokenV2 public immutable haoxToken;
    
    // 奖励参数
    uint256 public constant BASE_REWARD = 1_000 * 10**18; // 1,000 HAOX per invitation
    uint256 public constant MILESTONE_5_REWARD = 10_000 * 10**18; // 10,000 HAOX at 5 invitations
    uint256 public constant MILESTONE_10_REWARD = 50_000 * 10**18; // 50,000 HAOX at 10 invitations
    uint256 public constant TOTAL_REWARD_POOL = 300_000_000 * 10**18; // 300M HAOX total
    uint256 public constant MIN_PURCHASE_AMOUNT = 0.1 * 10**18; // 0.1 BNB minimum
    
    // 排行榜奖励池
    uint256 public constant LEADERBOARD_POOL = 2_700_000 * 10**18; // 2.7M HAOX
    uint256[20] public LEADERBOARD_REWARDS = [
        1_000_000 * 10**18, // 1st place
        500_000 * 10**18,   // 2nd place
        200_000 * 10**18,   // 3rd place
        100_000 * 10**18,   // 4th-10th place (7 positions)
        100_000 * 10**18,
        100_000 * 10**18,
        100_000 * 10**18,
        100_000 * 10**18,
        100_000 * 10**18,
        100_000 * 10**18,
        50_000 * 10**18,    // 11th-20th place (10 positions)
        50_000 * 10**18,
        50_000 * 10**18,
        50_000 * 10**18,
        50_000 * 10**18,
        50_000 * 10**18,
        50_000 * 10**18,
        50_000 * 10**18,
        50_000 * 10**18,
        50_000 * 10**18
    ];
    
    // 状态变量
    uint256 public totalRewardsDistributed;
    bool public presaleEnded;
    bool public leaderboardFinalized;
    
    // 用户数据结构
    struct UserStats {
        uint256 successfulInvitations;
        uint256 totalRewards;
        uint256 claimableRewards;
        bool milestone5Claimed;
        bool milestone10Claimed;
        uint256 leaderboardReward;
        bool leaderboardClaimed;
    }
    
    // 邀请记录结构
    struct InvitationRecord {
        address inviter;
        address invitee;
        uint256 purchaseAmount;
        uint256 timestamp;
        bool rewardClaimed;
    }
    
    // 映射
    mapping(address =&gt; UserStats) public userStats;
    mapping(address =&gt; address) public inviterOf; // invitee =&gt; inviter
    mapping(address =&gt; address[]) public inviteesOf; // inviter =&gt; invitees[]
    mapping(bytes32 =&gt; InvitationRecord) public invitationRecords;
    
    // 排行榜
    address[] public leaderboard;
    mapping(address =&gt; uint256) public leaderboardPosition; // 1-based, 0 means not in top 20
    
    // 事件
    event InvitationRecorded(
        address indexed inviter,
        address indexed invitee,
        uint256 purchaseAmount,
        uint256 baseReward
    );
    event MilestoneReached(address indexed user, uint256 milestone, uint256 reward);
    event RewardClaimed(address indexed user, uint256 amount);
    event LeaderboardFinalized(address[] topUsers);
    event LeaderboardRewardClaimed(address indexed user, uint256 position, uint256 reward);
    
    constructor(address _haoxToken) Ownable(msg.sender) {
        <span class="missing-if-branch" title="else path not taken" >E</span>require(_haoxToken != address(0), "Invalid token address");
        haoxToken = HAOXTokenV2(_haoxToken);
    }
    
    /**
     * @dev 记录邀请关系和购买（由预售合约调用）
     */
<span class="fstat-no" title="function not covered" >    function recordInvitation(</span>
        address inviter,
        address invitee,
        uint256 purchaseAmount
    ) external {
<span class="cstat-no" title="statement not covered" >        require(msg.sender == address(haoxToken.presaleContract()), "Only presale contract")</span>;
<span class="cstat-no" title="statement not covered" >        require(!presaleEnded, "Presale ended")</span>;
<span class="cstat-no" title="statement not covered" >        require(purchaseAmount &gt;= MIN_PURCHASE_AMOUNT, "Purchase below minimum")</span>;
<span class="cstat-no" title="statement not covered" >        require(inviter != invitee, "Cannot invite self")</span>;
<span class="cstat-no" title="statement not covered" >        require(inviterOf[invitee] == address(0), "Already has inviter")</span>;
        
        // 记录邀请关系
        inviterOf[invitee] = inviter;
<span class="cstat-no" title="statement not covered" >        inviteesOf[inviter].push(invitee)</span>;
        
        // 创建邀请记录
<span class="cstat-no" title="statement not covered" >        bytes32 recordId = keccak256(abi.encodePacked(inviter, invitee, block.timestamp));</span>
        invitationRecords[recordId] = InvitationRecord({
            inviter: inviter,
            invitee: invitee,
            purchaseAmount: purchaseAmount,
            timestamp: block.timestamp,
            rewardClaimed: false
        });
        
        // 更新邀请者统计
<span class="cstat-no" title="statement not covered" >        UserStats storage stats = userStats[inviter];</span>
        stats.successfulInvitations++;
        
        // 计算基础奖励
<span class="cstat-no" title="statement not covered" >        uint256 baseReward = BASE_REWARD;</span>
        stats.claimableRewards += baseReward;
        
        // 检查里程碑奖励
<span class="cstat-no" title="statement not covered" >        if (stats.successfulInvitations == 5 &amp;&amp; !stats.milestone5Claimed) {</span>
            stats.claimableRewards += MILESTONE_5_REWARD;
            stats.milestone5Claimed = true;
<span class="cstat-no" title="statement not covered" >            emit MilestoneReached(inviter, 5, MILESTONE_5_REWARD);</span>
        }
        
<span class="cstat-no" title="statement not covered" >        if (stats.successfulInvitations == 10 &amp;&amp; !stats.milestone10Claimed) {</span>
            stats.claimableRewards += MILESTONE_10_REWARD;
            stats.milestone10Claimed = true;
<span class="cstat-no" title="statement not covered" >            emit MilestoneReached(inviter, 10, MILESTONE_10_REWARD);</span>
        }
        
<span class="cstat-no" title="statement not covered" >        emit InvitationRecorded(inviter, invitee, purchaseAmount, baseReward);</span>
    }
    
    /**
     * @dev 用户主动领取奖励
     */
    function claimRewards() external <span class="missing-if-branch" title="else path not taken" >E</span>nonReentrant whenNotPaused {
        UserStats storage stats = userStats[msg.sender];
        uint256 claimableAmount = stats.claimableRewards;
        
        <span class="missing-if-branch" title="if path not taken" >I</span>require(claimableAmount &gt; 0, "No rewards to claim");
<span class="cstat-no" title="statement not covered" >        require(presaleEnded, "Presale not ended yet")</span>;
<span class="cstat-no" title="statement not covered" >        require(</span>
            totalRewardsDistributed + claimableAmount &lt;= TOTAL_REWARD_POOL,
            "Exceeds reward pool"
        );
        
        // 更新状态
        stats.claimableRewards = 0;
        stats.totalRewards += claimableAmount;
        totalRewardsDistributed += claimableAmount;
        
        // 转账代币
<span class="cstat-no" title="statement not covered" >        require(haoxToken.transfer(msg.sender, claimableAmount), "Transfer failed")</span>;
        
<span class="cstat-no" title="statement not covered" >        emit RewardClaimed(msg.sender, claimableAmount);</span>
    }
    
    /**
     * @dev 领取排行榜奖励
     */
    function claimLeaderboardReward() external <span class="missing-if-branch" title="else path not taken" >E</span>nonReentrant <span class="missing-if-branch" title="else path not taken" >E</span>whenNotPaused {
        <span class="missing-if-branch" title="else path not taken" >E</span>require(presaleEnded, "Presale not ended");
        require(leaderboardFinalized, "Leaderboard not finalized");
        
        UserStats storage stats = userStats[msg.sender];
        <span class="missing-if-branch" title="if path not taken" >I</span>require(stats.leaderboardReward &gt; 0, "No leaderboard reward");
<span class="cstat-no" title="statement not covered" >        require(!stats.leaderboardClaimed, "Already claimed")</span>;
        
<span class="cstat-no" title="statement not covered" >        uint256 reward = stats.leaderboardReward;</span>
        stats.leaderboardClaimed = true;
        stats.totalRewards += reward;
        totalRewardsDistributed += reward;
        
<span class="cstat-no" title="statement not covered" >        require(haoxToken.transfer(msg.sender, reward), "Transfer failed")</span>;
        
<span class="cstat-no" title="statement not covered" >        uint256 position = leaderboardPosition[msg.sender];</span>
<span class="cstat-no" title="statement not covered" >        emit LeaderboardRewardClaimed(msg.sender, position, reward);</span>
    }
&nbsp;
    /**
     * @dev 预售结束，管理员调用
     */
    function endPresale() external onlyOwner {
        require(!presaleEnded, "Already ended");
        presaleEnded = true;
    }
&nbsp;
    /**
     * @dev 生成并确定排行榜（管理员调用）
     */
    function finalizeLeaderboard() external onlyOwner {
        require(presaleEnded, "Presale not ended");
        require(!leaderboardFinalized, "Already finalized");
&nbsp;
        // 获取所有有邀请记录的用户
        address[] memory allUsers = _getAllInviters();
&nbsp;
        // 按邀请数量排序（冒泡排序，适合小数据集）
        for (uint256 i = 0; i &lt; allUsers.length; i++) {
<span class="cstat-no" title="statement not covered" >            for (uint256 j = i + 1; j &lt; allUsers.length; j++) {</span>
<span class="cstat-no" title="statement not covered" >                if (userStats[allUsers[i]].successfulInvitations &lt; userStats[allUsers[j]].successfulInvitations) {</span>
<span class="cstat-no" title="statement not covered" >                    address temp = allUsers[i];</span>
                    allUsers[i] = allUsers[j];
                    allUsers[j] = temp;
                }
            }
        }
&nbsp;
        // 设置前20名的排行榜奖励
        uint256 topCount = allUsers.length &gt; 20 ? <span class="missing-if-branch" title="if path not taken" >I</span>20 : allUsers.length;
        leaderboard = new address[](topCount);
&nbsp;
        for (uint256 i = 0; i &lt; topCount; i++) {
<span class="cstat-no" title="statement not covered" >            address user = allUsers[i];</span>
            leaderboard[i] = user;
            leaderboardPosition[user] = i + 1; // 1-based position
            userStats[user].leaderboardReward = LEADERBOARD_REWARDS[i];
        }
&nbsp;
        leaderboardFinalized = true;
        emit LeaderboardFinalized(leaderboard);
    }
&nbsp;
    /**
     * @dev 获取所有邀请者地址（内部函数）
     */
    function _getAllInviters() internal view returns (address[] memory) {
        // 这里简化实现，实际应该维护一个邀请者列表
        // 为了演示，返回一个固定大小的数组
        address[] memory inviters = new address[](100); // 假设最多100个邀请者
        uint256 count = 0;
&nbsp;
        // 注意：这里需要根据实际情况实现
        // 可以通过事件日志或维护邀请者列表来获取
&nbsp;
        // 创建一个临时数组返回实际数量
        address[] memory result = new address[](count);
        for (uint256 i = 0; i &lt; count; i++) {
            result[i] = inviters[i];
        }
&nbsp;
        return result;
    }
&nbsp;
    /**
     * @dev 获取用户邀请统计
     */
    function getUserStats(address user) external view returns (
        uint256 successfulInvitations,
        uint256 totalRewards,
        uint256 claimableRewards,
        bool milestone5Claimed,
        bool milestone10Claimed,
        uint256 leaderboardReward,
        bool leaderboardClaimed
    ) {
        UserStats memory stats = userStats[user];
        return (
            stats.successfulInvitations,
            stats.totalRewards,
            stats.claimableRewards,
            stats.milestone5Claimed,
            stats.milestone10Claimed,
            stats.leaderboardReward,
            stats.leaderboardClaimed
        );
    }
&nbsp;
    /**
     * @dev 获取用户邀请的所有人
     */
    function getUserInvitees(address user) external view returns (address[] memory) {
        return inviteesOf[user];
    }
&nbsp;
    /**
     * @dev 获取排行榜
     */
    function getLeaderboard() external view returns (address[] memory) {
        return leaderboard;
    }
&nbsp;
    /**
     * @dev 紧急暂停
     */
    function pause() external onlyOwner {
        _pause();
    }
&nbsp;
    /**
     * @dev 恢复运行
     */
    function unpause() external <span class="missing-if-branch" title="else path not taken" >E</span>onlyOwner {
        _unpause();
    }
&nbsp;
    /**
     * @dev 紧急提取代币（仅限管理员）
     */
    function emergencyWithdraw(uint256 amount) external onlyOwner {
        require(haoxToken.transfer(owner(), amount), "Transfer failed");
    }
}
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Sat Aug 02 2025 00:11:34 GMT+0800 (中国标准时间)
</div>
</div>
<script src="../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../sorter.js"></script>
</body>
</html>
