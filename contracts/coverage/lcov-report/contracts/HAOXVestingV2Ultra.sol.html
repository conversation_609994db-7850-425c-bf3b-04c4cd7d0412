<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for contracts/HAOXVestingV2Ultra.sol</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../index.html">all files</a> / <a href="index.html">contracts/</a> HAOXVestingV2Ultra.sol
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">75% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>36/48</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">56% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>28/50</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">57.14% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>8/14</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">80.33% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>49/61</span>
      </div>
    </div>
  </div>
  <div class='status-line medium'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">38×</span>
<span class="cline-any cline-yes">38×</span>
<span class="cline-any cline-yes">38×</span>
<span class="cline-any cline-yes">38×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">38×</span>
<span class="cline-any cline-yes">38×</span>
<span class="cline-any cline-yes">38×</span>
<span class="cline-any cline-yes">38×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">38×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">38×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">38×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">38×</span>
<span class="cline-any cline-yes">38×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">31×</span>
<span class="cline-any cline-yes">31×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">31×</span>
<span class="cline-any cline-yes">31×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">31×</span>
<span class="cline-any cline-yes">31×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">31×</span>
<span class="cline-any cline-yes">12×</span>
<span class="cline-any cline-yes">12×</span>
<span class="cline-any cline-yes">12×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">31×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">5×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">5×</span>
<span class="cline-any cline-yes">5×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">5×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">5×</span>
<span class="cline-any cline-yes">5×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">31×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">31×</span>
<span class="cline-any cline-yes">31×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">7×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;
&nbsp;
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";
&nbsp;
/**
 * @title HAOXVestingV2Ultra
 * @dev 超精简版HAOX代币解锁合约 - 极致成本优化版本
 * 仅保留最核心的安全功能，最大化降低部署成本
 */
contract HAOXVestingV2Ultra is Ownable, ReentrancyGuard, Pausable {
    
    // 基础常量
    uint256 public constant TOTAL_ROUNDS = 31;
    uint256 public constant PRICE_MAINTAIN_DURATION = 7 days;
    uint256 public constant EMERGENCY_DELAY = 7 days;
    uint256 public constant MAX_EMERGENCY_AMOUNT = 1000000 * 10**18;
    
    // 核心状态变量
    IERC20 public immutable haoxToken;
    address public immutable priceOracle;
    address public immutable projectWallet;
    address public immutable communityWallet;
    
    uint256 public currentRound = 1;
    
    // 轮次信息结构（超紧凑存储）
    struct Round {
        uint128 triggerPrice;      // 触发价格 (8位小数)
        uint64 priceReachedTime;   // 价格达到时间
        bool priceConditionMet;    // 价格条件是否满足
        bool unlocked;             // 是否已解锁
    }
    
    // 紧急提取请求结构（超精简版）
    struct EmergencyRequest {
        uint128 amount;
        uint64 requestTime;
        bool executed;
    }
    
    // 状态映射（移除历史记录）
    mapping(uint256 =&gt; Round) public rounds;
    mapping(bytes32 =&gt; EmergencyRequest) public emergencyRequests;
    mapping(address =&gt; bool) public emergencySigners;
    
    uint256 public requiredSignatures = 1;
    
    // 事件定义（超精简版）
    event PriceConditionMet(uint256 indexed roundNumber, uint256 price);
    event RoundUnlocked(uint256 indexed roundNumber, uint256 timestamp);
    event EmergencyWithdrawRequested(bytes32 indexed requestId, uint256 amount);
    event EmergencyWithdrawExecuted(bytes32 indexed requestId, uint256 amount);
    
    constructor(
        address _haoxToken,
        address _priceOracle,
        address _projectWallet,
        address _communityWallet
    ) Ownable(msg.sender) {
        <span class="missing-if-branch" title="else path not taken" >E</span>require(_haoxToken != address(0), "Invalid token address");
        <span class="missing-if-branch" title="else path not taken" >E</span>require(_priceOracle != address(0), "Invalid oracle address");
        <span class="missing-if-branch" title="else path not taken" >E</span>require(_projectWallet != address(0), "Invalid project wallet");
        <span class="missing-if-branch" title="else path not taken" >E</span>require(_communityWallet != address(0), "Invalid community wallet");
        
        haoxToken = IERC20(_haoxToken);
        priceOracle = _priceOracle;
        projectWallet = _projectWallet;
        communityWallet = _communityWallet;
        
        emergencySigners[msg.sender] = true;
        
        // 初始化31轮价格阶梯
        _initializeRounds();
    }
    
    /**
     * @dev 初始化31轮价格阶梯
     */
    function _initializeRounds() internal {
        uint256[31] memory prices = [
            uint256(0.01e8), 0.02e8, 0.03e8, 0.04e8, 0.05e8, 0.06e8, 0.07e8, 0.08e8, 0.09e8, 0.10e8,
            0.11e8, 0.12e8, 0.13e8, 0.14e8, 0.15e8, 0.16e8, 0.17e8, 0.18e8, 0.19e8, 0.20e8,
            0.25e8, 0.30e8, 0.35e8, 0.40e8, 0.45e8, 0.50e8, 0.60e8, 0.70e8, 0.80e8, 0.90e8, 1.00e8
        ];
        
        for (uint256 i = 0; i &lt; TOTAL_ROUNDS; i++) {
            rounds[i + 1] = Round({
                triggerPrice: uint128(prices[i]),
                priceReachedTime: 0,
                priceConditionMet: false,
                unlocked: false
            });
        }
    }
    
    /**
     * @dev 检查价格条件（超精简版）
     */
    function checkPriceCondition() external whenNotPaused {
        uint256 roundNumber = currentRound;
        <span class="missing-if-branch" title="if path not taken" >I</span>if (roundNumber &gt; TOTAL_ROUNDS) <span class="cstat-no" title="statement not covered" >return;</span>
        
        Round storage round = rounds[roundNumber];
        <span class="missing-if-branch" title="if path not taken" >I</span>if (round.unlocked) <span class="cstat-no" title="statement not covered" >return;</span>
        
        uint256 currentPrice = _getCurrentPrice();
        bool conditionMet = currentPrice &gt;= round.triggerPrice;
        
        if (conditionMet &amp;&amp; !round.priceConditionMet) {
            round.priceConditionMet = true;
            round.priceReachedTime = uint64(block.timestamp);
            emit PriceConditionMet(roundNumber, currentPrice);
        } else if (!conditionMet &amp;&amp; round.priceConditionMet) {
            round.priceConditionMet = false;
            round.priceReachedTime = 0;
        }
        
        // 检查是否可以解锁
        if (round.priceConditionMet &amp;&amp; 
            block.timestamp &gt;= round.priceReachedTime + PRICE_MAINTAIN_DURATION) {
            _unlockRound(roundNumber);
        }
    }
    
    /**
     * @dev 解锁轮次
     */
    function _unlockRound(uint256 roundNumber) internal {
        Round storage round = rounds[roundNumber];
        round.unlocked = true;
        
        emit RoundUnlocked(roundNumber, block.timestamp);
        
        <span class="missing-if-branch" title="else path not taken" >E</span>if (roundNumber &lt; TOTAL_ROUNDS) {
            currentRound = roundNumber + 1;
        }
    }
    
    /**
     * @dev 获取当前价格
     */
    function _getCurrentPrice() internal view returns (uint256) {
        (bool success, bytes memory data) = priceOracle.staticcall(
            abi.encodeWithSignature("getLatestPrice()")
        );
        <span class="missing-if-branch" title="else path not taken" >E</span>require(success &amp;&amp; data.length &gt;= 32, "Price oracle call failed");
        return abi.decode(data, (uint256));
    }
    
    /**
     * @dev 紧急提取请求
     */
    function requestEmergencyWithdraw(
        address token,
        uint256 amount
    ) external onlyOwner returns (bytes32) {
        require(amount &lt;= MAX_EMERGENCY_AMOUNT, "Amount exceeds maximum");
        
        bytes32 requestId = keccak256(abi.encodePacked(
            token, amount, block.timestamp, msg.sender
        ));
        
        emergencyRequests[requestId] = EmergencyRequest({
            amount: uint128(amount),
            requestTime: uint64(block.timestamp),
            executed: false
        });
        
        emit EmergencyWithdrawRequested(requestId, amount);
        return requestId;
    }
    
    /**
     * @dev 执行紧急提取
     */
    function executeEmergencyWithdraw(
        bytes32 requestId,
        address token
    ) external <span class="missing-if-branch" title="else path not taken" >E</span>nonReentrant {
        <span class="missing-if-branch" title="else path not taken" >E</span>require(emergencySigners[msg.sender], "Not authorized signer");
        
        EmergencyRequest storage request = emergencyRequests[requestId];
        require(!request.executed, "Already executed");
        require(
            block.timestamp &gt;= request.requestTime + EMERGENCY_DELAY,
            "Time lock not expired"
        );
        
        request.executed = true;
        
        <span class="missing-if-branch" title="else path not taken" >E</span>if (token == address(haoxToken)) {
            haoxToken.transfer(projectWallet, request.amount);
        } else {
<span class="cstat-no" title="statement not covered" >            IERC20(token).transfer(projectWallet, request.amount)</span>;
        }
        
        emit EmergencyWithdrawExecuted(requestId, request.amount);
    }
    
    /**
     * @dev 管理紧急签名者
     */
<span class="fstat-no" title="function not covered" >    function setEmergencySigner(address signer, bool status) external onlyOwner {</span>
        emergencySigners[signer] = status;
    }
    
    /**
     * @dev 设置所需签名数量
     */
<span class="fstat-no" title="function not covered" >    function setRequiredSignatures(uint256 _required) external onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        require(_required &gt; 0, "Invalid signature count")</span>;
        requiredSignatures = _required;
    }
    
    /**
     * @dev 暂停/恢复合约
     */
    function pause() external <span class="missing-if-branch" title="else path not taken" >E</span>onlyOwner {
        _pause();
    }
    
<span class="fstat-no" title="function not covered" >    function unpause() external onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        _unpause()</span>;
    }
    
    /**
     * @dev 获取轮次信息
     */
<span class="fstat-no" title="function not covered" >    function getRoundInfo(uint256 roundNumber) external view returns (</span>
        uint256 triggerPrice,
        bool priceConditionMet,
        bool unlocked,
        uint256 priceReachedTime
    ) {
<span class="cstat-no" title="statement not covered" >        Round memory round = rounds[roundNumber];</span>
<span class="cstat-no" title="statement not covered" >        return (</span>
            round.triggerPrice,
            round.priceConditionMet,
            round.unlocked,
            round.priceReachedTime
        );
    }
    
    /**
     * @dev 获取解锁进度
     */
<span class="fstat-no" title="function not covered" >    function getUnlockProgress() external view returns (</span>
        uint256 totalRounds,
        uint256 currentRoundNumber,
        uint256 unlockedRounds
    ) {
<span class="cstat-no" title="statement not covered" >        uint256 unlocked = 0;</span>
<span class="cstat-no" title="statement not covered" >        for (uint256 i = 1; i &lt;= TOTAL_ROUNDS; i++) {</span>
<span class="cstat-no" title="statement not covered" >            if (rounds[i].unlocked) unlocked++;</span>
        }
        
<span class="cstat-no" title="statement not covered" >        return (TOTAL_ROUNDS, currentRound, unlocked);</span>
    }
    
    /**
     * @dev 获取当前价格（外部调用）
     */
<span class="fstat-no" title="function not covered" >    function getCurrentPrice() external view returns (uint256) {</span>
<span class="cstat-no" title="statement not covered" >        return _getCurrentPrice();</span>
    }
}
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Sat Aug 02 2025 00:11:34 GMT+0800 (中国标准时间)
</div>
</div>
<script src="../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../sorter.js"></script>
</body>
</html>
