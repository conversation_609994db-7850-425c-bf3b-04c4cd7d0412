<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for contracts/HAOXVestingV2Fixed.sol</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../index.html">all files</a> / <a href="index.html">contracts/</a> HAOXVestingV2Fixed.sol
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>0/68</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>0/74</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>0/16</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>0/100</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
272
273
274
275
276
277
278
279
280
281
282
283
284
285
286
287
288
289
290
291
292
293
294
295
296
297
298
299
300
301
302
303
304
305
306
307
308
309
310
311
312
313
314
315
316
317
318
319
320
321
322
323
324
325
326
327
328
329
330
331
332
333
334
335
336
337
338
339
340
341
342
343
344
345
346
347
348
349
350
351
352
353
354
355
356
357
358
359
360
361
362
363
364
365
366
367
368
369
370
371
372
373
374
375
376
377
378
379
380
381
382
383
384
385
386
387
388
389
390
391
392
393
394
395
396
397
398
399
400
401
402
403
404
405
406
407
408</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;
&nbsp;
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "./HAOXTokenV2.sol";
import "./HAOXPriceOracleV2.sol";
&nbsp;
/**
 * @title HAOXVestingV2Fixed
 * @dev 修正版HAOX代币解锁合约
 * 
 * 解锁机制：
 * - 第1轮：5亿HAOX（已完成，预售结束）
 * - 第2-31轮：每轮1.5亿HAOX，共45亿HAOX
 * - 价格触发：第2-11轮(+100%)，第12-21轮(+50%)，第22-31轮(+20%)
 * - 维持期：价格需连续7天保持在触发价格以上
 * - 分配：项目钱包40%，社区钱包60%
 */
contract HAOXVestingV2Fixed is ReentrancyGuard, Pausable, Ownable {
    
    HAOXTokenV2 public immutable haoxToken;
    HAOXPriceOracleV2 public priceOracle;
    
    // 解锁参数
    uint256 public constant TOTAL_ROUNDS = 31;
    uint256 public constant TOKENS_PER_ROUND = 150_000_000 * 10**18; // 1.5亿HAOX
    uint256 public constant FIRST_ROUND_TOKENS = 500_000_000 * 10**18; // 5亿HAOX（第1轮）
    uint256 public constant PROJECT_SHARE = 40; // 40%
    uint256 public constant COMMUNITY_SHARE = 60; // 60%
    uint256 public constant PRICE_MAINTENANCE_PERIOD = 7 days; // 价格维持期
    
    // 基准价格和增长率（以8位小数存储，$0.003041 = 304100）
    uint256 public constant BASE_PRICE = 304100; // $0.003041 (8位小数)
    uint256 public constant ROUND_2_11_INCREASE = 200; // 100% increase (200% of previous)
    uint256 public constant ROUND_12_21_INCREASE = 150; // 50% increase (150% of previous)
    uint256 public constant ROUND_22_31_INCREASE = 120; // 20% increase (120% of previous)
    
    // 解锁轮次结构
    struct UnlockRound {
        uint256 roundNumber;
        uint256 triggerPrice; // 8位小数
        uint256 priceReachedTime; // 价格首次达到时间
        bool priceConditionMet; // 价格条件是否达成
        bool unlocked; // 是否已解锁
        uint256 unlockTime; // 解锁时间
        uint256 projectTokens; // 项目钱包代币数量
        uint256 communityTokens; // 社区钱包代币数量
    }
    
    mapping(uint256 =&gt; UnlockRound) public unlockRounds;
    uint256 public currentRound = 1; // 第1轮已完成
    
    address public projectWallet;
    address public communityWallet;
    
    // 价格检查历史记录
    struct PriceCheck {
        uint256 timestamp;
        uint256 price;
        uint256 targetPrice;
        bool conditionMet;
    }
    
    mapping(uint256 =&gt; PriceCheck[]) public priceCheckHistory;
    
    // 事件
    event RoundUnlocked(
        uint256 indexed roundNumber,
        uint256 triggerPrice,
        uint256 projectTokens,
        uint256 communityTokens,
        uint256 timestamp
    );
    event PriceConditionMet(
        uint256 indexed roundNumber, 
        uint256 price, 
        uint256 timestamp
    );
    event PriceConditionReset(
        uint256 indexed roundNumber, 
        uint256 price, 
        uint256 timestamp
    );
    event WalletUpdated(string walletType, address oldWallet, address newWallet);
    event PriceChecked(
        uint256 indexed roundNumber,
        uint256 currentPrice,
        uint256 targetPrice,
        bool conditionMet,
        uint256 timestamp
    );
    
<span class="fstat-no" title="function not covered" >    modifier validRound(uint256 roundNumber) {</span>
<span class="cstat-no" title="statement not covered" >        require(roundNumber &gt;= 1 &amp;&amp; roundNumber &lt;= TOTAL_ROUNDS, "Invalid round number")</span>;
        _;
    }
    
<span class="fstat-no" title="function not covered" >    constructor(</span>
        address _haoxToken,
        address _priceOracle,
        address _projectWallet,
        address _communityWallet
    ) Ownable(msg.sender) {
<span class="cstat-no" title="statement not covered" >        require(_haoxToken != address(0), "Invalid token address")</span>;
<span class="cstat-no" title="statement not covered" >        require(_priceOracle != address(0), "Invalid oracle address")</span>;
<span class="cstat-no" title="statement not covered" >        require(_projectWallet != address(0), "Invalid project wallet")</span>;
<span class="cstat-no" title="statement not covered" >        require(_communityWallet != address(0), "Invalid community wallet")</span>;
        
        haoxToken = HAOXTokenV2(_haoxToken);
        priceOracle = HAOXPriceOracleV2(_priceOracle);
        projectWallet = _projectWallet;
        communityWallet = _communityWallet;
        
        // 初始化所有解锁轮次
<span class="cstat-no" title="statement not covered" >        _initializeRounds()</span>;
    }
    
    /**
     * @dev 初始化所有解锁轮次
     */
<span class="fstat-no" title="function not covered" >    function _initializeRounds() internal {</span>
<span class="cstat-no" title="statement not covered" >        uint256 triggerPrice = BASE_PRICE;</span>
        
        // 第1轮（已完成，预售结束）
        unlockRounds[1] = UnlockRound({
            roundNumber: 1,
            triggerPrice: triggerPrice,
            priceReachedTime: block.timestamp,
            priceConditionMet: true,
            unlocked: true,
            unlockTime: block.timestamp,
            projectTokens: (FIRST_ROUND_TOKENS * PROJECT_SHARE) / 100,
            communityTokens: (FIRST_ROUND_TOKENS * COMMUNITY_SHARE) / 100
        });
        
        // 第2-11轮：每轮价格上涨100%
<span class="cstat-no" title="statement not covered" >        for (uint256 i = 2; i &lt;= 11; i++) {</span>
            triggerPrice = (triggerPrice * ROUND_2_11_INCREASE) / 100;
<span class="cstat-no" title="statement not covered" >            _createRound(i, triggerPrice)</span>;
        }
        
        // 第12-21轮：每轮价格上涨50%
<span class="cstat-no" title="statement not covered" >        for (uint256 i = 12; i &lt;= 21; i++) {</span>
            triggerPrice = (triggerPrice * ROUND_12_21_INCREASE) / 100;
<span class="cstat-no" title="statement not covered" >            _createRound(i, triggerPrice)</span>;
        }
        
        // 第22-31轮：每轮价格上涨20%
<span class="cstat-no" title="statement not covered" >        for (uint256 i = 22; i &lt;= 31; i++) {</span>
            triggerPrice = (triggerPrice * ROUND_22_31_INCREASE) / 100;
<span class="cstat-no" title="statement not covered" >            _createRound(i, triggerPrice)</span>;
        }
    }
    
    /**
     * @dev 创建解锁轮次
     */
<span class="fstat-no" title="function not covered" >    function _createRound(uint256 roundNumber, uint256 triggerPrice) internal {</span>
<span class="cstat-no" title="statement not covered" >        uint256 projectTokens = (TOKENS_PER_ROUND * PROJECT_SHARE) / 100;</span>
<span class="cstat-no" title="statement not covered" >        uint256 communityTokens = (TOKENS_PER_ROUND * COMMUNITY_SHARE) / 100;</span>
        
        unlockRounds[roundNumber] = UnlockRound({
            roundNumber: roundNumber,
            triggerPrice: triggerPrice,
            priceReachedTime: 0,
            priceConditionMet: false,
            unlocked: false,
            unlockTime: 0,
            projectTokens: projectTokens,
            communityTokens: communityTokens
        });
    }
    
    /**
     * @dev 检查价格条件并更新状态
     */
<span class="fstat-no" title="function not covered" >    function checkPriceCondition() public virtual whenNotPaused {</span>
<span class="cstat-no" title="statement not covered" >        uint256 nextRound = currentRound + 1;</span>
<span class="cstat-no" title="statement not covered" >        if (nextRound &gt; TOTAL_ROUNDS) <span class="cstat-no" title="statement not covered" >return;</span></span>
        
<span class="cstat-no" title="statement not covered" >        UnlockRound storage round = unlockRounds[nextRound];</span>
<span class="cstat-no" title="statement not covered" >        if (round.unlocked) <span class="cstat-no" title="statement not covered" >return;</span></span>
        
<span class="cstat-no" title="statement not covered" >        uint256 currentPrice = priceOracle.getLatestPrice();</span>
<span class="cstat-no" title="statement not covered" >        bool conditionMet = currentPrice &gt;= round.triggerPrice;</span>
        
        // 记录价格检查历史
<span class="cstat-no" title="statement not covered" >        priceCheckHistory[nextRound].push(PriceCheck({</span>
            timestamp: block.timestamp,
            price: currentPrice,
            targetPrice: round.triggerPrice,
            conditionMet: conditionMet
        }));
        
<span class="cstat-no" title="statement not covered" >        emit PriceChecked(nextRound, currentPrice, round.triggerPrice, conditionMet, block.timestamp);</span>
        
<span class="cstat-no" title="statement not covered" >        if (conditionMet) {</span>
<span class="cstat-no" title="statement not covered" >            if (!round.priceConditionMet) {</span>
                // 首次达到价格条件
                round.priceConditionMet = true;
                round.priceReachedTime = block.timestamp;
<span class="cstat-no" title="statement not covered" >                emit PriceConditionMet(nextRound, currentPrice, block.timestamp);</span>
            } else {
                // 检查是否维持了足够长时间
<span class="cstat-no" title="statement not covered" >                if (block.timestamp &gt;= round.priceReachedTime + PRICE_MAINTENANCE_PERIOD) {</span>
<span class="cstat-no" title="statement not covered" >                    _unlockRound(nextRound)</span>;
                }
            }
        } else {
            // 价格跌破，重置条件
<span class="cstat-no" title="statement not covered" >            if (round.priceConditionMet) {</span>
                round.priceConditionMet = false;
                round.priceReachedTime = 0;
<span class="cstat-no" title="statement not covered" >                emit PriceConditionReset(nextRound, currentPrice, block.timestamp);</span>
            }
        }
    }
    
    /**
     * @dev 解锁指定轮次
     */
<span class="fstat-no" title="function not covered" >    function _unlockRound(uint256 roundNumber) internal {</span>
<span class="cstat-no" title="statement not covered" >        UnlockRound storage round = unlockRounds[roundNumber];</span>
<span class="cstat-no" title="statement not covered" >        require(!round.unlocked, "Round already unlocked")</span>;
<span class="cstat-no" title="statement not covered" >        require(round.priceConditionMet, "Price condition not met")</span>;
<span class="cstat-no" title="statement not covered" >        require(</span>
            block.timestamp &gt;= round.priceReachedTime + PRICE_MAINTENANCE_PERIOD,
            "Price maintenance period not completed"
        );
        
        round.unlocked = true;
        round.unlockTime = block.timestamp;
        currentRound = roundNumber;
        
        // 转移代币
<span class="cstat-no" title="statement not covered" >        require(</span>
            haoxToken.transfer(projectWallet, round.projectTokens),
            "Project transfer failed"
        );
<span class="cstat-no" title="statement not covered" >        require(</span>
            haoxToken.transfer(communityWallet, round.communityTokens),
            "Community transfer failed"
        );
        
<span class="cstat-no" title="statement not covered" >        emit RoundUnlocked(</span>
            roundNumber,
            round.triggerPrice,
            round.projectTokens,
            round.communityTokens,
            block.timestamp
        );
    }
    
    /**
     * @dev 获取轮次信息
     */
<span class="fstat-no" title="function not covered" >    function getRoundInfo(uint256 roundNumber) external view validRound(roundNumber) returns (</span>
        uint256 triggerPrice,
        bool priceConditionMet,
        bool unlocked,
        uint256 priceReachedTime,
        uint256 unlockTime,
        uint256 projectTokens,
        uint256 communityTokens
    ) {
<span class="cstat-no" title="statement not covered" >        UnlockRound memory round = unlockRounds[roundNumber];</span>
<span class="cstat-no" title="statement not covered" >        return (</span>
            round.triggerPrice,
            round.priceConditionMet,
            round.unlocked,
            round.priceReachedTime,
            round.unlockTime,
            round.projectTokens,
            round.communityTokens
        );
    }
    
    /**
     * @dev 获取当前价格和下一轮解锁进度
     */
<span class="fstat-no" title="function not covered" >    function getUnlockProgress() external view returns (</span>
        uint256 currentPrice,
        uint256 nextRoundNumber,
        uint256 nextRoundTriggerPrice,
        bool nextRoundPriceConditionMet,
        uint256 timeRemaining,
        uint256 priceReachedTime
    ) {
        currentPrice = priceOracle.getLatestPrice();
        nextRoundNumber = currentRound + 1;
        
<span class="cstat-no" title="statement not covered" >        if (nextRoundNumber &lt;= TOTAL_ROUNDS) {</span>
<span class="cstat-no" title="statement not covered" >            UnlockRound memory nextRound = unlockRounds[nextRoundNumber];</span>
            nextRoundTriggerPrice = nextRound.triggerPrice;
            nextRoundPriceConditionMet = nextRound.priceConditionMet;
            priceReachedTime = nextRound.priceReachedTime;
            
<span class="cstat-no" title="statement not covered" >            if (nextRound.priceConditionMet &amp;&amp; nextRound.priceReachedTime &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >                uint256 elapsed = block.timestamp - nextRound.priceReachedTime;</span>
                timeRemaining = elapsed &gt;= PRICE_MAINTENANCE_PERIOD ? 0 : 
                               PRICE_MAINTENANCE_PERIOD - elapsed;
            }
        }
    }
    
    /**
     * @dev 获取价格检查历史
     */
<span class="fstat-no" title="function not covered" >    function getPriceCheckHistory(uint256 roundNumber, uint256 limit)</span>
        external view virtual validRound(roundNumber) returns (PriceCheck[] memory) {
<span class="cstat-no" title="statement not covered" >        PriceCheck[] memory history = priceCheckHistory[roundNumber];</span>
<span class="cstat-no" title="statement not covered" >        uint256 length = history.length;</span>
        
<span class="cstat-no" title="statement not covered" >        if (limit == 0 || limit &gt; length) {</span>
            limit = length;
        }
        
<span class="cstat-no" title="statement not covered" >        PriceCheck[] memory result = new PriceCheck[](limit);</span>
<span class="cstat-no" title="statement not covered" >        for (uint256 i = 0; i &lt; limit; i++) {</span>
            result[i] = history[length - limit + i];
        }
        
<span class="cstat-no" title="statement not covered" >        return result;</span>
    }
    
    /**
     * @dev 获取解锁统计信息
     */
<span class="fstat-no" title="function not covered" >    function getUnlockStatistics() external view returns (</span>
        uint256 totalUnlockedRounds,
        uint256 totalUnlockedTokens,
        uint256 totalProjectTokens,
        uint256 totalCommunityTokens,
        uint256 remainingTokens
    ) {
<span class="cstat-no" title="statement not covered" >        for (uint256 i = 1; i &lt;= TOTAL_ROUNDS; i++) {</span>
<span class="cstat-no" title="statement not covered" >            if (unlockRounds[i].unlocked) {</span>
                totalUnlockedRounds++;
<span class="cstat-no" title="statement not covered" >                if (i == 1) {</span>
                    totalUnlockedTokens += FIRST_ROUND_TOKENS;
                } else {
                    totalUnlockedTokens += TOKENS_PER_ROUND;
                }
                totalProjectTokens += unlockRounds[i].projectTokens;
                totalCommunityTokens += unlockRounds[i].communityTokens;
            }
        }
        
<span class="cstat-no" title="statement not covered" >        uint256 totalSupply = FIRST_ROUND_TOKENS + (TOKENS_PER_ROUND * (TOTAL_ROUNDS - 1));</span>
        remainingTokens = totalSupply - totalUnlockedTokens;
    }
    
    /**
     * @dev 更新价格预言机地址
     */
<span class="fstat-no" title="function not covered" >    function updatePriceOracle(address _newOracle) external onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        require(_newOracle != address(0), "Invalid oracle address")</span>;
<span class="cstat-no" title="statement not covered" >        address oldOracle = address(priceOracle);</span>
        priceOracle = HAOXPriceOracleV2(_newOracle);
<span class="cstat-no" title="statement not covered" >        emit WalletUpdated("priceOracle", oldOracle, _newOracle);</span>
    }
    
    /**
     * @dev 更新项目钱包地址
     */
<span class="fstat-no" title="function not covered" >    function updateProjectWallet(address _newWallet) external onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        require(_newWallet != address(0), "Invalid wallet address")</span>;
<span class="cstat-no" title="statement not covered" >        address oldWallet = projectWallet;</span>
        projectWallet = _newWallet;
<span class="cstat-no" title="statement not covered" >        emit WalletUpdated("project", oldWallet, _newWallet);</span>
    }
    
    /**
     * @dev 更新社区钱包地址
     */
<span class="fstat-no" title="function not covered" >    function updateCommunityWallet(address _newWallet) external onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        require(_newWallet != address(0), "Invalid wallet address")</span>;
<span class="cstat-no" title="statement not covered" >        address oldWallet = communityWallet;</span>
        communityWallet = _newWallet;
<span class="cstat-no" title="statement not covered" >        emit WalletUpdated("community", oldWallet, _newWallet);</span>
    }
    
    /**
     * @dev 紧急暂停
     */
<span class="fstat-no" title="function not covered" >    function pause() external onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        _pause()</span>;
    }
    
    /**
     * @dev 恢复运行
     */
<span class="fstat-no" title="function not covered" >    function unpause() external onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        _unpause()</span>;
    }
    
    /**
     * @dev 紧急提取代币（仅限所有者）
     */
<span class="fstat-no" title="function not covered" >    function emergencyWithdraw(address token, uint256 amount) external virtual onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        require(token != address(0), "Invalid token address")</span>;
<span class="cstat-no" title="statement not covered" >        require(IERC20(token).transfer(owner(), amount), "Transfer failed")</span>;
    }
}
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Aug 01 2025 12:47:30 GMT+0800 (中国标准时间)
</div>
</div>
<script src="../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../sorter.js"></script>
</body>
</html>
