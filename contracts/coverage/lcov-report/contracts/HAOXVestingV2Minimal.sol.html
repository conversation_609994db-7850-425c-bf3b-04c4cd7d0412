<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for contracts/HAOXVestingV2Minimal.sol</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../index.html">all files</a> / <a href="index.html">contracts/</a> HAOXVestingV2Minimal.sol
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">79.63% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>43/54</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">59.68% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>37/62</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">73.33% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>11/15</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">79.49% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>62/78</span>
      </div>
    </div>
  </div>
  <div class='status-line medium'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
272
273
274
275
276
277
278
279
280
281
282
283
284
285
286
287
288
289
290
291
292
293
294
295
296
297
298
299
300
301
302
303
304
305
306
307
308
309
310
311
312
313
314
315
316
317
318
319
320
321
322
323
324
325
326
327
328
329
330
331
332
333
334
335
336
337
338
339
340
341
342
343
344
345
346
347
348
349
350
351
352
353
354
355
356
357
358
359
360
361</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">38×</span>
<span class="cline-any cline-yes">37×</span>
<span class="cline-any cline-yes">37×</span>
<span class="cline-any cline-yes">37×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">37×</span>
<span class="cline-any cline-yes">37×</span>
<span class="cline-any cline-yes">37×</span>
<span class="cline-any cline-yes">37×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">37×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">37×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">37×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">37×</span>
<span class="cline-any cline-yes">37×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">22×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">22×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">22×</span>
<span class="cline-any cline-yes">22×</span>
<span class="cline-any cline-yes">22×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">22×</span>
<span class="cline-any cline-yes">22×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">22×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">22×</span>
<span class="cline-any cline-yes">19×</span>
<span class="cline-any cline-yes">11×</span>
<span class="cline-any cline-yes">11×</span>
<span class="cline-any cline-yes">11×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-yes">7×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">7×</span>
<span class="cline-any cline-yes">7×</span>
<span class="cline-any cline-yes">7×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">7×</span>
<span class="cline-any cline-yes">7×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">7×</span>
<span class="cline-any cline-yes">7×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">22×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">22×</span>
<span class="cline-any cline-yes">22×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;
&nbsp;
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";
&nbsp;
/**
 * @title HAOXVestingV2Minimal
 * <AUTHOR> Team
 * @notice 精简版HAOX代币解锁合约 - 成本优化版本
 * @dev 保留核心安全功能，移除非必要特性以降低部署成本
 * 实现31轮价格阶梯解锁机制，每轮需要价格维持7天才能解锁
 */
contract HAOXVestingV2Minimal is Ownable, ReentrancyGuard, Pausable {
    
    /// @notice 总解锁轮次数量
    uint256 public constant TOTAL_ROUNDS = 31;
    /// @notice 价格维持持续时间（7天）
    uint256 public constant PRICE_MAINTAIN_DURATION = 7 days;
    /// @notice 紧急提取延迟时间（7天）
    uint256 public constant EMERGENCY_DELAY = 7 days;
    /// @notice 最大紧急提取金额（100万代币）
    uint256 public constant MAX_EMERGENCY_AMOUNT = 1000000 * 10**18;
    
    /// @notice HAOX代币合约地址
    IERC20 public immutable HAOX_TOKEN;
    /// @notice 价格预言机合约地址
    address public immutable PRICE_ORACLE;
    /// @notice 项目方钱包地址
    address public immutable PROJECT_WALLET;
    /// @notice 社区钱包地址
    address public immutable COMMUNITY_WALLET;
    
    uint256 public currentRound = 1;
    
    // 轮次信息结构（优化存储）
    struct Round {
        uint128 triggerPrice;      // 触发价格 (8位小数)
        uint64 priceReachedTime;   // 价格达到时间
        uint64 unlockTime;         // 解锁时间
        bool priceConditionMet;    // 价格条件是否满足
        bool unlocked;             // 是否已解锁
    }
    
    // 紧急提取请求结构（精简版）
    struct EmergencyRequest {
        uint128 amount;
        uint64 requestTime;
        bool executed;
        address requester;
    }
    
    // 状态映射
    mapping(uint256 =&gt; Round) public rounds;
    mapping(bytes32 =&gt; EmergencyRequest) public emergencyRequests;
    mapping(address =&gt; bool) public emergencySigners;
    
    // 精简的价格历史（仅保留最近10条）
    struct PriceCheck {
        uint64 timestamp;
        uint64 price;
        bool conditionMet;
    }
    mapping(uint256 =&gt; PriceCheck[10]) public priceHistory;
    mapping(uint256 =&gt; uint8) public historyIndex;
    
    uint256 public requiredSignatures = 1;
    
    /**
     * @notice 价格条件满足事件
     * @param roundNumber 轮次编号
     * @param price 当前价格
     * @param timestamp 时间戳
     */
    event PriceConditionMet(uint256 indexed roundNumber, uint256 indexed price, uint256 indexed timestamp);
&nbsp;
    /**
     * @notice 轮次解锁事件
     * @param roundNumber 轮次编号
     * @param triggerPrice 触发价格
     * @param projectTokens 项目方获得的代币数量
     * @param communityTokens 社区获得的代币数量
     * @param timestamp 解锁时间戳
     */
    event RoundUnlocked(
        uint256 indexed roundNumber,
        uint256 indexed triggerPrice,
        uint256 indexed projectTokens,
        uint256 communityTokens,
        uint256 timestamp
    );
&nbsp;
    /**
     * @notice 价格条件重置事件
     * @param roundNumber 轮次编号
     * @param price 当前价格
     * @param timestamp 时间戳
     */
    event PriceConditionReset(uint256 indexed roundNumber, uint256 indexed price, uint256 indexed timestamp);
&nbsp;
    /**
     * @notice 紧急提取请求事件
     * @param requestId 请求ID
     * @param amount 提取金额
     * @param requestTime 请求时间
     */
    event EmergencyWithdrawRequested(bytes32 indexed requestId, uint256 indexed amount, uint256 indexed requestTime);
&nbsp;
    /**
     * @notice 紧急提取执行事件
     * @param requestId 请求ID
     * @param amount 提取金额
     */
    event EmergencyWithdrawExecuted(bytes32 indexed requestId, uint256 indexed amount);
&nbsp;
    /**
     * @notice 构造函数，初始化解锁合约
     * @param _haoxToken HAOX代币合约地址
     * @param _priceOracle 价格预言机合约地址
     * @param _projectWallet 项目方钱包地址
     * @param _communityWallet 社区钱包地址
     */
    constructor(
        address _haoxToken,
        address _priceOracle,
        address _projectWallet,
        address _communityWallet
    ) Ownable(msg.sender) {
        require(_haoxToken != address(0), "Invalid token address");
        <span class="missing-if-branch" title="else path not taken" >E</span>require(_priceOracle != address(0), "Invalid oracle address");
        <span class="missing-if-branch" title="else path not taken" >E</span>require(_projectWallet != address(0), "Invalid project wallet");
        <span class="missing-if-branch" title="else path not taken" >E</span>require(_communityWallet != address(0), "Invalid community wallet");
        
        HAOX_TOKEN = IERC20(_haoxToken);
        PRICE_ORACLE = _priceOracle;
        PROJECT_WALLET = _projectWallet;
        COMMUNITY_WALLET = _communityWallet;
        
        // 初始化紧急签名者
        emergencySigners[msg.sender] = true;
        
        // 初始化轮次数据（精简版）
        _initializeRounds();
    }
&nbsp;
    /**
     * @dev 初始化轮次数据（Gas优化版本）
     */
    function _initializeRounds() internal {
        // 使用紧凑的初始化方式
        uint128[31] memory prices = [
            uint128(0.01 * 10**8), uint128(0.015 * 10**8), uint128(0.02 * 10**8), uint128(0.025 * 10**8), uint128(0.03 * 10**8),
            uint128(0.035 * 10**8), uint128(0.04 * 10**8), uint128(0.045 * 10**8), uint128(0.05 * 10**8), uint128(0.055 * 10**8),
            uint128(0.06 * 10**8), uint128(0.065 * 10**8), uint128(0.07 * 10**8), uint128(0.075 * 10**8), uint128(0.08 * 10**8),
            uint128(0.085 * 10**8), uint128(0.09 * 10**8), uint128(0.095 * 10**8), uint128(0.1 * 10**8), uint128(0.11 * 10**8),
            uint128(0.12 * 10**8), uint128(0.13 * 10**8), uint128(0.14 * 10**8), uint128(0.15 * 10**8), uint128(0.16 * 10**8),
            uint128(0.17 * 10**8), uint128(0.18 * 10**8), uint128(0.19 * 10**8), uint128(0.2 * 10**8), uint128(0.22 * 10**8),
            uint128(0.25 * 10**8)
        ];
        
        for (uint256 i = 0; i &lt; 31; i++) {
            rounds[i + 1].triggerPrice = prices[i];
        }
    }
&nbsp;
    /**
     * @dev 检查价格条件（Gas优化版本）
     */
    function checkPriceCondition() external <span class="missing-if-branch" title="else path not taken" >E</span>nonReentrant whenNotPaused {
        uint256 roundNumber = currentRound;
        
        <span class="missing-if-branch" title="if path not taken" >I</span>if (roundNumber &gt; TOTAL_ROUNDS) {
<span class="cstat-no" title="statement not covered" >            return;</span>
        }
        
        // 获取当前价格
        uint256 currentPrice = _getCurrentPrice();
        Round storage round = rounds[roundNumber];
        bool conditionMet = currentPrice &gt;= round.triggerPrice;
        
        // 添加到历史记录（循环覆盖）
        uint8 index = historyIndex[roundNumber];
        priceHistory[roundNumber][index] = PriceCheck({
            timestamp: uint64(block.timestamp),
            price: uint64(currentPrice),
            conditionMet: conditionMet
        });
        historyIndex[roundNumber] = (index + 1) % 10;
        
        if (conditionMet) {
            if (!round.priceConditionMet) {
                round.priceConditionMet = true;
                round.priceReachedTime = uint64(block.timestamp);
                emit PriceConditionMet(roundNumber, currentPrice, block.timestamp);
            } else {
                uint256 maintainedDuration = block.timestamp - round.priceReachedTime;
                if (maintainedDuration &gt;= PRICE_MAINTAIN_DURATION &amp;&amp; !round.unlocked) {
                    _unlockRound(roundNumber, currentPrice);
                }
            }
        } else {
            if (round.priceConditionMet &amp;&amp; !round.unlocked) {
                round.priceConditionMet = false;
                round.priceReachedTime = 0;
                emit PriceConditionReset(roundNumber, currentPrice, block.timestamp);
            }
        }
    }
&nbsp;
    /**
     * @dev 解锁轮次（精简版）
     */
    function _unlockRound(uint256 roundNumber, uint256 triggerPrice) internal {
        Round storage round = rounds[roundNumber];
        round.unlocked = true;
        round.unlockTime = uint64(block.timestamp);
        
        // 计算代币分配（简化计算）
        uint256 projectTokens = 80000000 * 10**18; // 8000万项目代币
        uint256 communityTokens = 80000000 * 10**18; // 8000万社区代币
        
        // 转移代币
        <span class="missing-if-branch" title="else path not taken" >E</span>require(HAOX_TOKEN.transfer(PROJECT_WALLET, projectTokens), "Project transfer failed");
        require(HAOX_TOKEN.transfer(COMMUNITY_WALLET, communityTokens), "Community transfer failed");
        
        emit RoundUnlocked(roundNumber, triggerPrice, projectTokens, communityTokens, block.timestamp);
        
        // 移动到下一轮
        currentRound = roundNumber + 1;
    }
&nbsp;
    /**
     * @dev 获取当前价格（精简版）
     */
    function _getCurrentPrice() internal view returns (uint256) {
        (bool success, bytes memory data) = PRICE_ORACLE.staticcall(
            abi.encodeWithSignature("getLatestPrice()")
        );
        <span class="missing-if-branch" title="else path not taken" >E</span>require(success &amp;&amp; data.length &gt;= 32, "Price oracle failed");
        return abi.decode(data, (uint256));
    }
&nbsp;
    /**
     * @dev 请求紧急提取（精简版）
     */
    function requestEmergencyWithdraw(uint256 amount) external <span class="missing-if-branch" title="else path not taken" >E</span>whenPaused {
        require(emergencySigners[msg.sender], "Not authorized");
        require(amount &lt;= MAX_EMERGENCY_AMOUNT, "Amount too large");
        
        bytes32 requestId = keccak256(abi.encodePacked(amount, block.timestamp, msg.sender));
        
        emergencyRequests[requestId] = EmergencyRequest({
            amount: uint128(amount),
            requestTime: uint64(block.timestamp),
            executed: false,
            requester: msg.sender
        });
        
        emit EmergencyWithdrawRequested(requestId, amount, block.timestamp);
    }
&nbsp;
    /**
     * @dev 执行紧急提取（精简版）
     */
    function executeEmergencyWithdraw(bytes32 requestId) external {
        EmergencyRequest storage request = emergencyRequests[requestId];
        <span class="missing-if-branch" title="else path not taken" >E</span>require(request.requestTime &gt; 0, "Request not found");
        <span class="missing-if-branch" title="else path not taken" >E</span>require(!request.executed, "Already executed");
        <span class="missing-if-branch" title="else path not taken" >E</span>require(block.timestamp &gt;= request.requestTime + EMERGENCY_DELAY, "Time lock active");
        <span class="missing-if-branch" title="else path not taken" >E</span>require(emergencySigners[msg.sender], "Not authorized");
        
        request.executed = true;
        <span class="missing-if-branch" title="else path not taken" >E</span>require(HAOX_TOKEN.transfer(owner(), request.amount), "Transfer failed");
        
        emit EmergencyWithdrawExecuted(requestId, request.amount);
    }
&nbsp;
    /**
     * @dev 获取解锁进度（精简版）
     */
<span class="fstat-no" title="function not covered" >    function getUnlockProgress() external view returns (</span>
        uint256 currentPrice,
        uint256 nextRound,
        uint256 targetPrice,
        bool conditionMet,
        uint256 timeRemaining
    ) {
        currentPrice = _getCurrentPrice();
        nextRound = currentRound;
        
<span class="cstat-no" title="statement not covered" >        if (nextRound &lt;= TOTAL_ROUNDS) {</span>
<span class="cstat-no" title="statement not covered" >            Round memory round = rounds[nextRound];</span>
            targetPrice = round.triggerPrice;
            conditionMet = round.priceConditionMet;
            
<span class="cstat-no" title="statement not covered" >            if (conditionMet &amp;&amp; round.priceReachedTime &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >                uint256 elapsed = block.timestamp - round.priceReachedTime;</span>
                timeRemaining = elapsed &gt;= PRICE_MAINTAIN_DURATION ? 0 : PRICE_MAINTAIN_DURATION - elapsed;
            }
        }
    }
&nbsp;
    /**
     * @dev 获取轮次信息
     */
<span class="fstat-no" title="function not covered" >    function getRoundInfo(uint256 roundNumber) external view returns (</span>
        uint256 triggerPrice,
        bool priceConditionMet,
        bool unlocked,
        uint256 priceReachedTime,
        uint256 unlockTime
    ) {
<span class="cstat-no" title="statement not covered" >        require(roundNumber &gt; 0 &amp;&amp; roundNumber &lt;= TOTAL_ROUNDS, "Invalid round")</span>;
<span class="cstat-no" title="statement not covered" >        Round memory round = rounds[roundNumber];</span>
        
<span class="cstat-no" title="statement not covered" >        return (</span>
            round.triggerPrice,
            round.priceConditionMet,
            round.unlocked,
            round.priceReachedTime,
            round.unlockTime
        );
    }
&nbsp;
    /**
     * @dev 获取价格历史（精简版）
     */
<span class="fstat-no" title="function not covered" >    function getPriceHistory(uint256 roundNumber) external view returns (PriceCheck[10] memory) {</span>
<span class="cstat-no" title="statement not covered" >        return priceHistory[roundNumber];</span>
    }
&nbsp;
    /**
     * @dev 管理员功能
     */
    function addEmergencySigner(address signer) external onlyOwner {
        emergencySigners[signer] = true;
    }
&nbsp;
    function removeEmergencySigner(address signer) external <span class="missing-if-branch" title="else path not taken" >E</span>onlyOwner {
        emergencySigners[signer] = false;
    }
&nbsp;
    function pause() external onlyOwner {
        _pause();
    }
&nbsp;
    function unpause() external <span class="missing-if-branch" title="else path not taken" >E</span>onlyOwner {
        _unpause();
    }
&nbsp;
    /**
     * @dev 紧急暂停（无时间锁）
     */
<span class="fstat-no" title="function not covered" >    function emergencyPause() external {</span>
<span class="cstat-no" title="statement not covered" >        require(emergencySigners[msg.sender], "Not authorized")</span>;
<span class="cstat-no" title="statement not covered" >        _pause()</span>;
    }
}
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Sat Aug 02 2025 00:11:34 GMT+0800 (中国标准时间)
</div>
</div>
<script src="../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../sorter.js"></script>
</body>
</html>
