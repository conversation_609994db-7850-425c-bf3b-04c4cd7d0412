<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for contracts/HAOXPriceAggregatorV2.sol</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../index.html">all files</a> / <a href="index.html">contracts/</a> HAOXPriceAggregatorV2.sol
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>0/116</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>0/130</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>0/28</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>0/177</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
272
273
274
275
276
277
278
279
280
281
282
283
284
285
286
287
288
289
290
291
292
293
294
295
296
297
298
299
300
301
302
303
304
305
306
307
308
309
310
311
312
313
314
315
316
317
318
319
320
321
322
323
324
325
326
327
328
329
330
331
332
333
334
335
336
337
338
339
340
341
342
343
344
345
346
347
348
349
350
351
352
353
354
355
356
357
358
359
360
361
362
363
364
365
366
367
368
369
370
371
372
373
374
375
376
377
378
379
380
381
382
383
384
385
386
387
388
389
390
391
392
393
394
395
396
397
398
399
400
401
402
403
404
405
406
407
408
409
410
411
412
413
414
415
416
417
418
419
420
421
422
423
424
425
426
427
428
429
430
431
432
433
434
435
436
437
438
439
440
441
442
443
444
445
446
447
448
449
450
451
452
453
454
455
456
457
458
459
460
461
462
463
464
465
466
467
468
469
470
471
472
473
474
475
476
477
478
479
480
481
482
483
484
485
486
487
488
489
490
491
492
493
494
495
496
497
498
499
500
501
502
503
504
505
506
507
508
509
510
511
512
513
514
515
516
517
518
519
520
521
522
523
524
525
526
527
528
529
530
531
532
533
534
535
536
537
538
539
540
541
542
543
544
545
546
547
548
549
550
551
552
553
554
555
556
557
558
559
560
561
562
563
564
565
566
567
568
569
570
571
572
573
574
575
576
577
578
579
580
581
582
583
584
585
586
587
588
589
590
591
592
593
594
595
596
597
598
599
600
601
602
603
604
605
606
607
608
609
610
611
612
613
614
615
616
617
618
619
620
621
622
623
624
625
626
627
628
629
630
631
632
633
634
635
636
637
638
639
640
641
642
643
644
645
646
647
648
649
650
651
652
653
654</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;
&nbsp;
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";
&nbsp;
/**
 * @title HAOXPriceAggregatorV2
 * @dev 多预言机价格聚合器
 * 支持多个价格源，实现价格偏差检测和自动故障转移
 */
contract HAOXPriceAggregatorV2 is Ownable, ReentrancyGuard, Pausable {
    
    // 价格源结构
    struct PriceSource {
        address oracle;
        uint256 weight;
        bool active;
        uint256 lastUpdate;
        uint256 lastPrice;
        string name;
        uint256 failureCount;
        uint256 maxFailures;
    }
    
    // 价格数据结构
    struct PriceData {
        uint256 price;
        uint256 timestamp;
        uint256 confidence;
        uint256 sourceCount;
    }
    
    // 状态变量
    mapping(uint256 =&gt; PriceSource) public priceSources;
    uint256 public sourceCount;
    
    // 聚合配置
    uint256 public constant MAX_PRICE_DEVIATION = 500; // 5% (基点)
    uint256 public constant PRICE_STALENESS_THRESHOLD = 3600; // 1小时
    uint256 public constant MIN_SOURCES_REQUIRED = 2;
    uint256 public constant MAX_SOURCES = 10;
    
    // 价格历史
    PriceData[] public priceHistory;
    uint256 public constant MAX_HISTORY_SIZE = 100;
    
    // 最新聚合价格
    PriceData public latestPrice;
    
    // 故障转移配置
    uint256 public emergencyPrice;
    uint256 public emergencyPriceTimestamp;
    bool public emergencyMode;
    
    // 事件定义
    event PriceSourceAdded(
        uint256 indexed sourceId,
        address indexed oracle,
        string name,
        uint256 weight
    );
    
    event PriceSourceUpdated(
        uint256 indexed sourceId,
        bool active,
        uint256 weight,
        uint256 maxFailures
    );
    
    event PriceSourceRemoved(uint256 indexed sourceId);
    
    event PriceAggregated(
        uint256 price,
        uint256 timestamp,
        uint256 sourceCount,
        uint256 confidence
    );
    
    event PriceSourceFailed(
        uint256 indexed sourceId,
        string reason,
        uint256 failureCount
    );
    
    event EmergencyModeActivated(uint256 price, uint256 timestamp);
    event EmergencyModeDeactivated();
    
    event PriceDeviationDetected(
        uint256 indexed sourceId,
        uint256 sourcePrice,
        uint256 aggregatedPrice,
        uint256 deviation
    );
&nbsp;
    // 修饰符
<span class="fstat-no" title="function not covered" >    modifier validSourceId(uint256 sourceId) {</span>
<span class="cstat-no" title="statement not covered" >        require(sourceId &lt; sourceCount, "Invalid source ID")</span>;
<span class="cstat-no" title="statement not covered" >        require(priceSources[sourceId].oracle != address(0), "Source not exists")</span>;
        _;
    }
&nbsp;
    /**
     * @dev 构造函数
     */
<span class="fstat-no" title="function not covered" >    constructor() Ownable(msg.sender) {</span>
        // 初始化默认值
        emergencyMode = false;
    }
&nbsp;
    /**
     * @dev 添加价格源
     */
<span class="fstat-no" title="function not covered" >    function addPriceSource(</span>
        address oracle,
        string calldata name,
        uint256 weight,
        uint256 maxFailures
    ) external onlyOwner {
<span class="cstat-no" title="statement not covered" >        require(oracle != address(0), "Invalid oracle address")</span>;
<span class="cstat-no" title="statement not covered" >        require(weight &gt; 0 &amp;&amp; weight &lt;= 100, "Invalid weight")</span>;
<span class="cstat-no" title="statement not covered" >        require(bytes(name).length &gt; 0, "Name required")</span>;
<span class="cstat-no" title="statement not covered" >        require(sourceCount &lt; MAX_SOURCES, "Too many sources")</span>;
<span class="cstat-no" title="statement not covered" >        require(maxFailures &gt; 0 &amp;&amp; maxFailures &lt;= 10, "Invalid max failures")</span>;
        
        // 检查是否已存在
<span class="cstat-no" title="statement not covered" >        for (uint256 i = 0; i &lt; sourceCount; i++) {</span>
<span class="cstat-no" title="statement not covered" >            require(priceSources[i].oracle != oracle, "Oracle already exists")</span>;
        }
        
        priceSources[sourceCount] = PriceSource({
            oracle: oracle,
            weight: weight,
            active: true,
            lastUpdate: 0,
            lastPrice: 0,
            name: name,
            failureCount: 0,
            maxFailures: maxFailures
        });
        
<span class="cstat-no" title="statement not covered" >        emit PriceSourceAdded(sourceCount, oracle, name, weight);</span>
        sourceCount++;
    }
&nbsp;
    /**
     * @dev 更新价格源配置
     */
<span class="fstat-no" title="function not covered" >    function updatePriceSource(</span>
        uint256 sourceId,
        bool active,
        uint256 weight,
        uint256 maxFailures
    ) external onlyOwner validSourceId(sourceId) {
<span class="cstat-no" title="statement not covered" >        require(weight &gt; 0 &amp;&amp; weight &lt;= 100, "Invalid weight")</span>;
<span class="cstat-no" title="statement not covered" >        require(maxFailures &gt; 0 &amp;&amp; maxFailures &lt;= 10, "Invalid max failures")</span>;
        
<span class="cstat-no" title="statement not covered" >        PriceSource storage source = priceSources[sourceId];</span>
        source.active = active;
        source.weight = weight;
        source.maxFailures = maxFailures;
        
        // 如果重新激活，重置失败计数
<span class="cstat-no" title="statement not covered" >        if (active) {</span>
            source.failureCount = 0;
        }
        
<span class="cstat-no" title="statement not covered" >        emit PriceSourceUpdated(sourceId, active, weight, maxFailures);</span>
    }
&nbsp;
    /**
     * @dev 移除价格源
     */
<span class="fstat-no" title="function not covered" >    function removePriceSource(uint256 sourceId) external onlyOwner validSourceId(sourceId) {</span>
        // 确保至少保留最小数量的源
<span class="cstat-no" title="statement not covered" >        uint256 activeCount = getActiveSourceCount();</span>
<span class="cstat-no" title="statement not covered" >        require(activeCount &gt; MIN_SOURCES_REQUIRED, "Cannot remove source below minimum")</span>;
        
        delete priceSources[sourceId];
<span class="cstat-no" title="statement not covered" >        emit PriceSourceRemoved(sourceId);</span>
    }
&nbsp;
    /**
     * @dev 获取聚合价格
     */
<span class="fstat-no" title="function not covered" >    function getLatestPrice() external view returns (uint256) {</span>
<span class="cstat-no" title="statement not covered" >        if (emergencyMode) {</span>
<span class="cstat-no" title="statement not covered" >            require(</span>
                block.timestamp - emergencyPriceTimestamp &lt;= PRICE_STALENESS_THRESHOLD * 2,
                "Emergency price too stale"
            );
<span class="cstat-no" title="statement not covered" >            return emergencyPrice;</span>
        }
        
<span class="cstat-no" title="statement not covered" >        require(latestPrice.timestamp &gt; 0, "No price available")</span>;
<span class="cstat-no" title="statement not covered" >        require(</span>
            block.timestamp - latestPrice.timestamp &lt;= PRICE_STALENESS_THRESHOLD,
            "Price too stale"
        );
        
<span class="cstat-no" title="statement not covered" >        return latestPrice.price;</span>
    }
&nbsp;
    /**
     * @dev 获取最后更新时间
     */
<span class="fstat-no" title="function not covered" >    function getLastUpdateTime() external view returns (uint256) {</span>
<span class="cstat-no" title="statement not covered" >        if (emergencyMode) {</span>
<span class="cstat-no" title="statement not covered" >            return emergencyPriceTimestamp;</span>
        }
<span class="cstat-no" title="statement not covered" >        return latestPrice.timestamp;</span>
    }
&nbsp;
    /**
     * @dev 更新聚合价格
     */
<span class="fstat-no" title="function not covered" >    function updateAggregatedPrice() external nonReentrant whenNotPaused {</span>
<span class="cstat-no" title="statement not covered" >        require(!emergencyMode, "Emergency mode active")</span>;
        
<span class="cstat-no" title="statement not covered" >        (uint256 aggregatedPrice, uint256 confidence, uint256 validSources) = _calculateAggregatedPrice();</span>
        
<span class="cstat-no" title="statement not covered" >        require(validSources &gt;= MIN_SOURCES_REQUIRED, "Insufficient valid sources")</span>;
        
        // 更新最新价格
        latestPrice = PriceData({
            price: aggregatedPrice,
            timestamp: block.timestamp,
            confidence: confidence,
            sourceCount: validSources
        });
        
        // 添加到历史记录
<span class="cstat-no" title="statement not covered" >        _addToHistory(latestPrice)</span>;
        
<span class="cstat-no" title="statement not covered" >        emit PriceAggregated(aggregatedPrice, block.timestamp, validSources, confidence);</span>
    }
&nbsp;
    /**
     * @dev 计算聚合价格
     */
<span class="fstat-no" title="function not covered" >    function _calculateAggregatedPrice() internal returns (</span>
        uint256 aggregatedPrice,
        uint256 confidence,
        uint256 validSources
    ) {
<span class="cstat-no" title="statement not covered" >        uint256 totalWeight = 0;</span>
<span class="cstat-no" title="statement not covered" >        uint256 weightedSum = 0;</span>
<span class="cstat-no" title="statement not covered" >        uint256[] memory prices = new uint256[](sourceCount);</span>
<span class="cstat-no" title="statement not covered" >        uint256[] memory weights = new uint256[](sourceCount);</span>
        validSources = 0;
        
        // 收集所有有效价格
<span class="cstat-no" title="statement not covered" >        for (uint256 i = 0; i &lt; sourceCount; i++) {</span>
<span class="cstat-no" title="statement not covered" >            PriceSource storage source = priceSources[i];</span>
<span class="cstat-no" title="statement not covered" >            if (!source.active) continue;</span>
&nbsp;
            // 使用低级调用替代try-catch
<span class="cstat-no" title="statement not covered" >            (bool priceSuccess, uint256 price) = _tryGetPriceFromSource(i);</span>
<span class="cstat-no" title="statement not covered" >            if (!priceSuccess) {</span>
<span class="cstat-no" title="statement not covered" >                _handleSourceFailure(i, "Oracle call failed")</span>;
                continue;
            }
&nbsp;
<span class="cstat-no" title="statement not covered" >            (bool timeSuccess, uint256 updateTime) = _tryGetUpdateTimeFromSource(i);</span>
<span class="cstat-no" title="statement not covered" >            if (!timeSuccess) {</span>
                updateTime = block.timestamp; // 使用当前时间作为默认值
            }
&nbsp;
            // 检查价格是否过期
<span class="cstat-no" title="statement not covered" >            if (block.timestamp - updateTime &gt; PRICE_STALENESS_THRESHOLD) {</span>
<span class="cstat-no" title="statement not covered" >                _handleSourceFailure(i, "Price too stale")</span>;
                continue;
            }
&nbsp;
            // 检查价格是否合理（不能为0或过大）
<span class="cstat-no" title="statement not covered" >            if (price == 0 || price &gt; 1e18) { // 假设最大价格为1 ETH</span>
<span class="cstat-no" title="statement not covered" >                _handleSourceFailure(i, "Invalid price value")</span>;
                continue;
            }
&nbsp;
            prices[validSources] = price;
            weights[validSources] = source.weight;
            validSources++;
&nbsp;
            weightedSum += price * source.weight;
            totalWeight += source.weight;
&nbsp;
            // 更新源信息
            source.lastUpdate = updateTime;
            source.lastPrice = price;
&nbsp;
            // 重置失败计数（成功获取价格）
<span class="cstat-no" title="statement not covered" >            if (source.failureCount &gt; 0) {</span>
                source.failureCount = 0;
            }
        }
        
<span class="cstat-no" title="statement not covered" >        require(validSources &gt;= MIN_SOURCES_REQUIRED, "Insufficient valid sources")</span>;
        
        aggregatedPrice = weightedSum / totalWeight;
        
        // 验证价格偏差
<span class="cstat-no" title="statement not covered" >        _validatePriceDeviation(prices, weights, validSources, aggregatedPrice)</span>;
        
        // 计算置信度（基于源数量和权重分布）
        confidence = _calculateConfidence(validSources, totalWeight);
        
<span class="cstat-no" title="statement not covered" >        return (aggregatedPrice, confidence, validSources);</span>
    }
&nbsp;
    /**
     * @dev 从价格源获取价格
     */
<span class="fstat-no" title="function not covered" >    function _getPriceFromSource(uint256 sourceId) internal view returns (uint256) {</span>
<span class="cstat-no" title="statement not covered" >        PriceSource memory source = priceSources[sourceId];</span>
        
        // 调用预言机接口
<span class="cstat-no" title="statement not covered" >        (bool success, bytes memory data) = source.oracle.staticcall(</span>
            abi.encodeWithSignature("getLatestPrice()")
        );
        
<span class="cstat-no" title="statement not covered" >        require(success, "Oracle call failed")</span>;
<span class="cstat-no" title="statement not covered" >        require(data.length &gt;= 32, "Invalid response")</span>;
        
<span class="cstat-no" title="statement not covered" >        return abi.decode(data, (uint256));</span>
    }
&nbsp;
    /**
     * @dev 从价格源获取更新时间
     */
<span class="fstat-no" title="function not covered" >    function _getUpdateTimeFromSource(uint256 sourceId) internal view returns (uint256) {</span>
<span class="cstat-no" title="statement not covered" >        PriceSource memory source = priceSources[sourceId];</span>
&nbsp;
        // 尝试获取更新时间
<span class="cstat-no" title="statement not covered" >        (bool success, bytes memory data) = source.oracle.staticcall(</span>
            abi.encodeWithSignature("getLastUpdateTime()")
        );
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (success &amp;&amp; data.length &gt;= 32) {</span>
<span class="cstat-no" title="statement not covered" >            return abi.decode(data, (uint256));</span>
        }
&nbsp;
        // 如果没有更新时间接口，使用当前时间
<span class="cstat-no" title="statement not covered" >        return block.timestamp;</span>
    }
&nbsp;
    /**
     * @dev 尝试从价格源获取价格（安全版本）
     */
<span class="fstat-no" title="function not covered" >    function _tryGetPriceFromSource(uint256 sourceId) internal view returns (bool success, uint256 price) {</span>
<span class="cstat-no" title="statement not covered" >        PriceSource memory source = priceSources[sourceId];</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        (bool callSuccess, bytes memory data) = source.oracle.staticcall(</span>
            abi.encodeWithSignature("getLatestPrice()")
        );
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (callSuccess &amp;&amp; data.length &gt;= 32) {</span>
            price = abi.decode(data, (uint256));
            success = true;
        } else {
            success = false;
            price = 0;
        }
    }
&nbsp;
    /**
     * @dev 尝试从价格源获取更新时间（安全版本）
     */
<span class="fstat-no" title="function not covered" >    function _tryGetUpdateTimeFromSource(uint256 sourceId) internal view returns (bool success, uint256 updateTime) {</span>
<span class="cstat-no" title="statement not covered" >        PriceSource memory source = priceSources[sourceId];</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        (bool callSuccess, bytes memory data) = source.oracle.staticcall(</span>
            abi.encodeWithSignature("getLastUpdateTime()")
        );
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (callSuccess &amp;&amp; data.length &gt;= 32) {</span>
            updateTime = abi.decode(data, (uint256));
            success = true;
        } else {
            success = false;
            updateTime = block.timestamp;
        }
    }
&nbsp;
    /**
     * @dev 验证价格偏差
     */
<span class="fstat-no" title="function not covered" >    function _validatePriceDeviation(</span>
        uint256[] memory prices,
        uint256[] memory weights,
        uint256 count,
        uint256 aggregatedPrice
    ) internal {
<span class="cstat-no" title="statement not covered" >        for (uint256 i = 0; i &lt; count; i++) {</span>
<span class="cstat-no" title="statement not covered" >            uint256 deviation = prices[i] &gt; aggregatedPrice</span>
                ? (prices[i] - aggregatedPrice) * 10000 / aggregatedPrice
                : (aggregatedPrice - prices[i]) * 10000 / aggregatedPrice;
                
<span class="cstat-no" title="statement not covered" >            if (deviation &gt; MAX_PRICE_DEVIATION) {</span>
<span class="cstat-no" title="statement not covered" >                emit PriceDeviationDetected(i, prices[i], aggregatedPrice, deviation);</span>
                
                // 如果偏差过大，降低该源的权重或标记为失败
<span class="cstat-no" title="statement not covered" >                if (deviation &gt; MAX_PRICE_DEVIATION * 2) {</span>
<span class="cstat-no" title="statement not covered" >                    _handleSourceFailure(i, "Price deviation too high")</span>;
                }
            }
        }
    }
&nbsp;
    /**
     * @dev 计算置信度
     */
<span class="fstat-no" title="function not covered" >    function _calculateConfidence(uint256 validSourceCount, uint256 totalWeight) internal pure returns (uint256) {</span>
        // 基础置信度基于源数量
<span class="cstat-no" title="statement not covered" >        uint256 baseConfidence = validSourceCount * 20;</span> // 每个源贡献20%
<span class="cstat-no" title="statement not covered" >        if (baseConfidence &gt; 80) baseConfidence = 80;</span>
&nbsp;
        // 权重分布奖励（权重越分散，置信度越高）
<span class="cstat-no" title="statement not covered" >        uint256 weightBonus = totalWeight &gt; 0 ? (validSourceCount * 100) / totalWeight : 0;</span>
<span class="cstat-no" title="statement not covered" >        if (weightBonus &gt; 20) weightBonus = 20;</span>
        
<span class="cstat-no" title="statement not covered" >        uint256 confidence = baseConfidence + weightBonus;</span>
<span class="cstat-no" title="statement not covered" >        return confidence &gt; 100 ? 100 : confidence;</span>
    }
&nbsp;
    /**
     * @dev 处理源失败
     */
<span class="fstat-no" title="function not covered" >    function _handleSourceFailure(uint256 sourceId, string memory reason) internal {</span>
<span class="cstat-no" title="statement not covered" >        PriceSource storage source = priceSources[sourceId];</span>
        source.failureCount++;
        
<span class="cstat-no" title="statement not covered" >        emit PriceSourceFailed(sourceId, reason, source.failureCount);</span>
        
        // 如果失败次数过多，自动禁用
<span class="cstat-no" title="statement not covered" >        if (source.failureCount &gt;= source.maxFailures) {</span>
            source.active = false;
<span class="cstat-no" title="statement not covered" >            emit PriceSourceUpdated(sourceId, false, source.weight, source.maxFailures);</span>
        }
    }
&nbsp;
    /**
     * @dev 添加到历史记录
     */
<span class="fstat-no" title="function not covered" >    function _addToHistory(PriceData memory priceData) internal {</span>
<span class="cstat-no" title="statement not covered" >        if (priceHistory.length &gt;= MAX_HISTORY_SIZE) {</span>
            // 移除最旧的记录
<span class="cstat-no" title="statement not covered" >            for (uint256 i = 0; i &lt; priceHistory.length - 1; i++) {</span>
                priceHistory[i] = priceHistory[i + 1];
            }
            priceHistory[priceHistory.length - 1] = priceData;
        } else {
<span class="cstat-no" title="statement not covered" >            priceHistory.push(priceData)</span>;
        }
    }
&nbsp;
    /**
     * @dev 激活紧急模式
     */
<span class="fstat-no" title="function not covered" >    function activateEmergencyMode(uint256 price) external onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        require(price &gt; 0, "Invalid emergency price")</span>;
        
        emergencyMode = true;
        emergencyPrice = price;
        emergencyPriceTimestamp = block.timestamp;
        
<span class="cstat-no" title="statement not covered" >        emit EmergencyModeActivated(price, block.timestamp);</span>
    }
&nbsp;
    /**
     * @dev 停用紧急模式
     */
<span class="fstat-no" title="function not covered" >    function deactivateEmergencyMode() external onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        require(emergencyMode, "Emergency mode not active")</span>;
<span class="cstat-no" title="statement not covered" >        require(getActiveSourceCount() &gt;= MIN_SOURCES_REQUIRED, "Insufficient active sources")</span>;
        
        emergencyMode = false;
        emergencyPrice = 0;
        emergencyPriceTimestamp = 0;
        
<span class="cstat-no" title="statement not covered" >        emit EmergencyModeDeactivated();</span>
    }
&nbsp;
    /**
     * @dev 获取活跃源数量
     */
<span class="fstat-no" title="function not covered" >    function getActiveSourceCount() public view returns (uint256) {</span>
<span class="cstat-no" title="statement not covered" >        uint256 count = 0;</span>
<span class="cstat-no" title="statement not covered" >        for (uint256 i = 0; i &lt; sourceCount; i++) {</span>
<span class="cstat-no" title="statement not covered" >            if (priceSources[i].active &amp;&amp; priceSources[i].oracle != address(0)) {</span>
                count++;
            }
        }
<span class="cstat-no" title="statement not covered" >        return count;</span>
    }
&nbsp;
    /**
     * @dev 获取所有价格源信息
     */
<span class="fstat-no" title="function not covered" >    function getAllSources() external view returns (</span>
        address[] memory oracles,
        string[] memory names,
        uint256[] memory weights,
        bool[] memory activeStates,
        uint256[] memory lastPrices,
        uint256[] memory failureCounts
    ) {
        oracles = new address[](sourceCount);
        names = new string[](sourceCount);
        weights = new uint256[](sourceCount);
        activeStates = new bool[](sourceCount);
        lastPrices = new uint256[](sourceCount);
        failureCounts = new uint256[](sourceCount);
        
<span class="cstat-no" title="statement not covered" >        for (uint256 i = 0; i &lt; sourceCount; i++) {</span>
<span class="cstat-no" title="statement not covered" >            PriceSource memory source = priceSources[i];</span>
            oracles[i] = source.oracle;
            names[i] = source.name;
            weights[i] = source.weight;
            activeStates[i] = source.active;
            lastPrices[i] = source.lastPrice;
            failureCounts[i] = source.failureCount;
        }
    }
&nbsp;
    /**
     * @dev 获取价格历史
     */
<span class="fstat-no" title="function not covered" >    function getPriceHistory(uint256 limit) external view returns (PriceData[] memory) {</span>
<span class="cstat-no" title="statement not covered" >        uint256 length = priceHistory.length;</span>
<span class="cstat-no" title="statement not covered" >        if (limit &gt; length) limit = length;</span>
        
<span class="cstat-no" title="statement not covered" >        PriceData[] memory result = new PriceData[](limit);</span>
<span class="cstat-no" title="statement not covered" >        for (uint256 i = 0; i &lt; limit; i++) {</span>
            result[i] = priceHistory[length - limit + i];
        }
        
<span class="cstat-no" title="statement not covered" >        return result;</span>
    }
&nbsp;
    /**
     * @dev 获取聚合器状态
     */
<span class="fstat-no" title="function not covered" >    function getAggregatorStatus() external view returns (</span>
        uint256 totalSources,
        uint256 activeSources,
        bool isEmergencyMode,
        uint256 lastUpdateTime,
        uint256 latestPriceValue,
        uint256 confidence
    ) {
<span class="cstat-no" title="statement not covered" >        return (</span>
            sourceCount,
            getActiveSourceCount(),
            emergencyMode,
            latestPrice.timestamp,
            latestPrice.price,
            latestPrice.confidence
        );
    }
&nbsp;
    /**
     * @dev 暂停合约
     */
<span class="fstat-no" title="function not covered" >    function pause() external onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        _pause()</span>;
    }
&nbsp;
    /**
     * @dev 恢复合约
     */
<span class="fstat-no" title="function not covered" >    function unpause() external onlyOwner {</span>
<span class="cstat-no" title="statement not covered" >        _unpause()</span>;
    }
&nbsp;
    /**
     * @dev 批量更新价格源状态
     */
<span class="fstat-no" title="function not covered" >    function batchUpdateSources(</span>
        uint256[] calldata sourceIds,
        bool[] calldata activeStates
    ) external onlyOwner {
<span class="cstat-no" title="statement not covered" >        require(sourceIds.length == activeStates.length, "Array length mismatch")</span>;
&nbsp;
<span class="cstat-no" title="statement not covered" >        for (uint256 i = 0; i &lt; sourceIds.length; i++) {</span>
<span class="cstat-no" title="statement not covered" >            if (sourceIds[i] &lt; sourceCount &amp;&amp; priceSources[sourceIds[i]].oracle != address(0)) {</span>
                priceSources[sourceIds[i]].active = activeStates[i];
&nbsp;
                // 如果重新激活，重置失败计数
<span class="cstat-no" title="statement not covered" >                if (activeStates[i]) {</span>
                    priceSources[sourceIds[i]].failureCount = 0;
                }
&nbsp;
<span class="cstat-no" title="statement not covered" >                emit PriceSourceUpdated(</span>
                    sourceIds[i],
                    activeStates[i],
                    priceSources[sourceIds[i]].weight,
                    priceSources[sourceIds[i]].maxFailures
                );
            }
        }
    }
&nbsp;
    /**
     * @dev 重置源失败计数
     */
<span class="fstat-no" title="function not covered" >    function resetSourceFailures(uint256 sourceId) external onlyOwner validSourceId(sourceId) {</span>
        priceSources[sourceId].failureCount = 0;
&nbsp;
        // 如果源被禁用，重新激活
<span class="cstat-no" title="statement not covered" >        if (!priceSources[sourceId].active) {</span>
            priceSources[sourceId].active = true;
<span class="cstat-no" title="statement not covered" >            emit PriceSourceUpdated(</span>
                sourceId,
                true,
                priceSources[sourceId].weight,
                priceSources[sourceId].maxFailures
            );
        }
    }
&nbsp;
    /**
     * @dev 获取详细的源状态
     */
<span class="fstat-no" title="function not covered" >    function getSourceDetails(uint256 sourceId) external view validSourceId(sourceId) returns (</span>
        address oracle,
        string memory name,
        uint256 weight,
        bool active,
        uint256 lastUpdate,
        uint256 lastPrice,
        uint256 failureCount,
        uint256 maxFailures,
        bool isStale
    ) {
<span class="cstat-no" title="statement not covered" >        PriceSource memory source = priceSources[sourceId];</span>
<span class="cstat-no" title="statement not covered" >        bool stale = source.lastUpdate &gt; 0 &amp;&amp;</span>
                    (block.timestamp - source.lastUpdate) &gt; PRICE_STALENESS_THRESHOLD;
&nbsp;
<span class="cstat-no" title="statement not covered" >        return (</span>
            source.oracle,
            source.name,
            source.weight,
            source.active,
            source.lastUpdate,
            source.lastPrice,
            source.failureCount,
            source.maxFailures,
            stale
        );
    }
}
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Aug 01 2025 12:47:30 GMT+0800 (中国标准时间)
</div>
</div>
<script src="../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../sorter.js"></script>
</body>
</html>
