import { useState, useCallback, useEffect, useRef } from 'react';
import { ethers } from 'ethers';

/**
 * 安全钱包连接Hook
 * 实现分离的只读查询和签名操作模式
 * 添加网络验证和安全交易执行流程
 */
export const useSecureWallet = () => {
    const [account, setAccount] = useState('');
    const [isConnected, setIsConnected] = useState(false);
    const [chainId, setChainId] = useState(null);
    const [balance, setBalance] = useState('0');
    const [isConnecting, setIsConnecting] = useState(false);
    const [error, setError] = useState(null);
    
    // 使用ref存储provider，避免重复创建
    const readOnlyProviderRef = useRef(null);
    const browserProviderRef = useRef(null);
    
    // 网络配置
    const NETWORKS = {
        BSC_TESTNET: {
            chainId: '0x61', // 97
            chainName: 'BSC Testnet',
            nativeCurrency: {
                name: 'BN<PERSON>',
                symbol: 'BNB',
                decimals: 18
            },
            rpcUrls: ['https://data-seed-prebsc-1-s1.binance.org:8545/'],
            blockExplorerUrls: ['https://testnet.bscscan.com/']
        },
        BSC_MAINNET: {
            chainId: '0x38', // 56
            chainName: 'BSC Mainnet',
            nativeCurrency: {
                name: 'BNB',
                symbol: 'BNB',
                decimals: 18
            },
            rpcUrls: ['https://bsc-dataseed1.binance.org/'],
            blockExplorerUrls: ['https://bscscan.com/']
        }
    };

    // 获取目标网络配置
    const getTargetNetwork = useCallback(() => {
        const targetChainId = process.env.NEXT_PUBLIC_CHAIN_ID || '97';
        return targetChainId === '97' ? NETWORKS.BSC_TESTNET : NETWORKS.BSC_MAINNET;
    }, []);

    // 只读连接（用于查询）
    const getReadOnlyProvider = useCallback(() => {
        if (!readOnlyProviderRef.current) {
            const rpcUrl = process.env.NEXT_PUBLIC_BSC_RPC_URL;
            if (!rpcUrl) {
                throw new Error('RPC URL not configured');
            }
            
            readOnlyProviderRef.current = new ethers.JsonRpcProvider(rpcUrl);
        }
        return readOnlyProviderRef.current;
    }, []);

    // 检查钱包是否可用
    const checkWalletAvailability = useCallback(() => {
        if (typeof window === 'undefined') {
            throw new Error('Window object not available');
        }
        
        if (!window.ethereum) {
            throw new Error('请安装MetaMask钱包或其他Web3钱包');
        }
        
        return true;
    }, []);

    // 检查和切换网络
    const checkAndSwitchNetwork = useCallback(async () => {
        const targetNetwork = getTargetNetwork();
        
        try {
            // 获取当前网络
            const currentChainId = await window.ethereum.request({ 
                method: 'eth_chainId' 
            });
            
            setChainId(currentChainId);
            
            if (currentChainId !== targetNetwork.chainId) {
                console.log(`当前网络: ${currentChainId}, 目标网络: ${targetNetwork.chainId}`);
                
                try {
                    // 尝试切换网络
                    await window.ethereum.request({
                        method: 'wallet_switchEthereumChain',
                        params: [{ chainId: targetNetwork.chainId }],
                    });
                    
                    setChainId(targetNetwork.chainId);
                    console.log('✅ 网络切换成功');
                } catch (switchError) {
                    // 如果网络不存在，尝试添加网络
                    if (switchError.code === 4902) {
                        await window.ethereum.request({
                            method: 'wallet_addEthereumChain',
                            params: [targetNetwork],
                        });
                        
                        setChainId(targetNetwork.chainId);
                        console.log('✅ 网络添加并切换成功');
                    } else {
                        throw switchError;
                    }
                }
            }
            
            return true;
        } catch (error) {
            console.error('❌ 网络检查/切换失败:', error);
            throw new Error(`网络切换失败: ${error.message}`);
        }
    }, [getTargetNetwork]);

    // 安全的钱包连接（仅在需要签名时）
    const connectForSigning = useCallback(async () => {
        setIsConnecting(true);
        setError(null);
        
        try {
            // 检查钱包可用性
            checkWalletAvailability();
            
            // 检查和切换网络
            await checkAndSwitchNetwork();
            
            // 请求账户访问
            const accounts = await window.ethereum.request({
                method: 'eth_requestAccounts'
            });

            if (!accounts || accounts.length === 0) {
                throw new Error('未找到可用账户');
            }

            // 创建浏览器provider
            const browserProvider = new ethers.BrowserProvider(window.ethereum);
            browserProviderRef.current = browserProvider;
            
            const signer = await browserProvider.getSigner();
            const address = await signer.getAddress();
            
            // 获取余额
            const balanceWei = await browserProvider.getBalance(address);
            const balanceEth = ethers.formatEther(balanceWei);

            setAccount(address);
            setIsConnected(true);
            setBalance(balanceEth);
            
            console.log('✅ 钱包连接成功:', address);
            console.log('💰 账户余额:', balanceEth, 'BNB');

            return { signer, address, balance: balanceEth };
        } catch (error) {
            console.error('❌ 钱包连接失败:', error);
            setError(error.message);
            throw error;
        } finally {
            setIsConnecting(false);
        }
    }, [checkWalletAvailability, checkAndSwitchNetwork]);

    // 只读模式连接（用于查询数据）
    const connectReadOnly = useCallback(async () => {
        try {
            const provider = getReadOnlyProvider();
            
            // 测试连接
            const network = await provider.getNetwork();
            console.log('✅ 只读连接成功，网络:', network.name);
            
            return provider;
        } catch (error) {
            console.error('❌ 只读连接失败:', error);
            throw new Error(`只读连接失败: ${error.message}`);
        }
    }, [getReadOnlyProvider]);

    // 安全的合约交互
    const executeTransaction = useCallback(async (contractAddress, abi, functionName, params = [], options = {}) => {
        try {
            setError(null);
            
            // 确保已连接钱包
            if (!isConnected) {
                const { signer } = await connectForSigning();
                
                // 创建合约实例
                const contract = new ethers.Contract(contractAddress, abi, signer);
                
                return await executeContractFunction(contract, functionName, params, options);
            } else {
                // 使用现有连接
                if (!browserProviderRef.current) {
                    throw new Error('Browser provider not available');
                }
                
                const signer = await browserProviderRef.current.getSigner();
                const contract = new ethers.Contract(contractAddress, abi, signer);
                
                return await executeContractFunction(contract, functionName, params, options);
            }
        } catch (error) {
            console.error('❌ 交易执行失败:', error);
            setError(error.message);
            throw error;
        }
    }, [isConnected, connectForSigning]);

    // 执行合约函数
    const executeContractFunction = useCallback(async (contract, functionName, params, options) => {
        try {
            console.log(`🔄 执行合约函数: ${functionName}`);
            console.log('📝 参数:', params);
            
            // 估算Gas
            let gasEstimate;
            try {
                gasEstimate = await contract[functionName].estimateGas(...params);
                console.log('⛽ Gas估算:', gasEstimate.toString());
            } catch (gasError) {
                console.warn('⚠️  Gas估算失败，使用默认值:', gasError.message);
                gasEstimate = BigInt(500000); // 默认Gas限制
            }
            
            // 增加20%的Gas缓冲
            const gasLimit = gasEstimate * 120n / 100n;
            
            // 获取当前Gas价格
            const feeData = await contract.runner.provider.getFeeData();
            const gasPrice = feeData.gasPrice;
            
            console.log('⛽ Gas限制:', gasLimit.toString());
            console.log('💰 Gas价格:', ethers.formatUnits(gasPrice, 'gwei'), 'Gwei');
            
            // 合并交易选项
            const txOptions = {
                gasLimit,
                gasPrice,
                ...options
            };
            
            // 执行交易
            const tx = await contract[functionName](...params, txOptions);
            
            console.log('📤 交易已发送:', tx.hash);
            
            return {
                hash: tx.hash,
                wait: (confirmations = 1) => tx.wait(confirmations),
                tx
            };
        } catch (error) {
            console.error('❌ 合约函数执行失败:', error);
            
            // 解析错误信息
            let errorMessage = error.message;
            if (error.reason) {
                errorMessage = error.reason;
            } else if (error.data && error.data.message) {
                errorMessage = error.data.message;
            }
            
            throw new Error(`交易失败: ${errorMessage}`);
        }
    }, []);

    // 查询合约数据（只读）
    const queryContract = useCallback(async (contractAddress, abi, functionName, params = []) => {
        try {
            const provider = getReadOnlyProvider();
            const contract = new ethers.Contract(contractAddress, abi, provider);
            
            console.log(`🔍 查询合约函数: ${functionName}`);
            const result = await contract[functionName](...params);
            
            return result;
        } catch (error) {
            console.error('❌ 合约查询失败:', error);
            throw new Error(`查询失败: ${error.message}`);
        }
    }, [getReadOnlyProvider]);

    // 获取交易状态
    const getTransactionStatus = useCallback(async (txHash) => {
        try {
            const provider = getReadOnlyProvider();
            
            // 获取交易信息
            const tx = await provider.getTransaction(txHash);
            if (!tx) {
                return { status: 'not_found', tx: null, receipt: null };
            }
            
            // 获取交易收据
            const receipt = await provider.getTransactionReceipt(txHash);
            if (!receipt) {
                return { status: 'pending', tx, receipt: null };
            }
            
            const status = receipt.status === 1 ? 'success' : 'failed';
            return { status, tx, receipt };
        } catch (error) {
            console.error('❌ 获取交易状态失败:', error);
            throw error;
        }
    }, [getReadOnlyProvider]);

    // 断开连接
    const disconnect = useCallback(() => {
        setAccount('');
        setIsConnected(false);
        setChainId(null);
        setBalance('0');
        setError(null);
        
        // 清理provider引用
        browserProviderRef.current = null;
        
        console.log('🔌 钱包已断开连接');
    }, []);

    // 刷新账户信息
    const refreshAccount = useCallback(async () => {
        if (!isConnected || !browserProviderRef.current) {
            return;
        }
        
        try {
            const signer = await browserProviderRef.current.getSigner();
            const address = await signer.getAddress();
            const balanceWei = await browserProviderRef.current.getBalance(address);
            const balanceEth = ethers.formatEther(balanceWei);
            
            setAccount(address);
            setBalance(balanceEth);
        } catch (error) {
            console.error('❌ 刷新账户信息失败:', error);
        }
    }, [isConnected]);

    // 监听账户和网络变化
    useEffect(() => {
        if (typeof window === 'undefined' || !window.ethereum) {
            return;
        }

        const handleAccountsChanged = (accounts) => {
            console.log('👤 账户变化:', accounts);
            if (accounts.length === 0) {
                disconnect();
            } else if (accounts[0] !== account) {
                setAccount(accounts[0]);
                refreshAccount();
            }
        };

        const handleChainChanged = (newChainId) => {
            console.log('🔗 网络变化:', newChainId);
            setChainId(newChainId);
            
            // 检查是否为目标网络
            const targetNetwork = getTargetNetwork();
            if (newChainId !== targetNetwork.chainId) {
                setError(`请切换到${targetNetwork.chainName}`);
            } else {
                setError(null);
            }
        };

        const handleDisconnect = () => {
            console.log('🔌 钱包断开连接');
            disconnect();
        };

        // 添加事件监听器
        window.ethereum.on('accountsChanged', handleAccountsChanged);
        window.ethereum.on('chainChanged', handleChainChanged);
        window.ethereum.on('disconnect', handleDisconnect);

        // 清理函数
        return () => {
            if (window.ethereum && window.ethereum.removeListener) {
                window.ethereum.removeListener('accountsChanged', handleAccountsChanged);
                window.ethereum.removeListener('chainChanged', handleChainChanged);
                window.ethereum.removeListener('disconnect', handleDisconnect);
            }
        };
    }, [account, disconnect, refreshAccount, getTargetNetwork]);

    // 检查是否已连接
    useEffect(() => {
        const checkConnection = async () => {
            if (typeof window === 'undefined' || !window.ethereum) {
                return;
            }

            try {
                const accounts = await window.ethereum.request({ 
                    method: 'eth_accounts' 
                });
                
                if (accounts.length > 0) {
                    const chainId = await window.ethereum.request({ 
                        method: 'eth_chainId' 
                    });
                    
                    setAccount(accounts[0]);
                    setChainId(chainId);
                    setIsConnected(true);
                    
                    // 刷新余额
                    refreshAccount();
                }
            } catch (error) {
                console.error('❌ 检查连接状态失败:', error);
            }
        };

        checkConnection();
    }, [refreshAccount]);

    return {
        // 状态
        account,
        isConnected,
        chainId,
        balance,
        isConnecting,
        error,
        
        // 方法
        connectForSigning,
        connectReadOnly,
        executeTransaction,
        queryContract,
        getTransactionStatus,
        disconnect,
        refreshAccount,
        
        // 工具方法
        getReadOnlyProvider,
        checkAndSwitchNetwork,
        
        // 格式化方法
        formatAddress: (address) => address ? `${address.slice(0, 6)}...${address.slice(-4)}` : '',
        formatBalance: (balance) => parseFloat(balance).toFixed(4),
        isCorrectNetwork: () => {
            const targetNetwork = getTargetNetwork();
            return chainId === targetNetwork.chainId;
        }
    };
};
