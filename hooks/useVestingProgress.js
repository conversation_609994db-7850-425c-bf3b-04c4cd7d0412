import { useState, useEffect, useCallback } from 'react';
import { ethers } from 'ethers';

/**
 * HAOX解锁进度Hook
 * 监控解锁轮次、价格条件和7天维持期
 */
export const useVestingProgress = () => {
    const [unlockProgress, setUnlockProgress] = useState(null);
    const [allRounds, setAllRounds] = useState([]);
    const [statistics, setStatistics] = useState(null);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState(null);

    // 合约配置
    const VESTING_CONTRACT_ADDRESS = process.env.NEXT_PUBLIC_HAOX_VESTING_ADDRESS_V2_FIXED;
    const provider = new ethers.JsonRpcProvider(process.env.NEXT_PUBLIC_BSC_RPC_URL);
    
    // 合约ABI（简化版）
    const vestingABI = [
        'function getUnlockProgress() view returns (uint256, uint256, uint256, bool, uint256, uint256)',
        'function getRoundInfo(uint256) view returns (uint256, bool, bool, uint256, uint256, uint256, uint256)',
        'function getUnlockStatistics() view returns (uint256, uint256, uint256, uint256, uint256)',
        'function currentRound() view returns (uint256)',
        'function TOTAL_ROUNDS() view returns (uint256)',
        'function PRICE_MAINTENANCE_PERIOD() view returns (uint256)',
        'function checkPriceCondition()',
        'event PriceConditionMet(uint256 indexed roundNumber, uint256 price, uint256 timestamp)',
        'event RoundUnlocked(uint256 indexed roundNumber, uint256 triggerPrice, uint256 projectTokens, uint256 communityTokens, uint256 timestamp)',
        'event PriceConditionReset(uint256 indexed roundNumber, uint256 price, uint256 timestamp)'
    ];

    const vestingContract = new ethers.Contract(VESTING_CONTRACT_ADDRESS, vestingABI, provider);

    // 获取解锁进度
    const fetchUnlockProgress = useCallback(async () => {
        try {
            const progress = await vestingContract.getUnlockProgress();
            
            return {
                currentPrice: parseFloat(ethers.formatUnits(progress[0], 8)),
                nextRoundNumber: Number(progress[1]),
                nextRoundTriggerPrice: parseFloat(ethers.formatUnits(progress[2], 8)),
                priceConditionMet: progress[3],
                timeRemaining: Number(progress[4]),
                priceReachedTime: Number(progress[5])
            };
        } catch (error) {
            console.error('Failed to fetch unlock progress:', error);
            throw error;
        }
    }, [vestingContract]);

    // 获取所有轮次信息
    const fetchAllRounds = useCallback(async () => {
        try {
            const totalRounds = await vestingContract.TOTAL_ROUNDS();
            const rounds = [];
            
            for (let i = 1; i <= Number(totalRounds); i++) {
                const roundInfo = await vestingContract.getRoundInfo(i);
                rounds.push({
                    roundNumber: i,
                    triggerPrice: parseFloat(ethers.formatUnits(roundInfo[0], 8)),
                    priceConditionMet: roundInfo[1],
                    unlocked: roundInfo[2],
                    priceReachedTime: Number(roundInfo[3]),
                    unlockTime: Number(roundInfo[4]),
                    projectTokens: parseFloat(ethers.formatEther(roundInfo[5])),
                    communityTokens: parseFloat(ethers.formatEther(roundInfo[6]))
                });
            }
            
            return rounds;
        } catch (error) {
            console.error('Failed to fetch rounds:', error);
            throw error;
        }
    }, [vestingContract]);

    // 获取解锁统计
    const fetchStatistics = useCallback(async () => {
        try {
            const stats = await vestingContract.getUnlockStatistics();
            
            return {
                totalUnlockedRounds: Number(stats[0]),
                totalUnlockedTokens: parseFloat(ethers.formatEther(stats[1])),
                totalProjectTokens: parseFloat(ethers.formatEther(stats[2])),
                totalCommunityTokens: parseFloat(ethers.formatEther(stats[3])),
                remainingTokens: parseFloat(ethers.formatEther(stats[4]))
            };
        } catch (error) {
            console.error('Failed to fetch statistics:', error);
            throw error;
        }
    }, [vestingContract]);

    // 计算价格进度百分比
    const calculatePriceProgress = useCallback((currentPrice, targetPrice) => {
        if (!currentPrice || !targetPrice) return 0;
        return Math.min((currentPrice / targetPrice) * 100, 100);
    }, []);

    // 格式化剩余时间
    const formatTimeRemaining = useCallback((seconds) => {
        if (seconds <= 0) return '已完成';
        
        const days = Math.floor(seconds / 86400);
        const hours = Math.floor((seconds % 86400) / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        
        if (days > 0) {
            return `${days}天 ${hours}小时 ${minutes}分钟`;
        } else if (hours > 0) {
            return `${hours}小时 ${minutes}分钟`;
        } else {
            return `${minutes}分钟`;
        }
    }, []);

    // 获取轮次状态
    const getRoundStatus = useCallback((round, currentRound) => {
        if (round.unlocked) return 'completed';
        if (round.roundNumber === currentRound + 1) {
            if (round.priceConditionMet) return 'waiting';
            return 'active';
        }
        if (round.roundNumber <= currentRound) return 'completed';
        return 'pending';
    }, []);

    // 获取下一个里程碑
    const getNextMilestone = useCallback((rounds, currentRound) => {
        const milestones = [5, 10, 15, 20, 25, 31]; // 重要的轮次里程碑
        
        for (const milestone of milestones) {
            if (milestone > currentRound) {
                const round = rounds.find(r => r.roundNumber === milestone);
                if (round) {
                    return {
                        roundNumber: milestone,
                        triggerPrice: round.triggerPrice,
                        tokensToUnlock: (milestone - currentRound) * 150, // 每轮1.5亿
                        description: milestone === 31 ? '最终解锁' : `第${milestone}轮里程碑`
                    };
                }
            }
        }
        
        return null;
    }, []);

    // 更新所有数据
    const updateData = useCallback(async () => {
        setIsLoading(true);
        setError(null);
        
        try {
            const [progress, rounds, stats] = await Promise.all([
                fetchUnlockProgress(),
                fetchAllRounds(),
                fetchStatistics()
            ]);
            
            setUnlockProgress(progress);
            setAllRounds(rounds);
            setStatistics(stats);
            
        } catch (error) {
            console.error('Failed to update vesting data:', error);
            setError(error.message);
        } finally {
            setIsLoading(false);
        }
    }, [fetchUnlockProgress, fetchAllRounds, fetchStatistics]);

    // 监听合约事件
    useEffect(() => {
        if (!vestingContract) return;
        
        const handlePriceConditionMet = (roundNumber, price, timestamp) => {
            console.log(`Price condition met for round ${roundNumber}: $${ethers.formatUnits(price, 8)}`);
            updateData(); // 刷新数据
        };
        
        const handleRoundUnlocked = (roundNumber, triggerPrice, projectTokens, communityTokens, timestamp) => {
            console.log(`Round ${roundNumber} unlocked!`);
            updateData(); // 刷新数据
        };
        
        const handlePriceConditionReset = (roundNumber, price, timestamp) => {
            console.log(`Price condition reset for round ${roundNumber}: $${ethers.formatUnits(price, 8)}`);
            updateData(); // 刷新数据
        };
        
        // 监听事件
        vestingContract.on('PriceConditionMet', handlePriceConditionMet);
        vestingContract.on('RoundUnlocked', handleRoundUnlocked);
        vestingContract.on('PriceConditionReset', handlePriceConditionReset);
        
        return () => {
            vestingContract.off('PriceConditionMet', handlePriceConditionMet);
            vestingContract.off('RoundUnlocked', handleRoundUnlocked);
            vestingContract.off('PriceConditionReset', handlePriceConditionReset);
        };
    }, [vestingContract, updateData]);

    // 定期更新数据
    useEffect(() => {
        updateData();
        const interval = setInterval(updateData, 60000); // 每分钟更新
        return () => clearInterval(interval);
    }, [updateData]);

    // 计算衍生数据
    const derivedData = {
        // 价格进度
        priceProgress: unlockProgress ? calculatePriceProgress(
            unlockProgress.currentPrice, 
            unlockProgress.nextRoundTriggerPrice
        ) : 0,
        
        // 格式化时间
        formattedTimeRemaining: unlockProgress ? formatTimeRemaining(unlockProgress.timeRemaining) : '',
        
        // 轮次状态
        roundsWithStatus: allRounds.map(round => ({
            ...round,
            status: getRoundStatus(round, statistics?.totalUnlockedRounds || 0)
        })),
        
        // 下一个里程碑
        nextMilestone: allRounds.length > 0 && statistics ? 
            getNextMilestone(allRounds, statistics.totalUnlockedRounds) : null,
        
        // 总体进度
        overallProgress: statistics ? {
            completedRounds: statistics.totalUnlockedRounds,
            totalRounds: 31,
            completionPercentage: (statistics.totalUnlockedRounds / 31) * 100,
            unlockedPercentage: (statistics.totalUnlockedTokens / 5000) * 100 // 总共50亿代币
        } : null
    };

    return {
        // 基础数据
        unlockProgress,
        allRounds,
        statistics,
        
        // 状态
        isLoading,
        error,
        
        // 衍生数据
        ...derivedData,
        
        // 方法
        refreshData: updateData,
        
        // 格式化方法
        formatPrice: (price) => `$${price?.toFixed(6) || '0.000000'}`,
        formatTokens: (tokens) => `${tokens?.toLocaleString() || '0'} HAOX`,
        formatPercentage: (percent) => `${percent?.toFixed(1) || '0.0'}%`
    };
};
