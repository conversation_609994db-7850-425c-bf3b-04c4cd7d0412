import { useState, useEffect, useCallback } from 'react';
import { ethers } from 'ethers';

/**
 * HAOX价格预言机Hook
 * 集成Chainlink BNB/USD价格预言机和Binance API
 * 实现故障转移和价格验证机制
 */
export const usePriceOracle = () => {
    const [currentPrice, setCurrentPrice] = useState(0);
    const [bnbPrice, setBnbPrice] = useState(0);
    const [isLoading, setIsLoading] = useState(true);
    const [lastUpdated, setLastUpdated] = useState(Date.now());
    const [priceHistory, setPriceHistory] = useState([]);
    const [error, setError] = useState(null);
    const [dataSource, setDataSource] = useState('chainlink');

    // BSC测试网Chainlink BNB/USD价格预言机
    const CHAINLINK_BNB_USD = "******************************************";
    
    // 创建provider和合约实例
    const provider = new ethers.JsonRpcProvider(process.env.NEXT_PUBLIC_BSC_RPC_URL);
    const chainlinkContract = new ethers.Contract(
        CHAINLINK_BNB_USD,
        ['function latestRoundData() view returns (uint80, int256, uint256, uint256, uint80)'],
        provider
    );

    // 获取Chainlink BNB价格
    const fetchChainlinkPrice = useCallback(async () => {
        try {
            const roundData = await chainlinkContract.latestRoundData();
            const price = parseFloat(ethers.formatUnits(roundData[1], 8));
            const timestamp = Number(roundData[3]) * 1000;
            
            // 检查价格数据是否过期（超过1小时）
            const now = Date.now();
            if (now - timestamp > 60 * 60 * 1000) {
                throw new Error('Chainlink price data is stale');
            }
            
            return { price, timestamp, source: 'chainlink' };
        } catch (error) {
            console.error('Chainlink price fetch failed:', error);
            throw error;
        }
    }, [chainlinkContract]);

    // 获取Binance BNB价格
    const fetchBinancePrice = useCallback(async () => {
        try {
            const response = await fetch('https://api.binance.com/api/v3/ticker/price?symbol=BNBUSDT', {
                timeout: 5000
            });
            
            if (!response.ok) {
                throw new Error(`Binance API error: ${response.status}`);
            }
            
            const data = await response.json();
            const price = parseFloat(data.price);
            
            if (isNaN(price) || price <= 0) {
                throw new Error('Invalid price data from Binance');
            }
            
            return { price, timestamp: Date.now(), source: 'binance' };
        } catch (error) {
            console.error('Binance price fetch failed:', error);
            throw error;
        }
    }, []);

    // 获取BNB价格（带故障转移）
    const fetchBNBPrice = useCallback(async () => {
        let chainlinkPrice = null;
        let binancePrice = null;
        
        // 并行获取两个数据源
        try {
            [chainlinkPrice, binancePrice] = await Promise.allSettled([
                fetchChainlinkPrice(),
                fetchBinancePrice()
            ]);
        } catch (error) {
            console.error('Failed to fetch prices:', error);
        }
        
        // 处理Chainlink结果
        const chainlinkData = chainlinkPrice?.status === 'fulfilled' ? chainlinkPrice.value : null;
        const binanceData = binancePrice?.status === 'fulfilled' ? binancePrice.value : null;
        
        // 如果两个数据源都可用，进行价格验证
        if (chainlinkData && binanceData) {
            const priceDiff = Math.abs(chainlinkData.price - binanceData.price) / chainlinkData.price;
            
            if (priceDiff > 0.05) { // 5%差异阈值
                console.warn('Price deviation detected:', {
                    chainlink: chainlinkData.price,
                    binance: binanceData.price,
                    deviation: `${(priceDiff * 100).toFixed(2)}%`
                });
                
                // 使用更保守的价格（较低的价格）
                const finalPrice = Math.min(chainlinkData.price, binanceData.price);
                setDataSource('conservative');
                return finalPrice;
            }
            
            // 使用加权平均（Chainlink权重更高）
            const weightedPrice = (chainlinkData.price * 0.7 + binanceData.price * 0.3);
            setDataSource('weighted');
            return weightedPrice;
        }
        
        // 如果只有一个数据源可用
        if (chainlinkData) {
            setDataSource('chainlink');
            return chainlinkData.price;
        }
        
        if (binanceData) {
            setDataSource('binance');
            return binanceData.price;
        }
        
        // 如果都不可用，抛出错误
        throw new Error('All price sources failed');
    }, [fetchChainlinkPrice, fetchBinancePrice]);

    // 计算HAOX价格
    const calculateHAOXPrice = useCallback((bnbPrice) => {
        // 基于预售结束汇率计算
        const FINAL_PRESALE_RATE = 263111; // 1 BNB = 263,111 HAOX
        return bnbPrice / FINAL_PRESALE_RATE;
    }, []);

    // 更新价格数据
    const updatePrices = useCallback(async () => {
        setIsLoading(true);
        setError(null);
        
        try {
            const bnbPrice = await fetchBNBPrice();
            setBnbPrice(bnbPrice);
            
            const haoxPrice = calculateHAOXPrice(bnbPrice);
            setCurrentPrice(haoxPrice);
            
            // 更新价格历史
            const now = Date.now();
            setPriceHistory(prev => {
                const newHistory = [...prev, { 
                    timestamp: now, 
                    price: haoxPrice, 
                    bnbPrice,
                    source: dataSource
                }];
                // 保留最近24小时的数据
                return newHistory.filter(item => now - item.timestamp < 24 * 60 * 60 * 1000);
            });
            
            setLastUpdated(now);
            
        } catch (error) {
            console.error('Price update failed:', error);
            setError(error.message);
        } finally {
            setIsLoading(false);
        }
    }, [fetchBNBPrice, calculateHAOXPrice, dataSource]);

    // 获取价格变化百分比
    const getPriceChange = useCallback(() => {
        if (priceHistory.length < 2) return { change: 0, percentage: 0 };
        
        const current = priceHistory[priceHistory.length - 1];
        const previous = priceHistory[priceHistory.length - 2];
        
        const change = current.price - previous.price;
        const percentage = (change / previous.price) * 100;
        
        return { change, percentage };
    }, [priceHistory]);

    // 获取24小时价格统计
    const get24HourStats = useCallback(() => {
        if (priceHistory.length === 0) return null;
        
        const now = Date.now();
        const last24Hours = priceHistory.filter(item => now - item.timestamp <= 24 * 60 * 60 * 1000);
        
        if (last24Hours.length === 0) return null;
        
        const prices = last24Hours.map(item => item.price);
        const high = Math.max(...prices);
        const low = Math.min(...prices);
        const open = last24Hours[0].price;
        const close = last24Hours[last24Hours.length - 1].price;
        const change = close - open;
        const changePercent = (change / open) * 100;
        
        return {
            high,
            low,
            open,
            close,
            change,
            changePercent,
            volume: last24Hours.length // 数据点数量作为活跃度指标
        };
    }, [priceHistory]);

    // 定期更新价格
    useEffect(() => {
        updatePrices();
        const interval = setInterval(updatePrices, 30000); // 每30秒更新
        return () => clearInterval(interval);
    }, [updatePrices]);

    // 监听网络状态变化
    useEffect(() => {
        const handleOnline = () => {
            console.log('Network back online, refreshing prices...');
            updatePrices();
        };
        
        const handleOffline = () => {
            console.log('Network offline');
            setError('网络连接断开');
        };
        
        window.addEventListener('online', handleOnline);
        window.addEventListener('offline', handleOffline);
        
        return () => {
            window.removeEventListener('online', handleOnline);
            window.removeEventListener('offline', handleOffline);
        };
    }, [updatePrices]);

    return {
        // 价格数据
        currentPrice,
        bnbPrice,
        priceHistory,
        
        // 状态
        isLoading,
        error,
        lastUpdated,
        dataSource,
        
        // 统计数据
        priceChange: getPriceChange(),
        stats24h: get24HourStats(),
        
        // 方法
        refreshPrice: updatePrices,
        
        // 格式化方法
        formatPrice: (price) => `$${price?.toFixed(8) || '0.00000000'}`,
        formatBNBPrice: (price) => `$${price?.toFixed(2) || '0.00'}`,
        formatPercentage: (percent) => `${percent >= 0 ? '+' : ''}${percent?.toFixed(2) || '0.00'}%`
    };
};
