import { useState, useEffect, useCallback } from 'react';
import { useWalletAuth } from './useWalletAuth';

interface InvitationStats {
  total_invitations: number;
  successful_invitations: number;
  presale_conversions: number;
  total_rewards: number;
  last_invitation_at: string | null;
}

interface Milestone {
  current: number;
  next: number | null;
  progress: number;
}

interface Reward {
  id: string;
  reward_type: string;
  reward_amount: number;
  reward_description: string;
  status: string;
  created_at: string;
  distributed_at: string | null;
}

interface InvitationHistory {
  id: string;
  invitee_id: string;
  invitee_username: string;
  status: string;
  created_at: string;
  accepted_at: string | null;
}

interface InvitationData {
  invitation_code: string | null;
  invitation_link: string | null;
  stats: InvitationStats;
  milestones: Milestone;
  rewards: Reward[];
  invitation_history: InvitationHistory[];
}

export function useInvitation() {
  const { user, isAuthenticated } = useWalletAuth();
  const [invitationData, setInvitationData] = useState<InvitationData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 生成邀请码
  const generateInvitationCode = useCallback(async () => {
    if (!user?.id) {
      setError('User not authenticated');
      return null;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/invitation/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId: user.id }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to generate invitation code');
      }

      return data.invitation_code;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setError(errorMessage);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [user?.id]);

  // 获取邀请统计
  const fetchInvitationStats = useCallback(async () => {
    if (!user?.id) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/invitation/stats?userId=${user.id}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch invitation stats');
      }

      setInvitationData(data.data);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [user?.id]);

  // 接受邀请
  const acceptInvitation = useCallback(async (invitationCode: string) => {
    if (!user?.id) {
      setError('User not authenticated');
      return false;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/invitation/accept', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          invitationCode,
          inviteeId: user.id,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to accept invitation');
      }

      // 刷新统计数据
      await fetchInvitationStats();
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setError(errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [user?.id, fetchInvitationStats]);

  // 复制邀请链接
  const copyInvitationLink = useCallback(async () => {
    if (!invitationData?.invitation_link) {
      setError('No invitation link available');
      return false;
    }

    try {
      await navigator.clipboard.writeText(invitationData.invitation_link);
      return true;
    } catch (error) {
      setError('Failed to copy invitation link');
      return false;
    }
  }, [invitationData?.invitation_link]);

  // 分享到社交平台
  const shareToSocial = useCallback((platform: 'twitter' | 'telegram' | 'whatsapp') => {
    if (!invitationData?.invitation_link) {
      setError('No invitation link available');
      return;
    }

    const text = `加入 SocioMint，体验社交化的加密货币交易！使用我的邀请链接注册可获得额外奖励：`;
    const url = invitationData.invitation_link;

    let shareUrl = '';

    switch (platform) {
      case 'twitter':
        shareUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`;
        break;
      case 'telegram':
        shareUrl = `https://t.me/share/url?url=${encodeURIComponent(url)}&text=${encodeURIComponent(text)}`;
        break;
      case 'whatsapp':
        shareUrl = `https://wa.me/?text=${encodeURIComponent(text + ' ' + url)}`;
        break;
    }

    if (shareUrl) {
      window.open(shareUrl, '_blank', 'width=600,height=400');
    }
  }, [invitationData?.invitation_link]);

  // 获取奖励里程碑信息
  const getMilestoneInfo = useCallback(() => {
    if (!invitationData) return null;

    const milestones = [
      { count: 1, reward: 50, description: '首次邀请奖励' },
      { count: 5, reward: 300, description: '邀请达人' },
      { count: 10, reward: 800, description: '推广专家' },
      { count: 25, reward: 2000, description: '社区建设者' },
      { count: 50, reward: 5000, description: 'VIP商家资格' },
      { count: 100, reward: 10000, description: '超级推广者' },
    ];

    const current = invitationData.stats.successful_invitations;
    const completed = milestones.filter(m => m.count <= current);
    const next = milestones.find(m => m.count > current);

    return {
      completed,
      next,
      progress: next ? (current / next.count) * 100 : 100,
    };
  }, [invitationData]);

  // 初始化时获取数据
  useEffect(() => {
    if (isAuthenticated && user?.id) {
      fetchInvitationStats();
    }
  }, [isAuthenticated, user?.id, fetchInvitationStats]);

  return {
    invitationData,
    isLoading,
    error,
    generateInvitationCode,
    fetchInvitationStats,
    acceptInvitation,
    copyInvitationLink,
    shareToSocial,
    getMilestoneInfo,
    // 便捷访问器
    invitationCode: invitationData?.invitation_code,
    invitationLink: invitationData?.invitation_link,
    stats: invitationData?.stats,
    milestones: invitationData?.milestones,
    rewards: invitationData?.rewards || [],
    invitationHistory: invitationData?.invitation_history || [],
  };
}
