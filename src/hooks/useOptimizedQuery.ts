'use client';

import { useState, useEffect, useRef, useCallback, useMemo } from 'react';

interface QueryOptions<T> {
  enabled?: boolean;
  staleTime?: number;
  cacheTime?: number;
  refetchOnWindowFocus?: boolean;
  refetchOnReconnect?: boolean;
  retry?: number | boolean;
  retryDelay?: number;
  onSuccess?: (data: T) => void;
  onError?: (error: Error) => void;
  select?: (data: any) => T;
  placeholderData?: T;
}

interface QueryResult<T> {
  data: T | undefined;
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  isStale: boolean;
  refetch: () => Promise<void>;
  invalidate: () => void;
}

// 全局缓存
const queryCache = new Map<string, {
  data: any;
  timestamp: number;
  promise?: Promise<any>;
}>();

// 请求去重
const ongoingRequests = new Map<string, Promise<any>>();

/**
 * 高性能数据获取 Hook
 * 特性：
 * - 智能缓存和去重
 * - 自动重试和错误处理
 * - 后台更新
 * - 乐观更新支持
 */
export function useOptimizedQuery<T = any>(
  queryKey: string | string[],
  queryFn: () => Promise<T>,
  options: QueryOptions<T> = {}
): QueryResult<T> {
  const {
    enabled = true,
    staleTime = 5 * 60 * 1000, // 5 minutes
    cacheTime = 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus = true,
    refetchOnReconnect = true,
    retry = 3,
    retryDelay = 1000,
    onSuccess,
    onError,
    select,
    placeholderData,
  } = options;

  const key = Array.isArray(queryKey) ? queryKey.join(':') : queryKey;
  const [data, setData] = useState<T | undefined>(placeholderData);
  const [isLoading, setIsLoading] = useState(false);
  const [isError, setIsError] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [isStale, setIsStale] = useState(false);

  const retryCountRef = useRef(0);
  const mountedRef = useRef(true);
  const lastFetchRef = useRef<number>(0);

  // 检查缓存是否新鲜
  const isCacheFresh = useCallback((timestamp: number) => {
    return Date.now() - timestamp < staleTime;
  }, [staleTime]);

  // 检查缓存是否有效
  const isCacheValid = useCallback((timestamp: number) => {
    return Date.now() - timestamp < cacheTime;
  }, [cacheTime]);

  // 执行查询
  const executeQuery = useCallback(async (isBackground = false) => {
    if (!enabled) return;

    const cached = queryCache.get(key);
    
    // 如果有新鲜的缓存，直接使用
    if (cached && isCacheFresh(cached.timestamp)) {
      const result = select ? select(cached.data) : cached.data;
      setData(result);
      setIsStale(false);
      return;
    }

    // 如果有进行中的请求，等待它完成
    const ongoingRequest = ongoingRequests.get(key);
    if (ongoingRequest) {
      try {
        const result = await ongoingRequest;
        if (mountedRef.current) {
          const finalResult = select ? select(result) : result;
          setData(finalResult);
          setIsStale(false);
        }
        return;
      } catch (err) {
        // 继续执行新的请求
      }
    }

    if (!isBackground) {
      setIsLoading(true);
    }
    setIsError(false);
    setError(null);

    const fetchPromise = queryFn();
    ongoingRequests.set(key, fetchPromise);

    try {
      const result = await fetchPromise;
      
      if (mountedRef.current) {
        const finalResult = select ? select(result) : result;
        setData(finalResult);
        setIsLoading(false);
        setIsStale(false);
        
        // 更新缓存
        queryCache.set(key, {
          data: result,
          timestamp: Date.now(),
        });

        lastFetchRef.current = Date.now();
        retryCountRef.current = 0;
        onSuccess?.(finalResult);
      }
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error');
      
      if (mountedRef.current) {
        setIsLoading(false);
        setIsError(true);
        setError(error);
        onError?.(error);

        // 重试逻辑
        if (retry && retryCountRef.current < (typeof retry === 'number' ? retry : 3)) {
          retryCountRef.current++;
          setTimeout(() => {
            if (mountedRef.current) {
              executeQuery(isBackground);
            }
          }, retryDelay * retryCountRef.current);
        }
      }
    } finally {
      ongoingRequests.delete(key);
    }
  }, [key, enabled, queryFn, select, onSuccess, onError, retry, retryDelay, isCacheFresh]);

  // 手动重新获取
  const refetch = useCallback(async () => {
    queryCache.delete(key);
    await executeQuery();
  }, [key, executeQuery]);

  // 使缓存失效
  const invalidate = useCallback(() => {
    queryCache.delete(key);
    setIsStale(true);
  }, [key]);

  // 初始化和缓存检查
  useEffect(() => {
    if (!enabled) return;

    const cached = queryCache.get(key);
    
    if (cached) {
      if (isCacheValid(cached.timestamp)) {
        const result = select ? select(cached.data) : cached.data;
        setData(result);
        setIsStale(!isCacheFresh(cached.timestamp));
        
        // 如果缓存过期但仍有效，后台更新
        if (!isCacheFresh(cached.timestamp)) {
          executeQuery(true);
        }
      } else {
        // 缓存无效，删除并重新获取
        queryCache.delete(key);
        executeQuery();
      }
    } else {
      executeQuery();
    }
  }, [key, enabled, executeQuery, select, isCacheValid, isCacheFresh]);

  // 窗口焦点重新获取
  useEffect(() => {
    if (!refetchOnWindowFocus) return;

    const handleFocus = () => {
      const timeSinceLastFetch = Date.now() - lastFetchRef.current;
      if (timeSinceLastFetch > staleTime) {
        executeQuery(true);
      }
    };

    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, [refetchOnWindowFocus, staleTime, executeQuery]);

  // 网络重连重新获取
  useEffect(() => {
    if (!refetchOnReconnect) return;

    const handleOnline = () => {
      executeQuery(true);
    };

    window.addEventListener('online', handleOnline);
    return () => window.removeEventListener('online', handleOnline);
  }, [refetchOnReconnect, executeQuery]);

  // 清理
  useEffect(() => {
    return () => {
      mountedRef.current = false;
    };
  }, []);

  return useMemo(() => ({
    data,
    isLoading,
    isError,
    error,
    isStale,
    refetch,
    invalidate,
  }), [data, isLoading, isError, error, isStale, refetch, invalidate]);
}

/**
 * 批量查询 Hook
 */
export function useOptimizedQueries<T = any>(
  queries: Array<{
    queryKey: string | string[];
    queryFn: () => Promise<T>;
    options?: QueryOptions<T>;
  }>
) {
  const results = queries.map(({ queryKey, queryFn, options }) =>
    useOptimizedQuery(queryKey, queryFn, options)
  );

  return useMemo(() => ({
    results,
    isLoading: results.some(r => r.isLoading),
    isError: results.some(r => r.isError),
    data: results.map(r => r.data),
  }), [results]);
}

/**
 * 无限查询 Hook
 */
export function useInfiniteQuery<T = any>(
  queryKey: string | string[],
  queryFn: (pageParam: number) => Promise<{ data: T[]; nextPage?: number }>,
  options: QueryOptions<{ pages: { data: T[]; nextPage?: number }[] }> = {}
) {
  const [pages, setPages] = useState<{ data: T[]; nextPage?: number }[]>([]);
  const [hasNextPage, setHasNextPage] = useState(true);
  const [isFetchingNextPage, setIsFetchingNextPage] = useState(false);

  const baseQuery = useOptimizedQuery(
    Array.isArray(queryKey) ? [...queryKey, 'page-0'] : `${queryKey}:page-0`,
    () => queryFn(0),
    {
      ...options,
      onSuccess: (data: any) => {
        setPages([data]);
        setHasNextPage(!!data.nextPage);
        if (options.onSuccess) {
          options.onSuccess({ pages: [data] } as any);
        }
      },
    }
  );

  const fetchNextPage = useCallback(async () => {
    if (!hasNextPage || isFetchingNextPage) return;

    setIsFetchingNextPage(true);
    try {
      const nextPageNum = pages.length;
      const nextData = await queryFn(nextPageNum);
      
      setPages(prev => [...prev, nextData]);
      setHasNextPage(!!nextData.nextPage);
    } catch (error) {
      console.error('Failed to fetch next page:', error);
    } finally {
      setIsFetchingNextPage(false);
    }
  }, [hasNextPage, isFetchingNextPage, pages.length, queryFn]);

  const data = useMemo(() => 
    pages.flatMap(page => page.data), 
    [pages]
  );

  return {
    ...baseQuery,
    data,
    pages,
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
  };
}
