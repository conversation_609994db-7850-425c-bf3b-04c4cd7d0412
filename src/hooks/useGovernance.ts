import { useState, useEffect, useCallback } from 'react';
import { useWalletAuth } from './useWalletAuth';

interface ProposalType {
  id: string;
  type_name: string;
  display_name: string;
  description: string;
  min_voting_power: number;
  min_proposal_stake: number;
  voting_duration: number;
  quorum_threshold: number;
  approval_threshold: number;
}

interface Proposal {
  id: string;
  title: string;
  description: string;
  status: string;
  start_time: string;
  end_time: string;
  execution_time?: string;
  votes_for: number;
  votes_against: number;
  votes_abstain: number;
  total_votes: number;
  voter_count: number;
  approval_rate: number;
  stake_amount: number;
  tags: string[];
  created_at: string;
  proposal_type: ProposalType;
  proposer: {
    id: string;
    username: string;
  };
  is_active: boolean;
  time_remaining: number;
  can_vote: boolean;
}

interface Vote {
  id: string;
  vote_choice: string;
  voting_power: number;
  reason?: string;
  voted_at: string;
  proposal: {
    id: string;
    title: string;
    status: string;
    end_time: string;
  };
}

interface GovernanceStats {
  proposals_created: number;
  votes_cast: number;
  voting_power_used: number;
  total_rewards: number;
  participation_rate: number;
}

export function useGovernance() {
  const { user, isAuthenticated } = useWalletAuth();
  const [proposals, setProposals] = useState<Proposal[]>([]);
  const [proposalTypes, setProposalTypes] = useState<ProposalType[]>([]);
  const [userVotes, setUserVotes] = useState<Vote[]>([]);
  const [userStats, setUserStats] = useState<GovernanceStats | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    total_pages: 0,
    has_next: false,
    has_prev: false
  });

  // 获取提案列表
  const fetchProposals = useCallback(async (filters: {
    status?: string;
    type?: string;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: string;
  } = {}) => {
    setIsLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        page: (filters.page || pagination.page).toString(),
        limit: (filters.limit || pagination.limit).toString(),
        sortBy: filters.sortBy || 'created_at',
        sortOrder: filters.sortOrder || 'desc',
        ...(filters.status && { status: filters.status }),
        ...(filters.type && { type: filters.type })
      });

      const response = await fetch(`/api/governance/proposals?${params}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch proposals');
      }

      setProposals(data.data.proposals);
      setPagination(data.data.pagination);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [pagination.page, pagination.limit]);

  // 获取提案类型
  const fetchProposalTypes = useCallback(async () => {
    try {
      const response = await fetch('/api/governance/proposal-types');
      const data = await response.json();

      if (response.ok) {
        setProposalTypes(data.data.proposal_types || []);
      }
    } catch (error) {
      console.error('Error fetching proposal types:', error);
    }
  }, []);

  // 创建提案
  const createProposal = useCallback(async (proposalData: {
    proposalTypeId: string;
    title: string;
    description: string;
    content?: any;
    executionData?: any;
    stakeAmount: number;
    discussionUrl?: string;
    tags?: string[];
  }) => {
    if (!user?.id) {
      setError('User not authenticated');
      return false;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/governance/proposals', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          proposerId: user.id,
          ...proposalData,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create proposal');
      }

      // 刷新提案列表
      await fetchProposals();
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setError(errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [user?.id, fetchProposals]);

  // 投票
  const castVote = useCallback(async (
    proposalId: string,
    voteChoice: 'for' | 'against' | 'abstain',
    reason?: string
  ) => {
    if (!user?.id) {
      setError('User not authenticated');
      return false;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/governance/vote', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          proposalId,
          voterId: user.id,
          voteChoice,
          reason,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to cast vote');
      }

      // 刷新提案列表和用户投票记录
      await Promise.all([
        fetchProposals(),
        fetchUserVotes()
      ]);
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setError(errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [user?.id, fetchProposals]);

  // 获取用户投票记录
  const fetchUserVotes = useCallback(async () => {
    if (!user?.id) return;

    try {
      const response = await fetch(`/api/governance/vote?userId=${user.id}`);
      const data = await response.json();

      if (response.ok) {
        setUserVotes(data.data.votes);
        setUserStats(data.data.stats);
      }
    } catch (error) {
      console.error('Error fetching user votes:', error);
    }
  }, [user?.id]);

  // 检查用户是否已对特定提案投票
  const hasUserVoted = useCallback((proposalId: string) => {
    return userVotes.some(vote => vote.proposal.id === proposalId);
  }, [userVotes]);

  // 获取用户对特定提案的投票
  const getUserVote = useCallback((proposalId: string) => {
    return userVotes.find(vote => vote.proposal.id === proposalId);
  }, [userVotes]);

  // 计算治理统计
  const getGovernanceStatistics = useCallback(() => {
    const activeProposals = proposals.filter(p => p.is_active);
    const passedProposals = proposals.filter(p => p.status === 'passed');
    const totalVotingPower = proposals.reduce((sum, p) => sum + p.total_votes, 0);

    return {
      total_proposals: proposals.length,
      active_proposals: activeProposals.length,
      passed_proposals: passedProposals.length,
      total_voting_power: totalVotingPower,
      avg_participation: proposals.length > 0 ? 
        proposals.reduce((sum, p) => sum + p.voter_count, 0) / proposals.length : 0,
      user_participation_rate: userStats?.participation_rate || 0
    };
  }, [proposals, userStats]);

  // 初始化时获取数据
  useEffect(() => {
    fetchProposalTypes();
  }, [fetchProposalTypes]);

  useEffect(() => {
    if (isAuthenticated) {
      fetchProposals();
      fetchUserVotes();
    }
  }, [isAuthenticated, fetchProposals, fetchUserVotes]);

  return {
    // 数据
    proposals,
    proposalTypes,
    userVotes,
    userStats,
    pagination,
    
    // 状态
    isLoading,
    error,
    
    // 方法
    fetchProposals,
    fetchProposalTypes,
    createProposal,
    castVote,
    fetchUserVotes,
    hasUserVoted,
    getUserVote,
    
    // 计算属性
    governanceStatistics: getGovernanceStatistics(),
    
    // 便捷过滤器
    activeProposals: proposals.filter(p => p.is_active),
    passedProposals: proposals.filter(p => p.status === 'passed'),
    rejectedProposals: proposals.filter(p => p.status === 'rejected'),
    userProposals: proposals.filter(p => p.proposer.id === user?.id),
  };
}
