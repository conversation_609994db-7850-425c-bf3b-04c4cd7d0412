/**
 * 钱包认证 Hook
 * 处理钱包连接、认证和用户状态管理
 */

import { useState, useEffect, useCallback } from 'react';
import { useTelegramAuth } from './useTelegramAuth';
import { useToast } from '@/components/ui';

export interface WalletAuthState {
  isConnected: boolean;
  address: string | null;
  isAuthenticated: boolean;
  user: {
    id: string;
    telegramId?: number;
    username?: string;
    walletAddress?: string;
    role: 'user' | 'merchant' | 'admin';
    isVerified: boolean;
  } | null;
}

export interface UseWalletAuthReturn {
  // 状态
  walletAuth: WalletAuthState;
  isLoading: boolean;
  error: string | null;
  
  // 方法
  connectWallet: () => Promise<void>;
  disconnectWallet: () => Promise<void>;
  authenticateUser: () => Promise<void>;
  refreshAuth: () => Promise<void>;
  
  // 便捷方法
  isConnected: boolean;
  isAuthenticated: boolean;
  userRole: 'user' | 'merchant' | 'admin';
  canAccessMerchantFeatures: boolean;
}

export const useWalletAuth = (): UseWalletAuthReturn => {
  const { isAuthenticated: telegramAuth, user: telegramUser } = useTelegramAuth();
  const { addToast } = useToast();

  const [walletAuth, setWalletAuth] = useState<WalletAuthState>({
    isConnected: false,
    address: null,
    isAuthenticated: false,
    user: null,
  });

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 初始化认证状态
  useEffect(() => {
    if (telegramAuth && telegramUser) {
      setWalletAuth(prev => ({
        ...prev,
        isAuthenticated: true,
        user: {
          id: telegramUser.id.toString(),
          telegramId: telegramUser.id,
          username: telegramUser.username,
          walletAddress: telegramUser.walletAddress,
          role: telegramUser.role || 'user',
          isVerified: telegramUser.isVerified || false,
        },
      }));
    }
  }, [telegramAuth, telegramUser]);

  // 连接钱包（模拟）
  const connectWallet = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      // 在实际应用中，这里会连接真实的钱包
      // 目前使用 Telegram 认证作为钱包认证
      if (!telegramAuth) {
        throw new Error('请先完成 Telegram 认证');
      }

      // 模拟钱包连接
      const mockAddress = `0x${Math.random().toString(16).substr(2, 40)}`;
      
      setWalletAuth(prev => ({
        ...prev,
        isConnected: true,
        address: mockAddress,
      }));

      addToast({
        type: 'success',
        title: '钱包连接成功',
        message: '您的钱包已成功连接',
      });

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '钱包连接失败';
      setError(errorMessage);
      
      addToast({
        type: 'error',
        title: '连接失败',
        message: errorMessage,
      });
    } finally {
      setIsLoading(false);
    }
  }, [telegramAuth, addToast]);

  // 断开钱包连接
  const disconnectWallet = useCallback(async () => {
    setIsLoading(true);

    try {
      setWalletAuth(prev => ({
        ...prev,
        isConnected: false,
        address: null,
      }));

      addToast({
        type: 'info',
        title: '钱包已断开',
        message: '您的钱包连接已断开',
      });

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '断开连接失败';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [addToast]);

  // 认证用户
  const authenticateUser = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      if (!telegramAuth || !telegramUser) {
        throw new Error('请先完成 Telegram 认证');
      }

      // 这里可以添加额外的认证逻辑
      // 例如验证钱包签名、检查用户权限等

      addToast({
        type: 'success',
        title: '认证成功',
        message: '用户认证已完成',
      });

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '用户认证失败';
      setError(errorMessage);
      
      addToast({
        type: 'error',
        title: '认证失败',
        message: errorMessage,
      });
    } finally {
      setIsLoading(false);
    }
  }, [telegramAuth, telegramUser, addToast]);

  // 刷新认证状态
  const refreshAuth = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      // 重新获取用户信息和认证状态
      // 在实际应用中，这里会调用 API 获取最新状态
      
      if (telegramUser) {
        setWalletAuth(prev => ({
          ...prev,
          user: {
            ...prev.user!,
            role: telegramUser.role || 'user',
            isVerified: telegramUser.isVerified || false,
          },
        }));
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '刷新认证状态失败';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [telegramUser]);

  // 便捷属性
  const isConnected = walletAuth.isConnected;
  const isAuthenticated = walletAuth.isAuthenticated;
  const userRole = walletAuth.user?.role || 'user';
  const canAccessMerchantFeatures = userRole === 'merchant' || userRole === 'admin';

  return {
    // 状态
    walletAuth,
    isLoading,
    error,
    
    // 方法
    connectWallet,
    disconnectWallet,
    authenticateUser,
    refreshAuth,
    
    // 便捷属性
    isConnected,
    isAuthenticated,
    userRole,
    canAccessMerchantFeatures,
  };
};

export default useWalletAuth;
