'use client';

import { useState, useEffect } from 'react';
import { useToast } from '@/components/ui';
import { supabase } from '@/lib/supabase';
import type { SocialAccount } from '@/types';

export const useSocialAccounts = (userId?: string) => {
  const [accounts, setAccounts] = useState<SocialAccount[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isConnecting, setIsConnecting] = useState<string | null>(null);
  const { addToast } = useToast();

  // Fetch social accounts
  const fetchAccounts = async () => {
    if (!userId) return;

    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from('social_accounts')
        .select('*')
        .eq('user_id', userId);

      if (error) throw error;
      setAccounts(data || []);
    } catch (error) {
      console.error('Failed to fetch social accounts:', error);
      addToast({
        type: 'error',
        title: '获取社交账户失败',
        message: '无法加载您的社交账户信息',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Connect Twitter account
  const connectTwitter = async () => {
    if (!userId) {
      addToast({
        type: 'error',
        title: '连接失败',
        message: '请先登录您的账户',
      });
      return;
    }

    setIsConnecting('twitter');
    try {
      // In a real implementation, this would redirect to Twitter OAuth
      // For now, we'll simulate the connection process
      
      // Simulate OAuth redirect
      const twitterAuthUrl = `https://api.twitter.com/oauth/authorize?oauth_token=mock_token&oauth_callback=${encodeURIComponent(window.location.origin + '/auth/twitter/callback')}`;
      
      // For demo purposes, we'll simulate a successful connection
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Mock Twitter user data
      const mockTwitterData = {
        platform_user_id: 'twitter_' + Math.random().toString(36).substr(2, 9),
        platform_username: 'user_' + Math.random().toString(36).substr(2, 6),
      };

      const { error } = await supabase
        .from('social_accounts')
        .insert({
          user_id: userId,
          platform: 'twitter',
          platform_user_id: mockTwitterData.platform_user_id,
          platform_username: mockTwitterData.platform_username,
          is_verified: true,
        });

      if (error) throw error;

      addToast({
        type: 'success',
        title: 'Twitter 连接成功',
        message: `已成功连接 @${mockTwitterData.platform_username}`,
      });

      fetchAccounts();
    } catch (error) {
      console.error('Twitter connection failed:', error);
      addToast({
        type: 'error',
        title: 'Twitter 连接失败',
        message: '连接 Twitter 账户时出现错误',
      });
    } finally {
      setIsConnecting(null);
    }
  };

  // Connect Discord account
  const connectDiscord = async () => {
    if (!userId) {
      addToast({
        type: 'error',
        title: '连接失败',
        message: '请先登录您的账户',
      });
      return;
    }

    setIsConnecting('discord');
    try {
      // Simulate Discord OAuth process
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const mockDiscordData = {
        platform_user_id: 'discord_' + Math.random().toString(36).substr(2, 9),
        platform_username: 'User#' + Math.floor(Math.random() * 9999).toString().padStart(4, '0'),
      };

      const { error } = await supabase
        .from('social_accounts')
        .insert({
          user_id: userId,
          platform: 'discord',
          platform_user_id: mockDiscordData.platform_user_id,
          platform_username: mockDiscordData.platform_username,
          is_verified: true,
        });

      if (error) throw error;

      addToast({
        type: 'success',
        title: 'Discord 连接成功',
        message: `已成功连接 ${mockDiscordData.platform_username}`,
      });

      fetchAccounts();
    } catch (error) {
      console.error('Discord connection failed:', error);
      addToast({
        type: 'error',
        title: 'Discord 连接失败',
        message: '连接 Discord 账户时出现错误',
      });
    } finally {
      setIsConnecting(null);
    }
  };

  // Connect Telegram account
  const connectTelegram = async () => {
    if (!userId) {
      addToast({
        type: 'error',
        title: '连接失败',
        message: '请先登录您的账户',
      });
      return;
    }

    setIsConnecting('telegram');
    try {
      // Simulate Telegram OAuth process
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const mockTelegramData = {
        platform_user_id: 'telegram_' + Math.random().toString(36).substr(2, 9),
        platform_username: '@user_' + Math.random().toString(36).substr(2, 6),
      };

      const { error } = await supabase
        .from('social_accounts')
        .insert({
          user_id: userId,
          platform: 'telegram',
          platform_user_id: mockTelegramData.platform_user_id,
          platform_username: mockTelegramData.platform_username,
          is_verified: true,
        });

      if (error) throw error;

      addToast({
        type: 'success',
        title: 'Telegram 连接成功',
        message: `已成功连接 ${mockTelegramData.platform_username}`,
      });

      fetchAccounts();
    } catch (error) {
      console.error('Telegram connection failed:', error);
      addToast({
        type: 'error',
        title: 'Telegram 连接失败',
        message: '连接 Telegram 账户时出现错误',
      });
    } finally {
      setIsConnecting(null);
    }
  };

  // Disconnect social account
  const disconnectAccount = async (accountId: string, platform: string) => {
    try {
      const { error } = await supabase
        .from('social_accounts')
        .delete()
        .eq('id', accountId);

      if (error) throw error;

      addToast({
        type: 'info',
        title: '账户已断开',
        message: `已成功断开 ${platform} 账户连接`,
      });

      fetchAccounts();
    } catch (error) {
      console.error('Failed to disconnect account:', error);
      addToast({
        type: 'error',
        title: '断开连接失败',
        message: '断开社交账户连接时出现错误',
      });
    }
  };

  // Get account by platform
  const getAccountByPlatform = (platform: 'twitter' | 'discord' | 'telegram') => {
    return accounts.find(account => account.platform === platform);
  };

  // Check if platform is connected
  const isConnected = (platform: 'twitter' | 'discord' | 'telegram') => {
    return accounts.some(account => account.platform === platform && account.is_verified);
  };

  // Get connection status
  const getConnectionStatus = () => {
    return {
      twitter: isConnected('twitter'),
      discord: isConnected('discord'),
      telegram: isConnected('telegram'),
      total: accounts.filter(account => account.is_verified).length,
    };
  };

  // Load accounts on mount
  useEffect(() => {
    if (userId) {
      fetchAccounts();
    }
  }, [userId]);

  return {
    accounts,
    isLoading,
    isConnecting,
    
    // Connection methods
    connectTwitter,
    connectDiscord,
    connectTelegram,
    disconnectAccount,
    
    // Utility methods
    getAccountByPlatform,
    isConnected,
    getConnectionStatus,
    refetch: fetchAccounts,
  };
};
