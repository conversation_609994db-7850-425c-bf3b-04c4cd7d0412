import { useState, useEffect, useCallback } from 'react';
import { ethers } from 'ethers';
import { useWallet } from './useWallet';
import { useWalletAuth } from './useWalletAuth';
import { createSafeContractConfig, safeCreateContract } from '@/lib/contract-utils';

// Contract ABIs (simplified for demo - in production, import from generated types)
const HAOX_TOKEN_ABI = [
  "function balanceOf(address owner) view returns (uint256)",
  "function totalSupply() view returns (uint256)",
  "function getAllocationInfo() view returns (uint256, uint256, uint256, uint256, uint256, uint256, uint256)",
  "function getRemainingAllocations() view returns (uint256, uint256, uint256)",
  "event Transfer(address indexed from, address indexed to, uint256 value)",
  "event TokensMinted(address indexed to, uint256 amount, string purpose)"
];

const HAOX_PRESALE_ABI = [
  "function buyTokens() payable",
  "function getCurrentRate() view returns (uint256)",
  "function calculateTokenAmount(uint256 bnbAmount) view returns (uint256)",
  "function getPresaleInfo() view returns (bool, uint256, uint256, uint256, uint256, uint256, uint256, uint256, uint256)",
  "function getUserInfo(address user) view returns (bool, uint256, uint256, uint256, uint256)",
  "function whitelist(address user) view returns (bool)",
  "event TokensPurchased(address indexed buyer, uint256 bnbAmount, uint256 tokenAmount, uint256 currentRate, uint256 stage)"
];

const HAOX_INVITATION_ABI = [
  "function getUserStats(address user) view returns (uint256, uint256, uint256, uint256, bool, bool, bool, uint256)",
  "function claimRewards()",
  "function pendingRewards(address user) view returns (uint256)",
  "function getInvitationTree(address user) view returns (address[])",
  "function getMonthlyLeaderboard(uint256 month, uint256 limit) view returns (address[], uint256[], uint256[])",
  "event RewardClaimed(address indexed user, uint256 amount)",
  "event InvitationRecorded(address indexed inviter, address indexed invitee, uint256 investmentAmount, uint256 timestamp)"
];

const HAOX_PRICE_ORACLE_ABI = [
  "function getCurrentPrice() view returns (uint256, uint256, bool)",
  "function getTWAP(uint256 period) view returns (uint256)",
  "function getOracleHealth() view returns (bool, uint256, uint256, uint256, uint256)",
  "event PriceUpdated(uint256 indexed timestamp, uint256 chainlinkPrice, uint256 pancakeswapPrice, uint256 aggregatedPrice, uint256 confidence)"
];

const HAOX_VESTING_ABI = [
  "function getVestingStatus() view returns (uint256, uint256, uint256, uint256, uint256, bool, uint256, uint256)",
  "function validatePriceStability() view returns (bool, uint256)",
  "function getUnlockHistory(uint256 limit) view returns (uint256[], uint256[], uint256[], uint256[])",
  "event UnlockTriggered(uint256 indexed cycle, uint256 amount, uint256 timestamp, uint256 triggerPrice)"
];

// Contract addresses (BSC Testnet - 2025-01-24 部署) - 使用安全配置
const CONTRACT_ADDRESSES = createSafeContractConfig();

interface TokenInfo {
  balance: string;
  totalSupply: string;
  maxSupply: string;
  totalMinted: string;
  presaleAllocation: string;
  presaleMinted: string;
  invitationAllocation: string;
  invitationMinted: string;
  vestingMinted: string;
}

interface PresaleInfo {
  active: boolean;
  startTime: number;
  endTime: number;
  currentStage: number;
  currentRate: string;
  totalRaised: string;
  totalSold: string;
  tokensRemaining: string;
  stageTokensRemaining: string;
}

interface UserPresaleInfo {
  isWhitelisted: boolean;
  bnbInvested: string;
  tokensOwned: string;
  maxInvestment: string;
  remainingInvestment: string;
}

interface InvitationStats {
  totalInvitations: number;
  successfulInvitations: number;
  totalRewards: string;
  unclaimedRewards: string;
  milestone5Claimed: boolean;
  milestone10Claimed: boolean;
  milestone30Claimed: boolean;
  currentMonthInvitations: number;
}

interface PriceInfo {
  price: string;
  timestamp: number;
  isValid: boolean;
  twap24h: string;
  oracleHealth: {
    isHealthy: boolean;
    lastUpdate: number;
    timeSinceUpdate: number;
    validSources: number;
    confidence: number;
  };
}

interface VestingInfo {
  totalSupply: string;
  totalUnlocked: string;
  remainingToUnlock: string;
  currentCycle: number;
  unlockTriggerPrice: string;
  stabilityActive: boolean;
  stabilityStartTime: number;
  stabilityDuration: number;
}

export function useHAOXContract() {
  const { provider, signer, isConnected, address } = useWallet();
  const { user } = useWalletAuth();
  
  const [tokenInfo, setTokenInfo] = useState<TokenInfo | null>(null);
  const [presaleInfo, setPresaleInfo] = useState<PresaleInfo | null>(null);
  const [userPresaleInfo, setUserPresaleInfo] = useState<UserPresaleInfo | null>(null);
  const [invitationStats, setInvitationStats] = useState<InvitationStats | null>(null);
  const [priceInfo, setPriceInfo] = useState<PriceInfo | null>(null);
  const [vestingInfo, setVestingInfo] = useState<VestingInfo | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Validate contract address
  // Validate contract address with strict checking
  const isValidAddress = (address: string): boolean => {
    return address && 
           address !== '' && 
           address !== '0x' && 
           address.length === 42 && 
           address.startsWith('0x') && 
           ethers.isAddress(address);
  };

  // Create contract instances
  const getContracts = useCallback(() => {
    if (!provider) return null;

    // Validate all contract addresses before creating instances
    if (!isValidAddress(CONTRACT_ADDRESSES.HAOX_TOKEN)) {
      console.warn('Invalid HAOX_TOKEN address:', CONTRACT_ADDRESSES.HAOX_TOKEN);
      return null;
    }

    try {
      return {
        token: safeCreateContract(CONTRACT_ADDRESSES.HAOX_TOKEN, HAOX_TOKEN_ABI, provider),
        presale: safeCreateContract(CONTRACT_ADDRESSES.HAOX_PRESALE, HAOX_PRESALE_ABI, provider),
        invitation: safeCreateContract(CONTRACT_ADDRESSES.HAOX_INVITATION, HAOX_INVITATION_ABI, provider),
        priceOracle: safeCreateContract(CONTRACT_ADDRESSES.HAOX_PRICE_ORACLE, HAOX_PRICE_ORACLE_ABI, provider),
        vesting: safeCreateContract(CONTRACT_ADDRESSES.HAOX_VESTING, HAOX_VESTING_ABI, provider),
      };
    } catch (error) {
      console.error('Error creating contract instances:', error);
      return null;
    }
  }, [provider]);

  // Fetch token information
  const fetchTokenInfo = useCallback(async () => {
    const contracts = getContracts();
    if (!contracts?.token || !address) return;

    try {
      const [balance, totalSupply, allocationInfo] = await Promise.all([
        contracts.token.balanceOf(address),
        contracts.token.totalSupply(),
        contracts.token.getAllocationInfo(),
      ]);

      setTokenInfo({
        balance: ethers.formatEther(balance),
        totalSupply: ethers.formatEther(totalSupply),
        maxSupply: ethers.formatEther(allocationInfo[0]),
        totalMinted: ethers.formatEther(allocationInfo[1]),
        presaleAllocation: ethers.formatEther(allocationInfo[2]),
        presaleMinted: ethers.formatEther(allocationInfo[3]),
        invitationAllocation: ethers.formatEther(allocationInfo[4]),
        invitationMinted: ethers.formatEther(allocationInfo[5]),
        vestingMinted: ethers.formatEther(allocationInfo[6]),
      });
    } catch (error) {
      console.error('Error fetching token info:', error);
      setError('Failed to fetch token information');
    }
  }, [getContracts, address]);

  // Fetch presale information
  const fetchPresaleInfo = useCallback(async () => {
    const contracts = getContracts();
    if (!contracts) return;

    try {
      const presaleData = await contracts.presale.getPresaleInfo();
      
      setPresaleInfo({
        active: presaleData[0],
        startTime: Number(presaleData[1]),
        endTime: Number(presaleData[2]),
        currentStage: Number(presaleData[3]),
        currentRate: presaleData[4].toString(),
        totalRaised: ethers.formatEther(presaleData[5]),
        totalSold: ethers.formatEther(presaleData[6]),
        tokensRemaining: ethers.formatEther(presaleData[7]),
        stageTokensRemaining: ethers.formatEther(presaleData[8]),
      });

      // Fetch user-specific presale info if connected
      if (address) {
        const userData = await contracts.presale.getUserInfo(address);
        setUserPresaleInfo({
          isWhitelisted: userData[0],
          bnbInvested: ethers.formatEther(userData[1]),
          tokensOwned: ethers.formatEther(userData[2]),
          maxInvestment: ethers.formatEther(userData[3]),
          remainingInvestment: ethers.formatEther(userData[4]),
        });
      }
    } catch (error) {
      console.error('Error fetching presale info:', error);
    }
  }, [getContracts, address]);

  // Fetch invitation statistics
  const fetchInvitationStats = useCallback(async () => {
    const contracts = getContracts();
    if (!contracts || !address) return;

    try {
      const stats = await contracts.invitation.getUserStats(address);
      
      setInvitationStats({
        totalInvitations: Number(stats[0]),
        successfulInvitations: Number(stats[1]),
        totalRewards: ethers.formatEther(stats[2]),
        unclaimedRewards: ethers.formatEther(stats[3]),
        milestone5Claimed: stats[4],
        milestone10Claimed: stats[5],
        milestone30Claimed: stats[6],
        currentMonthInvitations: Number(stats[7]),
      });
    } catch (error) {
      console.error('Error fetching invitation stats:', error);
    }
  }, [getContracts, address]);

  // Fetch price information
  const fetchPriceInfo = useCallback(async () => {
    const contracts = getContracts();
    if (!contracts) return;

    try {
      const [currentPrice, twap24h, oracleHealth] = await Promise.all([
        contracts.priceOracle.getCurrentPrice(),
        contracts.priceOracle.getTWAP(24 * 60 * 60), // 24 hours
        contracts.priceOracle.getOracleHealth(),
      ]);

      setPriceInfo({
        price: ethers.formatUnits(currentPrice[0], 8), // Price has 8 decimals
        timestamp: Number(currentPrice[1]),
        isValid: currentPrice[2],
        twap24h: ethers.formatUnits(twap24h, 8),
        oracleHealth: {
          isHealthy: oracleHealth[0],
          lastUpdate: Number(oracleHealth[1]),
          timeSinceUpdate: Number(oracleHealth[2]),
          validSources: Number(oracleHealth[3]),
          confidence: Number(oracleHealth[4]),
        },
      });
    } catch (error) {
      console.error('Error fetching price info:', error);
    }
  }, [getContracts]);

  // Fetch vesting information
  const fetchVestingInfo = useCallback(async () => {
    const contracts = getContracts();
    if (!contracts) return;

    try {
      const vestingData = await contracts.vesting.getVestingStatus();
      
      setVestingInfo({
        totalSupply: ethers.formatEther(vestingData[0]),
        totalUnlocked: ethers.formatEther(vestingData[1]),
        remainingToUnlock: ethers.formatEther(vestingData[2]),
        currentCycle: Number(vestingData[3]),
        unlockTriggerPrice: ethers.formatUnits(vestingData[4], 8),
        stabilityActive: vestingData[5],
        stabilityStartTime: Number(vestingData[6]),
        stabilityDuration: Number(vestingData[7]),
      });
    } catch (error) {
      console.error('Error fetching vesting info:', error);
    }
  }, [getContracts]);

  // Buy tokens in presale
  const buyTokens = useCallback(async (bnbAmount: string) => {
    if (!signer) {
      setError('Wallet not connected');
      return false;
    }

    const contracts = getContracts();
    if (!contracts) {
      setError('Contracts not available');
      return false;
    }

    setIsLoading(true);
    setError(null);

    try {
      const presaleWithSigner = contracts.presale.connect(signer);
      const tx = await presaleWithSigner.buyTokens({
        value: ethers.parseEther(bnbAmount),
      });

      await tx.wait();
      
      // Refresh data after successful purchase
      await Promise.all([
        fetchTokenInfo(),
        fetchPresaleInfo(),
        fetchInvitationStats(),
      ]);

      return true;
    } catch (error: any) {
      const errorMessage = error.reason || error.message || 'Transaction failed';
      setError(errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [signer, getContracts, fetchTokenInfo, fetchPresaleInfo, fetchInvitationStats]);

  // Claim invitation rewards
  const claimInvitationRewards = useCallback(async () => {
    if (!signer) {
      setError('Wallet not connected');
      return false;
    }

    const contracts = getContracts();
    if (!contracts) {
      setError('Contracts not available');
      return false;
    }

    setIsLoading(true);
    setError(null);

    try {
      const invitationWithSigner = contracts.invitation.connect(signer);
      const tx = await invitationWithSigner.claimRewards();

      await tx.wait();
      
      // Refresh data after successful claim
      await Promise.all([
        fetchTokenInfo(),
        fetchInvitationStats(),
      ]);

      return true;
    } catch (error: any) {
      const errorMessage = error.reason || error.message || 'Transaction failed';
      setError(errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [signer, getContracts, fetchTokenInfo, fetchInvitationStats]);

  // Calculate token amount for BNB
  const calculateTokenAmount = useCallback(async (bnbAmount: string) => {
    const contracts = getContracts();
    if (!contracts || !bnbAmount) return '0';

    try {
      const tokenAmount = await contracts.presale.calculateTokenAmount(
        ethers.parseEther(bnbAmount)
      );
      return ethers.formatEther(tokenAmount);
    } catch (error) {
      console.error('Error calculating token amount:', error);
      return '0';
    }
  }, [getContracts]);

  // Fetch all data
  const fetchAllData = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      await Promise.all([
        fetchTokenInfo(),
        fetchPresaleInfo(),
        fetchInvitationStats(),
        fetchPriceInfo(),
        fetchVestingInfo(),
      ]);
    } catch (error) {
      console.error('Error fetching contract data:', error);
      setError('Failed to fetch contract data');
    } finally {
      setIsLoading(false);
    }
  }, [fetchTokenInfo, fetchPresaleInfo, fetchInvitationStats, fetchPriceInfo, fetchVestingInfo]);

  // Initialize data when wallet connects
  useEffect(() => {
    if (isConnected && provider) {
      fetchAllData();
    }
  }, [isConnected, provider, fetchAllData]);

  // Set up event listeners
  useEffect(() => {
    const contracts = getContracts();
    if (!contracts) return;

    const handleTokensPurchased = () => {
      fetchTokenInfo();
      fetchPresaleInfo();
    };

    const handleRewardClaimed = () => {
      fetchTokenInfo();
      fetchInvitationStats();
    };

    const handlePriceUpdated = () => {
      fetchPriceInfo();
    };

    // Add event listeners only for valid contracts
    const listeners: Array<() => void> = [];

    if (contracts.presale) {
      contracts.presale.on('TokensPurchased', handleTokensPurchased);
      listeners.push(() => contracts.presale?.off('TokensPurchased', handleTokensPurchased));
    }

    if (contracts.invitation) {
      contracts.invitation.on('RewardClaimed', handleRewardClaimed);
      listeners.push(() => contracts.invitation?.off('RewardClaimed', handleRewardClaimed));
    }

    if (contracts.priceOracle) {
      contracts.priceOracle.on('PriceUpdated', handlePriceUpdated);
      listeners.push(() => contracts.priceOracle?.off('PriceUpdated', handlePriceUpdated));
    }

    // Cleanup
    return () => {
      listeners.forEach(cleanup => cleanup());
    };
  }, [getContracts, fetchTokenInfo, fetchPresaleInfo, fetchInvitationStats, fetchPriceInfo]);

  // Format price with proper decimals
  const formatPrice = useCallback((price: string, decimals: number = 8) => {
    const num = parseFloat(price);
    if (num === 0) return '0.00';
    if (num < 0.001) return num.toExponential(2);
    return num.toFixed(decimals > 4 ? 4 : decimals);
  }, []);

  // Calculate presale progress
  const getPresaleProgress = useCallback(() => {
    if (!presaleInfo) return { percentage: 0, raised: '0', target: '300' };

    const raised = parseFloat(presaleInfo.totalRaised);
    const target = 300; // 300 BNB target
    const percentage = Math.min((raised / target) * 100, 100);

    return {
      percentage,
      raised: raised.toFixed(2),
      target: target.toString(),
    };
  }, [presaleInfo]);

  return {
    // Data
    tokenInfo,
    presaleInfo,
    userPresaleInfo,
    invitationStats,
    priceInfo,
    vestingInfo,

    // State
    isLoading,
    error,

    // Actions
    buyTokens,
    claimInvitationRewards,
    calculateTokenAmount,
    fetchAllData,

    // Utilities
    isContractsAvailable: !!getContracts(),
    contractAddresses: CONTRACT_ADDRESSES,
    formatPrice,
    getPresaleProgress,
  };
}
