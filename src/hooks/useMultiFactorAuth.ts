/**
 * 多重验证Hook
 * 管理多重验证流程和状态
 */

import { useState, useCallback } from 'react';
import { useTelegramAuth } from './useTelegramAuth';
import { 
  multiFactorAuthService, 
  VerificationMethod, 
  VerificationRequest 
} from '@/services/security/MultiFactorAuthService';
import { useToast } from './useToast';

interface UseMultiFactorAuthReturn {
  verificationMethods: VerificationMethod[];
  currentRequest: VerificationRequest | null;
  isLoading: boolean;
  error: string | null;
  requiresVerification: (action: string, data: Record<string, any>) => Promise<boolean>;
  startVerification: (action: string, data: Record<string, any>) => Promise<VerificationRequest>;
  sendCode: (method: 'telegram' | 'email' | 'sms') => Promise<boolean>;
  verifyCode: (method: 'telegram' | 'email' | 'sms', code: string) => Promise<boolean>;
  setupMethod: (method: 'email' | 'sms' | 'totp', data: Record<string, any>) => Promise<boolean>;
  loadMethods: () => Promise<void>;
}

export const useMultiFactorAuth = (): UseMultiFactorAuthReturn => {
  const { user } = useTelegramAuth();
  const { addToast } = useToast();
  
  const [verificationMethods, setVerificationMethods] = useState<VerificationMethod[]>([]);
  const [currentRequest, setCurrentRequest] = useState<VerificationRequest | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * 加载用户验证方法
   */
  const loadMethods = useCallback(async () => {
    if (!user?.id) return;

    try {
      setIsLoading(true);
      setError(null);

      const methods = await multiFactorAuthService.getUserVerificationMethods(user.id);
      setVerificationMethods(methods);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '加载验证方法失败';
      setError(errorMessage);
      console.error('Failed to load verification methods:', err);
    } finally {
      setIsLoading(false);
    }
  }, [user?.id]);

  /**
   * 检查是否需要多重验证
   */
  const requiresVerification = useCallback(async (
    action: string,
    data: Record<string, any>
  ): Promise<boolean> => {
    if (!user?.id) return false;

    try {
      const required = await multiFactorAuthService.requiresMultiFactorAuth(
        user.id,
        action as any,
        data
      );
      return required;
    } catch (err) {
      console.error('Failed to check verification requirement:', err);
      return true; // 出错时默认需要验证
    }
  }, [user?.id]);

  /**
   * 开始验证流程
   */
  const startVerification = useCallback(async (
    action: string,
    data: Record<string, any>
  ): Promise<VerificationRequest> => {
    if (!user?.id) {
      throw new Error('用户未登录');
    }

    try {
      setIsLoading(true);
      setError(null);

      const request = await multiFactorAuthService.createVerificationRequest(
        user.id,
        action as any,
        data
      );

      setCurrentRequest(request);
      
      addToast({
        type: 'info',
        title: '需要验证',
        message: '请完成多重验证以继续操作',
      });

      return request;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '创建验证请求失败';
      setError(errorMessage);
      
      addToast({
        type: 'error',
        title: '验证失败',
        message: errorMessage,
      });
      
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [user?.id, addToast]);

  /**
   * 发送验证码
   */
  const sendCode = useCallback(async (
    method: 'telegram' | 'email' | 'sms'
  ): Promise<boolean> => {
    if (!user?.id) {
      throw new Error('用户未登录');
    }

    try {
      setIsLoading(true);
      setError(null);

      const result = await multiFactorAuthService.sendVerificationCode(
        user.id,
        method,
        currentRequest?.id
      );

      if (result.success) {
        addToast({
          type: 'success',
          title: '验证码已发送',
          message: `验证码已通过${getMethodName(method)}发送`,
        });
        return true;
      } else {
        throw new Error(result.error || '发送验证码失败');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '发送验证码失败';
      setError(errorMessage);
      
      addToast({
        type: 'error',
        title: '发送失败',
        message: errorMessage,
      });
      
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [user?.id, currentRequest?.id, addToast]);

  /**
   * 验证验证码
   */
  const verifyCode = useCallback(async (
    method: 'telegram' | 'email' | 'sms',
    code: string
  ): Promise<boolean> => {
    if (!user?.id) {
      throw new Error('用户未登录');
    }

    try {
      setIsLoading(true);
      setError(null);

      const result = await multiFactorAuthService.verifyCode(
        user.id,
        method,
        code,
        currentRequest?.id
      );

      if (result.success) {
        // 更新当前请求状态
        if (currentRequest) {
          const updatedRequest = {
            ...currentRequest,
            completedMethods: [...currentRequest.completedMethods, method],
          };
          
          // 检查是否所有验证都完成
          const allCompleted = currentRequest.methods.every(m => 
            updatedRequest.completedMethods.includes(m)
          );
          
          if (allCompleted) {
            updatedRequest.isCompleted = true;
            setCurrentRequest(null);
            
            addToast({
              type: 'success',
              title: '验证完成',
              message: '所有验证步骤已完成',
            });
          } else {
            setCurrentRequest(updatedRequest);
            
            addToast({
              type: 'success',
              title: '验证成功',
              message: `${getMethodName(method)}验证成功`,
            });
          }
        }
        
        return true;
      } else {
        throw new Error(result.error || '验证码错误');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '验证失败';
      setError(errorMessage);
      
      addToast({
        type: 'error',
        title: '验证失败',
        message: errorMessage,
      });
      
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [user?.id, currentRequest, addToast]);

  /**
   * 设置验证方法
   */
  const setupMethod = useCallback(async (
    method: 'email' | 'sms' | 'totp',
    data: Record<string, any>
  ): Promise<boolean> => {
    if (!user?.id) {
      throw new Error('用户未登录');
    }

    try {
      setIsLoading(true);
      setError(null);

      const result = await multiFactorAuthService.setupVerificationMethod(
        user.id,
        method,
        data
      );

      if (result.success) {
        // 重新加载验证方法
        await loadMethods();
        
        addToast({
          type: 'success',
          title: '设置成功',
          message: `${getMethodName(method)}验证已设置`,
        });
        
        return true;
      } else {
        throw new Error(result.error || '设置验证方法失败');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '设置验证方法失败';
      setError(errorMessage);
      
      addToast({
        type: 'error',
        title: '设置失败',
        message: errorMessage,
      });
      
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [user?.id, loadMethods, addToast]);

  return {
    verificationMethods,
    currentRequest,
    isLoading,
    error,
    requiresVerification,
    startVerification,
    sendCode,
    verifyCode,
    setupMethod,
    loadMethods,
  };
};

/**
 * 验证码输入Hook
 */
export const useVerificationCode = (length: number = 6) => {
  const [code, setCode] = useState<string[]>(new Array(length).fill(''));
  const [isComplete, setIsComplete] = useState(false);

  const updateCode = useCallback((index: number, value: string) => {
    if (value.length > 1) {
      // 处理粘贴的情况
      const pastedCode = value.slice(0, length).split('');
      const newCode = [...code];
      
      for (let i = 0; i < pastedCode.length && index + i < length; i++) {
        newCode[index + i] = pastedCode[i];
      }
      
      setCode(newCode);
      setIsComplete(newCode.every(digit => digit !== ''));
    } else {
      const newCode = [...code];
      newCode[index] = value;
      setCode(newCode);
      setIsComplete(newCode.every(digit => digit !== ''));
    }
  }, [code, length]);

  const clearCode = useCallback(() => {
    setCode(new Array(length).fill(''));
    setIsComplete(false);
  }, [length]);

  const getCodeString = useCallback(() => {
    return code.join('');
  }, [code]);

  return {
    code,
    isComplete,
    updateCode,
    clearCode,
    getCodeString,
  };
};

/**
 * 获取验证方法名称
 */
function getMethodName(method: string): string {
  switch (method) {
    case 'telegram':
      return 'Telegram';
    case 'email':
      return '邮箱';
    case 'sms':
      return '短信';
    case 'totp':
      return '身份验证器';
    default:
      return method;
  }
}
