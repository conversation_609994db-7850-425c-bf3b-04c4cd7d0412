'use client';

import { useState } from 'react';
import { useToast } from '@/components/ui';

interface PaymentOrder {
  orderId: string;
  amount: number;
  currency: 'CNY' | 'USD';
  method: 'alipay' | 'wechat' | 'crypto';
  status: 'pending' | 'paid' | 'failed' | 'cancelled';
  createdAt: string;
  paidAt?: string;
  qrCode?: string;
  paymentUrl?: string;
}

interface CreateOrderParams {
  amount: number;
  currency: 'CNY' | 'USD';
  method: 'alipay' | 'wechat' | 'crypto';
  description?: string;
  userId?: string;
}

export const usePayment = () => {
  const [isCreatingOrder, setIsCreatingOrder] = useState(false);
  const [isCheckingStatus, setIsCheckingStatus] = useState(false);
  const [currentOrder, setCurrentOrder] = useState<PaymentOrder | null>(null);
  const { addToast } = useToast();

  // Create payment order
  const createOrder = async (params: CreateOrderParams): Promise<PaymentOrder | null> => {
    setIsCreatingOrder(true);
    try {
      // Simulate API call to create payment order
      await new Promise(resolve => setTimeout(resolve, 1500));

      const orderId = 'order_' + Math.random().toString(36).substr(2, 9);
      
      const order: PaymentOrder = {
        orderId,
        amount: params.amount,
        currency: params.currency,
        method: params.method,
        status: 'pending',
        createdAt: new Date().toISOString(),
      };

      // Generate mock payment data based on method
      if (params.method === 'alipay') {
        order.qrCode = `data:image/svg+xml;base64,${btoa(`
          <svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
            <rect width="200" height="200" fill="white"/>
            <text x="100" y="100" text-anchor="middle" font-family="Arial" font-size="12" fill="black">
              支付宝二维码
            </text>
            <text x="100" y="120" text-anchor="middle" font-family="Arial" font-size="10" fill="gray">
              订单号: ${orderId}
            </text>
          </svg>
        `)}`;
        order.paymentUrl = `alipays://platformapi/startapp?saId=10000007&qrcode=${encodeURIComponent('mock_qr_data')}`;
      } else if (params.method === 'wechat') {
        order.qrCode = `data:image/svg+xml;base64,${btoa(`
          <svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
            <rect width="200" height="200" fill="white"/>
            <text x="100" y="100" text-anchor="middle" font-family="Arial" font-size="12" fill="black">
              微信支付二维码
            </text>
            <text x="100" y="120" text-anchor="middle" font-family="Arial" font-size="10" fill="gray">
              订单号: ${orderId}
            </text>
          </svg>
        `)}`;
        order.paymentUrl = `weixin://wxpay/bizpayurl?pr=${encodeURIComponent('mock_payment_code')}`;
      }

      setCurrentOrder(order);
      
      addToast({
        type: 'info',
        title: '订单创建成功',
        message: '请使用相应的支付方式完成付款',
      });

      return order;
    } catch (error) {
      console.error('Failed to create payment order:', error);
      addToast({
        type: 'error',
        title: '创建订单失败',
        message: '创建支付订单时出现错误，请重试',
      });
      return null;
    } finally {
      setIsCreatingOrder(false);
    }
  };

  // Check payment status
  const checkPaymentStatus = async (orderId: string): Promise<PaymentOrder | null> => {
    setIsCheckingStatus(true);
    try {
      // Simulate API call to check payment status
      await new Promise(resolve => setTimeout(resolve, 1000));

      // For demo purposes, randomly determine if payment is successful
      const isSuccess = Math.random() > 0.3; // 70% success rate

      if (currentOrder && currentOrder.orderId === orderId) {
        const updatedOrder: PaymentOrder = {
          ...currentOrder,
          status: isSuccess ? 'paid' : 'pending',
          paidAt: isSuccess ? new Date().toISOString() : undefined,
        };

        setCurrentOrder(updatedOrder);

        if (isSuccess) {
          addToast({
            type: 'success',
            title: '支付成功',
            message: '您的支付已完成，交易正在处理中',
          });
        }

        return updatedOrder;
      }

      return null;
    } catch (error) {
      console.error('Failed to check payment status:', error);
      addToast({
        type: 'error',
        title: '查询支付状态失败',
        message: '无法查询支付状态，请重试',
      });
      return null;
    } finally {
      setIsCheckingStatus(false);
    }
  };

  // Cancel payment order
  const cancelOrder = async (orderId: string): Promise<boolean> => {
    try {
      // Simulate API call to cancel order
      await new Promise(resolve => setTimeout(resolve, 500));

      if (currentOrder && currentOrder.orderId === orderId) {
        const updatedOrder: PaymentOrder = {
          ...currentOrder,
          status: 'cancelled',
        };

        setCurrentOrder(updatedOrder);

        addToast({
          type: 'info',
          title: '订单已取消',
          message: '支付订单已取消',
        });
      }

      return true;
    } catch (error) {
      console.error('Failed to cancel order:', error);
      addToast({
        type: 'error',
        title: '取消订单失败',
        message: '取消支付订单时出现错误，请重试',
      });
      return false;
    }
  };

  // Get payment method info
  const getPaymentMethodInfo = (method: 'alipay' | 'wechat' | 'crypto') => {
    switch (method) {
      case 'alipay':
        return {
          name: '支付宝',
          icon: '💰',
          color: 'text-blue-600',
          description: '使用支付宝扫码支付',
          instructions: [
            '打开支付宝APP',
            '点击扫一扫',
            '扫描下方二维码',
            '确认支付金额',
            '完成支付',
          ],
        };
      case 'wechat':
        return {
          name: '微信支付',
          icon: '💚',
          color: 'text-green-600',
          description: '使用微信扫码支付',
          instructions: [
            '打开微信APP',
            '点击扫一扫',
            '扫描下方二维码',
            '确认支付金额',
            '完成支付',
          ],
        };
      case 'crypto':
        return {
          name: '加密货币',
          icon: '₿',
          color: 'text-orange-600',
          description: '使用加密货币支付',
          instructions: [
            '连接您的钱包',
            '确认交易详情',
            '签署交易',
            '等待区块确认',
            '完成支付',
          ],
        };
      default:
        return {
          name: '未知支付方式',
          icon: '❓',
          color: 'text-gray-600',
          description: '未知支付方式',
          instructions: [],
        };
    }
  };

  // Format amount for display
  const formatAmount = (amount: number, currency: 'CNY' | 'USD') => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency,
      minimumFractionDigits: 2,
    }).format(amount);
  };

  // Get order status info
  const getOrderStatusInfo = (status: PaymentOrder['status']) => {
    switch (status) {
      case 'pending':
        return {
          text: '待支付',
          color: 'text-system-orange',
          bgColor: 'bg-system-orange/10',
          borderColor: 'border-system-orange/20',
        };
      case 'paid':
        return {
          text: '已支付',
          color: 'text-system-green',
          bgColor: 'bg-system-green/10',
          borderColor: 'border-system-green/20',
        };
      case 'failed':
        return {
          text: '支付失败',
          color: 'text-system-red',
          bgColor: 'bg-system-red/10',
          borderColor: 'border-system-red/20',
        };
      case 'cancelled':
        return {
          text: '已取消',
          color: 'text-system-gray',
          bgColor: 'bg-system-gray/10',
          borderColor: 'border-system-gray/20',
        };
      default:
        return {
          text: '未知状态',
          color: 'text-secondary-label',
          bgColor: 'bg-system-gray-6',
          borderColor: 'border-system-gray-4',
        };
    }
  };

  // Clear current order
  const clearOrder = () => {
    setCurrentOrder(null);
  };

  return {
    // State
    isCreatingOrder,
    isCheckingStatus,
    currentOrder,

    // Actions
    createOrder,
    checkPaymentStatus,
    cancelOrder,
    clearOrder,

    // Utilities
    getPaymentMethodInfo,
    formatAmount,
    getOrderStatusInfo,
  };
};
