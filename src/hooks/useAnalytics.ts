'use client';

import { useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { analytics, track, identify, trackUserAction, trackBusinessEvent } from '@/lib/analytics';
import { monitoring } from '@/lib/monitoring';

interface AnalyticsEvent {
  name: string;
  properties?: Record<string, any>;
  timestamp?: number;
}

interface UserProperties {
  userId?: string;
  email?: string;
  walletAddress?: string;
  isMerchant?: boolean;
  registrationDate?: string;
}

interface UseAnalyticsReturn {
  track: (eventName: string, properties?: Record<string, any>) => void;
  identify: (userId: string, properties?: UserProperties) => void;
  page: (pageName: string, properties?: Record<string, any>) => void;
  setUserProperties: (properties: UserProperties) => void;
}

export const useAnalytics = (): UseAnalyticsReturn => {
  const router = useRouter();

  // Initialize analytics
  useEffect(() => {
    // Initialize analytics services
    if (typeof window !== 'undefined') {
      // Google Analytics 4
      if (process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID) {
        const script = document.createElement('script');
        script.src = `https://www.googletagmanager.com/gtag/js?id=${process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID}`;
        script.async = true;
        document.head.appendChild(script);

        (window as any).dataLayer = (window as any).dataLayer || [];
        const gtag = (...args: any[]) => {
          (window as any).dataLayer.push(args);
        };
        gtag('js', new Date());
        gtag('config', process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID, {
          page_title: document.title,
          page_location: window.location.href,
        });
      }

      // Cloudflare Web Analytics (可选)
      // if (process.env.NEXT_PUBLIC_CLOUDFLARE_ANALYTICS_TOKEN) {
      //   // Cloudflare Web Analytics 会自动初始化
      //   // 只需在 HTML 中添加脚本标签
      // }
    }
  }, []);

  // Track events
  const track = useCallback((eventName: string, properties?: Record<string, any>) => {
    const event: AnalyticsEvent = {
      name: eventName,
      properties: {
        ...properties,
        timestamp: Date.now(),
        url: window.location.href,
        referrer: document.referrer,
        userAgent: navigator.userAgent,
      },
    };

    // Google Analytics 4
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', eventName, {
        custom_map: properties,
        ...properties,
      });
    }

    // Cloudflare Web Analytics (可选)
    // Cloudflare Web Analytics 会自动跟踪页面浏览
    // 自定义事件需要通过其他方式实现

    // Custom analytics (send to your backend)
    if (process.env.NODE_ENV === 'production') {
      fetch('/api/analytics/track', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(event),
      }).catch(console.error);
    }

    // Development logging
    if (process.env.NODE_ENV === 'development') {
      console.log('[Analytics] Track:', event);
    }
  }, []);

  // Identify user
  const identify = useCallback((userId: string, properties?: UserProperties) => {
    // Google Analytics 4
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('config', process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID, {
        user_id: userId,
        custom_map: properties,
      });
    }

    // Custom analytics
    if (process.env.NODE_ENV === 'production') {
      fetch('/api/analytics/identify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          properties: {
            ...properties,
            timestamp: Date.now(),
          },
        }),
      }).catch(console.error);
    }

    // Development logging
    if (process.env.NODE_ENV === 'development') {
      console.log('[Analytics] Identify:', { userId, properties });
    }
  }, []);

  // Track page views
  const page = useCallback((pageName: string, properties?: Record<string, any>) => {
    // Google Analytics 4
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('config', process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID, {
        page_title: pageName,
        page_location: window.location.href,
        custom_map: properties,
      });
    }

    // Custom analytics
    track('page_view', {
      page_name: pageName,
      ...properties,
    });
  }, [track]);

  // Set user properties
  const setUserProperties = useCallback((properties: UserProperties) => {
    // Google Analytics 4
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('config', process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID, {
        custom_map: properties,
      });
    }

    // Development logging
    if (process.env.NODE_ENV === 'development') {
      console.log('[Analytics] Set User Properties:', properties);
    }
  }, []);

  return {
    track,
    identify,
    page,
    setUserProperties,
  };
};

// Pre-defined analytics events
export const analyticsEvents = {
  // Authentication events
  signUp: (method: string) => ({ name: 'sign_up', properties: { method } }),
  signIn: (method: string) => ({ name: 'sign_in', properties: { method } }),
  signOut: () => ({ name: 'sign_out' }),

  // Wallet events
  walletConnect: (walletType: string) => ({ 
    name: 'wallet_connect', 
    properties: { wallet_type: walletType } 
  }),
  walletDisconnect: () => ({ name: 'wallet_disconnect' }),

  // Transaction events
  transactionStart: (type: 'buy' | 'sell', amount: string) => ({
    name: 'transaction_start',
    properties: { transaction_type: type, amount }
  }),
  transactionComplete: (type: 'buy' | 'sell', amount: string, txHash: string) => ({
    name: 'transaction_complete',
    properties: { transaction_type: type, amount, tx_hash: txHash }
  }),
  transactionFail: (type: 'buy' | 'sell', amount: string, error: string) => ({
    name: 'transaction_fail',
    properties: { transaction_type: type, amount, error }
  }),

  // Social events
  socialConnect: (platform: string) => ({
    name: 'social_connect',
    properties: { platform }
  }),
  taskComplete: (taskId: string, reward: number) => ({
    name: 'task_complete',
    properties: { task_id: taskId, reward }
  }),

  // Merchant events
  merchantApply: () => ({ name: 'merchant_apply' }),
  merchantApproved: () => ({ name: 'merchant_approved' }),
  merchantRejected: (reason: string) => ({
    name: 'merchant_rejected',
    properties: { reason }
  }),

  // UI events
  buttonClick: (buttonName: string, location: string) => ({
    name: 'button_click',
    properties: { button_name: buttonName, location }
  }),
  modalOpen: (modalName: string) => ({
    name: 'modal_open',
    properties: { modal_name: modalName }
  }),
  modalClose: (modalName: string) => ({
    name: 'modal_close',
    properties: { modal_name: modalName }
  }),

  // Performance events
  pageLoadTime: (pageName: string, loadTime: number) => ({
    name: 'page_load_time',
    properties: { page_name: pageName, load_time: loadTime }
  }),
  apiResponseTime: (endpoint: string, responseTime: number) => ({
    name: 'api_response_time',
    properties: { endpoint, response_time: responseTime }
  }),
};

// Analytics hook for specific features
export const useFeatureAnalytics = (featureName: string) => {
  const { track } = useAnalytics();

  return {
    trackFeatureUsage: (action: string, properties?: Record<string, any>) => {
      track(`${featureName}_${action}`, {
        feature: featureName,
        ...properties,
      });
    },
    trackFeatureError: (error: string, properties?: Record<string, any>) => {
      track(`${featureName}_error`, {
        feature: featureName,
        error,
        ...properties,
      });
    },
  };
};
