import { useState, useEffect, useCallback } from 'react';
import { useWalletAuth } from './useWalletAuth';

interface TaskType {
  id: string;
  type_name: string;
  platform: string;
  display_name: string;
  description: string;
  base_reward: number;
  min_reward: number;
  max_reward: number;
  verification_method: string;
  required_fields: any;
}

interface Task {
  id: string;
  title: string;
  description: string;
  target_url: string;
  reward_amount: number;
  remaining_budget: number;
  max_completions?: number;
  current_completions: number;
  end_time?: string;
  status: string;
  priority: number;
  tags: string[];
  created_at: string;
  task_type: TaskType;
  publisher: {
    id: string;
    username: string;
  };
  completions_count: number;
  user_has_completed: boolean;
  user_completion_status?: string;
  can_complete: boolean;
}

interface TaskCompletion {
  id: string;
  verification_status: string;
  reward_amount: number;
  submitted_at: string;
}

interface TaskStats {
  tasks_published: number;
  tasks_completed: number;
  total_earned: number;
  total_spent: number;
  success_rate: number;
}

export function useTaskSystem() {
  const { user, isAuthenticated } = useWalletAuth();
  const [tasks, setTasks] = useState<Task[]>([]);
  const [taskTypes, setTaskTypes] = useState<TaskType[]>([]);
  const [groupedTaskTypes, setGroupedTaskTypes] = useState<Record<string, TaskType[]>>({});
  const [userStats, setUserStats] = useState<TaskStats | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    total_pages: 0,
    has_next: false,
    has_prev: false
  });

  // 获取任务类型
  const fetchTaskTypes = useCallback(async () => {
    try {
      const response = await fetch('/api/tasks/publish');
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch task types');
      }

      setTaskTypes(data.data.task_types);
      setGroupedTaskTypes(data.data.grouped_task_types);
    } catch (error) {
      console.error('Error fetching task types:', error);
    }
  }, []);

  // 获取任务列表
  const fetchTasks = useCallback(async (filters: {
    platform?: string;
    status?: string;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: string;
  } = {}) => {
    setIsLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        page: (filters.page || pagination.page).toString(),
        limit: (filters.limit || pagination.limit).toString(),
        sortBy: filters.sortBy || 'created_at',
        sortOrder: filters.sortOrder || 'desc',
        ...(user?.id && { userId: user.id }),
        ...(filters.platform && { platform: filters.platform }),
        ...(filters.status && { status: filters.status })
      });

      const response = await fetch(`/api/tasks?${params}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch tasks');
      }

      setTasks(data.data.tasks);
      setPagination(data.data.pagination);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [user?.id, pagination.page, pagination.limit]);

  // 发布任务
  const publishTask = useCallback(async (taskData: {
    taskTypeId: string;
    title: string;
    description: string;
    targetUrl: string;
    targetIdentifier?: string;
    rewardAmount: number;
    totalBudget: number;
    maxCompletions?: number;
    endTime?: string;
    verificationRequirements?: any;
    tags?: string[];
  }) => {
    if (!user?.id) {
      setError('User not authenticated');
      return false;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/tasks/publish', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          publisherId: user.id,
          ...taskData,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to publish task');
      }

      // 刷新任务列表
      await fetchTasks();
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setError(errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [user?.id, fetchTasks]);

  // 完成任务
  const completeTask = useCallback(async (taskId: string, submissionData: any) => {
    if (!user?.id) {
      setError('User not authenticated');
      return false;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/tasks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          taskId,
          userId: user.id,
          submissionData,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to complete task');
      }

      // 刷新任务列表
      await fetchTasks();
      return data.completion;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setError(errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [user?.id, fetchTasks]);

  // 获取用户统计
  const fetchUserStats = useCallback(async () => {
    if (!user?.id) return;

    try {
      const response = await fetch(`/api/tasks/stats?userId=${user.id}`);
      const data = await response.json();

      if (response.ok) {
        setUserStats(data.data);
      }
    } catch (error) {
      console.error('Error fetching user stats:', error);
    }
  }, [user?.id]);

  // 计算任务统计
  const getTaskStatistics = useCallback(() => {
    const availableTasks = tasks.filter(task => task.can_complete);
    const completedTasks = tasks.filter(task => task.user_has_completed);
    const totalRewards = completedTasks.reduce((sum, task) => sum + task.reward_amount, 0);

    return {
      available_tasks: availableTasks.length,
      completed_tasks: completedTasks.length,
      total_rewards: totalRewards,
      platforms: [...new Set(tasks.map(task => task.task_type.platform))],
      avg_reward: availableTasks.length > 0 ? 
        availableTasks.reduce((sum, task) => sum + task.reward_amount, 0) / availableTasks.length : 0
    };
  }, [tasks]);

  // 初始化时获取数据
  useEffect(() => {
    fetchTaskTypes();
  }, [fetchTaskTypes]);

  useEffect(() => {
    if (isAuthenticated) {
      fetchTasks();
      fetchUserStats();
    }
  }, [isAuthenticated, fetchTasks, fetchUserStats]);

  return {
    // 数据
    tasks,
    taskTypes,
    groupedTaskTypes,
    userStats,
    pagination,
    
    // 状态
    isLoading,
    error,
    
    // 方法
    fetchTasks,
    fetchTaskTypes,
    publishTask,
    completeTask,
    fetchUserStats,
    
    // 计算属性
    taskStatistics: getTaskStatistics(),
    
    // 便捷过滤器
    availableTasks: tasks.filter(task => task.can_complete),
    completedTasks: tasks.filter(task => task.user_has_completed),
    pendingTasks: tasks.filter(task => task.user_completion_status === 'pending'),
  };
}
