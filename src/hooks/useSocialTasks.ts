'use client';

import { useState, useEffect } from 'react';
import { useToast } from '@/components/ui';
import { supabase } from '@/lib/supabase';
import type { SocialTask, UserTaskCompletion } from '@/types';

export const useSocialTasks = (userId?: string) => {
  const [tasks, setTasks] = useState<SocialTask[]>([]);
  const [completions, setCompletions] = useState<UserTaskCompletion[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isCompleting, setIsCompleting] = useState<string | null>(null);
  const { addToast } = useToast();

  // Fetch available tasks
  const fetchTasks = async () => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from('social_tasks')
        .select('*')
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setTasks(data || []);
    } catch (error) {
      console.error('Failed to fetch social tasks:', error);
      addToast({
        type: 'error',
        title: '获取任务失败',
        message: '无法加载社交任务列表',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch user completions
  const fetchCompletions = async () => {
    if (!userId) return;

    try {
      const { data, error } = await supabase
        .from('user_task_completions')
        .select('*')
        .eq('user_id', userId);

      if (error) throw error;
      setCompletions(data || []);
    } catch (error) {
      console.error('Failed to fetch task completions:', error);
    }
  };

  // Complete a task
  const completeTask = async (taskId: string, verificationData?: any) => {
    if (!userId) {
      addToast({
        type: 'error',
        title: '完成任务失败',
        message: '请先登录您的账户',
      });
      return false;
    }

    // Check if task is already completed
    const existingCompletion = completions.find(c => c.task_id === taskId);
    if (existingCompletion) {
      addToast({
        type: 'warning',
        title: '任务已完成',
        message: '您已经完成过这个任务了',
      });
      return false;
    }

    setIsCompleting(taskId);
    try {
      // Simulate task verification process
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Insert task completion
      const { error: insertError } = await supabase
        .from('user_task_completions')
        .insert({
          user_id: userId,
          task_id: taskId,
          verification_data: verificationData,
          reward_claimed: true, // Auto-claim rewards for now
        });

      if (insertError) throw insertError;

      // Update task completion count
      const task = tasks.find(t => t.id === taskId);
      if (task) {
        const { error: updateError } = await supabase
          .from('social_tasks')
          .update({
            current_completions: task.current_completions + 1,
          })
          .eq('id', taskId);

        if (updateError) throw updateError;
      }

      addToast({
        type: 'success',
        title: '任务完成',
        message: `恭喜！您获得了 ${task?.reward_amount || 0} HAOX 奖励`,
      });

      // Refresh data
      await Promise.all([fetchTasks(), fetchCompletions()]);
      return true;
    } catch (error) {
      console.error('Failed to complete task:', error);
      addToast({
        type: 'error',
        title: '完成任务失败',
        message: '任务完成时出现错误，请重试',
      });
      return false;
    } finally {
      setIsCompleting(null);
    }
  };

  // Check if task is completed by user
  const isTaskCompleted = (taskId: string) => {
    return completions.some(c => c.task_id === taskId);
  };

  // Get task completion
  const getTaskCompletion = (taskId: string) => {
    return completions.find(c => c.task_id === taskId);
  };

  // Get tasks by platform
  const getTasksByPlatform = (platform: 'twitter' | 'discord' | 'telegram') => {
    return tasks.filter(task => task.platform === platform);
  };

  // Get completed tasks
  const getCompletedTasks = () => {
    return tasks.filter(task => isTaskCompleted(task.id));
  };

  // Get available tasks (not completed)
  const getAvailableTasks = () => {
    return tasks.filter(task => !isTaskCompleted(task.id));
  };

  // Calculate total rewards earned
  const getTotalRewardsEarned = () => {
    return completions.reduce((total, completion) => {
      const task = tasks.find(t => t.id === completion.task_id);
      return total + (task?.reward_amount || 0);
    }, 0);
  };

  // Get completion stats
  const getCompletionStats = () => {
    const totalTasks = tasks.length;
    const completedTasks = completions.length;
    const totalRewards = getTotalRewardsEarned();

    return {
      totalTasks,
      completedTasks,
      completionRate: totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0,
      totalRewards,
    };
  };

  // Mock task data for development
  const createMockTasks = async () => {
    const mockTasks = [
      {
        title: '关注官方 Twitter',
        description: '关注 @SocioMint 官方 Twitter 账户',
        platform: 'twitter' as const,
        task_type: 'follow' as const,
        reward_amount: 50,
        max_completions: 1000,
        current_completions: 245,
        is_active: true,
      },
      {
        title: '转发推广推文',
        description: '转发我们的最新推广推文并添加评论',
        platform: 'twitter' as const,
        task_type: 'retweet' as const,
        reward_amount: 30,
        max_completions: 500,
        current_completions: 123,
        is_active: true,
      },
      {
        title: '加入 Discord 社区',
        description: '加入 SocioMint 官方 Discord 服务器',
        platform: 'discord' as const,
        task_type: 'join' as const,
        reward_amount: 100,
        max_completions: 2000,
        current_completions: 567,
        is_active: true,
      },
      {
        title: '分享邀请链接',
        description: '在 Telegram 群组中分享您的邀请链接',
        platform: 'telegram' as const,
        task_type: 'share' as const,
        reward_amount: 75,
        max_completions: 1500,
        current_completions: 89,
        is_active: true,
      },
      {
        title: '点赞最新公告',
        description: '为我们在 Twitter 上的最新公告点赞',
        platform: 'twitter' as const,
        task_type: 'like' as const,
        reward_amount: 20,
        max_completions: 1000,
        current_completions: 456,
        is_active: true,
      },
    ];

    // In a real app, these would be inserted into the database
    // For now, we'll just set them directly
    setTasks(mockTasks as any);
  };

  // Load data on mount
  useEffect(() => {
    // For development, use mock data
    createMockTasks();
    
    // In production, uncomment this:
    // fetchTasks();
    
    if (userId) {
      fetchCompletions();
    }
  }, [userId]);

  return {
    tasks,
    completions,
    isLoading,
    isCompleting,
    
    // Actions
    completeTask,
    
    // Utilities
    isTaskCompleted,
    getTaskCompletion,
    getTasksByPlatform,
    getCompletedTasks,
    getAvailableTasks,
    getTotalRewardsEarned,
    getCompletionStats,
    
    // Refresh
    refetch: () => Promise.all([fetchTasks(), fetchCompletions()]),
  };
};
