/**
 * Gas费管理Hook
 * 提供Gas费估算、优化和代付功能
 */

import { useState, useCallback, useEffect } from 'react';
import { gasFeeService, GasFeeEstimate, TransactionRequest } from '@/services/gas/GasFeeService';
import { useToast } from '@/components/ui';

interface UseGasFeeReturn {
  gasFees: GasFeeEstimate | null;
  isLoading: boolean;
  error: string | null;
  selectedSpeed: 'slow' | 'standard' | 'fast';
  setSelectedSpeed: (speed: 'slow' | 'standard' | 'fast') => void;
  estimateTransactionGas: (request: TransactionRequest) => Promise<any>;
  optimizeGasFee: (request: TransactionRequest) => Promise<any>;
  checkSponsorship: (userAddress: string, transactionValue: string, gasEstimate: string) => Promise<any>;
  refreshGasFees: () => Promise<void>;
  networkCongestion: {
    level: 'low' | 'medium' | 'high';
    description: string;
    recommendedGasPrice: string;
  } | null;
}

export const useGasFee = (): UseGasFeeReturn => {
  const { addToast } = useToast();
  const [gasFees, setGasFees] = useState<GasFeeEstimate | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedSpeed, setSelectedSpeed] = useState<'slow' | 'standard' | 'fast'>('standard');
  const [networkCongestion, setNetworkCongestion] = useState<{
    level: 'low' | 'medium' | 'high';
    description: string;
    recommendedGasPrice: string;
  } | null>(null);

  /**
   * 加载Gas费估算
   */
  const loadGasFees = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const [fees, congestion] = await Promise.all([
        gasFeeService.getGasFeeEstimate(),
        gasFeeService.getNetworkCongestion(),
      ]);

      setGasFees(fees);
      setNetworkCongestion(congestion);

      // 根据网络拥堵情况调整推荐速度
      if (congestion.level === 'high') {
        setSelectedSpeed('fast');
      } else if (congestion.level === 'low') {
        setSelectedSpeed('slow');
      } else {
        setSelectedSpeed('standard');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '加载Gas费失败';
      setError(errorMessage);
      console.error('Failed to load gas fees:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * 估算交易Gas费用
   */
  const estimateTransactionGas = useCallback(async (request: TransactionRequest) => {
    try {
      setIsLoading(true);
      setError(null);

      const estimate = await gasFeeService.estimateTransactionGas(request);
      return estimate;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '估算Gas费失败';
      setError(errorMessage);
      
      addToast({
        type: 'error',
        title: 'Gas费估算失败',
        message: errorMessage,
      });
      
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [addToast]);

  /**
   * 优化Gas费用
   */
  const optimizeGasFee = useCallback(async (request: TransactionRequest) => {
    try {
      setIsLoading(true);
      setError(null);

      const optimization = await gasFeeService.optimizeGasFee(request);
      
      if (optimization.savingsPercentage > 5) {
        addToast({
          type: 'success',
          title: 'Gas费优化成功',
          message: `节省了 ${optimization.savingsPercentage.toFixed(1)}% 的Gas费用`,
        });
      }
      
      return optimization;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Gas费优化失败';
      setError(errorMessage);
      
      addToast({
        type: 'error',
        title: 'Gas费优化失败',
        message: errorMessage,
      });
      
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [addToast]);

  /**
   * 检查代付资格
   */
  const checkSponsorship = useCallback(async (
    userAddress: string,
    transactionValue: string,
    gasEstimate: string
  ) => {
    try {
      const eligibility = await gasFeeService.checkSponsorshipEligibility(
        userAddress,
        transactionValue,
        gasEstimate
      );

      if (eligibility.eligible) {
        addToast({
          type: 'success',
          title: '符合代付条件',
          message: '平台将为您代付Gas费用',
        });
      } else {
        addToast({
          type: 'info',
          title: '不符合代付条件',
          message: eligibility.reason || '需要自行支付Gas费用',
        });
      }

      return eligibility;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '检查代付资格失败';
      setError(errorMessage);
      
      addToast({
        type: 'error',
        title: '检查代付资格失败',
        message: errorMessage,
      });
      
      return {
        eligible: false,
        reason: errorMessage,
      };
    }
  }, [addToast]);

  /**
   * 刷新Gas费数据
   */
  const refreshGasFees = useCallback(async () => {
    await loadGasFees();
  }, [loadGasFees]);

  /**
   * 初始化加载
   */
  useEffect(() => {
    loadGasFees();
    
    // 每30秒刷新一次Gas费
    const interval = setInterval(loadGasFees, 30000);
    
    return () => clearInterval(interval);
  }, [loadGasFees]);

  return {
    gasFees,
    isLoading,
    error,
    selectedSpeed,
    setSelectedSpeed,
    estimateTransactionGas,
    optimizeGasFee,
    checkSponsorship,
    refreshGasFees,
    networkCongestion,
  };
};

/**
 * Gas费选择器Hook
 */
export const useGasFeeSelector = (initialSpeed: 'slow' | 'standard' | 'fast' = 'standard') => {
  const [selectedSpeed, setSelectedSpeed] = useState(initialSpeed);
  const [customGasPrice, setCustomGasPrice] = useState<string>('');
  const [useCustomGas, setUseCustomGas] = useState(false);

  const getSelectedGasPrice = useCallback((gasFees: GasFeeEstimate | null) => {
    if (!gasFees) return '5'; // 默认值

    if (useCustomGas && customGasPrice) {
      return customGasPrice;
    }

    return gasFees[selectedSpeed].gasPrice;
  }, [selectedSpeed, useCustomGas, customGasPrice]);

  const getSelectedCost = useCallback((gasFees: GasFeeEstimate | null) => {
    if (!gasFees) return '0.000105'; // 默认值

    if (useCustomGas && customGasPrice) {
      // 计算自定义Gas价格的成本
      const gasPrice = parseFloat(customGasPrice);
      const standardGasLimit = 21000;
      const costInGwei = gasPrice * standardGasLimit;
      return (costInGwei / 1e9).toFixed(9);
    }

    return gasFees[selectedSpeed].cost;
  }, [selectedSpeed, useCustomGas, customGasPrice]);

  const getEstimatedTime = useCallback((gasFees: GasFeeEstimate | null) => {
    if (!gasFees) return '1-2分钟'; // 默认值

    if (useCustomGas) {
      const gasPrice = parseFloat(customGasPrice);
      const standardPrice = parseFloat(gasFees.standard.gasPrice);
      
      if (gasPrice < standardPrice * 0.8) {
        return '3-5分钟';
      } else if (gasPrice > standardPrice * 1.2) {
        return '30秒-1分钟';
      } else {
        return '1-2分钟';
      }
    }

    return gasFees[selectedSpeed].estimatedTime;
  }, [selectedSpeed, useCustomGas, customGasPrice]);

  return {
    selectedSpeed,
    setSelectedSpeed,
    customGasPrice,
    setCustomGasPrice,
    useCustomGas,
    setUseCustomGas,
    getSelectedGasPrice,
    getSelectedCost,
    getEstimatedTime,
  };
};

/**
 * Gas费监控Hook
 */
export const useGasFeeMonitor = () => {
  const [priceHistory, setPriceHistory] = useState<Array<{
    timestamp: Date;
    gasPrice: string;
  }>>([]);
  const [priceAlert, setPriceAlert] = useState<{
    enabled: boolean;
    threshold: string;
  }>({
    enabled: false,
    threshold: '10',
  });

  const addPricePoint = useCallback((gasPrice: string) => {
    setPriceHistory(prev => {
      const newHistory = [...prev, {
        timestamp: new Date(),
        gasPrice,
      }];
      
      // 只保留最近24小时的数据
      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
      return newHistory.filter(point => point.timestamp > oneDayAgo);
    });
  }, []);

  const checkPriceAlert = useCallback((currentPrice: string) => {
    if (!priceAlert.enabled) return false;

    const current = parseFloat(currentPrice);
    const threshold = parseFloat(priceAlert.threshold);

    return current <= threshold;
  }, [priceAlert]);

  return {
    priceHistory,
    priceAlert,
    setPriceAlert,
    addPricePoint,
    checkPriceAlert,
  };
};
