/**
 * 福气系统相关的React Hooks
 */

import { useState, useEffect, useCallback } from 'react';
import { useSupabaseClient, useUser } from '@supabase/auth-helpers-react';

// 福气账户接口
export interface FortuneAccount {
  user_id: string;
  available_fortune: number;
  locked_fortune: number;
  total_earned: number;
  total_spent: number;
  total_deposited: number;
  total_withdrawn: number;
  fortune_level: number;
  fortune_level_name: string;
  consecutive_checkin_days: number;
  last_checkin_date: string | null;
  total_checkin_days: number;
  total_invitations: number;
  successful_invitations: number;
  created_at: string;
  updated_at: string;
}

// 福气交易记录接口
export interface FortuneTransaction {
  id: string;
  user_id: string;
  transaction_type: string;
  amount: number;
  balance_before: number;
  balance_after: number;
  reference_id?: string;
  reference_type?: string;
  description: string;
  created_at: string;
}

// 签到状态接口
export interface CheckInStatus {
  hasCheckedInToday: boolean;
  todayReward: number;
  consecutiveDays: number;
  totalCheckInDays: number;
  lastCheckInDate: string | null;
  baseReward: number;
  bonusReward: number;
  nextBonusAt: number;
  bonusProgress: number;
  bonusProgressPercentage: number;
  nextCheckIn: string;
  canCheckIn: boolean;
  recentCheckIns: Array<{
    date: string;
    reward: number;
    consecutiveDays: number;
  }>;
  encouragement: string;
}

// 邀请统计接口
export interface InviteStats {
  totalInvitations: number;
  successfulInvitations: number;
  totalRewards: number;
  availableFortune: number;
  fortuneLevel: number;
  fortuneLevelName: string;
  nextMilestone?: {
    target: number;
    reward: number;
  };
  recentInvitations: Array<{
    inviteeId: string;
    inviteCode: string;
    status: string;
    createdAt: string;
    inviteeInfo: {
      email?: string;
      telegramUsername?: string;
    };
  }>;
  rewardHistory: Array<{
    amount: number;
    description: string;
    createdAt: string;
  }>;
}

// 分享奖励统计接口
export interface ShareRewardStats {
  totalShares: number;
  totalShareReward: number;
  todayShares: number;
  todayReward: number;
  platformStats: Record<string, { count: number; totalReward: number }>;
  contentTypeStats: Record<string, { count: number; totalReward: number }>;
  recentShares: Array<{
    contentType: string;
    platform: string;
    rewardAmount: number;
    createdAt: string;
  }>;
  rewardPerShare: number;
  encouragement: string;
}

/**
 * 福气账户管理Hook
 */
export const useFortune = () => {
  const [fortuneAccount, setFortuneAccount] = useState<FortuneAccount | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const supabase = useSupabaseClient();
  const user = useUser();

  // 获取福气账户信息
  const fetchFortuneAccount = useCallback(async () => {
    if (!user) {
      setFortuneAccount(null);
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const { data, error } = await supabase
        .from('user_fortune')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      setFortuneAccount(data);
    } catch (err) {
      console.error('Failed to fetch fortune account:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  }, [user, supabase]);

  // 刷新福气账户
  const refreshFortune = useCallback(() => {
    return fetchFortuneAccount();
  }, [fetchFortuneAccount]);

  // 初始加载
  useEffect(() => {
    fetchFortuneAccount();
  }, [fetchFortuneAccount]);

  return {
    fortuneAccount,
    isLoading,
    error,
    refreshFortune
  };
};

/**
 * 福气奖励管理Hook
 */
export const useFortuneRewards = () => {
  const [checkInStatus, setCheckInStatus] = useState<CheckInStatus | null>(null);
  const [shareRewards, setShareRewards] = useState<ShareRewardStats | null>(null);
  const [inviteStats, setInviteStats] = useState<InviteStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const user = useUser();

  // 获取签到状态
  const fetchCheckInStatus = useCallback(async () => {
    if (!user) return;

    try {
      const response = await fetch('/api/fortune/daily-checkin');
      const result = await response.json();

      if (result.success) {
        setCheckInStatus(result.data);
      }
    } catch (err) {
      console.error('Failed to fetch check-in status:', err);
    }
  }, [user]);

  // 获取分享奖励统计
  const fetchShareRewards = useCallback(async () => {
    if (!user) return;

    try {
      const response = await fetch('/api/fortune/share-reward');
      const result = await response.json();

      if (result.success) {
        setShareRewards(result.data);
      }
    } catch (err) {
      console.error('Failed to fetch share rewards:', err);
    }
  }, [user]);

  // 获取邀请统计
  const fetchInviteStats = useCallback(async () => {
    if (!user) return;

    try {
      const response = await fetch('/api/fortune/invite-reward');
      const result = await response.json();

      if (result.success) {
        setInviteStats(result.data);
      }
    } catch (err) {
      console.error('Failed to fetch invite stats:', err);
    }
  }, [user]);

  // 每日签到
  const dailyCheckIn = useCallback(async () => {
    if (!user) throw new Error('User not authenticated');

    const response = await fetch('/api/fortune/daily-checkin', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.error || 'Check-in failed');
    }

    // 刷新签到状态
    await fetchCheckInStatus();

    return result.data;
  }, [user, fetchCheckInStatus]);

  // 处理分享奖励
  const processShareReward = useCallback(async (
    contentType: string,
    contentId: string,
    platform: string
  ) => {
    if (!user) throw new Error('User not authenticated');

    const response = await fetch('/api/fortune/share-reward', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contentType,
        contentId,
        platform,
      }),
    });

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.error || 'Share reward failed');
    }

    // 刷新分享奖励统计
    await fetchShareRewards();

    return result.data;
  }, [user, fetchShareRewards]);

  // 处理邀请奖励
  const processInviteReward = useCallback(async (inviteeId: string, inviteCode?: string) => {
    if (!user) throw new Error('User not authenticated');

    const response = await fetch('/api/fortune/invite-reward', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        inviteeId,
        inviteCode,
      }),
    });

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.error || 'Invite reward failed');
    }

    // 刷新邀请统计
    await fetchInviteStats();

    return result.data;
  }, [user, fetchInviteStats]);

  // 初始加载所有数据
  useEffect(() => {
    if (user) {
      setIsLoading(true);
      Promise.all([
        fetchCheckInStatus(),
        fetchShareRewards(),
        fetchInviteStats(),
      ]).finally(() => {
        setIsLoading(false);
      });
    } else {
      setIsLoading(false);
    }
  }, [user, fetchCheckInStatus, fetchShareRewards, fetchInviteStats]);

  return {
    checkInStatus,
    shareRewards,
    inviteStats,
    isLoading,
    error,
    dailyCheckIn,
    processShareReward,
    processInviteReward,
    refreshCheckInStatus: fetchCheckInStatus,
    refreshShareRewards: fetchShareRewards,
    refreshInviteStats: fetchInviteStats,
  };
};
