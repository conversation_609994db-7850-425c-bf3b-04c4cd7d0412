'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { cache<PERSON>anager, CacheConfig, CACHE_STRATEGIES } from '@/lib/cache-strategy';

// 缓存Hook选项
interface UseCacheOptions<T> extends CacheConfig {
  initialData?: T;
  enabled?: boolean;
  onSuccess?: (data: T) => void;
  onError?: (error: Error) => void;
  staleTime?: number; // 数据被认为过期的时间
  refetchOnWindowFocus?: boolean;
  refetchOnReconnect?: boolean;
}

// 缓存Hook返回值
interface UseCacheReturn<T> {
  data: T | null;
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  isStale: boolean;
  refetch: () => Promise<void>;
  mutate: (data: T) => Promise<void>;
  invalidate: () => Promise<void>;
}

/**
 * 缓存Hook
 */
export function useCache<T>(
  key: string,
  fetcher: () => Promise<T>,
  options: UseCacheOptions<T> = {}
): UseCacheReturn<T> {
  const {
    initialData = null,
    enabled = true,
    onSuccess,
    onError,
    staleTime = 0,
    refetchOnWindowFocus = false,
    refetchOnReconnect = false,
    ...cacheConfig
  } = options;

  const [data, setData] = useState<T | null>(initialData);
  const [isLoading, setIsLoading] = useState(false);
  const [isError, setIsError] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [isStale, setIsStale] = useState(false);
  const [lastFetchTime, setLastFetchTime] = useState<number>(0);

  const fetcherRef = useRef(fetcher);
  const optionsRef = useRef(options);

  // 更新refs
  useEffect(() => {
    fetcherRef.current = fetcher;
    optionsRef.current = options;
  });

  // 检查数据是否过期
  const checkStale = useCallback(() => {
    if (staleTime > 0 && lastFetchTime > 0) {
      const isDataStale = Date.now() - lastFetchTime > staleTime;
      setIsStale(isDataStale);
      return isDataStale;
    }
    return false;
  }, [staleTime, lastFetchTime]);

  // 获取数据
  const fetchData = useCallback(async (force = false) => {
    if (!enabled) return;

    setIsLoading(true);
    setIsError(false);
    setError(null);

    try {
      // 如果不是强制刷新，先尝试从缓存获取
      if (!force) {
        const cachedData = await cacheManager.get<T>(key, cacheConfig);
        if (cachedData !== null) {
          setData(cachedData);
          setLastFetchTime(Date.now());
          setIsLoading(false);
          onSuccess?.(cachedData);
          return;
        }
      }

      // 从fetcher获取数据
      const newData = await fetcherRef.current();
      
      // 缓存数据
      await cacheManager.set(key, newData, cacheConfig);
      
      setData(newData);
      setLastFetchTime(Date.now());
      setIsStale(false);
      onSuccess?.(newData);

    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error');
      setError(error);
      setIsError(true);
      onError?.(error);
    } finally {
      setIsLoading(false);
    }
  }, [key, enabled, cacheConfig, onSuccess, onError]);

  // 重新获取数据
  const refetch = useCallback(() => fetchData(true), [fetchData]);

  // 更新缓存数据
  const mutate = useCallback(async (newData: T) => {
    await cacheManager.set(key, newData, cacheConfig);
    setData(newData);
    setLastFetchTime(Date.now());
    setIsStale(false);
  }, [key, cacheConfig]);

  // 使缓存失效
  const invalidate = useCallback(async () => {
    await cacheManager.delete(key, cacheConfig.type);
    setData(null);
    setLastFetchTime(0);
    setIsStale(true);
  }, [key, cacheConfig.type]);

  // 初始加载
  useEffect(() => {
    if (enabled) {
      fetchData();
    }
  }, [fetchData, enabled]);

  // 检查数据是否过期
  useEffect(() => {
    const interval = setInterval(checkStale, 1000);
    return () => clearInterval(interval);
  }, [checkStale]);

  // 窗口焦点重新获取
  useEffect(() => {
    if (!refetchOnWindowFocus) return;

    const handleFocus = () => {
      if (checkStale()) {
        fetchData();
      }
    };

    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, [refetchOnWindowFocus, fetchData, checkStale]);

  // 网络重连重新获取
  useEffect(() => {
    if (!refetchOnReconnect) return;

    const handleOnline = () => {
      if (checkStale()) {
        fetchData();
      }
    };

    window.addEventListener('online', handleOnline);
    return () => window.removeEventListener('online', handleOnline);
  }, [refetchOnReconnect, fetchData, checkStale]);

  return {
    data,
    isLoading,
    isError,
    error,
    isStale,
    refetch,
    mutate,
    invalidate,
  };
}

/**
 * API缓存Hook
 */
export function useApiCache<T>(
  key: string,
  fetcher: () => Promise<T>,
  options: Omit<UseCacheOptions<T>, 'type'> = {}
) {
  return useCache(key, fetcher, {
    ...CACHE_STRATEGIES.api,
    ...options,
  });
}

/**
 * 用户数据缓存Hook
 */
export function useUserCache<T>(
  key: string,
  fetcher: () => Promise<T>,
  options: Omit<UseCacheOptions<T>, 'type'> = {}
) {
  return useCache(key, fetcher, {
    ...CACHE_STRATEGIES.user,
    ...options,
  });
}

/**
 * 静态资源缓存Hook
 */
export function useStaticCache<T>(
  key: string,
  fetcher: () => Promise<T>,
  options: Omit<UseCacheOptions<T>, 'type'> = {}
) {
  return useCache(key, fetcher, {
    ...CACHE_STRATEGIES.static,
    ...options,
  });
}

/**
 * 会话缓存Hook
 */
export function useSessionCache<T>(
  key: string,
  fetcher: () => Promise<T>,
  options: Omit<UseCacheOptions<T>, 'type'> = {}
) {
  return useCache(key, fetcher, {
    ...CACHE_STRATEGIES.session,
    ...options,
  });
}

/**
 * 大型数据缓存Hook
 */
export function useLargeDataCache<T>(
  key: string,
  fetcher: () => Promise<T>,
  options: Omit<UseCacheOptions<T>, 'type'> = {}
) {
  return useCache(key, fetcher, {
    ...CACHE_STRATEGIES.large,
    ...options,
  });
}

/**
 * 缓存状态Hook
 */
export function useCacheStatus() {
  const [cacheSize, setCacheSize] = useState({
    memory: 0,
    localStorage: 0,
    sessionStorage: 0,
  });

  const updateCacheSize = useCallback(() => {
    // 计算localStorage大小
    let localStorageSize = 0;
    for (let key in localStorage) {
      if (localStorage.hasOwnProperty(key)) {
        localStorageSize += localStorage[key].length;
      }
    }

    // 计算sessionStorage大小
    let sessionStorageSize = 0;
    for (let key in sessionStorage) {
      if (sessionStorage.hasOwnProperty(key)) {
        sessionStorageSize += sessionStorage[key].length;
      }
    }

    setCacheSize({
      memory: 0, // 内存缓存大小需要从CacheManager获取
      localStorage: localStorageSize,
      sessionStorage: sessionStorageSize,
    });
  }, []);

  const clearAllCaches = useCallback(async () => {
    await cacheManager.clear();
    updateCacheSize();
  }, [updateCacheSize]);

  const clearCacheByType = useCallback(async (type: 'memory' | 'localStorage' | 'sessionStorage' | 'indexedDB') => {
    await cacheManager.clear(type);
    updateCacheSize();
  }, [updateCacheSize]);

  useEffect(() => {
    updateCacheSize();
    
    // 定期更新缓存大小
    const interval = setInterval(updateCacheSize, 10000);
    return () => clearInterval(interval);
  }, [updateCacheSize]);

  return {
    cacheSize,
    updateCacheSize,
    clearAllCaches,
    clearCacheByType,
  };
}

/**
 * 缓存预热Hook
 */
export function useCacheWarmup() {
  const warmupCache = useCallback(async (
    entries: Array<{
      key: string;
      fetcher: () => Promise<any>;
      config?: CacheConfig;
    }>
  ) => {
    const promises = entries.map(async ({ key, fetcher, config }) => {
      try {
        // 检查缓存是否已存在
        const cached = await cacheManager.get(key, config);
        if (cached === null) {
          // 预加载数据
          const data = await fetcher();
          await cacheManager.set(key, data, config);
        }
      } catch (error) {
        console.warn(`Failed to warmup cache for key: ${key}`, error);
      }
    });

    await Promise.allSettled(promises);
  }, []);

  return { warmupCache };
}
