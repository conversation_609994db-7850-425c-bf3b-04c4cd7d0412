/**
 * 福气奖励兼容性Hook
 * 提供与原有useRewards Hook兼容的接口，但使用福气系统
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useTelegramAuth } from './useTelegramAuth';
import { useToast } from './useToast';
import { useFortune, useFortuneRewards } from './useFortune';

// 兼容原有的奖励接口
interface UserReward {
  id: string;
  type: string;
  amount: string;
  tokenType: string; // 现在固定为 'FORTUNE'
  status: 'pending' | 'claimed' | 'expired';
  description: string;
  createdAt: Date;
  expiresAt?: Date;
}

interface RewardClaim {
  id: string;
  rewardId: string;
  amount: string;
  tokenType: string;
  claimedAt: Date;
  txHash?: string;
}

interface UseRewardsReturn {
  // 奖励列表
  rewards: UserReward[];
  isLoadingRewards: boolean;
  refreshRewards: () => Promise<void>;

  // 待领取奖励
  pendingRewards: UserReward[];
  totalPendingAmount: string;

  // 领取奖励
  claimReward: (rewardId: string) => Promise<{ success: boolean; error?: string }>;
  claimAllRewards: () => Promise<{ success: boolean; claimed: number; error?: string }>;
  isClaiming: boolean;

  // 奖励历史
  claimHistory: RewardClaim[];
  isLoadingHistory: boolean;
  refreshHistory: () => Promise<void>;

  // 每日签到
  processDailySignIn: () => Promise<void>;

  // 错误状态
  error: string | null;
  clearError: () => void;

  // 新增：福气系统特有功能
  fortuneAccount: any;
  checkInStatus: any;
  shareRewards: any;
  inviteStats: any;
}

/**
 * 福气奖励兼容性Hook
 * 将福气系统适配到原有的奖励接口
 */
export const useFortuneRewardsCompat = (): UseRewardsReturn => {
  const { user, isAuthenticated } = useTelegramAuth();
  const { addToast } = useToast();
  
  // 使用新的福气系统Hook
  const { fortuneAccount, isLoading: fortuneLoading, refreshFortune } = useFortune();
  const {
    checkInStatus,
    shareRewards,
    inviteStats,
    isLoading: rewardsLoading,
    dailyCheckIn,
    processShareReward,
    processInviteReward
  } = useFortuneRewards();

  // 兼容性状态
  const [rewards, setRewards] = useState<UserReward[]>([]);
  const [claimHistory, setClaimHistory] = useState<RewardClaim[]>([]);
  const [isClaiming, setIsClaiming] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const isLoadingRewards = fortuneLoading || rewardsLoading;
  const isLoadingHistory = false; // 福气系统中历史记录是实时的

  /**
   * 生成兼容的奖励列表
   */
  const generateCompatRewards = useCallback((): UserReward[] => {
    const compatRewards: UserReward[] = [];

    // 每日签到奖励
    if (checkInStatus && !checkInStatus.hasCheckedInToday && checkInStatus.canCheckIn) {
      compatRewards.push({
        id: 'daily_checkin',
        type: 'daily_signin',
        amount: checkInStatus.baseReward.toString(),
        tokenType: 'FORTUNE',
        status: 'pending',
        description: '每日签到奖励',
        createdAt: new Date(),
        expiresAt: new Date(checkInStatus.nextCheckIn)
      });
    }

    // 连续签到奖励
    if (checkInStatus && checkInStatus.bonusReward > 0 && !checkInStatus.hasCheckedInToday) {
      compatRewards.push({
        id: 'consecutive_bonus',
        type: 'consecutive_signin',
        amount: checkInStatus.bonusReward.toString(),
        tokenType: 'FORTUNE',
        status: 'pending',
        description: '连续签到奖励',
        createdAt: new Date(),
        expiresAt: new Date(checkInStatus.nextCheckIn)
      });
    }

    // 邀请里程碑奖励
    if (inviteStats?.nextMilestone) {
      const progress = inviteStats.successfulInvitations / inviteStats.nextMilestone.target;
      if (progress >= 0.8) { // 接近里程碑时显示
        compatRewards.push({
          id: 'invite_milestone',
          type: 'invitation_milestone',
          amount: inviteStats.nextMilestone.reward.toString(),
          tokenType: 'FORTUNE',
          status: 'pending',
          description: `邀请${inviteStats.nextMilestone.target}人里程碑奖励`,
          createdAt: new Date()
        });
      }
    }

    return compatRewards;
  }, [checkInStatus, inviteStats]);

  /**
   * 生成兼容的领取历史
   */
  const generateCompatHistory = useCallback((): RewardClaim[] => {
    const compatHistory: RewardClaim[] = [];

    // 从邀请奖励历史生成
    if (inviteStats?.rewardHistory) {
      inviteStats.rewardHistory.forEach((reward, index) => {
        compatHistory.push({
          id: `invite_${index}`,
          rewardId: `invite_reward_${index}`,
          amount: reward.amount.toString(),
          tokenType: 'FORTUNE',
          claimedAt: new Date(reward.createdAt)
        });
      });
    }

    // 从签到历史生成
    if (checkInStatus?.recentCheckIns) {
      checkInStatus.recentCheckIns.forEach((checkIn, index) => {
        compatHistory.push({
          id: `checkin_${index}`,
          rewardId: `daily_checkin_${index}`,
          amount: checkIn.reward.toString(),
          tokenType: 'FORTUNE',
          claimedAt: new Date(checkIn.date)
        });
      });
    }

    return compatHistory.sort((a, b) => b.claimedAt.getTime() - a.claimedAt.getTime());
  }, [inviteStats, checkInStatus]);

  /**
   * 刷新奖励列表
   */
  const refreshRewards = useCallback(async () => {
    try {
      setError(null);
      await refreshFortune();
      const newRewards = generateCompatRewards();
      setRewards(newRewards);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取奖励列表失败';
      setError(errorMessage);
      console.error('Failed to refresh rewards:', err);
    }
  }, [refreshFortune, generateCompatRewards]);

  /**
   * 刷新领取历史
   */
  const refreshHistory = useCallback(async () => {
    try {
      setError(null);
      const newHistory = generateCompatHistory();
      setClaimHistory(newHistory);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取领取历史失败';
      setError(errorMessage);
      console.error('Failed to refresh claim history:', err);
    }
  }, [generateCompatHistory]);

  /**
   * 领取单个奖励
   */
  const claimReward = useCallback(async (rewardId: string) => {
    if (!user?.id || !isAuthenticated) {
      return { success: false, error: '用户未认证' };
    }

    try {
      setIsClaiming(true);
      setError(null);

      switch (rewardId) {
        case 'daily_checkin':
        case 'consecutive_bonus':
          await dailyCheckIn();
          addToast({
            type: 'success',
            title: '签到成功',
            message: '恭喜您获得福气奖励！'
          });
          break;

        default:
          throw new Error('未知的奖励类型');
      }

      await refreshRewards();
      await refreshFortune();

      return { success: true };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '领取奖励失败';
      setError(errorMessage);
      addToast({
        type: 'error',
        title: '领取失败',
        message: errorMessage
      });
      return { success: false, error: errorMessage };
    } finally {
      setIsClaiming(false);
    }
  }, [user?.id, isAuthenticated, dailyCheckIn, refreshRewards, refreshFortune, addToast]);

  /**
   * 领取所有奖励
   */
  const claimAllRewards = useCallback(async () => {
    if (!user?.id || !isAuthenticated) {
      return { success: false, claimed: 0, error: '用户未认证' };
    }

    try {
      setIsClaiming(true);
      setError(null);

      let claimed = 0;

      // 处理每日签到
      if (checkInStatus && !checkInStatus.hasCheckedInToday && checkInStatus.canCheckIn) {
        await dailyCheckIn();
        claimed++;
      }

      if (claimed > 0) {
        addToast({
          type: 'success',
          title: '批量领取成功',
          message: `成功领取${claimed}个奖励！`
        });

        await refreshRewards();
        await refreshFortune();
      }

      return { success: true, claimed };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '批量领取失败';
      setError(errorMessage);
      addToast({
        type: 'error',
        title: '领取失败',
        message: errorMessage
      });
      return { success: false, claimed: 0, error: errorMessage };
    } finally {
      setIsClaiming(false);
    }
  }, [user?.id, isAuthenticated, checkInStatus, dailyCheckIn, refreshRewards, refreshFortune, addToast]);

  /**
   * 处理每日签到
   */
  const processDailySignIn = useCallback(async () => {
    await claimReward('daily_checkin');
  }, [claimReward]);

  /**
   * 清除错误
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // 计算待领取奖励
  const pendingRewards = useMemo(() => {
    return rewards.filter(reward => reward.status === 'pending');
  }, [rewards]);

  // 计算待领取总金额
  const totalPendingAmount = useMemo(() => {
    return pendingRewards.reduce((total, reward) => {
      return total + parseFloat(reward.amount);
    }, 0).toString();
  }, [pendingRewards]);

  // 初始化数据
  useEffect(() => {
    if (isAuthenticated && user?.id) {
      refreshRewards();
      refreshHistory();
    }
  }, [isAuthenticated, user?.id, refreshRewards, refreshHistory]);

  // 更新奖励列表当福气数据变化时
  useEffect(() => {
    const newRewards = generateCompatRewards();
    setRewards(newRewards);
  }, [generateCompatRewards]);

  // 更新历史记录当福气数据变化时
  useEffect(() => {
    const newHistory = generateCompatHistory();
    setClaimHistory(newHistory);
  }, [generateCompatHistory]);

  return {
    // 奖励列表
    rewards,
    isLoadingRewards,
    refreshRewards,

    // 待领取奖励
    pendingRewards,
    totalPendingAmount,

    // 领取奖励
    claimReward,
    claimAllRewards,
    isClaiming,

    // 奖励历史
    claimHistory,
    isLoadingHistory,
    refreshHistory,

    // 每日签到
    processDailySignIn,

    // 错误状态
    error,
    clearError,

    // 新增：福气系统特有功能
    fortuneAccount,
    checkInStatus,
    shareRewards,
    inviteStats
  };
};
