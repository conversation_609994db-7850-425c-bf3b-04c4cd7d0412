'use client';

// 暂时注释掉所有 Wagmi 相关代码，等待重构为 Telegram OAuth + 托管钱包系统
// import { useReadContract, useWriteContract, useWaitForTransactionReceipt, useAccount, useChainId } from 'wagmi';
import { useState, useEffect } from 'react';
// import { parseUnits, formatUnits } from 'viem';
import { useToast } from '@/components/ui';
// import { HAOX_CONTRACTS, ERC20_ABI } from '@/lib/wagmi';
// import { HAOX_TOKEN } from '@/constants';

export const useHAOX = () => {
  // 暂时注释掉 Wagmi hooks
  // const { address } = useAccount();
  // const chainId = useChainId();
  const { addToast } = useToast();
  // const { writeContract, data: hash, isPending: isWritePending, error: writeError } = useWriteContract();
  // const { isLoading: isConfirming, isSuccess: isConfirmed } = useWaitForTransactionReceipt({
  //   hash,
  // });

  const [isTransacting, setIsTransacting] = useState(false);

  // 临时模拟数据
  const address = null;
  const chainId = 1;
  const contractAddress = null;

  // 暂时注释掉所有合约读取操作
  // const { data: balance, isLoading: isBalanceLoading, refetch: refetchBalance } = useReadContract({
  //   address: contractAddress as `0x${string}`,
  //   abi: ERC20_ABI,
  //   functionName: 'balanceOf',
  //   args: address ? [address] : undefined,
  //   query: {
  //     enabled: !!address && !!contractAddress,
  //   },
  // });

  // const { data: totalSupply } = useReadContract({
  //   address: contractAddress as `0x${string}`,
  //   abi: ERC20_ABI,
  //   functionName: 'totalSupply',
  //   query: {
  //     enabled: !!contractAddress,
  //   },
  // });

  // 临时模拟数据
  const balance = null;
  const isBalanceLoading = false;
  const refetchBalance = async () => {};
  const totalSupply = null;

  // 暂时注释掉 allowance 功能
  const getAllowance = (spender: string) => {
    // const { data: allowance, isLoading: isAllowanceLoading, refetch: refetchAllowance } = useReadContract({
    //   address: contractAddress as `0x${string}`,
    //   abi: ERC20_ABI,
    //   functionName: 'allowance',
    //   args: address && spender ? [address, spender as `0x${string}`] : undefined,
    //   query: {
    //     enabled: !!address && !!spender && !!contractAddress,
    //   },
    // });

    return {
      allowance: null,
      isAllowanceLoading: false,
      refetchAllowance: async () => {}
    };
  };

  // 暂时注释掉交易处理逻辑
  // useEffect(() => {
  //   if (isConfirmed) {
  //     setIsTransacting(false);
  //     addToast({
  //       type: 'success',
  //       title: '交易成功',
  //       message: 'HAOX 代币交易已成功完成',
  //     });
  //     refetchBalance();
  //   }
  // }, [isConfirmed, addToast, refetchBalance]);

  // useEffect(() => {
  //   if (writeError) {
  //     setIsTransacting(false);
  //     addToast({
  //       type: 'error',
  //       title: '交易失败',
  //       message: getErrorMessage(writeError),
  //     });
  //   }
  // }, [writeError, addToast]);

  // 暂时注释掉转账功能，替换为模拟函数
  const transfer = async (to: string, amount: string) => {
    console.log('Transfer function placeholder:', { to, amount });
    addToast({
      type: 'info',
      title: '功能开发中',
      message: '转账功能正在开发中，敬请期待',
    });
  };

  // 暂时注释掉原始转账逻辑
  // const transfer = async (to: string, amount: string) => {
  //   if (!contractAddress) {
  //     addToast({
  //       type: 'error',
  //       title: '网络不支持',
  //       message: '当前网络不支持 HAOX 代币',
  //     });
  //     return;
  //   }

  //   try {
  //     setIsTransacting(true);
  //     const parsedAmount = parseUnits(amount, HAOX_TOKEN.DECIMALS);

  //     await writeContract({
  //       address: contractAddress as `0x${string}`,
  //       abi: ERC20_ABI,
  //       functionName: 'transfer',
  //       args: [to as `0x${string}`, parsedAmount],
  //     });

  //     addToast({
  //       type: 'info',
  //       title: '交易已提交',
  //       message: '请在钱包中确认交易',
  //     });
  //   } catch (error) {
  //     setIsTransacting(false);
  //     console.error('Transfer error:', error);
  //     addToast({
  //       type: 'error',
  //       title: '转账失败',
  //       message: getErrorMessage(error),
  //     });
  //   }
  // };

  // 暂时注释掉授权功能，替换为模拟函数
  const approve = async (spender: string, amount: string) => {
    console.log('Approve function placeholder:', { spender, amount });
    addToast({
      type: 'info',
      title: '功能开发中',
      message: '授权功能正在开发中，敬请期待',
    });
  };

  // 暂时注释掉原始授权逻辑
  // const approve = async (spender: string, amount: string) => {
  //   if (!contractAddress) {
  //     addToast({
  //       type: 'error',
  //       title: '网络不支持',
  //       message: '当前网络不支持 HAOX 代币',
  //     });
  //     return;
  //   }

  //   try {
  //     setIsTransacting(true);
  //     const parsedAmount = parseUnits(amount, HAOX_TOKEN.DECIMALS);

  //     await writeContract({
  //       address: contractAddress as `0x${string}`,
  //       abi: ERC20_ABI,
  //       functionName: 'approve',
  //       args: [spender as `0x${string}`, parsedAmount],
  //     });

  //     addToast({
  //       type: 'info',
  //       title: '授权已提交',
  //       message: '请在钱包中确认授权交易',
  //     });
  //   } catch (error) {
  //     setIsTransacting(false);
  //     console.error('Approve error:', error);
  //     addToast({
  //       type: 'error',
  //       title: '授权失败',
  //       message: getErrorMessage(error),
  //     });
  //   }
  // };

  // 暂时注释掉工具函数，替换为模拟函数
  const formatHAOXAmount = (amount: bigint | undefined): string => {
    return '0.00'; // 临时返回固定值
  };

  // 暂时注释掉原始格式化函数
  // const formatHAOXAmount = (amount: bigint | undefined): string => {
  //   if (!amount) return '0';
  //   return formatUnits(amount, HAOX_TOKEN.DECIMALS);
  // };

  const parseHAOXAmount = (amount: string): bigint => {
    return BigInt(0); // 临时返回固定值
  };

  // 暂时注释掉原始解析函数
  // const parseHAOXAmount = (amount: string): bigint => {
  //   return parseUnits(amount, HAOX_TOKEN.DECIMALS);
  // };

  // 简化的错误处理函数
  const getErrorMessage = (error: any): string => {
    return error?.message || '操作失败';
  };

  // 暂时注释掉原始错误处理
  // const getErrorMessage = (error: any): string => {
  //   if (!error) return '未知错误';
  //   if (error.message?.includes('User rejected')) {
  //     return '用户取消了交易';
  //   }
  //   if (error.message?.includes('insufficient funds')) {
  //     return '余额不足';
  //   }
  //   if (error.message?.includes('gas required exceeds allowance')) {
  //     return 'Gas 费用不足';
  //   }
  //   if (error.message?.includes('execution reverted')) {
  //     return '交易执行失败，请检查参数';
  //   }
  //   return error.message || error.toString() || '交易失败';
  // };

  // 模拟余额检查函数
  const hasEnoughBalance = (amount: string): boolean => {
    return true; // 临时返回 true
  };

  // 暂时注释掉原始余额检查
  // const hasEnoughBalance = (amount: string): boolean => {
  //   if (!balance || !amount) return false;
  //   try {
  //     const parsedAmount = parseUnits(amount, HAOX_TOKEN.DECIMALS);
  //     return balance >= parsedAmount;
  //   } catch {
  //     return false;
  //   }
  // };

  // 模拟金额验证函数
  const isValidAmount = (amount: string): boolean => {
    return amount && amount !== '0' && parseFloat(amount) > 0;
  };

  // 暂时注释掉原始金额验证
  // const isValidAmount = (amount: string): boolean => {
  //   if (!amount || amount === '0') return false;
  //   try {
  //     const parsedAmount = parseUnits(amount, HAOX_TOKEN.DECIMALS);
  //     return parsedAmount > BigInt(0);
  //   } catch {
  //     return false;
  //   }
  // };

  return {
    // Contract info
    contractAddress,
    isContractSupported: !!contractAddress,

    // Balance - 使用模拟数据
    balance,
    balanceFormatted: formatHAOXAmount(balance),
    isBalanceLoading,
    refetchBalance,

    // Total supply - 使用模拟数据
    totalSupply,
    totalSupplyFormatted: formatHAOXAmount(totalSupply),

    // Transaction state - 使用模拟状态
    isTransacting: isTransacting,
    isWritePending: false,
    isConfirming: false,
    isConfirmed: false,
    transactionHash: null,

    // Actions - 使用模拟函数
    transfer,
    approve,
    getAllowance,

    // Utilities - 使用模拟函数
    formatHAOXAmount,
    parseHAOXAmount,
    hasEnoughBalance,
    isValidAmount,
    getErrorMessage,
  };
};
