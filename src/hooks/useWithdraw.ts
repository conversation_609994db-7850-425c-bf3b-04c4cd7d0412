/**
 * 提现功能Hook
 * 处理HAOX和BNB的提现操作
 */

import { useState, useCallback } from 'react';
import { useTelegramAuth } from './useTelegramAuth';
import { useWalletBalance } from './useWalletBalance';
import { WalletService } from '@/services/wallet/WalletService';
import { BlockchainService } from '@/services/blockchain/BlockchainService';
import { AuditLogger, AuditLoggerConfig } from '@/services/wallet/AuditLogger';
import { Transaction } from '@/services/wallet/types';
import { useToast } from '@/components/ui';

interface WithdrawRequest {
  to: string;
  amount: string;
  tokenType: 'HAOX' | 'BNB';
}

interface UseWithdrawReturn {
  isWithdrawing: boolean;
  withdrawToExternal: (toAddress: string, amount: string, tokenType: 'HAOX' | 'BNB') => Promise<Transaction>;
  estimateWithdrawFee: (request: WithdrawRequest) => Promise<string>;
  getWithdrawLimits: (tokenType: 'HAOX' | 'BNB') => {
    min: string;
    max: string;
    dailyLimit: string;
    remainingDaily: string;
  };
  error: string | null;
}

export const useWithdraw = (): UseWithdrawReturn => {
  const { user, isAuthenticated } = useTelegramAuth();
  const { refreshBalance } = useWalletBalance(false);
  const { addToast } = useToast();
  const [isWithdrawing, setIsWithdrawing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * 初始化服务
   */
  const initializeServices = useCallback(async () => {
    const auditLoggerConfig: AuditLoggerConfig = {
      logLevel: 'info',
      enableConsoleLogging: process.env.NODE_ENV === 'development',
      enableDatabaseLogging: true,
      enableCloudWatchLogging: process.env.NODE_ENV === 'production',
      retentionDays: 365,
      encryptLogs: true,
    };

    const auditLogger = new AuditLogger(auditLoggerConfig);
    const walletService = new WalletService();
    const blockchainService = new BlockchainService(auditLogger);

    await walletService.initialize();
    await blockchainService.initialize();

    return { walletService, blockchainService };
  }, []);

  /**
   * 提现到外部钱包
   */
  const withdrawToExternal = useCallback(async (
    toAddress: string,
    amount: string,
    tokenType: 'HAOX' | 'BNB'
  ): Promise<Transaction> => {
    if (!isAuthenticated || !user) {
      throw new Error('请先登录');
    }

    try {
      setIsWithdrawing(true);
      setError(null);

      // 验证提现限制
      const limits = getWithdrawLimits(tokenType);
      const withdrawAmount = parseFloat(amount);
      
      if (withdrawAmount < parseFloat(limits.min)) {
        throw new Error(`最小提现金额: ${limits.min} ${tokenType}`);
      }
      
      if (withdrawAmount > parseFloat(limits.max)) {
        throw new Error(`最大提现金额: ${limits.max} ${tokenType}`);
      }
      
      if (withdrawAmount > parseFloat(limits.remainingDaily)) {
        throw new Error(`超出每日提现限额，剩余额度: ${limits.remainingDaily} ${tokenType}`);
      }

      const { walletService } = await initializeServices();
      
      // 创建提现交易记录
      const transaction: Transaction = {
        id: generateTransactionId(),
        fromAddress: '', // 将由服务填充
        toAddress,
        amount,
        tokenType,
        status: 'pending',
        type: 'withdrawal',
        createdAt: new Date(),
        metadata: {
          telegramUserId: user.id,
          withdrawType: 'external',
          requiresApproval: true,
        },
      };

      // 对于大额提现，需要人工审核
      if (withdrawAmount > 1000) {
        transaction.metadata.requiresManualApproval = true;
        
        addToast({
          type: 'info',
          title: '提现申请已提交',
          message: '大额提现需要人工审核，预计1-24小时内处理完成',
        });
      } else {
        // 小额提现可以自动处理
        // 这里应该调用实际的提现逻辑
        addToast({
          type: 'success',
          title: '提现申请已提交',
          message: `${amount} ${tokenType} 提现申请已提交，预计30分钟内到账`,
        });
      }

      // 刷新余额
      await refreshBalance();

      return transaction;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '提现失败';
      setError(errorMessage);
      
      addToast({
        type: 'error',
        title: '提现失败',
        message: errorMessage,
      });
      
      throw err;
    } finally {
      setIsWithdrawing(false);
    }
  }, [isAuthenticated, user, refreshBalance, addToast]);

  /**
   * 估算提现手续费
   */
  const estimateWithdrawFee = useCallback(async (request: WithdrawRequest): Promise<string> => {
    try {
      const { blockchainService } = await initializeServices();
      
      // 估算区块链手续费
      const gasFee = await blockchainService.estimateGasFees({
        to: request.to,
        value: request.tokenType === 'BNB' ? request.amount : undefined,
        data: request.tokenType === 'HAOX' ? 'transfer_data' : undefined,
      });

      // 提现手续费 = Gas费 + 平台手续费
      const platformFee = request.tokenType === 'HAOX' ? '0.001' : '0.002'; // BNB
      const totalFee = parseFloat(gasFee.standard.cost) + parseFloat(platformFee);

      return totalFee.toFixed(6);
    } catch (err) {
      console.error('Failed to estimate withdraw fee:', err);
      
      // 返回默认手续费
      return request.tokenType === 'HAOX' ? '0.005' : '0.01';
    }
  }, [initializeServices]);

  /**
   * 获取提现限制
   */
  const getWithdrawLimits = useCallback((tokenType: 'HAOX' | 'BNB') => {
    const limits = {
      HAOX: {
        min: '10',
        max: '10000',
        dailyLimit: '50000',
        remainingDaily: '50000', // 这里应该从API获取实际剩余额度
      },
      BNB: {
        min: '0.01',
        max: '10',
        dailyLimit: '50',
        remainingDaily: '50', // 这里应该从API获取实际剩余额度
      },
    };

    return limits[tokenType];
  }, []);

  return {
    isWithdrawing,
    withdrawToExternal,
    estimateWithdrawFee,
    getWithdrawLimits,
    error,
  };
};

/**
 * 提现历史Hook
 */
export const useWithdrawHistory = () => {
  const { user, isAuthenticated } = useTelegramAuth();
  const [history, setHistory] = useState<Transaction[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const loadHistory = useCallback(async (page: number = 1, limit: number = 20) => {
    if (!isAuthenticated || !user) {
      setHistory([]);
      return;
    }

    try {
      setIsLoading(true);

      const walletService = new WalletService();
      await walletService.initialize();
      
      const transactions = await walletService.getUserTransactionHistory(user.id, page, limit);
      
      // 只显示提现相关的交易
      const withdrawTransactions = transactions.filter(tx => tx.type === 'withdrawal');
      
      setHistory(withdrawTransactions);
    } catch (error) {
      console.error('Failed to load withdraw history:', error);
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, user]);

  return {
    history,
    isLoading,
    loadHistory,
  };
};

/**
 * 提现状态Hook
 */
export const useWithdrawStatus = () => {
  const [status, setStatus] = useState<{
    pending: number;
    processing: number;
    completed: number;
    failed: number;
  }>({
    pending: 0,
    processing: 0,
    completed: 0,
    failed: 0,
  });

  const refreshStatus = useCallback(async () => {
    try {
      // 这里应该调用API获取提现状态统计
      // 暂时使用模拟数据
      setStatus({
        pending: 2,
        processing: 1,
        completed: 15,
        failed: 0,
      });
    } catch (error) {
      console.error('Failed to refresh withdraw status:', error);
    }
  }, []);

  return {
    status,
    refreshStatus,
  };
};

/**
 * 提现验证Hook
 */
export const useWithdrawVerification = () => {
  const [isVerifying, setIsVerifying] = useState(false);
  const [verificationStep, setVerificationStep] = useState<'email' | 'sms' | 'totp' | null>(null);

  const startVerification = useCallback(async (method: 'email' | 'sms' | 'totp') => {
    try {
      setIsVerifying(true);
      setVerificationStep(method);

      // 这里应该调用API发送验证码
      console.log(`Starting ${method} verification`);

      // 模拟验证过程
      await new Promise(resolve => setTimeout(resolve, 2000));
      
    } catch (error) {
      console.error('Failed to start verification:', error);
      throw error;
    } finally {
      setIsVerifying(false);
    }
  }, []);

  const verifyCode = useCallback(async (code: string): Promise<boolean> => {
    try {
      setIsVerifying(true);

      // 这里应该调用API验证验证码
      console.log(`Verifying code: ${code}`);

      // 模拟验证过程
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 模拟验证结果
      const isValid = code === '123456';
      
      if (isValid) {
        setVerificationStep(null);
      }
      
      return isValid;
    } catch (error) {
      console.error('Failed to verify code:', error);
      return false;
    } finally {
      setIsVerifying(false);
    }
  }, []);

  return {
    isVerifying,
    verificationStep,
    startVerification,
    verifyCode,
  };
};

/**
 * 生成交易ID
 */
function generateTransactionId(): string {
  return `withdraw_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}
