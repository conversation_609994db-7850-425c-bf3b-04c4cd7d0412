'use client';

import { useState, useEffect, useCallback } from 'react';
import { useToast } from '@/components/ui';

export interface PresaleData {
  participantCount: number;
  currentPrice: number;
  nextPrice: number;
  totalRaised: number;
  targetAmount: number;
  endTime: Date;
  walletAddress: string;
  isActive: boolean;
  currentRound: number;
  totalRounds: number;
  // 新增的动态定价相关属性
  currentRate?: number; // HAOX per BNB
  nextRate?: number; // HAOX per BNB
  currentStage?: number; // 当前阶段
  totalStages?: number; // 总阶段数
  bnbUsdPrice?: number; // BNB USD价格
  tokensRemainingInStage?: number; // 当前阶段剩余代币
  tokensPerStage?: number; // 每阶段代币数量
  totalTokensSold?: number; // 已售出代币总数
  totalTokensForSale?: number; // 预售代币总数
  minInvestment?: number; // 最低投资
  maxInvestment?: number; // 最高投资
  progressPercentage?: number; // 进度百分比
  recentPurchases: Array<{
    amount: number;
    timestamp: Date;
    address: string;
    txHash?: string;
    bnbAmount?: number;
    stage?: number;
  }>;
  stageHistory?: Array<{
    stage: number;
    rate: number;
    usdPrice: number;
    completed: boolean;
  }>;
}

export interface PresaleStats {
  timeLeft: {
    days: number;
    hours: number;
    minutes: number;
    seconds: number;
  };
  progressPercentage: number;
  priceIncreasePercentage: number;
  isEnded: boolean;
}

export const usePresale = () => {
  const { addToast } = useToast();
  const [isClient, setIsClient] = useState(false);

  // 使用固定的时间戳避免hydration错误
  const baseTime = new Date('2024-01-15T00:00:00Z').getTime();

  const [presaleData, setPresaleData] = useState<PresaleData>({
    participantCount: 2847,
    currentPrice: 0.0008,
    nextPrice: 0.0012,
    totalRaised: 1247500,
    targetAmount: 2000000,
    endTime: new Date(baseTime + 7 * 24 * 60 * 60 * 1000), // 7 days from base time
    walletAddress: process.env.NEXT_PUBLIC_HAOX_CONTRACT_ADDRESS_BSC || '******************************************',
    isActive: true,
    currentRound: 1,
    totalRounds: 5,
    recentPurchases: [
      {
        amount: 50000,
        timestamp: new Date(baseTime - 2 * 60 * 1000),
        address: '0x1234...5678',
        txHash: '0xabc123...'
      },
      {
        amount: 25000,
        timestamp: new Date(baseTime - 5 * 60 * 1000),
        address: '0xabcd...efgh',
        txHash: '0xdef456...'
      },
      {
        amount: 100000,
        timestamp: new Date(baseTime - 8 * 60 * 1000),
        address: '0x9876...5432',
        txHash: '0x789xyz...'
      },
    ]
  });

  const [stats, setStats] = useState<PresaleStats>({
    timeLeft: { days: 0, hours: 0, minutes: 0, seconds: 0 },
    progressPercentage: 0,
    priceIncreasePercentage: 0,
    isEnded: false
  });

  // 确保只在客户端运行
  useEffect(() => {
    setIsClient(true);
  }, []);

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Calculate stats from presale data
  const calculateStats = useCallback((data: PresaleData): PresaleStats => {
    const now = new Date().getTime();
    const distance = data.endTime.getTime() - now;
    
    const timeLeft = distance > 0 ? {
      days: Math.floor(distance / (1000 * 60 * 60 * 24)),
      hours: Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
      minutes: Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60)),
      seconds: Math.floor((distance % (1000 * 60)) / 1000)
    } : { days: 0, hours: 0, minutes: 0, seconds: 0 };

    const progressPercentage = (data.totalRaised / data.targetAmount) * 100;
    const priceIncreasePercentage = ((data.nextPrice - data.currentPrice) / data.currentPrice) * 100;
    const isEnded = distance <= 0;

    return {
      timeLeft,
      progressPercentage,
      priceIncreasePercentage,
      isEnded
    };
  }, []);

  // Update countdown timer - 只在客户端运行
  useEffect(() => {
    if (!isClient) return;

    // 立即计算一次
    setStats(prev => ({
      ...prev,
      ...calculateStats(presaleData)
    }));

    const timer = setInterval(() => {
      setStats(prev => ({
        ...prev,
        ...calculateStats(presaleData)
      }));
    }, 1000);

    return () => clearInterval(timer);
  }, [presaleData, calculateStats, isClient]);

  // Simulate real-time updates
  useEffect(() => {
    const updateTimer = setInterval(() => {
      setPresaleData(prev => {
        const newParticipantCount = prev.participantCount + Math.floor(Math.random() * 3);
        const newTotalRaised = prev.totalRaised + Math.floor(Math.random() * 1000);
        
        // Add new recent purchase occasionally
        const shouldAddPurchase = Math.random() < 0.3; // 30% chance
        let newRecentPurchases = prev.recentPurchases;
        
        if (shouldAddPurchase) {
          const newPurchase = {
            amount: Math.floor(Math.random() * 50000) + 10000,
            timestamp: new Date(),
            address: `0x${Math.random().toString(16).substr(2, 4)}...${Math.random().toString(16).substr(2, 4)}`,
            txHash: `0x${Math.random().toString(16).substr(2, 8)}...`
          };
          
          newRecentPurchases = [newPurchase, ...prev.recentPurchases.slice(0, 4)];
        }

        return {
          ...prev,
          participantCount: newParticipantCount,
          totalRaised: newTotalRaised,
          recentPurchases: newRecentPurchases
        };
      });
    }, 30000); // Update every 30 seconds

    return () => clearInterval(updateTimer);
  }, []);

  // Fetch presale data from API
  const fetchPresaleData = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // In a real implementation, this would fetch from your API
      const response = await fetch('/api/presale/data');
      
      if (!response.ok) {
        throw new Error('Failed to fetch presale data');
      }
      
      const data = await response.json();
      setPresaleData(data);
      setStats(calculateStats(data));
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      
      // Use mock data on error
      console.warn('Using mock presale data due to API error:', errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [calculateStats]);

  // Participate in presale
  const participateInPresale = useCallback(async (amount: number, paymentMethod: 'crypto' | 'fiat' = 'crypto') => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/presale/participate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount,
          paymentMethod,
          walletAddress: presaleData.walletAddress
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to participate in presale');
      }
      
      const result = await response.json();
      
      addToast({
        type: 'success',
        title: '参与成功',
        message: `您已成功参与预售，购买了 ${amount.toLocaleString()} HAOX 代币`
      });
      
      // Refresh presale data
      await fetchPresaleData();
      
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Participation failed';
      setError(errorMessage);
      
      addToast({
        type: 'error',
        title: '参与失败',
        message: errorMessage
      });
      
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [presaleData.walletAddress, addToast, fetchPresaleData]);

  // Copy wallet address to clipboard
  const copyWalletAddress = useCallback(async () => {
    try {
      await navigator.clipboard.writeText(presaleData.walletAddress);
      addToast({
        type: 'success',
        title: '地址已复制',
        message: '钱包地址已复制到剪贴板'
      });
      return true;
    } catch (err) {
      addToast({
        type: 'error',
        title: '复制失败',
        message: '无法复制地址，请手动复制'
      });
      return false;
    }
  }, [presaleData.walletAddress, addToast]);

  // Get presale status
  const getPresaleStatus = useCallback(() => {
    if (stats.isEnded) {
      return 'ended';
    }
    
    if (!presaleData.isActive) {
      return 'inactive';
    }
    
    if (stats.progressPercentage >= 100) {
      return 'sold_out';
    }
    
    if (stats.timeLeft.days <= 1) {
      return 'ending_soon';
    }
    
    return 'active';
  }, [stats, presaleData.isActive]);

  // Initialize data on mount
  useEffect(() => {
    // For now, we'll use mock data. In production, uncomment the line below:
    // fetchPresaleData();
    
    // Initialize stats with current data
    setStats(calculateStats(presaleData));
  }, [calculateStats, presaleData]);

  return {
    presaleData,
    stats,
    isLoading,
    error,
    fetchPresaleData,
    participateInPresale,
    copyWalletAddress,
    getPresaleStatus,
    refresh: fetchPresaleData
  };
};
