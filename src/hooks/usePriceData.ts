'use client';

import { useState, useEffect, useCallback } from 'react';
import { useQuery } from '@tanstack/react-query';

interface PriceDataPoint {
  time: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

interface PriceData {
  symbol: string;
  data: PriceDataPoint[];
  currentPrice: number;
  priceChange24h: number;
  volume24h: number;
  marketCap: number;
}

interface UsePriceDataOptions {
  symbol?: string;
  timeframe?: string;
  limit?: number;
  enableRealTime?: boolean;
  refetchInterval?: number;
}

export const usePriceData = (options: UsePriceDataOptions = {}) => {
  const {
    symbol = 'HAOX',
    timeframe = '1D',
    limit = 30,
    enableRealTime = false,
    refetchInterval = 30000 // 30秒
  } = options;

  const [realTimePrice, setRealTimePrice] = useState<number | null>(null);
  const [isConnected, setIsConnected] = useState(false);

  // 获取历史价格数据
  const {
    data: priceData,
    isLoading,
    error,
    refetch
  } = useQuery<PriceData>({
    queryKey: ['priceData', symbol, timeframe, limit],
    queryFn: async () => {
      const params = new URLSearchParams({
        symbol,
        timeframe,
        limit: limit.toString()
      });

      const response = await fetch(`/api/price-data?${params}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch price data');
      }

      return response.json();
    },
    staleTime: 30000, // 30秒内数据被认为是新鲜的
    gcTime: 300000, // 5分钟缓存
    refetchInterval: enableRealTime ? refetchInterval : false,
    refetchOnWindowFocus: false,
  });

  // 实时价格更新
  useEffect(() => {
    if (!enableRealTime) return;

    let intervalId: NodeJS.Timeout;

    const fetchRealTimePrice = async () => {
      try {
        const response = await fetch(`/api/price-data?symbol=${symbol}`, {
          method: 'PATCH'
        });

        if (response.ok) {
          const data = await response.json();
          setRealTimePrice(data.price);
          setIsConnected(true);
        } else {
          setIsConnected(false);
        }
      } catch (error) {
        console.error('Real-time price fetch error:', error);
        setIsConnected(false);
      }
    };

    // 立即获取一次
    fetchRealTimePrice();

    // 设置定时更新
    intervalId = setInterval(fetchRealTimePrice, refetchInterval);

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [symbol, enableRealTime, refetchInterval]);

  // 设置价格预警
  const setPriceAlert = useCallback(async (targetPrice: number, alertType: 'above' | 'below') => {
    try {
      const response = await fetch('/api/price-data', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          symbol,
          targetPrice,
          alertType
        })
      });

      if (!response.ok) {
        throw new Error('Failed to set price alert');
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Set price alert error:', error);
      throw error;
    }
  }, [symbol]);

  // 格式化价格数据用于图表
  const formatChartData = useCallback((data: PriceDataPoint[]) => {
    return {
      candlestick: data.map(item => ({
        time: item.time,
        open: item.open,
        high: item.high,
        low: item.low,
        close: item.close,
      })),
      volume: data.map(item => ({
        time: item.time,
        value: item.volume,
        color: item.close >= item.open ? '#00C85380' : '#FF174480',
      }))
    };
  }, []);

  // 计算技术指标
  const calculateMA = useCallback((data: PriceDataPoint[], period: number = 20) => {
    const ma = [];
    for (let i = period - 1; i < data.length; i++) {
      const sum = data.slice(i - period + 1, i + 1).reduce((acc, item) => acc + item.close, 0);
      ma.push({
        time: data[i].time,
        value: sum / period
      });
    }
    return ma;
  }, []);

  // 计算RSI
  const calculateRSI = useCallback((data: PriceDataPoint[], period: number = 14) => {
    if (data.length < period + 1) return [];

    const changes = [];
    for (let i = 1; i < data.length; i++) {
      changes.push(data[i].close - data[i - 1].close);
    }

    const rsi = [];
    for (let i = period; i < changes.length; i++) {
      const gains = changes.slice(i - period, i).filter(change => change > 0);
      const losses = changes.slice(i - period, i).filter(change => change < 0).map(loss => Math.abs(loss));

      const avgGain = gains.length > 0 ? gains.reduce((a, b) => a + b, 0) / period : 0;
      const avgLoss = losses.length > 0 ? losses.reduce((a, b) => a + b, 0) / period : 0;

      const rs = avgLoss === 0 ? 100 : avgGain / avgLoss;
      const rsiValue = 100 - (100 / (1 + rs));

      rsi.push({
        time: data[i + 1].time,
        value: rsiValue
      });
    }

    return rsi;
  }, []);

  // 获取当前显示的价格（实时价格优先）
  const getCurrentPrice = useCallback(() => {
    if (enableRealTime && realTimePrice !== null) {
      return realTimePrice;
    }
    return priceData?.currentPrice || 0;
  }, [enableRealTime, realTimePrice, priceData?.currentPrice]);

  return {
    // 数据
    priceData,
    chartData: priceData ? formatChartData(priceData.data) : null,
    currentPrice: getCurrentPrice(),
    priceChange24h: priceData?.priceChange24h || 0,
    volume24h: priceData?.volume24h || 0,
    marketCap: priceData?.marketCap || 0,
    
    // 状态
    isLoading,
    error,
    isConnected,
    
    // 方法
    refetch,
    setPriceAlert,
    
    // 技术指标
    calculateMA: (period?: number) => priceData ? calculateMA(priceData.data, period) : [],
    calculateRSI: (period?: number) => priceData ? calculateRSI(priceData.data, period) : [],
    
    // 配置
    symbol,
    timeframe,
  };
};
