/**
 * 钱包余额Hook
 * 实时查询和管理用户钱包余额
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { useTelegramAuth } from './useTelegramAuth';
import { WalletService } from '@/services/wallet/WalletService';
import { WalletBalance, PendingReward } from '@/services/wallet/types';

interface UseWalletBalanceReturn {
  balance: WalletBalance | null;
  pendingRewards: PendingReward[];
  isLoading: boolean;
  error: string | null;
  refreshBalance: () => Promise<void>;
  totalPendingAmount: string;
  estimatedUsdValue: string;
}

export const useWalletBalance = (autoRefresh: boolean = true): UseWalletBalanceReturn => {
  const { user, isAuthenticated } = useTelegramAuth();
  const [balance, setBalance] = useState<WalletBalance | null>(null);
  const [pendingRewards, setPendingRewards] = useState<PendingReward[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const refreshIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const walletService = new WalletService();

  /**
   * 刷新余额数据
   */
  const refreshBalance = useCallback(async () => {
    if (!isAuthenticated || !user) {
      setBalance(null);
      setPendingRewards([]);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // 初始化钱包服务
      await walletService.initialize();

      // 并行获取余额和待领取奖励
      const [balanceData, rewardsData] = await Promise.all([
        walletService.getWalletBalance(user.id),
        walletService.getUserPendingRewards(user.id),
      ]);

      setBalance(balanceData);
      setPendingRewards(rewardsData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取余额失败';
      setError(errorMessage);
      console.error('Failed to refresh wallet balance:', err);
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, user]);

  /**
   * 计算总待领取金额
   */
  const totalPendingAmount = pendingRewards.reduce((total, reward) => {
    return (parseFloat(total) + parseFloat(reward.amount)).toString();
  }, '0');

  /**
   * 估算USD价值
   */
  const estimatedUsdValue = (() => {
    if (!balance) return '0.00';
    
    // 简单的价格估算（实际应该从价格预言机获取）
    const bnbPrice = 300; // $300 per BNB
    const haoxPrice = 0.1; // $0.1 per HAOX
    
    const bnbValue = parseFloat(balance.bnbBalance) * bnbPrice;
    const haoxValue = parseFloat(balance.haoxBalance) * haoxPrice;
    const pendingValue = parseFloat(totalPendingAmount) * haoxPrice;
    
    return (bnbValue + haoxValue + pendingValue).toFixed(2);
  })();

  /**
   * 设置自动刷新
   */
  useEffect(() => {
    if (autoRefresh && isAuthenticated) {
      // 立即刷新一次
      refreshBalance();

      // 设置定时刷新（每30秒）
      refreshIntervalRef.current = setInterval(() => {
        refreshBalance();
      }, 30000);

      return () => {
        if (refreshIntervalRef.current) {
          clearInterval(refreshIntervalRef.current);
          refreshIntervalRef.current = null;
        }
      };
    } else {
      // 清除定时器
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
        refreshIntervalRef.current = null;
      }
    }
  }, [autoRefresh, isAuthenticated, refreshBalance]);

  /**
   * 用户认证状态变化时重置数据
   */
  useEffect(() => {
    if (!isAuthenticated) {
      setBalance(null);
      setPendingRewards([]);
      setError(null);
    }
  }, [isAuthenticated]);

  return {
    balance,
    pendingRewards,
    isLoading,
    error,
    refreshBalance,
    totalPendingAmount,
    estimatedUsdValue,
  };
};

/**
 * 余额格式化Hook
 */
export const useBalanceFormatter = () => {
  const formatBalance = useCallback((balance: string, decimals: number = 4): string => {
    const num = parseFloat(balance);
    if (isNaN(num)) return '0';
    
    if (num === 0) return '0';
    if (num < 0.0001) return '< 0.0001';
    
    return num.toLocaleString('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: decimals,
    });
  }, []);

  const formatUSD = useCallback((amount: string): string => {
    const num = parseFloat(amount);
    if (isNaN(num)) return '$0.00';
    
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(num);
  }, []);

  const formatPercentage = useCallback((value: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'percent',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value / 100);
  }, []);

  return {
    formatBalance,
    formatUSD,
    formatPercentage,
  };
};

/**
 * 余额变化动画Hook
 */
export const useBalanceAnimation = (currentBalance: string, previousBalance: string) => {
  const [isIncreasing, setIsIncreasing] = useState<boolean | null>(null);
  const [showAnimation, setShowAnimation] = useState(false);

  useEffect(() => {
    const current = parseFloat(currentBalance);
    const previous = parseFloat(previousBalance);

    if (!isNaN(current) && !isNaN(previous) && current !== previous) {
      setIsIncreasing(current > previous);
      setShowAnimation(true);

      // 3秒后隐藏动画
      const timer = setTimeout(() => {
        setShowAnimation(false);
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [currentBalance, previousBalance]);

  return {
    isIncreasing,
    showAnimation,
  };
};

/**
 * 余额历史Hook
 */
export const useBalanceHistory = (telegramUserId: number) => {
  const [history, setHistory] = useState<Array<{
    timestamp: Date;
    haoxBalance: string;
    bnbBalance: string;
    usdValue: string;
  }>>([]);

  const addBalanceSnapshot = useCallback((balance: WalletBalance) => {
    setHistory(prev => {
      const newSnapshot = {
        timestamp: new Date(),
        haoxBalance: balance.haoxBalance,
        bnbBalance: balance.bnbBalance,
        usdValue: '0', // 需要计算USD价值
      };

      // 保留最近100个快照
      const updated = [newSnapshot, ...prev].slice(0, 100);
      return updated;
    });
  }, []);

  const getBalanceChange24h = useCallback(() => {
    if (history.length < 2) return null;

    const now = new Date();
    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    
    const recent = history[0];
    const dayAgo = history.find(h => h.timestamp <= yesterday) || history[history.length - 1];

    const currentValue = parseFloat(recent.haoxBalance);
    const previousValue = parseFloat(dayAgo.haoxBalance);
    
    if (previousValue === 0) return null;

    const change = ((currentValue - previousValue) / previousValue) * 100;
    return {
      percentage: change,
      absolute: (currentValue - previousValue).toString(),
      isPositive: change >= 0,
    };
  }, [history]);

  return {
    history,
    addBalanceSnapshot,
    getBalanceChange24h,
  };
};
