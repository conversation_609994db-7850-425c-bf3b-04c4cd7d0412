'use client';

import { useState, useCallback, useRef } from 'react';
import { toast } from 'sonner';

// 错误类型定义
export interface AppError {
  code?: string;
  message: string;
  details?: any;
  statusCode?: number;
  timestamp?: string;
}

// 错误处理选项
export interface ErrorHandlerOptions {
  showToast?: boolean;
  logError?: boolean;
  retryable?: boolean;
  fallbackMessage?: string;
  onError?: (error: AppError) => void;
}

// 错误处理Hook返回类型
export interface UseErrorHandlerReturn {
  error: AppError | null;
  isError: boolean;
  clearError: () => void;
  handleError: (error: any, options?: ErrorHandlerOptions) => void;
  retryCount: number;
  canRetry: boolean;
  retry: () => void;
}

// 默认错误处理选项
const DEFAULT_OPTIONS: ErrorHandlerOptions = {
  showToast: true,
  logError: true,
  retryable: false,
  fallbackMessage: '操作失败，请稍后重试',
};

/**
 * 全局错误处理Hook
 */
export function useErrorHandler(
  defaultOptions: Partial<ErrorHandlerOptions> = {}
): UseErrorHandlerReturn {
  const [error, setError] = useState<AppError | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const retryFunctionRef = useRef<(() => void) | null>(null);
  
  const mergedOptions = { ...DEFAULT_OPTIONS, ...defaultOptions };

  // 清除错误
  const clearError = useCallback(() => {
    setError(null);
    setRetryCount(0);
    retryFunctionRef.current = null;
  }, []);

  // 解析错误对象
  const parseError = useCallback((error: any): AppError => {
    // 如果已经是AppError格式
    if (error && typeof error === 'object' && error.message) {
      return {
        code: error.code,
        message: error.message,
        details: error.details,
        statusCode: error.statusCode,
        timestamp: error.timestamp || new Date().toISOString(),
      };
    }

    // 如果是API响应错误
    if (error?.response) {
      const response = error.response;
      return {
        code: response.data?.error?.code || 'API_ERROR',
        message: response.data?.error?.message || response.statusText || '请求失败',
        details: response.data?.error?.details,
        statusCode: response.status,
        timestamp: new Date().toISOString(),
      };
    }

    // 如果是fetch错误
    if (error instanceof TypeError && error.message.includes('fetch')) {
      return {
        code: 'NETWORK_ERROR',
        message: '网络连接失败，请检查您的网络连接',
        statusCode: 0,
        timestamp: new Date().toISOString(),
      };
    }

    // 如果是字符串错误
    if (typeof error === 'string') {
      return {
        code: 'UNKNOWN_ERROR',
        message: error,
        timestamp: new Date().toISOString(),
      };
    }

    // 如果是Error对象
    if (error instanceof Error) {
      return {
        code: error.name || 'UNKNOWN_ERROR',
        message: error.message,
        details: error.stack,
        timestamp: new Date().toISOString(),
      };
    }

    // 默认错误
    return {
      code: 'UNKNOWN_ERROR',
      message: mergedOptions.fallbackMessage || '未知错误',
      timestamp: new Date().toISOString(),
    };
  }, [mergedOptions.fallbackMessage]);

  // 获取用户友好的错误消息
  const getUserFriendlyMessage = useCallback((error: AppError): string => {
    // 根据错误代码返回用户友好的消息
    const errorMessages: Record<string, string> = {
      'NETWORK_ERROR': '网络连接失败，请检查您的网络连接',
      'UNAUTHORIZED': '您的登录已过期，请重新登录',
      'FORBIDDEN': '您没有权限执行此操作',
      'NOT_FOUND': '请求的资源不存在',
      'VALIDATION_ERROR': '输入的信息有误，请检查后重试',
      'RATE_LIMIT_EXCEEDED': '操作过于频繁，请稍后重试',
      'SERVER_ERROR': '服务器暂时无法处理您的请求，请稍后重试',
      'DATABASE_ERROR': '数据处理失败，请稍后重试',
      'EXTERNAL_SERVICE_ERROR': '外部服务暂时不可用，请稍后重试',
    };

    return errorMessages[error.code || ''] || error.message;
  }, []);

  // 处理错误
  const handleError = useCallback((
    error: any, 
    options: ErrorHandlerOptions = {}
  ) => {
    const finalOptions = { ...mergedOptions, ...options };
    const parsedError = parseError(error);
    
    // 设置错误状态
    setError(parsedError);

    // 记录错误
    if (finalOptions.logError) {
      console.error('Error handled:', {
        error: parsedError,
        originalError: error,
        timestamp: new Date().toISOString(),
      });

      // 发送错误到监控服务
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', 'exception', {
          description: parsedError.message,
          fatal: false,
          custom_map: {
            error_code: parsedError.code,
            status_code: parsedError.statusCode,
          },
        });
      }
    }

    // 显示Toast通知
    if (finalOptions.showToast) {
      const userMessage = getUserFriendlyMessage(parsedError);
      
      if (parsedError.statusCode && parsedError.statusCode >= 500) {
        toast.error('服务器错误', {
          description: userMessage,
          action: finalOptions.retryable ? {
            label: '重试',
            onClick: () => retry(),
          } : undefined,
        });
      } else if (parsedError.code === 'NETWORK_ERROR') {
        toast.error('网络错误', {
          description: userMessage,
          action: {
            label: '重试',
            onClick: () => window.location.reload(),
          },
        });
      } else {
        toast.error('操作失败', {
          description: userMessage,
        });
      }
    }

    // 调用自定义错误处理函数
    if (finalOptions.onError) {
      finalOptions.onError(parsedError);
    }
  }, [mergedOptions, parseError, getUserFriendlyMessage]);

  // 重试功能
  const retry = useCallback(() => {
    if (retryFunctionRef.current) {
      setRetryCount(prev => prev + 1);
      clearError();
      retryFunctionRef.current();
    }
  }, [clearError]);

  // 设置重试函数
  const setRetryFunction = useCallback((fn: () => void) => {
    retryFunctionRef.current = fn;
  }, []);

  return {
    error,
    isError: error !== null,
    clearError,
    handleError,
    retryCount,
    canRetry: retryFunctionRef.current !== null,
    retry,
  };
}

/**
 * API错误处理Hook
 */
export function useApiErrorHandler() {
  const errorHandler = useErrorHandler({
    showToast: true,
    logError: true,
    retryable: true,
  });

  const handleApiError = useCallback((error: any, retryFn?: () => void) => {
    // 设置重试函数
    if (retryFn) {
      (errorHandler as any).setRetryFunction = retryFn;
    }

    errorHandler.handleError(error, {
      retryable: !!retryFn,
    });
  }, [errorHandler]);

  return {
    ...errorHandler,
    handleApiError,
  };
}

/**
 * 表单错误处理Hook
 */
export function useFormErrorHandler() {
  const errorHandler = useErrorHandler({
    showToast: false, // 表单错误通常在表单内显示
    logError: true,
    retryable: false,
  });

  const handleFormError = useCallback((error: any) => {
    errorHandler.handleError(error, {
      showToast: false,
    });
  }, [errorHandler]);

  return {
    ...errorHandler,
    handleFormError,
  };
}

/**
 * 网络错误处理Hook
 */
export function useNetworkErrorHandler() {
  const errorHandler = useErrorHandler({
    showToast: true,
    logError: true,
    retryable: true,
    fallbackMessage: '网络连接失败，请检查您的网络连接',
  });

  const handleNetworkError = useCallback((error: any, retryFn?: () => void) => {
    errorHandler.handleError(error, {
      retryable: !!retryFn,
      onError: (parsedError) => {
        // 网络错误的特殊处理
        if (parsedError.code === 'NETWORK_ERROR') {
          // 可以添加网络状态检查等逻辑
          console.warn('Network error detected, checking connection...');
        }
      },
    });
  }, [errorHandler]);

  return {
    ...errorHandler,
    handleNetworkError,
  };
}

/**
 * 全局错误边界Hook
 */
export function useGlobalErrorHandler() {
  const errorHandler = useErrorHandler({
    showToast: true,
    logError: true,
    retryable: false,
  });

  // 监听全局错误
  React.useEffect(() => {
    const handleGlobalError = (event: ErrorEvent) => {
      errorHandler.handleError(event.error || event.message);
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      errorHandler.handleError(event.reason);
    };

    window.addEventListener('error', handleGlobalError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('error', handleGlobalError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, [errorHandler]);

  return errorHandler;
}
