/**
 * 转账功能Hook
 * 处理HAOX和BNB的转账操作
 */

import { useState, useCallback } from 'react';
import { useTelegramAuth } from './useTelegramAuth';
import { useWalletBalance } from './useWalletBalance';
import { WalletService } from '@/services/wallet/WalletService';
import { BlockchainService } from '@/services/blockchain/BlockchainService';
import { AuditLogger, AuditLoggerConfig } from '@/services/wallet/AuditLogger';
import { Transaction, GasFeeEstimate } from '@/services/wallet/types';
import { useToast } from '@/components/ui';

interface TransferRequest {
  to: string;
  amount: string;
  tokenType: 'HAOX' | 'BNB';
}

interface UseTransferReturn {
  isTransferring: boolean;
  transferHAOX: (toAddress: string, amount: string, verificationCode?: string) => Promise<Transaction>;
  transferBNB: (toAddress: string, amount: string, verificationCode?: string) => Promise<Transaction>;
  estimateGasFee: (request: TransferRequest) => Promise<GasFeeEstimate>;
  error: string | null;
}

export const useTransfer = (): UseTransferReturn => {
  const { user, isAuthenticated } = useTelegramAuth();
  const { refreshBalance } = useWalletBalance(false);
  const { addToast } = useToast();
  const [isTransferring, setIsTransferring] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * 初始化服务
   */
  const initializeServices = useCallback(async () => {
    const auditLoggerConfig: AuditLoggerConfig = {
      logLevel: 'info',
      enableConsoleLogging: process.env.NODE_ENV === 'development',
      enableDatabaseLogging: true,
      enableCloudWatchLogging: process.env.NODE_ENV === 'production',
      retentionDays: 365,
      encryptLogs: true,
    };

    const auditLogger = new AuditLogger(auditLoggerConfig);
    const walletService = new WalletService();
    const blockchainService = new BlockchainService(auditLogger);

    await walletService.initialize();
    await blockchainService.initialize();

    return { walletService, blockchainService };
  }, []);

  /**
   * 转账HAOX代币
   */
  const transferHAOX = useCallback(async (
    toAddress: string,
    amount: string,
    verificationCode?: string
  ): Promise<Transaction> => {
    if (!isAuthenticated || !user) {
      throw new Error('请先登录');
    }

    try {
      setIsTransferring(true);
      setError(null);

      const { walletService } = await initializeServices();
      
      const transaction = await walletService.transferHAOX(
        user.id,
        toAddress,
        amount,
        verificationCode
      );

      // 刷新余额
      await refreshBalance();

      addToast({
        type: 'success',
        title: '转账成功',
        message: `成功发送 ${amount} HAOX 到 ${toAddress.slice(0, 6)}...${toAddress.slice(-4)}`,
      });

      return transaction;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '转账失败';
      setError(errorMessage);
      
      addToast({
        type: 'error',
        title: '转账失败',
        message: errorMessage,
      });
      
      throw err;
    } finally {
      setIsTransferring(false);
    }
  }, [isAuthenticated, user, initializeServices, refreshBalance, addToast]);

  /**
   * 转账BNB
   */
  const transferBNB = useCallback(async (
    toAddress: string,
    amount: string,
    verificationCode?: string
  ): Promise<Transaction> => {
    if (!isAuthenticated || !user) {
      throw new Error('请先登录');
    }

    try {
      setIsTransferring(true);
      setError(null);

      const { blockchainService } = await initializeServices();
      
      // 这里需要实现BNB转账逻辑
      // 暂时抛出错误，因为需要用户私钥
      throw new Error('BNB转账功能暂未实现');

      // const txHash = await blockchainService.sendBNB(
      //   privateKey, // 需要获取用户私钥
      //   toAddress,
      //   amount
      // );

      // // 创建交易记录
      // const transaction: Transaction = {
      //   id: `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      //   fromAddress: userWallet.address,
      //   toAddress,
      //   amount,
      //   tokenType: 'BNB',
      //   txHash,
      //   status: 'pending',
      //   type: 'transfer',
      //   createdAt: new Date(),
      //   metadata: {
      //     telegramUserId: user.id,
      //     verificationCode,
      //   },
      // };

      // return transaction;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'BNB转账失败';
      setError(errorMessage);
      
      addToast({
        type: 'error',
        title: 'BNB转账失败',
        message: errorMessage,
      });
      
      throw err;
    } finally {
      setIsTransferring(false);
    }
  }, [isAuthenticated, user, initializeServices, addToast]);

  /**
   * 估算Gas费用
   */
  const estimateGasFee = useCallback(async (request: TransferRequest): Promise<GasFeeEstimate> => {
    try {
      const { blockchainService } = await initializeServices();
      
      const estimate = await blockchainService.estimateGasFees({
        to: request.to,
        value: request.tokenType === 'BNB' ? request.amount : undefined,
        data: request.tokenType === 'HAOX' ? 'transfer_data' : undefined,
      });

      return estimate;
    } catch (err) {
      console.error('Failed to estimate gas fee:', err);
      
      // 返回默认估算值
      return {
        slow: {
          gasPrice: '5000000000', // 5 Gwei
          estimatedTime: 60,
          cost: '0.001',
        },
        standard: {
          gasPrice: '10000000000', // 10 Gwei
          estimatedTime: 30,
          cost: '0.002',
        },
        fast: {
          gasPrice: '20000000000', // 20 Gwei
          estimatedTime: 15,
          cost: '0.004',
        },
      };
    }
  }, [initializeServices]);

  return {
    isTransferring,
    transferHAOX,
    transferBNB,
    estimateGasFee,
    error,
  };
};

/**
 * 转账历史Hook
 */
export const useTransferHistory = () => {
  const { user, isAuthenticated } = useTelegramAuth();
  const [history, setHistory] = useState<Transaction[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const loadHistory = useCallback(async (page: number = 1, limit: number = 20) => {
    if (!isAuthenticated || !user) {
      setHistory([]);
      return;
    }

    try {
      setIsLoading(true);

      const walletService = new WalletService();
      await walletService.initialize();
      
      const transactions = await walletService.getUserTransactionHistory(user.id, page, limit);
      
      // 只显示转账相关的交易
      const transferTransactions = transactions.filter(tx => 
        tx.type === 'transfer' || tx.type === 'withdrawal'
      );
      
      setHistory(transferTransactions);
    } catch (error) {
      console.error('Failed to load transfer history:', error);
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, user]);

  return {
    history,
    isLoading,
    loadHistory,
  };
};

/**
 * 地址验证Hook
 */
export const useAddressValidation = () => {
  const validateBSCAddress = useCallback((address: string): {
    isValid: boolean;
    error?: string;
  } => {
    if (!address) {
      return { isValid: false };
    }

    // 检查地址格式
    if (!/^0x[a-fA-F0-9]{40}$/.test(address)) {
      return { 
        isValid: false, 
        error: '地址格式不正确，应为42位十六进制字符串' 
      };
    }

    // 检查校验和（简化版）
    const lowerAddress = address.toLowerCase();
    if (lowerAddress !== address && address.toUpperCase() !== address) {
      // 这里应该实现完整的EIP-55校验和验证
      // 暂时简化处理
    }

    return { isValid: true };
  }, []);

  const isContractAddress = useCallback(async (address: string): Promise<boolean> => {
    try {
      // 这里应该调用区块链服务检查是否为合约地址
      // 暂时返回false
      return false;
    } catch (error) {
      console.error('Failed to check contract address:', error);
      return false;
    }
  }, []);

  return {
    validateBSCAddress,
    isContractAddress,
  };
};

/**
 * 转账确认Hook
 */
export const useTransferConfirmation = () => {
  const [isConfirming, setIsConfirming] = useState(false);
  const [confirmationData, setConfirmationData] = useState<{
    to: string;
    amount: string;
    tokenType: 'HAOX' | 'BNB';
    gasFee: string;
  } | null>(null);

  const showConfirmation = useCallback((data: {
    to: string;
    amount: string;
    tokenType: 'HAOX' | 'BNB';
    gasFee: string;
  }) => {
    setConfirmationData(data);
    setIsConfirming(true);
  }, []);

  const hideConfirmation = useCallback(() => {
    setIsConfirming(false);
    setConfirmationData(null);
  }, []);

  return {
    isConfirming,
    confirmationData,
    showConfirmation,
    hideConfirmation,
  };
};
