'use client';

import { useState, useCallback, useRef, useEffect } from 'react';

// 加载状态的类型定义
export interface LoadingState {
  isLoading: boolean;
  error: Error | null;
  data: any;
  progress?: number;
}

// 加载配置选项
export interface LoadingOptions {
  // 最小加载时间（毫秒），防止闪烁
  minDuration?: number;
  // 超时时间（毫秒）
  timeout?: number;
  // 是否显示进度
  showProgress?: boolean;
  // 错误重试次数
  maxRetries?: number;
  // 重试延迟（毫秒）
  retryDelay?: number;
}

// 异步操作的返回类型
export interface AsyncOperation<T> {
  execute: (...args: any[]) => Promise<T>;
  isLoading: boolean;
  error: Error | null;
  data: T | null;
  progress: number;
  retry: () => Promise<T | null>;
  cancel: () => void;
  reset: () => void;
}

// 简单加载状态管理（保持向后兼容）
interface SimpleLoadingState {
  [key: string]: boolean;
}

interface UseSimpleLoadingStateReturn {
  isLoading: (key?: string) => boolean;
  setLoading: (key: string, loading: boolean) => void;
  startLoading: (key: string) => void;
  stopLoading: (key: string) => void;
  withLoading: <T>(key: string, asyncFn: () => Promise<T>) => Promise<T>;
  clearAll: () => void;
  loadingStates: SimpleLoadingState;
}

export const useLoadingState = (): UseSimpleLoadingStateReturn => {
  const [loadingStates, setLoadingStates] = useState<SimpleLoadingState>({});
  const timeoutsRef = useRef<{ [key: string]: NodeJS.Timeout }>({});

  const setLoading = useCallback((key: string, loading: boolean) => {
    // Clear any existing timeout for this key
    if (timeoutsRef.current[key]) {
      clearTimeout(timeoutsRef.current[key]);
      delete timeoutsRef.current[key];
    }

    setLoadingStates(prev => ({
      ...prev,
      [key]: loading,
    }));

    // Auto-clear loading state after 30 seconds to prevent stuck states
    if (loading) {
      timeoutsRef.current[key] = setTimeout(() => {
        setLoadingStates(prev => ({
          ...prev,
          [key]: false,
        }));
        delete timeoutsRef.current[key];
      }, 30000);
    }
  }, []);

  const startLoading = useCallback((key: string) => {
    setLoading(key, true);
  }, [setLoading]);

  const stopLoading = useCallback((key: string) => {
    setLoading(key, false);
  }, [setLoading]);

  const isLoading = useCallback((key?: string) => {
    if (key) {
      return loadingStates[key] || false;
    }
    // If no key provided, check if any loading state is true
    return Object.values(loadingStates).some(loading => loading);
  }, [loadingStates]);

  const withLoading = useCallback(async <T>(
    key: string,
    asyncFn: () => Promise<T>
  ): Promise<T> => {
    try {
      startLoading(key);
      const result = await asyncFn();
      return result;
    } finally {
      stopLoading(key);
    }
  }, [startLoading, stopLoading]);

  const clearAll = useCallback(() => {
    // Clear all timeouts
    Object.values(timeoutsRef.current).forEach(timeout => {
      clearTimeout(timeout);
    });
    timeoutsRef.current = {};
    
    setLoadingStates({});
  }, []);

  return {
    isLoading,
    setLoading,
    startLoading,
    stopLoading,
    withLoading,
    clearAll,
    loadingStates,
  };
};

/**
 * 增强的加载状态管理 Hook
 */
export function useEnhancedLoading<T = any>(
  asyncFn: (...args: any[]) => Promise<T>,
  options: LoadingOptions = {}
): AsyncOperation<T> {
  const {
    minDuration = 300,
    timeout = 30000,
    showProgress = false,
    maxRetries = 3,
    retryDelay = 1000,
  } = options;

  const [state, setState] = useState<LoadingState>({
    isLoading: false,
    error: null,
    data: null,
    progress: 0,
  });

  const retryCountRef = useRef(0);
  const timeoutRef = useRef<NodeJS.Timeout>();
  const cancelRef = useRef<boolean>(false);
  const lastArgsRef = useRef<any[]>([]);

  // 执行异步操作
  const execute = useCallback(async (...args: any[]): Promise<T> => {
    lastArgsRef.current = args;
    cancelRef.current = false;
    retryCountRef.current = 0;

    const startTime = Date.now();

    setState(prev => ({
      ...prev,
      isLoading: true,
      error: null,
      progress: showProgress ? 0 : prev.progress,
    }));

    try {
      // 设置超时
      const timeoutPromise = new Promise<never>((_, reject) => {
        timeoutRef.current = setTimeout(() => {
          reject(new Error(`操作超时 (${timeout}ms)`));
        }, timeout);
      });

      // 进度模拟（如果启用）
      let progressInterval: NodeJS.Timeout | undefined;
      if (showProgress) {
        progressInterval = setInterval(() => {
          setState(prev => ({
            ...prev,
            progress: Math.min(prev.progress + Math.random() * 10, 90),
          }));
        }, 100);
      }

      // 执行实际操作
      const result = await Promise.race([
        asyncFn(...args),
        timeoutPromise,
      ]);

      // 清理
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      if (progressInterval) {
        clearInterval(progressInterval);
      }

      // 检查是否被取消
      if (cancelRef.current) {
        throw new Error('操作已取消');
      }

      // 确保最小加载时间
      const elapsed = Date.now() - startTime;
      if (elapsed < minDuration) {
        await new Promise(resolve => setTimeout(resolve, minDuration - elapsed));
      }

      setState(prev => ({
        ...prev,
        isLoading: false,
        data: result,
        progress: showProgress ? 100 : prev.progress,
      }));

      return result;
    } catch (error) {
      // 清理
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      const errorObj = error instanceof Error ? error : new Error(String(error));

      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorObj,
        progress: showProgress ? 0 : prev.progress,
      }));

      throw errorObj;
    }
  }, [asyncFn, minDuration, timeout, showProgress]);

  // 重试操作
  const retry = useCallback(async (): Promise<T | null> => {
    if (retryCountRef.current >= maxRetries) {
      throw new Error(`已达到最大重试次数 (${maxRetries})`);
    }

    retryCountRef.current++;

    // 重试延迟
    if (retryDelay > 0) {
      await new Promise(resolve => setTimeout(resolve, retryDelay));
    }

    try {
      return await execute(...lastArgsRef.current);
    } catch (error) {
      if (retryCountRef.current < maxRetries) {
        return retry();
      }
      throw error;
    }
  }, [execute, maxRetries, retryDelay]);

  // 取消操作
  const cancel = useCallback(() => {
    cancelRef.current = true;
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setState(prev => ({
      ...prev,
      isLoading: false,
    }));
  }, []);

  // 重置状态
  const reset = useCallback(() => {
    cancel();
    setState({
      isLoading: false,
      error: null,
      data: null,
      progress: 0,
    });
    retryCountRef.current = 0;
  }, [cancel]);

  // 清理副作用
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return {
    execute,
    isLoading: state.isLoading,
    error: state.error,
    data: state.data,
    progress: state.progress,
    retry,
    cancel,
    reset,
  };
}
