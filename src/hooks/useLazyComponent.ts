'use client';

import { useState, useEffect, useRef, useCallback } from 'react';

// 懒加载组件的配置选项
interface LazyComponentOptions {
  // 是否在组件挂载时立即加载
  immediate?: boolean;
  // 延迟加载时间（毫秒）
  delay?: number;
  // 是否使用 Intersection Observer 进行可视区域检测
  useIntersectionObserver?: boolean;
  // Intersection Observer 的配置
  intersectionOptions?: IntersectionObserverInit;
}

// 懒加载组件的状态
interface LazyComponentState<T> {
  component: T | null;
  loading: boolean;
  error: Error | null;
  loaded: boolean;
}

// 懒加载组件的返回值
interface LazyComponentReturn<T> extends LazyComponentState<T> {
  load: () => Promise<void>;
  ref: React.RefObject<HTMLElement>;
}

/**
 * 懒加载组件的 Hook
 * @param importFn 动态导入函数
 * @param options 配置选项
 */
export function useLazyComponent<T = React.ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  options: LazyComponentOptions = {}
): LazyComponentReturn<T> {
  const {
    immediate = false,
    delay = 0,
    useIntersectionObserver = false,
    intersectionOptions = { threshold: 0.1 }
  } = options;

  const [state, setState] = useState<LazyComponentState<T>>({
    component: null,
    loading: false,
    error: null,
    loaded: false
  });

  const ref = useRef<HTMLElement>(null);
  const loadedRef = useRef(false);
  const timeoutRef = useRef<NodeJS.Timeout>();

  // 加载组件的函数
  const load = useCallback(async () => {
    if (loadedRef.current || state.loading) return;

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const delayPromise = delay > 0 ? new Promise(resolve => setTimeout(resolve, delay)) : Promise.resolve();
      
      const [module] = await Promise.all([
        importFn(),
        delayPromise
      ]);

      setState(prev => ({
        ...prev,
        component: module.default,
        loading: false,
        loaded: true
      }));
      
      loadedRef.current = true;
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: error as Error
      }));
    }
  }, [importFn, delay, state.loading]);

  // 使用 Intersection Observer 进行可视区域检测
  useEffect(() => {
    if (!useIntersectionObserver || !ref.current || loadedRef.current) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting) {
          load();
          observer.disconnect();
        }
      },
      intersectionOptions
    );

    observer.observe(ref.current);

    return () => {
      observer.disconnect();
    };
  }, [load, useIntersectionObserver, intersectionOptions]);

  // 立即加载或延迟加载
  useEffect(() => {
    if (immediate) {
      if (delay > 0) {
        timeoutRef.current = setTimeout(load, delay);
      } else {
        load();
      }
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [immediate, delay, load]);

  return {
    ...state,
    load,
    ref
  };
}

/**
 * 预加载组件的 Hook
 * @param importFns 动态导入函数数组
 */
export function usePreloadComponents(
  importFns: Array<() => Promise<{ default: any }>>
) {
  const [preloadedCount, setPreloadedCount] = useState(0);
  const [isPreloading, setIsPreloading] = useState(false);

  const preload = useCallback(async () => {
    if (isPreloading) return;

    setIsPreloading(true);
    let count = 0;

    try {
      await Promise.all(
        importFns.map(async (importFn) => {
          try {
            await importFn();
            count++;
            setPreloadedCount(count);
          } catch (error) {
            console.warn('Failed to preload component:', error);
          }
        })
      );
    } finally {
      setIsPreloading(false);
    }
  }, [importFns, isPreloading]);

  return {
    preload,
    preloadedCount,
    isPreloading,
    totalCount: importFns.length,
    progress: importFns.length > 0 ? (preloadedCount / importFns.length) * 100 : 0
  };
}

/**
 * 智能预加载 Hook - 基于用户行为预加载可能需要的组件
 */
export function useSmartPreload() {
  const [userActivity, setUserActivity] = useState({
    mouseMovements: 0,
    scrolls: 0,
    clicks: 0
  });

  useEffect(() => {
    let mouseTimer: NodeJS.Timeout;
    let scrollTimer: NodeJS.Timeout;

    const handleMouseMove = () => {
      clearTimeout(mouseTimer);
      mouseTimer = setTimeout(() => {
        setUserActivity(prev => ({ ...prev, mouseMovements: prev.mouseMovements + 1 }));
      }, 100);
    };

    const handleScroll = () => {
      clearTimeout(scrollTimer);
      scrollTimer = setTimeout(() => {
        setUserActivity(prev => ({ ...prev, scrolls: prev.scrolls + 1 }));
      }, 100);
    };

    const handleClick = () => {
      setUserActivity(prev => ({ ...prev, clicks: prev.clicks + 1 }));
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('scroll', handleScroll);
    document.addEventListener('click', handleClick);

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('scroll', handleScroll);
      document.removeEventListener('click', handleClick);
      clearTimeout(mouseTimer);
      clearTimeout(scrollTimer);
    };
  }, []);

  // 判断用户是否活跃，决定是否进行预加载
  const isUserActive = userActivity.mouseMovements > 5 || userActivity.scrolls > 3 || userActivity.clicks > 1;

  return {
    userActivity,
    isUserActive,
    shouldPreload: isUserActive
  };
}
