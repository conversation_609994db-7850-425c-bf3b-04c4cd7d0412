'use client';

import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/lib/supabase';

export interface Notification {
  id: string;
  type: 'transaction' | 'social' | 'merchant' | 'system';
  title: string;
  message: string;
  read: boolean;
  created_at: string;
  metadata?: Record<string, any>;
}

interface UseNotificationsReturn {
  notifications: Notification[];
  unreadCount: number;
  isLoading: boolean;
  markAsRead: (id: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  deleteNotification: (id: string) => Promise<void>;
  requestPermission: () => Promise<boolean>;
  sendPushNotification: (title: string, body: string, options?: NotificationOptions) => void;
}

export const useNotifications = (): UseNotificationsReturn => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load notifications
  const loadNotifications = useCallback(async () => {
    try {
      // Check if supabase client is available
      if (!supabase) {
        console.warn('Supabase client not available');
        setIsLoading(false);
        return;
      }

      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(50);

      if (error) throw error;
      setNotifications(data || []);
    } catch (error) {
      console.error('Failed to load notifications:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Subscribe to real-time notifications
  useEffect(() => {
    loadNotifications();

    // Check if supabase client is available
    if (!supabase) {
      return;
    }

    const channel = supabase
      .channel('notifications')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'notifications',
        },
        (payload) => {
          if (payload.eventType === 'INSERT') {
            setNotifications(prev => [payload.new as Notification, ...prev]);
            
            // Show browser notification if permission granted
            if (Notification.permission === 'granted') {
              const notification = payload.new as Notification;
              new Notification(notification.title, {
                body: notification.message,
                icon: '/icons/icon-192x192.png',
                badge: '/icons/icon-72x72.png',
                tag: notification.id,
              });
            }
          } else if (payload.eventType === 'UPDATE') {
            setNotifications(prev =>
              prev.map(n => n.id === payload.new.id ? payload.new as Notification : n)
            );
          } else if (payload.eventType === 'DELETE') {
            setNotifications(prev => prev.filter(n => n.id !== payload.old.id));
          }
        }
      )
      .subscribe();

    return () => {
      if (supabase) {
        supabase.removeChannel(channel);
      }
    };
  }, [loadNotifications]);

  // Mark notification as read
  const markAsRead = useCallback(async (id: string) => {
    try {
      if (!supabase) {
        console.warn('Supabase client not available');
        return;
      }

      const { error } = await supabase
        .from('notifications')
        .update({ read: true })
        .eq('id', id);

      if (error) throw error;

      setNotifications(prev =>
        prev.map(n => n.id === id ? { ...n, read: true } : n)
      );
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
    }
  }, []);

  // Mark all notifications as read
  const markAllAsRead = useCallback(async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const { error } = await supabase
        .from('notifications')
        .update({ read: true })
        .eq('user_id', user.id)
        .eq('read', false);

      if (error) throw error;

      setNotifications(prev =>
        prev.map(n => ({ ...n, read: true }))
      );
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
    }
  }, []);

  // Delete notification
  const deleteNotification = useCallback(async (id: string) => {
    try {
      const { error } = await supabase
        .from('notifications')
        .delete()
        .eq('id', id);

      if (error) throw error;

      setNotifications(prev => prev.filter(n => n.id !== id));
    } catch (error) {
      console.error('Failed to delete notification:', error);
    }
  }, []);

  // Request notification permission
  const requestPermission = useCallback(async (): Promise<boolean> => {
    if (!('Notification' in window)) {
      console.warn('This browser does not support notifications');
      return false;
    }

    if (Notification.permission === 'granted') {
      return true;
    }

    if (Notification.permission === 'denied') {
      return false;
    }

    const permission = await Notification.requestPermission();
    return permission === 'granted';
  }, []);

  // Send push notification
  const sendPushNotification = useCallback((
    title: string,
    body: string,
    options?: NotificationOptions
  ) => {
    if (Notification.permission === 'granted') {
      new Notification(title, {
        body,
        icon: '/icons/icon-192x192.png',
        badge: '/icons/icon-72x72.png',
        ...options,
      });
    }
  }, []);

  const unreadCount = notifications.filter(n => !n.read).length;

  return {
    notifications,
    unreadCount,
    isLoading,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    requestPermission,
    sendPushNotification,
  };
};

// Notification utility functions
export const notificationUtils = {
  /**
   * Create a new notification
   */
  async create(
    userId: string,
    type: Notification['type'],
    title: string,
    message: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    try {
      const { error } = await supabase
        .from('notifications')
        .insert({
          user_id: userId,
          type,
          title,
          message,
          metadata,
          read: false,
        });

      if (error) throw error;
    } catch (error) {
      console.error('Failed to create notification:', error);
    }
  },

  /**
   * Send transaction notification
   */
  async sendTransactionNotification(
    userId: string,
    transactionType: 'buy' | 'sell',
    amount: string,
    status: 'completed' | 'failed'
  ): Promise<void> {
    const title = status === 'completed' ? '交易成功' : '交易失败';
    const action = transactionType === 'buy' ? '购买' : '出售';
    const message = status === 'completed'
      ? `您的${action} ${amount} HAOX 交易已完成`
      : `您的${action} ${amount} HAOX 交易失败，请重试`;

    await this.create(userId, 'transaction', title, message, {
      transactionType,
      amount,
      status,
    });
  },

  /**
   * Send social task notification
   */
  async sendTaskNotification(
    userId: string,
    taskTitle: string,
    reward: number,
    completed: boolean
  ): Promise<void> {
    const title = completed ? '任务完成' : '新任务可用';
    const message = completed
      ? `恭喜！您完成了"${taskTitle}"任务，获得 ${reward} HAOX 奖励`
      : `新任务"${taskTitle}"现已可用，完成可获得 ${reward} HAOX`;

    await this.create(userId, 'social', title, message, {
      taskTitle,
      reward,
      completed,
    });
  },

  /**
   * Send merchant status notification
   */
  async sendMerchantNotification(
    userId: string,
    status: 'approved' | 'rejected',
    reason?: string
  ): Promise<void> {
    const title = status === 'approved' ? '商家认证通过' : '商家认证未通过';
    const message = status === 'approved'
      ? '恭喜！您的商家认证申请已通过，现在可以享受商家特权'
      : `很抱歉，您的商家认证申请未通过。${reason ? `原因：${reason}` : ''}`;

    await this.create(userId, 'merchant', title, message, {
      status,
      reason,
    });
  },
};
