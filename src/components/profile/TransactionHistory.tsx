'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, Button, Icon, Badge, Loading } from '@/components/ui';
import { 
  FinanceIcons, 
  ActionIcons, 
  FeatureIcons,
  NavigationIcons 
} from '@/config/icons';
import type { Transaction, UserProfile } from '@/types/user';
import { cn } from '@/lib/utils';

interface TransactionHistoryProps {
  user: UserProfile | null;
}

const TransactionHistory: React.FC<TransactionHistoryProps> = ({ user }) => {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'deposit' | 'withdraw' | 'transfer' | 'reward'>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  // 模拟获取交易数据
  useEffect(() => {
    const fetchTransactions = async () => {
      setIsLoading(true);
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockTransactions: Transaction[] = [
        {
          id: 'tx_001',
          type: 'reward',
          amount: '5000',
          token: 'HAOX',
          status: 'confirmed',
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
          txHash: '0xabc123...'
        },
        {
          id: 'tx_002',
          type: 'transfer',
          amount: '10000',
          token: 'HAOX',
          to: '0x1234...5678',
          status: 'confirmed',
          timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000),
          txHash: '0xdef456...',
          gasUsed: '21000',
          gasFee: '0.002'
        },
        {
          id: 'tx_003',
          type: 'deposit',
          amount: '50000',
          token: 'HAOX',
          from: '0x9876...4321',
          status: 'confirmed',
          timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000),
          txHash: '0x789xyz...'
        },
        {
          id: 'tx_004',
          type: 'withdraw',
          amount: '25000',
          token: 'HAOX',
          to: '0x5555...6666',
          status: 'pending',
          timestamp: new Date(Date.now() - 30 * 60 * 1000),
          txHash: '0x111aaa...'
        },
        {
          id: 'tx_005',
          type: 'reward',
          amount: '3000',
          token: 'HAOX',
          status: 'confirmed',
          timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
          txHash: '0x222bbb...'
        }
      ];
      
      setTransactions(mockTransactions);
      setIsLoading(false);
    };

    fetchTransactions();
  }, []);

  const getTransactionIcon = (type: Transaction['type']) => {
    switch (type) {
      case 'deposit':
        return ActionIcons.receive;
      case 'withdraw':
        return ActionIcons.send;
      case 'transfer':
        return ActionIcons.exchange;
      case 'reward':
        return RewardIcons.gift;
      default:
        return FinanceIcons.coins;
    }
  };

  const getTransactionColor = (type: Transaction['type']) => {
    switch (type) {
      case 'deposit':
        return 'text-system-green';
      case 'withdraw':
        return 'text-system-red';
      case 'transfer':
        return 'text-system-blue';
      case 'reward':
        return 'text-system-purple';
      default:
        return 'text-label';
    }
  };

  const getStatusBadge = (status: Transaction['status']) => {
    switch (status) {
      case 'confirmed':
        return <Badge variant="success">已确认</Badge>;
      case 'pending':
        return <Badge variant="warning">处理中</Badge>;
      case 'failed':
        return <Badge variant="error">失败</Badge>;
      default:
        return <Badge variant="default">{status}</Badge>;
    }
  };

  const formatDate = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 60) {
      return `${minutes}分钟前`;
    } else if (hours < 24) {
      return `${hours}小时前`;
    } else if (days < 7) {
      return `${days}天前`;
    } else {
      return date.toLocaleDateString('zh-CN');
    }
  };

  const filteredTransactions = transactions.filter(tx => 
    filter === 'all' || tx.type === filter
  );

  const paginatedTransactions = filteredTransactions.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const totalPages = Math.ceil(filteredTransactions.length / itemsPerPage);

  const filters = [
    { key: 'all' as const, label: '全部', count: transactions.length },
    { key: 'deposit' as const, label: '充值', count: transactions.filter(tx => tx.type === 'deposit').length },
    { key: 'withdraw' as const, label: '提现', count: transactions.filter(tx => tx.type === 'withdraw').length },
    { key: 'transfer' as const, label: '转账', count: transactions.filter(tx => tx.type === 'transfer').length },
    { key: 'reward' as const, label: '奖励', count: transactions.filter(tx => tx.type === 'reward').length }
  ];

  if (isLoading) {
    return (
      <Card title="交易历史">
        <div className="p-6 text-center">
          <Loading size="lg" />
          <p className="text-body text-secondary-label mt-4">
            正在加载交易记录...
          </p>
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* 筛选器 */}
      <Card>
        <div className="p-6">
          <div className="flex flex-wrap gap-2">
            {filters.map((filterOption) => (
              <Button
                key={filterOption.key}
                variant={filter === filterOption.key ? 'primary' : 'outline'}
                size="sm"
                onClick={() => {
                  setFilter(filterOption.key);
                  setCurrentPage(1);
                }}
                className="flex items-center space-x-1"
              >
                <span>{filterOption.label}</span>
                <span className="text-xs opacity-75">({filterOption.count})</span>
              </Button>
            ))}
          </div>
        </div>
      </Card>

      {/* 交易列表 */}
      <Card title="交易记录">
        <div className="divide-y divide-system-gray-4">
          {paginatedTransactions.length === 0 ? (
            <div className="p-6 text-center">
              <Icon icon={FeatureIcons.history} size="xl" color="muted" />
              <p className="text-body text-secondary-label mt-4">
                暂无交易记录
              </p>
            </div>
          ) : (
            paginatedTransactions.map((transaction, index) => (
              <motion.div
                key={transaction.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
                className="p-6 hover:bg-system-gray-6 transition-colors"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className={cn(
                      'w-10 h-10 rounded-lg flex items-center justify-center',
                      'bg-system-gray-6'
                    )}>
                      <Icon 
                        icon={getTransactionIcon(transaction.type)} 
                        size="md" 
                        className={getTransactionColor(transaction.type)}
                      />
                    </div>

                    <div>
                      <div className="flex items-center space-x-2 mb-1">
                        <p className="text-body font-sf-pro font-medium text-label">
                          {transaction.type === 'deposit' && '充值'}
                          {transaction.type === 'withdraw' && '提现'}
                          {transaction.type === 'transfer' && '转账'}
                          {transaction.type === 'reward' && '奖励'}
                        </p>
                        {getStatusBadge(transaction.status)}
                      </div>
                      
                      <div className="flex items-center space-x-4 text-caption-1 text-secondary-label">
                        <span>{formatDate(transaction.timestamp)}</span>
                        {transaction.txHash && (
                          <button
                            onClick={() => window.open(`https://bscscan.com/tx/${transaction.txHash}`, '_blank')}
                            className="flex items-center space-x-1 hover:text-system-blue transition-colors"
                          >
                            <span>查看详情</span>
                            <Icon icon={NavigationIcons.externalLink} size="xs" />
                          </button>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="text-right">
                    <p className={cn(
                      'text-body font-sf-pro font-medium',
                      transaction.type === 'deposit' || transaction.type === 'reward' 
                        ? 'text-system-green' 
                        : 'text-system-red'
                    )}>
                      {transaction.type === 'deposit' || transaction.type === 'reward' ? '+' : '-'}
                      {parseFloat(transaction.amount).toLocaleString()} {transaction.token}
                    </p>
                    
                    {transaction.gasFee && (
                      <p className="text-caption-1 text-secondary-label">
                        Gas: {transaction.gasFee} ETH
                      </p>
                    )}
                  </div>
                </div>
              </motion.div>
            ))
          )}
        </div>

        {/* 分页 */}
        {totalPages > 1 && (
          <div className="p-6 border-t border-system-gray-4">
            <div className="flex items-center justify-between">
              <p className="text-caption-1 text-secondary-label">
                显示 {(currentPage - 1) * itemsPerPage + 1} - {Math.min(currentPage * itemsPerPage, filteredTransactions.length)} 条，
                共 {filteredTransactions.length} 条记录
              </p>
              
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                >
                  <Icon icon={NavigationIcons.chevronLeft} size="sm" />
                </Button>
                
                <span className="text-sm text-label px-3">
                  {currentPage} / {totalPages}
                </span>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                >
                  <Icon icon={NavigationIcons.chevronRight} size="sm" />
                </Button>
              </div>
            </div>
          </div>
        )}
      </Card>
    </div>
  );
};

export default TransactionHistory;
