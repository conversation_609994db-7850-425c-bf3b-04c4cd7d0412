'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, Button, Icon, Badge, Input } from '@/components/ui';
import {
  UserIcons,
  ActionIcons,
  FinanceIcons,
  SocialIcons,
  FeatureIcons,
  RewardIcons
} from '@/config/icons';
import type { InvitationData, UserProfile } from '@/types/user';
import { cn } from '@/lib/utils';

interface InvitationSystemProps {
  user: UserProfile | null;
}

const InvitationSystem: React.FC<InvitationSystemProps> = ({ user }) => {
  const [invitationData, setInvitationData] = useState<InvitationData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isCopied, setIsCopied] = useState(false);

  // 模拟获取邀请数据
  useEffect(() => {
    const fetchInvitationData = async () => {
      setIsLoading(true);
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockData: InvitationData = {
        inviteCode: `HAOX${user?.address?.slice(-6).toUpperCase() || 'ABC123'}`,
        invitedCount: Math.floor(Math.random() * 50) + 10,
        totalRewards: (Math.random() * 10000 + 5000).toFixed(0),
        pendingRewards: (Math.random() * 1000 + 500).toFixed(0),
        invitees: [
          {
            address: '0x1234...5678',
            nickname: 'CryptoTrader',
            joinedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
            reward: '500',
            status: 'completed'
          },
          {
            address: '0x9876...4321',
            nickname: 'BlockchainFan',
            joinedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
            reward: '500',
            status: 'completed'
          },
          {
            address: '0x5555...6666',
            joinedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
            reward: '500',
            status: 'pending'
          }
        ]
      };
      
      setInvitationData(mockData);
      setIsLoading(false);
    };

    if (user) {
      fetchInvitationData();
    }
  }, [user]);

  const copyInviteCode = async () => {
    if (invitationData?.inviteCode) {
      try {
        await navigator.clipboard.writeText(invitationData.inviteCode);
        setIsCopied(true);
        setTimeout(() => setIsCopied(false), 2000);
      } catch (error) {
        console.error('Failed to copy invite code:', error);
      }
    }
  };

  const shareInviteLink = async (platform: 'twitter' | 'telegram' | 'copy') => {
    const inviteLink = `https://sociomint.com/invite/${invitationData?.inviteCode}`;
    const shareText = `🚀 加入 SocioMint，开启您的加密货币交易之旅！\n\n使用我的邀请码：${invitationData?.inviteCode}\n立即获得奖励！\n\n${inviteLink}`;

    switch (platform) {
      case 'twitter':
        window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}`, '_blank');
        break;
      case 'telegram':
        window.open(`https://t.me/share/url?url=${encodeURIComponent(inviteLink)}&text=${encodeURIComponent(shareText)}`, '_blank');
        break;
      case 'copy':
        try {
          await navigator.clipboard.writeText(shareText);
          // 可以添加成功提示
        } catch (error) {
          console.error('Failed to copy share text:', error);
        }
        break;
    }
  };

  if (isLoading) {
    return (
      <Card title="邀请系统">
        <div className="p-6 text-center">
          <p className="text-body text-secondary-label">
            正在加载邀请数据...
          </p>
        </div>
      </Card>
    );
  }

  if (!invitationData) {
    return (
      <Card title="邀请系统">
        <div className="p-6 text-center">
          <p className="text-body text-secondary-label">
            无法加载邀请数据
          </p>
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* 邀请概览 */}
      <Card className="bg-gradient-to-r from-system-purple/5 to-system-blue/5 border-system-purple/20">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-gradient-to-br from-system-purple to-system-blue rounded-xl flex items-center justify-center">
                <Icon icon={UserIcons.userPlus} size="lg" className="text-white" />
              </div>
              <div>
                <h3 className="text-title-3 font-sf-pro font-bold text-label">
                  邀请好友
                </h3>
                <p className="text-caption-1 text-secondary-label">
                  邀请朋友加入，共同获得奖励
                </p>
              </div>
            </div>
          </div>

          <div className="grid md:grid-cols-3 gap-6">
            <div className="text-center">
              <p className="text-title-2 font-sf-pro font-bold text-system-purple">
                {invitationData.invitedCount}
              </p>
              <p className="text-caption-1 text-secondary-label">已邀请人数</p>
            </div>
            
            <div className="text-center">
              <p className="text-title-2 font-sf-pro font-bold text-system-green">
                {parseFloat(invitationData.totalRewards).toLocaleString()}
              </p>
              <p className="text-caption-1 text-secondary-label">总奖励 (HAOX)</p>
            </div>
            
            <div className="text-center">
              <p className="text-title-2 font-sf-pro font-bold text-system-orange">
                {parseFloat(invitationData.pendingRewards).toLocaleString()}
              </p>
              <p className="text-caption-1 text-secondary-label">待领取 (HAOX)</p>
            </div>
          </div>
        </div>
      </Card>

      {/* 邀请码和分享 */}
      <Card title="我的邀请码">
        <div className="p-6 space-y-6">
          <div className="bg-system-gray-6 rounded-xl p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-sf-pro font-medium text-label mb-1">
                  邀请码
                </p>
                <p className="text-title-3 font-mono font-bold text-system-blue">
                  {invitationData.inviteCode}
                </p>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={copyInviteCode}
                className="flex items-center space-x-2"
              >
                <Icon icon={isCopied ? ActionIcons.check : ActionIcons.copy} size="sm" />
                <span>{isCopied ? '已复制' : '复制'}</span>
              </Button>
            </div>
          </div>

          <div>
            <p className="text-sm font-sf-pro font-medium text-label mb-3">
              分享邀请链接
            </p>
            <div className="grid grid-cols-3 gap-3">
              <Button
                variant="outline"
                size="md"
                onClick={() => shareInviteLink('twitter')}
                className="flex flex-col items-center space-y-2 h-auto py-4"
              >
                <Icon icon={SocialIcons.twitter} size="lg" color="primary" />
                <span className="text-sm">Twitter</span>
              </Button>

              <Button
                variant="outline"
                size="md"
                onClick={() => shareInviteLink('telegram')}
                className="flex flex-col items-center space-y-2 h-auto py-4"
              >
                <Icon icon={SocialIcons.telegram} size="lg" color="primary" />
                <span className="text-sm">Telegram</span>
              </Button>

              <Button
                variant="outline"
                size="md"
                onClick={() => shareInviteLink('copy')}
                className="flex flex-col items-center space-y-2 h-auto py-4"
              >
                <Icon icon={ActionIcons.copy} size="lg" color="muted" />
                <span className="text-sm">复制链接</span>
              </Button>
            </div>
          </div>

          <div className="bg-system-blue/10 border border-system-blue/20 rounded-xl p-4">
            <div className="flex items-start space-x-3">
              <Icon icon={ActionIcons.info} size="sm" color="primary" className="mt-0.5" />
              <div>
                <p className="text-sm font-sf-pro font-medium text-label mb-1">
                  邀请奖励规则（已更新）
                </p>
                <ul className="text-xs text-secondary-label space-y-1">
                  <li>• 每成功邀请一位用户参与预售（≥0.1 BNB），您将获得 1,000 HAOX 奖励</li>
                  <li>• 邀请满5人：额外获得 10,000 HAOX 里程碑奖励</li>
                  <li>• 邀请满10人：额外获得 50,000 HAOX 里程碑奖励</li>
                  <li>• 排行榜奖励：预售结束后根据排名发放（最高100万HAOX）</li>
                  <li>• 奖励在预售结束后可在"我的奖励"页面领取</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* 邀请记录 */}
      <Card title="邀请记录">
        <div className="divide-y divide-system-gray-4">
          {invitationData.invitees.length === 0 ? (
            <div className="p-6 text-center">
              <Icon icon={UserIcons.users} size="xl" color="muted" />
              <p className="text-body text-secondary-label mt-4">
                还没有邀请记录
              </p>
              <p className="text-caption-1 text-secondary-label mt-1">
                分享您的邀请码，开始赚取奖励！
              </p>
            </div>
          ) : (
            invitationData.invitees.map((invitee, index) => (
              <motion.div
                key={invitee.address}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="p-6"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-gradient-to-br from-system-blue to-system-purple rounded-full flex items-center justify-center">
                      <Icon icon={UserIcons.user} size="sm" className="text-white" />
                    </div>
                    
                    <div>
                      <p className="text-body font-sf-pro font-medium text-label">
                        {invitee.nickname || '匿名用户'}
                      </p>
                      <p className="text-caption-1 text-secondary-label">
                        {invitee.address}
                      </p>
                      <p className="text-caption-2 text-tertiary-label">
                        {invitee.joinedAt.toLocaleDateString('zh-CN')} 加入
                      </p>
                    </div>
                  </div>

                  <div className="text-right">
                    <div className="flex items-center space-x-2 mb-1">
                      <Badge variant={invitee.status === 'completed' ? 'success' : 'warning'}>
                        {invitee.status === 'completed' ? '已完成' : '待完成'}
                      </Badge>
                    </div>
                    <p className="text-sm font-sf-pro font-medium text-system-green">
                      +{parseFloat(invitee.reward).toLocaleString()} HAOX
                    </p>
                  </div>
                </div>
              </motion.div>
            ))
          )}
        </div>
      </Card>
    </div>
  );
};

export default InvitationSystem;
