'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Card, Icon, Button } from '@/components/ui';
import { 
  FinanceIcons, 
  ActionIcons, 
  RewardIcons,
  FeatureIcons 
} from '@/config/icons';
import type { UserAssets, UserProfile } from '@/types/user';
import { cn } from '@/lib/utils';

interface AssetOverviewProps {
  assets: UserAssets | null;
  user: UserProfile | null;
}

const AssetOverview: React.FC<AssetOverviewProps> = ({ assets, user }) => {
  const formatNumber = (value: string | number) => {
    const num = typeof value === 'string' ? parseFloat(value) : value;
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(2)}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(2)}K`;
    }
    return num.toLocaleString();
  };

  const formatCurrency = (value: string) => {
    return parseFloat(value).toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  };

  if (!assets) {
    return (
      <div className="space-y-6">
        <Card title="资产概览">
          <div className="p-6 text-center">
            <p className="text-body text-secondary-label">
              正在加载资产数据...
            </p>
          </div>
        </Card>
      </div>
    );
  }

  const totalBalance = parseFloat(assets.haoxBalance.available) + 
                      parseFloat(assets.haoxBalance.locked) + 
                      parseFloat(assets.haoxBalance.pending);

  const assetCards = [
    {
      title: '可用余额',
      value: assets.haoxBalance.available,
      icon: FinanceIcons.wallet,
      color: 'text-system-green',
      bgColor: 'bg-system-green/10',
      borderColor: 'border-system-green/20',
      description: '可自由使用的 HAOX 代币'
    },
    {
      title: '锁定余额',
      value: assets.haoxBalance.locked,
      icon: FeatureIcons.lock,
      color: 'text-system-orange',
      bgColor: 'bg-system-orange/10',
      borderColor: 'border-system-orange/20',
      description: '质押或锁定中的代币'
    },
    {
      title: '待领取奖励',
      value: assets.haoxBalance.pending,
      icon: RewardIcons.gift,
      color: 'text-system-purple',
      bgColor: 'bg-system-purple/10',
      borderColor: 'border-system-purple/20',
      description: '完成任务获得的奖励'
    }
  ];

  return (
    <div className="space-y-6">
      {/* 总资产卡片 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card className="bg-gradient-to-br from-system-blue/5 to-system-purple/5 border-system-blue/20">
          <div className="p-6">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-gradient-to-br from-system-blue to-system-purple rounded-xl flex items-center justify-center">
                  <Icon icon={FinanceIcons.coins} size="lg" className="text-white" />
                </div>
                <div>
                  <h3 className="text-title-3 font-sf-pro font-bold text-label">
                    总资产
                  </h3>
                  <p className="text-caption-1 text-secondary-label">
                    Total Assets
                  </p>
                </div>
              </div>
              
              <Button variant="outline" size="sm">
                <Icon icon={ActionIcons.refresh} size="sm" className="mr-2" />
                刷新
              </Button>
            </div>

            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <p className="text-caption-1 text-secondary-label mb-2">
                  HAOX 代币总量
                </p>
                <p className="text-large-title font-sf-pro font-bold text-label">
                  {formatNumber(totalBalance)}
                </p>
                <p className="text-caption-1 text-secondary-label mt-1">
                  HAOX
                </p>
              </div>

              <div>
                <p className="text-caption-1 text-secondary-label mb-2">
                  估值
                </p>
                <div className="space-y-1">
                  <p className="text-title-2 font-sf-pro font-bold text-label">
                    ${formatCurrency(assets.usdValue)}
                  </p>
                  <p className="text-body text-secondary-label">
                    ¥{formatCurrency(assets.cnyValue)}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </Card>
      </motion.div>

      {/* 资产分布 */}
      <div className="grid md:grid-cols-3 gap-6">
        {assetCards.map((card, index) => (
          <motion.div
            key={card.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
          >
            <Card className={cn('border', card.borderColor, card.bgColor)}>
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className={cn(
                    'w-10 h-10 rounded-lg flex items-center justify-center',
                    card.bgColor
                  )}>
                    <Icon icon={card.icon} size="md" className={card.color} />
                  </div>
                  
                  {card.title === '待领取奖励' && parseFloat(card.value) > 0 && (
                    <Button variant="primary" size="sm">
                      领取
                    </Button>
                  )}
                </div>

                <div>
                  <p className="text-caption-1 text-secondary-label mb-1">
                    {card.title}
                  </p>
                  <p className="text-title-2 font-sf-pro font-bold text-label">
                    {formatNumber(card.value)}
                  </p>
                  <p className="text-caption-1 text-secondary-label mt-1">
                    HAOX
                  </p>
                </div>

                <p className="text-caption-2 text-tertiary-label mt-3">
                  {card.description}
                </p>
              </div>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* 快速操作 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.4 }}
      >
        <Card title="快速操作">
          <div className="p-6">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Button
                variant="outline"
                size="lg"
                className="flex flex-col items-center space-y-2 h-auto py-4"
              >
                <Icon icon={ActionIcons.send} size="lg" color="primary" />
                <span className="text-sm">发送</span>
              </Button>

              <Button
                variant="outline"
                size="lg"
                className="flex flex-col items-center space-y-2 h-auto py-4"
              >
                <Icon icon={ActionIcons.receive} size="lg" color="success" />
                <span className="text-sm">接收</span>
              </Button>

              <Button
                variant="outline"
                size="lg"
                className="flex flex-col items-center space-y-2 h-auto py-4"
              >
                <Icon icon={FinanceIcons.exchange} size="lg" color="warning" />
                <span className="text-sm">兑换</span>
              </Button>

              <Button
                variant="outline"
                size="lg"
                className="flex flex-col items-center space-y-2 h-auto py-4"
              >
                <Icon icon={FeatureIcons.history} size="lg" color="muted" />
                <span className="text-sm">历史</span>
              </Button>
            </div>
          </div>
        </Card>
      </motion.div>

      {/* 资产统计图表占位符 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.5 }}
      >
        <Card title="资产趋势">
          <div className="p-6">
            <div className="h-64 bg-system-gray-6 rounded-xl flex items-center justify-center">
              <div className="text-center">
                <Icon icon={FinanceIcons.lineChart} size="xl" color="muted" />
                <p className="text-body text-secondary-label mt-2">
                  图表功能开发中
                </p>
              </div>
            </div>
          </div>
        </Card>
      </motion.div>
    </div>
  );
};

export default AssetOverview;
