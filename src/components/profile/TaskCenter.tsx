'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, Button, Icon, Badge, Progress } from '@/components/ui';
import { 
  FeatureIcons, 
  ActionIcons, 
  SocialIcons,
  RewardIcons,
  FinanceIcons,
  UserIcons 
} from '@/config/icons';
import type { Task, UserProfile } from '@/types/user';
import { cn } from '@/lib/utils';

interface TaskCenterProps {
  user: UserProfile | null;
}

const TaskCenter: React.FC<TaskCenterProps> = ({ user }) => {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'available' | 'completed'>('all');

  // 模拟获取任务数据
  useEffect(() => {
    const fetchTasks = async () => {
      setIsLoading(true);
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockTasks: Task[] = [
        {
          id: 'task_001',
          title: '完成首次交易',
          description: '在平台上完成您的第一笔交易，体验 SocioMint 的交易功能',
          type: 'trading',
          reward: '1000',
          status: 'completed',
          requirements: ['连接钱包', '完成KYC验证', '进行一笔交易']
        },
        {
          id: 'task_002',
          title: '绑定 Twitter 账户',
          description: '绑定您的 Twitter 账户，获得社交媒体奖励',
          type: 'social',
          reward: '500',
          status: 'available',
          requirements: ['拥有 Twitter 账户', '关注官方账户', '完成验证']
        },
        {
          id: 'task_003',
          title: '邀请 5 位朋友',
          description: '邀请 5 位朋友加入 SocioMint，共同获得奖励',
          type: 'referral',
          reward: '2500',
          status: 'in_progress',
          progress: {
            current: 2,
            target: 5
          },
          requirements: ['分享邀请链接', '朋友完成注册', '朋友完成首次交易']
        },
        {
          id: 'task_004',
          title: '每日签到',
          description: '连续 7 天登录平台，获得每日奖励',
          type: 'daily',
          reward: '100',
          status: 'available',
          progress: {
            current: 3,
            target: 7
          },
          deadline: new Date(Date.now() + 4 * 24 * 60 * 60 * 1000),
          requirements: ['每日登录', '保持连续性']
        },
        {
          id: 'task_005',
          title: '加入 Telegram 群组',
          description: '加入官方 Telegram 群组，获取最新资讯和奖励',
          type: 'social',
          reward: '300',
          status: 'available',
          requirements: ['拥有 Telegram 账户', '加入官方群组', '完成验证']
        },
        {
          id: 'task_006',
          title: '交易量达到 10,000 HAOX',
          description: '累计交易量达到 10,000 HAOX，获得交易大师徽章',
          type: 'trading',
          reward: '5000',
          status: 'in_progress',
          progress: {
            current: 6500,
            target: 10000
          },
          requirements: ['完成多笔交易', '累计交易量']
        }
      ];
      
      setTasks(mockTasks);
      setIsLoading(false);
    };

    fetchTasks();
  }, []);

  const getTaskIcon = (type: Task['type']) => {
    switch (type) {
      case 'social':
        return SocialIcons.share;
      case 'trading':
        return FinanceIcons.trendUp;
      case 'referral':
        return UserIcons.userPlus;
      case 'daily':
        return FeatureIcons.calendar;
      default:
        return FeatureIcons.target;
    }
  };

  const getTaskColor = (type: Task['type']) => {
    switch (type) {
      case 'social':
        return 'text-system-blue';
      case 'trading':
        return 'text-system-green';
      case 'referral':
        return 'text-system-purple';
      case 'daily':
        return 'text-system-orange';
      default:
        return 'text-label';
    }
  };

  const getStatusBadge = (status: Task['status']) => {
    switch (status) {
      case 'available':
        return <Badge variant="default">可完成</Badge>;
      case 'in_progress':
        return <Badge variant="warning">进行中</Badge>;
      case 'completed':
        return <Badge variant="success">已完成</Badge>;
      case 'claimed':
        return <Badge variant="success">已领取</Badge>;
      default:
        return <Badge variant="default">{status}</Badge>;
    }
  };

  const handleTaskAction = async (task: Task) => {
    if (task.status === 'available') {
      // 开始任务
      setTasks(prev => prev.map(t => 
        t.id === task.id ? { ...t, status: 'in_progress' } : t
      ));
    } else if (task.status === 'completed') {
      // 领取奖励
      setTasks(prev => prev.map(t => 
        t.id === task.id ? { ...t, status: 'claimed' } : t
      ));
    }
  };

  const filteredTasks = tasks.filter(task => {
    if (filter === 'all') return true;
    if (filter === 'available') return task.status === 'available' || task.status === 'in_progress';
    if (filter === 'completed') return task.status === 'completed' || task.status === 'claimed';
    return true;
  });

  const taskStats = {
    total: tasks.length,
    completed: tasks.filter(t => t.status === 'completed' || t.status === 'claimed').length,
    inProgress: tasks.filter(t => t.status === 'in_progress').length,
    totalRewards: tasks
      .filter(t => t.status === 'completed' || t.status === 'claimed')
      .reduce((sum, t) => sum + parseFloat(t.reward), 0)
  };

  if (isLoading) {
    return (
      <Card title="任务中心">
        <div className="p-6 text-center">
          <p className="text-body text-secondary-label">
            正在加载任务数据...
          </p>
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* 任务统计 */}
      <Card className="bg-gradient-to-r from-system-green/5 to-system-blue/5 border-system-green/20">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-gradient-to-br from-system-green to-system-blue rounded-xl flex items-center justify-center">
                <Icon icon={FeatureIcons.target} size="lg" className="text-white" />
              </div>
              <div>
                <h3 className="text-title-3 font-sf-pro font-bold text-label">
                  任务中心
                </h3>
                <p className="text-caption-1 text-secondary-label">
                  完成任务，获得 HAOX 奖励
                </p>
              </div>
            </div>
          </div>

          <div className="grid md:grid-cols-4 gap-6">
            <div className="text-center">
              <p className="text-title-2 font-sf-pro font-bold text-system-blue">
                {taskStats.total}
              </p>
              <p className="text-caption-1 text-secondary-label">总任务数</p>
            </div>
            
            <div className="text-center">
              <p className="text-title-2 font-sf-pro font-bold text-system-green">
                {taskStats.completed}
              </p>
              <p className="text-caption-1 text-secondary-label">已完成</p>
            </div>
            
            <div className="text-center">
              <p className="text-title-2 font-sf-pro font-bold text-system-orange">
                {taskStats.inProgress}
              </p>
              <p className="text-caption-1 text-secondary-label">进行中</p>
            </div>
            
            <div className="text-center">
              <p className="text-title-2 font-sf-pro font-bold text-system-purple">
                {taskStats.totalRewards.toLocaleString()}
              </p>
              <p className="text-caption-1 text-secondary-label">已获得奖励</p>
            </div>
          </div>
        </div>
      </Card>

      {/* 筛选器 */}
      <Card>
        <div className="p-6">
          <div className="flex space-x-2">
            <Button
              variant={filter === 'all' ? 'primary' : 'outline'}
              size="sm"
              onClick={() => setFilter('all')}
            >
              全部任务
            </Button>
            <Button
              variant={filter === 'available' ? 'primary' : 'outline'}
              size="sm"
              onClick={() => setFilter('available')}
            >
              可完成
            </Button>
            <Button
              variant={filter === 'completed' ? 'primary' : 'outline'}
              size="sm"
              onClick={() => setFilter('completed')}
            >
              已完成
            </Button>
          </div>
        </div>
      </Card>

      {/* 任务列表 */}
      <div className="space-y-4">
        {filteredTasks.map((task, index) => (
          <motion.div
            key={task.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
          >
            <Card className="hover:shadow-apple-lg transition-shadow">
              <div className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4 flex-1">
                    <div className={cn(
                      'w-12 h-12 rounded-xl flex items-center justify-center',
                      'bg-system-gray-6'
                    )}>
                      <Icon 
                        icon={getTaskIcon(task.type)} 
                        size="lg" 
                        className={getTaskColor(task.type)}
                      />
                    </div>

                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <h4 className="text-headline font-sf-pro font-semibold text-label">
                          {task.title}
                        </h4>
                        {getStatusBadge(task.status)}
                      </div>
                      
                      <p className="text-body text-secondary-label mb-3">
                        {task.description}
                      </p>

                      {task.progress && (
                        <div className="mb-3">
                          <div className="flex items-center justify-between mb-1">
                            <span className="text-sm text-secondary-label">进度</span>
                            <span className="text-sm font-sf-pro font-medium text-label">
                              {task.progress.current} / {task.progress.target}
                            </span>
                          </div>
                          <Progress 
                            value={(task.progress.current / task.progress.target) * 100}
                            className="h-2"
                          />
                        </div>
                      )}

                      {task.deadline && (
                        <p className="text-caption-1 text-system-orange">
                          截止时间: {task.deadline.toLocaleDateString('zh-CN')}
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="text-right ml-4">
                    <div className="mb-3">
                      <p className="text-caption-1 text-secondary-label">奖励</p>
                      <p className="text-title-3 font-sf-pro font-bold text-system-green">
                        +{parseFloat(task.reward).toLocaleString()}
                      </p>
                      <p className="text-caption-1 text-secondary-label">HAOX</p>
                    </div>

                    <Button
                      variant={task.status === 'completed' ? 'primary' : 'outline'}
                      size="sm"
                      onClick={() => handleTaskAction(task)}
                      disabled={task.status === 'claimed'}
                      className="min-w-[80px]"
                    >
                      {task.status === 'available' && '开始'}
                      {task.status === 'in_progress' && '进行中'}
                      {task.status === 'completed' && '领取奖励'}
                      {task.status === 'claimed' && '已领取'}
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default TaskCenter;
