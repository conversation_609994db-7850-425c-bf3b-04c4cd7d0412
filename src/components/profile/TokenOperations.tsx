'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, Button, Input, Icon, Modal } from '@/components/ui';
import {
  FinanceIcons,
  ActionIcons,
  UserIcons,
  MiscIcons,
  FeatureIcons
} from '@/config/icons';
import type { UserAssets, UserProfile } from '@/types/user';
import { cn } from '@/lib/utils';

interface TokenOperationsProps {
  assets: UserAssets | null;
  user: UserProfile | null;
}

const TokenOperations: React.FC<TokenOperationsProps> = ({ assets, user }) => {
  const [activeOperation, setActiveOperation] = useState<'send' | 'receive' | 'exchange' | null>(null);
  const [sendForm, setSendForm] = useState({
    recipient: '',
    amount: '',
    memo: ''
  });
  const [isLoading, setIsLoading] = useState(false);

  const operations = [
    {
      id: 'send' as const,
      title: '发送代币',
      description: '向其他地址发送 HAOX 代币',
      icon: ActionIcons.send,
      color: 'text-system-blue',
      bgColor: 'bg-system-blue/10',
      borderColor: 'border-system-blue/20'
    },
    {
      id: 'receive' as const,
      title: '接收代币',
      description: '显示您的钱包地址以接收代币',
      icon: ActionIcons.receive,
      color: 'text-system-green',
      bgColor: 'bg-system-green/10',
      borderColor: 'border-system-green/20'
    },
    {
      id: 'exchange' as const,
      title: '代币兑换',
      description: 'HAOX 与其他代币的兑换',
      icon: FinanceIcons.exchange,
      color: 'text-system-orange',
      bgColor: 'bg-system-orange/10',
      borderColor: 'border-system-orange/20'
    }
  ];

  const handleSendSubmit = async () => {
    if (!sendForm.recipient || !sendForm.amount) return;
    
    setIsLoading(true);
    // 模拟发送操作
    await new Promise(resolve => setTimeout(resolve, 2000));
    setIsLoading(false);
    setActiveOperation(null);
    setSendForm({ recipient: '', amount: '', memo: '' });
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      // 这里可以添加成功提示
    } catch (error) {
      console.error('Failed to copy:', error);
    }
  };

  const renderSendModal = () => (
    <Modal
      isOpen={activeOperation === 'send'}
      onClose={() => setActiveOperation(null)}
      title="发送 HAOX 代币"
      size="md"
    >
      <div className="p-6 space-y-6">
        <div className="bg-system-yellow/10 border border-system-yellow/20 rounded-xl p-4">
          <div className="flex items-start space-x-3">
            <Icon icon={ActionIcons.warning} size="sm" color="warning" className="mt-0.5" />
            <div>
              <p className="text-sm font-sf-pro font-medium text-label mb-1">
                安全提醒
              </p>
              <ul className="text-xs text-secondary-label space-y-1">
                <li>• 请仔细核对接收地址，转账无法撤销</li>
                <li>• 确保接收地址支持 HAOX 代币</li>
                <li>• 建议先发送小额测试</li>
              </ul>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-sf-pro font-medium text-label mb-2">
              接收地址
            </label>
            <Input
              placeholder="输入接收地址或扫描二维码"
              value={sendForm.recipient}
              onChange={(value) => setSendForm(prev => ({ ...prev, recipient: value }))}
              className="w-full"
            />
          </div>

          <div>
            <label className="block text-sm font-sf-pro font-medium text-label mb-2">
              发送数量
            </label>
            <div className="relative">
              <Input
                type="number"
                placeholder="0.00"
                value={sendForm.amount}
                onChange={(value) => setSendForm(prev => ({ ...prev, amount: value }))}
                className="w-full pr-20"
              />
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                <span className="text-sm text-secondary-label">HAOX</span>
              </div>
            </div>
            {assets && (
              <p className="text-xs text-secondary-label mt-1">
                可用余额: {parseFloat(assets.haoxBalance.available).toLocaleString()} HAOX
              </p>
            )}
          </div>

          <div>
            <label className="block text-sm font-sf-pro font-medium text-label mb-2">
              备注 (可选)
            </label>
            <Input
              placeholder="添加备注信息"
              value={sendForm.memo}
              onChange={(e) => setSendForm(prev => ({ ...prev, memo: e.target.value }))}
              className="w-full"
            />
          </div>
        </div>

        <div className="bg-system-gray-6 rounded-xl p-4">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm text-secondary-label">预估 Gas 费用</span>
            <span className="text-sm font-sf-pro font-medium text-label">~0.002 ETH</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm text-secondary-label">总计</span>
            <span className="text-sm font-sf-pro font-medium text-label">
              {sendForm.amount || '0'} HAOX + Gas
            </span>
          </div>
        </div>

        <div className="flex space-x-3">
          <Button
            variant="outline"
            size="lg"
            onClick={() => setActiveOperation(null)}
            className="flex-1"
          >
            取消
          </Button>
          <Button
            variant="primary"
            size="lg"
            onClick={handleSendSubmit}
            loading={isLoading}
            disabled={!sendForm.recipient || !sendForm.amount || isLoading}
            className="flex-1"
          >
            确认发送
          </Button>
        </div>
      </div>
    </Modal>
  );

  const renderReceiveModal = () => (
    <Modal
      isOpen={activeOperation === 'receive'}
      onClose={() => setActiveOperation(null)}
      title="接收 HAOX 代币"
      size="md"
    >
      <div className="p-6 space-y-6">
        <div className="text-center">
          <div className="w-48 h-48 bg-white border border-system-gray-4 rounded-xl mx-auto mb-4 flex items-center justify-center">
            <div className="text-center">
              <Icon icon={MiscIcons.qrCode} size="xl" color="muted" />
              <p className="text-xs text-secondary-label mt-2">二维码</p>
            </div>
          </div>
          
          <p className="text-sm text-secondary-label mb-4">
            扫描二维码或复制地址来接收 HAOX 代币
          </p>
        </div>

        <div className="bg-system-gray-6 rounded-xl p-4">
          <div className="flex items-center justify-between">
            <div className="flex-1 mr-3">
              <p className="text-xs text-secondary-label mb-1">钱包地址</p>
              <p className="text-sm font-mono text-label break-all">
                {user?.address}
              </p>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => copyToClipboard(user?.address || '')}
            >
              <Icon icon={ActionIcons.copy} size="sm" />
            </Button>
          </div>
        </div>

        <div className="bg-system-blue/10 border border-system-blue/20 rounded-xl p-4">
          <div className="flex items-start space-x-3">
            <Icon icon={ActionIcons.info} size="sm" color="primary" className="mt-0.5" />
            <div>
              <p className="text-sm font-sf-pro font-medium text-label mb-1">
                重要提醒
              </p>
              <ul className="text-xs text-secondary-label space-y-1">
                <li>• 仅发送 HAOX 代币到此地址</li>
                <li>• 发送其他代币可能导致永久丢失</li>
                <li>• 确认网络为 BSC (Binance Smart Chain)</li>
              </ul>
            </div>
          </div>
        </div>

        <Button
          variant="outline"
          size="lg"
          onClick={() => setActiveOperation(null)}
          className="w-full"
        >
          关闭
        </Button>
      </div>
    </Modal>
  );

  const renderExchangeModal = () => (
    <Modal
      isOpen={activeOperation === 'exchange'}
      onClose={() => setActiveOperation(null)}
      title="代币兑换"
      size="md"
    >
      <div className="p-6 space-y-6">
        <div className="text-center">
          <Icon icon={FinanceIcons.exchange} size="xl" color="primary" />
          <h3 className="text-title-3 font-sf-pro font-bold text-label mt-4 mb-2">
            兑换功能即将上线
          </h3>
          <p className="text-body text-secondary-label">
            我们正在开发代币兑换功能，敬请期待！
          </p>
        </div>

        <div className="bg-system-gray-6 rounded-xl p-4">
          <h4 className="text-sm font-sf-pro font-medium text-label mb-2">
            即将支持的兑换对
          </h4>
          <ul className="text-sm text-secondary-label space-y-1">
            <li>• HAOX ↔ USDT</li>
            <li>• HAOX ↔ BNB</li>
            <li>• HAOX ↔ ETH</li>
          </ul>
        </div>

        <Button
          variant="primary"
          size="lg"
          onClick={() => setActiveOperation(null)}
          className="w-full"
        >
          了解更多
        </Button>
      </div>
    </Modal>
  );

  return (
    <div className="space-y-6">
      {/* 操作卡片 */}
      <div className="grid md:grid-cols-3 gap-6">
        {operations.map((operation, index) => (
          <motion.div
            key={operation.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
          >
            <Card 
              className={cn(
                'cursor-pointer transition-all duration-200 hover:shadow-apple-lg border',
                operation.borderColor,
                operation.bgColor
              )}
              onClick={() => setActiveOperation(operation.id)}
            >
              <div className="p-6 text-center">
                <div className={cn(
                  'w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-4',
                  operation.bgColor
                )}>
                  <Icon icon={operation.icon} size="xl" className={operation.color} />
                </div>
                
                <h3 className="text-headline font-sf-pro font-semibold text-label mb-2">
                  {operation.title}
                </h3>
                
                <p className="text-caption-1 text-secondary-label">
                  {operation.description}
                </p>
              </div>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* 余额信息 */}
      {assets && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <Card title="当前余额">
            <div className="p-6">
              <div className="grid md:grid-cols-3 gap-6">
                <div className="text-center">
                  <p className="text-caption-1 text-secondary-label mb-1">可用余额</p>
                  <p className="text-title-2 font-sf-pro font-bold text-system-green">
                    {parseFloat(assets.haoxBalance.available).toLocaleString()}
                  </p>
                  <p className="text-caption-1 text-secondary-label">HAOX</p>
                </div>
                
                <div className="text-center">
                  <p className="text-caption-1 text-secondary-label mb-1">锁定余额</p>
                  <p className="text-title-2 font-sf-pro font-bold text-system-orange">
                    {parseFloat(assets.haoxBalance.locked).toLocaleString()}
                  </p>
                  <p className="text-caption-1 text-secondary-label">HAOX</p>
                </div>
                
                <div className="text-center">
                  <p className="text-caption-1 text-secondary-label mb-1">待领取</p>
                  <p className="text-title-2 font-sf-pro font-bold text-system-purple">
                    {parseFloat(assets.haoxBalance.pending).toLocaleString()}
                  </p>
                  <p className="text-caption-1 text-secondary-label">HAOX</p>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>
      )}

      {/* 模态框 */}
      {renderSendModal()}
      {renderReceiveModal()}
      {renderExchangeModal()}
    </div>
  );
};

export default TokenOperations;
