'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Card, Button, Icon, Badge, Input } from '@/components/ui';
import { 
  FeatureIcons, 
  ActionIcons, 
  UserIcons,
  SocialIcons 
} from '@/config/icons';
import type { UserProfile, UserSettings as UserSettingsType } from '@/types/user';
import { cn } from '@/lib/utils';

interface UserSettingsProps {
  user: UserProfile | null;
}

const UserSettings: React.FC<UserSettingsProps> = ({ user }) => {
  const [settings, setSettings] = useState<UserSettingsType>({
    language: 'zh',
    theme: 'auto',
    currency: 'USD',
    notifications: {
      email: true,
      push: true,
      trading: true,
      rewards: true,
      social: false
    },
    security: {
      twoFactorEnabled: false,
      transactionPassword: false,
      loginNotifications: true
    }
  });

  const [isLoading, setIsLoading] = useState(false);

  const handleSettingChange = (category: keyof UserSettingsType, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [category]: typeof prev[category] === 'object' 
        ? { ...prev[category], [key]: value }
        : value
    }));
  };

  const handleSaveSettings = async () => {
    setIsLoading(true);
    // 模拟保存设置
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsLoading(false);
  };

  return (
    <div className="space-y-6">
      {/* 基本设置 */}
      <Card title="基本设置">
        <div className="p-6 space-y-6">
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-sf-pro font-medium text-label mb-2">
                语言设置
              </label>
              <select
                value={settings.language}
                onChange={(e) => handleSettingChange('language', '', e.target.value)}
                className="w-full p-3 border border-system-gray-4 rounded-lg text-sm"
              >
                <option value="zh">简体中文</option>
                <option value="en">English</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-sf-pro font-medium text-label mb-2">
                主题设置
              </label>
              <select
                value={settings.theme}
                onChange={(e) => handleSettingChange('theme', '', e.target.value)}
                className="w-full p-3 border border-system-gray-4 rounded-lg text-sm"
              >
                <option value="light">浅色模式</option>
                <option value="dark">深色模式</option>
                <option value="auto">跟随系统</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-sf-pro font-medium text-label mb-2">
                货币单位
              </label>
              <select
                value={settings.currency}
                onChange={(e) => handleSettingChange('currency', '', e.target.value)}
                className="w-full p-3 border border-system-gray-4 rounded-lg text-sm"
              >
                <option value="USD">美元 (USD)</option>
                <option value="CNY">人民币 (CNY)</option>
              </select>
            </div>
          </div>
        </div>
      </Card>

      {/* 通知设置 */}
      <Card title="通知设置">
        <div className="p-6 space-y-4">
          {Object.entries(settings.notifications).map(([key, value]) => (
            <div key={key} className="flex items-center justify-between">
              <div>
                <p className="text-body font-sf-pro font-medium text-label">
                  {key === 'email' && '邮件通知'}
                  {key === 'push' && '推送通知'}
                  {key === 'trading' && '交易通知'}
                  {key === 'rewards' && '奖励通知'}
                  {key === 'social' && '社交通知'}
                </p>
                <p className="text-caption-1 text-secondary-label">
                  {key === 'email' && '接收重要邮件通知'}
                  {key === 'push' && '接收浏览器推送通知'}
                  {key === 'trading' && '交易状态变化通知'}
                  {key === 'rewards' && '奖励到账通知'}
                  {key === 'social' && '社交活动通知'}
                </p>
              </div>
              <button
                onClick={() => handleSettingChange('notifications', key, !value)}
                className={cn(
                  'relative inline-flex h-6 w-11 items-center rounded-full transition-colors',
                  value ? 'bg-system-blue' : 'bg-system-gray-4'
                )}
              >
                <span
                  className={cn(
                    'inline-block h-4 w-4 transform rounded-full bg-white transition-transform',
                    value ? 'translate-x-6' : 'translate-x-1'
                  )}
                />
              </button>
            </div>
          ))}
        </div>
      </Card>

      {/* 安全设置 */}
      <Card title="安全设置">
        <div className="p-6 space-y-6">
          {Object.entries(settings.security).map(([key, value]) => (
            <div key={key} className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-system-blue/10 rounded-lg flex items-center justify-center">
                  <Icon 
                    icon={
                      key === 'twoFactorEnabled' ? UserIcons.shield :
                      key === 'transactionPassword' ? ActionIcons.lock :
                      FeatureIcons.bell
                    } 
                    size="sm" 
                    color="primary" 
                  />
                </div>
                <div>
                  <p className="text-body font-sf-pro font-medium text-label">
                    {key === 'twoFactorEnabled' && '双重验证'}
                    {key === 'transactionPassword' && '交易密码'}
                    {key === 'loginNotifications' && '登录通知'}
                  </p>
                  <p className="text-caption-1 text-secondary-label">
                    {key === 'twoFactorEnabled' && '启用双重验证保护账户安全'}
                    {key === 'transactionPassword' && '交易时需要输入密码'}
                    {key === 'loginNotifications' && '新设备登录时发送通知'}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <Badge variant={value ? 'success' : 'default'}>
                  {value ? '已启用' : '未启用'}
                </Badge>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleSettingChange('security', key, !value)}
                >
                  {value ? '关闭' : '启用'}
                </Button>
              </div>
            </div>
          ))}

          <div className="bg-system-yellow/10 border border-system-yellow/20 rounded-xl p-4">
            <div className="flex items-start space-x-3">
              <Icon icon={ActionIcons.warning} size="sm" color="warning" className="mt-0.5" />
              <div>
                <p className="text-sm font-sf-pro font-medium text-label mb-1">
                  安全建议
                </p>
                <ul className="text-xs text-secondary-label space-y-1">
                  <li>• 建议启用双重验证以提高账户安全性</li>
                  <li>• 设置交易密码可防止未授权交易</li>
                  <li>• 定期检查登录记录</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* 账户管理 */}
      <Card title="账户管理">
        <div className="p-6 space-y-4">
          <div className="grid md:grid-cols-2 gap-4">
            <Button
              variant="outline"
              size="lg"
              className="flex items-center justify-center space-x-2 h-auto py-4"
            >
              <Icon icon={ActionIcons.download} size="lg" color="primary" />
              <div className="text-left">
                <p className="text-sm font-sf-pro font-medium text-label">导出数据</p>
                <p className="text-xs text-secondary-label">下载您的账户数据</p>
              </div>
            </Button>

            <Button
              variant="outline"
              size="lg"
              className="flex items-center justify-center space-x-2 h-auto py-4"
            >
              <Icon icon={FeatureIcons.history} size="lg" color="muted" />
              <div className="text-left">
                <p className="text-sm font-sf-pro font-medium text-label">登录记录</p>
                <p className="text-xs text-secondary-label">查看登录历史</p>
              </div>
            </Button>

            <Button
              variant="outline"
              size="lg"
              className="flex items-center justify-center space-x-2 h-auto py-4"
            >
              <Icon icon={ActionIcons.refresh} size="lg" color="success" />
              <div className="text-left">
                <p className="text-sm font-sf-pro font-medium text-label">重置设置</p>
                <p className="text-xs text-secondary-label">恢复默认设置</p>
              </div>
            </Button>

            <Button
              variant="outline"
              size="lg"
              className="flex items-center justify-center space-x-2 h-auto py-4 border-system-red/20 text-system-red hover:bg-system-red/10"
            >
              <Icon icon={ActionIcons.trash} size="lg" color="error" />
              <div className="text-left">
                <p className="text-sm font-sf-pro font-medium">删除账户</p>
                <p className="text-xs text-secondary-label">永久删除账户</p>
              </div>
            </Button>
          </div>
        </div>
      </Card>

      {/* 保存按钮 */}
      <div className="flex justify-end">
        <Button
          variant="primary"
          size="lg"
          onClick={handleSaveSettings}
          loading={isLoading}
          className="flex items-center space-x-2"
        >
          <Icon icon={ActionIcons.check} size="sm" />
          <span>保存设置</span>
        </Button>
      </div>
    </div>
  );
};

export default UserSettings;
