/**
 * Telegram 频道加入组件
 * 引导用户加入官方 Telegram 频道 @sociomint9
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button, Card, Icon } from '@/components/ui';
import { SocialIcons, ActionIcons, MiscIcons } from '@/config/icons';
import { cn } from '@/lib/utils';
import { useTelegramAuth } from '@/hooks/useTelegramAuth';
import {
  checkTelegramBinding,
  simulateVerification,
  openTelegramChannel,
  getChannelJoinInstructions,
  TELEGRAM_CONFIG
} from '@/lib/telegramVerification';

interface TelegramChannelJoinProps {
  onJoinComplete?: (success: boolean) => void;
  onEligibilityGranted?: () => void;
  className?: string;
}

export default function TelegramChannelJoin({
  onJoinComplete,
  onEligibilityGranted,
  className
}: TelegramChannelJoinProps) {
  const { user, isAuthenticated } = useTelegramAuth();
  const [isJoining, setIsJoining] = useState(false);
  const [hasJoined, setHasJoined] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const [verificationStep, setVerificationStep] = useState(1);
  const [error, setError] = useState<string | null>(null);

  const instructions = getChannelJoinInstructions();

  // 检查初始绑定状态
  useEffect(() => {
    const checkInitialBinding = async () => {
      if (!user?.id) return;

      try {
        const binding = await checkTelegramBinding(user.id.toString());
        if (binding.isBound && binding.presaleEligible) {
          setHasJoined(true);
          onJoinComplete?.(true);
          onEligibilityGranted?.();
        }
      } catch (error) {
        console.error('检查初始绑定状态失败:', error);
      }
    };

    checkInitialBinding();
  }, [user?.id, onJoinComplete, onEligibilityGranted]);

  // 检查用户是否已加入频道
  const checkChannelMembership = async () => {
    if (!user?.id) {
      setError('请先登录Telegram');
      return false;
    }

    try {
      setIsVerifying(true);
      setError(null);

      // 使用模拟验证（在生产环境中应该调用真实的Telegram Bot API）
      const result = await simulateVerification(user.id.toString());

      if (result.success && result.data) {
        setHasJoined(true);
        onJoinComplete?.(true);

        if (result.data.presaleEligible) {
          onEligibilityGranted?.();
        }

        return true;
      } else {
        setError(result.message);
        return false;
      }
    } catch (error) {
      console.error('验证频道成员身份失败:', error);
      setError('验证失败，请稍后重试');
      return false;
    } finally {
      setIsVerifying(false);
    }
  };

  // 打开Telegram频道
  const handleOpenChannel = () => {
    setIsJoining(true);
    setVerificationStep(2);
    setError(null);

    // 使用优化的频道打开逻辑
    openTelegramChannel();

    // 3秒后允许验证
    setTimeout(() => {
      setIsJoining(false);
    }, 3000);
  };

  // 手动验证加入状态
  const handleVerifyJoin = async () => {
    const joined = await checkChannelMembership();
    
    if (!joined) {
      setVerificationStep(1);
    }
  };

  if (hasJoined) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        onClick={(e) => {
          if (e.target === e.currentTarget) {
            onJoinComplete?.(true);
          }
        }}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.9, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.9, y: 20 }}
          className="w-full max-w-md"
          onClick={(e) => e.stopPropagation()}
        >
          <Card className={cn('text-center', className)}>
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              className="py-6"
            >
              <div className="w-16 h-16 bg-system-green/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <Icon icon={ActionIcons.checkCircle} size="xl" color="success" />
              </div>

              <h3 className="text-headline font-sf-pro font-semibold text-label mb-2">
                🎉 加入成功！
              </h3>

              <p className="text-body text-secondary-label mb-4">
                您已成功加入 SocioMint 官方频道
              </p>

              <div className="flex items-center justify-center space-x-2 text-sm text-system-green mb-4">
                <Icon icon={SocialIcons.telegram} size="sm" />
                <span>{TELEGRAM_CONFIG.OFFICIAL_CHANNEL}</span>
              </div>

              <Button
                onClick={() => onJoinComplete?.(true)}
                className="w-full"
              >
                确定
              </Button>
            </motion.div>
          </Card>
        </motion.div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      onClick={(e) => {
        if (e.target === e.currentTarget) {
          onJoinComplete?.(false);
        }
      }}
    >
      <motion.div
        initial={{ opacity: 0, scale: 0.9, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.9, y: 20 }}
        className="w-full max-w-md"
        onClick={(e) => e.stopPropagation()}
      >
        <Card className={cn('', className)}>
          <div className="text-center">
        <div className="w-16 h-16 bg-system-blue/20 rounded-full flex items-center justify-center mx-auto mb-4">
          <Icon icon={SocialIcons.telegram} size="xl" color="primary" />
        </div>
        
        <h3 className="text-headline font-sf-pro font-semibold text-label mb-2">
          加入官方 Telegram 频道
        </h3>
        
        <p className="text-body text-secondary-label mb-6">
          加入我们的官方频道获取最新资讯和预售资格
        </p>

        {verificationStep === 1 && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-4"
          >
            <div className="bg-system-gray-6 rounded-xl p-4 mb-4">
              <div className="flex items-center justify-center space-x-2 mb-2">
                <Icon icon={SocialIcons.telegram} size="sm" color="primary" />
                <span className="font-sf-pro font-medium text-label">
                  {TELEGRAM_CONFIG.OFFICIAL_CHANNEL}
                </span>
              </div>
              <p className="text-sm text-secondary-label">
                SocioMint 官方频道
              </p>
            </div>

            {error && (
              <div className="bg-system-red/10 border border-system-red/20 rounded-lg p-3 mb-4">
                <p className="text-sm text-system-red">{error}</p>
              </div>
            )}

            <Button
              onClick={handleOpenChannel}
              disabled={isJoining || !isAuthenticated}
              className="w-full flex items-center justify-center space-x-2"
              size="lg"
            >
              <Icon icon={SocialIcons.telegram} size="sm" />
              <span>{isJoining ? '正在打开...' : '加入频道'}</span>
              <Icon icon={MiscIcons.externalLink} size="sm" />
            </Button>

            <p className="text-xs text-secondary-label">
              点击按钮将在新窗口中打开 Telegram 频道
            </p>
          </motion.div>
        )}

        {verificationStep === 2 && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-4"
          >
            <div className="bg-system-blue/10 rounded-xl p-4 mb-4">
              <h4 className="font-sf-pro font-medium text-label mb-2">
                📱 请完成以下步骤：
              </h4>
              <ol className="text-sm text-secondary-label text-left space-y-1">
                {instructions.steps.map((step, index) => (
                  <li key={index}>{index + 1}. {step}</li>
                ))}
              </ol>
            </div>

            {error && (
              <div className="bg-system-red/10 border border-system-red/20 rounded-lg p-3 mb-4">
                <p className="text-sm text-system-red">{error}</p>
              </div>
            )}

            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={() => {
                  setVerificationStep(1);
                  setError(null);
                }}
                className="flex-1"
              >
                重新打开
              </Button>

              <Button
                onClick={handleVerifyJoin}
                disabled={isVerifying || !isAuthenticated}
                className="flex-1 flex items-center justify-center space-x-2"
              >
                {isVerifying ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    <span>验证中...</span>
                  </>
                ) : (
                  <>
                    <Icon icon={ActionIcons.check} size="sm" />
                    <span>验证加入</span>
                  </>
                )}
              </Button>
            </div>

            <p className="text-xs text-secondary-label">
              验证可能需要几秒钟时间
            </p>
          </motion.div>
        )}
      </div>
    </Card>
      </motion.div>
    </motion.div>
  );
}

// 导出频道信息供其他组件使用
export const TELEGRAM_CHANNEL_INFO = {
  username: TELEGRAM_CONFIG.OFFICIAL_CHANNEL,
  url: TELEGRAM_CONFIG.CHANNEL_URL,
  name: 'SocioMint 官方频道',
  description: '获取最新资讯、预售信息和社区动态',
};
