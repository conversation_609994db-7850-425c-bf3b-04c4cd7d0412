'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, Breadcrumb, Button, Input, Badge, Loading } from '@/components/ui';
import { useAuth } from '@/contexts/AuthContext';

interface ExchangeConfig {
  exchangeRate: number;
  minDeposit: number;
  maxDeposit: number;
  minWithdrawal: number;
  maxWithdrawal: number;
  depositFeeRate: number;
  withdrawalFeeRate: number;
  minWithdrawalFee: number;
  dailyWithdrawalLimit: number;
  smallAmountThreshold: number;
  dailyLimit?: {
    totalWithdrawn: number;
    remainingLimit: number;
  };
}

interface TransactionStats {
  totalDeposits: number;
  totalWithdrawals: number;
  totalDepositAmount: number;
  totalWithdrawalAmount: number;
  totalFees: number;
  pendingTransactions: number;
  completedTransactions: number;
}

const FortuneExchangePage: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const [activeTab, setActiveTab] = useState<'deposit' | 'withdraw' | 'history'>('deposit');
  const [config, setConfig] = useState<ExchangeConfig | null>(null);
  const [stats, setStats] = useState<TransactionStats | null>(null);
  const [loading, setLoading] = useState(true);

  // 充值表单
  const [depositForm, setDepositForm] = useState({
    amount: '',
    txHash: '',
    walletAddress: ''
  });
  const [depositLoading, setDepositLoading] = useState(false);

  // 提现表单
  const [withdrawForm, setWithdrawForm] = useState({
    amount: '',
    walletAddress: ''
  });
  const [withdrawLoading, setWithdrawLoading] = useState(false);

  // 消息状态
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  useEffect(() => {
    if (isAuthenticated && user?.id) {
      loadExchangeConfig();
      loadTransactionStats();
    }
  }, [isAuthenticated, user]);

  const loadExchangeConfig = async () => {
    try {
      setLoading(true);
      
      // 加载充值配置
      const depositResponse = await fetch('/api/fortune/deposit');
      const depositResult = await depositResponse.json();
      
      // 加载提现配置
      const withdrawResponse = await fetch(`/api/fortune/withdraw?userId=${user?.id}`);
      const withdrawResult = await withdrawResponse.json();

      if (depositResult.success && withdrawResult.success) {
        setConfig({
          ...depositResult.data,
          ...withdrawResult.data
        });
      }
    } catch (error) {
      console.error('加载配置失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadTransactionStats = async () => {
    try {
      const response = await fetch(`/api/fortune/transactions/${user?.id}?limit=100`);
      const result = await response.json();

      if (result.success) {
        setStats(result.data.stats);
      }
    } catch (error) {
      console.error('加载统计信息失败:', error);
    }
  };

  const handleDeposit = async () => {
    if (!depositForm.amount || !depositForm.txHash || !depositForm.walletAddress) {
      setError('请填写完整的充值信息');
      return;
    }

    const amount = parseFloat(depositForm.amount);
    if (amount <= 0) {
      setError('请输入有效的充值金额');
      return;
    }

    try {
      setDepositLoading(true);
      setError('');
      setSuccess('');

      const response = await fetch('/api/fortune/deposit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user?.id,
          amount: amount,
          txHash: depositForm.txHash,
          walletAddress: depositForm.walletAddress
        })
      });

      const result = await response.json();

      if (result.success) {
        setSuccess('✅ 充值请求提交成功！我们将在1-3个工作日内处理您的充值申请。');
        setDepositForm({ amount: '', txHash: '', walletAddress: '' });
        loadTransactionStats();

        // 3秒后清除成功消息
        setTimeout(() => setSuccess(''), 5000);
      } else {
        setError(`充值失败：${result.message || '未知错误'}`);
      }
    } catch (error) {
      console.error('充值失败:', error);
      setError('充值失败，请检查网络连接后重试');
    } finally {
      setDepositLoading(false);
    }
  };

  const handleWithdraw = async () => {
    if (!withdrawForm.amount || !withdrawForm.walletAddress) {
      setError('请填写完整的提现信息');
      return;
    }

    const amount = parseFloat(withdrawForm.amount);
    if (amount <= 0) {
      setError('请输入有效的提现金额');
      return;
    }

    try {
      setWithdrawLoading(true);
      setError('');
      setSuccess('');

      const response = await fetch('/api/fortune/withdraw', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user?.id,
          amount: amount,
          walletAddress: withdrawForm.walletAddress
        })
      });

      const result = await response.json();

      if (result.success) {
        setSuccess('✅ 提现请求提交成功！我们将在1-3个工作日内处理您的提现申请。');
        setWithdrawForm({ amount: '', walletAddress: '' });
        loadTransactionStats();
        loadExchangeConfig(); // 重新加载每日限额

        // 3秒后清除成功消息
        setTimeout(() => setSuccess(''), 5000);
      } else {
        setError(`提现失败：${result.message || '未知错误'}`);
      }
    } catch (error) {
      console.error('提现失败:', error);
      setError('提现失败，请检查网络连接后重试');
    } finally {
      setWithdrawLoading(false);
    }
  };

  const calculateDepositFee = (amount: number): number => {
    if (!config) return 0;
    return Math.max(amount * config.depositFeeRate, 0);
  };

  const calculateWithdrawFee = (amount: number): number => {
    if (!config) return 0;
    return Math.max(amount * config.withdrawalFeeRate, config.minWithdrawalFee);
  };

  if (!isAuthenticated) {
    return (
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Breadcrumb
          items={[
            { label: '首页', href: '/', icon: '🏠' },
            { label: '福气中心', href: '/fortune', icon: '🍀' },
            { label: '充值提现', icon: '💰' }
          ]}
        />
        
        <div className="text-center py-12">
          <div className="text-6xl mb-4">🔒</div>
          <h2 className="text-2xl font-bold text-label mb-4">需要登录</h2>
          <p className="text-secondary-label mb-6">
            福气充值提现需要登录账户，请先完成登录。
          </p>
          <Button href="/auth/login">
            立即登录
          </Button>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-center items-center min-h-96">
          <Loading size="large" />
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* 面包屑导航 */}
      <div className="mb-6">
        <Breadcrumb
          items={[
            { label: '首页', href: '/', icon: '🏠' },
            { label: '福气中心', href: '/fortune', icon: '🍀' },
            { label: '充值提现', icon: '💰' }
          ]}
        />
      </div>

      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-label mb-2">
          💰 福气充值提现
        </h1>
        <p className="text-secondary-label">
          HAOX与福气1:1兑换，安全便捷的数字资产管理
        </p>
      </div>

      {/* 统计卡片 */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <Card className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600 mb-1">
              {stats.totalDepositAmount.toLocaleString()}
            </div>
            <div className="text-sm text-secondary-label">累计充值</div>
          </Card>
          <Card className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600 mb-1">
              {stats.totalWithdrawalAmount.toLocaleString()}
            </div>
            <div className="text-sm text-secondary-label">累计提现</div>
          </Card>
          <Card className="p-4 text-center">
            <div className="text-2xl font-bold text-orange-600 mb-1">
              {stats.totalFees.toLocaleString()}
            </div>
            <div className="text-sm text-secondary-label">累计手续费</div>
          </Card>
          <Card className="p-4 text-center">
            <div className="text-2xl font-bold text-purple-600 mb-1">
              {stats.pendingTransactions}
            </div>
            <div className="text-sm text-secondary-label">待处理交易</div>
          </Card>
        </div>
      )}

      {/* 标签页 */}
      <div className="flex space-x-1 mb-6 bg-system-gray-6 rounded-lg p-1">
        {[
          { key: 'deposit', label: '充值', icon: '💰' },
          { key: 'withdraw', label: '提现', icon: '💸' },
          { key: 'history', label: '交易历史', icon: '📋' }
        ].map((tab) => (
          <button
            key={tab.key}
            onClick={() => setActiveTab(tab.key as any)}
            className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md transition-all duration-200 ${
              activeTab === tab.key
                ? 'border-2 border-blue-500 text-blue-600 bg-blue-50/50 font-semibold'
                : 'text-secondary-label hover:text-label hover:bg-white/20 border-2 border-transparent'
            }`}
          >
            <span>{tab.icon}</span>
            <span className="font-medium">{tab.label}</span>
          </button>
        ))}
      </div>

      {/* 错误和成功消息 */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center">
            <span className="text-red-600 mr-2">❌</span>
            <span className="text-red-800 font-medium">{error}</span>
          </div>
        </div>
      )}

      {success && (
        <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
          <div className="flex items-center">
            <span className="text-green-600 mr-2">✅</span>
            <span className="text-green-800 font-medium">{success}</span>
          </div>
        </div>
      )}

      {/* 充值标签页 */}
      {activeTab === 'deposit' && config && (
        <Card className="p-6">
          <h2 className="text-xl font-bold text-label mb-6">💰 HAOX充值福气</h2>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-label mb-2">
                    充值金额 (福气)
                  </label>
                  <Input
                    type="number"
                    value={depositForm.amount}
                    onChange={(e) => setDepositForm(prev => ({ ...prev, amount: e.target.value }))}
                    placeholder={`${config.minDeposit} - ${config.maxDeposit}`}
                    className="w-full"
                  />
                  <p className="text-xs text-secondary-label mt-1">
                    最小充值：{config.minDeposit}福气，最大充值：{config.maxDeposit}福气
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-label mb-2">
                    交易哈希
                  </label>
                  <Input
                    value={depositForm.txHash}
                    onChange={(e) => setDepositForm(prev => ({ ...prev, txHash: e.target.value }))}
                    placeholder="0x..."
                    className="w-full"
                  />
                  <p className="text-xs text-secondary-label mt-1">
                    HAOX转账交易的哈希值
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-label mb-2">
                    发送钱包地址
                  </label>
                  <Input
                    value={depositForm.walletAddress}
                    onChange={(e) => setDepositForm(prev => ({ ...prev, walletAddress: e.target.value }))}
                    placeholder="0x..."
                    className="w-full"
                  />
                  <p className="text-xs text-secondary-label mt-1">
                    发送HAOX的钱包地址
                  </p>
                </div>

                <Button
                  onClick={handleDeposit}
                  disabled={depositLoading || !depositForm.amount || !depositForm.txHash || !depositForm.walletAddress}
                  className="w-full"
                >
                  {depositLoading ? '处理中...' : '确认充值'}
                </Button>
              </div>
            </div>

            <div>
              <h3 className="font-semibold text-label mb-4">充值信息</h3>
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-secondary-label">兑换比例:</span>
                  <span className="font-medium text-label">1 HAOX = 1 福气</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-secondary-label">充值金额:</span>
                  <span className="font-medium text-label">
                    {depositForm.amount || '0'} 福气
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-secondary-label">手续费:</span>
                  <span className="font-medium text-label">
                    {depositForm.amount ? calculateDepositFee(parseFloat(depositForm.amount)) : 0} 福气
                  </span>
                </div>
                <div className="flex justify-between border-t pt-2">
                  <span className="text-secondary-label">实际到账:</span>
                  <span className="font-bold text-green-600">
                    {depositForm.amount ? parseFloat(depositForm.amount) - calculateDepositFee(parseFloat(depositForm.amount)) : 0} 福气
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-secondary-label">处理时间:</span>
                  <span className="font-medium text-label">即时到账</span>
                </div>
              </div>

              <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                <h4 className="font-semibold text-blue-800 mb-2">充值说明</h4>
                <ul className="text-sm text-blue-700 space-y-1">
                  <li>• 发送HAOX到指定地址</li>
                  <li>• 复制交易哈希</li>
                  <li>• 填写充值表单提交</li>
                  <li>• 系统验证后即时到账</li>
                </ul>
              </div>
            </div>
          </div>
        </Card>
      )}

      {/* 提现标签页 */}
      {activeTab === 'withdraw' && config && (
        <Card className="p-6">
          <h2 className="text-xl font-bold text-label mb-6">💸 福气提现HAOX</h2>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-label mb-2">
                    提现金额 (福气)
                  </label>
                  <Input
                    type="number"
                    value={withdrawForm.amount}
                    onChange={(e) => setWithdrawForm(prev => ({ ...prev, amount: e.target.value }))}
                    placeholder={`${config.minWithdrawal} - ${config.maxWithdrawal}`}
                    className="w-full"
                  />
                  <p className="text-xs text-secondary-label mt-1">
                    最小提现：{config.minWithdrawal}福气，最大提现：{config.maxWithdrawal}福气
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-label mb-2">
                    接收钱包地址
                  </label>
                  <Input
                    value={withdrawForm.walletAddress}
                    onChange={(e) => setWithdrawForm(prev => ({ ...prev, walletAddress: e.target.value }))}
                    placeholder="0x..."
                    className="w-full"
                  />
                  <p className="text-xs text-secondary-label mt-1">
                    接收HAOX的钱包地址
                  </p>
                </div>

                {config.dailyLimit && (
                  <div className="p-3 bg-yellow-50 rounded-lg">
                    <p className="text-sm text-yellow-800">
                      今日剩余提现额度：{config.dailyLimit.remainingLimit.toLocaleString()} 福气
                    </p>
                  </div>
                )}

                <Button
                  onClick={handleWithdraw}
                  disabled={withdrawLoading || !withdrawForm.amount || !withdrawForm.walletAddress}
                  className="w-full"
                >
                  {withdrawLoading ? '处理中...' : '确认提现'}
                </Button>
              </div>
            </div>

            <div>
              <h3 className="font-semibold text-label mb-4">提现信息</h3>
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-secondary-label">兑换比例:</span>
                  <span className="font-medium text-label">1 福气 = 1 HAOX</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-secondary-label">提现金额:</span>
                  <span className="font-medium text-label">
                    {withdrawForm.amount || '0'} 福气
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-secondary-label">手续费:</span>
                  <span className="font-medium text-label">
                    {withdrawForm.amount ? calculateWithdrawFee(parseFloat(withdrawForm.amount)) : 0} 福气
                  </span>
                </div>
                <div className="flex justify-between border-t pt-2">
                  <span className="text-secondary-label">实际到账:</span>
                  <span className="font-bold text-green-600">
                    {withdrawForm.amount ? parseFloat(withdrawForm.amount) - calculateWithdrawFee(parseFloat(withdrawForm.amount)) : 0} HAOX
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-secondary-label">处理时间:</span>
                  <span className="font-medium text-label">
                    {withdrawForm.amount && parseFloat(withdrawForm.amount) <= config.smallAmountThreshold 
                      ? '5分钟内' 
                      : '24小时内（需审核）'
                    }
                  </span>
                </div>
              </div>

              <div className="mt-6 p-4 bg-orange-50 rounded-lg">
                <h4 className="font-semibold text-orange-800 mb-2">提现说明</h4>
                <ul className="text-sm text-orange-700 space-y-1">
                  <li>• 小额提现（≤{config.smallAmountThreshold}福气）5分钟内到账</li>
                  <li>• 大额提现需人工审核，24小时内处理</li>
                  <li>• 每日提现限额：{config.dailyWithdrawalLimit.toLocaleString()}福气</li>
                  <li>• 提现手续费：{(config.withdrawalFeeRate * 100).toFixed(1)}%</li>
                </ul>
              </div>
            </div>
          </div>
        </Card>
      )}

      {/* 交易历史标签页 */}
      {activeTab === 'history' && (
        <Card className="p-6">
          <h2 className="text-xl font-bold text-label mb-6">📋 交易历史</h2>
          <div className="text-center py-8 text-secondary-label">
            交易历史功能开发中...
          </div>
        </Card>
      )}
    </div>
  );
};

export default FortuneExchangePage;
