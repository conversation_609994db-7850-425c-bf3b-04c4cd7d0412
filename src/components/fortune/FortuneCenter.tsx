'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Button, Icon, Card } from '@/components/ui';
import { RewardIcons, ActionIcons, FinanceIcons, UserIcons, SocialIcons, FeatureIcons, NotificationIcons } from '@/config/icons';
import { useFortune, useFortuneRewards } from '@/hooks/useFortune';
import { cn } from '@/lib/utils';

interface FortuneCenterProps {
  onClose?: () => void;
}

const FortuneCenter: React.FC<FortuneCenterProps> = ({ onClose }) => {
  const { 
    fortuneAccount, 
    isLoading: fortuneLoading,
    refreshFortune 
  } = useFortune();
  
  const {
    checkInStatus,
    shareRewards,
    inviteStats,
    isLoading: rewardsLoading,
    dailyCheckIn,
    processShareReward
  } = useFortuneRewards();

  const [activeTab, setActiveTab] = useState<'overview' | 'checkin' | 'invite' | 'share'>('overview');
  const [isProcessing, setIsProcessing] = useState(false);

  /**
   * 处理每日签到
   */
  const handleDailyCheckIn = async () => {
    if (checkInStatus?.hasCheckedInToday || isProcessing) return;
    
    setIsProcessing(true);
    try {
      await dailyCheckIn();
      await refreshFortune();
    } catch (error) {
      console.error('Daily check-in failed:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  /**
   * 处理分享奖励
   */
  const handleShareReward = async (contentType: string, contentId: string, platform: string) => {
    setIsProcessing(true);
    try {
      await processShareReward(contentType, contentId, platform);
      await refreshFortune();
    } catch (error) {
      console.error('Share reward failed:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  /**
   * 格式化福气数量
   */
  const formatFortune = (amount: number): string => {
    if (amount >= 1000000) {
      return `${(amount / 1000000).toFixed(1)}M`;
    } else if (amount >= 1000) {
      return `${(amount / 1000).toFixed(1)}K`;
    }
    return amount.toFixed(0);
  };

  /**
   * 获取福气等级颜色
   */
  const getLevelColor = (level: number): string => {
    switch (level) {
      case 1: return 'text-system-gray';
      case 2: return 'text-system-green';
      case 3: return 'text-system-blue';
      case 4: return 'text-system-purple';
      case 5: return 'text-system-orange';
      default: return 'text-system-gray';
    }
  };

  /**
   * 获取福气等级图标
   */
  const getLevelIcon = (level: number): string => {
    switch (level) {
      case 1: return '🌱';
      case 2: return '🍀';
      case 3: return '🎋';
      case 4: return '⭐';
      case 5: return '🌟';
      default: return '🌱';
    }
  };

  if (fortuneLoading || rewardsLoading) {
    return (
      <Card className="p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-system-gray-5 rounded mb-4"></div>
          <div className="grid grid-cols-2 gap-4 mb-6">
            <div className="h-20 bg-system-gray-5 rounded"></div>
            <div className="h-20 bg-system-gray-5 rounded"></div>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <span className="text-2xl">🍀</span>
          <h3 className="text-title-2 font-sf-pro font-bold text-label">
            福气中心
          </h3>
        </div>
        {onClose && (
          <Button variant="ghost" size="sm" onClick={onClose}>
            <Icon icon={ActionIcons.close} size="sm" />
          </Button>
        )}
      </div>

      {/* 福气账户概览 */}
      {fortuneAccount && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-gradient-to-br from-system-green/10 to-system-green/5 rounded-xl p-4 text-center border border-system-green/20">
            <p className="text-caption-1 text-secondary-label mb-1">可用福气</p>
            <p className="text-title-3 font-sf-pro font-bold text-system-green">
              {formatFortune(fortuneAccount.available_fortune)}
            </p>
          </div>
          <div className="bg-gradient-to-br from-system-blue/10 to-system-blue/5 rounded-xl p-4 text-center border border-system-blue/20">
            <p className="text-caption-1 text-secondary-label mb-1">总福气</p>
            <p className="text-title-3 font-sf-pro font-bold text-system-blue">
              {formatFortune(fortuneAccount.total_earned)}
            </p>
          </div>
          <div className="bg-gradient-to-br from-system-purple/10 to-system-purple/5 rounded-xl p-4 text-center border border-system-purple/20">
            <div className="flex items-center justify-center space-x-1 mb-1">
              <span className="text-caption-1 text-secondary-label">福气等级</span>
              <span className="text-sm">{getLevelIcon(fortuneAccount.fortune_level)}</span>
            </div>
            <p className={cn("text-title-3 font-sf-pro font-bold", getLevelColor(fortuneAccount.fortune_level))}>
              {fortuneAccount.fortune_level_name}
            </p>
          </div>
          <div className="bg-gradient-to-br from-system-orange/10 to-system-orange/5 rounded-xl p-4 text-center border border-system-orange/20">
            <p className="text-caption-1 text-secondary-label mb-1">连续签到</p>
            <p className="text-title-3 font-sf-pro font-bold text-system-orange">
              {fortuneAccount.consecutive_checkin_days}天
            </p>
          </div>
        </div>
      )}

      {/* 标签页 */}
      <div className="flex space-x-1 mb-6 bg-system-gray-6 rounded-lg p-1">
        {[
          { id: 'overview', label: '概览', icon: FeatureIcons.home },
          { id: 'checkin', label: '每日签到', icon: FeatureIcons.calendar },
          { id: 'invite', label: '邀请好友', icon: UserIcons.userAdd },
          { id: 'share', label: '分享奖励', icon: SocialIcons.share },
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={cn(
              'flex-1 flex items-center justify-center space-x-2 py-2 px-3 rounded-md transition-colors text-sm',
              activeTab === tab.id
                ? 'bg-system-background text-label shadow-sm'
                : 'text-secondary-label hover:text-label'
            )}
          >
            <Icon icon={tab.icon} size="sm" />
            <span className="font-sf-pro font-medium">{tab.label}</span>
          </button>
        ))}
      </div>

      {/* 概览页面 */}
      {activeTab === 'overview' && (
        <div className="space-y-4">
          {/* 每日签到卡片 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gradient-to-r from-system-green/10 to-system-blue/10 rounded-xl p-4 border border-system-green/20"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <span className="text-2xl">📅</span>
                <div>
                  <h4 className="text-body font-sf-pro font-semibold text-label">每日签到</h4>
                  <p className="text-caption-1 text-secondary-label">
                    {checkInStatus?.hasCheckedInToday ? '今日已签到' : '获得10福气 + 连续奖励'}
                  </p>
                </div>
              </div>
              <Button
                variant={checkInStatus?.hasCheckedInToday ? "secondary" : "primary"}
                size="sm"
                onClick={handleDailyCheckIn}
                disabled={checkInStatus?.hasCheckedInToday || isProcessing}
              >
                {checkInStatus?.hasCheckedInToday ? '已签到' : '签到'}
              </Button>
            </div>
          </motion.div>

          {/* 邀请好友卡片 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-gradient-to-r from-system-purple/10 to-system-pink/10 rounded-xl p-4 border border-system-purple/20"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <span className="text-2xl">👥</span>
                <div>
                  <h4 className="text-body font-sf-pro font-semibold text-label">邀请好友</h4>
                  <p className="text-caption-1 text-secondary-label">
                    每邀请1人获得1000福气，里程碑奖励更丰厚
                  </p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-title-3 font-sf-pro font-bold text-system-purple">
                  {inviteStats?.successfulInvitations || 0}人
                </p>
                <p className="text-caption-2 text-secondary-label">已邀请</p>
              </div>
            </div>
          </motion.div>

          {/* 分享奖励卡片 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-gradient-to-r from-system-orange/10 to-system-yellow/10 rounded-xl p-4 border border-system-orange/20"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <span className="text-2xl">📤</span>
                <div>
                  <h4 className="text-body font-sf-pro font-semibold text-label">分享奖励</h4>
                  <p className="text-caption-1 text-secondary-label">
                    分享内容到社交平台，每次获得20福气
                  </p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-title-3 font-sf-pro font-bold text-system-orange">
                  {shareRewards?.totalShares || 0}次
                </p>
                <p className="text-caption-2 text-secondary-label">已分享</p>
              </div>
            </div>
          </motion.div>
        </div>
      )}

      {/* 每日签到页面 */}
      {activeTab === 'checkin' && checkInStatus && (
        <div className="space-y-4">
          <div className="text-center py-6">
            <div className="text-6xl mb-4">
              {checkInStatus.hasCheckedInToday ? '✅' : '📅'}
            </div>
            <h4 className="text-title-2 font-sf-pro font-bold text-label mb-2">
              {checkInStatus.hasCheckedInToday ? '今日已签到' : '每日签到'}
            </h4>
            <p className="text-body text-secondary-label mb-6">
              {checkInStatus.hasCheckedInToday 
                ? `获得了${checkInStatus.todayReward}福气奖励`
                : '连续签到7天可获得额外奖励'
              }
            </p>
            
            {!checkInStatus.hasCheckedInToday && (
              <Button
                variant="primary"
                size="lg"
                onClick={handleDailyCheckIn}
                disabled={isProcessing}
                className="px-8"
              >
                {isProcessing ? '签到中...' : '立即签到'}
              </Button>
            )}
          </div>

          {/* 签到进度 */}
          <div className="bg-system-gray-6 rounded-xl p-4">
            <div className="flex items-center justify-between mb-3">
              <span className="text-body font-sf-pro font-medium text-label">连续签到进度</span>
              <span className="text-caption-1 text-secondary-label">
                {checkInStatus.consecutiveDays}/7天
              </span>
            </div>
            <div className="w-full bg-system-gray-5 rounded-full h-2 mb-3">
              <div 
                className="bg-gradient-to-r from-system-green to-system-blue h-2 rounded-full transition-all duration-300"
                style={{ width: `${Math.min((checkInStatus.consecutiveDays / 7) * 100, 100)}%` }}
              ></div>
            </div>
            <p className="text-caption-1 text-secondary-label">
              {checkInStatus.encouragement}
            </p>
          </div>

          {/* 最近签到记录 */}
          {checkInStatus.recentCheckIns && checkInStatus.recentCheckIns.length > 0 && (
            <div className="bg-system-gray-6 rounded-xl p-4">
              <h5 className="text-body font-sf-pro font-semibold text-label mb-3">最近签到记录</h5>
              <div className="space-y-2">
                {checkInStatus.recentCheckIns.slice(0, 5).map((checkIn, index) => (
                  <div key={index} className="flex items-center justify-between py-2">
                    <span className="text-caption-1 text-secondary-label">
                      {new Date(checkIn.date).toLocaleDateString()}
                    </span>
                    <span className="text-caption-1 font-sf-pro font-medium text-system-green">
                      +{checkIn.reward}福气
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* 其他标签页内容... */}
      {/* 这里可以继续添加邀请和分享页面的内容 */}
    </Card>
  );
};

export default FortuneCenter;
