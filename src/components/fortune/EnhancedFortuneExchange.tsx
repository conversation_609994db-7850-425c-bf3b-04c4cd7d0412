/**
 * 增强版福气充值提现页面
 * 修复所有功能问题，提供企业级用户体验
 */

'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Loading from '@/components/ui/Loading';

interface UserBalance {
  haox: number;
  fortune: number;
}

interface Transaction {
  id: string;
  type: 'deposit' | 'withdraw';
  amount: number;
  status: 'pending' | 'completed' | 'failed';
  timestamp: string;
  txHash?: string;
}

const EnhancedFortuneExchange: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const router = useRouter();
  
  const [activeTab, setActiveTab] = useState<'deposit' | 'withdraw' | 'history'>('deposit');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  
  const [userBalance, setUserBalance] = useState<UserBalance>({
    haox: 5000,
    fortune: 1250
  });
  
  const [depositAmount, setDepositAmount] = useState('');
  const [withdrawAmount, setWithdrawAmount] = useState('');
  const [walletAddress, setWalletAddress] = useState('');
  
  const [transactions, setTransactions] = useState<Transaction[]>([
    {
      id: '1',
      type: 'deposit',
      amount: 500,
      status: 'completed',
      timestamp: '2024-01-15T10:30:00Z',
      txHash: '0x1234...abcd'
    },
    {
      id: '2',
      type: 'withdraw',
      amount: 200,
      status: 'pending',
      timestamp: '2024-01-14T15:45:00Z'
    }
  ]);

  // 处理HAOX充值福气
  const handleDeposit = async () => {
    const amount = parseFloat(depositAmount);
    
    if (!amount || amount <= 0) {
      setError('请输入有效的充值金额');
      return;
    }
    
    if (amount > userBalance.haox) {
      setError('HAOX余额不足');
      return;
    }

    setLoading(true);
    setError('');
    setSuccess('');

    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 更新余额
      setUserBalance(prev => ({
        haox: prev.haox - amount,
        fortune: prev.fortune + amount
      }));

      // 添加交易记录
      const newTransaction: Transaction = {
        id: Date.now().toString(),
        type: 'deposit',
        amount: amount,
        status: 'completed',
        timestamp: new Date().toISOString()
      };
      setTransactions(prev => [newTransaction, ...prev]);

      setSuccess(`✅ 成功充值 ${amount} 福气！HAOX已扣除相应金额。`);
      setDepositAmount('');
      
      // 3秒后清除成功消息
      setTimeout(() => setSuccess(''), 3000);
      
    } catch (error) {
      setError('充值失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 处理福气提现HAOX
  const handleWithdraw = async () => {
    const amount = parseFloat(withdrawAmount);
    
    if (!amount || amount <= 0) {
      setError('请输入有效的提现金额');
      return;
    }
    
    if (amount > userBalance.fortune) {
      setError('福气余额不足');
      return;
    }

    if (!walletAddress.trim()) {
      setError('请输入钱包地址');
      return;
    }

    setLoading(true);
    setError('');
    setSuccess('');

    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 更新余额
      setUserBalance(prev => ({
        haox: prev.haox + amount,
        fortune: prev.fortune - amount
      }));

      // 添加交易记录
      const newTransaction: Transaction = {
        id: Date.now().toString(),
        type: 'withdraw',
        amount: amount,
        status: 'completed',
        timestamp: new Date().toISOString()
      };
      setTransactions(prev => [newTransaction, ...prev]);

      setSuccess(`✅ 成功提现 ${amount} HAOX！福气已扣除相应金额。`);
      setWithdrawAmount('');
      setWalletAddress('');
      
      // 3秒后清除成功消息
      setTimeout(() => setSuccess(''), 3000);
      
    } catch (error) {
      setError('提现失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const badges: Record<string, { text: string; color: string }> = {
      pending: { text: '⏳ 处理中', color: 'bg-yellow-100 text-yellow-800 border-yellow-200' },
      completed: { text: '✅ 已完成', color: 'bg-green-100 text-green-800 border-green-200' },
      failed: { text: '❌ 失败', color: 'bg-red-100 text-red-800 border-red-200' }
    };
    return badges[status] || badges.pending;
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center">
        <div className="max-w-md mx-auto text-center p-8">
          <div className="text-6xl mb-4">🔒</div>
          <h2 className="text-2xl font-bold text-gray-900 mb-4">需要登录</h2>
          <p className="text-gray-600 mb-6">
            充值提现需要登录账户，请先完成登录。
          </p>
          <Button 
            onClick={() => router.push('/auth/login')}
            className="bg-blue-600 text-white hover:bg-blue-700"
          >
            立即登录
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* 导航栏 */}
      <div className="bg-white/80 backdrop-blur-md border-b border-gray-200/50 sticky top-0 z-40">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.back()}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              <div className="flex items-center space-x-2">
                <span className="text-2xl">💰</span>
                <h1 className="text-xl font-bold text-gray-900">福气充值提现</h1>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="hidden md:flex items-center space-x-6 text-sm text-gray-600">
                <span>首页</span>
                <span className="text-gray-400">/</span>
                <span>福气中心</span>
                <span className="text-gray-400">/</span>
                <span className="text-blue-600 font-medium">充值提现</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 余额卡片 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <div className="bg-gradient-to-r from-green-600 via-blue-600 to-purple-700 rounded-2xl p-8 text-white shadow-2xl relative overflow-hidden">
            <div className="absolute inset-0 bg-black/10"></div>
            <div className="absolute top-0 right-0 w-64 h-64 bg-white/5 rounded-full -translate-y-32 translate-x-32"></div>
            <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full translate-y-24 -translate-x-24"></div>
            
            <div className="relative z-10">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h2 className="text-2xl font-bold mb-2">我的数字资产</h2>
                  <p className="text-blue-100">HAOX与福气1:1兑换</p>
                </div>
                <div className="text-6xl opacity-20">💎</div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-blue-100">HAOX余额</span>
                    <span className="text-2xl">🪙</span>
                  </div>
                  <div className="text-3xl font-bold">{userBalance.haox.toLocaleString()}</div>
                  <div className="text-blue-100 text-sm mt-1">可用于充值福气</div>
                </div>
                
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-blue-100">福气余额</span>
                    <span className="text-2xl">🍀</span>
                  </div>
                  <div className="text-3xl font-bold">{userBalance.fortune.toLocaleString()}</div>
                  <div className="text-blue-100 text-sm mt-1">可用于参与赌约</div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* 错误和成功消息 */}
        <AnimatePresence>
          {error && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg"
            >
              <div className="flex items-center">
                <span className="text-red-600 mr-2">❌</span>
                <span className="text-red-800 font-medium">{error}</span>
              </div>
            </motion.div>
          )}
          
          {success && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg"
            >
              <div className="flex items-center">
                <span className="text-green-600 mr-2">✅</span>
                <span className="text-green-800 font-medium">{success}</span>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* 标签页 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="mb-8"
        >
          <div className="bg-white rounded-xl shadow-sm border border-gray-200/50 p-2">
            <div className="flex space-x-2">
              {[
                { key: 'deposit', label: '💰 HAOX充值福气', icon: '💰' },
                { key: 'withdraw', label: '💸 福气提现HAOX', icon: '💸' },
                { key: 'history', label: '📋 交易历史', icon: '📋' }
              ].map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setActiveTab(tab.key as any)}
                  className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-lg transition-all duration-200 ${
                    activeTab === tab.key
                      ? 'bg-blue-500 text-white shadow-md'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  <span>{tab.icon}</span>
                  <span className="font-medium">{tab.label}</span>
                </button>
              ))}
            </div>
          </div>
        </motion.div>

        {/* 充值标签页 */}
        {activeTab === 'deposit' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Card className="p-8 bg-white shadow-sm border border-gray-200/50">
              <h2 className="text-2xl font-semibold text-gray-900 mb-6 flex items-center">
                <span className="mr-3">💰</span>
                HAOX充值福气
              </h2>
              
              <div className="max-w-md mx-auto">
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      充值金额 (福气)
                    </label>
                    <input
                      type="number"
                      value={depositAmount}
                      onChange={(e) => setDepositAmount(e.target.value)}
                      placeholder="请输入充值金额"
                      min="1"
                      max={userBalance.haox}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 placeholder-gray-400"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      可用HAOX余额：{userBalance.haox.toLocaleString()}
                    </p>
                  </div>

                  <div className="bg-blue-50 p-4 rounded-lg">
                    <h3 className="font-medium text-blue-900 mb-2">兑换说明</h3>
                    <ul className="text-sm text-blue-800 space-y-1">
                      <li>• 1 HAOX = 1 福气</li>
                      <li>• 充值即时到账</li>
                      <li>• 无手续费</li>
                    </ul>
                  </div>

                  <Button
                    onClick={handleDeposit}
                    disabled={loading || !depositAmount}
                    className="w-full py-3 bg-gradient-to-r from-green-500 to-blue-600 text-white hover:from-green-600 hover:to-blue-700 disabled:opacity-50"
                  >
                    {loading ? (
                      <div className="flex items-center justify-center">
                        <Loading size="small" />
                        <span className="ml-2">充值中...</span>
                      </div>
                    ) : (
                      '💰 立即充值'
                    )}
                  </Button>
                </div>
              </div>
            </Card>
          </motion.div>
        )}

        {/* 提现标签页 */}
        {activeTab === 'withdraw' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Card className="p-8 bg-white shadow-sm border border-gray-200/50">
              <h2 className="text-2xl font-semibold text-gray-900 mb-6 flex items-center">
                <span className="mr-3">💸</span>
                福气提现HAOX
              </h2>
              
              <div className="max-w-md mx-auto">
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      提现金额 (HAOX)
                    </label>
                    <input
                      type="number"
                      value={withdrawAmount}
                      onChange={(e) => setWithdrawAmount(e.target.value)}
                      placeholder="请输入提现金额"
                      min="1"
                      max={userBalance.fortune}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 placeholder-gray-400"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      可用福气余额：{userBalance.fortune.toLocaleString()}
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      钱包地址
                    </label>
                    <input
                      type="text"
                      value={walletAddress}
                      onChange={(e) => setWalletAddress(e.target.value)}
                      placeholder="请输入HAOX钱包地址"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 placeholder-gray-400"
                    />
                  </div>

                  <div className="bg-yellow-50 p-4 rounded-lg">
                    <h3 className="font-medium text-yellow-900 mb-2">提现说明</h3>
                    <ul className="text-sm text-yellow-800 space-y-1">
                      <li>• 1 福气 = 1 HAOX</li>
                      <li>• 1-3个工作日到账</li>
                      <li>• 无手续费</li>
                    </ul>
                  </div>

                  <Button
                    onClick={handleWithdraw}
                    disabled={loading || !withdrawAmount || !walletAddress}
                    className="w-full py-3 bg-gradient-to-r from-purple-500 to-pink-600 text-white hover:from-purple-600 hover:to-pink-700 disabled:opacity-50"
                  >
                    {loading ? (
                      <div className="flex items-center justify-center">
                        <Loading size="small" />
                        <span className="ml-2">提现中...</span>
                      </div>
                    ) : (
                      '💸 立即提现'
                    )}
                  </Button>
                </div>
              </div>
            </Card>
          </motion.div>
        )}

        {/* 交易历史标签页 */}
        {activeTab === 'history' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Card className="p-6 bg-white shadow-sm border border-gray-200/50">
              <h2 className="text-2xl font-semibold text-gray-900 mb-6 flex items-center">
                <span className="mr-3">📋</span>
                交易历史
              </h2>
              
              <div className="space-y-4">
                {transactions.map((tx) => (
                  <div key={tx.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="text-2xl">
                        {tx.type === 'deposit' ? '💰' : '💸'}
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">
                          {tx.type === 'deposit' ? 'HAOX充值福气' : '福气提现HAOX'}
                        </div>
                        <div className="text-sm text-gray-500">
                          {new Date(tx.timestamp).toLocaleString()}
                        </div>
                      </div>
                    </div>
                    
                    <div className="text-right">
                      <div className="font-bold text-gray-900">
                        {tx.type === 'deposit' ? '+' : '-'}{tx.amount.toLocaleString()}
                      </div>
                      <div className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusBadge(tx.status).color}`}>
                        {getStatusBadge(tx.status).text}
                      </div>
                    </div>
                  </div>
                ))}
                
                {transactions.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <div className="text-4xl mb-2">📋</div>
                    <p>暂无交易记录</p>
                  </div>
                )}
              </div>
            </Card>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default EnhancedFortuneExchange;
