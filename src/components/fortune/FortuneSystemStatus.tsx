'use client';

import React, { useState, useEffect } from 'react';
import { Card, Button, Icon } from '@/components/ui';
import { FeatureIcons, ActionIcons } from '@/config/icons';
import { cn } from '@/lib/utils';

interface SystemStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  services: {
    application: { status: string; message: string };
    database: { status: string; message: string };
    fortuneSystem?: { status: string; message: string };
  };
}

interface FortuneSystemStatusProps {
  className?: string;
  showDetails?: boolean;
}

const FortuneSystemStatus: React.FC<FortuneSystemStatusProps> = ({ 
  className,
  showDetails = false 
}) => {
  const [status, setStatus] = useState<SystemStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showFullDetails, setShowFullDetails] = useState(showDetails);

  /**
   * 获取系统状态
   */
  const fetchSystemStatus = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/health');
      const data = await response.json();

      setStatus(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch system status');
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 获取状态颜色
   */
  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'healthy': return 'text-system-green';
      case 'degraded': return 'text-system-orange';
      case 'unhealthy': return 'text-system-red';
      default: return 'text-system-gray';
    }
  };

  /**
   * 获取状态图标
   */
  const getStatusIcon = (status: string): string => {
    switch (status) {
      case 'healthy': return '✅';
      case 'degraded': return '⚠️';
      case 'unhealthy': return '❌';
      default: return '❓';
    }
  };

  /**
   * 获取状态描述
   */
  const getStatusDescription = (status: string): string => {
    switch (status) {
      case 'healthy': return '系统运行正常';
      case 'degraded': return '系统部分功能受限';
      case 'unhealthy': return '系统存在问题';
      default: return '状态未知';
    }
  };

  // 初始加载
  useEffect(() => {
    fetchSystemStatus();
  }, []);

  // 自动刷新（每30秒）
  useEffect(() => {
    const interval = setInterval(fetchSystemStatus, 30000);
    return () => clearInterval(interval);
  }, []);

  if (isLoading && !status) {
    return (
      <Card className={cn("p-4", className)}>
        <div className="animate-pulse">
          <div className="h-4 bg-system-gray-5 rounded mb-2"></div>
          <div className="h-3 bg-system-gray-5 rounded w-3/4"></div>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={cn("p-4 border-system-red/20", className)}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-lg">❌</span>
            <div>
              <p className="text-body font-sf-pro font-medium text-system-red">
                系统状态检查失败
              </p>
              <p className="text-caption-1 text-secondary-label">{error}</p>
            </div>
          </div>
          <Button variant="ghost" size="sm" onClick={fetchSystemStatus}>
            <Icon icon={ActionIcons.refresh} size="sm" />
          </Button>
        </div>
      </Card>
    );
  }

  if (!status) {
    return null;
  }

  return (
    <Card className={cn("p-4", className)}>
      {/* 系统总体状态 */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <span className="text-xl">{getStatusIcon(status.status)}</span>
          <div>
            <h4 className="text-body font-sf-pro font-semibold text-label">
              福气系统状态
            </h4>
            <p className={cn("text-caption-1", getStatusColor(status.status))}>
              {getStatusDescription(status.status)}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={() => setShowFullDetails(!showFullDetails)}
          >
            <Icon icon={showFullDetails ? ActionIcons.chevronUp : ActionIcons.chevronDown} size="sm" />
          </Button>
          <Button variant="ghost" size="sm" onClick={fetchSystemStatus}>
            <Icon icon={ActionIcons.refresh} size="sm" />
          </Button>
        </div>
      </div>

      {/* 详细状态信息 */}
      {showFullDetails && (
        <div className="space-y-3 pt-3 border-t border-separator">
          {/* 应用服务器状态 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <span className="text-sm">{getStatusIcon(status.services.application.status)}</span>
              <span className="text-caption-1 text-label">应用服务器</span>
            </div>
            <span className={cn("text-caption-1", getStatusColor(status.services.application.status))}>
              {status.services.application.message}
            </span>
          </div>

          {/* 数据库状态 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <span className="text-sm">{getStatusIcon(status.services.database.status)}</span>
              <span className="text-caption-1 text-label">数据库</span>
            </div>
            <span className={cn("text-caption-1", getStatusColor(status.services.database.status))}>
              {status.services.database.message}
            </span>
          </div>

          {/* 福气系统状态 */}
          {status.services.fortuneSystem && (
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <span className="text-sm">{getStatusIcon(status.services.fortuneSystem.status)}</span>
                <span className="text-caption-1 text-label">福气系统</span>
              </div>
              <span className={cn("text-caption-1", getStatusColor(status.services.fortuneSystem.status))}>
                {status.services.fortuneSystem.message}
              </span>
            </div>
          )}

          {/* 最后更新时间 */}
          <div className="flex items-center justify-between pt-2 border-t border-separator">
            <span className="text-caption-2 text-tertiary-label">最后更新</span>
            <span className="text-caption-2 text-secondary-label">
              {new Date(status.timestamp).toLocaleString('zh-CN')}
            </span>
          </div>
        </div>
      )}

      {/* 快速状态指示器（简化模式） */}
      {!showFullDetails && (
        <div className="flex items-center space-x-4 text-caption-2 text-secondary-label">
          <span>应用: {getStatusIcon(status.services.application.status)}</span>
          <span>数据库: {getStatusIcon(status.services.database.status)}</span>
          {status.services.fortuneSystem && (
            <span>福气: {getStatusIcon(status.services.fortuneSystem.status)}</span>
          )}
          <span className="ml-auto">
            {new Date(status.timestamp).toLocaleTimeString('zh-CN')}
          </span>
        </div>
      )}
    </Card>
  );
};

export default FortuneSystemStatus;
