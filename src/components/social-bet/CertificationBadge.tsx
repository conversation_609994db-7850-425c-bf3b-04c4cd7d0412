'use client';

import React from 'react';
import { motion } from 'framer-motion';

interface CertificationBadgeProps {
  level: number;
  size?: 'small' | 'medium' | 'large';
  showLabel?: boolean;
  className?: string;
}

const CertificationBadge: React.FC<CertificationBadgeProps> = ({
  level,
  size = 'medium',
  showLabel = true,
  className = ''
}) => {
  const getCertificationInfo = (level: number) => {
    switch (level) {
      case 0:
        return {
          name: '新手',
          icon: '🥉',
          color: 'from-gray-400 to-gray-600',
          textColor: 'text-gray-600',
          requirement: '无要求'
        };
      case 1:
        return {
          name: '青铜',
          icon: '🥉',
          color: 'from-amber-600 to-amber-800',
          textColor: 'text-amber-700',
          requirement: '1,000 HAOX'
        };
      case 2:
        return {
          name: '白银',
          icon: '🥈',
          color: 'from-gray-400 to-gray-600',
          textColor: 'text-gray-600',
          requirement: '5,000 HAOX'
        };
      case 3:
        return {
          name: '黄金',
          icon: '🥇',
          color: 'from-yellow-400 to-yellow-600',
          textColor: 'text-yellow-600',
          requirement: '20,000 HAOX'
        };
      case 4:
        return {
          name: '铂金',
          icon: '💎',
          color: 'from-blue-400 to-blue-600',
          textColor: 'text-blue-600',
          requirement: '50,000 HAOX'
        };
      case 5:
        return {
          name: '钻石',
          icon: '💎',
          color: 'from-purple-400 to-purple-600',
          textColor: 'text-purple-600',
          requirement: '100,000 HAOX'
        };
      default:
        return {
          name: '未知',
          icon: '❓',
          color: 'from-gray-400 to-gray-600',
          textColor: 'text-gray-600',
          requirement: '未知'
        };
    }
  };

  const getBenefits = (level: number) => {
    const baseBenefits = {
      feeDiscount: level * 5, // 5% per level
      dailyJudgments: 3 + level * 5, // 3 + 5 per level
      specialFeatures: []
    };

    switch (level) {
      case 0:
        return { ...baseBenefits, feeDiscount: 0, dailyJudgments: 3 };
      case 1:
        return { 
          ...baseBenefits, 
          specialFeatures: ['基础分析工具'] 
        };
      case 2:
        return { 
          ...baseBenefits, 
          specialFeatures: ['基础分析工具', '历史数据查看'] 
        };
      case 3:
        return { 
          ...baseBenefits, 
          specialFeatures: ['基础分析工具', '历史数据查看', '高级统计'] 
        };
      case 4:
        return { 
          ...baseBenefits, 
          specialFeatures: ['基础分析工具', '历史数据查看', '高级统计', '专属赌约'] 
        };
      case 5:
        return { 
          ...baseBenefits, 
          specialFeatures: ['基础分析工具', '历史数据查看', '高级统计', '专属赌约', 'VIP客服'] 
        };
      default:
        return baseBenefits;
    }
  };

  const getSizeClasses = (size: string) => {
    switch (size) {
      case 'small':
        return {
          container: 'w-8 h-8',
          icon: 'text-lg',
          text: 'text-xs'
        };
      case 'large':
        return {
          container: 'w-16 h-16',
          icon: 'text-3xl',
          text: 'text-lg'
        };
      default: // medium
        return {
          container: 'w-12 h-12',
          icon: 'text-2xl',
          text: 'text-sm'
        };
    }
  };

  const certInfo = getCertificationInfo(level);
  const benefits = getBenefits(level);
  const sizeClasses = getSizeClasses(size);

  return (
    <div className={`inline-flex items-center space-x-2 ${className}`}>
      {/* 认证徽章 */}
      <motion.div
        className={`
          ${sizeClasses.container}
          bg-gradient-to-br ${certInfo.color}
          rounded-full
          flex items-center justify-center
          shadow-lg
          relative
          overflow-hidden
        `}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        title={`${certInfo.name}认证 - ${certInfo.requirement}`}
      >
        {/* 背景光效 */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent" />
        
        {/* 图标 */}
        <span className={`${sizeClasses.icon} relative z-10`}>
          {certInfo.icon}
        </span>
        
        {/* 等级数字 */}
        <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-white rounded-full flex items-center justify-center text-xs font-bold text-gray-800">
          {level}
        </div>
      </motion.div>

      {/* 认证信息 */}
      {showLabel && (
        <div className="flex flex-col">
          <span className={`font-semibold ${certInfo.textColor} ${sizeClasses.text}`}>
            {certInfo.name}认证
          </span>
          {size !== 'small' && (
            <span className="text-xs text-secondary-label">
              Lv.{level}
            </span>
          )}
        </div>
      )}
    </div>
  );
};

// 认证等级详情组件
export const CertificationDetails: React.FC<{ level: number }> = ({ level }) => {
  const certInfo = getCertificationInfo(level);
  const benefits = getBenefits(level);

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 max-w-md">
      <div className="flex items-center space-x-4 mb-4">
        <CertificationBadge level={level} size="large" showLabel={false} />
        <div>
          <h3 className="text-xl font-bold text-label">
            {certInfo.name}认证
          </h3>
          <p className="text-sm text-secondary-label">
            等级 {level}
          </p>
        </div>
      </div>

      <div className="space-y-4">
        <div>
          <h4 className="font-semibold text-label mb-2">🎯 认证要求</h4>
          <p className="text-sm text-secondary-label">
            持有 {certInfo.requirement}
          </p>
        </div>

        <div>
          <h4 className="font-semibold text-label mb-2">🎁 专属权益</h4>
          <ul className="space-y-1 text-sm text-secondary-label">
            <li>• 手续费折扣: {benefits.feeDiscount}%</li>
            <li>• 每日裁定次数: {benefits.dailyJudgments}次</li>
            {benefits.specialFeatures.map((feature, index) => (
              <li key={index}>• {feature}</li>
            ))}
          </ul>
        </div>

        {level < 5 && (
          <div>
            <h4 className="font-semibold text-label mb-2">⬆️ 下一等级</h4>
            <p className="text-sm text-secondary-label">
              需要持有 {getCertificationInfo(level + 1).requirement}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

// 认证进度组件
export const CertificationProgress: React.FC<{
  currentLevel: number;
  currentHolding: number;
}> = ({ currentLevel, currentHolding }) => {
  const nextLevel = Math.min(currentLevel + 1, 5);
  const nextRequirement = [0, 1000, 5000, 20000, 50000, 100000][nextLevel];
  const currentRequirement = [0, 1000, 5000, 20000, 50000, 100000][currentLevel];
  
  const progress = nextLevel > 5 ? 100 : 
    ((currentHolding - currentRequirement) / (nextRequirement - currentRequirement)) * 100;

  return (
    <div className="bg-white rounded-lg p-4 border border-system-gray-4">
      <div className="flex items-center justify-between mb-2">
        <span className="text-sm font-medium text-label">认证进度</span>
        <span className="text-xs text-secondary-label">
          {currentHolding.toLocaleString()} / {nextRequirement.toLocaleString()} HAOX
        </span>
      </div>
      
      <div className="w-full bg-system-gray-5 rounded-full h-2 mb-2">
        <div 
          className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-300"
          style={{ width: `${Math.min(progress, 100)}%` }}
        />
      </div>
      
      <div className="flex items-center justify-between">
        <CertificationBadge level={currentLevel} size="small" />
        {nextLevel <= 5 && (
          <div className="text-right">
            <div className="text-xs text-secondary-label">下一等级</div>
            <CertificationBadge level={nextLevel} size="small" />
          </div>
        )}
      </div>
    </div>
  );
};

export default CertificationBadge;
