'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON>, Card, Loading, Breadcrumb, Badge, Progress } from '@/components/ui';
import { useAuth } from '@/contexts/AuthContext';
import CertificationBadge from './CertificationBadge';

interface JudgmentDetailPageProps {
  betId: string;
}

interface JudgmentStatus {
  betInfo: any;
  judgmentHistory: any;
  currentRoundStatus: any;
  userStatus: any;
}

const JudgmentDetailPage: React.FC<JudgmentDetailPageProps> = ({ betId }) => {
  const { user, isAuthenticated } = useAuth();
  const [judgmentStatus, setJudgmentStatus] = useState<JudgmentStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    loadJudgmentStatus();
  }, [betId, user]);

  const loadJudgmentStatus = async () => {
    try {
      setLoading(true);
      
      const url = `/api/social-bet/${betId}/judgment-status${user?.id ? `?userId=${user.id}` : ''}`;
      const response = await fetch(url);
      const result = await response.json();

      if (result.success) {
        setJudgmentStatus(result.data);
      } else {
        console.error('获取裁定状态失败:', result.message);
      }
    } catch (error) {
      console.error('加载裁定状态失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const submitVote = async (voteOption: number) => {
    if (!user?.id || !judgmentStatus) return;

    try {
      setSubmitting(true);

      const response = await fetch(`/api/social-bet/${betId}/judge`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          round: judgmentStatus.currentRoundStatus.round,
          voteOption,
          userId: user.id
        })
      });

      const result = await response.json();

      if (result.success) {
        alert('投票提交成功！');
        // 重新加载状态
        loadJudgmentStatus();
      } else {
        alert(`投票失败: ${result.message}`);
      }
    } catch (error) {
      console.error('提交投票失败:', error);
      alert('投票失败，请稍后重试');
    } finally {
      setSubmitting(false);
    }
  };

  const formatTimeRemaining = (deadline: string): string => {
    const now = new Date();
    const deadlineDate = new Date(deadline);
    const diff = deadlineDate.getTime() - now.getTime();
    
    if (diff <= 0) return '已截止';
    
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    if (days > 0) return `${days}天${hours}小时`;
    if (hours > 0) return `${hours}小时${minutes}分钟`;
    return `${minutes}分钟`;
  };

  const getRoundName = (round: number): string => {
    const names = { 1: '大众评审', 2: '专业评审', 3: '终审裁定' };
    return names[round] || `第${round}轮`;
  };

  const getRoundDescription = (round: number): string => {
    const descriptions = {
      1: '所有认证用户(X1-X5)可参与，需要11/20票通过',
      2: 'X1-X3认证用户可参与，需要6/10票通过',
      3: 'X4-X5认证用户可参与，需要3/5票通过'
    };
    return descriptions[round] || '';
  };

  if (!isAuthenticated) {
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Breadcrumb
          items={[
            { label: '首页', href: '/', icon: '🏠' },
            { label: '社交赌注', href: '/social-bet', icon: '🎲' },
            { label: '参与裁定', href: '/social-bet/judgment', icon: '⚖️' },
            { label: '裁定详情', icon: '📋' }
          ]}
        />
        
        <div className="text-center py-12">
          <div className="text-6xl mb-4">🔒</div>
          <h2 className="text-2xl font-bold text-label mb-4">需要登录</h2>
          <p className="text-secondary-label mb-6">
            查看裁定详情需要登录账户，请先完成登录。
          </p>
          <Button href="/auth/login">
            立即登录
          </Button>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-center items-center min-h-96">
          <Loading size="large" />
        </div>
      </div>
    );
  }

  if (!judgmentStatus) {
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center py-12">
          <div className="text-6xl mb-4">❌</div>
          <h2 className="text-2xl font-bold text-label mb-4">赌约不存在</h2>
          <p className="text-secondary-label mb-6">
            请检查链接是否正确，或返回裁定列表。
          </p>
          <Button href="/social-bet/judgment">
            返回裁定列表
          </Button>
        </div>
      </div>
    );
  }

  const { betInfo, judgmentHistory, currentRoundStatus, userStatus } = judgmentStatus;
  const canVote = userStatus && !userStatus.isParticipant && !userStatus.votes[currentRoundStatus?.round];

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* 面包屑导航 */}
      <div className="mb-6">
        <Breadcrumb
          items={[
            { label: '首页', href: '/', icon: '🏠' },
            { label: '社交赌注', href: '/social-bet', icon: '🎲' },
            { label: '参与裁定', href: '/social-bet/judgment', icon: '⚖️' },
            { label: betInfo.title, icon: '📋' }
          ]}
        />
      </div>

      {/* 赌约基本信息 */}
      <Card className="p-6 mb-6">
        <div className="flex justify-between items-start mb-4">
          <div className="flex-1">
            <h1 className="text-2xl font-bold text-label mb-2">
              {betInfo.title}
            </h1>
            <p className="text-secondary-label mb-4">
              {betInfo.description}
            </p>
            <div className="flex items-center space-x-4 text-sm text-secondary-label">
              <span>💰 奖池: {betInfo.total_fortune_pool || 0}福气</span>
              <span>📅 开始时间: {new Date(betInfo.judging_started_at).toLocaleString()}</span>
            </div>
          </div>
          <Badge className="bg-purple-100 text-purple-800">
            {betInfo.status === 'judging' ? '裁定中' : '已完成'}
          </Badge>
        </div>

        {/* 选项展示 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {betInfo.options.map((option: string, index: number) => (
            <div key={index} className="p-3 border rounded-lg text-center">
              <span className="font-medium text-label">{option}</span>
            </div>
          ))}
        </div>
      </Card>

      {/* 当前轮次状态 */}
      {currentRoundStatus && (
        <Card className="p-6 mb-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-bold text-label">
              {getRoundName(currentRoundStatus.round)}
            </h2>
            <Badge className="bg-blue-100 text-blue-800">
              第{currentRoundStatus.round}轮
            </Badge>
          </div>

          <p className="text-sm text-secondary-label mb-4">
            {getRoundDescription(currentRoundStatus.round)}
          </p>

          {/* 投票进度 */}
          <div className="mb-4">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm text-secondary-label">投票进度</span>
              <span className="text-sm font-medium text-label">
                {currentRoundStatus.currentVotes}/{currentRoundStatus.requiredVotes}票
              </span>
            </div>
            <Progress 
              value={currentRoundStatus.progress} 
              className="h-2"
            />
          </div>

          {/* 剩余时间 */}
          <div className="flex justify-between items-center mb-6">
            <span className="text-sm text-secondary-label">剩余时间</span>
            <span className="text-sm font-medium text-label">
              {formatTimeRemaining(betInfo.round_deadline)}
            </span>
          </div>

          {/* 投票选项 */}
          {canVote && currentRoundStatus.round && (
            <div className="space-y-3">
              <h3 className="font-semibold text-label">请选择您的裁定结果：</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                {betInfo.options.map((option: string, index: number) => (
                  <Button
                    key={index}
                    onClick={() => submitVote(index)}
                    disabled={submitting}
                    className="bg-gradient-to-r from-purple-500 to-pink-600 text-white"
                  >
                    {submitting ? '提交中...' : `投票: ${option}`}
                  </Button>
                ))}
              </div>
            </div>
          )}

          {/* 用户状态提示 */}
          {userStatus?.isParticipant && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <p className="text-yellow-800">
                ⚠️ 您是此赌约的参与者，无法担任裁判
              </p>
            </div>
          )}

          {userStatus?.votes[currentRoundStatus.round] && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <p className="text-green-800">
                ✅ 您已在第{currentRoundStatus.round}轮投票，选择了: {betInfo.options[userStatus.votes[currentRoundStatus.round].voteOption]}
              </p>
            </div>
          )}
        </Card>
      )}

      {/* 裁定历史 */}
      {judgmentHistory && Object.keys(judgmentHistory).length > 0 && (
        <Card className="p-6">
          <h2 className="text-xl font-bold text-label mb-4">裁定历史</h2>
          
          {Object.entries(judgmentHistory).map(([round, votes]: [string, any]) => (
            <div key={round} className="mb-6 last:mb-0">
              <h3 className="font-semibold text-label mb-3">
                {getRoundName(parseInt(round))} - {votes.length}票
              </h3>
              
              <div className="space-y-2">
                {votes.map((vote: any, index: number) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <CertificationBadge level={vote.certificationLevel.replace('X', '')} size="small" />
                      <div>
                        <p className="font-medium text-label">
                          {vote.judge.telegramId || vote.judge.username}
                        </p>
                        <p className="text-xs text-secondary-label">
                          信誉分数: {vote.reputationScore}
                        </p>
                      </div>
                    </div>
                    
                    <div className="text-right">
                      <p className="font-medium text-label">
                        投票: {betInfo.options[vote.voteOption]}
                      </p>
                      <p className="text-xs text-secondary-label">
                        {new Date(vote.votedAt).toLocaleString()}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </Card>
      )}
    </div>
  );
};

export default JudgmentDetailPage;
