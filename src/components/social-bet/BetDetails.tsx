'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button, Card, Input, Loading, Progress } from '@/components/ui';
import { useAuth } from '@/hooks/useAuth';

interface BetOption {
  id: string;
  text: string;
  participants: number;
  totalAmount: number;
}

interface SocialBet {
  id: string;
  title: string;
  description: string;
  category: string;
  betType: '1v1' | '1vN';
  status: string;
  totalPool: number;
  participantCount: number;
  minBetAmount: number;
  maxBetAmount: number;
  bettingDeadline: string;
  resultDeadline: string;
  createdAt: string;
  creatorId: string;
  options: BetOption[];
  winningOption?: string;
}

interface BetDetailsProps {
  betId: string;
}

const BetDetails: React.FC<BetDetailsProps> = ({ betId }) => {
  const { user, isAuthenticated } = useAuth();
  const [bet, setBet] = useState<SocialBet | null>(null);
  const [loading, setLoading] = useState(true);
  const [participating, setParticipating] = useState(false);
  const [selectedOption, setSelectedOption] = useState<string>('');
  const [betAmount, setBetAmount] = useState<number>(0);
  const [userParticipation, setUserParticipation] = useState<any>(null);

  useEffect(() => {
    loadBetDetails();
  }, [betId]);

  const loadBetDetails = async () => {
    try {
      setLoading(true);
      
      const response = await fetch(`/api/social-bet/bets/${betId}`);
      if (response.ok) {
        const data = await response.json();
        setBet(data.data);
        setBetAmount(data.data.minBetAmount);
      } else {
        console.error('加载赌约详情失败');
      }

      // 如果用户已登录，检查是否已参与
      if (isAuthenticated && user) {
        const participationResponse = await fetch(`/api/social-bet/participation?betId=${betId}&userId=${user.id}`);
        if (participationResponse.ok) {
          const participationData = await participationResponse.json();
          setUserParticipation(participationData.data);
        }
      }
    } catch (error) {
      console.error('加载赌约详情失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleParticipate = async () => {
    if (!isAuthenticated || !selectedOption || !betAmount) {
      alert('请选择投注选项和金额');
      return;
    }

    setParticipating(true);
    
    try {
      const response = await fetch('/api/social-bet/participate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          betId,
          selectedOption,
          betAmount
        })
      });

      if (response.ok) {
        alert('投注成功！');
        loadBetDetails(); // 重新加载数据
      } else {
        const error = await response.json();
        alert(`投注失败: ${error.error || '未知错误'}`);
      }
    } catch (error) {
      console.error('投注失败:', error);
      alert('投注失败，请稍后重试');
    } finally {
      setParticipating(false);
    }
  };

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'open': return 'text-green-500';
      case 'betting_closed': return 'text-yellow-500';
      case 'judging': return 'text-blue-500';
      case 'confirming': return 'text-purple-500';
      case 'settled': return 'text-gray-500';
      default: return 'text-gray-500';
    }
  };

  const getStatusText = (status: string): string => {
    switch (status) {
      case 'open': return '开放投注';
      case 'betting_closed': return '投注截止';
      case 'judging': return '裁定中';
      case 'confirming': return '确认中';
      case 'settled': return '已结算';
      default: return '未知状态';
    }
  };

  const formatTimeRemaining = (deadline: string): string => {
    const now = new Date();
    const deadlineDate = new Date(deadline);
    const diff = deadlineDate.getTime() - now.getTime();
    
    if (diff <= 0) return '已截止';
    
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    if (days > 0) return `${days}天${hours}小时`;
    if (hours > 0) return `${hours}小时${minutes}分钟`;
    return `${minutes}分钟`;
  };

  const calculateOptionPercentage = (option: BetOption): number => {
    if (!bet || bet.totalPool === 0) return 0;
    return (option.totalAmount / bet.totalPool) * 100;
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <Loading size="large" />
      </div>
    );
  }

  if (!bet) {
    return (
      <div className="text-center py-12">
        <div className="text-6xl mb-4">❌</div>
        <h3 className="text-xl font-semibold text-label mb-2">赌约不存在</h3>
        <p className="text-secondary-label mb-6">
          该赌约可能已被删除或不存在
        </p>
        <Button href="/social-bet">
          返回赌约列表
        </Button>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* 赌约标题和状态 */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-3xl font-bold text-label">
            {bet.title}
          </h1>
          <span className={`text-sm font-medium px-3 py-1 rounded-full ${getStatusColor(bet.status)} bg-opacity-10`}>
            {getStatusText(bet.status)}
          </span>
        </div>
        
        <p className="text-secondary-label text-lg">
          {bet.description}
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* 主要内容 */}
        <div className="lg:col-span-2 space-y-6">
          {/* 投注选项 */}
          <Card className="p-6">
            <h2 className="text-xl font-semibold text-label mb-6">🎲 投注选项</h2>
            
            <div className="space-y-4">
              {bet.options.map((option) => {
                const percentage = calculateOptionPercentage(option);
                const isWinning = bet.winningOption === option.id;
                
                return (
                  <div
                    key={option.id}
                    className={`p-4 border rounded-lg transition-colors ${
                      selectedOption === option.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-system-gray-4 hover:border-system-gray-3'
                    } ${isWinning ? 'ring-2 ring-green-500' : ''}`}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-3">
                        {bet.status === 'open' && !userParticipation && (
                          <input
                            type="radio"
                            name="betOption"
                            value={option.id}
                            checked={selectedOption === option.id}
                            onChange={(e) => setSelectedOption(e.target.value)}
                            className="w-4 h-4 text-blue-600"
                          />
                        )}
                        <span className="font-medium text-label">
                          {option.text}
                        </span>
                        {isWinning && (
                          <span className="text-green-500 text-sm">🏆 获胜</span>
                        )}
                      </div>
                      
                      <div className="text-right">
                        <div className="text-lg font-bold text-label">
                          {option.totalAmount}福气
                        </div>
                        <div className="text-sm text-secondary-label">
                          {option.participants}人投注
                        </div>
                      </div>
                    </div>
                    
                    <div className="mt-2">
                      <div className="flex justify-between text-sm text-secondary-label mb-1">
                        <span>投注占比</span>
                        <span>{percentage.toFixed(1)}%</span>
                      </div>
                      <Progress value={percentage} className="h-2" />
                    </div>
                  </div>
                );
              })}
            </div>

            {/* 投注表单 */}
            {bet.status === 'open' && isAuthenticated && !userParticipation && (
              <div className="mt-6 p-4 bg-system-gray-6 rounded-lg">
                <h3 className="font-semibold text-label mb-4">💰 参与投注</h3>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-label mb-2">
                      投注金额 (福气)
                    </label>
                    <Input
                      type="number"
                      value={betAmount}
                      onChange={(e) => setBetAmount(parseInt(e.target.value) || 0)}
                      min={bet.minBetAmount}
                      max={bet.maxBetAmount}
                      placeholder={`${bet.minBetAmount} - ${bet.maxBetAmount}`}
                    />
                    <p className="text-xs text-secondary-label mt-1">
                      最小: {bet.minBetAmount}福气，最大: {bet.maxBetAmount}福气
                    </p>
                  </div>
                  
                  <Button
                    onClick={handleParticipate}
                    disabled={!selectedOption || participating || betAmount < bet.minBetAmount}
                    className="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white"
                  >
                    {participating ? <Loading size="small" /> : '🎯 确认投注'}
                  </Button>
                </div>
              </div>
            )}

            {/* 用户已参与提示 */}
            {userParticipation && (
              <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-center space-x-2">
                  <span className="text-green-500">✅</span>
                  <span className="font-medium text-green-700">
                    您已参与此赌约
                  </span>
                </div>
                <div className="mt-2 text-sm text-green-600">
                  选择: {userParticipation.selectedOption} | 
                  投注: {userParticipation.betAmount}福气
                </div>
              </div>
            )}
          </Card>

          {/* 赌约规则 */}
          <Card className="p-6">
            <h2 className="text-xl font-semibold text-label mb-4">📋 赌约规则</h2>
            
            <div className="space-y-3 text-sm text-secondary-label">
              <div>• 投注截止后将不能再参与投注</div>
              <div>• 结果截止后将进入三轮DAO裁定流程</div>
              <div>• 裁定完成后需要双方确认结果</div>
              <div>• 确认完成后自动分配奖励给获胜者</div>
              <div>• 平台收取5%手续费，10%用于转发奖励</div>
            </div>
          </Card>
        </div>

        {/* 侧边栏 */}
        <div className="space-y-6">
          {/* 赌约信息 */}
          <Card className="p-6">
            <h3 className="font-semibold text-label mb-4">📊 赌约信息</h3>
            
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-secondary-label">奖池总额</span>
                <span className="font-bold text-label">{bet.totalPool}福气</span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-secondary-label">参与人数</span>
                <span className="font-bold text-label">{bet.participantCount}人</span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-secondary-label">赌约类型</span>
                <span className="font-bold text-label">
                  {bet.betType === '1v1' ? '1对1' : '1对多'}
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-secondary-label">分类</span>
                <span className="font-bold text-label">{bet.category}</span>
              </div>
            </div>
          </Card>

          {/* 时间信息 */}
          <Card className="p-6">
            <h3 className="font-semibold text-label mb-4">⏰ 时间信息</h3>
            
            <div className="space-y-3">
              {bet.status === 'open' && (
                <div>
                  <div className="text-sm text-secondary-label">投注截止</div>
                  <div className="font-bold text-label">
                    {formatTimeRemaining(bet.bettingDeadline)}
                  </div>
                </div>
              )}
              
              <div>
                <div className="text-sm text-secondary-label">结果截止</div>
                <div className="font-bold text-label">
                  {formatTimeRemaining(bet.resultDeadline)}
                </div>
              </div>
              
              <div>
                <div className="text-sm text-secondary-label">创建时间</div>
                <div className="font-bold text-label">
                  {new Date(bet.createdAt).toLocaleDateString()}
                </div>
              </div>
            </div>
          </Card>

          {/* 操作按钮 */}
          <div className="space-y-3">
            <Button
              href="/social-bet"
              variant="outline"
              className="w-full"
            >
              返回列表
            </Button>
            
            {bet.status === 'judging' && isAuthenticated && (
              <Button
                href={`/social-bet/judgment/${bet.id}`}
                className="w-full bg-gradient-to-r from-purple-500 to-pink-600 text-white"
              >
                ⚖️ 参与裁定
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default BetDetails;
