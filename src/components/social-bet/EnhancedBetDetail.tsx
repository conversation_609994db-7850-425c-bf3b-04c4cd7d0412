/**
 * 增强版赌约详情页面
 * 显示赌约详细信息，支持投注、分享、邀请等功能
 */

'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Loading from '@/components/ui/Loading';

interface BetDetail {
  id: string;
  title: string;
  description: string;
  category: string;
  creator: {
    id: string;
    username: string;
    certificationLevel: string;
    reputationScore: number;
  };
  status: 'active' | 'ended' | 'judging';
  betType: '1v1' | '1vN';
  minBetAmount: number;
  maxBetAmount: number;
  totalPool: number;
  participants: number;
  bettingDeadline: string;
  resultDeadline: string;
  createdAt: string;
  options: Array<{
    id: string;
    text: string;
    odds: number;
    bets: number;
    percentage: number;
  }>;
  myBet?: {
    amount: number;
    selectedOption: string;
    timestamp: string;
  };
  result?: {
    winningOption: string;
    finalizedAt: string;
    totalWinners: number;
    totalPayout: number;
  };
  evidence?: string[];
  judgmentRounds?: Array<{
    round: number;
    status: 'pending' | 'completed';
    votes: Array<{
      option: string;
      count: number;
    }>;
    deadline: string;
  }>;
}

interface EnhancedBetDetailProps {
  betId: string;
}

const EnhancedBetDetail: React.FC<EnhancedBetDetailProps> = ({ betId }) => {
  const { user, isAuthenticated } = useAuth();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [betting, setBetting] = useState(false);
  const [sharing, setSharing] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  
  const [betDetail, setBetDetail] = useState<BetDetail | null>(null);
  const [selectedOption, setSelectedOption] = useState('');
  const [betAmount, setBetAmount] = useState('');
  const [showBetForm, setShowBetForm] = useState(false);

  useEffect(() => {
    loadBetDetail();
  }, [betId]);

  const loadBetDetail = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/social-bet/${betId}?userId=${user?.id || ''}`);
      const result = await response.json();

      if (result.success) {
        setBetDetail(result.data);
      } else {
        setError(result.message || '加载失败');
      }
    } catch (error) {
      console.error('加载赌约详情失败:', error);
      setError('加载失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const handlePlaceBet = async () => {
    if (!isAuthenticated) {
      router.push('/auth/login');
      return;
    }

    if (!selectedOption || !betAmount) {
      setError('请选择投注选项和金额');
      return;
    }

    const amount = parseFloat(betAmount);
    if (amount < betDetail!.minBetAmount || amount > betDetail!.maxBetAmount) {
      setError(`投注金额必须在 ${betDetail!.minBetAmount} - ${betDetail!.maxBetAmount} 福气之间`);
      return;
    }

    try {
      setBetting(true);
      setError('');

      const response = await fetch('/api/social-bet/place-bet', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          betId,
          selectedOption,
          amount
        })
      });

      const result = await response.json();

      if (result.success) {
        setSuccess(`🎉 投注成功！您投注了 ${amount} 福气在选项 "${betDetail!.options.find(opt => opt.id === selectedOption)?.text}" 上`);
        setShowBetForm(false);
        setSelectedOption('');
        setBetAmount('');
        
        // 重新加载赌约详情
        loadBetDetail();
        
        // 3秒后清除成功消息
        setTimeout(() => setSuccess(''), 3000);
      } else {
        setError(result.message || '投注失败');
      }
    } catch (error) {
      console.error('投注失败:', error);
      setError('投注失败，请稍后重试');
    } finally {
      setBetting(false);
    }
  };

  const handleShare = async (platform: 'wechat' | 'weibo' | 'qq' | 'link') => {
    try {
      setSharing(true);
      
      const shareUrl = `${window.location.origin}/social-bet/${betId}?ref=${user?.id || 'guest'}`;
      const shareText = `我在SocioMint发现了一个有趣的赌约：${betDetail?.title}，快来参与吧！`;

      // 记录分享行为
      if (user?.id) {
        await fetch('/api/social-bet/share', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            betId,
            platform,
            userId: user.id
          })
        });
      }

      // 根据平台执行分享
      switch (platform) {
        case 'link':
          await navigator.clipboard.writeText(shareUrl);
          setSuccess('✅ 链接已复制到剪贴板！');
          break;
        case 'wechat':
          // 微信分享逻辑
          setSuccess('✅ 请在微信中分享此链接');
          break;
        case 'weibo':
          window.open(`https://service.weibo.com/share/share.php?url=${encodeURIComponent(shareUrl)}&title=${encodeURIComponent(shareText)}`);
          break;
        case 'qq':
          window.open(`https://connect.qq.com/widget/shareqq/index.html?url=${encodeURIComponent(shareUrl)}&title=${encodeURIComponent(shareText)}`);
          break;
      }

      setTimeout(() => setSuccess(''), 3000);
    } catch (error) {
      console.error('分享失败:', error);
      setError('分享失败，请稍后重试');
    } finally {
      setSharing(false);
    }
  };

  const getCategoryIcon = (category: string) => {
    const icons: Record<string, string> = {
      sports: '🏈',
      politics: '🏛️',
      technology: '💻',
      entertainment: '🎬',
      finance: '💰',
      weather: '🌤️',
      social: '👥',
      other: '🔮'
    };
    return icons[category] || '🎲';
  };

  const getStatusBadge = (status: string) => {
    const badges: Record<string, { text: string; color: string }> = {
      active: { text: '🔥 火热进行', color: 'bg-green-100 text-green-800 border-green-200' },
      ended: { text: '✅ 已结束', color: 'bg-gray-100 text-gray-800 border-gray-200' },
      judging: { text: '⚖️ 裁定中', color: 'bg-yellow-100 text-yellow-800 border-yellow-200' }
    };
    return badges[status] || badges.active;
  };

  const getCertificationInfo = (level: string) => {
    const certifications: Record<string, { name: string; color: string; icon: string }> = {
      X1: { name: '新手认证', color: 'from-gray-400 to-gray-600', icon: '🥉' },
      X2: { name: '进阶认证', color: 'from-blue-400 to-blue-600', icon: '🥈' },
      X3: { name: '专家认证', color: 'from-purple-400 to-purple-600', icon: '🏅' },
      X4: { name: '大师认证', color: 'from-yellow-400 to-yellow-600', icon: '👑' },
      X5: { name: '传奇认证', color: 'from-red-400 to-red-600', icon: '💎' }
    };
    return certifications[level] || certifications.X1;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <Loading size="large" />
          <p className="mt-4 text-gray-600 font-medium">加载中...</p>
        </div>
      </div>
    );
  }

  if (!betDetail) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center">
        <div className="max-w-md mx-auto text-center p-8">
          <div className="text-6xl mb-4">❌</div>
          <h2 className="text-2xl font-bold text-gray-900 mb-4">赌约不存在</h2>
          <p className="text-gray-600 mb-6">
            抱歉，您访问的赌约不存在或已被删除。
          </p>
          <Button 
            onClick={() => router.push('/social-bet')}
            className="bg-blue-600 text-white hover:bg-blue-700"
          >
            返回赌约列表
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* 导航栏 */}
      <div className="bg-white/80 backdrop-blur-md border-b border-gray-200/50 sticky top-0 z-40">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.back()}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              <div className="flex items-center space-x-2">
                <span className="text-2xl">{getCategoryIcon(betDetail.category)}</span>
                <h1 className="text-xl font-bold text-gray-900">赌约详情</h1>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="hidden md:flex items-center space-x-6 text-sm text-gray-600">
                <span>首页</span>
                <span className="text-gray-400">/</span>
                <span>社交赌约</span>
                <span className="text-gray-400">/</span>
                <span className="text-blue-600 font-medium">赌约详情</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 错误和成功消息 */}
        <AnimatePresence>
          {error && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg"
            >
              <div className="flex items-center">
                <span className="text-red-600 mr-2">❌</span>
                <span className="text-red-800 font-medium">{error}</span>
              </div>
            </motion.div>
          )}
          
          {success && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg"
            >
              <div className="flex items-center">
                <span className="text-green-600 mr-2">✅</span>
                <span className="text-green-800 font-medium">{success}</span>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 主要内容区域 */}
          <div className="lg:col-span-2 space-y-8">
            {/* 赌约基本信息 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <Card className="p-8 bg-white shadow-sm border border-gray-200/50">
                <div className="flex items-start justify-between mb-6">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-4">
                      <span className="text-3xl">{getCategoryIcon(betDetail.category)}</span>
                      <div className={`px-3 py-1 rounded-full text-sm font-medium border ${getStatusBadge(betDetail.status).color}`}>
                        {getStatusBadge(betDetail.status).text}
                      </div>
                      <div className="px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 border border-blue-200">
                        {betDetail.betType === '1v1' ? '1对1对决' : '1对多参与'}
                      </div>
                    </div>
                    <h1 className="text-3xl font-bold text-gray-900 mb-4">{betDetail.title}</h1>
                    <p className="text-gray-600 text-lg leading-relaxed">{betDetail.description}</p>
                  </div>
                </div>

                {/* 创建者信息 */}
                <div className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg mb-6">
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
                    {betDetail.creator.username.charAt(0).toUpperCase()}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <span className="font-semibold text-gray-900">{betDetail.creator.username}</span>
                      <div className={`px-2 py-1 rounded-full bg-gradient-to-r ${getCertificationInfo(betDetail.creator.certificationLevel).color} text-white text-xs font-medium flex items-center space-x-1`}>
                        <span>{getCertificationInfo(betDetail.creator.certificationLevel).icon}</span>
                        <span>{getCertificationInfo(betDetail.creator.certificationLevel).name}</span>
                      </div>
                    </div>
                    <div className="text-sm text-gray-600">
                      信誉积分: {betDetail.creator.reputationScore} | 创建时间: {new Date(betDetail.createdAt).toLocaleString()}
                    </div>
                  </div>
                </div>

                {/* 时间信息 */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                  <div className="p-4 bg-blue-50 rounded-lg">
                    <div className="text-sm font-medium text-blue-900 mb-1">投注截止时间</div>
                    <div className="text-blue-800 font-semibold">{new Date(betDetail.bettingDeadline).toLocaleString()}</div>
                  </div>
                  <div className="p-4 bg-purple-50 rounded-lg">
                    <div className="text-sm font-medium text-purple-900 mb-1">结果截止时间</div>
                    <div className="text-purple-800 font-semibold">{new Date(betDetail.resultDeadline).toLocaleString()}</div>
                  </div>
                </div>

                {/* 投注选项 */}
                <div className="mb-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">投注选项</h3>
                  <div className="space-y-3">
                    {betDetail.options.map((option) => (
                      <div key={option.id} className="p-4 border border-gray-200 rounded-lg hover:border-blue-300 transition-colors">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-bold">
                              {option.id}
                            </div>
                            <span className="font-medium text-gray-900">{option.text}</span>
                          </div>
                          <div className="text-right">
                            <div className="text-lg font-bold text-blue-600">{option.odds}x</div>
                            <div className="text-xs text-gray-500">{option.percentage.toFixed(1)}%</div>
                          </div>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${option.percentage}%` }}
                          ></div>
                        </div>
                        <div className="text-sm text-gray-600 mt-1">
                          {option.bets.toLocaleString()} 福气 ({option.percentage.toFixed(1)}%)
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 我的投注信息 */}
                {betDetail.myBet && (
                  <div className="p-4 bg-green-50 border border-green-200 rounded-lg mb-6">
                    <h4 className="font-semibold text-green-900 mb-2">我的投注</h4>
                    <div className="text-green-800">
                      选项: {betDetail.options.find(opt => opt.id === betDetail.myBet!.selectedOption)?.text}
                    </div>
                    <div className="text-green-800">
                      金额: {betDetail.myBet.amount.toLocaleString()} 福气
                    </div>
                    <div className="text-green-700 text-sm">
                      投注时间: {new Date(betDetail.myBet.timestamp).toLocaleString()}
                    </div>
                  </div>
                )}

                {/* 结果信息 */}
                {betDetail.result && (
                  <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <h4 className="font-semibold text-yellow-900 mb-2">赌约结果</h4>
                    <div className="text-yellow-800">
                      获胜选项: {betDetail.options.find(opt => opt.id === betDetail.result!.winningOption)?.text}
                    </div>
                    <div className="text-yellow-800">
                      总获胜者: {betDetail.result.totalWinners} 人
                    </div>
                    <div className="text-yellow-800">
                      总奖金: {betDetail.result.totalPayout.toLocaleString()} 福气
                    </div>
                    <div className="text-yellow-700 text-sm">
                      结算时间: {new Date(betDetail.result.finalizedAt).toLocaleString()}
                    </div>
                  </div>
                )}
              </Card>
            </motion.div>

            {/* 投注表单 */}
            {betDetail.status === 'active' && !betDetail.myBet && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
              >
                <Card className="p-6 bg-white shadow-sm border border-gray-200/50">
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">🎯 参与投注</h3>
                  
                  {!isAuthenticated ? (
                    <div className="text-center py-8">
                      <div className="text-4xl mb-4">🔒</div>
                      <p className="text-gray-600 mb-4">请先登录后参与投注</p>
                      <Button
                        onClick={() => router.push('/auth/login')}
                        className="bg-blue-600 text-white hover:bg-blue-700"
                      >
                        立即登录
                      </Button>
                    </div>
                  ) : showBetForm ? (
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">选择投注选项:</label>
                        <div className="space-y-2">
                          {betDetail.options.map((option) => (
                            <label key={option.id} className="flex items-center p-3 border border-gray-200 rounded-lg hover:border-blue-300 cursor-pointer">
                              <input
                                type="radio"
                                name="betOption"
                                value={option.id}
                                checked={selectedOption === option.id}
                                onChange={(e) => setSelectedOption(e.target.value)}
                                className="mr-3"
                              />
                              <div className="flex-1">
                                <span className="font-medium">{option.text}</span>
                                <div className="text-sm text-gray-600">赔率: {option.odds}x</div>
                              </div>
                            </label>
                          ))}
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">投注金额 (福气):</label>
                        <input
                          type="number"
                          value={betAmount}
                          onChange={(e) => setBetAmount(e.target.value)}
                          placeholder={`最小 ${betDetail.minBetAmount}，最大 ${betDetail.maxBetAmount}`}
                          min={betDetail.minBetAmount}
                          max={betDetail.maxBetAmount}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                        <p className="text-xs text-gray-500 mt-1">
                          投注范围: {betDetail.minBetAmount} - {betDetail.maxBetAmount} 福气
                        </p>
                      </div>

                      <div className="flex space-x-3">
                        <Button
                          onClick={handlePlaceBet}
                          disabled={betting || !selectedOption || !betAmount}
                          className="flex-1 bg-gradient-to-r from-green-500 to-blue-600 text-white hover:from-green-600 hover:to-blue-700 disabled:opacity-50"
                        >
                          {betting ? (
                            <div className="flex items-center justify-center">
                              <Loading size="small" />
                              <span className="ml-2">投注中...</span>
                            </div>
                          ) : (
                            '🎯 确认投注'
                          )}
                        </Button>
                        <Button
                          onClick={() => setShowBetForm(false)}
                          variant="outline"
                          className="border-gray-300 text-gray-600 hover:bg-gray-50"
                        >
                          取消
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center">
                      <Button
                        onClick={() => setShowBetForm(true)}
                        className="bg-gradient-to-r from-green-500 to-blue-600 text-white hover:from-green-600 hover:to-blue-700"
                      >
                        🎯 开始投注
                      </Button>
                    </div>
                  )}
                </Card>
              </motion.div>
            )}
          </div>

          {/* 侧边栏 */}
          <div className="space-y-6">
            {/* 赌约统计 */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <Card className="p-6 bg-white shadow-sm border border-gray-200/50">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">📊 赌约统计</h3>
                
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-gray-600">总奖池</span>
                    <span className="font-semibold text-gray-900">{betDetail.totalPool.toLocaleString()} 福气</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">参与人数</span>
                    <span className="font-semibold text-gray-900">{betDetail.participants} 人</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">投注范围</span>
                    <span className="font-semibold text-gray-900">{betDetail.minBetAmount}-{betDetail.maxBetAmount} 福气</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">赌约类型</span>
                    <span className="font-semibold text-gray-900">{betDetail.betType === '1v1' ? '1对1' : '1对多'}</span>
                  </div>
                </div>
              </Card>
            </motion.div>

            {/* 分享功能 */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <Card className="p-6 bg-white shadow-sm border border-gray-200/50">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">📤 分享赌约</h3>
                
                <div className="grid grid-cols-2 gap-3">
                  <Button
                    onClick={() => handleShare('wechat')}
                    disabled={sharing}
                    className="bg-green-500 text-white hover:bg-green-600 text-sm"
                  >
                    💬 微信
                  </Button>
                  <Button
                    onClick={() => handleShare('weibo')}
                    disabled={sharing}
                    className="bg-red-500 text-white hover:bg-red-600 text-sm"
                  >
                    📱 微博
                  </Button>
                  <Button
                    onClick={() => handleShare('qq')}
                    disabled={sharing}
                    className="bg-blue-500 text-white hover:bg-blue-600 text-sm"
                  >
                    🐧 QQ
                  </Button>
                  <Button
                    onClick={() => handleShare('link')}
                    disabled={sharing}
                    className="bg-gray-500 text-white hover:bg-gray-600 text-sm"
                  >
                    🔗 复制链接
                  </Button>
                </div>
                
                <div className="mt-4 p-3 bg-yellow-50 rounded-lg">
                  <div className="text-sm text-yellow-800">
                    💡 分享赌约可获得邀请奖励！
                  </div>
                </div>
              </Card>
            </motion.div>

            {/* 裁定信息 */}
            {betDetail.status === 'judging' && betDetail.judgmentRounds && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                <Card className="p-6 bg-white shadow-sm border border-gray-200/50">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">⚖️ 裁定进度</h3>
                  
                  <div className="space-y-3">
                    {betDetail.judgmentRounds.map((round) => (
                      <div key={round.round} className={`p-3 rounded-lg ${round.status === 'completed' ? 'bg-green-50' : 'bg-yellow-50'}`}>
                        <div className="flex justify-between items-center mb-2">
                          <span className="font-medium">第{round.round}轮</span>
                          <span className={`text-sm ${round.status === 'completed' ? 'text-green-600' : 'text-yellow-600'}`}>
                            {round.status === 'completed' ? '已完成' : '进行中'}
                          </span>
                        </div>
                        {round.votes.map((vote) => (
                          <div key={vote.option} className="text-sm text-gray-600">
                            {betDetail.options.find(opt => opt.id === vote.option)?.text}: {vote.count} 票
                          </div>
                        ))}
                        <div className="text-xs text-gray-500 mt-1">
                          截止: {new Date(round.deadline).toLocaleString()}
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  <Button
                    onClick={() => router.push(`/social-bet/judgment?betId=${betId}`)}
                    className="w-full mt-4 bg-gradient-to-r from-yellow-500 to-orange-600 text-white hover:from-yellow-600 hover:to-orange-700"
                  >
                    ⚖️ 参与裁定
                  </Button>
                </Card>
              </motion.div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnhancedBetDetail;
