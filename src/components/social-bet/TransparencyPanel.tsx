'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, Badge, Progress } from '@/components/ui';
import CertificationBadge from './CertificationBadge';

interface TransparencyPanelProps {
  betId: string;
  className?: string;
}

interface JudgmentRound {
  round: number;
  status: 'completed' | 'active' | 'pending';
  requiredVotes: number;
  currentVotes: number;
  deadline: string;
  votes: Array<{
    id: string;
    judgeId: string;
    judgeTelegramId: string;
    judgeUsername: string;
    certificationLevel: string;
    reputationScore: number;
    voteOption: number;
    optionText: string;
    votedAt: string;
    isCorrect?: boolean;
  }>;
  result?: {
    winningOption: number;
    winningText: string;
    consensus: number; // 共识度百分比
  };
}

const TransparencyPanel: React.FC<TransparencyPanelProps> = ({ betId, className = '' }) => {
  const [rounds, setRounds] = useState<JudgmentRound[]>([]);
  const [loading, setLoading] = useState(true);
  const [expandedRound, setExpandedRound] = useState<number | null>(null);

  useEffect(() => {
    loadTransparencyData();
  }, [betId]);

  const loadTransparencyData = async () => {
    try {
      setLoading(true);
      
      // 模拟API调用获取透明度数据
      const mockRounds: JudgmentRound[] = [
        {
          round: 1,
          status: 'completed',
          requiredVotes: 11,
          currentVotes: 15,
          deadline: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          votes: [
            {
              id: 'vote-1',
              judgeId: 'judge-1',
              judgeTelegramId: '@judge_master',
              judgeUsername: '预测大师',
              certificationLevel: 'X2',
              reputationScore: 95,
              voteOption: 0,
              optionText: '巴西',
              votedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
              isCorrect: true
            },
            {
              id: 'vote-2',
              judgeId: 'judge-2',
              judgeTelegramId: '@smart_investor',
              judgeUsername: '智慧投资者',
              certificationLevel: 'X1',
              reputationScore: 88,
              voteOption: 0,
              optionText: '巴西',
              votedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000 + 30 * 60 * 1000).toISOString(),
              isCorrect: true
            },
            {
              id: 'vote-3',
              judgeId: 'judge-3',
              judgeTelegramId: '@crypto_expert',
              judgeUsername: '加密专家',
              certificationLevel: 'X3',
              reputationScore: 92,
              voteOption: 1,
              optionText: '阿根廷',
              votedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000 + 60 * 60 * 1000).toISOString(),
              isCorrect: false
            }
          ],
          result: {
            winningOption: 0,
            winningText: '巴西',
            consensus: 73 // 11/15 = 73%
          }
        },
        {
          round: 2,
          status: 'active',
          requiredVotes: 6,
          currentVotes: 4,
          deadline: new Date(Date.now() + 6 * 60 * 60 * 1000).toISOString(),
          votes: [
            {
              id: 'vote-4',
              judgeId: 'judge-4',
              judgeTelegramId: '@pro_analyst',
              judgeUsername: '专业分析师',
              certificationLevel: 'X4',
              reputationScore: 98,
              voteOption: 0,
              optionText: '巴西',
              votedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
            },
            {
              id: 'vote-5',
              judgeId: 'judge-5',
              judgeTelegramId: '@market_guru',
              judgeUsername: '市场大师',
              certificationLevel: 'X3',
              reputationScore: 94,
              voteOption: 0,
              optionText: '巴西',
              votedAt: new Date(Date.now() - 90 * 60 * 1000).toISOString()
            }
          ]
        },
        {
          round: 3,
          status: 'pending',
          requiredVotes: 3,
          currentVotes: 0,
          deadline: new Date(Date.now() + 12 * 60 * 60 * 1000).toISOString(),
          votes: []
        }
      ];

      setRounds(mockRounds);
    } catch (error) {
      console.error('加载透明度数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const getRoundName = (round: number): string => {
    const names = { 1: '大众评审', 2: '专业评审', 3: '终审裁定' };
    return names[round] || `第${round}轮`;
  };

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'active': return 'bg-blue-100 text-blue-800';
      case 'pending': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string): string => {
    switch (status) {
      case 'completed': return '已完成';
      case 'active': return '进行中';
      case 'pending': return '待开始';
      default: return '未知';
    }
  };

  const formatTimeRemaining = (deadline: string): string => {
    const now = new Date();
    const deadlineDate = new Date(deadline);
    const diff = deadlineDate.getTime() - now.getTime();
    
    if (diff <= 0) return '已截止';
    
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    if (days > 0) return `${days}天${hours}小时`;
    if (hours > 0) return `${hours}小时${minutes}分钟`;
    return `${minutes}分钟`;
  };

  if (loading) {
    return (
      <Card className={`p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card className={`p-6 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-bold text-label">🔍 裁定透明度</h3>
        <Badge className="bg-blue-100 text-blue-800">
          全程可追溯
        </Badge>
      </div>

      <div className="space-y-4">
        {rounds.map((round) => (
          <motion.div
            key={round.round}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="border rounded-lg overflow-hidden">
              <div
                className="p-4 bg-gray-50 cursor-pointer hover:bg-gray-100 transition-colors"
                onClick={() => setExpandedRound(expandedRound === round.round ? null : round.round)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <h4 className="font-semibold text-label">
                      {getRoundName(round.round)}
                    </h4>
                    <Badge className={getStatusColor(round.status)}>
                      {getStatusText(round.status)}
                    </Badge>
                    {round.result && (
                      <Badge className="bg-purple-100 text-purple-800">
                        {round.result.winningText} ({round.result.consensus}%共识)
                      </Badge>
                    )}
                  </div>
                  
                  <div className="flex items-center space-x-4 text-sm text-secondary-label">
                    <span>{round.currentVotes}/{round.requiredVotes}票</span>
                    {round.status === 'active' && (
                      <span>⏰ {formatTimeRemaining(round.deadline)}</span>
                    )}
                    <span className="text-lg">
                      {expandedRound === round.round ? '▼' : '▶'}
                    </span>
                  </div>
                </div>

                {/* 进度条 */}
                {round.status !== 'pending' && (
                  <div className="mt-3">
                    <Progress 
                      value={(round.currentVotes / round.requiredVotes) * 100} 
                      className="h-2"
                    />
                  </div>
                )}
              </div>

              <AnimatePresence>
                {expandedRound === round.round && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3 }}
                    className="overflow-hidden"
                  >
                    <div className="p-4 border-t">
                      {round.votes.length > 0 ? (
                        <div className="space-y-3">
                          <h5 className="font-semibold text-label mb-3">投票记录</h5>
                          {round.votes.map((vote) => (
                            <div key={vote.id} className="flex items-center justify-between p-3 bg-white rounded-lg border">
                              <div className="flex items-center space-x-3">
                                <div className="w-8 h-8 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-white text-sm font-bold">
                                  {vote.judgeUsername.charAt(0)}
                                </div>
                                <div>
                                  <div className="flex items-center space-x-2">
                                    <p className="font-medium text-label">{vote.judgeUsername}</p>
                                    <CertificationBadge level={vote.certificationLevel.replace('X', '')} size="small" />
                                    {vote.isCorrect !== undefined && (
                                      <Badge className={vote.isCorrect ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                                        {vote.isCorrect ? '✅' : '❌'}
                                      </Badge>
                                    )}
                                  </div>
                                  <p className="text-xs text-secondary-label">
                                    {vote.judgeTelegramId} • 信誉: {vote.reputationScore}分
                                  </p>
                                </div>
                              </div>
                              
                              <div className="text-right">
                                <p className="font-medium text-label">{vote.optionText}</p>
                                <p className="text-xs text-secondary-label">
                                  {new Date(vote.votedAt).toLocaleString()}
                                </p>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-6 text-secondary-label">
                          {round.status === 'pending' ? '等待开始' : '暂无投票'}
                        </div>
                      )}
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </motion.div>
        ))}
      </div>

      {/* 透明度说明 */}
      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <h4 className="font-semibold text-label mb-2">🔒 透明度保证</h4>
        <ul className="text-sm text-secondary-label space-y-1">
          <li>• 所有裁判的Telegram ID和认证等级公开可查</li>
          <li>• 每一票的投票时间和选择完全透明</li>
          <li>• 裁判的历史准确率和信誉分数公开展示</li>
          <li>• 投票过程实时更新，无法篡改</li>
        </ul>
      </div>
    </Card>
  );
};

export default TransparencyPanel;
