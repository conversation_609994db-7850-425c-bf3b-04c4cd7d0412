'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, Badge, Button, Loading, Progress } from '@/components/ui';
import CertificationBadge from './CertificationBadge';
import { useAuth } from '@/contexts/AuthContext';

interface CertificationInfo {
  userId: string;
  walletAddress: string;
  haoxBalance: number;
  certificationLevel: string;
  benefits: {
    level: string;
    minHaox: number;
    dailyJudgmentQuota: string;
    allowedRounds: number[];
    feeDiscount: number;
    benefits: string[];
  };
  lastUpdated: string;
  needsUpdate: boolean;
  reputation: {
    current_score: number;
    total_judgments: number;
    correct_judgments: number;
    daily_judgments_used: number;
  };
}

interface CertificationBenefitsPanelProps {
  className?: string;
}

const CertificationBenefitsPanel: React.FC<CertificationBenefitsPanelProps> = ({ className = '' }) => {
  const { user, isAuthenticated } = useAuth();
  const [certificationInfo, setCertificationInfo] = useState<CertificationInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);

  useEffect(() => {
    if (isAuthenticated && user?.id) {
      loadCertificationInfo();
    }
  }, [isAuthenticated, user]);

  const loadCertificationInfo = async () => {
    try {
      setLoading(true);
      
      const response = await fetch(`/api/certification/${user?.id}`);
      const result = await response.json();

      if (result.success) {
        setCertificationInfo(result.data);
      } else {
        console.error('获取认证信息失败:', result.message);
      }
    } catch (error) {
      console.error('加载认证信息失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateCertification = async () => {
    try {
      setUpdating(true);
      
      const response = await fetch(`/api/certification/${user?.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({})
      });

      const result = await response.json();

      if (result.success) {
        setCertificationInfo(prev => prev ? {
          ...prev,
          haoxBalance: result.data.haoxBalance,
          certificationLevel: result.data.newLevel,
          benefits: result.data.benefits,
          lastUpdated: result.data.lastUpdated,
          needsUpdate: false
        } : null);
        
        alert(`认证等级更新成功！当前等级：${result.data.newLevel}`);
      } else {
        alert(`更新失败：${result.message}`);
      }
    } catch (error) {
      console.error('更新认证等级失败:', error);
      alert('更新失败，请稍后重试');
    } finally {
      setUpdating(false);
    }
  };

  const formatHaoxBalance = (balance: number): string => {
    if (balance >= 1000000) {
      return `${(balance / 1000000).toFixed(1)}M`;
    } else if (balance >= 1000) {
      return `${(balance / 1000).toFixed(1)}K`;
    }
    return balance.toString();
  };

  const getNextLevelInfo = (currentLevel: string, currentBalance: number) => {
    const levels = [
      { level: 'X1', minHaox: 10 },
      { level: 'X2', minHaox: 10000 },
      { level: 'X3', minHaox: 1000000 },
      { level: 'X4', minHaox: 10000000 },
      { level: 'X5', minHaox: 100000000 }
    ];

    const currentIndex = levels.findIndex(l => l.level === currentLevel);
    if (currentIndex === -1 || currentIndex === levels.length - 1) {
      return null; // 已经是最高等级
    }

    const nextLevel = levels[currentIndex + 1];
    const needed = nextLevel.minHaox - currentBalance;
    const progress = (currentBalance / nextLevel.minHaox) * 100;

    return {
      level: nextLevel.level,
      needed: needed > 0 ? needed : 0,
      progress: Math.min(progress, 100)
    };
  };

  if (!isAuthenticated) {
    return (
      <Card className={`p-6 ${className}`}>
        <div className="text-center">
          <div className="text-4xl mb-4">🔒</div>
          <h3 className="text-lg font-semibold text-label mb-2">需要登录</h3>
          <p className="text-secondary-label mb-4">
            查看认证等级信息需要登录账户
          </p>
          <Button href="/auth/login" size="small">
            立即登录
          </Button>
        </div>
      </Card>
    );
  }

  if (loading) {
    return (
      <Card className={`p-6 ${className}`}>
        <div className="flex justify-center items-center py-8">
          <Loading size="large" />
        </div>
      </Card>
    );
  }

  if (!certificationInfo) {
    return (
      <Card className={`p-6 ${className}`}>
        <div className="text-center">
          <div className="text-4xl mb-4">❌</div>
          <h3 className="text-lg font-semibold text-label mb-2">认证信息不存在</h3>
          <p className="text-secondary-label mb-4">
            请先绑定钱包地址以获取认证等级
          </p>
          <Button onClick={updateCertification} size="small">
            绑定钱包
          </Button>
        </div>
      </Card>
    );
  }

  const nextLevelInfo = getNextLevelInfo(certificationInfo.certificationLevel, certificationInfo.haoxBalance);

  return (
    <Card className={`p-6 ${className}`}>
      {/* 标题和更新按钮 */}
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-xl font-bold text-label">🏆 认证等级</h3>
        <div className="flex items-center space-x-2">
          {certificationInfo.needsUpdate && (
            <Badge className="bg-yellow-100 text-yellow-800">
              需要更新
            </Badge>
          )}
          <Button
            onClick={updateCertification}
            disabled={updating}
            size="small"
            variant="outline"
          >
            {updating ? '更新中...' : '🔄 更新等级'}
          </Button>
        </div>
      </div>

      {/* 当前等级信息 */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <CertificationBadge level={certificationInfo.certificationLevel.replace('X', '')} size="large" />
            <div>
              <h4 className="text-lg font-semibold text-label">
                {certificationInfo.certificationLevel} 认证
              </h4>
              <p className="text-sm text-secondary-label">
                HAOX余额: {formatHaoxBalance(certificationInfo.haoxBalance)}
              </p>
            </div>
          </div>
          
          <div className="text-right">
            <p className="text-sm text-secondary-label">手续费折扣</p>
            <p className="text-lg font-bold text-green-600">
              {certificationInfo.benefits.feeDiscount}%
            </p>
          </div>
        </div>

        {/* 升级进度 */}
        {nextLevelInfo && (
          <div className="mb-4">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm text-secondary-label">
                升级到 {nextLevelInfo.level}
              </span>
              <span className="text-sm font-medium text-label">
                还需 {formatHaoxBalance(nextLevelInfo.needed)} HAOX
              </span>
            </div>
            <Progress value={nextLevelInfo.progress} className="h-2" />
          </div>
        )}
      </div>

      {/* 等级权益 */}
      <div className="mb-6">
        <h4 className="font-semibold text-label mb-3">🎁 等级权益</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div className="p-3 bg-blue-50 rounded-lg">
            <p className="text-sm text-secondary-label">每日裁定次数</p>
            <p className="font-semibold text-label">
              {certificationInfo.benefits.dailyJudgmentQuota}
            </p>
          </div>
          <div className="p-3 bg-green-50 rounded-lg">
            <p className="text-sm text-secondary-label">可参与轮次</p>
            <p className="font-semibold text-label">
              第{certificationInfo.benefits.allowedRounds.join('、')}轮
            </p>
          </div>
        </div>

        <div className="space-y-2">
          {certificationInfo.benefits.benefits.map((benefit, index) => (
            <div key={index} className="flex items-center space-x-2">
              <span className="text-green-500">✓</span>
              <span className="text-sm text-label">{benefit}</span>
            </div>
          ))}
        </div>
      </div>

      {/* 信誉统计 */}
      {certificationInfo.reputation && (
        <div className="border-t pt-4">
          <h4 className="font-semibold text-label mb-3">📊 信誉统计</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-lg font-bold text-label">
                {certificationInfo.reputation.current_score}
              </p>
              <p className="text-xs text-secondary-label">信誉分数</p>
            </div>
            <div className="text-center">
              <p className="text-lg font-bold text-label">
                {certificationInfo.reputation.total_judgments}
              </p>
              <p className="text-xs text-secondary-label">总裁定次数</p>
            </div>
            <div className="text-center">
              <p className="text-lg font-bold text-green-600">
                {certificationInfo.reputation.total_judgments > 0 
                  ? Math.round((certificationInfo.reputation.correct_judgments / certificationInfo.reputation.total_judgments) * 100)
                  : 0}%
              </p>
              <p className="text-xs text-secondary-label">准确率</p>
            </div>
            <div className="text-center">
              <p className="text-lg font-bold text-blue-600">
                {certificationInfo.reputation.daily_judgments_used}
              </p>
              <p className="text-xs text-secondary-label">今日已用</p>
            </div>
          </div>
        </div>
      )}

      {/* 更新时间 */}
      <div className="mt-4 pt-4 border-t">
        <p className="text-xs text-secondary-label">
          最后更新: {new Date(certificationInfo.lastUpdated).toLocaleString()}
        </p>
      </div>
    </Card>
  );
};

export default CertificationBenefitsPanel;
