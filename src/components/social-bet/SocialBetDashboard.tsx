'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button, Card, Icon, Loading } from '@/components/ui';
import { useAuth } from '@/contexts/AuthContext';

// 赌约状态类型
type BetStatus = 'open' | 'betting_closed' | 'judging' | 'confirming' | 'settled' | 'cancelled' | 'expired';

// 赌约接口
interface SocialBet {
  id: string;
  title: string;
  description: string;
  category: string;
  betType: '1v1' | '1vN';
  status: BetStatus;
  totalPool: number;
  participantCount: number;
  minBetAmount: number;
  maxBetAmount: number;
  bettingDeadline: string;
  resultDeadline: string;
  createdAt: string;
  creatorId: string;
  options: Array<{
    id: string;
    text: string;
    participants: number;
    totalAmount: number;
  }>;
}

// 用户统计接口
interface UserStats {
  totalBets: number;
  totalWins: number;
  totalEarnings: number;
  winRate: number;
  currentStreak: number;
  certificationLevel: number;
  reputationScore: number;
}

const SocialBetDashboard: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const [activeBets, setActiveBets] = useState<SocialBet[]>([]);
  const [userStats, setUserStats] = useState<UserStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'all' | 'my-bets' | 'judging'>('all');

  useEffect(() => {
    loadDashboardData();
  }, [isAuthenticated]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      // 加载活跃赌约
      const betsResponse = await fetch('/api/social-bet/bets');
      if (betsResponse.ok) {
        const betsData = await betsResponse.json();
        setActiveBets(betsData.data || []);
      }

      // 如果用户已登录，加载用户统计
      if (isAuthenticated && user) {
        const statsResponse = await fetch(`/api/social-bet/user-stats?userId=${user.id}`);
        if (statsResponse.ok) {
          const statsData = await statsResponse.json();
          setUserStats(statsData.data);
        }
      }
    } catch (error) {
      console.error('加载仪表板数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: BetStatus): string => {
    switch (status) {
      case 'open': return 'text-green-500';
      case 'betting_closed': return 'text-yellow-500';
      case 'judging': return 'text-blue-500';
      case 'confirming': return 'text-purple-500';
      case 'settled': return 'text-gray-500';
      case 'cancelled': return 'text-red-500';
      case 'expired': return 'text-gray-400';
      default: return 'text-gray-500';
    }
  };

  const getStatusText = (status: BetStatus): string => {
    switch (status) {
      case 'open': return '开放投注';
      case 'betting_closed': return '投注截止';
      case 'judging': return '裁定中';
      case 'confirming': return '确认中';
      case 'settled': return '已结算';
      case 'cancelled': return '已取消';
      case 'expired': return '已过期';
      default: return '未知状态';
    }
  };

  const formatTimeRemaining = (deadline: string): string => {
    const now = new Date();
    const deadlineDate = new Date(deadline);
    const diff = deadlineDate.getTime() - now.getTime();
    
    if (diff <= 0) return '已截止';
    
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    if (days > 0) return `${days}天${hours}小时`;
    if (hours > 0) return `${hours}小时${minutes}分钟`;
    return `${minutes}分钟`;
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <Loading size="large" />
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-label mb-2">
          🎲 社交赌注
        </h1>
        <p className="text-secondary-label">
          基于福气的去中心化社交赌约平台，公平透明，智能裁定
        </p>
      </div>

      {/* 用户统计卡片 */}
      {isAuthenticated && userStats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-secondary-label">总投注次数</p>
                <p className="text-2xl font-bold text-label">{userStats.totalBets}</p>
              </div>
              <div className="text-2xl">📊</div>
            </div>
          </Card>
          
          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-secondary-label">胜率</p>
                <p className="text-2xl font-bold text-label">{userStats.winRate.toFixed(1)}%</p>
              </div>
              <div className="text-2xl">🏆</div>
            </div>
          </Card>
          
          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-secondary-label">总收益</p>
                <p className="text-2xl font-bold text-label">{userStats.totalEarnings}福气</p>
              </div>
              <div className="text-2xl">💰</div>
            </div>
          </Card>
          
          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-secondary-label">认证等级</p>
                <p className="text-2xl font-bold text-label">Lv.{userStats.certificationLevel}</p>
              </div>
              <div className="text-2xl">🏅</div>
            </div>
          </Card>
        </div>
      )}

      {/* 操作按钮 */}
      <div className="flex flex-wrap gap-4 mb-8">
        <Button 
          href="/social-bet/create"
          className="bg-gradient-to-r from-blue-500 to-purple-600 text-white"
        >
          🎯 创建赌约
        </Button>
        
        {isAuthenticated && (
          <>
            <Button 
              href="/social-bet/my-bets"
              variant="outline"
            >
              📋 我的赌约
            </Button>
            
            <Button 
              href="/social-bet/judgment"
              variant="outline"
            >
              ⚖️ 参与裁定
            </Button>
          </>
        )}
        
        <Button 
          href="/social-bet/leaderboard"
          variant="outline"
        >
          🏆 排行榜
        </Button>
      </div>

      {/* 标签页 */}
      <div className="flex space-x-1 mb-6 bg-system-gray-6 rounded-lg p-1">
        {[
          { key: 'all', label: '全部赌约', icon: '🎲' },
          { key: 'my-bets', label: '我的参与', icon: '👤' },
          { key: 'judging', label: '待裁定', icon: '⚖️' }
        ].map((tab) => (
          <button
            key={tab.key}
            onClick={() => setActiveTab(tab.key as any)}
            className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md transition-colors ${
              activeTab === tab.key
                ? 'bg-white text-label shadow-sm'
                : 'text-secondary-label hover:text-label'
            }`}
          >
            <span>{tab.icon}</span>
            <span className="font-medium">{tab.label}</span>
          </button>
        ))}
      </div>

      {/* 赌约列表 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {activeBets.map((bet) => (
          <motion.div
            key={bet.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Card className="p-6 hover:shadow-lg transition-shadow cursor-pointer">
              <div className="flex justify-between items-start mb-4">
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-label mb-2">
                    {bet.title}
                  </h3>
                  <p className="text-sm text-secondary-label line-clamp-2">
                    {bet.description}
                  </p>
                </div>
                <div className="ml-4">
                  <span className={`text-sm font-medium ${getStatusColor(bet.status)}`}>
                    {getStatusText(bet.status)}
                  </span>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <p className="text-xs text-secondary-label">奖池总额</p>
                  <p className="text-lg font-bold text-label">{bet.totalPool}福气</p>
                </div>
                <div>
                  <p className="text-xs text-secondary-label">参与人数</p>
                  <p className="text-lg font-bold text-label">{bet.participantCount}人</p>
                </div>
              </div>

              <div className="mb-4">
                <p className="text-xs text-secondary-label mb-2">投注选项</p>
                <div className="space-y-2">
                  {bet.options.map((option) => (
                    <div key={option.id} className="flex justify-between items-center">
                      <span className="text-sm text-label">{option.text}</span>
                      <div className="text-right">
                        <span className="text-sm font-medium text-label">
                          {option.totalAmount}福气
                        </span>
                        <span className="text-xs text-secondary-label ml-2">
                          ({option.participants}人)
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {bet.status === 'open' && (
                <div className="mb-4">
                  <p className="text-xs text-secondary-label">
                    投注截止: {formatTimeRemaining(bet.bettingDeadline)}
                  </p>
                </div>
              )}

              <div className="flex justify-between items-center">
                <div className="flex items-center space-x-2">
                  <span className="text-xs bg-system-gray-5 text-secondary-label px-2 py-1 rounded">
                    {bet.category}
                  </span>
                  <span className="text-xs bg-system-gray-5 text-secondary-label px-2 py-1 rounded">
                    {bet.betType === '1v1' ? '1对1' : '1对多'}
                  </span>
                </div>
                
                <Button
                  href={`/social-bet/${bet.id}`}
                  size="small"
                  variant="outline"
                >
                  查看详情
                </Button>
              </div>
            </Card>
          </motion.div>
        ))}
      </div>

      {activeBets.length === 0 && (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">🎲</div>
          <h3 className="text-xl font-semibold text-label mb-2">暂无活跃赌约</h3>
          <p className="text-secondary-label mb-6">
            成为第一个创建赌约的用户，开启社交赌注之旅！
          </p>
          <Button 
            href="/social-bet/create"
            className="bg-gradient-to-r from-blue-500 to-purple-600 text-white"
          >
            🎯 创建第一个赌约
          </Button>
        </div>
      )}
    </div>
  );
};

export default SocialBetDashboard;
