'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, Loading, Breadcrumb, Badge } from '@/components/ui';
import CertificationBadge from './CertificationBadge';

interface LeaderboardUser {
  id: string;
  username: string;
  avatar?: string;
  totalWinnings: number;
  winRate: number;
  totalBets: number;
  certificationLevel: number;
  rank: number;
}

const SocialBetLeaderboard: React.FC = () => {
  const [leaderboard, setLeaderboard] = useState<LeaderboardUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'winnings' | 'winrate' | 'volume'>('winnings');

  useEffect(() => {
    loadLeaderboard();
  }, [activeTab]);

  const loadLeaderboard = async () => {
    try {
      setLoading(true);
      
      // 模拟数据
      const mockUsers: LeaderboardUser[] = [
        {
          id: '1',
          username: '福气大师',
          totalWinnings: 15680,
          winRate: 78.5,
          totalBets: 156,
          certificationLevel: 5,
          rank: 1
        },
        {
          id: '2',
          username: '预测之王',
          totalWinnings: 12450,
          winRate: 72.3,
          totalBets: 203,
          certificationLevel: 4,
          rank: 2
        },
        {
          id: '3',
          username: '智慧投资者',
          totalWinnings: 9870,
          winRate: 69.8,
          totalBets: 142,
          certificationLevel: 4,
          rank: 3
        },
        {
          id: '4',
          username: '幸运星',
          totalWinnings: 8520,
          winRate: 65.4,
          totalBets: 189,
          certificationLevel: 3,
          rank: 4
        },
        {
          id: '5',
          username: '稳健玩家',
          totalWinnings: 7890,
          winRate: 71.2,
          totalBets: 98,
          certificationLevel: 3,
          rank: 5
        }
      ];

      // 根据选择的标签排序
      const sortedUsers = [...mockUsers].sort((a, b) => {
        switch (activeTab) {
          case 'winnings':
            return b.totalWinnings - a.totalWinnings;
          case 'winrate':
            return b.winRate - a.winRate;
          case 'volume':
            return b.totalBets - a.totalBets;
          default:
            return 0;
        }
      }).map((user, index) => ({ ...user, rank: index + 1 }));

      setLeaderboard(sortedUsers);
    } catch (error) {
      console.error('加载排行榜失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const getRankIcon = (rank: number): string => {
    switch (rank) {
      case 1: return '🥇';
      case 2: return '🥈';
      case 3: return '🥉';
      default: return `#${rank}`;
    }
  };

  const getRankColor = (rank: number): string => {
    switch (rank) {
      case 1: return 'text-yellow-600';
      case 2: return 'text-gray-500';
      case 3: return 'text-orange-600';
      default: return 'text-label';
    }
  };

  const getTabLabel = (tab: string): string => {
    switch (tab) {
      case 'winnings': return '总收益';
      case 'winrate': return '胜率';
      case 'volume': return '参与量';
      default: return '';
    }
  };

  const getTabValue = (user: LeaderboardUser, tab: string): string => {
    switch (tab) {
      case 'winnings': return `${user.totalWinnings}福气`;
      case 'winrate': return `${user.winRate}%`;
      case 'volume': return `${user.totalBets}次`;
      default: return '';
    }
  };

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-center items-center min-h-96">
          <Loading size="large" />
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* 面包屑导航 */}
      <div className="mb-6">
        <Breadcrumb
          items={[
            { label: '首页', href: '/', icon: '🏠' },
            { label: '社交赌注', href: '/social-bet', icon: '🎲' },
            { label: '排行榜', icon: '🏆' }
          ]}
        />
      </div>

      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-label mb-2">
          🏆 社交赌注排行榜
        </h1>
        <p className="text-secondary-label">
          查看社区中最优秀的预测者和投资者
        </p>
      </div>

      {/* 标签页 */}
      <div className="flex space-x-1 mb-6 bg-system-gray-6 rounded-lg p-1">
        {[
          { key: 'winnings', label: '总收益榜', icon: '💰' },
          { key: 'winrate', label: '胜率榜', icon: '🎯' },
          { key: 'volume', label: '活跃榜', icon: '📊' }
        ].map((tab) => (
          <button
            key={tab.key}
            onClick={() => setActiveTab(tab.key as any)}
            className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md transition-all duration-200 ${
              activeTab === tab.key
                ? 'border-2 border-blue-500 text-blue-600 bg-blue-50/50 font-semibold'
                : 'text-secondary-label hover:text-label hover:bg-white/20 border-2 border-transparent'
            }`}
          >
            <span>{tab.icon}</span>
            <span className="font-medium">{tab.label}</span>
          </button>
        ))}
      </div>

      {/* 前三名展示 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
        {leaderboard.slice(0, 3).map((user, index) => (
          <motion.div
            key={user.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
          >
            <Card className={`p-6 text-center ${index === 0 ? 'ring-2 ring-yellow-400' : ''}`}>
              <div className="text-4xl mb-2">
                {getRankIcon(user.rank)}
              </div>
              <h3 className="font-bold text-label mb-2">{user.username}</h3>
              <CertificationBadge level={user.certificationLevel} size="small" />
              <div className="mt-4">
                <p className="text-sm text-secondary-label">{getTabLabel(activeTab)}</p>
                <p className={`text-lg font-bold ${getRankColor(user.rank)}`}>
                  {getTabValue(user, activeTab)}
                </p>
              </div>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* 完整排行榜 */}
      <Card className="overflow-hidden">
        <div className="p-6 border-b">
          <h2 className="text-xl font-bold text-label">
            完整排行榜 - {getTabLabel(activeTab)}
          </h2>
        </div>
        
        <div className="divide-y">
          {leaderboard.map((user, index) => (
            <motion.div
              key={user.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: index * 0.05 }}
              className="p-4 hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className={`text-2xl font-bold ${getRankColor(user.rank)} min-w-[3rem]`}>
                    {getRankIcon(user.rank)}
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-white font-bold">
                      {user.username.charAt(0)}
                    </div>
                    <div>
                      <h3 className="font-semibold text-label">{user.username}</h3>
                      <div className="flex items-center space-x-2">
                        <CertificationBadge level={user.certificationLevel} size="small" />
                        <span className="text-xs text-secondary-label">
                          {user.totalBets}次参与
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="text-right">
                  <p className="text-lg font-bold text-label">
                    {getTabValue(user, activeTab)}
                  </p>
                  <div className="flex items-center space-x-4 text-sm text-secondary-label">
                    <span>胜率 {user.winRate}%</span>
                    <span>收益 {user.totalWinnings}福气</span>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </Card>

      {/* 说明信息 */}
      <Card className="p-6 mt-8 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 shadow-sm">
        <div className="flex items-start space-x-3">
          <div className="text-2xl">📊</div>
          <div className="flex-1">
            <h3 className="text-lg font-bold text-gray-800 mb-3">排行榜说明</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-yellow-500 rounded-full"></span>
                  <span className="text-sm font-medium text-gray-700">总收益榜：按累计福气收益排序</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                  <span className="text-sm font-medium text-gray-700">胜率榜：按预测准确率排序（最少10次参与）</span>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                  <span className="text-sm font-medium text-gray-700">活跃榜：按参与赌约次数排序</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-purple-500 rounded-full"></span>
                  <span className="text-sm font-medium text-gray-700">认证等级越高，可参与的高级裁定越多</span>
                </div>
              </div>
            </div>
            <div className="mt-4 p-3 bg-white/60 rounded-lg border border-blue-100">
              <p className="text-xs text-gray-600 leading-relaxed">
                💡 <strong>提示：</strong>排行榜每小时更新一次，数据基于用户在社交赌约中的真实表现。
                高等级认证用户享有更多特权，包括参与高价值赌约裁定和获得额外奖励。
              </p>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default SocialBetLeaderboard;
