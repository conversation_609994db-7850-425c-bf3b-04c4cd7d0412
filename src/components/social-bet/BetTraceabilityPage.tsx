'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, Loading, B<PERSON><PERSON>rum<PERSON>, <PERSON><PERSON>, Button } from '@/components/ui';

interface BetTraceabilityPageProps {
  betId: string;
}

interface TraceabilityData {
  lifecycle: {
    betId: string;
    betTitle: string;
    createdAt: string;
    currentStatus: string;
    operations: Array<{
      id: string;
      typeText: string;
      typeIcon: string;
      typeColor: string;
      operatorUsername: string;
      operatorTelegramId: string;
      description: string;
      timestamp: string;
      formattedTime: string;
      relativeTime: string;
      metadata: Record<string, any>;
      txHash?: string;
    }>;
    fortuneFlows: Array<{
      id: string;
      typeText: string;
      typeIcon: string;
      typeColor: string;
      fromUserId?: string;
      toUserId?: string;
      amount: number;
      formattedAmount: string;
      description: string;
      timestamp: string;
      formattedTime: string;
      relativeTime: string;
      metadata: Record<string, any>;
      txHash?: string;
    }>;
    participants: Array<{
      userId: string;
      username: string;
      telegramId: string;
      role: string;
      joinedAt: string;
    }>;
    timeline: Array<{
      timestamp: string;
      type: 'operation' | 'fortune_flow';
      data: any;
      formattedTime: string;
      relativeTime: string;
    }>;
  };
  stats: {
    totalInflow: number;
    totalOutflow: number;
    totalFees: number;
    totalRewards: number;
    flowCount: number;
    operationCount: number;
    participantCount: number;
    timelineEvents: number;
  };
}

const BetTraceabilityPage: React.FC<BetTraceabilityPageProps> = ({ betId }) => {
  const [data, setData] = useState<TraceabilityData | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'timeline' | 'operations' | 'fortune' | 'participants'>('timeline');

  useEffect(() => {
    loadTraceabilityData();
  }, [betId]);

  const loadTraceabilityData = async () => {
    try {
      setLoading(true);
      
      const response = await fetch(`/api/social-bet/${betId}/traceability`);
      const result = await response.json();

      if (result.success) {
        setData(result.data);
      } else {
        console.error('获取可追溯性数据失败:', result.message);
      }
    } catch (error) {
      console.error('加载可追溯性数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-center items-center min-h-96">
          <Loading size="large" />
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center py-12">
          <div className="text-6xl mb-4">❌</div>
          <h3 className="text-xl font-semibold text-label mb-2">数据加载失败</h3>
          <p className="text-secondary-label mb-6">
            无法获取赌约的可追溯性数据，请稍后重试。
          </p>
          <Button onClick={loadTraceabilityData}>
            重新加载
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* 面包屑导航 */}
      <div className="mb-6">
        <Breadcrumb
          items={[
            { label: '首页', href: '/', icon: '🏠' },
            { label: '社交赌注', href: '/social-bet', icon: '🎲' },
            { label: '赌约详情', href: `/social-bet/${betId}`, icon: '🎯' },
            { label: '可追溯性', icon: '🔍' }
          ]}
        />
      </div>

      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-label mb-2">
          🔍 赌约可追溯性
        </h1>
        <p className="text-secondary-label">
          {data.lifecycle.betTitle} - 完整生命周期追踪
        </p>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-blue-600 mb-1">
            {data.stats.timelineEvents}
          </div>
          <div className="text-sm text-secondary-label">时间线事件</div>
        </Card>
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-green-600 mb-1">
            {data.stats.operationCount}
          </div>
          <div className="text-sm text-secondary-label">操作记录</div>
        </Card>
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-purple-600 mb-1">
            {data.stats.flowCount}
          </div>
          <div className="text-sm text-secondary-label">福气流转</div>
        </Card>
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-orange-600 mb-1">
            {data.stats.participantCount}
          </div>
          <div className="text-sm text-secondary-label">参与人数</div>
        </Card>
      </div>

      {/* 标签页 */}
      <div className="flex space-x-1 mb-6 bg-system-gray-6 rounded-lg p-1">
        {[
          { key: 'timeline', label: '时间线', icon: '📅' },
          { key: 'operations', label: '操作记录', icon: '📝' },
          { key: 'fortune', label: '福气流转', icon: '💰' },
          { key: 'participants', label: '参与者', icon: '👥' }
        ].map((tab) => (
          <button
            key={tab.key}
            onClick={() => setActiveTab(tab.key as any)}
            className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md transition-all duration-200 ${
              activeTab === tab.key
                ? 'border-2 border-blue-500 text-blue-600 bg-blue-50/50 font-semibold'
                : 'text-secondary-label hover:text-label hover:bg-white/20 border-2 border-transparent'
            }`}
          >
            <span>{tab.icon}</span>
            <span className="font-medium">{tab.label}</span>
          </button>
        ))}
      </div>

      {/* 时间线标签页 */}
      {activeTab === 'timeline' && (
        <Card className="p-6">
          <h2 className="text-xl font-bold text-label mb-6">📅 完整时间线</h2>
          
          <div className="space-y-4">
            {data.lifecycle.timeline.map((event, index) => (
              <motion.div
                key={`${event.type}-${event.data.id}`}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg"
              >
                <div className="flex-shrink-0">
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center text-lg ${
                    event.type === 'operation' ? 'bg-blue-100' : 'bg-green-100'
                  }`}>
                    {event.type === 'operation' ? event.data.typeIcon : event.data.typeIcon}
                  </div>
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-1">
                    <Badge className={event.type === 'operation' ? event.data.typeColor : event.data.typeColor}>
                      {event.type === 'operation' ? event.data.typeText : event.data.typeText}
                    </Badge>
                    <span className="text-sm text-secondary-label">
                      {event.relativeTime}
                    </span>
                  </div>
                  
                  <p className="text-sm text-label mb-1">
                    {event.data.description}
                  </p>
                  
                  {event.type === 'operation' && (
                    <p className="text-xs text-secondary-label">
                      操作者: {event.data.operatorUsername} ({event.data.operatorTelegramId})
                    </p>
                  )}
                  
                  {event.type === 'fortune_flow' && (
                    <p className="text-xs text-secondary-label">
                      金额: {event.data.formattedAmount} 福气
                    </p>
                  )}
                  
                  <p className="text-xs text-secondary-label">
                    {event.formattedTime}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        </Card>
      )}

      {/* 操作记录标签页 */}
      {activeTab === 'operations' && (
        <Card className="p-6">
          <h2 className="text-xl font-bold text-label mb-6">📝 操作记录</h2>
          
          <div className="space-y-4">
            {data.lifecycle.operations.map((operation, index) => (
              <motion.div
                key={operation.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="border rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <span className="text-2xl">{operation.typeIcon}</span>
                    <div>
                      <Badge className={operation.typeColor}>
                        {operation.typeText}
                      </Badge>
                      <p className="text-sm text-secondary-label mt-1">
                        {operation.relativeTime}
                      </p>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <p className="text-sm font-medium text-label">
                      {operation.operatorUsername}
                    </p>
                    <p className="text-xs text-secondary-label">
                      {operation.operatorTelegramId}
                    </p>
                  </div>
                </div>
                
                <p className="text-sm text-label mb-2">
                  {operation.description}
                </p>
                
                <div className="flex justify-between items-center text-xs text-secondary-label">
                  <span>{operation.formattedTime}</span>
                  {operation.txHash && (
                    <span className="font-mono">
                      TX: {operation.txHash.slice(0, 10)}...
                    </span>
                  )}
                </div>
              </motion.div>
            ))}
          </div>
        </Card>
      )}

      {/* 福气流转标签页 */}
      {activeTab === 'fortune' && (
        <Card className="p-6">
          <h2 className="text-xl font-bold text-label mb-6">💰 福气流转记录</h2>
          
          {/* 福气统计 */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="p-3 bg-green-50 rounded-lg text-center">
              <p className="text-sm text-secondary-label">总流入</p>
              <p className="text-lg font-bold text-green-600">
                {data.stats.totalInflow.toLocaleString()}
              </p>
            </div>
            <div className="p-3 bg-red-50 rounded-lg text-center">
              <p className="text-sm text-secondary-label">总流出</p>
              <p className="text-lg font-bold text-red-600">
                {data.stats.totalOutflow.toLocaleString()}
              </p>
            </div>
            <div className="p-3 bg-orange-50 rounded-lg text-center">
              <p className="text-sm text-secondary-label">总手续费</p>
              <p className="text-lg font-bold text-orange-600">
                {data.stats.totalFees.toLocaleString()}
              </p>
            </div>
            <div className="p-3 bg-purple-50 rounded-lg text-center">
              <p className="text-sm text-secondary-label">总奖励</p>
              <p className="text-lg font-bold text-purple-600">
                {data.stats.totalRewards.toLocaleString()}
              </p>
            </div>
          </div>
          
          <div className="space-y-4">
            {data.lifecycle.fortuneFlows.map((flow, index) => (
              <motion.div
                key={flow.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="border rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <span className="text-2xl">{flow.typeIcon}</span>
                    <div>
                      <Badge className={flow.typeColor}>
                        {flow.typeText}
                      </Badge>
                      <p className="text-sm text-secondary-label mt-1">
                        {flow.relativeTime}
                      </p>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <p className="text-lg font-bold text-label">
                      {flow.formattedAmount} 福气
                    </p>
                    <p className="text-xs text-secondary-label">
                      {flow.amount.toLocaleString()}
                    </p>
                  </div>
                </div>
                
                <p className="text-sm text-label mb-2">
                  {flow.description}
                </p>
                
                <div className="flex justify-between items-center text-xs text-secondary-label">
                  <span>{flow.formattedTime}</span>
                  {flow.txHash && (
                    <span className="font-mono">
                      TX: {flow.txHash.slice(0, 10)}...
                    </span>
                  )}
                </div>
              </motion.div>
            ))}
          </div>
        </Card>
      )}

      {/* 参与者标签页 */}
      {activeTab === 'participants' && (
        <Card className="p-6">
          <h2 className="text-xl font-bold text-label mb-6">👥 参与者信息</h2>
          
          <div className="space-y-4">
            {data.lifecycle.participants.map((participant, index) => (
              <motion.div
                key={participant.userId}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="flex items-center justify-between p-4 border rounded-lg hover:shadow-md transition-shadow"
              >
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-white font-bold">
                    {participant.username.charAt(0)}
                  </div>
                  <div>
                    <p className="font-semibold text-label">{participant.username}</p>
                    <p className="text-sm text-secondary-label">{participant.telegramId}</p>
                  </div>
                </div>
                
                <div className="text-right">
                  <Badge className={
                    participant.role === 'creator' ? 'bg-blue-100 text-blue-800' :
                    participant.role === 'judge' ? 'bg-purple-100 text-purple-800' :
                    'bg-green-100 text-green-800'
                  }>
                    {participant.role === 'creator' ? '创建者' :
                     participant.role === 'judge' ? '裁判' : '参与者'}
                  </Badge>
                  <p className="text-xs text-secondary-label mt-1">
                    {new Date(participant.joinedAt).toLocaleString()}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        </Card>
      )}
    </div>
  );
};

export default BetTraceabilityPage;
