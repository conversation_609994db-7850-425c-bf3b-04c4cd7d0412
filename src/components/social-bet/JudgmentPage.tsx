'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON>, Card, Loading, Breadcrumb, Badge } from '@/components/ui';
import { useAuth } from '@/contexts/AuthContext';
import CertificationBadge from './CertificationBadge';

interface JudgmentBet {
  id: string;
  title: string;
  description: string;
  round: number;
  deadline: string;
  options: Array<{
    id: string;
    text: string;
    votes: number;
  }>;
  requiredLevel: number;
  reward: number;
  totalVotes: number;
}

const JudgmentPage: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const [judgmentBets, setJudgmentBets] = useState<JudgmentBet[]>([]);
  const [loading, setLoading] = useState(true);
  const [userLevel, setUserLevel] = useState(2); // 模拟用户认证等级

  useEffect(() => {
    loadJudgmentBets();
  }, [isAuthenticated]);

  const loadJudgmentBets = async () => {
    try {
      setLoading(true);
      
      // 模拟数据
      const mockBets: JudgmentBet[] = [
        {
          id: 'judgment-1',
          title: '2024年世界杯冠军预测',
          description: '根据比赛结果，判断哪个队伍获得了冠军',
          round: 1,
          deadline: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),
          options: [
            { id: 'A', text: '巴西', votes: 15 },
            { id: 'B', text: '阿根廷', votes: 25 },
            { id: 'C', text: '法国', votes: 8 }
          ],
          requiredLevel: 0,
          reward: 50,
          totalVotes: 48
        },
        {
          id: 'judgment-2',
          title: 'Bitcoin价格预测',
          description: '根据市场数据，判断比特币价格走势预测的结果',
          round: 2,
          deadline: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000).toISOString(),
          options: [
            { id: 'A', text: '上涨', votes: 12 },
            { id: 'B', text: '下跌', votes: 8 }
          ],
          requiredLevel: 1,
          reward: 80,
          totalVotes: 20
        },
        {
          id: 'judgment-3',
          title: '苹果股价预测',
          description: '根据财报和市场表现，判断苹果股价预测结果',
          round: 3,
          deadline: new Date(Date.now() + 6 * 60 * 60 * 1000).toISOString(),
          options: [
            { id: 'A', text: '上涨', votes: 5 },
            { id: 'B', text: '下跌', votes: 3 }
          ],
          requiredLevel: 2,
          reward: 120,
          totalVotes: 8
        }
      ];

      setJudgmentBets(mockBets);
    } catch (error) {
      console.error('加载裁定赌约失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatTimeRemaining = (deadline: string): string => {
    const now = new Date();
    const deadlineDate = new Date(deadline);
    const diff = deadlineDate.getTime() - now.getTime();
    
    if (diff <= 0) return '已截止';
    
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    
    if (days > 0) return `${days}天${hours}小时`;
    return `${hours}小时`;
  };

  const canParticipate = (requiredLevel: number): boolean => {
    return userLevel >= requiredLevel;
  };

  const getConsensusPercentage = (option: any, totalVotes: number): number => {
    return totalVotes > 0 ? (option.votes / totalVotes) * 100 : 0;
  };

  if (!isAuthenticated) {
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Breadcrumb
          items={[
            { label: '首页', href: '/', icon: '🏠' },
            { label: '社交赌注', href: '/social-bet', icon: '🎲' },
            { label: '参与裁定', icon: '⚖️' }
          ]}
        />
        
        <div className="text-center py-12">
          <div className="text-6xl mb-4">🔒</div>
          <h2 className="text-2xl font-bold text-label mb-4">需要登录</h2>
          <p className="text-secondary-label mb-6">
            参与裁定需要登录账户，请先完成登录。
          </p>
          <Button href="/auth/login">
            立即登录
          </Button>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-center items-center min-h-96">
          <Loading size="large" />
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* 面包屑导航 */}
      <div className="mb-6">
        <Breadcrumb
          items={[
            { label: '首页', href: '/', icon: '🏠' },
            { label: '社交赌注', href: '/social-bet', icon: '🎲' },
            { label: '参与裁定', icon: '⚖️' }
          ]}
        />
      </div>

      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-label mb-2">
          ⚖️ 参与裁定
        </h1>
        <p className="text-secondary-label">
          参与DAO裁定机制，获得裁定奖励，维护平台公正
        </p>
      </div>

      {/* 用户认证等级 */}
      <Card className="p-6 mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-label mb-2">我的认证等级</h3>
            <CertificationBadge level={userLevel} size="large" />
          </div>
          <div className="text-right">
            <p className="text-sm text-secondary-label">今日剩余裁定次数</p>
            <p className="text-2xl font-bold text-label">{8 + userLevel * 5}</p>
          </div>
        </div>
      </Card>

      {/* 裁定列表 */}
      <div className="space-y-6">
        {judgmentBets.map((bet) => (
          <motion.div
            key={bet.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Card className="p-6 hover:shadow-lg transition-shadow">
              <div className="flex justify-between items-start mb-4">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h3 className="text-lg font-semibold text-label">
                      {bet.title}
                    </h3>
                    <Badge className="bg-purple-100 text-purple-800">
                      第{bet.round}轮裁定
                    </Badge>
                    <Badge className="bg-yellow-100 text-yellow-800">
                      +{bet.reward}福气
                    </Badge>
                  </div>
                  <p className="text-sm text-secondary-label mb-3">
                    {bet.description}
                  </p>
                  
                  <div className="flex items-center space-x-4 text-sm text-secondary-label">
                    <span>⏰ {formatTimeRemaining(bet.deadline)}</span>
                    <span>👥 {bet.totalVotes}人已投票</span>
                    <span>🏆 需要Lv.{bet.requiredLevel}+认证</span>
                  </div>
                </div>
              </div>

              {/* 投票选项 */}
              <div className="space-y-3 mb-4">
                {bet.options.map((option) => {
                  const percentage = getConsensusPercentage(option, bet.totalVotes);
                  return (
                    <div key={option.id} className="p-3 border rounded-lg">
                      <div className="flex justify-between items-center mb-2">
                        <span className="font-medium text-label">{option.text}</span>
                        <div className="text-right">
                          <span className="text-sm font-semibold text-label">
                            {option.votes}票 ({percentage.toFixed(1)}%)
                          </span>
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${percentage}%` }}
                        />
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* 操作按钮 */}
              <div className="flex justify-between items-center">
                <div className="flex items-center space-x-2">
                  <CertificationBadge level={bet.requiredLevel} size="small" />
                  <span className="text-sm text-secondary-label">
                    需要认证等级
                  </span>
                </div>
                
                {canParticipate(bet.requiredLevel) ? (
                  <Button
                    href={`/social-bet/judgment/${bet.id}`}
                    className="bg-gradient-to-r from-purple-500 to-pink-600 text-white"
                  >
                    ⚖️ 参与裁定
                  </Button>
                ) : (
                  <Button disabled variant="outline">
                    认证等级不足
                  </Button>
                )}
              </div>
            </Card>
          </motion.div>
        ))}
      </div>

      {judgmentBets.length === 0 && (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">⚖️</div>
          <h3 className="text-xl font-semibold text-label mb-2">暂无待裁定赌约</h3>
          <p className="text-secondary-label mb-6">
            当前没有需要裁定的赌约，请稍后再来查看。
          </p>
          <Button href="/social-bet">
            浏览赌约
          </Button>
        </div>
      )}
    </div>
  );
};

export default JudgmentPage;
