/**
 * 企业级社交赌约仪表板
 * 世界500强级别的视觉设计和用户体验
 */

'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Loading from '@/components/ui/Loading';

interface BetData {
  id: string;
  title: string;
  description: string;
  category: string;
  creator: string;
  totalPool: number;
  participants: number;
  endTime: string;
  status: 'active' | 'ended' | 'judging';
  trending?: boolean;
  featured?: boolean;
  options: Array<{
    id: string;
    text: string;
    odds: number;
    bets: number;
  }>;
}

interface UserStats {
  fortuneBalance: number;
  totalBets: number;
  winRate: number;
  totalEarnings: number;
  rank: number;
}

const EnterpriseSocialBetDashboard: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [activeBets, setActiveBets] = useState<BetData[]>([]);
  const [userStats, setUserStats] = useState<UserStats>({
    fortuneBalance: 1250,
    totalBets: 23,
    winRate: 68.5,
    totalEarnings: 3420,
    rank: 156
  });

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      // 模拟数据加载
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockBets: BetData[] = [
        {
          id: '1',
          title: '2024年美国总统大选结果预测',
          description: '预测2024年美国总统大选的获胜者',
          category: 'politics',
          creator: 'PoliticalAnalyst',
          totalPool: 125000,
          participants: 342,
          endTime: '2024-11-05T20:00:00Z',
          status: 'active',
          trending: true,
          featured: true,
          options: [
            { id: 'A', text: '民主党候选人', odds: 1.85, bets: 67500 },
            { id: 'B', text: '共和党候选人', odds: 2.10, bets: 57500 }
          ]
        },
        {
          id: '2',
          title: 'ChatGPT-5发布时间预测',
          description: '预测OpenAI何时发布ChatGPT-5',
          category: 'technology',
          creator: 'TechGuru',
          totalPool: 89000,
          participants: 156,
          endTime: '2024-12-31T23:59:59Z',
          status: 'active',
          trending: true,
          options: [
            { id: 'A', text: '2024年内', odds: 2.5, bets: 35600 },
            { id: 'B', text: '2025年上半年', odds: 1.8, bets: 49400 },
            { id: 'C', text: '2025年下半年或更晚', odds: 3.2, bets: 4000 }
          ]
        }
      ];
      
      setActiveBets(mockBets);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getCategoryIcon = (category: string) => {
    const icons: Record<string, string> = {
      sports: '🏈',
      politics: '🏛️',
      technology: '💻',
      entertainment: '🎬',
      finance: '💰',
      weather: '🌤️',
      social: '👥',
      other: '🔮'
    };
    return icons[category] || '🎲';
  };

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      sports: 'from-orange-500 to-red-500',
      politics: 'from-blue-500 to-indigo-600',
      technology: 'from-purple-500 to-pink-500',
      entertainment: 'from-pink-500 to-rose-500',
      finance: 'from-green-500 to-emerald-600',
      weather: 'from-cyan-500 to-blue-500',
      social: 'from-violet-500 to-purple-600',
      other: 'from-gray-500 to-slate-600'
    };
    return colors[category] || 'from-gray-500 to-slate-600';
  };

  const getStatusBadge = (status: string) => {
    const badges: Record<string, { text: string; color: string }> = {
      active: { text: '🔥 火热进行', color: 'bg-green-100 text-green-800 border-green-200' },
      ended: { text: '⏰ 已结束', color: 'bg-gray-100 text-gray-800 border-gray-200' },
      judging: { text: '⚖️ 裁定中', color: 'bg-yellow-100 text-yellow-800 border-yellow-200' }
    };
    return badges[status] || badges.active;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <Loading size="large" />
          <p className="mt-4 text-gray-600 font-medium">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* 导航栏 */}
      <div className="bg-white/80 backdrop-blur-md border-b border-gray-200/50 sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.back()}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              <div className="flex items-center space-x-2">
                <span className="text-2xl">🎲</span>
                <h1 className="text-xl font-bold text-gray-900">社交赌约</h1>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="hidden md:flex items-center space-x-6 text-sm text-gray-600">
                <span>首页</span>
                <span className="text-gray-400">/</span>
                <span className="text-blue-600 font-medium">社交赌约</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 用户福气余额卡片 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-700 rounded-2xl p-8 text-white shadow-2xl relative overflow-hidden">
            <div className="absolute inset-0 bg-black/10"></div>
            <div className="absolute top-0 right-0 w-64 h-64 bg-white/5 rounded-full -translate-y-32 translate-x-32"></div>
            <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full translate-y-24 -translate-x-24"></div>
            
            <div className="relative z-10">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h2 className="text-2xl font-bold mb-2">我的福气钱包</h2>
                  <p className="text-blue-100">您的数字资产中心</p>
                </div>
                <div className="text-6xl opacity-20">💎</div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                  <div className="text-3xl font-bold mb-1">{userStats.fortuneBalance.toLocaleString()}</div>
                  <div className="text-blue-100 text-sm">福气余额</div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                  <div className="text-3xl font-bold mb-1">{userStats.totalBets}</div>
                  <div className="text-blue-100 text-sm">参与赌约</div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                  <div className="text-3xl font-bold mb-1">{userStats.winRate}%</div>
                  <div className="text-blue-100 text-sm">胜率</div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                  <div className="text-3xl font-bold mb-1">#{userStats.rank}</div>
                  <div className="text-blue-100 text-sm">排名</div>
                </div>
              </div>
              
              <div className="flex flex-wrap gap-3 mt-6">
                <Button
                  onClick={() => router.push('/fortune/exchange')}
                  className="bg-white/20 hover:bg-white/30 text-white border-white/30 backdrop-blur-sm"
                  variant="outline"
                >
                  💰 充值福气
                </Button>
                <Button
                  onClick={() => router.push('/fortune/exchange')}
                  className="bg-white/20 hover:bg-white/30 text-white border-white/30 backdrop-blur-sm"
                  variant="outline"
                >
                  💸 提现HAOX
                </Button>
                <Button
                  onClick={() => router.push('/social-bet/create')}
                  className="bg-white text-blue-600 hover:bg-blue-50 font-semibold"
                >
                  🎯 创建赌约
                </Button>
              </div>
            </div>
          </div>
        </motion.div>

        {/* 快速操作栏 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="mb-8"
        >
          <div className="bg-white rounded-xl shadow-sm border border-gray-200/50 p-6">
            <div className="flex flex-wrap items-center justify-between gap-4">
              <div className="flex items-center space-x-6">
                <button className="flex items-center space-x-2 px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors">
                  <span>🔥</span>
                  <span className="font-medium">热门赌约</span>
                </button>
                <button className="flex items-center space-x-2 px-4 py-2 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors">
                  <span>⭐</span>
                  <span>精选推荐</span>
                </button>
                <button className="flex items-center space-x-2 px-4 py-2 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors">
                  <span>📊</span>
                  <span>排行榜</span>
                </button>
              </div>
              
              <div className="flex items-center space-x-3">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="搜索赌约..."
                    className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-64"
                  />
                  <svg className="absolute left-3 top-2.5 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* 活跃赌约列表 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">🔥 热门赌约</h2>
            <p className="text-gray-600">参与最受欢迎的预测，赢取丰厚奖励</p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <AnimatePresence>
              {activeBets.map((bet, index) => (
                <motion.div
                  key={bet.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="group"
                >
                  <div className="bg-white rounded-xl shadow-sm border border-gray-200/50 hover:shadow-lg transition-all duration-300 overflow-hidden">
                    {/* 卡片头部 */}
                    <div className={`h-2 bg-gradient-to-r ${getCategoryColor(bet.category)}`}></div>
                    
                    <div className="p-6">
                      {/* 标题和状态 */}
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <span className="text-2xl">{getCategoryIcon(bet.category)}</span>
                            {bet.trending && (
                              <span className="px-2 py-1 bg-red-100 text-red-600 text-xs font-medium rounded-full">
                                🔥 热门
                              </span>
                            )}
                            {bet.featured && (
                              <span className="px-2 py-1 bg-yellow-100 text-yellow-600 text-xs font-medium rounded-full">
                                ⭐ 精选
                              </span>
                            )}
                          </div>
                          <h3 className="text-lg font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                            {bet.title}
                          </h3>
                          <p className="text-gray-600 text-sm line-clamp-2">{bet.description}</p>
                        </div>
                        
                        <div className={`px-3 py-1 rounded-full text-xs font-medium border ${getStatusBadge(bet.status).color}`}>
                          {getStatusBadge(bet.status).text}
                        </div>
                      </div>
                      
                      {/* 统计信息 */}
                      <div className="grid grid-cols-3 gap-4 mb-4 p-4 bg-gray-50 rounded-lg">
                        <div className="text-center">
                          <div className="text-xl font-bold text-gray-900">{bet.totalPool.toLocaleString()}</div>
                          <div className="text-xs text-gray-600">奖池 (福气)</div>
                        </div>
                        <div className="text-center">
                          <div className="text-xl font-bold text-gray-900">{bet.participants}</div>
                          <div className="text-xs text-gray-600">参与人数</div>
                        </div>
                        <div className="text-center">
                          <div className="text-xl font-bold text-gray-900">
                            {Math.ceil((new Date(bet.endTime).getTime() - Date.now()) / (1000 * 60 * 60 * 24))}
                          </div>
                          <div className="text-xs text-gray-600">剩余天数</div>
                        </div>
                      </div>
                      
                      {/* 投注选项 */}
                      <div className="space-y-2 mb-4">
                        {bet.options.map((option) => (
                          <div key={option.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-blue-50 transition-colors cursor-pointer">
                            <div className="flex items-center space-x-3">
                              <div className="w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-bold">
                                {option.id}
                              </div>
                              <span className="font-medium text-gray-900">{option.text}</span>
                            </div>
                            <div className="text-right">
                              <div className="text-lg font-bold text-blue-600">{option.odds}x</div>
                              <div className="text-xs text-gray-500">{option.bets.toLocaleString()} 福气</div>
                            </div>
                          </div>
                        ))}
                      </div>
                      
                      {/* 操作按钮 */}
                      <div className="flex space-x-3">
                        <Button
                          onClick={() => router.push(`/social-bet/${bet.id}`)}
                          className="flex-1 bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:from-blue-600 hover:to-purple-700"
                        >
                          🎯 立即参与
                        </Button>
                        <Button
                          variant="outline"
                          className="px-4 border-gray-300 text-gray-600 hover:bg-gray-50"
                        >
                          📊 详情
                        </Button>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default EnterpriseSocialBetDashboard;
