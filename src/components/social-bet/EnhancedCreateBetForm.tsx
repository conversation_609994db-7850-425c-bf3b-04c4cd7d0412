/**
 * 增强版创建赌约表单
 * 修复所有输入问题，提供企业级用户体验
 */

'use client';

import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Loading from '@/components/ui/Loading';

interface BetOption {
  id: string;
  text: string;
}

interface CreateBetFormData {
  title: string;
  description: string;
  category: string;
  betType: '1v1' | '1vN';
  minBetAmount: number;
  maxBetAmount: number;
  bettingDeadline: string;
  resultDeadline: string;
  options: BetOption[];
  platformFeeRate: number;
  referralRewardRate: number;
}

const EnhancedCreateBetForm: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const [formData, setFormData] = useState<CreateBetFormData>({
    title: '',
    description: '',
    category: 'sports',
    betType: '1vN',
    minBetAmount: 10,
    maxBetAmount: 10000,
    bettingDeadline: '',
    resultDeadline: '',
    options: [
      { id: 'A', text: '' },
      { id: 'B', text: '' }
    ],
    platformFeeRate: 0.05,
    referralRewardRate: 0.10
  });

  const categories = [
    { value: 'sports', label: '🏈 体育竞技' },
    { value: 'entertainment', label: '🎬 娱乐影视' },
    { value: 'technology', label: '💻 科技数码' },
    { value: 'finance', label: '💰 金融投资' },
    { value: 'politics', label: '🏛️ 政治时事' },
    { value: 'weather', label: '🌤️ 天气预测' },
    { value: 'social', label: '👥 社会热点' },
    { value: 'other', label: '🔮 其他预测' }
  ];

  // 处理表单字段更新
  const handleInputChange = useCallback((field: keyof CreateBetFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    // 清除错误消息
    if (error) setError('');
  }, [error]);

  // 处理选项更新
  const handleOptionChange = useCallback((index: number, text: string) => {
    const newOptions = [...formData.options];
    newOptions[index].text = text;
    setFormData(prev => ({
      ...prev,
      options: newOptions
    }));
  }, [formData.options]);

  // 添加选项
  const addOption = useCallback(() => {
    if (formData.options.length < 10) {
      const newId = String.fromCharCode(65 + formData.options.length); // A, B, C...
      setFormData(prev => ({
        ...prev,
        options: [...prev.options, { id: newId, text: '' }]
      }));
    }
  }, [formData.options.length]);

  // 删除选项
  const removeOption = useCallback((index: number) => {
    if (formData.options.length > 2) {
      const newOptions = formData.options.filter((_, i) => i !== index);
      setFormData(prev => ({
        ...prev,
        options: newOptions
      }));
    }
  }, [formData.options.length]);

  // 表单验证
  const validateForm = (): boolean => {
    if (!formData.title.trim()) {
      setError('请输入赌约标题');
      return false;
    }
    if (!formData.description.trim()) {
      setError('请输入详细描述');
      return false;
    }
    if (formData.options.some(option => !option.text.trim())) {
      setError('请填写所有投注选项');
      return false;
    }
    if (!formData.bettingDeadline) {
      setError('请设置投注截止时间');
      return false;
    }
    if (!formData.resultDeadline) {
      setError('请设置结果截止时间');
      return false;
    }
    if (new Date(formData.bettingDeadline) <= new Date()) {
      setError('投注截止时间必须晚于当前时间');
      return false;
    }
    if (new Date(formData.resultDeadline) <= new Date(formData.bettingDeadline)) {
      setError('结果截止时间必须晚于投注截止时间');
      return false;
    }
    return true;
  };

  // 提交表单
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setError('');

    try {
      const response = await fetch('/api/social-bet/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          creatorId: user?.id
        })
      });

      const result = await response.json();

      if (result.success) {
        setSuccess('🎉 赌约创建成功！正在跳转到赌约详情页...');
        setTimeout(() => {
          router.push(`/social-bet/${result.data.id}`);
        }, 2000);
      } else {
        setError(result.message || '创建失败，请稍后重试');
      }
    } catch (error) {
      console.error('创建赌约失败:', error);
      setError('创建失败，请检查网络连接后重试');
    } finally {
      setLoading(false);
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center">
        <div className="max-w-md mx-auto text-center p-8">
          <div className="text-6xl mb-4">🔒</div>
          <h2 className="text-2xl font-bold text-gray-900 mb-4">需要登录</h2>
          <p className="text-gray-600 mb-6">
            创建赌约需要登录账户，请先完成登录。
          </p>
          <Button 
            onClick={() => router.push('/auth/login')}
            className="bg-blue-600 text-white hover:bg-blue-700"
          >
            立即登录
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* 导航栏 */}
      <div className="bg-white/80 backdrop-blur-md border-b border-gray-200/50 sticky top-0 z-40">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.back()}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              <div className="flex items-center space-x-2">
                <span className="text-2xl">🎯</span>
                <h1 className="text-xl font-bold text-gray-900">创建赌约</h1>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="hidden md:flex items-center space-x-6 text-sm text-gray-600">
                <span>首页</span>
                <span className="text-gray-400">/</span>
                <span>社交赌约</span>
                <span className="text-gray-400">/</span>
                <span className="text-blue-600 font-medium">创建赌约</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面标题 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8 text-center"
        >
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            🎯 创建赌约
          </h1>
          <p className="text-gray-600">
            设置一个有趣的预测话题，邀请其他用户参与投注
          </p>
        </motion.div>

        {/* 错误和成功消息 */}
        <AnimatePresence>
          {error && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg"
            >
              <div className="flex items-center">
                <span className="text-red-600 mr-2">❌</span>
                <span className="text-red-800 font-medium">{error}</span>
              </div>
            </motion.div>
          )}
          
          {success && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg"
            >
              <div className="flex items-center">
                <span className="text-green-600 mr-2">✅</span>
                <span className="text-green-800 font-medium">{success}</span>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* 基本信息 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <Card className="p-6 bg-white shadow-sm border border-gray-200/50">
              <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                <span className="mr-2">📝</span>
                基本信息
              </h2>
              
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    赌约标题 *
                  </label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    placeholder="例如：2024年世界杯冠军预测"
                    maxLength={100}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 placeholder-gray-400"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    {formData.title.length}/100 字符
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    详细描述 *
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder="详细描述赌约内容、规则和判定标准..."
                    rows={4}
                    maxLength={500}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 placeholder-gray-400 resize-vertical"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    {formData.description.length}/500 字符
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      分类 *
                    </label>
                    <select
                      value={formData.category}
                      onChange={(e) => handleInputChange('category', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900"
                    >
                      {categories.map(category => (
                        <option key={category.value} value={category.value}>
                          {category.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      赌约类型 *
                    </label>
                    <select
                      value={formData.betType}
                      onChange={(e) => handleInputChange('betType', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900"
                    >
                      <option value="1vN">1对多 (多人参与)</option>
                      <option value="1v1">1对1 (双人对决)</option>
                    </select>
                  </div>
                </div>
              </div>
            </Card>
          </motion.div>

          {/* 投注选项 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Card className="p-6 bg-white shadow-sm border border-gray-200/50">
              <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                <span className="mr-2">🎲</span>
                投注选项
              </h2>
              
              <div className="space-y-4">
                {formData.options.map((option, index) => (
                  <div key={option.id} className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-bold">
                      {option.id}
                    </div>
                    <div className="flex-1">
                      <input
                        type="text"
                        value={option.text}
                        onChange={(e) => handleOptionChange(index, e.target.value)}
                        placeholder={`选项 ${option.id} 的描述`}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 placeholder-gray-400"
                      />
                    </div>
                    {formData.options.length > 2 && (
                      <button
                        type="button"
                        onClick={() => removeOption(index)}
                        className="px-3 py-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                      >
                        删除
                      </button>
                    )}
                  </div>
                ))}
                
                {formData.options.length < 10 && (
                  <button
                    type="button"
                    onClick={addOption}
                    className="w-full py-3 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-blue-500 hover:text-blue-600 transition-colors"
                  >
                    + 添加选项
                  </button>
                )}
              </div>
            </Card>
          </motion.div>

          {/* 投注设置 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <Card className="p-6 bg-white shadow-sm border border-gray-200/50">
              <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                <span className="mr-2">💰</span>
                投注设置
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    最小投注金额 (福气) *
                  </label>
                  <input
                    type="number"
                    value={formData.minBetAmount}
                    onChange={(e) => handleInputChange('minBetAmount', parseInt(e.target.value) || 0)}
                    min={1}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    最大投注金额 (福气) *
                  </label>
                  <input
                    type="number"
                    value={formData.maxBetAmount}
                    onChange={(e) => handleInputChange('maxBetAmount', parseInt(e.target.value) || 0)}
                    min={formData.minBetAmount}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900"
                  />
                </div>
              </div>
            </Card>
          </motion.div>

          {/* 时间设置 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <Card className="p-6 bg-white shadow-sm border border-gray-200/50">
              <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                <span className="mr-2">⏰</span>
                时间设置
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    投注截止时间 *
                  </label>
                  <input
                    type="datetime-local"
                    value={formData.bettingDeadline}
                    onChange={(e) => handleInputChange('bettingDeadline', e.target.value)}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    此时间后将不能再投注
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    结果截止时间 *
                  </label>
                  <input
                    type="datetime-local"
                    value={formData.resultDeadline}
                    onChange={(e) => handleInputChange('resultDeadline', e.target.value)}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    此时间后开始裁定流程
                  </p>
                </div>
              </div>
            </Card>
          </motion.div>

          {/* 提交按钮 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            className="flex justify-end space-x-4"
          >
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
              className="px-6 py-3 border-gray-300 text-gray-700 hover:bg-gray-50"
            >
              取消
            </Button>
            
            <Button
              type="submit"
              disabled={loading}
              className="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:from-blue-600 hover:to-purple-700 disabled:opacity-50"
            >
              {loading ? (
                <div className="flex items-center">
                  <Loading size="small" />
                  <span className="ml-2">创建中...</span>
                </div>
              ) : (
                '🎯 创建赌约'
              )}
            </Button>
          </motion.div>
        </form>
      </div>
    </div>
  );
};

export default EnhancedCreateBetForm;
