'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Button, Card, Input, TextArea, Select, Loading } from '@/components/ui';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';

interface BetOption {
  id: string;
  text: string;
}

interface CreateBetFormData {
  title: string;
  description: string;
  category: string;
  betType: '1v1' | '1vN';
  minBetAmount: number;
  maxBetAmount: number;
  bettingDeadline: string;
  resultDeadline: string;
  options: BetOption[];
  platformFeeRate: number;
  referralRewardRate: number;
}

const CreateBetForm: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<CreateBetFormData>({
    title: '',
    description: '',
    category: 'sports',
    betType: '1vN',
    minBetAmount: 10,
    maxBetAmount: 10000,
    bettingDeadline: '',
    resultDeadline: '',
    options: [
      { id: 'A', text: '' },
      { id: 'B', text: '' }
    ],
    platformFeeRate: 0.05,
    referralRewardRate: 0.10
  });

  const categories = [
    { value: 'sports', label: '🏈 体育竞技' },
    { value: 'entertainment', label: '🎬 娱乐影视' },
    { value: 'technology', label: '💻 科技数码' },
    { value: 'finance', label: '💰 金融投资' },
    { value: 'politics', label: '🏛️ 政治时事' },
    { value: 'weather', label: '🌤️ 天气预测' },
    { value: 'social', label: '👥 社会热点' },
    { value: 'other', label: '🔮 其他预测' }
  ];

  const handleInputChange = (field: keyof CreateBetFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleOptionChange = (index: number, text: string) => {
    const newOptions = [...formData.options];
    newOptions[index].text = text;
    setFormData(prev => ({
      ...prev,
      options: newOptions
    }));
  };

  const addOption = () => {
    if (formData.options.length < 10) {
      const newId = String.fromCharCode(65 + formData.options.length); // A, B, C...
      setFormData(prev => ({
        ...prev,
        options: [...prev.options, { id: newId, text: '' }]
      }));
    }
  };

  const removeOption = (index: number) => {
    if (formData.options.length > 2) {
      const newOptions = formData.options.filter((_, i) => i !== index);
      setFormData(prev => ({
        ...prev,
        options: newOptions
      }));
    }
  };

  const validateForm = (): string | null => {
    if (!formData.title.trim()) return '请输入赌约标题';
    if (!formData.description.trim()) return '请输入赌约描述';
    if (formData.options.some(opt => !opt.text.trim())) return '请填写所有选项';
    if (formData.minBetAmount <= 0) return '最小投注金额必须大于0';
    if (formData.maxBetAmount < formData.minBetAmount) return '最大投注金额不能小于最小投注金额';
    if (!formData.bettingDeadline) return '请设置投注截止时间';
    if (!formData.resultDeadline) return '请设置结果截止时间';
    
    const bettingDate = new Date(formData.bettingDeadline);
    const resultDate = new Date(formData.resultDeadline);
    const now = new Date();
    
    if (bettingDate <= now) return '投注截止时间必须在当前时间之后';
    if (resultDate <= bettingDate) return '结果截止时间必须在投注截止时间之后';
    
    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!isAuthenticated) {
      alert('请先登录');
      return;
    }

    const validationError = validateForm();
    if (validationError) {
      alert(validationError);
      return;
    }

    setLoading(true);
    
    try {
      const response = await fetch('/api/social-bet/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
      });

      if (response.ok) {
        const result = await response.json();
        alert('赌约创建成功！');
        router.push(`/social-bet/${result.data.id}`);
      } else {
        const error = await response.json();
        alert(`创建失败: ${error.error || '未知错误'}`);
      }
    } catch (error) {
      console.error('创建赌约失败:', error);
      alert('创建失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="max-w-2xl mx-auto px-4 py-8 text-center">
        <div className="text-6xl mb-4">🔒</div>
        <h2 className="text-2xl font-bold text-label mb-4">需要登录</h2>
        <p className="text-secondary-label mb-6">
          创建赌约需要登录账户，请先完成登录。
        </p>
        <Button href="/auth/login">
          立即登录
        </Button>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-label mb-2">
          🎯 创建赌约
        </h1>
        <p className="text-secondary-label">
          设置一个有趣的预测话题，邀请其他用户参与投注
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* 基本信息 */}
        <Card className="p-6">
          <h2 className="text-xl font-semibold text-label mb-6">📝 基本信息</h2>
          
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-label mb-2">
                赌约标题 *
              </label>
              <Input
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                placeholder="例如：2024年世界杯冠军预测"
                maxLength={100}
                required
              />
              <p className="text-xs text-secondary-label mt-1">
                {formData.title.length}/100 字符
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-label mb-2">
                详细描述 *
              </label>
              <TextArea
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="详细描述赌约内容、规则和判定标准..."
                rows={4}
                maxLength={500}
                required
              />
              <p className="text-xs text-secondary-label mt-1">
                {formData.description.length}/500 字符
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-label mb-2">
                  分类 *
                </label>
                <Select
                  value={formData.category}
                  onChange={(value) => handleInputChange('category', value)}
                  options={categories}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-label mb-2">
                  赌约类型 *
                </label>
                <Select
                  value={formData.betType}
                  onChange={(value) => handleInputChange('betType', value)}
                  options={[
                    { value: '1v1', label: '1对1 (双方对赌)' },
                    { value: '1vN', label: '1对多 (多人参与)' }
                  ]}
                />
              </div>
            </div>
          </div>
        </Card>

        {/* 投注选项 */}
        <Card className="p-6">
          <h2 className="text-xl font-semibold text-label mb-6">🎲 投注选项</h2>
          
          <div className="space-y-4">
            {formData.options.map((option, index) => (
              <div key={option.id} className="flex items-center space-x-4">
                <div className="w-8 h-8 bg-system-gray-5 rounded-full flex items-center justify-center text-sm font-medium">
                  {option.id}
                </div>
                <div className="flex-1">
                  <Input
                    value={option.text}
                    onChange={(e) => handleOptionChange(index, e.target.value)}
                    placeholder={`选项 ${option.id} 的描述`}
                    required
                  />
                </div>
                {formData.options.length > 2 && (
                  <Button
                    type="button"
                    variant="outline"
                    size="small"
                    onClick={() => removeOption(index)}
                  >
                    删除
                  </Button>
                )}
              </div>
            ))}
            
            {formData.options.length < 10 && (
              <Button
                type="button"
                variant="outline"
                onClick={addOption}
                className="w-full"
              >
                + 添加选项
              </Button>
            )}
          </div>
        </Card>

        {/* 投注设置 */}
        <Card className="p-6">
          <h2 className="text-xl font-semibold text-label mb-6">💰 投注设置</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-label mb-2">
                最小投注金额 (福气) *
              </label>
              <Input
                type="number"
                value={formData.minBetAmount}
                onChange={(e) => handleInputChange('minBetAmount', parseInt(e.target.value) || 0)}
                min={1}
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-label mb-2">
                最大投注金额 (福气) *
              </label>
              <Input
                type="number"
                value={formData.maxBetAmount}
                onChange={(e) => handleInputChange('maxBetAmount', parseInt(e.target.value) || 0)}
                min={formData.minBetAmount}
                required
              />
            </div>
          </div>
        </Card>

        {/* 时间设置 */}
        <Card className="p-6">
          <h2 className="text-xl font-semibold text-label mb-6">⏰ 时间设置</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-label mb-2">
                投注截止时间 *
              </label>
              <Input
                type="datetime-local"
                value={formData.bettingDeadline}
                onChange={(e) => handleInputChange('bettingDeadline', e.target.value)}
                required
              />
              <p className="text-xs text-secondary-label mt-1">
                此时间后将不能再投注
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-label mb-2">
                结果截止时间 *
              </label>
              <Input
                type="datetime-local"
                value={formData.resultDeadline}
                onChange={(e) => handleInputChange('resultDeadline', e.target.value)}
                required
              />
              <p className="text-xs text-secondary-label mt-1">
                此时间后开始裁定流程
              </p>
            </div>
          </div>
        </Card>

        {/* 提交按钮 */}
        <div className="flex justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
          >
            取消
          </Button>
          
          <Button
            type="submit"
            disabled={loading}
            className="bg-gradient-to-r from-blue-500 to-purple-600 text-white"
          >
            {loading ? <Loading size="small" /> : '🎯 创建赌约'}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default CreateBetForm;
