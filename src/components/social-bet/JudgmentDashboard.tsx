/**
 * 裁定仪表板
 * 显示可裁定的赌约，提交裁定投票
 */

'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Loading from '@/components/ui/Loading';

interface JudgeableBet {
  id: string;
  title: string;
  description: string;
  category: string;
  options: Array<{
    id: string;
    text: string;
    votes: number;
  }>;
  currentRound: number;
  totalPool: number;
  participants: number;
  deadline: string;
  evidence?: string[];
}

interface UserJudgmentStats {
  certificationLevel: string;
  reputationScore: number;
  dailyJudgmentsUsed: number;
  dailyJudgmentLimit: number;
  totalJudgments: number;
  correctJudgments: number;
  accuracy: number;
  consecutiveCorrect: number;
}

const JudgmentDashboard: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  
  const [judgeableBets, setJudgeableBets] = useState<JudgeableBet[]>([]);
  const [userStats, setUserStats] = useState<UserJudgmentStats>({
    certificationLevel: 'X2',
    reputationScore: 125,
    dailyJudgmentsUsed: 2,
    dailyJudgmentLimit: 5,
    totalJudgments: 45,
    correctJudgments: 38,
    accuracy: 84.4,
    consecutiveCorrect: 3
  });
  
  const [selectedRound, setSelectedRound] = useState(1);

  useEffect(() => {
    if (isAuthenticated && user?.id) {
      loadJudgeableBets();
      loadUserJudgmentStats();
    }
  }, [isAuthenticated, user, selectedRound]);

  const loadJudgeableBets = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/social-bet/judgeable?round=${selectedRound}&userId=${user?.id}`);
      const result = await response.json();

      if (result.success) {
        setJudgeableBets(result.data || []);
      } else {
        setError(result.message || '加载失败');
      }
    } catch (error) {
      console.error('加载可裁定赌约失败:', error);
      setError('加载失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const loadUserJudgmentStats = async () => {
    try {
      const response = await fetch(`/api/social-bet/user-stats?userId=${user?.id}`);
      const result = await response.json();

      if (result.success && result.data.judgmentStats) {
        setUserStats(result.data.judgmentStats);
      }
    } catch (error) {
      console.error('加载用户裁定统计失败:', error);
    }
  };

  const handleSubmitJudgment = async (betId: string, selectedOption: string, confidenceLevel: number, reasoning: string) => {
    try {
      setSubmitting(true);
      setError('');

      const response = await fetch('/api/social-bet/judgment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          betId,
          selectedOption,
          round: selectedRound,
          confidenceLevel,
          reasoning
        })
      });

      const result = await response.json();

      if (result.success) {
        setSuccess('✅ 裁定提交成功！感谢您的参与。');
        
        // 更新统计信息
        setUserStats(prev => ({
          ...prev,
          dailyJudgmentsUsed: prev.dailyJudgmentsUsed + 1,
          totalJudgments: prev.totalJudgments + 1
        }));
        
        // 重新加载可裁定赌约
        loadJudgeableBets();
        
        // 3秒后清除成功消息
        setTimeout(() => setSuccess(''), 3000);
      } else {
        setError(result.message || '提交失败');
      }
    } catch (error) {
      console.error('提交裁定失败:', error);
      setError('提交失败，请稍后重试');
    } finally {
      setSubmitting(false);
    }
  };

  const getCertificationInfo = (level: string) => {
    const certifications: Record<string, { name: string; color: string; icon: string }> = {
      X1: { name: '新手认证', color: 'from-gray-400 to-gray-600', icon: '🥉' },
      X2: { name: '进阶认证', color: 'from-blue-400 to-blue-600', icon: '🥈' },
      X3: { name: '专家认证', color: 'from-purple-400 to-purple-600', icon: '🏅' },
      X4: { name: '大师认证', color: 'from-yellow-400 to-yellow-600', icon: '👑' },
      X5: { name: '传奇认证', color: 'from-red-400 to-red-600', icon: '💎' }
    };
    return certifications[level] || certifications.X1;
  };

  const getRoundInfo = (round: number) => {
    const rounds = {
      1: { name: '第一轮：大众评审', description: '所有认证用户可参与，需要11/20票通过', timeLimit: '24小时' },
      2: { name: '第二轮：专业评审', description: 'X1-X3认证用户可参与，需要6/10票通过', timeLimit: '12小时' },
      3: { name: '第三轮：终审裁定', description: 'X4-X5认证用户可参与，需要3/5票通过', timeLimit: '6小时' }
    };
    return rounds[round] || rounds[1];
  };

  const canParticipateInRound = (round: number, certLevel: string) => {
    const permissions = {
      1: ['X1', 'X2', 'X3', 'X4', 'X5'],
      2: ['X1', 'X2', 'X3'],
      3: ['X4', 'X5']
    };
    return permissions[round]?.includes(certLevel) || false;
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center">
        <div className="max-w-md mx-auto text-center p-8">
          <div className="text-6xl mb-4">🔒</div>
          <h2 className="text-2xl font-bold text-gray-900 mb-4">需要登录</h2>
          <p className="text-gray-600 mb-6">
            参与裁定需要登录账户，请先完成登录。
          </p>
          <Button 
            onClick={() => router.push('/auth/login')}
            className="bg-blue-600 text-white hover:bg-blue-700"
          >
            立即登录
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* 导航栏 */}
      <div className="bg-white/80 backdrop-blur-md border-b border-gray-200/50 sticky top-0 z-40">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.back()}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              <div className="flex items-center space-x-2">
                <span className="text-2xl">⚖️</span>
                <h1 className="text-xl font-bold text-gray-900">裁定中心</h1>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="hidden md:flex items-center space-x-6 text-sm text-gray-600">
                <span>首页</span>
                <span className="text-gray-400">/</span>
                <span>社交赌约</span>
                <span className="text-gray-400">/</span>
                <span className="text-blue-600 font-medium">裁定中心</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 用户裁定统计卡片 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <div className="bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-700 rounded-2xl p-8 text-white shadow-2xl relative overflow-hidden">
            <div className="absolute inset-0 bg-black/10"></div>
            <div className="absolute top-0 right-0 w-64 h-64 bg-white/5 rounded-full -translate-y-32 translate-x-32"></div>
            <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full translate-y-24 -translate-x-24"></div>
            
            <div className="relative z-10">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h2 className="text-2xl font-bold mb-2">我的裁定资格</h2>
                  <div className="flex items-center space-x-3">
                    <div className={`px-3 py-1 rounded-full bg-gradient-to-r ${getCertificationInfo(userStats.certificationLevel).color} text-white text-sm font-medium flex items-center space-x-1`}>
                      <span>{getCertificationInfo(userStats.certificationLevel).icon}</span>
                      <span>{getCertificationInfo(userStats.certificationLevel).name}</span>
                    </div>
                    <span className="text-blue-100">信誉积分: {userStats.reputationScore}</span>
                  </div>
                </div>
                <div className="text-6xl opacity-20">⚖️</div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                  <div className="text-3xl font-bold mb-1">{userStats.dailyJudgmentLimit - userStats.dailyJudgmentsUsed}</div>
                  <div className="text-blue-100 text-sm">今日剩余次数</div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                  <div className="text-3xl font-bold mb-1">{userStats.accuracy}%</div>
                  <div className="text-blue-100 text-sm">历史准确率</div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                  <div className="text-3xl font-bold mb-1">{userStats.consecutiveCorrect}</div>
                  <div className="text-blue-100 text-sm">连续正确</div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                  <div className="text-3xl font-bold mb-1">{userStats.totalJudgments}</div>
                  <div className="text-blue-100 text-sm">总裁定次数</div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* 错误和成功消息 */}
        <AnimatePresence>
          {error && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg"
            >
              <div className="flex items-center">
                <span className="text-red-600 mr-2">❌</span>
                <span className="text-red-800 font-medium">{error}</span>
              </div>
            </motion.div>
          )}
          
          {success && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg"
            >
              <div className="flex items-center">
                <span className="text-green-600 mr-2">✅</span>
                <span className="text-green-800 font-medium">{success}</span>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* 裁定轮次选择 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="mb-8"
        >
          <div className="bg-white rounded-xl shadow-sm border border-gray-200/50 p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">选择裁定轮次</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {[1, 2, 3].map((round) => {
                const roundInfo = getRoundInfo(round);
                const canParticipate = canParticipateInRound(round, userStats.certificationLevel);
                const isSelected = selectedRound === round;
                
                return (
                  <button
                    key={round}
                    onClick={() => canParticipate && setSelectedRound(round)}
                    disabled={!canParticipate}
                    className={`p-4 rounded-lg border-2 transition-all duration-200 text-left ${
                      isSelected
                        ? 'border-blue-500 bg-blue-50'
                        : canParticipate
                        ? 'border-gray-200 hover:border-blue-300 hover:bg-blue-50'
                        : 'border-gray-200 bg-gray-50 opacity-50 cursor-not-allowed'
                    }`}
                  >
                    <div className="font-semibold text-gray-900 mb-2">{roundInfo.name}</div>
                    <div className="text-sm text-gray-600 mb-2">{roundInfo.description}</div>
                    <div className="text-xs text-gray-500">时限: {roundInfo.timeLimit}</div>
                    {!canParticipate && (
                      <div className="text-xs text-red-500 mt-2">认证等级不足</div>
                    )}
                  </button>
                );
              })}
            </div>
          </div>
        </motion.div>

        {/* 可裁定赌约列表 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              ⚖️ 第{selectedRound}轮裁定 - 待裁定赌约
            </h2>
            <p className="text-gray-600">
              {getRoundInfo(selectedRound).description}
            </p>
          </div>
          
          {loading ? (
            <div className="flex justify-center py-12">
              <Loading size="large" />
            </div>
          ) : judgeableBets.length === 0 ? (
            <Card className="p-12 text-center">
              <div className="text-6xl mb-4">⚖️</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">暂无待裁定赌约</h3>
              <p className="text-gray-600">
                当前第{selectedRound}轮没有需要裁定的赌约，请稍后再来查看。
              </p>
            </Card>
          ) : (
            <div className="grid grid-cols-1 gap-6">
              {judgeableBets.map((bet, index) => (
                <JudgmentBetCard
                  key={bet.id}
                  bet={bet}
                  round={selectedRound}
                  onSubmitJudgment={handleSubmitJudgment}
                  submitting={submitting}
                  index={index}
                />
              ))}
            </div>
          )}
        </motion.div>
      </div>
    </div>
  );
};

// 裁定赌约卡片组件
interface JudgmentBetCardProps {
  bet: JudgeableBet;
  round: number;
  onSubmitJudgment: (betId: string, selectedOption: string, confidenceLevel: number, reasoning: string) => void;
  submitting: boolean;
  index: number;
}

const JudgmentBetCard: React.FC<JudgmentBetCardProps> = ({ bet, round, onSubmitJudgment, submitting, index }) => {
  const [selectedOption, setSelectedOption] = useState('');
  const [confidenceLevel, setConfidenceLevel] = useState(3);
  const [reasoning, setReasoning] = useState('');
  const [showForm, setShowForm] = useState(false);

  const handleSubmit = () => {
    if (!selectedOption || !reasoning.trim()) {
      alert('请选择选项并填写裁定理由');
      return;
    }

    onSubmitJudgment(bet.id, selectedOption, confidenceLevel, reasoning);
    setShowForm(false);
    setSelectedOption('');
    setReasoning('');
    setConfidenceLevel(3);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
    >
      <Card className="p-6 bg-white shadow-sm border border-gray-200/50 hover:shadow-lg transition-all duration-300">
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <h3 className="text-lg font-bold text-gray-900 mb-2">{bet.title}</h3>
            <p className="text-gray-600 text-sm mb-3">{bet.description}</p>
            
            <div className="flex items-center space-x-4 text-sm text-gray-500">
              <span>💰 奖池: {bet.totalPool.toLocaleString()} 福气</span>
              <span>👥 参与: {bet.participants} 人</span>
              <span>⏰ 截止: {new Date(bet.deadline).toLocaleString()}</span>
            </div>
          </div>
        </div>

        {/* 投票选项 */}
        <div className="mb-4">
          <h4 className="font-medium text-gray-900 mb-3">当前投票情况:</h4>
          <div className="space-y-2">
            {bet.options.map((option) => (
              <div key={option.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span className="font-medium text-gray-900">{option.text}</span>
                <div className="text-right">
                  <div className="text-sm font-medium text-gray-900">{option.votes} 票</div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 裁定表单 */}
        {showForm ? (
          <div className="border-t pt-4">
            <h4 className="font-medium text-gray-900 mb-3">提交您的裁定:</h4>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">选择您认为正确的选项:</label>
                <div className="space-y-2">
                  {bet.options.map((option) => (
                    <label key={option.id} className="flex items-center">
                      <input
                        type="radio"
                        name={`judgment-${bet.id}`}
                        value={option.id}
                        checked={selectedOption === option.id}
                        onChange={(e) => setSelectedOption(e.target.value)}
                        className="mr-3"
                      />
                      <span>{option.text}</span>
                    </label>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  信心等级: {confidenceLevel}/5
                </label>
                <input
                  type="range"
                  min="1"
                  max="5"
                  value={confidenceLevel}
                  onChange={(e) => setConfidenceLevel(parseInt(e.target.value))}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>不确定</span>
                  <span>非常确定</span>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">裁定理由:</label>
                <textarea
                  value={reasoning}
                  onChange={(e) => setReasoning(e.target.value)}
                  placeholder="请详细说明您的裁定理由和依据..."
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div className="flex space-x-3">
                <Button
                  onClick={handleSubmit}
                  disabled={submitting || !selectedOption || !reasoning.trim()}
                  className="bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-50"
                >
                  {submitting ? '提交中...' : '提交裁定'}
                </Button>
                <Button
                  onClick={() => setShowForm(false)}
                  variant="outline"
                  className="border-gray-300 text-gray-600 hover:bg-gray-50"
                >
                  取消
                </Button>
              </div>
            </div>
          </div>
        ) : (
          <div className="flex justify-end">
            <Button
              onClick={() => setShowForm(true)}
              className="bg-gradient-to-r from-purple-500 to-blue-600 text-white hover:from-purple-600 hover:to-blue-700"
            >
              ⚖️ 开始裁定
            </Button>
          </div>
        )}
      </Card>
    </motion.div>
  );
};

export default JudgmentDashboard;
