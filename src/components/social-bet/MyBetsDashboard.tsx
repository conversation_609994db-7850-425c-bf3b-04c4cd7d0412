/**
 * 我的赌约仪表板
 * 显示用户创建和参与的所有赌约
 */

'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Loading from '@/components/ui/Loading';

interface UserBet {
  id: string;
  title: string;
  description: string;
  category: string;
  status: 'active' | 'ended' | 'judging';
  role: 'creator' | 'participant';
  myBetAmount?: number;
  mySelectedOption?: string;
  totalPool: number;
  participants: number;
  createdAt: string;
  endTime: string;
  resultDeadline: string;
  options: Array<{
    id: string;
    text: string;
    odds: number;
    bets: number;
  }>;
  result?: {
    winningOption: string;
    myEarnings?: number;
    isWinner?: boolean;
  };
}

interface UserBetStats {
  totalCreated: number;
  totalParticipated: number;
  totalEarnings: number;
  winRate: number;
  activeBets: number;
  completedBets: number;
}

const MyBetsDashboard: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'all' | 'created' | 'participated' | 'active' | 'completed'>('all');
  
  const [userBets, setUserBets] = useState<UserBet[]>([]);
  const [userStats, setUserStats] = useState<UserBetStats>({
    totalCreated: 5,
    totalParticipated: 18,
    totalEarnings: 3420,
    winRate: 68.5,
    activeBets: 8,
    completedBets: 15
  });

  useEffect(() => {
    if (isAuthenticated && user?.id) {
      loadUserBets();
      loadUserStats();
    }
  }, [isAuthenticated, user, activeTab]);

  const loadUserBets = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/social-bet/user-bets?userId=${user?.id}&filter=${activeTab}`);
      const result = await response.json();

      if (result.success) {
        setUserBets(result.data || []);
      } else {
        console.error('加载用户赌约失败:', result.message);
      }
    } catch (error) {
      console.error('加载用户赌约失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadUserStats = async () => {
    try {
      const response = await fetch(`/api/social-bet/user-stats?userId=${user?.id}`);
      const result = await response.json();

      if (result.success && result.data.betStats) {
        setUserStats(result.data.betStats);
      }
    } catch (error) {
      console.error('加载用户统计失败:', error);
    }
  };

  const getCategoryIcon = (category: string) => {
    const icons: Record<string, string> = {
      sports: '🏈',
      politics: '🏛️',
      technology: '💻',
      entertainment: '🎬',
      finance: '💰',
      weather: '🌤️',
      social: '👥',
      other: '🔮'
    };
    return icons[category] || '🎲';
  };

  const getStatusBadge = (status: string) => {
    const badges: Record<string, { text: string; color: string }> = {
      active: { text: '🔥 进行中', color: 'bg-green-100 text-green-800 border-green-200' },
      ended: { text: '✅ 已结束', color: 'bg-gray-100 text-gray-800 border-gray-200' },
      judging: { text: '⚖️ 裁定中', color: 'bg-yellow-100 text-yellow-800 border-yellow-200' }
    };
    return badges[status] || badges.active;
  };

  const getRoleBadge = (role: string) => {
    const badges: Record<string, { text: string; color: string }> = {
      creator: { text: '👑 创建者', color: 'bg-purple-100 text-purple-800 border-purple-200' },
      participant: { text: '🎯 参与者', color: 'bg-blue-100 text-blue-800 border-blue-200' }
    };
    return badges[role] || badges.participant;
  };

  const filteredBets = userBets.filter(bet => {
    switch (activeTab) {
      case 'created':
        return bet.role === 'creator';
      case 'participated':
        return bet.role === 'participant';
      case 'active':
        return bet.status === 'active' || bet.status === 'judging';
      case 'completed':
        return bet.status === 'ended';
      default:
        return true;
    }
  });

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center">
        <div className="max-w-md mx-auto text-center p-8">
          <div className="text-6xl mb-4">🔒</div>
          <h2 className="text-2xl font-bold text-gray-900 mb-4">需要登录</h2>
          <p className="text-gray-600 mb-6">
            查看我的赌约需要登录账户，请先完成登录。
          </p>
          <Button 
            onClick={() => router.push('/auth/login')}
            className="bg-blue-600 text-white hover:bg-blue-700"
          >
            立即登录
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* 导航栏 */}
      <div className="bg-white/80 backdrop-blur-md border-b border-gray-200/50 sticky top-0 z-40">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.back()}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              <div className="flex items-center space-x-2">
                <span className="text-2xl">📋</span>
                <h1 className="text-xl font-bold text-gray-900">我的赌约</h1>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="hidden md:flex items-center space-x-6 text-sm text-gray-600">
                <span>首页</span>
                <span className="text-gray-400">/</span>
                <span>社交赌约</span>
                <span className="text-gray-400">/</span>
                <span className="text-blue-600 font-medium">我的赌约</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 用户赌约统计卡片 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <div className="bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-700 rounded-2xl p-8 text-white shadow-2xl relative overflow-hidden">
            <div className="absolute inset-0 bg-black/10"></div>
            <div className="absolute top-0 right-0 w-64 h-64 bg-white/5 rounded-full -translate-y-32 translate-x-32"></div>
            <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full translate-y-24 -translate-x-24"></div>
            
            <div className="relative z-10">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h2 className="text-2xl font-bold mb-2">我的赌约统计</h2>
                  <p className="text-blue-100">您的投注历史和收益概览</p>
                </div>
                <div className="text-6xl opacity-20">📊</div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                  <div className="text-2xl font-bold mb-1">{userStats.totalCreated}</div>
                  <div className="text-blue-100 text-sm">创建赌约</div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                  <div className="text-2xl font-bold mb-1">{userStats.totalParticipated}</div>
                  <div className="text-blue-100 text-sm">参与赌约</div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                  <div className="text-2xl font-bold mb-1">{userStats.activeBets}</div>
                  <div className="text-blue-100 text-sm">进行中</div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                  <div className="text-2xl font-bold mb-1">{userStats.completedBets}</div>
                  <div className="text-blue-100 text-sm">已完成</div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                  <div className="text-2xl font-bold mb-1">{userStats.winRate}%</div>
                  <div className="text-blue-100 text-sm">胜率</div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                  <div className="text-2xl font-bold mb-1">{userStats.totalEarnings.toLocaleString()}</div>
                  <div className="text-blue-100 text-sm">总收益</div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* 筛选标签页 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="mb-8"
        >
          <div className="bg-white rounded-xl shadow-sm border border-gray-200/50 p-2">
            <div className="flex flex-wrap gap-2">
              {[
                { key: 'all', label: '全部赌约', icon: '📋' },
                { key: 'created', label: '我创建的', icon: '👑' },
                { key: 'participated', label: '我参与的', icon: '🎯' },
                { key: 'active', label: '进行中', icon: '🔥' },
                { key: 'completed', label: '已完成', icon: '✅' }
              ].map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setActiveTab(tab.key as any)}
                  className={`flex items-center space-x-2 py-2 px-4 rounded-lg transition-all duration-200 ${
                    activeTab === tab.key
                      ? 'bg-blue-500 text-white shadow-md'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  <span>{tab.icon}</span>
                  <span className="font-medium">{tab.label}</span>
                </button>
              ))}
            </div>
          </div>
        </motion.div>

        {/* 赌约列表 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              📋 {activeTab === 'all' ? '全部赌约' : 
                   activeTab === 'created' ? '我创建的赌约' :
                   activeTab === 'participated' ? '我参与的赌约' :
                   activeTab === 'active' ? '进行中的赌约' : '已完成的赌约'}
            </h2>
            <p className="text-gray-600">
              共找到 {filteredBets.length} 个赌约
            </p>
          </div>
          
          {loading ? (
            <div className="flex justify-center py-12">
              <Loading size="large" />
            </div>
          ) : filteredBets.length === 0 ? (
            <Card className="p-12 text-center">
              <div className="text-6xl mb-4">📋</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">暂无赌约</h3>
              <p className="text-gray-600 mb-6">
                {activeTab === 'created' ? '您还没有创建任何赌约' :
                 activeTab === 'participated' ? '您还没有参与任何赌约' :
                 '暂时没有符合条件的赌约'}
              </p>
              <Button
                onClick={() => router.push('/social-bet/create')}
                className="bg-blue-600 text-white hover:bg-blue-700"
              >
                🎯 创建第一个赌约
              </Button>
            </Card>
          ) : (
            <div className="grid grid-cols-1 gap-6">
              <AnimatePresence>
                {filteredBets.map((bet, index) => (
                  <motion.div
                    key={bet.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                  >
                    <Card className="p-6 bg-white shadow-sm border border-gray-200/50 hover:shadow-lg transition-all duration-300">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <span className="text-2xl">{getCategoryIcon(bet.category)}</span>
                            <div className={`px-2 py-1 rounded-full text-xs font-medium border ${getRoleBadge(bet.role).color}`}>
                              {getRoleBadge(bet.role).text}
                            </div>
                            <div className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusBadge(bet.status).color}`}>
                              {getStatusBadge(bet.status).text}
                            </div>
                          </div>
                          <h3 className="text-lg font-bold text-gray-900 mb-2">{bet.title}</h3>
                          <p className="text-gray-600 text-sm mb-3 line-clamp-2">{bet.description}</p>
                        </div>
                      </div>

                      {/* 赌约信息 */}
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4 p-4 bg-gray-50 rounded-lg">
                        <div className="text-center">
                          <div className="text-lg font-bold text-gray-900">{bet.totalPool.toLocaleString()}</div>
                          <div className="text-xs text-gray-600">奖池 (福气)</div>
                        </div>
                        <div className="text-center">
                          <div className="text-lg font-bold text-gray-900">{bet.participants}</div>
                          <div className="text-xs text-gray-600">参与人数</div>
                        </div>
                        <div className="text-center">
                          <div className="text-lg font-bold text-gray-900">
                            {bet.myBetAmount ? bet.myBetAmount.toLocaleString() : '-'}
                          </div>
                          <div className="text-xs text-gray-600">我的投注</div>
                        </div>
                      </div>

                      {/* 我的选择和结果 */}
                      {bet.role === 'participant' && bet.mySelectedOption && (
                        <div className="mb-4 p-3 bg-blue-50 rounded-lg">
                          <div className="text-sm font-medium text-blue-900 mb-1">我的选择:</div>
                          <div className="text-blue-800">
                            {bet.options.find(opt => opt.id === bet.mySelectedOption)?.text}
                          </div>
                          {bet.result && (
                            <div className={`mt-2 text-sm font-medium ${bet.result.isWinner ? 'text-green-600' : 'text-red-600'}`}>
                              {bet.result.isWinner ? 
                                `🎉 恭喜获胜！收益: ${bet.result.myEarnings?.toLocaleString()} 福气` :
                                '😔 很遗憾，这次没有获胜'
                              }
                            </div>
                          )}
                        </div>
                      )}

                      {/* 操作按钮 */}
                      <div className="flex space-x-3">
                        <Button
                          onClick={() => router.push(`/social-bet/${bet.id}`)}
                          className="flex-1 bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:from-blue-600 hover:to-purple-700"
                        >
                          📊 查看详情
                        </Button>
                        {bet.status === 'judging' && (
                          <Button
                            onClick={() => router.push(`/social-bet/judgment?betId=${bet.id}`)}
                            className="bg-gradient-to-r from-yellow-500 to-orange-600 text-white hover:from-yellow-600 hover:to-orange-700"
                          >
                            ⚖️ 参与裁定
                          </Button>
                        )}
                      </div>
                    </Card>
                  </motion.div>
                ))}
              </AnimatePresence>
            </div>
          )}
        </motion.div>
      </div>
    </div>
  );
};

export default MyBetsDashboard;
