'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>ton, Card, Loading, Breadcrumb, Badge } from '@/components/ui';
import { useAuth } from '@/contexts/AuthContext';

interface MyBet {
  id: string;
  title: string;
  description: string;
  status: string;
  myOption: string;
  betAmount: number;
  potentialWin: number;
  createdAt: string;
  isWinner?: boolean;
}

const MyBetsPage: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const [myBets, setMyBets] = useState<MyBet[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'all' | 'active' | 'completed'>('all');

  useEffect(() => {
    loadMyBets();
  }, [isAuthenticated]);

  const loadMyBets = async () => {
    try {
      setLoading(true);
      
      if (!isAuthenticated || !user) {
        setMyBets([]);
        return;
      }

      // 模拟数据
      const mockBets: MyBet[] = [
        {
          id: 'my-bet-1',
          title: '2024年世界杯冠军预测',
          description: '预测2024年世界杯的冠军队伍',
          status: 'settled',
          myOption: '巴西',
          betAmount: 500,
          potentialWin: 1200,
          createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          isWinner: true
        },
        {
          id: 'my-bet-2',
          title: 'Bitcoin价格预测',
          description: '预测比特币在月底的价格走势',
          status: 'judging',
          myOption: '上涨',
          betAmount: 200,
          potentialWin: 400,
          createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 'my-bet-3',
          title: '苹果股价预测',
          description: '预测苹果股价在下周的表现',
          status: 'open',
          myOption: '上涨',
          betAmount: 100,
          potentialWin: 180,
          createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
        }
      ];

      setMyBets(mockBets);
    } catch (error) {
      console.error('加载我的赌约失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'open': return 'bg-green-100 text-green-800';
      case 'judging': return 'bg-blue-100 text-blue-800';
      case 'settled': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string): string => {
    switch (status) {
      case 'open': return '进行中';
      case 'judging': return '裁定中';
      case 'settled': return '已结算';
      default: return '未知';
    }
  };

  const filteredBets = myBets.filter(bet => {
    switch (activeTab) {
      case 'active': return ['open', 'judging'].includes(bet.status);
      case 'completed': return bet.status === 'settled';
      default: return true;
    }
  });

  if (!isAuthenticated) {
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Breadcrumb
          items={[
            { label: '首页', href: '/', icon: '🏠' },
            { label: '社交赌注', href: '/social-bet', icon: '🎲' },
            { label: '我的赌约', icon: '📋' }
          ]}
        />
        
        <div className="text-center py-12">
          <div className="text-6xl mb-4">🔒</div>
          <h2 className="text-2xl font-bold text-label mb-4">需要登录</h2>
          <p className="text-secondary-label mb-6">
            查看我的赌约需要登录账户，请先完成登录。
          </p>
          <Button href="/auth/login">
            立即登录
          </Button>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-center items-center min-h-96">
          <Loading size="large" />
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* 面包屑导航 */}
      <div className="mb-6">
        <Breadcrumb
          items={[
            { label: '首页', href: '/', icon: '🏠' },
            { label: '社交赌注', href: '/social-bet', icon: '🎲' },
            { label: '我的赌约', icon: '📋' }
          ]}
        />
      </div>

      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-label mb-2">
          📋 我的赌约
        </h1>
        <p className="text-secondary-label">
          查看我参与的所有赌约记录和收益情况
        </p>
      </div>

      {/* 标签页 */}
      <div className="flex space-x-1 mb-6 bg-system-gray-6 rounded-lg p-1">
        {[
          { key: 'all', label: '全部', count: myBets.length },
          { key: 'active', label: '进行中', count: myBets.filter(b => ['open', 'judging'].includes(b.status)).length },
          { key: 'completed', label: '已完成', count: myBets.filter(b => b.status === 'settled').length }
        ].map((tab) => (
          <button
            key={tab.key}
            onClick={() => setActiveTab(tab.key as any)}
            className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md transition-all duration-200 ${
              activeTab === tab.key
                ? 'border-2 border-blue-500 text-blue-600 bg-blue-50/50 font-semibold'
                : 'text-secondary-label hover:text-label hover:bg-white/20 border-2 border-transparent'
            }`}
          >
            <span className="font-medium">{tab.label}</span>
            <Badge variant="secondary" size="small">{tab.count}</Badge>
          </button>
        ))}
      </div>

      {/* 赌约列表 */}
      <div className="space-y-4">
        {filteredBets.map((bet) => (
          <motion.div
            key={bet.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Card className="p-6 hover:shadow-lg transition-shadow">
              <div className="flex justify-between items-start mb-4">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h3 className="text-lg font-semibold text-label">
                      {bet.title}
                    </h3>
                    <Badge className={getStatusColor(bet.status)}>
                      {getStatusText(bet.status)}
                    </Badge>
                    {bet.isWinner && (
                      <Badge className="bg-green-100 text-green-800">
                        🏆 获胜
                      </Badge>
                    )}
                  </div>
                  <p className="text-sm text-secondary-label">
                    {bet.description}
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                <div>
                  <p className="text-xs text-secondary-label">我的选择</p>
                  <p className="font-semibold text-label">{bet.myOption}</p>
                </div>
                <div>
                  <p className="text-xs text-secondary-label">投注金额</p>
                  <p className="font-semibold text-label">{bet.betAmount}福气</p>
                </div>
                <div>
                  <p className="text-xs text-secondary-label">
                    {bet.status === 'settled' ? '实际收益' : '预期收益'}
                  </p>
                  <p className={`font-semibold ${bet.isWinner ? 'text-green-600' : 'text-label'}`}>
                    {bet.isWinner ? `+${bet.potentialWin - bet.betAmount}` : bet.potentialWin}福气
                  </p>
                </div>
                <div>
                  <p className="text-xs text-secondary-label">参与时间</p>
                  <p className="font-semibold text-label">
                    {new Date(bet.createdAt).toLocaleDateString()}
                  </p>
                </div>
              </div>

              <div className="flex justify-end">
                <Button
                  href={`/social-bet/${bet.id}`}
                  size="small"
                  variant="outline"
                >
                  查看详情
                </Button>
              </div>
            </Card>
          </motion.div>
        ))}
      </div>

      {filteredBets.length === 0 && (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">📋</div>
          <h3 className="text-xl font-semibold text-label mb-2">暂无赌约记录</h3>
          <p className="text-secondary-label mb-6">
            您还没有参与任何赌约，快去创建或参与一个吧！
          </p>
          <Button href="/social-bet">
            浏览赌约
          </Button>
        </div>
      )}
    </div>
  );
};

export default MyBetsPage;
