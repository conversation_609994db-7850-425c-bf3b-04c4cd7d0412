/**
 * 企业级排行榜组件
 * 匹配EnterpriseSocialBetDashboard的设计风格
 */

'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Loading from '@/components/ui/Loading';

interface LeaderboardUser {
  id: string;
  username: string;
  avatar?: string;
  rank: number;
  score: number;
  certificationLevel: string;
  reputationScore: number;
  totalEarnings: number;
  winRate: number;
  totalBets: number;
  consecutiveWins: number;
  isCurrentUser?: boolean;
}

interface LeaderboardStats {
  totalUsers: number;
  averageScore: number;
  topScore: number;
  myRank: number;
  myScore: number;
}

const EnterpriseLeaderboard: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [activeCategory, setActiveCategory] = useState<'fortune' | 'reputation' | 'winRate' | 'earnings'>('fortune');
  
  const [leaderboardData, setLeaderboardData] = useState<LeaderboardUser[]>([]);
  const [stats, setStats] = useState<LeaderboardStats>({
    totalUsers: 1247,
    averageScore: 856,
    topScore: 2450,
    myRank: 156,
    myScore: 1250
  });

  useEffect(() => {
    loadLeaderboardData();
  }, [activeCategory]);

  const loadLeaderboardData = async () => {
    try {
      setLoading(true);
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 800));
      
      const mockData: LeaderboardUser[] = [
        {
          id: '1',
          username: 'FortuneKing',
          rank: 1,
          score: 2450,
          certificationLevel: 'X5',
          reputationScore: 980,
          totalEarnings: 125000,
          winRate: 89.5,
          totalBets: 156,
          consecutiveWins: 12
        },
        {
          id: '2',
          username: 'BetMaster',
          rank: 2,
          score: 2180,
          certificationLevel: 'X4',
          reputationScore: 920,
          totalEarnings: 98000,
          winRate: 85.2,
          totalBets: 142,
          consecutiveWins: 8
        },
        {
          id: '3',
          username: 'LuckyGamer',
          rank: 3,
          score: 1950,
          certificationLevel: 'X4',
          reputationScore: 875,
          totalEarnings: 87500,
          winRate: 82.1,
          totalBets: 128,
          consecutiveWins: 5
        },
        {
          id: user?.id || '4',
          username: user?.username || 'You',
          rank: 156,
          score: 1250,
          certificationLevel: 'X2',
          reputationScore: 125,
          totalEarnings: 3420,
          winRate: 68.5,
          totalBets: 23,
          consecutiveWins: 3,
          isCurrentUser: true
        }
      ];
      
      // 根据分类排序
      const sortedData = [...mockData].sort((a, b) => {
        switch (activeCategory) {
          case 'fortune':
            return b.score - a.score;
          case 'reputation':
            return b.reputationScore - a.reputationScore;
          case 'winRate':
            return b.winRate - a.winRate;
          case 'earnings':
            return b.totalEarnings - a.totalEarnings;
          default:
            return b.score - a.score;
        }
      });
      
      setLeaderboardData(sortedData);
    } catch (error) {
      console.error('加载排行榜失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const getCertificationInfo = (level: string) => {
    const certifications: Record<string, { name: string; color: string; icon: string }> = {
      X1: { name: '新手认证', color: 'from-gray-400 to-gray-600', icon: '🥉' },
      X2: { name: '进阶认证', color: 'from-blue-400 to-blue-600', icon: '🥈' },
      X3: { name: '专家认证', color: 'from-purple-400 to-purple-600', icon: '🏅' },
      X4: { name: '大师认证', color: 'from-yellow-400 to-yellow-600', icon: '👑' },
      X5: { name: '传奇认证', color: 'from-red-400 to-red-600', icon: '💎' }
    };
    return certifications[level] || certifications.X1;
  };

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return '🥇';
      case 2:
        return '🥈';
      case 3:
        return '🥉';
      default:
        return `#${rank}`;
    }
  };

  const getCategoryLabel = (category: string) => {
    const labels = {
      fortune: '福气排行',
      reputation: '信誉排行',
      winRate: '胜率排行',
      earnings: '收益排行'
    };
    return labels[category] || '福气排行';
  };

  const getCategoryValue = (user: LeaderboardUser, category: string) => {
    switch (category) {
      case 'fortune':
        return `${user.score.toLocaleString()} 福气`;
      case 'reputation':
        return `${user.reputationScore} 积分`;
      case 'winRate':
        return `${user.winRate}%`;
      case 'earnings':
        return `${user.totalEarnings.toLocaleString()} 福气`;
      default:
        return `${user.score.toLocaleString()} 福气`;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* 导航栏 */}
      <div className="bg-white/80 backdrop-blur-md border-b border-gray-200/50 sticky top-0 z-40">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.back()}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              <div className="flex items-center space-x-2">
                <span className="text-2xl">🏆</span>
                <h1 className="text-xl font-bold text-gray-900">排行榜</h1>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="hidden md:flex items-center space-x-6 text-sm text-gray-600">
                <span>首页</span>
                <span className="text-gray-400">/</span>
                <span>社交赌约</span>
                <span className="text-gray-400">/</span>
                <span className="text-blue-600 font-medium">排行榜</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 排行榜统计卡片 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <div className="bg-gradient-to-r from-yellow-500 via-orange-500 to-red-600 rounded-2xl p-8 text-white shadow-2xl relative overflow-hidden">
            <div className="absolute inset-0 bg-black/10"></div>
            <div className="absolute top-0 right-0 w-64 h-64 bg-white/5 rounded-full -translate-y-32 translate-x-32"></div>
            <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full translate-y-24 -translate-x-24"></div>
            
            <div className="relative z-10">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h2 className="text-2xl font-bold mb-2">🏆 排行榜统计</h2>
                  <p className="text-orange-100">查看社区顶尖玩家的表现</p>
                </div>
                <div className="text-6xl opacity-20">👑</div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                  <div className="text-3xl font-bold mb-1">{stats.totalUsers.toLocaleString()}</div>
                  <div className="text-orange-100 text-sm">总用户数</div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                  <div className="text-3xl font-bold mb-1">{stats.topScore.toLocaleString()}</div>
                  <div className="text-orange-100 text-sm">最高分数</div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                  <div className="text-3xl font-bold mb-1">#{stats.myRank}</div>
                  <div className="text-orange-100 text-sm">我的排名</div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                  <div className="text-3xl font-bold mb-1">{stats.myScore.toLocaleString()}</div>
                  <div className="text-orange-100 text-sm">我的分数</div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* 排行榜分类选择 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="mb-8"
        >
          <div className="bg-white rounded-xl shadow-sm border border-gray-200/50 p-2">
            <div className="flex flex-wrap gap-2">
              {[
                { key: 'fortune', label: '🍀 福气排行', icon: '🍀' },
                { key: 'reputation', label: '⭐ 信誉排行', icon: '⭐' },
                { key: 'winRate', label: '🎯 胜率排行', icon: '🎯' },
                { key: 'earnings', label: '💰 收益排行', icon: '💰' }
              ].map((category) => (
                <button
                  key={category.key}
                  onClick={() => setActiveCategory(category.key as any)}
                  className={`flex items-center space-x-2 py-3 px-4 rounded-lg transition-all duration-200 ${
                    activeCategory === category.key
                      ? 'bg-gradient-to-r from-yellow-500 to-orange-600 text-white shadow-md'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  <span>{category.icon}</span>
                  <span className="font-medium">{category.label}</span>
                </button>
              ))}
            </div>
          </div>
        </motion.div>

        {/* 排行榜列表 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              🏆 {getCategoryLabel(activeCategory)}
            </h2>
            <p className="text-gray-600">
              展示社区中表现最优秀的用户
            </p>
          </div>
          
          {loading ? (
            <div className="flex justify-center py-12">
              <Loading size="large" />
            </div>
          ) : (
            <div className="space-y-4">
              <AnimatePresence>
                {leaderboardData.map((user, index) => (
                  <motion.div
                    key={user.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                  >
                    <Card className={`p-6 bg-white shadow-sm border transition-all duration-300 hover:shadow-lg ${
                      user.isCurrentUser 
                        ? 'border-blue-300 bg-blue-50/50 ring-2 ring-blue-200' 
                        : 'border-gray-200/50'
                    }`}>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          {/* 排名 */}
                          <div className={`w-16 h-16 rounded-full flex items-center justify-center text-2xl font-bold ${
                            user.rank <= 3 
                              ? 'bg-gradient-to-r from-yellow-400 to-orange-500 text-white' 
                              : user.isCurrentUser
                              ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white'
                              : 'bg-gray-100 text-gray-600'
                          }`}>
                            {getRankIcon(user.rank)}
                          </div>
                          
                          {/* 用户信息 */}
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-2">
                              <h3 className={`text-lg font-bold ${user.isCurrentUser ? 'text-blue-900' : 'text-gray-900'}`}>
                                {user.username}
                                {user.isCurrentUser && <span className="ml-2 text-blue-600">(您)</span>}
                              </h3>
                              <div className={`px-2 py-1 rounded-full bg-gradient-to-r ${getCertificationInfo(user.certificationLevel).color} text-white text-xs font-medium flex items-center space-x-1`}>
                                <span>{getCertificationInfo(user.certificationLevel).icon}</span>
                                <span>{getCertificationInfo(user.certificationLevel).name}</span>
                              </div>
                            </div>
                            
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                              <div>
                                <span className="text-gray-500">总投注:</span>
                                <span className="ml-1 font-medium text-gray-900">{user.totalBets}</span>
                              </div>
                              <div>
                                <span className="text-gray-500">胜率:</span>
                                <span className="ml-1 font-medium text-gray-900">{user.winRate}%</span>
                              </div>
                              <div>
                                <span className="text-gray-500">连胜:</span>
                                <span className="ml-1 font-medium text-gray-900">{user.consecutiveWins}</span>
                              </div>
                              <div>
                                <span className="text-gray-500">信誉:</span>
                                <span className="ml-1 font-medium text-gray-900">{user.reputationScore}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                        
                        {/* 分数 */}
                        <div className="text-right">
                          <div className={`text-2xl font-bold ${user.isCurrentUser ? 'text-blue-600' : 'text-gray-900'}`}>
                            {getCategoryValue(user, activeCategory)}
                          </div>
                          <div className="text-sm text-gray-500">
                            排名 #{user.rank}
                          </div>
                        </div>
                      </div>
                    </Card>
                  </motion.div>
                ))}
              </AnimatePresence>
            </div>
          )}
        </motion.div>

        {/* 排行榜说明 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="mt-8"
        >
          <Card className="p-6 bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200/50">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <span className="mr-2">📋</span>
              排行榜说明
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-2 flex items-center">
                  <span className="w-3 h-3 bg-blue-500 rounded-full mr-2"></span>
                  排名计算规则
                </h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• 福气排行：基于当前福气余额</li>
                  <li>• 信誉排行：基于裁定准确率和参与度</li>
                  <li>• 胜率排行：基于投注胜率</li>
                  <li>• 收益排行：基于累计收益</li>
                </ul>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 mb-2 flex items-center">
                  <span className="w-3 h-3 bg-green-500 rounded-full mr-2"></span>
                  排行榜奖励
                </h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• 前3名：每周额外福气奖励</li>
                  <li>• 前10名：专属认证徽章</li>
                  <li>• 前50名：优先参与新功能测试</li>
                  <li>• 排名每日更新一次</li>
                </ul>
              </div>
            </div>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};

export default EnterpriseLeaderboard;
