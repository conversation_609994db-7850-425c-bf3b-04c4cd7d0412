'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, Loading, Breadcrumb, Badge, Button } from '@/components/ui';
import CertificationBadge from './CertificationBadge';

interface JudgmentRecord {
  id: string;
  betId: string;
  betTitle: string;
  round: number;
  voteOption: number;
  optionText: string;
  reputationScore: number;
  certificationLevel: string;
  votedAt: string;
  isCorrect: boolean | null;
  judge: {
    id: string;
    telegramId: string;
    username: string;
    totalJudgments: number;
    correctJudgments: number;
  };
  betInfo: {
    status: string;
    winningOption: number | null;
    totalPool: number;
  };
}

const JudgmentHistoryPage: React.FC = () => {
  const [judgmentRecords, setJudgmentRecords] = useState<JudgmentRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'all' | 'round1' | 'round2' | 'round3'>('all');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    loadJudgmentHistory();
  }, [activeTab, page]);

  const loadJudgmentHistory = async () => {
    try {
      setLoading(true);
      
      // 模拟API调用
      const mockRecords: JudgmentRecord[] = [
        {
          id: 'judgment-1',
          betId: 'bet-1',
          betTitle: '2024年世界杯冠军预测',
          round: 1,
          voteOption: 0,
          optionText: '巴西',
          reputationScore: 95,
          certificationLevel: 'X2',
          votedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          isCorrect: true,
          judge: {
            id: 'judge-1',
            telegramId: '@judge_master',
            username: '预测大师',
            totalJudgments: 45,
            correctJudgments: 38
          },
          betInfo: {
            status: 'settled',
            winningOption: 0,
            totalPool: 50000
          }
        },
        {
          id: 'judgment-2',
          betId: 'bet-1',
          betTitle: '2024年世界杯冠军预测',
          round: 1,
          voteOption: 0,
          optionText: '巴西',
          reputationScore: 88,
          certificationLevel: 'X1',
          votedAt: new Date(Date.now() - 90 * 60 * 1000).toISOString(),
          isCorrect: true,
          judge: {
            id: 'judge-2',
            telegramId: '@smart_investor',
            username: '智慧投资者',
            totalJudgments: 23,
            correctJudgments: 19
          },
          betInfo: {
            status: 'settled',
            winningOption: 0,
            totalPool: 50000
          }
        },
        {
          id: 'judgment-3',
          betId: 'bet-2',
          betTitle: 'Bitcoin价格预测',
          round: 2,
          voteOption: 1,
          optionText: '下跌',
          reputationScore: 92,
          certificationLevel: 'X3',
          votedAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
          isCorrect: null, // 还未确定结果
          judge: {
            id: 'judge-3',
            telegramId: '@crypto_expert',
            username: '加密专家',
            totalJudgments: 67,
            correctJudgments: 58
          },
          betInfo: {
            status: 'judging',
            winningOption: null,
            totalPool: 20000
          }
        }
      ];

      // 根据标签页过滤
      const filteredRecords = mockRecords.filter(record => {
        if (activeTab === 'all') return true;
        const roundNumber = parseInt(activeTab.replace('round', ''));
        return record.round === roundNumber;
      });

      setJudgmentRecords(filteredRecords);
      setTotalPages(1);
    } catch (error) {
      console.error('加载裁定历史失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const getAccuracyRate = (correct: number, total: number): number => {
    return total > 0 ? Math.round((correct / total) * 100) : 0;
  };

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'settled': return 'bg-green-100 text-green-800';
      case 'judging': return 'bg-blue-100 text-blue-800';
      case 'confirming': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string): string => {
    switch (status) {
      case 'settled': return '已结算';
      case 'judging': return '裁定中';
      case 'confirming': return '确认中';
      default: return '未知';
    }
  };

  const getRoundName = (round: number): string => {
    const names = { 1: '大众评审', 2: '专业评审', 3: '终审裁定' };
    return names[round] || `第${round}轮`;
  };

  if (loading) {
    return (
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-center items-center min-h-96">
          <Loading size="large" />
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* 面包屑导航 */}
      <div className="mb-6">
        <Breadcrumb
          items={[
            { label: '首页', href: '/', icon: '🏠' },
            { label: '社交赌注', href: '/social-bet', icon: '🎲' },
            { label: '裁定历史', icon: '📜' }
          ]}
        />
      </div>

      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-label mb-2">
          📜 裁定历史记录
        </h1>
        <p className="text-secondary-label">
          透明公开的DAO裁定过程，所有裁定信息完全可追溯
        </p>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-blue-600 mb-1">
            {judgmentRecords.length}
          </div>
          <div className="text-sm text-secondary-label">总裁定次数</div>
        </Card>
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-green-600 mb-1">
            {judgmentRecords.filter(r => r.isCorrect === true).length}
          </div>
          <div className="text-sm text-secondary-label">正确裁定</div>
        </Card>
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-purple-600 mb-1">
            {new Set(judgmentRecords.map(r => r.judge.id)).size}
          </div>
          <div className="text-sm text-secondary-label">参与裁判</div>
        </Card>
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-orange-600 mb-1">
            {judgmentRecords.filter(r => r.betInfo.status === 'settled').length}
          </div>
          <div className="text-sm text-secondary-label">已完成赌约</div>
        </Card>
      </div>

      {/* 标签页 */}
      <div className="flex space-x-1 mb-6 bg-system-gray-6 rounded-lg p-1">
        {[
          { key: 'all', label: '全部记录', icon: '📋' },
          { key: 'round1', label: '大众评审', icon: '👥' },
          { key: 'round2', label: '专业评审', icon: '🎓' },
          { key: 'round3', label: '终审裁定', icon: '⚖️' }
        ].map((tab) => (
          <button
            key={tab.key}
            onClick={() => setActiveTab(tab.key as any)}
            className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md transition-all duration-200 ${
              activeTab === tab.key
                ? 'border-2 border-blue-500 text-blue-600 bg-blue-50/50 font-semibold'
                : 'text-secondary-label hover:text-label hover:bg-white/20 border-2 border-transparent'
            }`}
          >
            <span>{tab.icon}</span>
            <span className="font-medium">{tab.label}</span>
          </button>
        ))}
      </div>

      {/* 裁定记录列表 */}
      <div className="space-y-4">
        {judgmentRecords.map((record) => (
          <motion.div
            key={record.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Card className="p-6 hover:shadow-lg transition-shadow">
              <div className="flex justify-between items-start mb-4">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h3 className="text-lg font-semibold text-label">
                      {record.betTitle}
                    </h3>
                    <Badge className={getStatusColor(record.betInfo.status)}>
                      {getStatusText(record.betInfo.status)}
                    </Badge>
                    <Badge className="bg-purple-100 text-purple-800">
                      {getRoundName(record.round)}
                    </Badge>
                    {record.isCorrect !== null && (
                      <Badge className={record.isCorrect ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                        {record.isCorrect ? '✅ 正确' : '❌ 错误'}
                      </Badge>
                    )}
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                    <div>
                      <p className="text-secondary-label">裁定选择</p>
                      <p className="font-semibold text-label">{record.optionText}</p>
                    </div>
                    <div>
                      <p className="text-secondary-label">投票时间</p>
                      <p className="font-semibold text-label">
                        {new Date(record.votedAt).toLocaleString()}
                      </p>
                    </div>
                    <div>
                      <p className="text-secondary-label">奖池规模</p>
                      <p className="font-semibold text-label">{record.betInfo.totalPool}福气</p>
                    </div>
                    <div>
                      <p className="text-secondary-label">最终结果</p>
                      <p className="font-semibold text-label">
                        {record.betInfo.winningOption !== null ? `选项${record.betInfo.winningOption + 1}获胜` : '待确定'}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* 裁判信息 */}
              <div className="border-t pt-4">
                <h4 className="font-semibold text-label mb-3">裁判信息</h4>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-white font-bold">
                      {record.judge.username.charAt(0)}
                    </div>
                    <div>
                      <div className="flex items-center space-x-2 mb-1">
                        <p className="font-semibold text-label">{record.judge.username}</p>
                        <CertificationBadge level={record.certificationLevel.replace('X', '')} size="small" />
                      </div>
                      <p className="text-sm text-secondary-label">
                        Telegram: {record.judge.telegramId}
                      </p>
                      <p className="text-sm text-secondary-label">
                        信誉分数: {record.reputationScore}分
                      </p>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <p className="text-sm text-secondary-label">裁定统计</p>
                    <p className="font-semibold text-label">
                      {record.judge.correctJudgments}/{record.judge.totalJudgments}次
                    </p>
                    <p className="text-sm text-secondary-label">
                      准确率: {getAccuracyRate(record.judge.correctJudgments, record.judge.totalJudgments)}%
                    </p>
                  </div>
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="flex justify-end mt-4 pt-4 border-t">
                <Button
                  href={`/social-bet/${record.betId}`}
                  size="small"
                  variant="outline"
                >
                  查看赌约详情
                </Button>
              </div>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* 空状态 */}
      {judgmentRecords.length === 0 && (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">📜</div>
          <h3 className="text-xl font-semibold text-label mb-2">暂无裁定记录</h3>
          <p className="text-secondary-label mb-6">
            当前筛选条件下没有找到裁定记录，请尝试其他筛选条件。
          </p>
          <Button href="/social-bet/judgment">
            参与裁定
          </Button>
        </div>
      )}

      {/* 分页 */}
      {totalPages > 1 && (
        <div className="flex justify-center mt-8">
          <div className="flex space-x-2">
            <Button
              onClick={() => setPage(Math.max(1, page - 1))}
              disabled={page === 1}
              variant="outline"
              size="small"
            >
              上一页
            </Button>
            <span className="flex items-center px-4 text-sm text-secondary-label">
              第 {page} 页，共 {totalPages} 页
            </span>
            <Button
              onClick={() => setPage(Math.min(totalPages, page + 1))}
              disabled={page === totalPages}
              variant="outline"
              size="small"
            >
              下一页
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default JudgmentHistoryPage;
