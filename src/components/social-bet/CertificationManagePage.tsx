'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Card, Breadcrumb, Button, Input } from '@/components/ui';
import CertificationBenefitsPanel from './CertificationBenefitsPanel';
import CertificationBadge from './CertificationBadge';
import { useAuth } from '@/contexts/AuthContext';

const CertificationManagePage: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const [walletAddress, setWalletAddress] = useState('');
  const [updating, setUpdating] = useState(false);

  // 认证等级配置
  const certificationLevels = [
    {
      level: 'X1',
      name: '基础认证',
      minHaox: 10,
      color: 'from-gray-400 to-gray-600',
      benefits: [
        '参与第一轮大众评审',
        '每日1次裁定机会',
        '基础福气奖励'
      ]
    },
    {
      level: 'X2',
      name: '进阶认证',
      minHaox: 10000,
      color: 'from-blue-400 to-blue-600',
      benefits: [
        '参与第一、二轮裁定',
        '每日5次裁定机会',
        '5%手续费折扣',
        '优先显示赌约'
      ]
    },
    {
      level: 'X3',
      name: '专业认证',
      minHaox: 1000000,
      color: 'from-purple-400 to-purple-600',
      benefits: [
        '参与第一、二轮裁定',
        '每日20次裁定机会',
        '10%手续费折扣',
        '专属认证标识',
        '高级统计数据'
      ]
    },
    {
      level: 'X4',
      name: '专家认证',
      minHaox: 10000000,
      color: 'from-orange-400 to-orange-600',
      benefits: [
        '参与第二、三轮专业裁定',
        '每日50次裁定机会',
        '15%手续费折扣',
        '终审裁定权限',
        '专属客服支持'
      ]
    },
    {
      level: 'X5',
      name: '大师认证',
      minHaox: 100000000,
      color: 'from-yellow-400 to-yellow-600',
      benefits: [
        '参与第二、三轮终审裁定',
        '无限制裁定机会',
        '20%手续费折扣',
        '最高裁定权限',
        '平台治理投票权',
        '专属VIP服务'
      ]
    }
  ];

  const formatHaoxAmount = (amount: number): string => {
    if (amount >= 1000000) {
      return `${(amount / 1000000).toFixed(0)}M`;
    } else if (amount >= 1000) {
      return `${(amount / 1000).toFixed(0)}K`;
    }
    return amount.toString();
  };

  const bindWallet = async () => {
    if (!walletAddress.trim()) {
      alert('请输入钱包地址');
      return;
    }

    try {
      setUpdating(true);
      
      const response = await fetch(`/api/certification/${user?.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          walletAddress: walletAddress.trim()
        })
      });

      const result = await response.json();

      if (result.success) {
        alert(`钱包绑定成功！认证等级：${result.data.newLevel}`);
        setWalletAddress('');
        // 刷新页面以更新认证信息
        window.location.reload();
      } else {
        alert(`绑定失败：${result.message}`);
      }
    } catch (error) {
      console.error('绑定钱包失败:', error);
      alert('绑定失败，请稍后重试');
    } finally {
      setUpdating(false);
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Breadcrumb
          items={[
            { label: '首页', href: '/', icon: '🏠' },
            { label: '社交赌注', href: '/social-bet', icon: '🎲' },
            { label: '认证等级', icon: '🏆' }
          ]}
        />
        
        <div className="text-center py-12">
          <div className="text-6xl mb-4">🔒</div>
          <h2 className="text-2xl font-bold text-label mb-4">需要登录</h2>
          <p className="text-secondary-label mb-6">
            管理认证等级需要登录账户，请先完成登录。
          </p>
          <Button href="/auth/login">
            立即登录
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* 面包屑导航 */}
      <div className="mb-6">
        <Breadcrumb
          items={[
            { label: '首页', href: '/', icon: '🏠' },
            { label: '社交赌注', href: '/social-bet', icon: '🎲' },
            { label: '认证等级', icon: '🏆' }
          ]}
        />
      </div>

      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-label mb-2">
          🏆 HAOX认证等级管理
        </h1>
        <p className="text-secondary-label">
          基于HAOX持币量的认证等级系统，享受更多平台权益
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* 左侧：当前认证信息 */}
        <div className="lg:col-span-1">
          <CertificationBenefitsPanel />
          
          {/* 钱包绑定 */}
          <Card className="p-6 mt-6">
            <h3 className="text-lg font-bold text-label mb-4">💼 钱包管理</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-label mb-2">
                  HAOX钱包地址
                </label>
                <Input
                  value={walletAddress}
                  onChange={(e) => setWalletAddress(e.target.value)}
                  placeholder="0x..."
                  className="w-full"
                />
              </div>
              <Button
                onClick={bindWallet}
                disabled={updating || !walletAddress.trim()}
                className="w-full"
              >
                {updating ? '绑定中...' : '🔗 绑定/更新钱包'}
              </Button>
            </div>
            
            <div className="mt-4 p-3 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-800">
                💡 绑定钱包后，系统将自动检测您的HAOX余额并更新认证等级
              </p>
            </div>
          </Card>
        </div>

        {/* 右侧：认证等级说明 */}
        <div className="lg:col-span-2">
          <Card className="p-6">
            <h2 className="text-xl font-bold text-label mb-6">📋 认证等级说明</h2>
            
            <div className="space-y-6">
              {certificationLevels.map((level, index) => (
                <motion.div
                  key={level.level}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  className="border rounded-lg p-6 hover:shadow-md transition-shadow"
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-4">
                      <CertificationBadge level={level.level.replace('X', '')} size="large" />
                      <div>
                        <h3 className="text-lg font-bold text-label">
                          {level.level} - {level.name}
                        </h3>
                        <p className="text-sm text-secondary-label">
                          最低要求: {formatHaoxAmount(level.minHaox)} HAOX
                        </p>
                      </div>
                    </div>
                    
                    <div className={`px-3 py-1 rounded-full bg-gradient-to-r ${level.color} text-white text-sm font-medium`}>
                      {level.level}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-semibold text-label mb-2">🎁 专属权益</h4>
                      <ul className="space-y-1">
                        {level.benefits.map((benefit, idx) => (
                          <li key={idx} className="flex items-center space-x-2 text-sm">
                            <span className="text-green-500">✓</span>
                            <span className="text-label">{benefit}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                    
                    <div>
                      <h4 className="font-semibold text-label mb-2">📊 等级要求</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-secondary-label">最低HAOX:</span>
                          <span className="font-medium text-label">
                            {formatHaoxAmount(level.minHaox)}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-secondary-label">手续费折扣:</span>
                          <span className="font-medium text-green-600">
                            {level.level === 'X1' ? '0%' : `${(index) * 5}%`}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </Card>

          {/* 认证说明 */}
          <Card className="p-6 mt-6">
            <h3 className="text-lg font-bold text-label mb-4">ℹ️ 认证说明</h3>
            <div className="space-y-3 text-sm text-secondary-label">
              <div className="flex items-start space-x-2">
                <span className="text-blue-500 mt-1">•</span>
                <span>认证等级基于您钱包中的HAOX代币余额自动计算</span>
              </div>
              <div className="flex items-start space-x-2">
                <span className="text-blue-500 mt-1">•</span>
                <span>系统每小时自动更新一次认证等级，您也可以手动更新</span>
              </div>
              <div className="flex items-start space-x-2">
                <span className="text-blue-500 mt-1">•</span>
                <span>更高的认证等级享受更多平台权益和手续费折扣</span>
              </div>
              <div className="flex items-start space-x-2">
                <span className="text-blue-500 mt-1">•</span>
                <span>X4和X5等级用户可参与终审裁定，获得更高奖励</span>
              </div>
              <div className="flex items-start space-x-2">
                <span className="text-blue-500 mt-1">•</span>
                <span>认证等级影响您的每日裁定次数限制</span>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default CertificationManagePage;
