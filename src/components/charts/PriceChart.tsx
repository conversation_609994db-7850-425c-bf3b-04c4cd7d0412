'use client';

import React, { useEffect, useRef, useState, memo } from 'react';
import { createChart, IChartApi, ISeriesApi, LineData, Time } from 'lightweight-charts';
import { Card } from '@/components/ui';
import { log } from '@/lib/logger';

interface PriceData {
  time: string;
  value: number;
}

interface PriceChartProps {
  symbol?: string;
  timeframe?: '1h' | '4h' | '1d' | '1w';
  height?: number;
  className?: string;
}

/**
 * 价格图表组件
 * 使用 lightweight-charts 显示 HAOX 代币的价格走势
 */
const PriceChart: React.FC<PriceChartProps> = memo(({
  symbol = 'HAOX',
  timeframe = '1d',
  height = 400,
  className,
}) => {
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<IChartApi | null>(null);
  const seriesRef = useRef<ISeriesApi<'Line'> | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [priceData, setPriceData] = useState<PriceData[]>([]);

  // 初始化图表
  useEffect(() => {
    if (!chartContainerRef.current) return;

    try {
      const chart = createChart(chartContainerRef.current, {
        width: chartContainerRef.current.clientWidth,
        height,
        layout: {
          background: { color: 'transparent' },
          textColor: '#333',
        },
        grid: {
          vertLines: { color: '#f0f0f0' },
          horzLines: { color: '#f0f0f0' },
        },
        crosshair: {
          mode: 1,
        },
        rightPriceScale: {
          borderColor: '#cccccc',
        },
        timeScale: {
          borderColor: '#cccccc',
          timeVisible: true,
          secondsVisible: false,
        },
      });

      const lineSeries = chart.addLineSeries({
        color: '#2563eb',
        lineWidth: 2,
        priceFormat: {
          type: 'price',
          precision: 6,
          minMove: 0.000001,
        },
      });

      chartRef.current = chart;
      seriesRef.current = lineSeries;

      // 响应式调整
      const handleResize = () => {
        if (chartContainerRef.current && chart) {
          chart.applyOptions({
            width: chartContainerRef.current.clientWidth,
          });
        }
      };

      window.addEventListener('resize', handleResize);

      return () => {
        window.removeEventListener('resize', handleResize);
        chart.remove();
      };
    } catch (error) {
      log.error('Failed to initialize chart', error);
      setError('图表初始化失败');
    }
  }, [height]);

  // 获取价格数据
  useEffect(() => {
    fetchPriceData();
  }, [symbol, timeframe]);

  // 更新图表数据
  useEffect(() => {
    if (seriesRef.current && priceData.length > 0) {
      const chartData: LineData[] = priceData.map(item => ({
        time: (new Date(item.time).getTime() / 1000) as Time,
        value: item.value,
      }));

      seriesRef.current.setData(chartData);
      setIsLoading(false);
    }
  }, [priceData]);

  const fetchPriceData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // 模拟价格数据（实际应该从 API 获取）
      const mockData = generateMockPriceData(timeframe);
      setPriceData(mockData);

      log.info('Price data fetched', { symbol, timeframe, dataPoints: mockData.length });
    } catch (error) {
      log.error('Failed to fetch price data', error, { symbol, timeframe });
      setError('获取价格数据失败');
      setIsLoading(false);
    }
  };

  const generateMockPriceData = (timeframe: string): PriceData[] => {
    const now = new Date();
    const data: PriceData[] = [];
    const basePrice = 0.001; // HAOX 基础价格
    let currentPrice = basePrice;

    // 根据时间框架生成不同数量的数据点
    const intervals = {
      '1h': { count: 60, step: 60 * 1000 }, // 60个点，每分钟一个
      '4h': { count: 96, step: 15 * 60 * 1000 }, // 96个点，每15分钟一个
      '1d': { count: 30, step: 24 * 60 * 60 * 1000 }, // 30个点，每天一个
      '1w': { count: 52, step: 7 * 24 * 60 * 60 * 1000 }, // 52个点，每周一个
    };

    const { count, step } = intervals[timeframe];

    for (let i = count; i >= 0; i--) {
      const time = new Date(now.getTime() - i * step);

      // 模拟价格波动
      const volatility = 0.05; // 5% 波动率
      const change = (Math.random() - 0.5) * 2 * volatility;
      currentPrice = Math.max(currentPrice * (1 + change), 0.0001);

      data.push({
        time: time.toISOString(),
        value: currentPrice,
      });
    }

    return data;
  };

  if (error) {
    return (
      <Card className={`p-6 ${className}`}>
        <div className="text-center text-red-500">
          <div className="w-16 h-16 bg-red-100 rounded-full mx-auto mb-4 flex items-center justify-center">
            ⚠️
          </div>
          <h3 className="text-lg font-medium mb-2">加载失败</h3>
          <p className="text-sm">{error}</p>
          <button
            onClick={fetchPriceData}
            className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            重试
          </button>
        </div>
      </Card>
    );
  }

  return (
    <Card className={`p-6 ${className}`}>
      <div className="mb-4 flex items-center justify-between">
        <h3 className="text-lg font-semibold">{symbol} 价格走势</h3>
        <div className="flex space-x-2">
          {(['1h', '4h', '1d', '1w'] as const).map((tf) => (
            <button
              key={tf}
              onClick={() => fetchPriceData()}
              className={`px-3 py-1 text-sm rounded ${
                timeframe === tf
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              {tf}
            </button>
          ))}
        </div>
      </div>

      <div className="relative">
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 z-10">
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
              <span className="text-sm text-gray-600">加载中...</span>
            </div>
          </div>
        )}

        <div
          ref={chartContainerRef}
          style={{ height: `${height}px` }}
          className="w-full"
        />
      </div>

      {priceData.length > 0 && (
        <div className="mt-4 grid grid-cols-3 gap-4 text-sm">
          <div>
            <span className="text-gray-500">当前价格</span>
            <div className="font-semibold">
              ${priceData[priceData.length - 1]?.value.toFixed(6)}
            </div>
          </div>
          <div>
            <span className="text-gray-500">24h 变化</span>
            <div className="font-semibold text-green-500">+2.34%</div>
          </div>
          <div>
            <span className="text-gray-500">成交量</span>
            <div className="font-semibold">$1,234,567</div>
          </div>
        </div>
      )}
    </Card>
  );
});

PriceChart.displayName = 'PriceChart';

export default PriceChart;
