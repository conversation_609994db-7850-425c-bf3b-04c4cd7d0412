'use client';

import React, { Suspense } from 'react';
import { motion } from 'framer-motion';
import { Icon } from '@/components/ui';
import { FeatureIcons } from '@/config/icons';

// 页面加载骨架屏组件
export const PageSkeleton: React.FC = () => {
  return (
    <div className="min-h-screen bg-system-background">
      {/* Header Skeleton */}
      <div className="h-16 bg-system-gray-6 animate-pulse" />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Title Skeleton */}
        <div className="text-center mb-8">
          <div className="h-8 bg-system-gray-6 rounded-lg w-64 mx-auto mb-4 animate-pulse" />
          <div className="h-4 bg-system-gray-6 rounded w-96 mx-auto animate-pulse" />
        </div>
        
        {/* Content Skeleton */}
        <div className="grid lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2 space-y-6">
            {[1, 2, 3].map((i) => (
              <div key={i} className="bg-white rounded-2xl p-6 shadow-apple-sm">
                <div className="h-6 bg-system-gray-6 rounded w-48 mb-4 animate-pulse" />
                <div className="space-y-3">
                  <div className="h-4 bg-system-gray-6 rounded animate-pulse" />
                  <div className="h-4 bg-system-gray-6 rounded w-3/4 animate-pulse" />
                  <div className="h-4 bg-system-gray-6 rounded w-1/2 animate-pulse" />
                </div>
              </div>
            ))}
          </div>
          
          <div className="space-y-6">
            {[1, 2].map((i) => (
              <div key={i} className="bg-white rounded-2xl p-6 shadow-apple-sm">
                <div className="h-6 bg-system-gray-6 rounded w-32 mb-4 animate-pulse" />
                <div className="h-20 bg-system-gray-6 rounded animate-pulse" />
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

// 加载指示器组件
export const LoadingSpinner: React.FC<{ size?: 'sm' | 'md' | 'lg' }> = ({ 
  size = 'md' 
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  };

  return (
    <div className="flex items-center justify-center">
      <motion.div
        animate={{ rotate: 360 }}
        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
        className={`${sizeClasses[size]} border-2 border-system-blue border-t-transparent rounded-full`}
      />
    </div>
  );
};

// 页面加载错误组件
export const PageError: React.FC<{ 
  error?: Error;
  retry?: () => void;
}> = ({ error, retry }) => {
  return (
    <div className="min-h-screen bg-system-background flex items-center justify-center">
      <div className="text-center max-w-md mx-auto px-4">
        <Icon icon={FeatureIcons.alertTriangle} size="3xl" color="error" className="mx-auto mb-4" />
        
        <h2 className="text-title-2 font-sf-pro font-bold text-label mb-2">
          页面加载失败
        </h2>
        
        <p className="text-body text-secondary-label mb-6">
          {error?.message || '页面加载时出现错误，请稍后重试'}
        </p>
        
        {retry && (
          <button
            onClick={retry}
            className="px-6 py-3 bg-system-blue text-white rounded-lg hover:bg-system-blue/90 transition-colors"
          >
            重新加载
          </button>
        )}
      </div>
    </div>
  );
};

// 通用页面加载器高阶组件
export const withPageLoader = <P extends object>(
  Component: React.ComponentType<P>,
  fallback?: React.ComponentType
) => {
  const WrappedComponent: React.FC<P> = (props) => {
    const FallbackComponent = fallback || PageSkeleton;
    
    return (
      <Suspense fallback={<FallbackComponent />}>
        <Component {...props} />
      </Suspense>
    );
  };
  
  WrappedComponent.displayName = `withPageLoader(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
};

// 动态导入页面的工具函数
export const createLazyPage = (
  importFn: () => Promise<{ default: React.ComponentType<any> }>,
  fallback?: React.ComponentType
) => {
  const LazyComponent = React.lazy(importFn);
  
  return withPageLoader(LazyComponent, fallback);
};
