'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Icon, Button } from '@/components/ui';
import { ActionIcons, NavigationIcons } from '@/config/icons';
import { LANGUAGE_CONFIG } from '@/types/whitepaper';
import { cn } from '@/lib/utils';

interface ReadingProgressProps {
  language: 'zh' | 'en';
  estimatedReadTime?: number;
  currentSection?: string;
  totalSections?: number;
  onBackToTop?: () => void;
  className?: string;
}

const ReadingProgress: React.FC<ReadingProgressProps> = ({
  language,
  estimatedReadTime = 30,
  currentSection,
  totalSections = 11,
  onBackToTop,
  className
}) => {
  const [scrollProgress, setScrollProgress] = useState(0);
  const [readingTime, setReadingTime] = useState(0);
  const [isVisible, setIsVisible] = useState(false);

  const t = LANGUAGE_CONFIG[language];

  useEffect(() => {
    let startTime = Date.now();
    
    const handleScroll = () => {
      const scrollTop = window.pageYOffset;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const progress = Math.min((scrollTop / docHeight) * 100, 100);
      
      setScrollProgress(progress);
      setIsVisible(scrollTop > 300); // 显示条件：滚动超过300px
      
      // 计算阅读时间
      const currentTime = Date.now();
      const timeSpent = Math.floor((currentTime - startTime) / 1000 / 60); // 转换为分钟
      setReadingTime(timeSpent);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    handleScroll();

    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleBackToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
    onBackToTop?.();
  };

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 20 }}
      className={cn(
        'fixed bottom-6 right-6 z-40',
        'lg:bottom-8 lg:right-8',
        className
      )}
    >
      {/* 主要进度指示器 */}
      <div className="bg-system-background/95 backdrop-blur-sm rounded-2xl border border-system-gray-4/30 shadow-apple-lg p-4">
        {/* 圆形进度指示器 */}
        <div className="relative w-16 h-16 mb-3">
          <svg className="w-16 h-16 transform -rotate-90" viewBox="0 0 64 64">
            {/* 背景圆环 */}
            <circle
              cx="32"
              cy="32"
              r="28"
              stroke="currentColor"
              strokeWidth="4"
              fill="none"
              className="text-system-gray-5"
            />
            {/* 进度圆环 */}
            <motion.circle
              cx="32"
              cy="32"
              r="28"
              stroke="currentColor"
              strokeWidth="4"
              fill="none"
              strokeLinecap="round"
              className="text-system-blue"
              initial={{ strokeDasharray: "0 175.93" }}
              animate={{ 
                strokeDasharray: `${(scrollProgress / 100) * 175.93} 175.93` 
              }}
              transition={{ duration: 0.3 }}
            />
          </svg>
          
          {/* 中心文字 */}
          <div className="absolute inset-0 flex items-center justify-center">
            <span className="text-xs font-sf-pro font-semibold text-label">
              {Math.round(scrollProgress)}%
            </span>
          </div>
        </div>

        {/* 阅读信息 */}
        <div className="text-center space-y-1">
          <div className="text-xs text-secondary-label">
            {t.readingTime}
          </div>
          <div className="text-xs font-sf-pro font-medium text-label">
            {readingTime} / {estimatedReadTime} {t.minutes}
          </div>
          {currentSection && (
            <div className="text-xs text-system-blue truncate max-w-20">
              {currentSection}
            </div>
          )}
        </div>

        {/* 返回顶部按钮 */}
        <motion.div
          className="mt-3"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <Button
            variant="outline"
            size="sm"
            onClick={handleBackToTop}
            className="w-full text-xs py-2"
          >
            <Icon icon={ActionIcons.arrowUp} size="xs" className="mr-1" />
            {t.backToTop}
          </Button>
        </motion.div>
      </div>

      {/* 浮动操作按钮 */}
      <motion.div
        className="mt-3 flex flex-col space-y-2"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3 }}
      >
        {/* 快速导航按钮 */}
        <motion.button
          onClick={() => {
            const tocElement = document.querySelector('[data-toc]');
            if (tocElement) {
              tocElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
          }}
          className="w-12 h-12 bg-system-blue/90 backdrop-blur-sm rounded-full flex items-center justify-center text-white shadow-apple-lg"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
        >
          <Icon icon={NavigationIcons.list} size="sm" />
        </motion.button>

        {/* 分享按钮 */}
        <motion.button
          onClick={() => {
            if (navigator.share) {
              navigator.share({
                title: 'HAOX Whitepaper',
                url: window.location.href
              });
            } else {
              navigator.clipboard.writeText(window.location.href);
            }
          }}
          className="w-12 h-12 bg-system-green/90 backdrop-blur-sm rounded-full flex items-center justify-center text-white shadow-apple-lg"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
        >
          <Icon icon={ActionIcons.share} size="sm" />
        </motion.button>
      </motion.div>
    </motion.div>
  );
};

export default ReadingProgress;
