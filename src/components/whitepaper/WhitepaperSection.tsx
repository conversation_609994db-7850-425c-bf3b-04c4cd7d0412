'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Card, Icon } from '@/components/ui';
import { WhitepaperSection as SectionType, SECTION_TYPES } from '@/types/whitepaper';
import { renderParagraph, renderListItem, detectContentType, renderCodeBlock, renderBlockquote } from '@/utils/markdownParser';
import { cn } from '@/lib/utils';

interface WhitepaperSectionProps {
  section: SectionType;
  index: number;
  isActive?: boolean;
  language: 'zh' | 'en';
  onSectionVisible?: (sectionId: string) => void;
}

const WhitepaperSection: React.FC<WhitepaperSectionProps> = ({
  section,
  index,
  isActive = false,
  language,
  onSectionVisible
}) => {
  const sectionType = SECTION_TYPES[section.type || 'default'];
  const sectionRef = React.useRef<HTMLDivElement>(null);

  // 监听章节可见性
  React.useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && onSectionVisible) {
          onSectionVisible(section.id);
        }
      },
      { threshold: 0.3, rootMargin: '-80px 0px -80px 0px' }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, [section.id, onSectionVisible]);

  // 渲染内容
  const renderContent = (content: string) => {
    const lines = content.split('\n');
    const elements: JSX.Element[] = [];
    let currentList: string[] = [];
    let currentCodeBlock: string[] = [];
    let isInCodeBlock = false;
    let codeLanguage = '';

    const flushList = () => {
      if (currentList.length > 0) {
        elements.push(
          <ul key={`list-${elements.length}`} className="space-y-2 my-4 ml-4">
            {currentList.map((item, idx) => (
              <li key={idx} dangerouslySetInnerHTML={{ 
                __html: renderListItem(item, false, idx) 
              }} />
            ))}
          </ul>
        );
        currentList = [];
      }
    };

    const flushCodeBlock = () => {
      if (currentCodeBlock.length > 0) {
        elements.push(
          <div key={`code-${elements.length}`} 
               dangerouslySetInnerHTML={{ 
                 __html: renderCodeBlock(currentCodeBlock.join('\n'), codeLanguage) 
               }} 
          />
        );
        currentCodeBlock = [];
        isInCodeBlock = false;
        codeLanguage = '';
      }
    };

    lines.forEach((line, lineIndex) => {
      const trimmed = line.trim();
      
      // 处理代码块
      if (trimmed.startsWith('```')) {
        if (isInCodeBlock) {
          flushCodeBlock();
        } else {
          flushList();
          isInCodeBlock = true;
          codeLanguage = trimmed.substring(3);
        }
        return;
      }

      if (isInCodeBlock) {
        currentCodeBlock.push(line);
        return;
      }

      const contentType = detectContentType(line);

      switch (contentType) {
        case 'empty':
          flushList();
          break;

        case 'separator':
          flushList();
          elements.push(
            <hr key={`hr-${elements.length}`} 
                className="border-system-gray-4 my-8" />
          );
          break;

        case 'header':
          flushList();
          const headerMatch = trimmed.match(/^(#{1,4})\s+(.+)$/);
          if (headerMatch) {
            const level = headerMatch[1].length;
            const title = headerMatch[2];
            const HeaderTag = `h${Math.min(level + 1, 6)}` as keyof JSX.IntrinsicElements;
            
            elements.push(
              <HeaderTag 
                key={`header-${elements.length}`}
                className={cn(
                  'font-sf-pro font-semibold text-label mt-8 mb-4',
                  level === 1 && 'text-2xl',
                  level === 2 && 'text-xl',
                  level === 3 && 'text-lg',
                  level === 4 && 'text-base'
                )}
                dangerouslySetInnerHTML={{ __html: renderParagraph(title) }}
              />
            );
          }
          break;

        case 'list':
          currentList.push(trimmed);
          break;

        case 'paragraph':
          flushList();
          
          // 检查是否是引用
          if (trimmed.startsWith('>')) {
            const quoteText = trimmed.substring(1).trim();
            elements.push(
              <div key={`quote-${elements.length}`}
                   dangerouslySetInnerHTML={{ __html: renderBlockquote(quoteText) }} />
            );
          } else if (trimmed) {
            elements.push(
              <p key={`p-${elements.length}`} 
                 className="text-body text-secondary-label leading-relaxed mb-4"
                 dangerouslySetInnerHTML={{ __html: renderParagraph(trimmed) }} />
            );
          }
          break;
      }
    });

    // 清理剩余内容
    flushList();
    flushCodeBlock();

    return elements;
  };

  return (
    <motion.div
      ref={sectionRef}
      id={`section-${section.id}`}
      data-section-id={section.id}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: index * 0.1 }}
      className="scroll-mt-20"
    >
      <Card className={cn(
        'transition-all duration-300',
        sectionType.bgColor,
        sectionType.borderColor,
        'border-l-4',
        isActive && 'shadow-apple-lg scale-[1.01]'
      )}>
        {/* 章节头部 */}
        <div className="flex items-start space-x-4 mb-6">
          {/* 章节图标 */}
          <div className={cn(
            'flex-shrink-0 w-12 h-12 rounded-xl flex items-center justify-center',
            sectionType.bgColor,
            'border',
            sectionType.borderColor
          )}>
            <span className="text-xl">{section.icon}</span>
          </div>

          {/* 章节标题 */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2 mb-2">
              {section.level === 1 && (
                <span className={cn(
                  'px-2 py-1 rounded-md text-xs font-sf-pro font-medium',
                  sectionType.bgColor,
                  `text-${sectionType.color}`
                )}>
                  {section.type === 'executive' && (language === 'zh' ? '执行摘要' : 'Executive Summary')}
                  {section.type === 'technical' && (language === 'zh' ? '技术细节' : 'Technical Details')}
                  {section.type === 'tokenomics' && (language === 'zh' ? '代币经济学' : 'Tokenomics')}
                  {section.type === 'roadmap' && (language === 'zh' ? '发展路线图' : 'Roadmap')}
                  {section.type === 'team' && (language === 'zh' ? '团队信息' : 'Team Info')}
                  {section.type === 'risk' && (language === 'zh' ? '风险披露' : 'Risk Disclosure')}
                  {section.type === 'appendix' && (language === 'zh' ? '附录' : 'Appendix')}
                  {section.type === 'default' && (language === 'zh' ? '章节' : 'Section')}
                </span>
              )}
            </div>
            
            <h2 className={cn(
              'font-sf-pro font-bold text-label leading-tight',
              section.level === 1 && 'text-3xl',
              section.level === 2 && 'text-2xl',
              section.level === 3 && 'text-xl',
              section.level >= 4 && 'text-lg'
            )}>
              {section.title}
            </h2>
          </div>
        </div>

        {/* 章节内容 */}
        <div className="prose prose-lg max-w-none">
          {renderContent(section.content)}
        </div>
      </Card>
    </motion.div>
  );
};

export default WhitepaperSection;
