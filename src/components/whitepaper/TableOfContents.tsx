'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, Icon, Button } from '@/components/ui';
import { NavigationIcons, ActionIcons } from '@/config/icons';
import { WhitepaperTOC, SECTION_TYPES, LANGUAGE_CONFIG } from '@/types/whitepaper';
import { cn } from '@/lib/utils';

interface TableOfContentsProps {
  toc: WhitepaperTOC[];
  activeSection: string;
  onSectionClick: (sectionId: string) => void;
  language: 'zh' | 'en';
  isSticky?: boolean;
  showProgress?: boolean;
  className?: string;
}

const TableOfContents: React.FC<TableOfContentsProps> = ({
  toc,
  activeSection,
  onSectionClick,
  language,
  isSticky = true,
  showProgress = true,
  className
}) => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [readingProgress, setReadingProgress] = useState(0);
  const [isVisible, setIsVisible] = useState(true);

  const t = LANGUAGE_CONFIG[language];

  // 计算阅读进度
  useEffect(() => {
    const calculateProgress = () => {
      const scrollTop = window.pageYOffset;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const progress = Math.min((scrollTop / docHeight) * 100, 100);
      setReadingProgress(progress);
    };

    const handleScroll = () => {
      calculateProgress();
      
      // 在移动端自动隐藏/显示TOC
      if (window.innerWidth < 1024) {
        const currentScrollY = window.pageYOffset;
        setIsVisible(currentScrollY < 100 || currentScrollY < window.lastScrollY);
        window.lastScrollY = currentScrollY;
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    calculateProgress();

    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // 平滑滚动到指定章节
  const handleSectionClick = (sectionId: string, anchor: string) => {
    // 更新活跃章节
    onSectionClick(sectionId);

    // 查找目标元素
    const element = document.getElementById(anchor);

    if (element) {
      const headerOffset = 100; // 考虑固定头部的高度
      const elementPosition = element.getBoundingClientRect().top;
      const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

      // 平滑滚动到目标位置
      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      });
    } else {
      // 如果找不到元素，尝试使用原生锚点跳转
      console.warn(`Element with id "${anchor}" not found`);

      // 尝试查找备用ID格式
      const fallbackElement = document.querySelector(`[data-section-id="${sectionId}"]`);

      if (fallbackElement) {
        fallbackElement.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
          inline: 'nearest'
        });
      }
    }

    // 在移动端点击后收起TOC
    if (window.innerWidth < 1024) {
      setIsCollapsed(true);
    }
  };

  // 渲染TOC项目
  const renderTOCItem = (item: WhitepaperTOC, index: number) => {
    const isActive = activeSection === item.id;
    const sectionType = item.type ? SECTION_TYPES[item.type as keyof typeof SECTION_TYPES] : SECTION_TYPES.default;
    
    return (
      <motion.div
        key={item.id}
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ delay: index * 0.05 }}
      >
        <button
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            handleSectionClick(item.id, item.anchor);
          }}
          className={cn(
            'w-full text-left px-3 py-2 rounded-lg transition-all duration-200 group',
            'hover:bg-system-gray-5 hover:scale-[1.02]',
            item.level === 1 && 'font-semibold text-sm',
            item.level === 2 && 'font-medium text-sm ml-2',
            item.level === 3 && 'text-sm ml-4',
            item.level === 4 && 'text-xs ml-6',
            isActive && `bg-${sectionType.color}/10 text-${sectionType.color} border-l-2 border-${sectionType.color}`,
            !isActive && 'text-secondary-label'
          )}
        >
          <div className="flex items-center space-x-2">
            {item.level === 1 && item.icon && (
              <span className="text-xs">{item.icon}</span>
            )}
            <span className="flex-1 truncate">{item.title}</span>
            {isActive && (
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                className="w-2 h-2 bg-current rounded-full"
              />
            )}
          </div>
        </button>

        {/* 子章节 */}
        {item.children && item.children.length > 0 && (
          <AnimatePresence>
            {(!isCollapsed || isActive) && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                className="overflow-hidden"
              >
                <div className="ml-2 mt-1 space-y-1">
                  {item.children.map((child, childIndex) => renderTOCItem(child, childIndex))}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        )}
      </motion.div>
    );
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ 
        opacity: isVisible ? 1 : 0.3, 
        y: 0,
        scale: isVisible ? 1 : 0.95
      }}
      className={cn(
        'transition-all duration-300',
        isSticky && 'sticky top-20',
        className
      )}
    >
      <Card className="bg-system-background/95 backdrop-blur-sm border-system-gray-4/30 shadow-apple-lg">
        {/* TOC 头部 */}
        <div className="flex items-center justify-between mb-4 pb-3 border-b border-system-gray-4/30">
          <div className="flex items-center space-x-2">
            <Icon icon={NavigationIcons.list} size="sm" className="text-system-blue" />
            <h3 className="text-body font-sf-pro font-semibold text-label">
              {t.tableOfContents}
            </h3>
          </div>
          
          <div className="flex items-center space-x-2">
            {/* 阅读进度 */}
            {showProgress && (
              <div className="flex items-center space-x-1">
                <div className="w-16 h-1 bg-system-gray-5 rounded-full overflow-hidden">
                  <motion.div
                    className="h-full bg-system-blue rounded-full"
                    initial={{ width: 0 }}
                    animate={{ width: `${readingProgress}%` }}
                    transition={{ duration: 0.3 }}
                  />
                </div>
                <span className="text-xs text-secondary-label">
                  {Math.round(readingProgress)}%
                </span>
              </div>
            )}
            
            {/* 折叠按钮 */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="p-1 lg:hidden"
            >
              <Icon 
                icon={isCollapsed ? NavigationIcons.chevronDown : NavigationIcons.chevronUp} 
                size="xs" 
              />
            </Button>
          </div>
        </div>

        {/* TOC 内容 */}
        <AnimatePresence>
          {(!isCollapsed || window.innerWidth >= 1024) && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              className="overflow-hidden"
            >
              <nav className="space-y-1 max-h-[60vh] overflow-y-auto scrollbar-thin scrollbar-thumb-system-gray-4 scrollbar-track-transparent">
                {toc.map((item, index) => renderTOCItem(item, index))}
              </nav>
            </motion.div>
          )}
        </AnimatePresence>

        {/* 底部操作 */}
        <div className="mt-4 pt-3 border-t border-system-gray-4/30">
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
            className="w-full text-xs"
          >
            <Icon icon={ActionIcons.arrowUp} size="xs" className="mr-1" />
            {t.backToTop}
          </Button>
        </div>
      </Card>
    </motion.div>
  );
};

export default TableOfContents;
