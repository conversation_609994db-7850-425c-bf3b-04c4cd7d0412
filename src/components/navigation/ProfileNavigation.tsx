'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { motion } from 'framer-motion';
import { Card, Icon } from '@/components/ui';
import {
  UserIcons,
  FinanceIcons,
  FeatureIcons,
  NavigationIcons,
  MediaIcons
} from '@/config/icons';
import { cn } from '@/lib/utils';

interface NavigationItem {
  href: string;
  label: string;
  icon: any;
  description: string;
  badge?: string;
}

const ProfileNavigation: React.FC = () => {
  const pathname = usePathname();

  const navigationItems: NavigationItem[] = [
    {
      href: '/profile',
      label: '个人中心',
      icon: UserIcons.user,
      description: '查看个人信息和钱包概览'
    },
    {
      href: '/profile/receive',
      label: '收款',
      icon: FinanceIcons.trendUp,
      description: '生成收款地址和二维码'
    },
    {
      href: '/profile/transactions',
      label: '交易记录',
      icon: FinanceIcons.activity,
      description: '查看所有交易历史'
    },
    {
      href: '/profile/history',
      label: '活动历史',
      icon: FeatureIcons.clock,
      description: '查看平台活动记录'
    },
    {
      href: '/whitepaper',
      label: '白皮书',
      icon: MediaIcons.file,
      description: '了解项目详细信息'
    }
  ];

  return (
    <Card className="p-6">
      <div className="flex items-center space-x-2 mb-6">
        <Icon icon={NavigationIcons.menu} size="md" className="text-system-blue" />
        <h3 className="text-lg font-semibold text-label">快速导航</h3>
      </div>
      
      <div className="space-y-2">
        {navigationItems.map((item, index) => {
          const isActive = pathname === item.href;
          
          return (
            <motion.div
              key={item.href}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Link
                href={item.href}
                className={cn(
                  'flex items-center space-x-3 p-3 rounded-lg transition-all duration-200',
                  'hover:bg-system-gray-5 hover:scale-[1.02]',
                  isActive 
                    ? 'bg-system-blue text-white shadow-lg' 
                    : 'text-secondary-label hover:text-label'
                )}
              >
                <div className={cn(
                  'w-10 h-10 rounded-lg flex items-center justify-center',
                  isActive 
                    ? 'bg-white/20' 
                    : 'bg-system-gray-6'
                )}>
                  <Icon 
                    icon={item.icon} 
                    size="md" 
                    className={isActive ? 'text-white' : 'text-system-blue'}
                  />
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <h4 className={cn(
                      'font-medium truncate',
                      isActive ? 'text-white' : 'text-label'
                    )}>
                      {item.label}
                    </h4>
                    {item.badge && (
                      <span className="px-2 py-1 text-xs bg-system-orange text-white rounded-full">
                        {item.badge}
                      </span>
                    )}
                  </div>
                  <p className={cn(
                    'text-sm truncate',
                    isActive ? 'text-white/80' : 'text-secondary-label'
                  )}>
                    {item.description}
                  </p>
                </div>
                
                <Icon 
                  icon={NavigationIcons.chevronRight} 
                  size="sm" 
                  className={cn(
                    'transition-transform',
                    isActive ? 'text-white' : 'text-secondary-label',
                    'group-hover:translate-x-1'
                  )}
                />
              </Link>
            </motion.div>
          );
        })}
      </div>
      
      <div className="mt-6 pt-6 border-t border-system-gray-4">
        <div className="flex items-center space-x-2 text-sm text-secondary-label">
          <Icon icon={FeatureIcons.info} size="sm" />
          <span>所有功能都已正常运行</span>
        </div>
      </div>
    </Card>
  );
};

export default ProfileNavigation;
