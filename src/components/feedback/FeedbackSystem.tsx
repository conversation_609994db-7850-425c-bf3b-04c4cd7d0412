'use client';

import React, { useState, useCallback, createContext, useContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button, Icon } from '@/components/ui';
import { ActionIcons, FeatureIcons, RewardIcons } from '@/config/icons';

// 反馈类型定义
export type FeedbackType = 'success' | 'error' | 'warning' | 'info' | 'loading';

export interface FeedbackMessage {
  id: string;
  type: FeedbackType;
  title: string;
  message: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
  persistent?: boolean;
}

// 反馈上下文
interface FeedbackContextType {
  messages: FeedbackMessage[];
  showFeedback: (feedback: Omit<FeedbackMessage, 'id'>) => string;
  hideFeedback: (id: string) => void;
  clearAll: () => void;
  showSuccess: (title: string, message: string, options?: Partial<FeedbackMessage>) => string;
  showError: (title: string, message: string, options?: Partial<FeedbackMessage>) => string;
  showWarning: (title: string, message: string, options?: Partial<FeedbackMessage>) => string;
  showInfo: (title: string, message: string, options?: Partial<FeedbackMessage>) => string;
  showLoading: (title: string, message: string, options?: Partial<FeedbackMessage>) => string;
}

const FeedbackContext = createContext<FeedbackContextType | null>(null);

// 反馈提供者组件
export const FeedbackProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [messages, setMessages] = useState<FeedbackMessage[]>([]);

  const showFeedback = useCallback((feedback: Omit<FeedbackMessage, 'id'>) => {
    const id = `feedback_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const newMessage: FeedbackMessage = {
      ...feedback,
      id,
      duration: feedback.duration ?? (feedback.type === 'loading' ? undefined : 5000),
    };

    setMessages(prev => [...prev, newMessage]);

    // 自动隐藏（除非是持久化或加载类型）
    if (newMessage.duration && !newMessage.persistent && newMessage.type !== 'loading') {
      setTimeout(() => {
        hideFeedback(id);
      }, newMessage.duration);
    }

    return id;
  }, []);

  const hideFeedback = useCallback((id: string) => {
    setMessages(prev => prev.filter(msg => msg.id !== id));
  }, []);

  const clearAll = useCallback(() => {
    setMessages([]);
  }, []);

  const showSuccess = useCallback((title: string, message: string, options?: Partial<FeedbackMessage>) => {
    return showFeedback({ ...options, type: 'success', title, message });
  }, [showFeedback]);

  const showError = useCallback((title: string, message: string, options?: Partial<FeedbackMessage>) => {
    return showFeedback({ ...options, type: 'error', title, message, duration: options?.duration ?? 8000 });
  }, [showFeedback]);

  const showWarning = useCallback((title: string, message: string, options?: Partial<FeedbackMessage>) => {
    return showFeedback({ ...options, type: 'warning', title, message });
  }, [showFeedback]);

  const showInfo = useCallback((title: string, message: string, options?: Partial<FeedbackMessage>) => {
    return showFeedback({ ...options, type: 'info', title, message });
  }, [showFeedback]);

  const showLoading = useCallback((title: string, message: string, options?: Partial<FeedbackMessage>) => {
    return showFeedback({ ...options, type: 'loading', title, message, persistent: true });
  }, [showFeedback]);

  const value: FeedbackContextType = {
    messages,
    showFeedback,
    hideFeedback,
    clearAll,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showLoading,
  };

  return (
    <FeedbackContext.Provider value={value}>
      {children}
      <FeedbackContainer />
    </FeedbackContext.Provider>
  );
};

// 反馈容器组件
const FeedbackContainer: React.FC = () => {
  const context = useContext(FeedbackContext);
  if (!context) return null;

  const { messages, hideFeedback } = context;

  return (
    <div className="fixed top-4 right-4 z-50 space-y-3 max-w-sm">
      <AnimatePresence>
        {messages.map((message) => (
          <FeedbackItem
            key={message.id}
            message={message}
            onClose={() => hideFeedback(message.id)}
          />
        ))}
      </AnimatePresence>
    </div>
  );
};

// 单个反馈项组件
const FeedbackItem: React.FC<{
  message: FeedbackMessage;
  onClose: () => void;
}> = ({ message, onClose }) => {
  const getConfig = () => {
    switch (message.type) {
      case 'success':
        return {
          icon: ActionIcons.checkCircle,
          bgColor: 'bg-system-green/10',
          borderColor: 'border-system-green/20',
          iconColor: 'text-system-green',
        };
      case 'error':
        return {
          icon: ActionIcons.alert,
          bgColor: 'bg-system-red/10',
          borderColor: 'border-system-red/20',
          iconColor: 'text-system-red',
        };
      case 'warning':
        return {
          icon: ActionIcons.warning,
          bgColor: 'bg-system-orange/10',
          borderColor: 'border-system-orange/20',
          iconColor: 'text-system-orange',
        };
      case 'info':
        return {
          icon: ActionIcons.info,
          bgColor: 'bg-system-blue/10',
          borderColor: 'border-system-blue/20',
          iconColor: 'text-system-blue',
        };
      case 'loading':
        return {
          icon: FeatureIcons.loader,
          bgColor: 'bg-system-gray-6',
          borderColor: 'border-system-gray-4',
          iconColor: 'text-system-blue',
        };
    }
  };

  const config = getConfig();

  return (
    <motion.div
      initial={{ opacity: 0, x: 300, scale: 0.3 }}
      animate={{ opacity: 1, x: 0, scale: 1 }}
      exit={{ opacity: 0, x: 300, scale: 0.5 }}
      transition={{ duration: 0.3, ease: 'easeOut' }}
      className={`
        p-4 rounded-2xl shadow-apple-xl backdrop-blur-md border
        ${config.bgColor} ${config.borderColor}
      `}
    >
      <div className="flex items-start space-x-3">
        <div className={`flex-shrink-0 ${config.iconColor}`}>
          {message.type === 'loading' ? (
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            >
              <Icon icon={config.icon} size="md" />
            </motion.div>
          ) : (
            <Icon icon={config.icon} size="md" />
          )}
        </div>

        <div className="flex-1 min-w-0">
          <h4 className="text-body font-sf-pro font-semibold text-label mb-1">
            {message.title}
          </h4>
          <p className="text-caption-1 text-secondary-label">
            {message.message}
          </p>

          {message.action && (
            <div className="mt-3">
              <Button
                size="sm"
                variant="outline"
                onClick={message.action.onClick}
                className="text-xs"
              >
                {message.action.label}
              </Button>
            </div>
          )}
        </div>

        {!message.persistent && (
          <button
            onClick={onClose}
            className="flex-shrink-0 p-1 hover:bg-black/10 rounded-lg transition-colors"
          >
            <Icon icon={ActionIcons.close} size="sm" className="text-secondary-label" />
          </button>
        )}
      </div>
    </motion.div>
  );
};

// 反馈 Hook
export const useFeedback = () => {
  const context = useContext(FeedbackContext);
  if (!context) {
    throw new Error('useFeedback must be used within a FeedbackProvider');
  }
  return context;
};

// 便捷的反馈 Hook
export const useNotification = () => {
  const feedback = useFeedback();

  return {
    success: (message: string, title = '成功') => 
      feedback.showSuccess(title, message),
    
    error: (message: string, title = '错误') => 
      feedback.showError(title, message),
    
    warning: (message: string, title = '警告') => 
      feedback.showWarning(title, message),
    
    info: (message: string, title = '提示') => 
      feedback.showInfo(title, message),
    
    loading: (message: string, title = '加载中') => 
      feedback.showLoading(title, message),
    
    hide: feedback.hideFeedback,
    clearAll: feedback.clearAll,
  };
};

// 操作反馈 Hook
export const useActionFeedback = () => {
  const notification = useNotification();

  const withFeedback = useCallback(async <T,>(
    asyncFn: () => Promise<T>,
    options: {
      loadingMessage?: string;
      successMessage?: string;
      errorMessage?: string;
      showSuccess?: boolean;
    } = {}
  ): Promise<T> => {
    const {
      loadingMessage = '处理中...',
      successMessage = '操作成功',
      errorMessage = '操作失败',
      showSuccess = true,
    } = options;

    const loadingId = notification.loading(loadingMessage);

    try {
      const result = await asyncFn();
      notification.hide(loadingId);
      
      if (showSuccess) {
        notification.success(successMessage);
      }
      
      return result;
    } catch (error) {
      notification.hide(loadingId);
      const message = error instanceof Error ? error.message : errorMessage;
      notification.error(message);
      throw error;
    }
  }, [notification]);

  return {
    withFeedback,
    ...notification,
  };
};
