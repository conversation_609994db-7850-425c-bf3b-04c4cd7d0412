'use client';

import React, { useState, useTransition } from 'react';
import { useLocale, useTranslations } from 'next-intl';
import { useRouter, usePathname } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { locales, localeNames, localeFlags, setStoredLocale, type Locale } from '@/lib/i18n/config';

interface LanguageSwitcherProps {
  className?: string;
  variant?: 'dropdown' | 'toggle' | 'select';
  showFlag?: boolean;
  showText?: boolean;
}

export const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({
  className = '',
  variant = 'select',
  showFlag = true,
  showText = true
}) => {
  const t = useTranslations('common');
  const locale = useLocale() as Locale;
  const router = useRouter();
  const pathname = usePathname();
  const [isPending, startTransition] = useTransition();
  const [isOpen, setIsOpen] = useState(false);

  const handleLanguageChange = (newLocale: Locale) => {
    if (newLocale === locale) return;

    startTransition(() => {
      // 保存语言偏好
      setStoredLocale(newLocale);

      // 构建新的路径
      const segments = pathname.split('/');

      // 如果当前路径包含语言代码，替换它
      if (locales.includes(segments[1] as Locale)) {
        segments[1] = newLocale;
      } else {
        // 如果没有语言代码，添加它
        segments.splice(1, 0, newLocale);
      }

      const newPath = segments.join('/');
      router.push(newPath);
    });

    setIsOpen(false);
  };

  // 简单的select版本（保持向后兼容）
  if (variant === 'select') {
    return (
      <div className={className}>
        <select
          value={locale}
          onChange={(e) => handleLanguageChange(e.target.value as Locale)}
          disabled={isPending}
          className="px-3 py-2 rounded-lg border border-system-gray-4 bg-system-background text-body focus:border-system-blue focus:outline-none"
        >
          {locales.map((loc) => (
            <option key={loc} value={loc}>
              {showFlag ? `${localeFlags[loc]} ` : ''}{localeNames[loc]}
            </option>
          ))}
        </select>
      </div>
    );
  }

  // 切换按钮版本
  if (variant === 'toggle') {
    const otherLocale = locale === 'zh' ? 'en' : 'zh';

    return (
      <button
        onClick={() => handleLanguageChange(otherLocale)}
        disabled={isPending}
        className={`
          flex items-center space-x-2 px-3 py-2 rounded-lg
          bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700
          transition-colors duration-200
          ${isPending ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
          ${className}
        `}
        title={`${t('language')}: ${localeNames[otherLocale]}`}
      >
        {showFlag && (
          <span className="text-lg">{localeFlags[otherLocale]}</span>
        )}
        {showText && (
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            {localeNames[otherLocale]}
          </span>
        )}
      </button>
    );
  }

  // 下拉菜单版本
  return (
    <div className={`relative ${className}`}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        disabled={isPending}
        className={`
          flex items-center space-x-2 px-3 py-2 rounded-lg
          bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700
          transition-colors duration-200
          ${isPending ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        `}
        title={`${t('language')}: ${localeNames[locale]}`}
      >
        {showFlag && (
          <span className="text-lg">{localeFlags[locale]}</span>
        )}
        {showText && (
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            {localeNames[locale]}
          </span>
        )}
        <svg
          className={`w-4 h-4 text-gray-500 transition-transform duration-200 ${
            isOpen ? 'rotate-180' : ''
          }`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      <AnimatePresence>
        {isOpen && (
          <>
            <div
              className="fixed inset-0 z-10"
              onClick={() => setIsOpen(false)}
            />

            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}
              className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-20"
            >
              <div className="py-1">
                {locales.map((loc) => (
                  <button
                    key={loc}
                    onClick={() => handleLanguageChange(loc)}
                    disabled={isPending}
                    className={`
                      w-full flex items-center space-x-3 px-4 py-2 text-left
                      hover:bg-gray-100 dark:hover:bg-gray-700
                      transition-colors duration-200
                      ${loc === locale ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' : 'text-gray-700 dark:text-gray-300'}
                      ${isPending ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                    `}
                  >
                    <span className="text-lg">{localeFlags[loc]}</span>
                    <span className="text-sm font-medium">{localeNames[loc]}</span>
                    {loc === locale && (
                      <svg className="w-4 h-4 ml-auto" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    )}
                  </button>
                ))}
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
};
