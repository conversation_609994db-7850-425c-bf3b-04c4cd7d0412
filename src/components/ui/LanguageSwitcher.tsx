/**
 * 语言切换组件
 */

import React from 'react';
import { useTranslation, type Language } from '@/lib/i18n';

interface LanguageSwitcherProps {
  className?: string;
}

export const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({ className }) => {
  const { language, changeLanguage } = useTranslation();

  return (
    <div className={className}>
      <select
        value={language}
        onChange={(e) => changeLanguage(e.target.value as Language)}
        className="px-3 py-2 rounded-lg border border-system-gray-4 bg-system-background text-body focus:border-system-blue focus:outline-none"
      >
        <option value="zh">中文</option>
        <option value="en">English</option>
      </select>
    </div>
  );
};
