'use client';

import React, { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';
import {
  buildOptimizedImageUrl,
  generateSrcSet,
  IMAGE_SIZE_PRESETS,
  ImageOptimizationConfig,
  defaultLazyLoader,
} from '@/lib/image-optimization';

interface OptimizedImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  quality?: 'low' | 'medium' | 'high' | 'lossless' | number;
  format?: 'webp' | 'avif' | 'jpeg' | 'png';
  fit?: 'cover' | 'contain' | 'fill' | 'inside' | 'outside';
  position?: 'center' | 'top' | 'bottom' | 'left' | 'right';
  lazy?: boolean;
  responsive?: boolean;
  sizes?: string;
  placeholder?: 'blur' | 'empty' | string;
  fallback?: string;
  onLoad?: () => void;
  onError?: () => void;
  className?: string;
}

/**
 * 优化的图片组件
 * 支持懒加载、响应式、格式优化等功能
 */
export function OptimizedImage({
  src,
  alt,
  width,
  height,
  quality = 'high',
  format,
  fit = 'cover',
  position = 'center',
  lazy = true,
  responsive = false,
  sizes,
  placeholder = 'blur',
  fallback,
  onLoad,
  onError,
  className,
  ...props
}: OptimizedImageProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [currentSrc, setCurrentSrc] = useState<string>('');
  const imgRef = useRef<HTMLImageElement>(null);

  // 构建优化配置
  const optimizationConfig: ImageOptimizationConfig = {
    quality,
    format,
    width,
    height,
    fit,
    position,
  };

  // 生成优化后的图片URL
  const optimizedSrc = buildOptimizedImageUrl(src, optimizationConfig);

  // 生成响应式srcSet
  const srcSet = responsive 
    ? generateSrcSet(src, ['small', 'medium', 'large'])
    : undefined;

  // 生成占位符
  const placeholderSrc = React.useMemo(() => {
    if (placeholder === 'empty') return '';
    if (placeholder === 'blur') {
      return buildOptimizedImageUrl(src, {
        ...optimizationConfig,
        width: 20,
        height: 20,
        quality: 'low',
        blur: 10,
      });
    }
    if (typeof placeholder === 'string') {
      return placeholder;
    }
    return '';
  }, [src, placeholder, optimizationConfig]);

  // 处理图片加载
  const handleLoad = () => {
    setIsLoaded(true);
    setHasError(false);
    onLoad?.();
  };

  // 处理图片错误
  const handleError = () => {
    setHasError(true);
    if (fallback) {
      setCurrentSrc(fallback);
    }
    onError?.();
  };

  // 懒加载设置
  useEffect(() => {
    const img = imgRef.current;
    if (!img || !lazy) {
      setCurrentSrc(optimizedSrc);
      return;
    }

    // 设置数据属性用于懒加载
    img.dataset.src = optimizedSrc;
    img.dataset.format = format || 'image/jpeg';

    // 使用懒加载观察器
    defaultLazyLoader.observe(img);

    return () => {
      if (img) {
        defaultLazyLoader.unobserve(img);
      }
    };
  }, [optimizedSrc, lazy, format]);

  // 非懒加载时直接设置src
  useEffect(() => {
    if (!lazy) {
      setCurrentSrc(optimizedSrc);
    }
  }, [optimizedSrc, lazy]);

  return (
    <div className={cn('relative overflow-hidden', className)}>
      {/* 占位符 */}
      {!isLoaded && placeholderSrc && (
        <img
          src={placeholderSrc}
          alt=""
          className={cn(
            'absolute inset-0 w-full h-full object-cover transition-opacity duration-300',
            isLoaded ? 'opacity-0' : 'opacity-100'
          )}
          aria-hidden="true"
        />
      )}

      {/* 主图片 */}
      <img
        ref={imgRef}
        src={lazy ? undefined : currentSrc}
        srcSet={srcSet}
        sizes={sizes}
        alt={alt}
        width={width}
        height={height}
        onLoad={handleLoad}
        onError={handleError}
        className={cn(
          'w-full h-full object-cover transition-opacity duration-300',
          isLoaded ? 'opacity-100' : 'opacity-0',
          hasError && 'opacity-50'
        )}
        loading={lazy ? 'lazy' : 'eager'}
        decoding="async"
        {...props}
      />

      {/* 错误状态 */}
      {hasError && !fallback && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 text-gray-400">
          <svg
            className="w-8 h-8"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
            />
          </svg>
        </div>
      )}

      {/* 加载指示器 */}
      {!isLoaded && !hasError && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
        </div>
      )}
    </div>
  );
}

/**
 * 头像组件
 */
export function Avatar({
  src,
  alt,
  size = 40,
  className,
  fallback,
  ...props
}: {
  src?: string;
  alt: string;
  size?: number;
  className?: string;
  fallback?: string;
} & Omit<OptimizedImageProps, 'width' | 'height'>) {
  const fallbackSrc = fallback || `https://ui-avatars.com/api/?name=${encodeURIComponent(alt)}&size=${size}&background=random`;

  if (!src) {
    return (
      <div
        className={cn(
          'rounded-full bg-gray-200 flex items-center justify-center text-gray-500',
          className
        )}
        style={{ width: size, height: size }}
      >
        <span className="text-xs font-medium">
          {alt.charAt(0).toUpperCase()}
        </span>
      </div>
    );
  }

  return (
    <OptimizedImage
      src={src}
      alt={alt}
      width={size}
      height={size}
      fit="cover"
      quality="high"
      fallback={fallbackSrc}
      className={cn('rounded-full', className)}
      {...props}
    />
  );
}

/**
 * 响应式图片组件
 */
export function ResponsiveImage({
  src,
  alt,
  aspectRatio = '16/9',
  sizes = '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',
  className,
  ...props
}: {
  aspectRatio?: string;
  sizes?: string;
} & OptimizedImageProps) {
  return (
    <div
      className={cn('relative w-full', className)}
      style={{ aspectRatio }}
    >
      <OptimizedImage
        src={src}
        alt={alt}
        responsive={true}
        sizes={sizes}
        className="absolute inset-0 w-full h-full"
        {...props}
      />
    </div>
  );
}

/**
 * 图片画廊组件
 */
export function ImageGallery({
  images,
  columns = 3,
  gap = 4,
  className,
}: {
  images: Array<{
    src: string;
    alt: string;
    caption?: string;
  }>;
  columns?: number;
  gap?: number;
  className?: string;
}) {
  return (
    <div
      className={cn('grid', className)}
      style={{
        gridTemplateColumns: `repeat(${columns}, 1fr)`,
        gap: `${gap * 0.25}rem`,
      }}
    >
      {images.map((image, index) => (
        <div key={index} className="relative group">
          <ResponsiveImage
            src={image.src}
            alt={image.alt}
            aspectRatio="1/1"
            className="rounded-lg overflow-hidden"
          />
          {image.caption && (
            <div className="absolute inset-x-0 bottom-0 bg-black/50 text-white p-2 text-sm opacity-0 group-hover:opacity-100 transition-opacity">
              {image.caption}
            </div>
          )}
        </div>
      ))}
    </div>
  );
}

/**
 * 英雄图片组件
 */
export function HeroImage({
  src,
  alt,
  overlay = false,
  children,
  className,
  ...props
}: {
  overlay?: boolean;
  children?: React.ReactNode;
} & OptimizedImageProps) {
  return (
    <div className={cn('relative w-full h-screen', className)}>
      <OptimizedImage
        src={src}
        alt={alt}
        width={1920}
        height={1080}
        fit="cover"
        quality="high"
        className="absolute inset-0 w-full h-full"
        {...props}
      />
      
      {overlay && (
        <div className="absolute inset-0 bg-black/30" />
      )}
      
      {children && (
        <div className="absolute inset-0 flex items-center justify-center text-white">
          {children}
        </div>
      )}
    </div>
  );
}
