'use client';

import React, { forwardRef, SelectHTMLAttributes } from 'react';
import { cn } from '@/lib/utils';

export interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
}

export interface SelectProps extends Omit<SelectHTMLAttributes<HTMLSelectElement>, 'onChange'> {
  label?: string;
  error?: string;
  helperText?: string;
  variant?: 'default' | 'filled' | 'outlined';
  size?: 'small' | 'medium' | 'large';
  options: SelectOption[];
  placeholder?: string;
  onChange?: (value: string) => void;
}

const Select = forwardRef<HTMLSelectElement, SelectProps>(
  ({ 
    className, 
    label, 
    error, 
    helperText, 
    variant = 'default', 
    size = 'medium',
    options,
    placeholder,
    onChange,
    id,
    value,
    ...props 
  }, ref) => {
    const selectId = id || `select-${Math.random().toString(36).substr(2, 9)}`;

    const baseClasses = cn(
      // Base styles
      'w-full rounded-md border transition-colors duration-200',
      'focus:outline-none focus:ring-2 focus:ring-offset-2',
      'disabled:cursor-not-allowed disabled:opacity-50',
      'appearance-none bg-no-repeat bg-right',
      'cursor-pointer',
      
      // Size variants
      {
        'px-3 py-2 text-sm pr-8': size === 'small',
        'px-3 py-2 text-base pr-10': size === 'medium',
        'px-4 py-3 text-lg pr-12': size === 'large',
      },
      
      // Variant styles
      {
        // Default variant
        'border-system-gray-4 bg-white text-label': 
          variant === 'default' && !error,
        'focus:border-system-blue focus:ring-system-blue/20': 
          variant === 'default' && !error,
        
        // Filled variant
        'border-transparent bg-system-gray-6 text-label': 
          variant === 'filled' && !error,
        'focus:border-system-blue focus:ring-system-blue/20 focus:bg-white': 
          variant === 'filled' && !error,
        
        // Outlined variant
        'border-2 border-system-gray-4 bg-white text-label': 
          variant === 'outlined' && !error,
        'focus:border-system-blue focus:ring-system-blue/20': 
          variant === 'outlined' && !error,
        
        // Error state
        'border-red-500 text-red-900': error,
        'focus:border-red-500 focus:ring-red-500/20': error,
      },
      
      className
    );

    const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
      if (onChange) {
        onChange(e.target.value);
      }
      if (props.onChange) {
        props.onChange(e);
      }
    };

    return (
      <div className="w-full">
        {label && (
          <label 
            htmlFor={selectId}
            className={cn(
              'block text-sm font-medium mb-2',
              error ? 'text-red-700' : 'text-label'
            )}
          >
            {label}
          </label>
        )}
        
        <div className="relative">
          <select
            ref={ref}
            id={selectId}
            className={baseClasses}
            value={value}
            onChange={handleChange}
            {...props}
          >
            {placeholder && (
              <option value="" disabled>
                {placeholder}
              </option>
            )}
            {options.map((option) => (
              <option 
                key={option.value} 
                value={option.value}
                disabled={option.disabled}
              >
                {option.label}
              </option>
            ))}
          </select>
          
          {/* Custom dropdown arrow */}
          <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
            <svg 
              className={cn(
                'w-5 h-5',
                error ? 'text-red-500' : 'text-secondary-label'
              )}
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M19 9l-7 7-7-7" 
              />
            </svg>
          </div>
        </div>
        
        {(error || helperText) && (
          <div className="mt-1">
            {error && (
              <p className="text-sm text-red-600">
                {error}
              </p>
            )}
            {helperText && !error && (
              <p className="text-sm text-secondary-label">
                {helperText}
              </p>
            )}
          </div>
        )}
      </div>
    );
  }
);

Select.displayName = 'Select';

export default Select;
