'use client';

import React, { memo, useMemo, useCallback } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import type { ButtonProps } from '@/types';

// 预计算的基础样式类
const BASE_CLASSES = [
  'inline-flex items-center justify-center',
  'font-sf-pro font-semibold',
  'rounded-xl',
  'transition-all duration-200',
  'focus:outline-none focus:ring-2 focus:ring-system-blue focus:ring-offset-2',
  'disabled:opacity-50 disabled:cursor-not-allowed',
  'tap-target press-effect',
].join(' ');

const Button: React.FC<ButtonProps> = memo(({
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  children,
  onClick,
  className,
  ...props
}) => {

  // 使用 useMemo 缓存样式计算
  const classes = useMemo(() => {
    const variantClasses = {
      primary: 'bg-system-blue text-white hover:bg-blue-600 active:bg-blue-700 shadow-apple hover:shadow-apple-lg',
      secondary: 'bg-system-gray-6 text-label hover:bg-system-gray-5 active:bg-system-gray-4 border border-system-gray-4',
      outline: 'bg-transparent text-system-blue border-2 border-system-blue hover:bg-system-blue hover:text-white active:bg-blue-700',
      ghost: 'bg-transparent text-system-blue hover:bg-system-gray-6 active:bg-system-gray-5',
      destructive: 'bg-system-red text-white hover:bg-red-600 active:bg-red-700 shadow-apple hover:shadow-apple-lg',
    };

    const sizeClasses = {
      sm: 'px-3 py-2 text-sm',
      md: 'px-4 py-3 text-base',
      lg: 'px-6 py-4 text-lg',
    };

    return cn(
      BASE_CLASSES,
      variantClasses[variant],
      sizeClasses[size],
      className
    );
  }, [variant, size, className]);

  // 使用 useCallback 缓存点击处理函数
  const handleClick = useCallback(() => {
    if (!disabled && !loading && onClick) {
      onClick();
    }
  }, [disabled, loading, onClick]);

  return (
    <motion.button
      className={classes}
      onClick={handleClick}
      disabled={disabled || loading}
      whileTap={{ scale: 0.96 }}
      whileHover={{ scale: 1.02 }}
      transition={{ type: 'spring', stiffness: 400, damping: 17 }}
      type={props.type}
      id={props.id}
      aria-label={props['aria-label']}
    >
      {loading && (
        <motion.div
          className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.2 }}
        />
      )}
      {children}
    </motion.button>
  );
});

// 设置显示名称以便调试
Button.displayName = 'Button';

export default Button;
