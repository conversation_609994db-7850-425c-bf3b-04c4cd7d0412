'use client';

import React, { memo, useMemo } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import type { CardProps } from '@/types';

// 预计算的样式类，避免重复计算
const PADDING_CLASSES = {
  sm: 'p-4',
  md: 'p-6',
  lg: 'p-8',
} as const;

const BASE_CARD_CLASSES = [
  'bg-system-background',
  'border border-system-gray-4',
  'rounded-2xl',
  'shadow-apple',
  'transition-all duration-200',
  'hover:shadow-apple-lg'
].join(' ');

const Card: React.FC<CardProps> = memo(({
  title,
  children,
  className,
  padding = 'md',
  ...props
}) => {
  // 使用 useMemo 缓存计算结果
  const cardClasses = useMemo(() => cn(
    BASE_CARD_CLASSES,
    PADDING_CLASSES[padding],
    className
  ), [padding, className]);

  return (
    <motion.div
      className={cardClasses}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, ease: 'easeOut' }}
      whileHover={{ y: -2 }}
      {...props}
    >
      {title && (
        <motion.h3
          className="text-title-3 font-sf-pro font-semibold text-label mb-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.1, duration: 0.2 }}
        >
          {title}
        </motion.h3>
      )}
      
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.2, duration: 0.3 }}
      >
        {children}
      </motion.div>
    </motion.div>
  );
});

// 设置显示名称以便调试
Card.displayName = 'Card';

export default Card;
