'use client';

import React from 'react';
import Link from 'next/link';
import { cn } from '@/lib/utils';

export interface BreadcrumbItem {
  label: string;
  href?: string;
  icon?: string;
}

export interface BreadcrumbProps {
  items: BreadcrumbItem[];
  className?: string;
  separator?: string;
}

const Breadcrumb: React.FC<BreadcrumbProps> = ({
  items,
  className = '',
  separator = '/'
}) => {
  if (!items || items.length === 0) {
    return null;
  }

  return (
    <nav 
      className={cn('flex items-center space-x-2 text-sm', className)}
      aria-label="面包屑导航"
    >
      {items.map((item, index) => {
        const isLast = index === items.length - 1;
        
        return (
          <React.Fragment key={index}>
            {index > 0 && (
              <span className="text-secondary-label mx-2">
                {separator}
              </span>
            )}
            
            {isLast ? (
              <span className="text-label font-medium flex items-center space-x-1">
                {item.icon && <span>{item.icon}</span>}
                <span>{item.label}</span>
              </span>
            ) : (
              <Link
                href={item.href || '#'}
                className="text-secondary-label hover:text-label transition-colors duration-200 flex items-center space-x-1"
              >
                {item.icon && <span>{item.icon}</span>}
                <span>{item.label}</span>
              </Link>
            )}
          </React.Fragment>
        );
      })}
    </nav>
  );
};

export default Breadcrumb;
