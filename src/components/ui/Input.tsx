'use client';

import React, { useState, useCallback, useMemo, memo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import type { InputProps } from '@/types';

const Input: React.FC<InputProps> = memo(({
  label,
  placeholder,
  type = 'text',
  value,
  onChange,
  error,
  disabled = false,
  required = false,
  className,
  icon,
  rightIcon,
  helperText,
  minLength,
  maxLength,
  ...props
}) => {
  const [isFocused, setIsFocused] = useState(false);

  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    if (onChange) {
      onChange(e);
    }
  }, [onChange]);

  const handleFocus = useCallback(() => {
    setIsFocused(true);
  }, []);

  const handleBlur = useCallback(() => {
    setIsFocused(false);
  }, []);

  const inputClasses = useMemo(() => cn(
    'w-full px-4 py-3',
    'font-sf-pro text-gray-900',
    'bg-white border-2 rounded-xl',
    'transition-all duration-200',
    'focus:outline-none focus:ring-0',
    'placeholder:text-gray-400',
    {
      'border-gray-300 focus:border-blue-500 hover:border-gray-400': !error,
      'border-red-500 focus:border-red-500': error,
      'bg-gray-100 cursor-not-allowed text-gray-500': disabled,
      'border-blue-500 shadow-sm': isFocused && !error,
      'pl-10': icon,
      'pr-10': rightIcon
    },
    className
  ), [error, disabled, isFocused, className, icon, rightIcon]);

  const labelClasses = useMemo(() => cn(
    'block text-sm font-medium font-sf-pro mb-2',
    {
      'text-label': !error,
      'text-system-red': error,
    }
  ), [error]);

  return (
    <div className="w-full">
      {label && (
        <motion.label
          className={labelClasses}
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.2 }}
        >
          {label}
          {required && <span className="text-system-red ml-1">*</span>}
        </motion.label>
      )}
      
      <div className="relative">
        {/* Left icon */}
        {icon && (
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-secondary-label">
            {icon}
          </div>
        )}

        <motion.input
          type={type}
          value={value}
          onChange={handleChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={placeholder}
          disabled={disabled}
          required={required}
          className={cn(inputClasses, {
            'pl-10': icon,
            'pr-10': rightIcon
          })}
          whileFocus={{ scale: 1.01 }}
          transition={{ type: 'spring', stiffness: 300, damping: 20 }}
          id={props.id}
          name={props.name}
          autoComplete={props.autoComplete}
          maxLength={maxLength}
          minLength={minLength}
        />

        {/* Right icon */}
        {rightIcon && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-secondary-label">
            {rightIcon}
          </div>
        )}
        
        {/* Focus ring animation */}
        <AnimatePresence>
          {isFocused && !error && (
            <motion.div
              className="absolute inset-0 rounded-xl border-2 border-system-blue pointer-events-none"
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              transition={{ duration: 0.2 }}
            />
          )}
        </AnimatePresence>
      </div>

      {/* Error message */}
      <AnimatePresence>
        {error && (
          <motion.p
            className="mt-2 text-sm text-system-red font-sf-pro"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
          >
            {error}
          </motion.p>
        )}
      </AnimatePresence>

      {/* Helper text */}
      {helperText && !error && (
        <motion.p
          className="mt-2 text-sm text-secondary-label font-sf-pro"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.2 }}
        >
          {helperText}
        </motion.p>
      )}
    </div>
  );
});

Input.displayName = 'Input';

export default Input;
