'use client';

import React, { useState, useCallback, useMemo, memo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import type { InputProps } from '@/types';

const Input: React.FC<InputProps> = memo(({
  label,
  placeholder,
  type = 'text',
  value,
  onChange,
  error,
  disabled = false,
  required = false,
  className,
  ...props
}) => {
  const [isFocused, setIsFocused] = useState(false);

  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.value);
  }, [onChange]);

  const handleFocus = useCallback(() => {
    setIsFocused(true);
  }, []);

  const handleBlur = useCallback(() => {
    setIsFocused(false);
  }, []);

  const inputClasses = useMemo(() => cn(
    'w-full px-4 py-3',
    'font-sf-pro text-body',
    'bg-system-background border-2 rounded-xl',
    'transition-all duration-200',
    'focus:outline-none focus:ring-0',
    'placeholder:text-system-gray',
    {
      'border-system-gray-4 focus:border-system-blue': !error,
      'border-system-red focus:border-system-red': error,
      'bg-system-gray-6 cursor-not-allowed': disabled,
      'border-system-blue': isFocused && !error,
    },
    className
  ), [error, disabled, isFocused, className]);

  const labelClasses = useMemo(() => cn(
    'block text-sm font-medium font-sf-pro mb-2',
    {
      'text-label': !error,
      'text-system-red': error,
    }
  ), [error]);

  return (
    <div className="w-full">
      {label && (
        <motion.label
          className={labelClasses}
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.2 }}
        >
          {label}
          {required && <span className="text-system-red ml-1">*</span>}
        </motion.label>
      )}
      
      <div className="relative">
        <motion.input
          type={type}
          value={value}
          onChange={handleChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={placeholder}
          disabled={disabled}
          required={required}
          className={inputClasses}
          whileFocus={{ scale: 1.01 }}
          transition={{ type: 'spring', stiffness: 300, damping: 20 }}
          id={props.id}
          name={props.name}
          autoComplete={props.autoComplete}
          maxLength={props.maxLength}
        />
        
        {/* Focus ring animation */}
        <AnimatePresence>
          {isFocused && !error && (
            <motion.div
              className="absolute inset-0 rounded-xl border-2 border-system-blue pointer-events-none"
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              transition={{ duration: 0.2 }}
            />
          )}
        </AnimatePresence>
      </div>

      {/* Error message */}
      <AnimatePresence>
        {error && (
          <motion.p
            className="mt-2 text-sm text-system-red font-sf-pro"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
          >
            {error}
          </motion.p>
        )}
      </AnimatePresence>
    </div>
  );
});

Input.displayName = 'Input';

export default Input;
