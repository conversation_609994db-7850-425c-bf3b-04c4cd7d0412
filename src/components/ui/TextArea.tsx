'use client';

import React, { forwardRef, TextareaHTMLAttributes } from 'react';
import { cn } from '@/lib/utils';

export interface TextAreaProps extends TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string;
  error?: string;
  helperText?: string;
  variant?: 'default' | 'filled' | 'outlined';
  size?: 'small' | 'medium' | 'large';
}

const TextArea = forwardRef<HTMLTextAreaElement, TextAreaProps>(
  ({ 
    className, 
    label, 
    error, 
    helperText, 
    variant = 'default', 
    size = 'medium',
    id,
    ...props 
  }, ref) => {
    const textareaId = id || `textarea-${Math.random().toString(36).substr(2, 9)}`;

    const baseClasses = cn(
      // Base styles
      'w-full rounded-md border transition-colors duration-200',
      'focus:outline-none focus:ring-2 focus:ring-offset-2',
      'disabled:cursor-not-allowed disabled:opacity-50',
      'resize-vertical',
      
      // Size variants
      {
        'px-3 py-2 text-sm min-h-[80px]': size === 'small',
        'px-3 py-2 text-base min-h-[100px]': size === 'medium',
        'px-4 py-3 text-lg min-h-[120px]': size === 'large',
      },
      
      // Variant styles
      {
        // Default variant
        'border-system-gray-4 bg-white text-label placeholder:text-secondary-label': 
          variant === 'default' && !error,
        'focus:border-system-blue focus:ring-system-blue/20': 
          variant === 'default' && !error,
        
        // Filled variant
        'border-transparent bg-system-gray-6 text-label placeholder:text-secondary-label': 
          variant === 'filled' && !error,
        'focus:border-system-blue focus:ring-system-blue/20 focus:bg-white': 
          variant === 'filled' && !error,
        
        // Outlined variant
        'border-2 border-system-gray-4 bg-transparent text-label placeholder:text-secondary-label': 
          variant === 'outlined' && !error,
        'focus:border-system-blue focus:ring-system-blue/20': 
          variant === 'outlined' && !error,
        
        // Error state
        'border-red-500 text-red-900 placeholder:text-red-400': error,
        'focus:border-red-500 focus:ring-red-500/20': error,
      },
      
      className
    );

    return (
      <div className="w-full">
        {label && (
          <label 
            htmlFor={textareaId}
            className={cn(
              'block text-sm font-medium mb-2',
              error ? 'text-red-700' : 'text-label'
            )}
          >
            {label}
          </label>
        )}
        
        <textarea
          ref={ref}
          id={textareaId}
          className={baseClasses}
          {...props}
        />
        
        {(error || helperText) && (
          <div className="mt-1">
            {error && (
              <p className="text-sm text-red-600">
                {error}
              </p>
            )}
            {helperText && !error && (
              <p className="text-sm text-secondary-label">
                {helperText}
              </p>
            )}
          </div>
        )}
      </div>
    );
  }
);

TextArea.displayName = 'TextArea';

export default TextArea;
