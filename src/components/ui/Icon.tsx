/**
 * 统一的图标组件包装器
 * 提供一致的图标使用接口和样式
 */

import React from 'react';
import { LucideIcon } from 'lucide-react';
import { ExclamationCircleIcon } from '@heroicons/react/24/outline';
import { cn } from '@/lib/utils';
import { IconProps, getIconSize, getIconSizeClass, getIconColor } from '@/config/icons';

// 支持Heroicons和Lucide图标的联合类型
type IconComponent = LucideIcon | React.ComponentType<React.SVGProps<SVGSVGElement>>;

interface BaseIconProps extends IconProps {
  icon: IconComponent;
  'aria-label'?: string;
  'aria-hidden'?: boolean;
}

/**
 * 基础图标组件
 */
export const Icon: React.FC<BaseIconProps> = ({
  icon: IconComponent,
  size = 'md',
  color = 'default',
  className,
  'aria-label': ariaLabel,
  'aria-hidden': ariaHidden = !ariaLabel,
  ...props
}) => {
  const iconSizeClass = getIconSizeClass(size);
  const iconColor = getIconColor(color);

  // 错误处理：如果图标组件未定义，显示警告图标
  if (!IconComponent) {
    console.error('Icon component is undefined');
    return (
      <ExclamationCircleIcon
        className={cn('text-system-red', iconSizeClass, className)}
        aria-label={ariaLabel || 'Missing icon'}
        aria-hidden={ariaHidden}
        {...props}
      />
    );
  }

  try {
    // 统一使用Heroicons的方式渲染，因为我们已经全面切换到Heroicons
    return (
      <IconComponent
        className={cn(iconColor, iconSizeClass, className)}
        aria-label={ariaLabel}
        aria-hidden={ariaHidden}
        {...(props as any)}
      />
    );
  } catch (error) {
    console.error('Error rendering icon:', error);
    return (
      <ExclamationCircleIcon
        className={cn('text-system-red', iconSizeClass, className)}
        aria-label={ariaLabel || 'Error icon'}
        aria-hidden={ariaHidden}
        {...(props as any)}
      />
    );
  }
};

/**
 * 可点击的图标按钮组件
 */
interface IconButtonProps extends BaseIconProps {
  onClick?: () => void;
  disabled?: boolean;
  variant?: 'ghost' | 'outline' | 'solid';
  rounded?: boolean;
}

export const IconButton: React.FC<IconButtonProps> = ({
  icon: IconComponent,
  size = 'md',
  color = 'default',
  className,
  onClick,
  disabled = false,
  variant = 'ghost',
  rounded = true,
  'aria-label': ariaLabel,
  ...props
}) => {
  const iconSizeClass = getIconSizeClass(size);
  const iconColor = getIconColor(color);

  const baseClasses = cn(
    'inline-flex items-center justify-center transition-all duration-200',
    'focus:outline-none focus:ring-2 focus:ring-system-blue focus:ring-offset-2',
    'disabled:opacity-50 disabled:cursor-not-allowed',
    {
      'rounded-full': rounded,
      'rounded-lg': !rounded,
    }
  );

  const variantClasses = {
    ghost: 'hover:bg-system-gray-6 active:bg-system-gray-5',
    outline: 'border border-system-gray-4 hover:border-system-gray-3 hover:bg-system-gray-6',
    solid: 'bg-system-blue text-white hover:bg-system-blue/90 active:bg-system-blue/80',
  };

  const sizeClasses = {
    xs: 'p-1',
    sm: 'p-1.5',
    md: 'p-2',
    lg: 'p-2.5',
    xl: 'p-3',
    '2xl': 'p-4',
    '3xl': 'p-6',
  };

  const sizeKey = typeof size === 'number' ? 'md' : size;

  return (
    <button
      type="button"
      onClick={onClick}
      disabled={disabled}
      className={cn(
        baseClasses,
        variantClasses[variant],
        sizeClasses[sizeKey],
        className
      )}
      aria-label={ariaLabel}
      {...props}
    >
      <IconComponent
        className={cn(
          variant === 'solid' ? 'text-white' : iconColor,
          iconSizeClass
        )}
      />
    </button>
  );
};

/**
 * 带标签的图标组件
 */
interface IconWithLabelProps extends BaseIconProps {
  label: string;
  labelPosition?: 'right' | 'left' | 'top' | 'bottom';
  gap?: 'sm' | 'md' | 'lg';
}

export const IconWithLabel: React.FC<IconWithLabelProps> = ({
  icon: IconComponent,
  label,
  labelPosition = 'right',
  gap = 'md',
  size = 'md',
  color = 'default',
  className,
  ...props
}) => {
  const iconSizeClass = getIconSizeClass(size);
  const iconColor = getIconColor(color);

  const gapClasses = {
    sm: 'gap-1',
    md: 'gap-2',
    lg: 'gap-3',
  };

  const containerClasses = cn(
    'inline-flex items-center',
    gapClasses[gap],
    {
      'flex-row': labelPosition === 'right',
      'flex-row-reverse': labelPosition === 'left',
      'flex-col': labelPosition === 'bottom',
      'flex-col-reverse': labelPosition === 'top',
    },
    className
  );

  return (
    <div className={containerClasses}>
      <IconComponent
        className={cn(iconColor, iconSizeClass)}
        {...(props as any)}
      />
      <span className="text-sm font-sf-pro text-label">{label}</span>
    </div>
  );
};

/**
 * 状态图标组件（带颜色状态）
 */
interface StatusIconProps extends Omit<BaseIconProps, 'color'> {
  status: 'success' | 'warning' | 'error' | 'info' | 'pending';
  showBackground?: boolean;
}

export const StatusIcon: React.FC<StatusIconProps> = ({
  icon: IconComponent,
  status,
  showBackground = false,
  size = 'md',
  className,
  ...props
}) => {
  const iconSizeClass = getIconSizeClass(size);

  const statusColors = {
    success: 'text-system-green',
    warning: 'text-system-orange',
    error: 'text-system-red',
    info: 'text-system-blue',
    pending: 'text-system-gray-2',
  };

  const backgroundColors = {
    success: 'bg-system-green/10',
    warning: 'bg-system-orange/10',
    error: 'bg-system-red/10',
    info: 'bg-system-blue/10',
    pending: 'bg-system-gray-6',
  };

  const iconClasses = cn(
    statusColors[status],
    {
      'p-2 rounded-full': showBackground,
    },
    showBackground && backgroundColors[status],
    className
  );

  return (
    <div className={showBackground ? 'inline-flex' : 'inline'}>
      <IconComponent
        className={cn(iconClasses, iconSizeClass)}
        {...(props as any)}
      />
    </div>
  );
};

/**
 * 加载状态图标组件
 */
interface LoadingIconProps extends Omit<BaseIconProps, 'icon'> {
  spinning?: boolean;
}

export const LoadingIcon: React.FC<LoadingIconProps> = ({
  spinning = true,
  size = 'md',
  color = 'default',
  className,
  ...props
}) => {
  const iconSize = getIconSize(size);
  const iconColor = getIconColor(color);

  return (
    <div
      className={cn(
        'inline-flex items-center justify-center',
        {
          'animate-spin': spinning,
        },
        className
      )}
      {...props}
    >
      <svg
        width={iconSize}
        height={iconSize}
        viewBox="0 0 24 24"
        fill="none"
        className={iconColor}
      >
        <circle
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeDasharray="32"
          strokeDashoffset="32"
        >
          <animate
            attributeName="stroke-dasharray"
            dur="2s"
            values="0 32;16 16;0 32;0 32"
            repeatCount="indefinite"
          />
          <animate
            attributeName="stroke-dashoffset"
            dur="2s"
            values="0;-16;-32;-32"
            repeatCount="indefinite"
          />
        </circle>
      </svg>
    </div>
  );
};


