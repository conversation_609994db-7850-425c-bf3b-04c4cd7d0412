'use client';

import React, { memo, useMemo } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface LoadingProps {
  size?: 'sm' | 'md' | 'lg';
  color?: 'blue' | 'gray' | 'white';
  text?: string;
  className?: string;
}

const Loading: React.FC<LoadingProps> = memo(({
  size = 'md',
  color = 'blue',
  text,
  className,
}) => {
  const sizeClasses = useMemo(() => ({
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12',
  }), []);

  const colorClasses = useMemo(() => ({
    blue: 'border-system-blue',
    gray: 'border-system-gray',
    white: 'border-white',
  }), []);

  const spinnerClasses = useMemo(() => cn(
    'animate-spin rounded-full border-2 border-t-transparent',
    sizeClasses[size],
    colorClasses[color],
    className
  ), [size, color, className, sizeClasses, colorClasses]);

  const textSizeClasses = useMemo(() => ({
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
  }), []);

  const textClasses = useMemo(() => cn(
    'font-sf-pro text-secondary-label',
    textSizeClasses[size]
  ), [size, textSizeClasses]);

  return (
    <div className="flex flex-col items-center justify-center space-y-3">
      <motion.div
        className={spinnerClasses}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.2 }}
      />
      
      {text && (
        <motion.p
          className={textClasses}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1, duration: 0.2 }}
        >
          {text}
        </motion.p>
      )}
    </div>
  );
});

Loading.displayName = 'Loading';

// Full screen loading overlay
export const LoadingOverlay: React.FC<{ text?: string }> = memo(({ text }) => {
  return (
    <motion.div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.2 }}
    >
      <motion.div
        className="bg-system-background rounded-2xl p-8 shadow-apple-xl"
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        transition={{ duration: 0.3, ease: 'easeOut' }}
      >
        <Loading size="lg" text={text || '加载中...'} />
      </motion.div>
    </motion.div>
  );
});

LoadingOverlay.displayName = 'LoadingOverlay';

// Skeleton loading component
export const Skeleton: React.FC<{ className?: string }> = memo(({ className }) => {
  return (
    <motion.div
      className={cn(
        'bg-system-gray-5 rounded-lg',
        'animate-pulse',
        className
      )}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.2 }}
    />
  );
});

Skeleton.displayName = 'Skeleton';

// Skeleton text lines
export const SkeletonText: React.FC<{ lines?: number; className?: string }> = memo(({
  lines = 3,
  className,
}) => {
  return (
    <div className={cn('space-y-2', className)}>
      {Array.from({ length: lines }).map((_, index) => (
        <Skeleton
          key={index}
          className={cn(
            'h-4',
            index === lines - 1 ? 'w-3/4' : 'w-full'
          )}
        />
      ))}
    </div>
  );
});

SkeletonText.displayName = 'SkeletonText';

export default Loading;
