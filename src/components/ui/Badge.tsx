'use client';

import React, { memo, useMemo } from 'react';
import { cn } from '@/lib/utils';

interface BadgeProps {
  children: React.ReactNode;
  variant?: 'default' | 'primary' | 'success' | 'warning' | 'error' | 'info';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const Badge: React.FC<BadgeProps> = memo(({
  children,
  variant = 'default',
  size = 'md',
  className
}) => {
  const baseClasses = 'inline-flex items-center justify-center font-sf-pro font-medium rounded-full';

  const sizeClasses = useMemo(() => ({
    sm: 'px-2 py-0.5 text-xs',
    md: 'px-2.5 py-1 text-sm',
    lg: 'px-3 py-1.5 text-base'
  }), []);

  const variantClasses = useMemo(() => ({
    default: 'bg-system-gray-5 text-label',
    primary: 'bg-system-blue/10 text-system-blue border border-system-blue/20',
    success: 'bg-system-green/10 text-system-green border border-system-green/20',
    warning: 'bg-system-orange/10 text-system-orange border border-system-orange/20',
    error: 'bg-system-red/10 text-system-red border border-system-red/20',
    info: 'bg-system-blue/10 text-system-blue border border-system-blue/20'
  }), []);

  const badgeClasses = useMemo(() => cn(
    baseClasses,
    sizeClasses[size],
    variantClasses[variant],
    className
  ), [baseClasses, size, variant, className, sizeClasses, variantClasses]);

  return (
    <span className={badgeClasses}>
      {children}
    </span>
  );
});

Badge.displayName = 'Badge';

export default Badge;
