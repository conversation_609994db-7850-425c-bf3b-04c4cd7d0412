'use client';

import React, { createContext, useContext, useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Icon } from '@/components/ui';
import { NavigationIcons, ActionIcons } from '@/config/icons';
import { cn } from '@/lib/utils';

interface Toast {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
}

interface ToastContextType {
  toasts: Toast[];
  addToast: (toast: Omit<Toast, 'id'>) => void;
  removeToast: (id: string) => void;
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);

export const useToast = () => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

export const ToastProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [toasts, setToasts] = useState<Toast[]>([]);

  const addToast = useCallback((toast: Omit<Toast, 'id'>) => {
    // 使用浏览器安全的ID生成方法
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 5);
    const id = `toast_${timestamp}_${random}`;
    const newToast = { ...toast, id };
    
    setToasts(prev => [...prev, newToast]);

    // Auto remove toast after duration
    const duration = toast.duration || 5000;
    setTimeout(() => {
      removeToast(id);
    }, duration);
  }, []);

  const removeToast = useCallback((id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  }, []);

  return (
    <ToastContext.Provider value={{ toasts, addToast, removeToast }}>
      {children}
      <ToastContainer />
    </ToastContext.Provider>
  );
};

const ToastContainer: React.FC = () => {
  const { toasts, removeToast } = useToast();

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2">
      <AnimatePresence>
        {toasts.map(toast => (
          <ToastItem
            key={toast.id}
            toast={toast}
            onRemove={() => removeToast(toast.id)}
          />
        ))}
      </AnimatePresence>
    </div>
  );
};

interface ToastItemProps {
  toast: Toast;
  onRemove: () => void;
}

const ToastItem: React.FC<ToastItemProps> = ({ toast, onRemove }) => {
  const icons = {
    success: ActionIcons.checkCircle,
    error: ActionIcons.alert,
    warning: ActionIcons.warning,
    info: ActionIcons.info,
  };

  const colors = {
    success: {
      bg: 'bg-system-green',
      text: 'text-white',
      icon: 'text-white',
    },
    error: {
      bg: 'bg-system-red',
      text: 'text-white',
      icon: 'text-white',
    },
    warning: {
      bg: 'bg-system-orange',
      text: 'text-white',
      icon: 'text-white',
    },
    info: {
      bg: 'bg-system-blue',
      text: 'text-white',
      icon: 'text-white',
    },
  };

  const iconComponent = icons[toast.type];
  const colorScheme = colors[toast.type];

  return (
    <motion.div
      initial={{ opacity: 0, x: 300, scale: 0.3 }}
      animate={{ opacity: 1, x: 0, scale: 1 }}
      exit={{ opacity: 0, x: 300, scale: 0.5 }}
      transition={{ duration: 0.3, ease: 'easeOut' }}
      className={cn(
        'min-w-80 max-w-md p-4 rounded-2xl shadow-apple-xl',
        'backdrop-blur-md',
        colorScheme.bg
      )}
    >
      <div className="flex items-start space-x-3">
        <Icon icon={iconComponent} size="md" className={cn('mt-0.5 flex-shrink-0', colorScheme.icon)} />
        
        <div className="flex-1 min-w-0">
          <h4 className={cn('font-sf-pro font-semibold text-sm', colorScheme.text)}>
            {toast.title}
          </h4>
          {toast.message && (
            <p className={cn('mt-1 text-sm opacity-90', colorScheme.text)}>
              {toast.message}
            </p>
          )}
        </div>

        <button
          onClick={onRemove}
          className={cn(
            'flex-shrink-0 p-1 rounded-lg transition-colors',
            'hover:bg-white hover:bg-opacity-20',
            colorScheme.text
          )}
        >
          <Icon icon={NavigationIcons.close} size="sm" />
        </button>
      </div>
    </motion.div>
  );
};

// Helper functions for common toast types
export const toast = {
  success: (title: string, message?: string, duration?: number) => {
    // This will be used with the hook
    return { type: 'success' as const, title, message, duration };
  },
  error: (title: string, message?: string, duration?: number) => {
    return { type: 'error' as const, title, message, duration };
  },
  warning: (title: string, message?: string, duration?: number) => {
    return { type: 'warning' as const, title, message, duration };
  },
  info: (title: string, message?: string, duration?: number) => {
    return { type: 'info' as const, title, message, duration };
  },
};

export default ToastProvider;
