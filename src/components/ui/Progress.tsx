'use client';

import React, { memo, useMemo } from 'react';
import { cn } from '@/lib/utils';

interface ProgressProps {
  value: number; // 0-100
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  color?: 'primary' | 'success' | 'warning' | 'error';
  showLabel?: boolean;
  label?: string;
}

const Progress: React.FC<ProgressProps> = memo(({
  value,
  className,
  size = 'md',
  color = 'primary',
  showLabel = false,
  label
}) => {
  const clampedValue = useMemo(() => Math.min(100, Math.max(0, value)), [value]);

  const sizeClasses = useMemo(() => ({
    sm: 'h-1',
    md: 'h-2',
    lg: 'h-3'
  }), []);

  const colorClasses = useMemo(() => ({
    primary: 'bg-system-blue',
    success: 'bg-system-green',
    warning: 'bg-system-orange',
    error: 'bg-system-red'
  }), []);

  const progressBarClasses = useMemo(() => cn(
    'h-full rounded-full transition-all duration-300 ease-out',
    colorClasses[color]
  ), [color, colorClasses]);

  const containerClasses = useMemo(() => cn(
    'w-full bg-system-gray-4 rounded-full overflow-hidden',
    sizeClasses[size]
  ), [size, sizeClasses]);

  return (
    <div className={cn('w-full', className)}>
      {(showLabel || label) && (
        <div className="flex items-center justify-between mb-1">
          {label && (
            <span className="text-sm font-sf-pro font-medium text-label">
              {label}
            </span>
          )}
          {showLabel && (
            <span className="text-sm text-secondary-label">
              {Math.round(clampedValue)}%
            </span>
          )}
        </div>
      )}
      
      <div className={containerClasses}>
        <div
          className={progressBarClasses}
          style={{ width: `${clampedValue}%` }}
        />
      </div>
    </div>
  );
});

Progress.displayName = 'Progress';

export default Progress;
