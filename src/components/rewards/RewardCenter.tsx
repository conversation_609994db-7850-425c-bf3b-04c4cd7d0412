'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Button, Icon, Card } from '@/components/ui';
import { RewardIcons, ActionIcons, FinanceIcons, UserIcons, SocialIcons, FeatureIcons, NotificationIcons } from '@/config/icons';
import { useRewards, useDailySignIn } from '@/hooks/useRewards';
import { useBalanceFormatter } from '@/hooks/useWalletBalance';
import { cn } from '@/lib/utils';

interface RewardCenterProps {
  onClose?: () => void;
}

const RewardCenter: React.FC<RewardCenterProps> = ({ onClose }) => {
  const { 
    pendingRewards, 
    availableTasks, 
    rewardStats, 
    isLoading, 
    claimReward, 
    completeTask 
  } = useRewards();
  const { hasSignedToday, isSigningIn, signIn } = useDailySignIn();
  const { formatBalance } = useBalanceFormatter();
  const [activeTab, setActiveTab] = useState<'rewards' | 'tasks'>('rewards');

  /**
   * 处理奖励领取
   */
  const handleClaimReward = async (rewardId: string) => {
    await claimReward(rewardId);
  };

  /**
   * 处理任务完成
   */
  const handleCompleteTask = async (taskId: string) => {
    if (taskId === 'task_daily_signin') {
      await signIn();
    } else {
      await completeTask(taskId);
    }
  };

  /**
   * 获取奖励类型图标
   */
  const getRewardIcon = (type: string) => {
    switch (type) {
      case 'daily_signin':
        return RewardIcons.gift;
      case 'referral':
        return UserIcons.userAdd;
      case 'social_task':
        return RewardIcons.star;
      case 'trading_bonus':
        return FinanceIcons.trendingUp;
      default:
        return RewardIcons.gift;
    }
  };

  /**
   * 获取任务类型图标
   */
  const getTaskIcon = (type: string) => {
    switch (type) {
      case 'daily_signin':
        return FeatureIcons.calendar;
      case 'telegram_join':
        return SocialIcons.telegram;
      case 'twitter_follow':
        return SocialIcons.twitter;
      case 'discord_join':
        return NotificationIcons.message;
      case 'referral_invite':
        return UserIcons.userAdd;
      default:
        return RewardIcons.target;
    }
  };

  return (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-title-2 font-sf-pro font-bold text-label">
          奖励中心
        </h3>
        {onClose && (
          <Button variant="ghost" size="sm" onClick={onClose}>
            <Icon icon={ActionIcons.close} size="sm" />
          </Button>
        )}
      </div>

      {/* 奖励统计 */}
      {rewardStats && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-system-gray-6 rounded-xl p-4 text-center">
            <p className="text-caption-1 text-secondary-label mb-1">总收益</p>
            <p className="text-title-3 font-sf-pro font-bold text-label">
              {formatBalance(rewardStats.totalEarned)} HAOX
            </p>
          </div>
          <div className="bg-system-gray-6 rounded-xl p-4 text-center">
            <p className="text-caption-1 text-secondary-label mb-1">已领取</p>
            <p className="text-title-3 font-sf-pro font-bold text-system-green">
              {formatBalance(rewardStats.totalClaimed)} HAOX
            </p>
          </div>
          <div className="bg-system-gray-6 rounded-xl p-4 text-center">
            <p className="text-caption-1 text-secondary-label mb-1">待领取</p>
            <p className="text-title-3 font-sf-pro font-bold text-system-orange">
              {formatBalance(rewardStats.pendingRewards)} 福气
            </p>
          </div>
          <div className="bg-system-gray-6 rounded-xl p-4 text-center">
            <p className="text-caption-1 text-secondary-label mb-1">已完成任务</p>
            <p className="text-title-3 font-sf-pro font-bold text-label">
              {rewardStats.completedTasks}
            </p>
          </div>
        </div>
      )}

      {/* 标签页 */}
      <div className="flex space-x-1 mb-6 bg-system-gray-6 rounded-lg p-1">
        {[
          { id: 'rewards', label: '待领取奖励', icon: RewardIcons.gift },
          { id: 'tasks', label: '可用任务', icon: RewardIcons.target },
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={cn(
              'flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md transition-colors',
              activeTab === tab.id
                ? 'bg-system-background text-label shadow-sm'
                : 'text-secondary-label hover:text-label'
            )}
          >
            <Icon icon={tab.icon} size="sm" />
            <span className="text-body font-sf-pro font-medium">{tab.label}</span>
          </button>
        ))}
      </div>

      {/* 待领取奖励 */}
      {activeTab === 'rewards' && (
        <div className="space-y-3">
          {pendingRewards.length === 0 ? (
            <div className="text-center py-12">
              <Icon icon={RewardIcons.gift} size="2xl" color="muted" className="mx-auto mb-4" />
              <h4 className="text-title-3 font-sf-pro font-semibold text-label mb-2">
                暂无待领取奖励
              </h4>
              <p className="text-body text-secondary-label">
                完成任务即可获得奖励
              </p>
            </div>
          ) : (
            pendingRewards.map((reward) => (
              <motion.div
                key={reward.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-system-gray-6 rounded-xl p-4"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-system-green/20 rounded-full flex items-center justify-center">
                    <Icon icon={getRewardIcon(reward.type)} size="lg" color="success" />
                  </div>
                  
                  <div className="flex-1">
                    <h4 className="text-body font-sf-pro font-medium text-label mb-1">
                      {reward.description}
                    </h4>
                    <p className="text-caption-1 text-secondary-label">
                      {reward.createdAt.toLocaleDateString()}
                    </p>
                  </div>
                  
                  <div className="text-right">
                    <p className="text-title-3 font-sf-pro font-bold text-system-green mb-2">
                      +{formatBalance(reward.amount)} {reward.tokenType}
                    </p>
                    <Button
                      size="sm"
                      onClick={() => handleClaimReward(reward.id)}
                      disabled={isLoading}
                    >
                      {isLoading ? '领取中...' : '领取'}
                    </Button>
                  </div>
                </div>
              </motion.div>
            ))
          )}
        </div>
      )}

      {/* 可用任务 */}
      {activeTab === 'tasks' && (
        <div className="space-y-3">
          {availableTasks.length === 0 ? (
            <div className="text-center py-12">
              <Icon icon={RewardIcons.target} size="2xl" color="muted" className="mx-auto mb-4" />
              <h4 className="text-title-3 font-sf-pro font-semibold text-label mb-2">
                暂无可用任务
              </h4>
              <p className="text-body text-secondary-label">
                请稍后查看新任务
              </p>
            </div>
          ) : (
            availableTasks.map((task) => (
              <motion.div
                key={task.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className={cn(
                  'bg-system-gray-6 rounded-xl p-4',
                  task.isCompleted && 'border-2 border-system-green/20'
                )}
              >
                <div className="flex items-center space-x-3">
                  <div className={cn(
                    'w-12 h-12 rounded-full flex items-center justify-center',
                    task.isCompleted 
                      ? 'bg-system-green/20' 
                      : 'bg-system-blue/20'
                  )}>
                    <Icon 
                      icon={getTaskIcon(task.type)} 
                      size="lg" 
                      color={task.isCompleted ? 'success' : 'primary'} 
                    />
                  </div>
                  
                  <div className="flex-1">
                    <h4 className="text-body font-sf-pro font-medium text-label mb-1">
                      {task.title}
                    </h4>
                    <p className="text-caption-1 text-secondary-label mb-2">
                      {task.description}
                    </p>
                    <div className="flex items-center space-x-2">
                      <Icon icon={FinanceIcons.coins} size="sm" color="warning" />
                      <span className="text-caption-1 font-sf-pro font-medium text-system-orange">
                        +{formatBalance(task.rewardAmount)} {task.tokenType}
                      </span>
                    </div>
                  </div>
                  
                  <div>
                    {task.isCompleted ? (
                      <div className="flex items-center space-x-2 text-system-green">
                        <Icon icon={ActionIcons.check} size="sm" />
                        <span className="text-caption-1 font-sf-pro font-medium">已完成</span>
                      </div>
                    ) : task.isClaimable ? (
                      <Button
                        size="sm"
                        onClick={() => handleCompleteTask(task.id)}
                        disabled={isLoading || (task.id === 'task_daily_signin' && hasSignedToday)}
                      >
                        {task.id === 'task_daily_signin' && hasSignedToday 
                          ? '已签到' 
                          : isLoading || isSigningIn 
                          ? '处理中...' 
                          : '完成任务'
                        }
                      </Button>
                    ) : (
                      <Button size="sm" variant="secondary" disabled>
                        暂不可用
                      </Button>
                    )}
                  </div>
                </div>
              </motion.div>
            ))
          )}
        </div>
      )}
    </Card>
  );
};

export default RewardCenter;
