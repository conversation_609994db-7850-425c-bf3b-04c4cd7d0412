'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, Button, Icon, Badge, Progress } from '@/components/ui';
import { ActionIcons, FinanceIcons, RewardIcons } from '@/config/icons';
import { cn } from '@/lib/utils';

interface RewardStats {
  successful_invitations: number;
  total_rewards: number;
  claimable_rewards: number;
  milestone_5_claimed: boolean;
  milestone_10_claimed: boolean;
  leaderboard_reward: number;
  leaderboard_claimed: boolean;
}

interface RewardBreakdown {
  base_rewards: number;
  milestone_rewards: number;
  leaderboard_rewards: number;
}

interface NextMilestone {
  target: number;
  current: number;
  reward: number;
}

interface MyRewardsProps {
  userId?: string;
}

const MyRewards: React.FC<MyRewardsProps> = ({ userId }) => {
  const [rewardStats, setRewardStats] = useState<RewardStats | null>(null);
  const [rewardBreakdown, setRewardBreakdown] = useState<RewardBreakdown | null>(null);
  const [nextMilestone, setNextMilestone] = useState<NextMilestone | null>(null);
  const [leaderboardPosition, setLeaderboardPosition] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isClaiming, setIsClaiming] = useState(false);

  useEffect(() => {
    fetchRewardData();
  }, [userId]);

  const fetchRewardData = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/invitation/rewards?userId=${userId || 'demo'}`);
      const data = await response.json();
      
      if (data.success) {
        setRewardStats(data.data.user_stats);
        setRewardBreakdown(data.data.reward_breakdown);
        setNextMilestone(data.data.next_milestone);
        setLeaderboardPosition(data.data.leaderboard_position);
      }
    } catch (error) {
      console.error('Failed to fetch reward data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClaimRewards = async (rewardType: 'base' | 'milestone' | 'leaderboard') => {
    if (!rewardStats || rewardStats.claimable_rewards <= 0) return;
    
    setIsClaiming(true);
    try {
      const response = await fetch('/api/invitation/rewards', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: userId || 'demo',
          rewardType
        }),
      });
      
      const data = await response.json();
      
      if (data.success) {
        // 刷新数据
        await fetchRewardData();
        // 可以添加成功提示
      }
    } catch (error) {
      console.error('Failed to claim rewards:', error);
    } finally {
      setIsClaiming(false);
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card title="我的奖励">
          <div className="p-6 text-center">
            <p className="text-body text-secondary-label">正在加载奖励数据...</p>
          </div>
        </Card>
      </div>
    );
  }

  if (!rewardStats) {
    return (
      <div className="space-y-6">
        <Card title="我的奖励">
          <div className="p-6 text-center">
            <Icon icon={RewardIcons.gift} size="xl" color="muted" />
            <p className="text-body text-secondary-label mt-4">暂无奖励数据</p>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 奖励概览 */}
      <Card title="奖励概览">
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* 可领取奖励 */}
            <div className="bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-100 text-sm">可领取奖励</p>
                  <p className="text-3xl font-bold">
                    {rewardStats.claimable_rewards.toLocaleString()}
                  </p>
                  <p className="text-green-200 text-xs">福气</p>
                </div>
                <Icon icon={FinanceIcons.coins} size="xl" className="text-green-200" />
              </div>
              {rewardStats.claimable_rewards > 0 && (
                <Button
                  variant="secondary"
                  size="sm"
                  className="mt-4 w-full bg-white/20 hover:bg-white/30 text-white border-white/30"
                  onClick={() => handleClaimRewards('base')}
                  disabled={isClaiming}
                >
                  {isClaiming ? '领取中...' : '立即领取'}
                </Button>
              )}
            </div>

            {/* 总奖励 */}
            <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100 text-sm">累计奖励</p>
                  <p className="text-3xl font-bold">
                    {rewardStats.total_rewards.toLocaleString()}
                  </p>
                  <p className="text-blue-200 text-xs">HAOX</p>
                </div>
                <Icon icon={RewardIcons.trophy} size="xl" className="text-blue-200" />
              </div>
            </div>

            {/* 邀请数量 */}
            <div className="bg-gradient-to-r from-orange-500 to-red-600 rounded-xl p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-orange-100 text-sm">成功邀请</p>
                  <p className="text-3xl font-bold">{rewardStats.successful_invitations}</p>
                  <p className="text-orange-200 text-xs">人</p>
                </div>
                <Icon icon={ActionIcons.users} size="xl" className="text-orange-200" />
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* 奖励分解 */}
      <Card title="奖励详情">
        <div className="p-6">
          <div className="space-y-4">
            {/* 基础奖励 */}
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Icon icon={RewardIcons.gift} size="sm" color="primary" />
                </div>
                <div>
                  <p className="font-medium text-label">基础邀请奖励</p>
                  <p className="text-sm text-secondary-label">
                    {rewardStats.successful_invitations} × 1,000 福气
                  </p>
                </div>
              </div>
              <p className="font-bold text-lg text-system-green">
                +{rewardBreakdown?.base_rewards.toLocaleString()} 福气
              </p>
            </div>

            {/* 里程碑奖励 */}
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                  <Icon icon={RewardIcons.trophy} size="sm" color="secondary" />
                </div>
                <div>
                  <p className="font-medium text-label">里程碑奖励</p>
                  <div className="flex space-x-2">
                    <Badge variant={rewardStats.milestone_5_claimed ? 'success' : 'default'}>
                      5人 {rewardStats.milestone_5_claimed ? '✓' : ''}
                    </Badge>
                    <Badge variant={rewardStats.milestone_10_claimed ? 'success' : 'default'}>
                      10人 {rewardStats.milestone_10_claimed ? '✓' : ''}
                    </Badge>
                  </div>
                </div>
              </div>
              <p className="font-bold text-lg text-system-purple">
                +{rewardBreakdown?.milestone_rewards.toLocaleString()} 福气
              </p>
            </div>

            {/* 排行榜奖励 */}
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                  <Icon icon={RewardIcons.medal} size="sm" color="warning" />
                </div>
                <div>
                  <p className="font-medium text-label">排行榜奖励</p>
                  <p className="text-sm text-secondary-label">
                    {leaderboardPosition ? `排名第 ${leaderboardPosition} 位` : '未上榜'}
                  </p>
                </div>
              </div>
              <p className="font-bold text-lg text-system-orange">
                +{rewardBreakdown?.leaderboard_rewards.toLocaleString()} 福气
              </p>
            </div>
          </div>
        </div>
      </Card>

      {/* 下一个里程碑 */}
      {nextMilestone && (
        <Card title="下一个里程碑">
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div>
                <p className="font-medium text-label">邀请满 {nextMilestone.target} 人</p>
                <p className="text-sm text-secondary-label">
                  还需邀请 {nextMilestone.target - nextMilestone.current} 人
                </p>
              </div>
              <div className="text-right">
                <p className="font-bold text-lg text-system-green">
                  +{nextMilestone.reward.toLocaleString()} HAOX
                </p>
                <p className="text-sm text-secondary-label">里程碑奖励</p>
              </div>
            </div>
            <Progress 
              value={(nextMilestone.current / nextMilestone.target) * 100}
              className="h-3"
            />
            <div className="flex justify-between text-sm text-secondary-label mt-2">
              <span>{nextMilestone.current} 人</span>
              <span>{nextMilestone.target} 人</span>
            </div>
          </div>
        </Card>
      )}

      {/* 奖励说明 */}
      <Card title="奖励说明">
        <div className="p-6">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <Icon icon={ActionIcons.info} size="sm" color="primary" className="mt-0.5" />
              <div>
                <p className="text-sm font-medium text-label mb-2">重要提醒</p>
                <ul className="text-xs text-secondary-label space-y-1">
                  <li>• 奖励需要在预售结束后才能领取</li>
                  <li>• 被邀请用户必须实际参与预售购买（≥0.1 BNB）</li>
                  <li>• 排行榜奖励将在预售结束后统一发放</li>
                  <li>• 领取奖励时需要支付少量Gas费用</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default MyRewards;
