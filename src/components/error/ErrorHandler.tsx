'use client';

import React, { Component, ReactNode, ErrorInfo } from 'react';
import { motion } from 'framer-motion';
import { Button, Icon } from '@/components/ui';
import { ActionIcons, NavigationIcons, FeatureIcons } from '@/config/icons';

// 错误类型定义
export interface ErrorDetails {
  message: string;
  stack?: string;
  componentStack?: string;
  errorBoundary?: string;
  timestamp: number;
  userAgent: string;
  url: string;
  userId?: string;
}

// 错误边界的 Props
interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: React.ComponentType<{ error: Error; retry: () => void }>;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  level?: 'page' | 'section' | 'component';
  showDetails?: boolean;
}

// 错误边界的 State
interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string | null;
}

// 增强的错误边界组件
export class EnhancedErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  private retryCount = 0;
  private maxRetries = 3;

  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    const errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    return {
      hasError: true,
      error,
      errorId,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({ errorInfo });

    // 记录错误详情
    const errorDetails: ErrorDetails = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      errorBoundary: this.props.level || 'unknown',
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      url: window.location.href,
    };

    // 发送错误报告
    this.reportError(errorDetails);

    // 调用外部错误处理器
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  private async reportError(errorDetails: ErrorDetails) {
    try {
      // 这里可以发送到错误监控服务
      console.error('Error reported:', errorDetails);
      
      // 存储到本地存储以便后续分析
      const existingErrors = JSON.parse(localStorage.getItem('app_errors') || '[]');
      existingErrors.push(errorDetails);
      
      // 只保留最近的50个错误
      if (existingErrors.length > 50) {
        existingErrors.splice(0, existingErrors.length - 50);
      }
      
      localStorage.setItem('app_errors', JSON.stringify(existingErrors));
    } catch (e) {
      console.error('Failed to report error:', e);
    }
  }

  private handleRetry = () => {
    if (this.retryCount < this.maxRetries) {
      this.retryCount++;
      this.setState({
        hasError: false,
        error: null,
        errorInfo: null,
        errorId: null,
      });
    }
  };

  private handleReload = () => {
    window.location.reload();
  };

  private handleGoHome = () => {
    window.location.href = '/';
  };

  render() {
    if (this.state.hasError) {
      const { fallback: Fallback, level = 'component', showDetails = false } = this.props;
      const { error } = this.state;

      if (Fallback && error) {
        return <Fallback error={error} retry={this.handleRetry} />;
      }

      return (
        <ErrorDisplay
          error={error}
          level={level}
          showDetails={showDetails}
          canRetry={this.retryCount < this.maxRetries}
          onRetry={this.handleRetry}
          onReload={this.handleReload}
          onGoHome={this.handleGoHome}
        />
      );
    }

    return this.props.children;
  }
}

// 错误显示组件
interface ErrorDisplayProps {
  error: Error | null;
  level: 'page' | 'section' | 'component';
  showDetails: boolean;
  canRetry: boolean;
  onRetry: () => void;
  onReload: () => void;
  onGoHome: () => void;
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  error,
  level,
  showDetails,
  canRetry,
  onRetry,
  onReload,
  onGoHome,
}) => {
  const [showErrorDetails, setShowErrorDetails] = React.useState(false);

  const getErrorConfig = () => {
    switch (level) {
      case 'page':
        return {
          title: '页面加载失败',
          description: '页面遇到了一个错误，请尝试刷新页面或返回首页',
          containerClass: 'min-h-screen bg-system-background flex items-center justify-center',
          iconSize: '3xl' as const,
        };
      case 'section':
        return {
          title: '内容加载失败',
          description: '这个部分遇到了错误，请尝试重新加载',
          containerClass: 'min-h-64 bg-system-gray-6 rounded-2xl flex items-center justify-center',
          iconSize: '2xl' as const,
        };
      case 'component':
        return {
          title: '组件错误',
          description: '组件加载失败',
          containerClass: 'p-4 bg-system-red/10 border border-system-red/20 rounded-lg',
          iconSize: 'lg' as const,
        };
    }
  };

  const config = getErrorConfig();

  return (
    <div className={config.containerClass}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center max-w-md mx-auto px-4"
      >
        <Icon 
          icon={ActionIcons.alert} 
          size={config.iconSize} 
          color="error" 
          className="mx-auto mb-4" 
        />
        
        <h2 className="text-title-2 font-sf-pro font-bold text-label mb-2">
          {config.title}
        </h2>
        
        <p className="text-body text-secondary-label mb-6">
          {config.description}
        </p>

        {error && showDetails && (
          <div className="mb-6">
            <button
              onClick={() => setShowErrorDetails(!showErrorDetails)}
              className="text-sm text-system-blue hover:underline mb-2"
            >
              {showErrorDetails ? '隐藏' : '显示'}错误详情
            </button>
            
            {showErrorDetails && (
              <div className="text-left bg-system-gray-6 rounded-lg p-4 text-xs font-mono text-secondary-label max-h-32 overflow-y-auto">
                <div className="mb-2">
                  <strong>错误信息:</strong> {error.message}
                </div>
                {error.stack && (
                  <div>
                    <strong>堆栈跟踪:</strong>
                    <pre className="whitespace-pre-wrap mt-1">{error.stack}</pre>
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          {canRetry && (
            <Button
              onClick={onRetry}
              variant="primary"
              size="sm"
              className="flex items-center space-x-2"
            >
              <Icon icon={ActionIcons.refresh} size="sm" />
              <span>重试</span>
            </Button>
          )}
          
          {level === 'page' && (
            <>
              <Button
                onClick={onReload}
                variant="outline"
                size="sm"
                className="flex items-center space-x-2"
              >
                <Icon icon={FeatureIcons.refresh} size="sm" />
                <span>刷新页面</span>
              </Button>
              
              <Button
                onClick={onGoHome}
                variant="ghost"
                size="sm"
                className="flex items-center space-x-2"
              >
                <Icon icon={NavigationIcons.home} size="sm" />
                <span>返回首页</span>
              </Button>
            </>
          )}
        </div>
      </motion.div>
    </div>
  );
};

// 错误边界的便捷包装器
export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<ErrorBoundaryProps, 'children'>
) => {
  const WrappedComponent: React.FC<P> = (props) => (
    <EnhancedErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </EnhancedErrorBoundary>
  );
  
  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
};
