'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Card, Button, Icon } from '@/components/ui';
import { ActionIcons, FeatureIcons } from '@/config/icons';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo
    });

    // 调用自定义错误处理函数
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // 在生产环境中，可以将错误发送到监控服务
    if (process.env.NODE_ENV === 'production') {
      // 这里可以集成错误监控服务，如 Sentry
      // Sentry.captureException(error, { contexts: { react: { componentStack: errorInfo.componentStack } } });
    }
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  handleReload = () => {
    window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      // 如果提供了自定义 fallback，使用它
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // 默认错误 UI
      return (
        <div className="min-h-screen bg-system-background flex items-center justify-center p-4">
          <Card className="max-w-lg w-full text-center">
            <div className="mb-6">
              <Icon 
                icon={ActionIcons.alert} 
                size="3xl" 
                className="text-system-red mx-auto mb-4" 
              />
              <h1 className="text-2xl font-bold text-label mb-2">
                出现了一些问题
              </h1>
              <p className="text-secondary-label">
                抱歉，应用程序遇到了意外错误。我们已经记录了这个问题，请稍后重试。
              </p>
            </div>

            {process.env.NODE_ENV === 'development' && this.state.error && (
              <div className="mb-6 p-4 bg-system-red/10 border border-system-red/20 rounded-lg text-left">
                <h3 className="font-semibold text-label mb-2">开发模式错误信息:</h3>
                <pre className="text-sm text-secondary-label whitespace-pre-wrap break-words">
                  {this.state.error.toString()}
                  {this.state.errorInfo?.componentStack}
                </pre>
                <details className="mt-2">
                  <summary className="cursor-pointer text-sm font-medium text-label">
                    ▶ 查看堆栈跟踪
                  </summary>
                  <pre className="mt-2 text-xs text-secondary-label whitespace-pre-wrap break-words">
                    {this.state.error.stack}
                  </pre>
                </details>
              </div>
            )}

            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Button
                onClick={this.handleRetry}
                className="flex items-center space-x-2"
              >
                <Icon icon={ActionIcons.refresh} size="sm" />
                <span>重试</span>
              </Button>
              
              <Button
                variant="outline"
                onClick={this.handleReload}
                className="flex items-center space-x-2"
              >
                <Icon icon={FeatureIcons.home} size="sm" />
                <span>返回首页</span>
              </Button>
            </div>

            {process.env.NODE_ENV === 'production' && (
              <div className="mt-6 pt-6 border-t border-system-gray-4">
                <p className="text-xs text-secondary-label">
                  错误ID: {Date.now().toString(36)}
                </p>
              </div>
            )}
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
