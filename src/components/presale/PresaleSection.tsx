'use client';

import React, { useState, useEffect, memo, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button, Card, Icon } from '@/components/ui';
import {
  FeatureIcons,
  RewardIcons,
  ActionIcons
} from '@/config/icons';
import { useToast } from '@/components/ui';
import { usePresale } from '@/hooks/usePresale';
import TelegramChannelJoin from '@/components/telegram/TelegramChannelJoin';
import WhitepaperModal from '@/components/whitepaper/WhitepaperModal';
import { useTelegramAuth } from '@/hooks/useTelegramAuth';

// 现代化倒计时组件 - 增强FOMO效应
const CountdownTimer = memo<{ timeLeft: { days: number; hours: number; minutes: number; seconds: number } }>(
  ({ timeLeft }) => {
    const [isClient, setIsClient] = useState(false);

    useEffect(() => {
      setIsClient(true);
    }, []);

    const timeItems = useMemo(() => [
      { label: '天', value: timeLeft.days },
      { label: '时', value: timeLeft.hours },
      { label: '分', value: timeLeft.minutes },
      { label: '秒', value: timeLeft.seconds }
    ], [timeLeft]);

    // 服务器端渲染时显示占位符
    if (!isClient) {
      return (
        <div className="grid grid-cols-4 gap-3">
          {['天', '时', '分', '秒'].map((label) => (
            <div key={label} className="text-center">
              <div className="bg-gradient-to-br from-system-red via-system-orange to-system-yellow text-white rounded-xl p-3 mb-2 shadow-lg">
                <span className="text-title-1 font-sf-pro font-black">
                  --
                </span>
              </div>
              <span className="text-caption-1 text-secondary-label font-medium">
                {label}
              </span>
            </div>
          ))}
        </div>
      );
    }

    return (
      <div className="grid grid-cols-4 gap-3">
        {timeItems.map((item, index) => (
          <motion.div
            key={item.label}
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ 
              duration: 0.5, 
              delay: index * 0.1,
              type: "spring",
              stiffness: 100
            }}
            className="text-center"
          >
            <motion.div 
              className="bg-gradient-to-br from-system-red via-system-orange to-system-yellow text-white rounded-xl p-3 mb-2 shadow-lg border border-white/20"
              animate={{ 
                boxShadow: item.label === '秒' ? [
                  "0 10px 25px rgba(255, 59, 48, 0.3)",
                  "0 15px 35px rgba(255, 149, 0, 0.4)",
                  "0 10px 25px rgba(255, 59, 48, 0.3)"
                ] : "0 10px 25px rgba(255, 59, 48, 0.3)"
              }}
              transition={{ 
                duration: 1, 
                repeat: item.label === '秒' ? Infinity : 0,
                repeatType: "reverse"
              }}
            >
              <span className="text-title-1 font-sf-pro font-black tracking-tight">
                {item.value.toString().padStart(2, '0')}
              </span>
            </motion.div>
            <span className="text-caption-1 text-secondary-label font-medium">
              {item.label}
            </span>
          </motion.div>
        ))}
      </div>
    );
  }
);

CountdownTimer.displayName = 'CountdownTimer';

// 价格信息组件
const PriceInfo = memo<{
  currentPrice: number;
  nextPrice: number;
  currentRate: number;
  nextRate: number;
  currentStage: number;
  totalStages: number;
  priceIncreasePercentage: number;
}>(({ currentPrice, nextPrice, currentRate, nextRate, currentStage, totalStages, priceIncreasePercentage }) => {
  return (
    <div className="grid grid-cols-2 gap-4">
      <div className="text-center p-6 bg-system-green/10 rounded-2xl border border-system-green/20">
        <div className="text-caption-1 text-secondary-label mb-2">当前价格</div>
        <div className="text-title-2 font-sf-pro font-bold text-system-green">
          ${currentPrice.toFixed(6)}
        </div>
        <div className="text-caption-1 text-system-green mt-1">
          每个 HAOX
        </div>
        <div className="text-xs text-secondary-label mt-2">
          {currentRate.toLocaleString()} HAOX/BNB
        </div>
      </div>

      <div className="text-center p-6 bg-system-red/10 rounded-2xl border border-system-red/20">
        <div className="text-caption-1 text-secondary-label mb-2">下阶段价格</div>
        <div className="text-title-2 font-sf-pro font-bold text-system-red">
          ${nextPrice.toFixed(6)}
        </div>
        <div className="text-caption-1 text-system-red mt-1">
          +{priceIncreasePercentage.toFixed(1)}% 涨幅
        </div>
        <div className="text-xs text-secondary-label mt-2">
          {nextRate.toLocaleString()} HAOX/BNB
        </div>
      </div>
    </div>
  );
});

PriceInfo.displayName = 'PriceInfo';

const PresaleSection: React.FC = () => {
  const { addToast } = useToast();
  const { presaleData, stats } = usePresale();
  const { user, isAuthenticated } = useTelegramAuth();

  const [showTelegramJoin, setShowTelegramJoin] = useState(false);
  const [showWhitepaper, setShowWhitepaper] = useState(false);

  // Telegram加入成功回调
  const handleTelegramJoinSuccess = () => {
    addToast({
      title: '加入成功！',
      message: '您已成功加入 SocioMint 官方频道',
      type: 'success',
    });
  };

  // 获得预售资格回调
  const handleEligibilityGranted = () => {
    setShowTelegramJoin(false);
    addToast({
      title: '恭喜！',
      message: '您已获得预售认购资格',
      type: 'success',
    });
  };

  return (
    <section className="relative py-16 bg-gradient-to-br from-black via-gray-900 to-black overflow-hidden">
      {/* 动态背景效果 */}
      <div className="absolute inset-0">
        {/* 动态光效 */}
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-gradient-to-r from-system-red/20 to-system-orange/20 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-0 right-1/4 w-80 h-80 bg-gradient-to-r from-system-orange/20 to-system-yellow/20 rounded-full blur-3xl animate-pulse delay-1000" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-to-r from-system-purple/10 to-system-blue/10 rounded-full blur-3xl animate-pulse delay-500" />
        
        {/* 网格背景 */}
        <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:50px_50px]" />
      </div>

      <div className="relative max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 震撼标题区域 */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, type: "spring", stiffness: 100 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          {/* 紧急标签 */}
          <motion.div 
            className="inline-flex items-center space-x-2 bg-gradient-to-r from-system-red to-system-orange px-6 py-2 rounded-full mb-6"
            animate={{ 
              scale: [1, 1.05, 1],
              boxShadow: [
                "0 0 20px rgba(255, 59, 48, 0.3)",
                "0 0 30px rgba(255, 149, 0, 0.5)",
                "0 0 20px rgba(255, 59, 48, 0.3)"
              ]
            }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            <Icon icon={RewardIcons.zap} size="sm" className="text-white" />
            <span className="text-white font-sf-pro font-bold text-sm uppercase tracking-wider">
              🔥 限时预售进行中
            </span>
            <Icon icon={RewardIcons.zap} size="sm" className="text-white" />
          </motion.div>
          
          {/* 主标题 */}
          <motion.h1 
            className="text-5xl md:text-6xl lg:text-7xl font-sf-pro font-black text-transparent bg-clip-text bg-gradient-to-r from-white via-system-orange to-system-red mb-6"
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            HAOX
            <span className="block text-3xl md:text-4xl lg:text-5xl mt-2 text-white/90">
              代币预售
            </span>
          </motion.h1>
          
          {/* 副标题 */}
          <motion.p 
            className="text-xl md:text-2xl text-white/80 max-w-3xl mx-auto mb-8 leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            🚀 抢先获得 HAOX 代币，享受
            <span className="text-system-orange font-bold"> 早期投资者专属价格 </span>
            优势
          </motion.p>

          {/* 关键数据展示 */}
          <motion.div 
            className="flex flex-wrap justify-center gap-6 mb-8"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            <div className="bg-white/10 backdrop-blur-sm rounded-xl px-6 py-3 border border-white/20">
              <div className="text-2xl font-bold text-system-orange">50亿</div>
              <div className="text-sm text-white/70">总供应量</div>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-xl px-6 py-3 border border-white/20">
              <div className="text-2xl font-bold text-system-green">10%</div>
              <div className="text-sm text-white/70">预售比例</div>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-xl px-6 py-3 border border-white/20">
              <div className="text-2xl font-bold text-system-red">限时</div>
              <div className="text-sm text-white/70">机会稀缺</div>
            </div>
          </motion.div>
        </motion.div>

        {/* 主要内容区域 */}
        <div className="grid lg:grid-cols-2 gap-12">
          {/* 左侧：倒计时和价格信息 */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            {/* 倒计时区域 */}
            <div className="bg-white/5 backdrop-blur-xl rounded-3xl p-8 border border-white/10">
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-3 h-3 bg-system-red rounded-full animate-pulse"></div>
                <span className="text-white font-sf-pro font-bold text-xl">
                  ⏰ 预售即将结束
                </span>
              </div>

              <CountdownTimer timeLeft={stats.timeLeft} />

              {/* 紧迫感文案 */}
              <motion.div
                className="mt-6 p-4 bg-gradient-to-r from-system-red/20 to-system-orange/20 rounded-xl border border-system-red/30"
                animate={{ opacity: [0.7, 1, 0.7] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                <p className="text-white text-center font-medium">
                  🔥 错过这次机会，下次价格将上涨 <span className="text-system-orange font-bold">2%</span>
                </p>
              </motion.div>
            </div>

            {/* 价格信息 */}
            <div className="bg-white/5 backdrop-blur-xl rounded-3xl p-8 border border-white/10">
              <PriceInfo
                currentPrice={presaleData.currentPrice}
                nextPrice={presaleData.nextPrice}
                currentRate={presaleData.currentRate || 1_912_125}
                nextRate={presaleData.nextRate || 1_873_683}
                currentStage={presaleData.currentStage || 1}
                totalStages={presaleData.totalStages || 100}
                priceIncreasePercentage={2}
              />
            </div>
          </motion.div>

          {/* 右侧：购买区域 */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
          >
            <div className="bg-white/10 backdrop-blur-xl rounded-3xl p-8 border border-white/20 sticky top-8">
              {/* 购买标题 */}
              <div className="text-center mb-8">
                <h3 className="text-2xl font-sf-pro font-bold text-white mb-2">
                  🚀 立即购买福气代币
                </h3>
                <p className="text-white/70">
                  抢占先机，享受早鸟价格
                </p>
              </div>

              {/* 购买按钮区域 */}
              <div className="space-y-4 mb-8">
                <Button
                  variant="primary"
                  size="lg"
                  className="w-full bg-gradient-to-r from-system-orange to-system-red hover:from-system-red hover:to-system-orange text-white font-bold py-4 text-lg shadow-lg"
                  onClick={() => setShowTelegramJoin(true)}
                >
                  💎 立即购买福气代币
                </Button>

                <Button
                  variant="outline"
                  size="lg"
                  className="w-full border-white/30 text-white hover:bg-white/10"
                  onClick={() => setShowWhitepaper(true)}
                >
                  📄 查看白皮书
                </Button>
              </div>

              {/* 优势展示 */}
              <div className="space-y-3">
                <div className="flex items-center space-x-3 text-white/80">
                  <Icon icon={ActionIcons.check} size="sm" className="text-system-green" />
                  <span>早期投资者专属价格</span>
                </div>
                <div className="flex items-center space-x-3 text-white/80">
                  <Icon icon={ActionIcons.check} size="sm" className="text-system-green" />
                  <span>BSC网络，低手续费</span>
                </div>
                <div className="flex items-center space-x-3 text-white/80">
                  <Icon icon={ActionIcons.check} size="sm" className="text-system-green" />
                  <span>智能合约保障安全</span>
                </div>
                <div className="flex items-center space-x-3 text-white/80">
                  <Icon icon={ActionIcons.check} size="sm" className="text-system-green" />
                  <span>PancakeSwap即时交易</span>
                </div>
              </div>
            </div>
          </motion.div>
        </div>

        {/* 底部统计信息 */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
          className="mt-16 text-center"
        >
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div className="bg-white/5 backdrop-blur-sm rounded-xl p-6">
              <div className="text-3xl font-bold text-system-orange mb-2">
                {stats.progressPercentage.toFixed(1)}%
              </div>
              <div className="text-white/70 text-sm">筹资进度</div>
            </div>
            <div className="bg-white/5 backdrop-blur-sm rounded-xl p-6">
              <div className="text-3xl font-bold text-system-green mb-2">
                1,250+
              </div>
              <div className="text-white/70 text-sm">参与人数</div>
            </div>
            <div className="bg-white/5 backdrop-blur-sm rounded-xl p-6">
              <div className="text-3xl font-bold text-system-blue mb-2">
                125.8
              </div>
              <div className="text-white/70 text-sm">已筹集 BNB</div>
            </div>
            <div className="bg-white/5 backdrop-blur-sm rounded-xl p-6">
              <div className="text-3xl font-bold text-system-purple mb-2">
                第{presaleData.currentStage || 1}阶段
              </div>
              <div className="text-white/70 text-sm">当前阶段</div>
            </div>
          </div>
        </motion.div>
      </div>

      {/* 白皮书模态框 */}
      <WhitepaperModal
        isOpen={showWhitepaper}
        onClose={() => setShowWhitepaper(false)}
      />

      {/* Telegram频道加入模态框 */}
      <AnimatePresence>
        {showTelegramJoin && (
          <TelegramChannelJoin
            onJoinComplete={(success) => {
              if (success) {
                handleTelegramJoinSuccess();
              }
              setShowTelegramJoin(false);
            }}
            onEligibilityGranted={handleEligibilityGranted}
          />
        )}
      </AnimatePresence>
    </section>
  );
};

export default PresaleSection;
