'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Button, Icon, Card } from '@/components/ui';
import { ActionIcons, FinanceIcons } from '@/config/icons';
import { useGasFee, useGasFeeSelector } from '@/hooks/useGasFee';
import { cn } from '@/lib/utils';

interface GasFeeSelectorProps {
  onGasSelected?: (gasPrice: string, estimatedCost: string) => void;
  className?: string;
  showAdvanced?: boolean;
}

const GasFeeSelector: React.FC<GasFeeSelectorProps> = ({
  onGasSelected,
  className,
  showAdvanced = false,
}) => {
  const { gasFees, isLoading, networkCongestion, refreshGasFees } = useGasFee();
  const {
    selectedSpeed,
    setSelectedSpeed,
    customGasPrice,
    setCustomGasPrice,
    useCustomGas,
    setUseCustomGas,
    getSelectedGasPrice,
    getSelectedCost,
    getEstimatedTime,
  } = useGasFeeSelector();

  const [showCustomInput, setShowCustomInput] = useState(false);

  /**
   * 处理速度选择
   */
  const handleSpeedSelect = (speed: 'slow' | 'standard' | 'fast') => {
    setSelectedSpeed(speed);
    setUseCustomGas(false);
    
    if (gasFees) {
      const gasPrice = gasFees[speed].gasPrice;
      const cost = gasFees[speed].cost;
      onGasSelected?.(gasPrice, cost);
    }
  };

  /**
   * 处理自定义Gas价格
   */
  const handleCustomGasChange = (value: string) => {
    setCustomGasPrice(value);
    setUseCustomGas(true);
    
    if (value && gasFees) {
      const cost = getSelectedCost(gasFees);
      onGasSelected?.(value, cost);
    }
  };

  /**
   * 获取速度标签颜色
   */
  const getSpeedColor = (speed: 'slow' | 'standard' | 'fast') => {
    switch (speed) {
      case 'slow':
        return 'text-system-green';
      case 'standard':
        return 'text-system-blue';
      case 'fast':
        return 'text-system-orange';
      default:
        return 'text-label';
    }
  };

  /**
   * 获取网络状态颜色
   */
  const getNetworkStatusColor = () => {
    if (!networkCongestion) return 'text-secondary-label';
    
    switch (networkCongestion.level) {
      case 'low':
        return 'text-system-green';
      case 'medium':
        return 'text-system-orange';
      case 'high':
        return 'text-system-red';
      default:
        return 'text-secondary-label';
    }
  };

  if (isLoading && !gasFees) {
    return (
      <Card className={cn('p-4', className)}>
        <div className="text-center">
          <div className="w-6 h-6 border-2 border-system-blue border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
          <p className="text-caption-1 text-secondary-label">加载Gas费信息...</p>
        </div>
      </Card>
    );
  }

  return (
    <Card className={cn('p-4', className)}>
      <div className="flex items-center justify-between mb-4">
        <h4 className="text-body font-sf-pro font-medium text-label">
          Gas费设置
        </h4>
        <Button
          variant="ghost"
          size="sm"
          onClick={refreshGasFees}
          disabled={isLoading}
        >
          <Icon 
            icon={ActionIcons.refresh} 
            size="sm" 
            className={isLoading ? 'animate-spin' : ''} 
          />
        </Button>
      </div>

      {/* 网络状态 */}
      {networkCongestion && (
        <div className="mb-4 p-3 bg-system-gray-6 rounded-lg">
          <div className="flex items-center space-x-2">
            <Icon icon={FinanceIcons.activity} size="sm" color="muted" />
            <span className="text-caption-1 text-secondary-label">网络状态:</span>
            <span className={cn('text-caption-1 font-sf-pro font-medium', getNetworkStatusColor())}>
              {networkCongestion.description}
            </span>
          </div>
        </div>
      )}

      {/* Gas费选项 */}
      {gasFees && (
        <div className="space-y-3 mb-4">
          {[
            { key: 'slow', label: '经济', description: '节省费用' },
            { key: 'standard', label: '标准', description: '推荐选择' },
            { key: 'fast', label: '快速', description: '优先处理' },
          ].map((option) => {
            const speed = option.key as 'slow' | 'standard' | 'fast';
            const gasData = gasFees[speed];
            const isSelected = selectedSpeed === speed && !useCustomGas;

            return (
              <motion.button
                key={speed}
                onClick={() => handleSpeedSelect(speed)}
                className={cn(
                  'w-full p-3 rounded-lg border-2 transition-colors text-left',
                  isSelected
                    ? 'border-system-blue bg-system-blue/10'
                    : 'border-system-gray-4 hover:border-system-gray-3'
                )}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <div className="flex items-center justify-between">
                  <div>
                    <div className="flex items-center space-x-2 mb-1">
                      <span className={cn('text-body font-sf-pro font-medium', getSpeedColor(speed))}>
                        {option.label}
                      </span>
                      <span className="text-caption-1 text-secondary-label">
                        ({option.description})
                      </span>
                    </div>
                    <p className="text-caption-1 text-secondary-label">
                      {gasData.gasPrice} Gwei • {gasData.estimatedTime}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-body font-sf-pro font-medium text-label">
                      {parseFloat(gasData.cost).toFixed(6)} BNB
                    </p>
                  </div>
                </div>
              </motion.button>
            );
          })}
        </div>
      )}

      {/* 高级选项 */}
      {showAdvanced && (
        <div className="border-t border-system-gray-4 pt-4">
          <button
            onClick={() => setShowCustomInput(!showCustomInput)}
            className="flex items-center space-x-2 text-caption-1 text-system-blue hover:text-system-blue/80 transition-colors mb-3"
          >
            <Icon 
              icon={showCustomInput ? ActionIcons.chevronUp : ActionIcons.chevronDown} 
              size="sm" 
            />
            <span>自定义Gas价格</span>
          </button>

          {showCustomInput && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="space-y-3"
            >
              <div>
                <label className="block text-caption-1 text-secondary-label mb-2">
                  Gas价格 (Gwei)
                </label>
                <div className="relative">
                  <input
                    type="number"
                    value={customGasPrice}
                    onChange={(e) => handleCustomGasChange(e.target.value)}
                    placeholder="输入自定义Gas价格"
                    className="w-full px-3 py-2 rounded-lg border border-system-gray-4 bg-system-background text-body focus:border-system-blue focus:outline-none"
                    min="1"
                    max="100"
                    step="0.1"
                  />
                  <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-caption-1 text-secondary-label">
                    Gwei
                  </span>
                </div>
              </div>

              {useCustomGas && customGasPrice && gasFees && (
                <div className="p-3 bg-system-gray-6 rounded-lg">
                  <div className="flex justify-between items-center">
                    <span className="text-caption-1 text-secondary-label">预估费用:</span>
                    <span className="text-caption-1 font-sf-pro font-medium text-label">
                      {parseFloat(getSelectedCost(gasFees)).toFixed(6)} BNB
                    </span>
                  </div>
                  <div className="flex justify-between items-center mt-1">
                    <span className="text-caption-1 text-secondary-label">预估时间:</span>
                    <span className="text-caption-1 text-secondary-label">
                      {getEstimatedTime(gasFees)}
                    </span>
                  </div>
                </div>
              )}
            </motion.div>
          )}
        </div>
      )}

      {/* 当前选择摘要 */}
      {gasFees && (
        <div className="mt-4 p-3 bg-system-blue/10 rounded-lg">
          <div className="flex justify-between items-center">
            <span className="text-caption-1 text-secondary-label">当前选择:</span>
            <div className="text-right">
              <p className="text-caption-1 font-sf-pro font-medium text-label">
                {getSelectedGasPrice(gasFees)} Gwei
              </p>
              <p className="text-caption-1 text-secondary-label">
                ≈ {parseFloat(getSelectedCost(gasFees)).toFixed(6)} BNB
              </p>
            </div>
          </div>
        </div>
      )}
    </Card>
  );
};

export default GasFeeSelector;
