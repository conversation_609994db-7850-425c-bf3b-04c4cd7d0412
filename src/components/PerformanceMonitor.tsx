/**
 * 性能监控组件
 * 监控页面性能指标并上报数据
 */

'use client';

import { useEffect, useRef } from 'react';
import { monitoring } from '@/lib/monitoring';

interface PerformanceMonitorProps {
  pageName?: string;
  enableWebVitals?: boolean;
  enableUserTiming?: boolean;
  enableResourceTiming?: boolean;
}

export function PerformanceMonitor({
  pageName = 'unknown',
  enableWebVitals = true,
  enableUserTiming = true,
  enableResourceTiming = true,
}: PerformanceMonitorProps) {
  const hasReported = useRef(false);
  const startTime = useRef(Date.now());

  useEffect(() => {
    // 避免重复上报
    if (hasReported.current) return;
    hasReported.current = true;

    // 页面加载完成后收集性能数据
    const collectPerformanceData = () => {
      try {
        // 基础性能指标
        collectBasicMetrics();
        
        // Web Vitals
        if (enableWebVitals) {
          collectWebVitals();
        }
        
        // 用户时间指标
        if (enableUserTiming) {
          collectUserTiming();
        }
        
        // 资源加载时间
        if (enableResourceTiming) {
          collectResourceTiming();
        }
        
        // 自定义指标
        collectCustomMetrics();
        
      } catch (error) {
        monitoring.error('Performance monitoring failed', { error, pageName });
      }
    };

    // 等待页面完全加载
    if (document.readyState === 'complete') {
      setTimeout(collectPerformanceData, 100);
    } else {
      window.addEventListener('load', () => {
        setTimeout(collectPerformanceData, 100);
      });
    }

    // 页面卸载时收集数据
    const handleBeforeUnload = () => {
      const sessionDuration = Date.now() - startTime.current;
      monitoring.performance('session_duration', sessionDuration, pageName);
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [pageName, enableWebVitals, enableUserTiming, enableResourceTiming]);

  return null; // 这是一个无UI的监控组件
}

/**
 * 收集基础性能指标
 */
function collectBasicMetrics() {
  if (!window.performance || !window.performance.timing) return;

  const timing = window.performance.timing;
  const navigation = window.performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;

  if (navigation) {
    // 页面加载时间
    const pageLoadTime = navigation.loadEventEnd - navigation.fetchStart;
    monitoring.performance('page_load_time', pageLoadTime);

    // DNS 查询时间
    const dnsTime = navigation.domainLookupEnd - navigation.domainLookupStart;
    monitoring.performance('dns_lookup_time', dnsTime);

    // TCP 连接时间
    const tcpTime = navigation.connectEnd - navigation.connectStart;
    monitoring.performance('tcp_connect_time', tcpTime);

    // SSL 握手时间
    if (navigation.secureConnectionStart > 0) {
      const sslTime = navigation.connectEnd - navigation.secureConnectionStart;
      monitoring.performance('ssl_handshake_time', sslTime);
    }

    // 首字节时间 (TTFB)
    const ttfb = navigation.responseStart - navigation.fetchStart;
    monitoring.performance('time_to_first_byte', ttfb);

    // DOM 解析时间
    const domParseTime = navigation.domContentLoadedEventEnd - navigation.domLoading;
    monitoring.performance('dom_parse_time', domParseTime);

    // 资源加载时间
    const resourceLoadTime = navigation.loadEventEnd - navigation.domContentLoadedEventEnd;
    monitoring.performance('resource_load_time', resourceLoadTime);
  }
}

/**
 * 收集 Web Vitals 指标
 */
function collectWebVitals() {
  // FCP (First Contentful Paint)
  const fcpObserver = new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      if (entry.name === 'first-contentful-paint') {
        monitoring.performance('first_contentful_paint', entry.startTime);
      }
    }
  });

  try {
    fcpObserver.observe({ entryTypes: ['paint'] });
  } catch (e) {
    // 浏览器不支持
  }

  // LCP (Largest Contentful Paint)
  const lcpObserver = new PerformanceObserver((list) => {
    const entries = list.getEntries();
    const lastEntry = entries[entries.length - 1];
    monitoring.performance('largest_contentful_paint', lastEntry.startTime);
  });

  try {
    lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
  } catch (e) {
    // 浏览器不支持
  }

  // FID (First Input Delay)
  const fidObserver = new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      const fid = (entry as any).processingStart - entry.startTime;
      monitoring.performance('first_input_delay', fid);
    }
  });

  try {
    fidObserver.observe({ entryTypes: ['first-input'] });
  } catch (e) {
    // 浏览器不支持
  }

  // CLS (Cumulative Layout Shift)
  let clsValue = 0;
  const clsObserver = new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      if (!(entry as any).hadRecentInput) {
        clsValue += (entry as any).value;
      }
    }
    monitoring.performance('cumulative_layout_shift', clsValue);
  });

  try {
    clsObserver.observe({ entryTypes: ['layout-shift'] });
  } catch (e) {
    // 浏览器不支持
  }
}

/**
 * 收集用户时间指标
 */
function collectUserTiming() {
  const userTimings = window.performance.getEntriesByType('measure');
  
  userTimings.forEach((timing) => {
    monitoring.performance(`user_timing_${timing.name}`, timing.duration);
  });

  const marks = window.performance.getEntriesByType('mark');
  marks.forEach((mark) => {
    monitoring.performance(`user_mark_${mark.name}`, mark.startTime);
  });
}

/**
 * 收集资源加载时间
 */
function collectResourceTiming() {
  const resources = window.performance.getEntriesByType('resource') as PerformanceResourceTiming[];
  
  const resourceStats = {
    scripts: { count: 0, totalSize: 0, totalTime: 0 },
    stylesheets: { count: 0, totalSize: 0, totalTime: 0 },
    images: { count: 0, totalSize: 0, totalTime: 0 },
    fonts: { count: 0, totalSize: 0, totalTime: 0 },
    xhr: { count: 0, totalSize: 0, totalTime: 0 },
    other: { count: 0, totalSize: 0, totalTime: 0 },
  };

  resources.forEach((resource) => {
    const duration = resource.responseEnd - resource.startTime;
    const size = resource.transferSize || 0;
    
    let category = 'other';
    
    if (resource.initiatorType === 'script') {
      category = 'scripts';
    } else if (resource.initiatorType === 'link' || resource.name.includes('.css')) {
      category = 'stylesheets';
    } else if (resource.initiatorType === 'img') {
      category = 'images';
    } else if (resource.name.includes('.woff') || resource.name.includes('.ttf')) {
      category = 'fonts';
    } else if (resource.initiatorType === 'xmlhttprequest' || resource.initiatorType === 'fetch') {
      category = 'xhr';
    }

    const stats = resourceStats[category as keyof typeof resourceStats];
    stats.count++;
    stats.totalSize += size;
    stats.totalTime += duration;
  });

  // 上报资源统计
  Object.entries(resourceStats).forEach(([category, stats]) => {
    if (stats.count > 0) {
      monitoring.performance(`resource_${category}_count`, stats.count);
      monitoring.performance(`resource_${category}_total_size`, stats.totalSize);
      monitoring.performance(`resource_${category}_avg_time`, stats.totalTime / stats.count);
    }
  });
}

/**
 * 收集自定义指标
 */
function collectCustomMetrics() {
  // 内存使用情况
  if ('memory' in window.performance) {
    const memory = (window.performance as any).memory;
    monitoring.performance('memory_used', memory.usedJSHeapSize);
    monitoring.performance('memory_total', memory.totalJSHeapSize);
    monitoring.performance('memory_limit', memory.jsHeapSizeLimit);
  }

  // 连接信息
  if ('connection' in navigator) {
    const connection = (navigator as any).connection;
    if (connection) {
      monitoring.performance('network_downlink', connection.downlink);
      monitoring.performance('network_rtt', connection.rtt);
      monitoring.metric('network_type', 1, { type: connection.effectiveType });
    }
  }

  // 设备信息
  monitoring.metric('device_pixel_ratio', window.devicePixelRatio || 1);
  monitoring.metric('viewport_width', window.innerWidth);
  monitoring.metric('viewport_height', window.innerHeight);
  monitoring.metric('screen_width', window.screen.width);
  monitoring.metric('screen_height', window.screen.height);
}

/**
 * 性能监控 Hook
 */
export function usePerformanceMonitoring(pageName: string) {
  useEffect(() => {
    // 标记页面开始时间
    window.performance.mark(`page_${pageName}_start`);
    
    return () => {
      // 标记页面结束时间
      window.performance.mark(`page_${pageName}_end`);
      
      // 测量页面停留时间
      try {
        window.performance.measure(
          `page_${pageName}_duration`,
          `page_${pageName}_start`,
          `page_${pageName}_end`
        );
      } catch (e) {
        // 忽略错误
      }
    };
  }, [pageName]);
}

/**
 * 手动标记性能时间点
 */
export function markPerformance(name: string) {
  try {
    window.performance.mark(name);
  } catch (e) {
    // 忽略错误
  }
}

/**
 * 手动测量性能时间段
 */
export function measurePerformance(name: string, startMark: string, endMark?: string) {
  try {
    window.performance.measure(name, startMark, endMark);
    
    const measures = window.performance.getEntriesByName(name, 'measure');
    if (measures.length > 0) {
      const measure = measures[measures.length - 1];
      monitoring.performance(name, measure.duration);
    }
  } catch (e) {
    // 忽略错误
  }
}
