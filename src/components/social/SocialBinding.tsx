'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ChatBubbleLeftRightIcon as MessageCircle,
  ArrowTopRightOnSquareIcon as ExternalLink,
  ShareIcon as Share2,
  ExclamationCircleIcon as AlertCircle,
  ArrowPathIcon as Loader2,
  TrophyIcon as Trophy
} from '@heroicons/react/24/outline';
import { ActionIcons, UserIcons, FeatureIcons } from '@/config/icons';
import { Button, Card } from '@/components/ui';
import { useToast } from '@/components/ui';

interface SocialBindingProps {
  userId: string;
  onBindingComplete: (data: any) => void;
  onEligibilityGranted: () => void;
}

interface BindingStatus {
  isBound: boolean;
  telegramId?: number;
  telegramUsername?: string;
  telegramFirstName?: string;
  presaleEligible: boolean;
}

const SocialBinding: React.FC<SocialBindingProps> = ({
  userId,
  onBindingComplete,
  onEligibilityGranted,
}) => {
  const { addToast } = useToast();
  const [bindingStatus, setBindingStatus] = useState<BindingStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isBinding, setIsBinding] = useState(false);
  const [shareStep, setShareStep] = useState<'bind' | 'complete'>('bind');

  // 检查绑定状态
  useEffect(() => {
    checkBindingStatus();
  }, [userId]);

  const checkBindingStatus = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/telegram/bind?userId=${userId}`);
      const data = await response.json();

      if (data.success) {
        setBindingStatus(data.data);

        // 简化流程：绑定成功即获得预售资格
        if (data.data.isBound) {
          setShareStep('complete');
        }
      }
    } catch (error) {
      console.error('Failed to check binding status:', error);
      addToast({
        title: '检查失败',
        message: '无法检查绑定状态，请刷新页面重试',
        type: 'error',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleTelegramBinding = async () => {
    try {
      setIsBinding(true);

      // 生成绑定验证码
      const response = await fetch('/api/telegram/generate-code', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId }),
      });

      const data = await response.json();

      if (data.success) {
        // 显示绑定指引
        addToast({
          title: '绑定指引',
          message: `请打开 Telegram，搜索 @sociomintbot，发送验证码：${data.bindingCode}`,
          type: 'info',
        });

        // 打开 Telegram Bot 链接
        window.open(data.bindingUrl, '_blank');

        // 开始轮询检查绑定状态
        startBindingPolling();
      } else {
        throw new Error(data.error || '生成绑定验证码失败');
      }
    } catch (error) {
      console.error('Failed to initiate binding:', error);
      addToast({
        title: '绑定失败',
        message: error instanceof Error ? error.message : '绑定过程中发生错误',
        type: 'error',
      });
    } finally {
      setIsBinding(false);
    }
  };

  const startBindingPolling = () => {
    const pollInterval = setInterval(async () => {
      try {
        const response = await fetch(`/api/telegram/bind?userId=${userId}`);
        const data = await response.json();

        if (data.success && data.data.isBound) {
          clearInterval(pollInterval);
          setBindingStatus(data.data);
          setShareStep('complete');
          onBindingComplete(data.data);
          onEligibilityGranted(); // 直接授予预售资格

          addToast({
            title: '绑定成功！',
            message: '您已获得预售资格，可以立即参与预售',
            type: 'success',
          });
        }
      } catch (error) {
        console.error('Polling error:', error);
      }
    }, 3000);

    // 30秒后停止轮询
    setTimeout(() => {
      clearInterval(pollInterval);
    }, 30000);
  };



  if (isLoading) {
    return (
      <Card className="p-6">
        <div className="flex items-center justify-center space-x-2">
          <Loader2 className="w-5 h-5 animate-spin" />
          <span>检查绑定状态...</span>
        </div>
      </Card>
    );
  }

  return (
    <Card className="p-6">
      <div className="space-y-6">
        {/* 步骤指示器 */}
        <div className="flex items-center justify-center">
          {['bind', 'complete'].map((step, index) => (
            <div key={step} className="flex items-center">
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  shareStep === step
                    ? 'bg-blue-500 text-white'
                    : index < ['bind', 'complete'].indexOf(shareStep)
                    ? 'bg-green-500 text-white'
                    : 'bg-gray-200 text-gray-600'
                }`}
              >
                {index < ['bind', 'complete'].indexOf(shareStep) ? (
                  <ActionIcons.checkCircle className="w-4 h-4" />
                ) : (
                  index + 1
                )}
              </div>
              {index < 1 && (
                <div
                  className={`w-16 h-1 mx-2 ${
                    index < ['bind', 'complete'].indexOf(shareStep)
                      ? 'bg-green-500'
                      : 'bg-gray-200'
                  }`}
                />
              )}
            </div>
          ))}
        </div>

        {/* 步骤内容 */}
        <AnimatePresence mode="wait">
          {shareStep === 'bind' && (
            <motion.div
              key="bind"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="text-center space-y-4"
            >
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
                <MessageCircle className="w-8 h-8 text-blue-500" />
              </div>
              
              <div>
                <h3 className="text-xl font-semibold mb-2">绑定 Telegram 账户</h3>
                <p className="text-gray-600 mb-4">
                  绑定您的 Telegram 账户即可获得预售资格，无需额外步骤
                </p>
              </div>

              <Button
                onClick={handleTelegramBinding}
                disabled={isBinding}
                className="w-full"
                size="lg"
              >
                {isBinding ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    正在生成绑定链接...
                  </>
                ) : (
                  <>
                    <MessageCircle className="w-4 h-4 mr-2" />
                    绑定 Telegram 账户
                  </>
                )}
              </Button>

              <div className="text-sm text-gray-500">
                <p>点击后将打开 Telegram Bot，请按照指示完成绑定</p>
              </div>
            </motion.div>
          )}

          {shareStep === 'complete' && (
            <motion.div
              key="complete"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="text-center space-y-4"
            >
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                <ActionIcons.checkCircle className="w-8 h-8 text-green-500" />
              </div>
              
              <div>
                <h3 className="text-xl font-semibold mb-2 text-green-600">恭喜！获得预售资格</h3>
                <p className="text-gray-600 mb-4">
                  您已成功绑定 Telegram 账户，现在可以参与 HAOX 代币预售
                </p>
              </div>

              <div className="bg-green-50 p-4 rounded-lg mb-4">
                <div className="flex items-start space-x-3">
                  <ActionIcons.checkCircle className="w-5 h-5 text-green-500 mt-0.5" />
                  <div className="text-left">
                    <h4 className="font-medium text-green-900">预售权限已激活</h4>
                    <ul className="text-sm text-green-700 mt-1 space-y-1">
                      <li>• 绑定成功即获得预售资格</li>
                      <li>• 每个账户限购一次</li>
                      <li>• 请及时完成认购，名额有限</li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* 邀请分享功能 */}
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="flex items-start space-x-3">
                  <Share2 className="w-5 h-5 text-blue-500 mt-0.5" />
                  <div className="text-left flex-1">
                    <h4 className="font-medium text-blue-900 mb-2">邀请好友获得奖励</h4>
                    <p className="text-sm text-blue-700 mb-3">
                      邀请好友加入 SocioMint，每成功邀请1人可获得50 HAOX奖励！
                    </p>
                    <div className="flex flex-wrap gap-2">
                      <Button
                        onClick={() => window.open('/invitation', '_blank')}
                        size="sm"
                        className="bg-blue-500 hover:bg-blue-600 text-white"
                      >
                        <UserIcons.users className="w-4 h-4 mr-1" />
                        查看邀请中心
                      </Button>
                      <Button
                        onClick={() => window.open('/leaderboard', '_blank')}
                        size="sm"
                        variant="outline"
                        className="border-blue-500 text-blue-500 hover:bg-blue-50"
                      >
                        <Trophy className="w-4 h-4 mr-1" />
                        排行榜
                      </Button>
                    </div>
                  </div>
                </div>
              </div>

              {bindingStatus?.telegramUsername && (
                <div className="text-sm text-gray-500">
                  绑定账户：@{bindingStatus.telegramUsername}
                </div>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </Card>
  );
};

export default SocialBinding;
