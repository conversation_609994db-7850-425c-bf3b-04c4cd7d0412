'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, Loading, Breadcrumb, <PERSON><PERSON>, Button } from '@/components/ui';
import { useAuth } from '@/contexts/AuthContext';

interface UserTraceabilityData {
  operations: Array<{
    id: string;
    betId: string;
    typeText: string;
    typeIcon: string;
    typeColor: string;
    description: string;
    timestamp: string;
    formattedTime: string;
    relativeTime: string;
    metadata: Record<string, any>;
    txHash?: string;
  }>;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  stats: {
    totalInflow: number;
    totalOutflow: number;
    totalFees: number;
    totalRewards: number;
    flowCount: number;
    totalOperations: number;
    todayOperations: number;
    weekOperations: number;
    monthOperations: number;
    betsCreated: number;
    betsJoined: number;
    judgmentsParticipated: number;
    confirmationsSubmitted: number;
    disputesRaised: number;
    lastActivityTime: string | null;
    mostActiveDay: string | null;
    averageOperationsPerDay: number;
  };
}

const UserTraceabilityPage: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const [data, setData] = useState<UserTraceabilityData | null>(null);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);

  useEffect(() => {
    if (isAuthenticated && user?.id) {
      loadUserTraceabilityData();
    }
  }, [isAuthenticated, user, page]);

  const loadUserTraceabilityData = async () => {
    try {
      setLoading(true);
      
      const response = await fetch(`/api/traceability/user/${user?.id}?page=${page}&limit=20`);
      const result = await response.json();

      if (result.success) {
        setData(result.data);
      } else {
        console.error('获取用户可追溯性数据失败:', result.message);
      }
    } catch (error) {
      console.error('加载用户可追溯性数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Breadcrumb
          items={[
            { label: '首页', href: '/', icon: '🏠' },
            { label: '我的操作历史', icon: '📋' }
          ]}
        />
        
        <div className="text-center py-12">
          <div className="text-6xl mb-4">🔒</div>
          <h2 className="text-2xl font-bold text-label mb-4">需要登录</h2>
          <p className="text-secondary-label mb-6">
            查看操作历史需要登录账户，请先完成登录。
          </p>
          <Button href="/auth/login">
            立即登录
          </Button>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-center items-center min-h-96">
          <Loading size="large" />
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center py-12">
          <div className="text-6xl mb-4">❌</div>
          <h3 className="text-xl font-semibold text-label mb-2">数据加载失败</h3>
          <p className="text-secondary-label mb-6">
            无法获取您的操作历史数据，请稍后重试。
          </p>
          <Button onClick={loadUserTraceabilityData}>
            重新加载
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* 面包屑导航 */}
      <div className="mb-6">
        <Breadcrumb
          items={[
            { label: '首页', href: '/', icon: '🏠' },
            { label: '我的操作历史', icon: '📋' }
          ]}
        />
      </div>

      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-label mb-2">
          📋 我的操作历史
        </h1>
        <p className="text-secondary-label">
          完整记录您在平台上的所有操作，透明可追溯
        </p>
      </div>

      {/* 活跃度统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-blue-600 mb-1">
            {data.stats.totalOperations}
          </div>
          <div className="text-sm text-secondary-label">总操作次数</div>
        </Card>
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-green-600 mb-1">
            {data.stats.betsCreated}
          </div>
          <div className="text-sm text-secondary-label">创建赌约</div>
        </Card>
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-purple-600 mb-1">
            {data.stats.betsJoined}
          </div>
          <div className="text-sm text-secondary-label">参与赌约</div>
        </Card>
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-orange-600 mb-1">
            {data.stats.judgmentsParticipated}
          </div>
          <div className="text-sm text-secondary-label">参与裁定</div>
        </Card>
      </div>

      {/* 福气流转统计 */}
      <Card className="p-6 mb-8">
        <h2 className="text-xl font-bold text-label mb-4">💰 福气流转统计</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="p-3 bg-green-50 rounded-lg text-center">
            <p className="text-sm text-secondary-label">总收入</p>
            <p className="text-lg font-bold text-green-600">
              {data.stats.totalInflow.toLocaleString()}
            </p>
          </div>
          <div className="p-3 bg-red-50 rounded-lg text-center">
            <p className="text-sm text-secondary-label">总支出</p>
            <p className="text-lg font-bold text-red-600">
              {data.stats.totalOutflow.toLocaleString()}
            </p>
          </div>
          <div className="p-3 bg-orange-50 rounded-lg text-center">
            <p className="text-sm text-secondary-label">手续费</p>
            <p className="text-lg font-bold text-orange-600">
              {data.stats.totalFees.toLocaleString()}
            </p>
          </div>
          <div className="p-3 bg-purple-50 rounded-lg text-center">
            <p className="text-sm text-secondary-label">奖励</p>
            <p className="text-lg font-bold text-purple-600">
              {data.stats.totalRewards.toLocaleString()}
            </p>
          </div>
        </div>
      </Card>

      {/* 活跃度分析 */}
      <Card className="p-6 mb-8">
        <h2 className="text-xl font-bold text-label mb-4">📊 活跃度分析</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <h3 className="font-semibold text-label mb-2">时间段活跃度</h3>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-secondary-label">今日操作:</span>
                <span className="font-medium text-label">{data.stats.todayOperations}次</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-secondary-label">本周操作:</span>
                <span className="font-medium text-label">{data.stats.weekOperations}次</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-secondary-label">本月操作:</span>
                <span className="font-medium text-label">{data.stats.monthOperations}次</span>
              </div>
            </div>
          </div>
          
          <div>
            <h3 className="font-semibold text-label mb-2">操作类型分布</h3>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-secondary-label">确认提交:</span>
                <span className="font-medium text-label">{data.stats.confirmationsSubmitted}次</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-secondary-label">争议提出:</span>
                <span className="font-medium text-label">{data.stats.disputesRaised}次</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-secondary-label">平均每日:</span>
                <span className="font-medium text-label">{data.stats.averageOperationsPerDay}次</span>
              </div>
            </div>
          </div>
          
          <div>
            <h3 className="font-semibold text-label mb-2">活跃度信息</h3>
            <div className="space-y-2">
              {data.stats.lastActivityTime && (
                <div className="flex justify-between">
                  <span className="text-sm text-secondary-label">最后活跃:</span>
                  <span className="font-medium text-label">
                    {new Date(data.stats.lastActivityTime).toLocaleDateString()}
                  </span>
                </div>
              )}
              {data.stats.mostActiveDay && (
                <div className="flex justify-between">
                  <span className="text-sm text-secondary-label">最活跃日:</span>
                  <span className="font-medium text-label">
                    {new Date(data.stats.mostActiveDay).toLocaleDateString()}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      </Card>

      {/* 操作记录列表 */}
      <Card className="p-6">
        <h2 className="text-xl font-bold text-label mb-6">📝 操作记录</h2>
        
        <div className="space-y-4">
          {data.operations.map((operation, index) => (
            <motion.div
              key={operation.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className="border rounded-lg p-4 hover:shadow-md transition-shadow"
            >
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">{operation.typeIcon}</span>
                  <div>
                    <Badge className={operation.typeColor}>
                      {operation.typeText}
                    </Badge>
                    <p className="text-sm text-secondary-label mt-1">
                      {operation.relativeTime}
                    </p>
                  </div>
                </div>
                
                <div className="text-right">
                  <Button
                    href={`/social-bet/${operation.betId}`}
                    size="small"
                    variant="outline"
                  >
                    查看赌约
                  </Button>
                </div>
              </div>
              
              <p className="text-sm text-label mb-2">
                {operation.description}
              </p>
              
              <div className="flex justify-between items-center text-xs text-secondary-label">
                <span>{operation.formattedTime}</span>
                {operation.txHash && (
                  <span className="font-mono">
                    TX: {operation.txHash.slice(0, 10)}...
                  </span>
                )}
              </div>
            </motion.div>
          ))}
        </div>

        {/* 分页 */}
        {data.pagination.totalPages > 1 && (
          <div className="flex justify-center mt-8">
            <div className="flex space-x-2">
              <Button
                onClick={() => setPage(Math.max(1, page - 1))}
                disabled={page === 1}
                variant="outline"
                size="small"
              >
                上一页
              </Button>
              <span className="flex items-center px-4 text-sm text-secondary-label">
                第 {page} 页，共 {data.pagination.totalPages} 页
              </span>
              <Button
                onClick={() => setPage(Math.min(data.pagination.totalPages, page + 1))}
                disabled={page === data.pagination.totalPages}
                variant="outline"
                size="small"
              >
                下一页
              </Button>
            </div>
          </div>
        )}

        {/* 空状态 */}
        {data.operations.length === 0 && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">📝</div>
            <h3 className="text-xl font-semibold text-label mb-2">暂无操作记录</h3>
            <p className="text-secondary-label mb-6">
              您还没有进行任何操作，快去参与赌约吧！
            </p>
            <Button href="/social-bet">
              浏览赌约
            </Button>
          </div>
        )}
      </Card>
    </div>
  );
};

export default UserTraceabilityPage;
