'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button, Icon, Card } from '@/components/ui';
import { ActionIcons, FinanceIcons, FeatureIcons, RewardIcons, NotificationIcons } from '@/config/icons';
import { useTelegramAuth } from '@/hooks/useTelegramAuth';
import { useWallet } from '@/hooks/useWallet';
import { WalletTransaction } from '@/services/wallet/types';
import { cn } from '@/lib/utils';

interface TransactionHistoryProps {
  onClose?: () => void;
}

const TransactionHistory: React.FC<TransactionHistoryProps> = ({ onClose }) => {
  const { user } = useTelegramAuth();
  const {
    transactionHistory: transactions,
    isLoadingHistory: isLoading,
    refreshHistory
  } = useWallet();

  // 格式化函数
  const formatBalance = (amount: string, decimals: number = 2): string => {
    const num = parseFloat(amount || '0');
    return num.toFixed(decimals);
  };

  const formatUSD = (amount: number): string => {
    return `$${amount.toFixed(2)}`;
  };

  // 模拟过滤和分页状态
  const [filter, setFilter] = useState<'all' | 'transfer' | 'withdrawal' | 'deposit' | 'reward_claim'>('all');
  const [hasMore] = useState(false);

  const loadMore = () => {
    // 模拟加载更多
    console.log('Load more transactions');
  };

  const [selectedTransaction, setSelectedTransaction] = useState<WalletTransaction | null>(null);

  useEffect(() => {
    if (user) {
      refreshHistory();
    }
  }, [user, refreshHistory]);

  /**
   * 获取交易图标
   */
  const getTransactionIcon = (transaction: WalletTransaction) => {
    switch (transaction.type) {
      case 'transfer':
        return NotificationIcons.send;
      case 'withdrawal':
        return ActionIcons.download;
      case 'deposit':
        return ActionIcons.upload;
      case 'reward_claim':
        return RewardIcons.gift;
      default:
        return FinanceIcons.coins;
    }
  };

  /**
   * 获取交易颜色
   */
  const getTransactionColor = (transaction: WalletTransaction) => {
    switch (transaction.type) {
      case 'transfer':
        return 'primary';
      case 'withdrawal':
        return 'warning';
      case 'deposit':
        return 'success';
      case 'reward_claim':
        return 'success';
      default:
        return 'muted';
    }
  };

  /**
   * 获取交易状态颜色
   */
  const getStatusColor = (status: WalletTransaction['status']) => {
    switch (status) {
      case 'confirmed':
        return 'text-system-green';
      case 'pending':
        return 'text-system-orange';
      case 'failed':
        return 'text-system-red';
      default:
        return 'text-secondary-label';
    }
  };

  /**
   * 获取交易状态文本
   */
  const getStatusText = (status: WalletTransaction['status']) => {
    switch (status) {
      case 'confirmed':
        return '已确认';
      case 'pending':
        return '处理中';
      case 'failed':
        return '失败';
      default:
        return '未知';
    }
  };

  /**
   * 获取交易类型文本
   */
  const getTypeText = (type: WalletTransaction['type']) => {
    switch (type) {
      case 'transfer':
        return '转账';
      case 'withdrawal':
        return '提现';
      case 'deposit':
        return '充值';
      case 'reward_claim':
        return '奖励领取';
      default:
        return '其他';
    }
  };

  /**
   * 格式化交易金额
   */
  const formatTransactionAmount = (transaction: WalletTransaction) => {
    const isOutgoing = transaction.type === 'transfer' || transaction.type === 'withdrawal';
    const prefix = isOutgoing ? '-' : '+';
    return `${prefix}${formatBalance(transaction.amount)} ${transaction.tokenType}`;
  };

  if (selectedTransaction) {
    return (
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-title-2 font-sf-pro font-bold text-label">
            交易详情
          </h3>
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={() => setSelectedTransaction(null)}
          >
            <Icon icon={ActionIcons.close} size="sm" />
          </Button>
        </div>

        <div className="space-y-4">
          {/* 交易基本信息 */}
          <div className="bg-system-gray-6 rounded-xl p-4">
            <div className="flex items-center space-x-3 mb-4">
              <div className={cn(
                'w-12 h-12 rounded-full flex items-center justify-center',
                selectedTransaction.type === 'reward_claim' ? 'bg-system-green/20' : 'bg-system-blue/20'
              )}>
                <Icon 
                  icon={getTransactionIcon(selectedTransaction)} 
                  size="lg" 
                  color={getTransactionColor(selectedTransaction) as any} 
                />
              </div>
              <div>
                <h4 className="text-title-3 font-sf-pro font-semibold text-label">
                  {getTypeText(selectedTransaction.type)}
                </h4>
                <p className="text-caption-1 text-secondary-label">
                  {selectedTransaction.createdAt.toLocaleString()}
                </p>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-caption-1 text-secondary-label mb-1">金额</p>
                <p className="text-title-3 font-sf-pro font-bold text-label">
                  {formatTransactionAmount(selectedTransaction)}
                </p>
              </div>
              <div>
                <p className="text-caption-1 text-secondary-label mb-1">状态</p>
                <p className={cn('text-body font-sf-pro font-medium', getStatusColor(selectedTransaction.status))}>
                  {getStatusText(selectedTransaction.status)}
                </p>
              </div>
            </div>
          </div>

          {/* 地址信息 */}
          <div className="bg-system-gray-6 rounded-xl p-4">
            <h5 className="text-body font-sf-pro font-medium text-label mb-3">地址信息</h5>
            <div className="space-y-3">
              <div>
                <p className="text-caption-1 text-secondary-label mb-1">发送地址</p>
                <p className="text-body font-mono text-label break-all">
                  {selectedTransaction.fromAddress}
                </p>
              </div>
              <div>
                <p className="text-caption-1 text-secondary-label mb-1">接收地址</p>
                <p className="text-body font-mono text-label break-all">
                  {selectedTransaction.toAddress}
                </p>
              </div>
            </div>
          </div>

          {/* 交易哈希 */}
          {selectedTransaction.txHash && (
            <div className="bg-system-gray-6 rounded-xl p-4">
              <div className="flex items-center justify-between mb-2">
                <p className="text-caption-1 text-secondary-label">交易哈希</p>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    const explorerUrl = `https://bscscan.com/tx/${selectedTransaction.txHash}`;
                    window.open(explorerUrl, '_blank');
                  }}
                >
                  <Icon icon={ActionIcons.externalLink} size="sm" />
                  <span>查看</span>
                </Button>
              </div>
              <p className="text-body font-mono text-label break-all">
                {selectedTransaction.txHash}
              </p>
            </div>
          )}

          {/* Gas费用 */}
          {selectedTransaction.gasFee && (
            <div className="bg-system-gray-6 rounded-xl p-4">
              <div className="flex justify-between items-center">
                <span className="text-caption-1 text-secondary-label">Gas费用</span>
                <span className="text-body text-label">
                  {formatBalance(selectedTransaction.gasFee, 6)} BNB
                </span>
              </div>
            </div>
          )}
        </div>
      </Card>
    );
  }

  return (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-title-2 font-sf-pro font-bold text-label">
          交易历史
        </h3>
        {onClose && (
          <Button variant="ghost" size="sm" onClick={onClose}>
            <Icon icon={ActionIcons.close} size="sm" />
          </Button>
        )}
      </div>

      {/* 筛选器 */}
      <div className="mb-6">
        <div className="flex space-x-2 overflow-x-auto pb-2">
          {[
            { value: 'all', label: '全部' },
            { value: 'transfer', label: '转账' },
            { value: 'withdrawal', label: '提现' },
            { value: 'deposit', label: '充值' },
            { value: 'reward_claim', label: '奖励' },
          ].map((filterOption) => (
            <button
              key={filterOption.value}
              onClick={() => setFilter(filterOption.value as any)}
              className={cn(
                'px-4 py-2 rounded-lg text-body font-sf-pro font-medium transition-colors whitespace-nowrap',
                filter === filterOption.value
                  ? 'bg-system-blue text-white'
                  : 'bg-system-gray-6 text-secondary-label hover:bg-system-gray-5'
              )}
            >
              {filterOption.label}
            </button>
          ))}
        </div>
      </div>

      {/* 交易列表 */}
      <div className="space-y-3">
        {isLoading && transactions.length === 0 ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-system-blue mx-auto mb-4"></div>
            <p className="text-body text-secondary-label">加载交易记录中...</p>
          </div>
        ) : transactions.length === 0 ? (
          <div className="text-center py-12">
            <Icon icon={FeatureIcons.history} size="2xl" color="muted" className="mx-auto mb-4" />
            <h4 className="text-title-3 font-sf-pro font-semibold text-label mb-2">
              暂无交易记录
            </h4>
            <p className="text-body text-secondary-label">
              您还没有任何交易记录
            </p>
          </div>
        ) : (
          <>
            {transactions.map((transaction) => (
              <motion.div
                key={transaction.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-system-gray-6 rounded-xl p-4 cursor-pointer hover:bg-system-gray-5 transition-colors"
                onClick={() => setSelectedTransaction(transaction)}
              >
                <div className="flex items-center space-x-3">
                  <div className={cn(
                    'w-10 h-10 rounded-full flex items-center justify-center',
                    transaction.type === 'reward_claim' ? 'bg-system-green/20' : 'bg-system-blue/20'
                  )}>
                    <Icon 
                      icon={getTransactionIcon(transaction)} 
                      size="md" 
                      color={getTransactionColor(transaction) as any} 
                    />
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <p className="text-body font-sf-pro font-medium text-label">
                        {getTypeText(transaction.type)}
                      </p>
                      <p className="text-body font-sf-pro font-medium text-label">
                        {formatTransactionAmount(transaction)}
                      </p>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <p className="text-caption-1 text-secondary-label">
                        {transaction.createdAt.toLocaleDateString()} {transaction.createdAt.toLocaleTimeString()}
                      </p>
                      <p className={cn('text-caption-1 font-sf-pro font-medium', getStatusColor(transaction.status))}>
                        {getStatusText(transaction.status)}
                      </p>
                    </div>
                  </div>
                  
                  <Icon icon={ActionIcons.chevronRight} size="sm" color="muted" />
                </div>
              </motion.div>
            ))}

            {/* 加载更多按钮 */}
            {hasMore && (
              <div className="text-center pt-4">
                <Button
                  variant="secondary"
                  size="md"
                  onClick={loadMore}
                  disabled={isLoading}
                >
                  {isLoading ? '加载中...' : '加载更多'}
                </Button>
              </div>
            )}
          </>
        )}
      </div>
    </Card>
  );
};

export default TransactionHistory;
