'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { Button, Icon, Card } from '@/components/ui';
import { ActionIcons, FinanceIcons, FeatureIcons } from '@/config/icons';
import { useTelegramAuth } from '@/hooks/useTelegramAuth';
import { useWallet } from '@/hooks/useWallet';
import { cn } from '@/lib/utils';
import QRCode from 'qrcode';

interface ReceiveFormProps {
  onClose?: () => void;
}

const ReceiveForm: React.FC<ReceiveFormProps> = ({ onClose }) => {
  const { user } = useTelegramAuth();
  const { balance, walletAddress } = useWallet();
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');
  const [selectedToken, setSelectedToken] = useState<'HAOX' | 'BNB'>('HAOX');
  const [amount, setAmount] = useState<string>('');
  const [note, setNote] = useState<string>('');
  const [copied, setCopied] = useState(false);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // 使用实际钱包地址
  const receiveAddress = walletAddress || '******************************************';

  /**
   * 生成收款二维码
   */
  useEffect(() => {
    const generateQRCode = async () => {
      try {
        let qrData = receiveAddress;

        // 如果指定了金额，生成包含金额的二维码
        if (amount && parseFloat(amount) > 0) {
          const paymentData = {
            address: receiveAddress,
            amount: amount,
            token: selectedToken,
            note: note || undefined,
          };
          qrData = JSON.stringify(paymentData);
        }

        const qrCodeDataUrl = await QRCode.toDataURL(qrData, {
          width: 256,
          margin: 2,
          color: {
            dark: '#000000',
            light: '#FFFFFF',
          },
        });

        setQrCodeUrl(qrCodeDataUrl);
      } catch (error) {
        console.error('Failed to generate QR code:', error);
      }
    };

    generateQRCode();
  }, [receiveAddress, amount, selectedToken, note]);

  /**
   * 复制钱包地址
   */
  const copyAddress = async () => {
    try {
      await navigator.clipboard.writeText(receiveAddress);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy address:', error);
    }
  };

  /**
   * 分享收款信息
   */
  const sharePaymentInfo = async () => {
    const shareData = {
      title: 'SocioMint 收款',
      text: `请向我的钱包地址发送 ${selectedToken}${amount ? ` (${amount} ${selectedToken})` : ''}`,
      url: `https://sociomint.app/pay?address=${receiveAddress}&token=${selectedToken}&amount=${amount}&note=${encodeURIComponent(note)}`,
    };

    if (navigator.share) {
      try {
        await navigator.share(shareData);
      } catch (error) {
        console.error('Failed to share:', error);
      }
    } else {
      // 降级到复制链接
      await navigator.clipboard.writeText(shareData.url);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  /**
   * 下载二维码
   */
  const downloadQRCode = () => {
    if (qrCodeUrl) {
      const link = document.createElement('a');
      link.download = `sociomint-receive-${selectedToken}-${Date.now()}.png`;
      link.href = qrCodeUrl;
      link.click();
    }
  };

  return (
    <Card className="p-6 max-w-md mx-auto">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-title-2 font-sf-pro font-bold text-label">
          收款
        </h3>
        {onClose && (
          <Button variant="ghost" size="sm" onClick={onClose}>
            <Icon icon={ActionIcons.close} size="sm" />
          </Button>
        )}
      </div>

      {/* 代币选择 */}
      <div className="mb-6">
        <label className="block text-body font-sf-pro font-medium text-label mb-3">
          选择代币
        </label>
        <div className="grid grid-cols-2 gap-3">
          {['HAOX', 'BNB'].map((token) => (
            <button
              key={token}
              onClick={() => setSelectedToken(token as 'HAOX' | 'BNB')}
              className={cn(
                'p-3 rounded-xl border-2 transition-colors',
                selectedToken === token
                  ? 'border-system-blue bg-system-blue/10'
                  : 'border-system-gray-4 hover:border-system-gray-3'
              )}
            >
              <div className="flex items-center space-x-2">
                <Icon 
                  icon={token === 'HAOX' ? FinanceIcons.coins : FinanceIcons.barChart} 
                  size="md" 
                  color={selectedToken === token ? 'primary' : 'muted'} 
                />
                <span className="text-body font-sf-pro font-medium text-label">
                  {token}
                </span>
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* 金额输入（可选） */}
      <div className="mb-6">
        <label className="block text-body font-sf-pro font-medium text-label mb-2">
          金额 (可选)
        </label>
        <div className="relative">
          <input
            type="number"
            value={amount}
            onChange={(e) => setAmount(e.target.value)}
            placeholder="输入收款金额"
            step="any"
            min="0"
            className="w-full px-4 py-3 rounded-xl border-2 border-system-gray-4 bg-system-background text-body focus:border-system-blue transition-colors"
          />
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <span className="text-body text-secondary-label">
              {selectedToken}
            </span>
          </div>
        </div>
      </div>

      {/* 备注输入（可选） */}
      <div className="mb-6">
        <label className="block text-body font-sf-pro font-medium text-label mb-2">
          备注 (可选)
        </label>
        <input
          type="text"
          value={note}
          onChange={(e) => setNote(e.target.value)}
          placeholder="添加收款备注"
          maxLength={50}
          className="w-full px-4 py-3 rounded-xl border-2 border-system-gray-4 bg-system-background text-body focus:border-system-blue transition-colors"
        />
      </div>

      {/* 二维码显示 */}
      <div className="mb-6">
        <div className="bg-white p-4 rounded-xl border-2 border-system-gray-4 text-center">
          {qrCodeUrl ? (
            <img
              src={qrCodeUrl}
              alt="收款二维码"
              className="w-48 h-48 mx-auto"
            />
          ) : (
            <div className="w-48 h-48 mx-auto bg-system-gray-6 rounded-lg flex items-center justify-center">
              <Icon icon={FinanceIcons.qrCode} size="2xl" color="muted" />
            </div>
          )}
          
          <div className="mt-4">
            <p className="text-caption-1 text-secondary-label mb-2">
              扫描二维码或复制地址进行转账
            </p>
            {amount && (
              <p className="text-body font-sf-pro font-medium text-label">
                收款金额: {amount} {selectedToken}
              </p>
            )}
            {note && (
              <p className="text-caption-1 text-secondary-label">
                备注: {note}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* 钱包地址 */}
      <div className="mb-6">
        <label className="block text-body font-sf-pro font-medium text-label mb-2">
          钱包地址
        </label>
        <div className="flex items-center space-x-2">
          <div className="flex-1 px-4 py-3 bg-system-gray-6 rounded-xl">
            <p className="text-body font-mono text-label break-all">
              {receiveAddress}
            </p>
          </div>
          <Button
            variant="secondary"
            size="md"
            onClick={copyAddress}
            className="flex-shrink-0"
          >
            <Icon 
              icon={copied ? FeatureIcons.checkCircle : ActionIcons.copy} 
              size="sm" 
              color={copied ? 'success' : 'muted'} 
            />
          </Button>
        </div>
        {copied && (
          <p className="text-caption-1 text-system-green mt-1">
            地址已复制到剪贴板
          </p>
        )}
      </div>

      {/* 操作按钮 */}
      <div className="grid grid-cols-2 gap-3">
        <Button
          variant="secondary"
          size="md"
          onClick={downloadQRCode}
          disabled={!qrCodeUrl}
        >
          <Icon icon={ActionIcons.download} size="sm" />
          <span>保存二维码</span>
        </Button>
        <Button
          variant="primary"
          size="md"
          onClick={sharePaymentInfo}
        >
          <Icon icon={ActionIcons.share} size="sm" />
          <span>分享收款</span>
        </Button>
      </div>

      {/* 安全提示 */}
      <div className="mt-6 p-4 bg-system-yellow/10 border border-system-yellow/20 rounded-xl">
        <div className="flex items-start space-x-2">
          <Icon icon={FeatureIcons.alert} size="sm" color="warning" className="mt-0.5" />
          <div>
            <p className="text-caption-1 text-label font-medium mb-1">
              安全提示
            </p>
            <ul className="text-caption-1 text-secondary-label space-y-1">
              <li>• 请确认发送方使用正确的网络 (BSC)</li>
              <li>• 只接收 {selectedToken} 代币到此地址</li>
              <li>• 小额测试后再进行大额转账</li>
            </ul>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default ReceiveForm;
