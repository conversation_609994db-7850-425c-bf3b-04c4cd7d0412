'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Button, Icon } from '@/components/ui';
import { FinanceIcons, ActionIcons, NavigationIcons } from '@/config/icons';
import { cn } from '@/lib/utils';

interface WalletConnectProps {
  className?: string;
  onConnect?: (address: string) => void;
  onError?: (error: string) => void;
}

const WalletConnect: React.FC<WalletConnectProps> = ({
  className,
  onConnect,
  onError
}) => {
  const [isConnecting, setIsConnecting] = useState(false);
  const [selectedWallet, setSelectedWallet] = useState<string | null>(null);

  // 模拟钱包连接
  const handleConnect = async (walletType: string) => {
    setIsConnecting(true);
    setSelectedWallet(walletType);

    try {
      // 模拟连接延迟
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 模拟成功连接
      const mockAddress = '******************************************';
      onConnect?.(mockAddress);
      
      console.log(`Connected to ${walletType} wallet`);
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '连接失败';
      onError?.(errorMessage);
      console.error('Wallet connection failed:', error);
    } finally {
      setIsConnecting(false);
      setSelectedWallet(null);
    }
  };

  const wallets = [
    {
      id: 'metamask',
      name: 'MetaMask',
      description: '连接到 MetaMask 钱包',
      icon: FinanceIcons.wallet,
      color: 'text-orange-500',
      bgColor: 'bg-orange-50 hover:bg-orange-100',
      borderColor: 'border-orange-200',
    },
    {
      id: 'walletconnect',
      name: 'WalletConnect',
      description: '扫码连接移动端钱包',
      icon: FinanceIcons.wallet,
      color: 'text-blue-500',
      bgColor: 'bg-blue-50 hover:bg-blue-100',
      borderColor: 'border-blue-200',
    },
    {
      id: 'coinbase',
      name: 'Coinbase Wallet',
      description: '连接到 Coinbase 钱包',
      icon: FinanceIcons.wallet,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50 hover:bg-blue-100',
      borderColor: 'border-blue-200',
    },
  ];

  return (
    <div className={cn('space-y-4', className)}>
      <div className="text-center mb-6">
        <h3 className="text-lg font-semibold text-label mb-2">
          连接钱包
        </h3>
        <p className="text-sm text-secondary-label">
          选择您的钱包来连接到 SocioMint
        </p>
      </div>

      <div className="grid gap-3">
        {wallets.map((wallet) => (
          <motion.button
            key={wallet.id}
            onClick={() => handleConnect(wallet.id)}
            disabled={isConnecting}
            className={cn(
              'w-full p-4 rounded-xl border-2 transition-all duration-200',
              'flex items-center space-x-4 text-left',
              wallet.bgColor,
              wallet.borderColor,
              isConnecting && selectedWallet !== wallet.id && 'opacity-50',
              isConnecting ? 'cursor-not-allowed' : 'cursor-pointer hover:shadow-md'
            )}
            whileHover={!isConnecting ? { scale: 1.02 } : {}}
            whileTap={!isConnecting ? { scale: 0.98 } : {}}
          >
            <div className={cn(
              'w-12 h-12 rounded-xl flex items-center justify-center',
              'bg-white shadow-sm'
            )}>
              {isConnecting && selectedWallet === wallet.id ? (
                <div className="w-6 h-6 border-2 border-current border-t-transparent rounded-full animate-spin" />
              ) : (
                <Icon icon={wallet.icon} size="lg" className={wallet.color} />
              )}
            </div>

            <div className="flex-1">
              <h4 className="font-semibold text-label">
                {wallet.name}
              </h4>
              <p className="text-sm text-secondary-label">
                {wallet.description}
              </p>
            </div>

            {!isConnecting && (
              <Icon 
                icon={NavigationIcons.chevronRight} 
                size="md" 
                className="text-secondary-label" 
              />
            )}
          </motion.button>
        ))}
      </div>

      <div className="mt-6 p-4 bg-system-yellow/10 border border-system-yellow/20 rounded-xl">
        <div className="flex items-start space-x-3">
          <Icon icon={ActionIcons.info} size="md" className="text-system-yellow mt-0.5" />
          <div>
            <h4 className="text-sm font-semibold text-system-yellow mb-1">
              安全提醒
            </h4>
            <p className="text-sm text-secondary-label">
              请确保您使用的是官方钱包应用，并妥善保管您的私钥和助记词。
            </p>
          </div>
        </div>
      </div>

      {process.env.NODE_ENV === 'development' && (
        <div className="mt-4 p-3 bg-system-blue/10 border border-system-blue/20 rounded-lg">
          <p className="text-sm text-system-blue">
            <strong>开发模式:</strong> 这是模拟的钱包连接，实际部署时会连接真实的钱包。
          </p>
        </div>
      )}
    </div>
  );
};

export default WalletConnect;