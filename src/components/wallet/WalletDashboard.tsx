'use client';

import React, { useState, useEffect } from 'react';
import { Button, Icon, Card } from '@/components/ui';
import { FinanceIcons, ActionIcons, UserIcons, FeatureIcons, RewardIcons, MiscIcons, NotificationIcons } from '@/config/icons';
import { useTelegramAuth } from '@/hooks/useTelegramAuth';
import { useWallet } from '@/hooks/useWallet';
import { useRewards } from '@/hooks/useRewards';
import TransferForm from '@/components/wallet/TransferForm';
import ReceiveForm from '@/components/wallet/ReceiveForm';
import WithdrawForm from '@/components/wallet/WithdrawForm';
import TransactionHistory from '@/components/wallet/TransactionHistory';
import PancakeSwapIntegration from '@/components/pancakeswap/PancakeSwapIntegration';
import { cn } from '@/lib/utils';

interface Transaction {
  id: string;
  type: 'transfer' | 'withdrawal' | 'deposit' | 'reward_claim';
  amount: string;
  status: 'pending' | 'confirmed' | 'failed';
  timestamp: Date;
  txHash?: string;
}

const WalletDashboard: React.FC = () => {
  const { user, isAuthenticated } = useTelegramAuth();
  const {
    balance,
    isLoadingBalance: isLoading,
    error,
    refreshBalance,
  } = useWallet();

  // 格式化函数
  const formatBalance = (amount: string, decimals: number = 2): string => {
    const num = parseFloat(amount || '0');
    return num.toFixed(decimals);
  };

  const formatUSD = (amount: number): string => {
    return `$${amount.toFixed(2)}`;
  };

  // 计算USD价值（模拟）
  const estimatedUsdValue = balance
    ? parseFloat(balance.haoxBalance) * 0.1 + parseFloat(balance.bnbBalance) * 300
    : 0;
  const {
    pendingRewards,
    totalPendingAmount,
    isClaiming,
    claimReward,
    claimAllRewards,
    processDailySignIn
  } = useRewards();

  const isClaimingAll = isClaiming;
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [activeTab, setActiveTab] = useState<'overview' | 'transfer' | 'receive' | 'withdraw' | 'history' | 'trade'>('overview');

  // 模拟交易数据加载
  useEffect(() => {
    if (isAuthenticated && user) {
      // 模拟交易历史数据
      setTransactions([
        {
          id: '1',
          type: 'reward_claim',
          amount: '100.00',
          status: 'confirmed',
          timestamp: new Date(Date.now() - 3600000),
          txHash: '0x1234...5678',
        },
        {
          id: '2',
          type: 'transfer',
          amount: '50.00',
          status: 'confirmed',
          timestamp: new Date(Date.now() - 7200000),
          txHash: '0x5678...9abc',
        },
      ]);
    }
  }, [isAuthenticated, user]);

  if (!isAuthenticated) {
    return (
      <div className="text-center py-12">
        <Icon icon={UserIcons.user} size="2xl" color="muted" className="mx-auto mb-4" />
        <h3 className="text-title-3 font-sf-pro font-semibold text-label mb-2">请先登录</h3>
        <p className="text-body text-secondary-label">使用Telegram账号登录以查看您的钱包</p>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="text-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-system-blue mx-auto mb-4"></div>
        <p className="text-body text-secondary-label">加载钱包信息中...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <Icon icon={ActionIcons.alert} size="2xl" color="error" className="mx-auto mb-4" />
        <h3 className="text-title-3 font-sf-pro font-semibold text-label mb-2">加载失败</h3>
        <p className="text-body text-secondary-label mb-4">{error}</p>
        <Button onClick={refreshBalance} variant="primary" size="sm">
          重试
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 钱包概览 */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-title-2 font-sf-pro font-bold text-label">我的钱包</h2>
            <p className="text-caption-1 text-secondary-label">
              欢迎，{user?.firstName}！
            </p>
          </div>
          <div className="w-12 h-12 rounded-full bg-system-blue/10 flex items-center justify-center">
            <Icon icon={FinanceIcons.wallet} size="lg" color="primary" />
          </div>
        </div>

        {/* 余额显示 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-gradient-to-r from-system-blue/10 to-system-purple/10 rounded-xl p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-caption-1 text-secondary-label">HAOX余额</span>
              <Icon icon={FinanceIcons.coins} size="sm" color="primary" />
            </div>
            <p className="text-title-3 font-sf-pro font-bold text-label">
              {balance ? formatBalance(balance.haoxBalance) : '0'}
            </p>
          </div>

          <div className="bg-gradient-to-r from-system-orange/10 to-system-yellow/10 rounded-xl p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-caption-1 text-secondary-label">BNB余额</span>
              <Icon icon={FinanceIcons.barChart} size="sm" color="warning" />
            </div>
            <p className="text-title-3 font-sf-pro font-bold text-label">
              {balance ? formatBalance(balance.bnbBalance, 6) : '0'}
            </p>
          </div>

          <div className="bg-gradient-to-r from-system-green/10 to-system-teal/10 rounded-xl p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-caption-1 text-secondary-label">估值 (USD)</span>
              <Icon icon={FinanceIcons.dollar} size="sm" color="success" />
            </div>
            <p className="text-title-3 font-sf-pro font-bold text-label">
              {formatUSD(estimatedUsdValue)}
            </p>
          </div>
        </div>

        {/* 待提现余额 */}
        {balance && (parseFloat(balance.pendingHaoxBalance) > 0 || parseFloat(balance.pendingBnbBalance) > 0) && (
          <div className="bg-system-blue/10 rounded-xl p-4 mb-6">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-2">
                <Icon icon={FinanceIcons.wallet} size="sm" color="primary" />
                <span className="text-body font-sf-pro font-medium text-label">
                  待提现余额
                </span>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              {parseFloat(balance.pendingHaoxBalance) > 0 && (
                <div>
                  <p className="text-caption-1 text-secondary-label mb-1">福气</p>
                  <p className="text-body font-sf-pro font-semibold text-label">
                    {formatBalance(balance.pendingHaoxBalance)} 福气
                  </p>
                </div>
              )}
              {parseFloat(balance.pendingBnbBalance) > 0 && (
                <div>
                  <p className="text-caption-1 text-secondary-label mb-1">BNB</p>
                  <p className="text-body font-sf-pro font-semibold text-label">
                    {formatBalance(balance.pendingBnbBalance, 6)} BNB
                  </p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* 待领取奖励 */}
        {pendingRewards.length > 0 && (
          <div className="bg-system-yellow/10 rounded-xl p-4 mb-6">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-2">
                <Icon icon={RewardIcons.gift} size="sm" color="warning" />
                <span className="text-body font-sf-pro font-medium text-label">
                  待领取奖励
                </span>
                <span className="text-caption-1 text-secondary-label">
                  (总计: {formatBalance(totalPendingAmount)} 福气)
                </span>
              </div>
              <Button
                size="sm"
                variant="primary"
                onClick={async () => {
                  const result = await claimAllRewards();
                  if (result.success) {
                    // 刷新余额以反映新的待提现金额
                    await refreshBalance();
                  }
                }}
                disabled={isClaimingAll || pendingRewards.length === 0}
              >
                {isClaimingAll ? '领取中...' : '全部领取'}
              </Button>
            </div>
            <div className="space-y-2">
              {pendingRewards.map((reward) => (
                <div key={reward.id} className="flex items-center justify-between p-2 bg-system-gray-6 rounded-lg">
                  <div className="flex-1">
                    <span className="text-body text-label block">
                      {reward.description}
                    </span>
                    <span className="text-caption-1 text-secondary-label">
                      {reward.createdAt.toLocaleDateString()}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-body font-sf-pro font-medium text-label">
                      +{formatBalance(reward.amount)} 福气
                    </span>
                    <Button
                      size="sm"
                      variant="primary"
                      onClick={() => claimReward(reward.id)}
                      disabled={isClaiming}
                    >
                      {isClaiming ? '...' : '领取'}
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </Card>

      {/* 功能标签页 */}
      <Card className="p-6">
        <div className="flex space-x-1 mb-6 bg-system-gray-6 rounded-lg p-1">
          {[
            { id: 'overview', label: '概览', icon: FinanceIcons.barChart },
            { id: 'transfer', label: '转账', icon: NotificationIcons.send },
            { id: 'receive', label: '收款', icon: MiscIcons.qrCode },
            { id: 'withdraw', label: '提现', icon: ActionIcons.download },
            { id: 'trade', label: '交易', icon: FinanceIcons.activity },
            { id: 'history', label: '历史', icon: FeatureIcons.history },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={cn(
                'flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md transition-colors',
                activeTab === tab.id
                  ? 'bg-system-background text-label shadow-sm'
                  : 'text-secondary-label hover:text-label'
              )}
            >
              <Icon icon={tab.icon} size="sm" />
              <span className="text-body font-sf-pro font-medium">{tab.label}</span>
            </button>
          ))}
        </div>

        {/* 标签页内容 */}
        <div className="min-h-[300px]">
          {activeTab === 'overview' && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-title-3 font-sf-pro font-semibold text-label mb-4">
                  快速操作
                </h3>
                <div className="space-y-3">
                  <Button
                    variant="primary"
                    size="md"
                    className="w-full justify-start"
                    onClick={processDailySignIn}
                  >
                    <Icon icon={RewardIcons.gift} size="sm" />
                    <span>每日签到</span>
                  </Button>
                  <Button
                    variant="primary"
                    size="md"
                    className="w-full justify-start"
                    onClick={() => setActiveTab('transfer')}
                  >
                    <Icon icon={NotificationIcons.send} size="sm" />
                    <span>发送福气</span>
                  </Button>
                  <Button
                    variant="secondary"
                    size="md"
                    className="w-full justify-start"
                    onClick={() => setActiveTab('withdraw')}
                  >
                    <Icon icon={ActionIcons.download} size="sm" />
                    <span>提现到外部钱包</span>
                  </Button>
                  <Button
                    variant="secondary"
                    size="md"
                    className="w-full justify-start"
                    onClick={() => setActiveTab('receive')}
                  >
                    <Icon icon={MiscIcons.qrCode} size="sm" />
                    <span>生成收款码</span>
                  </Button>
                  <Button
                    variant="secondary"
                    size="md"
                    className="w-full justify-start"
                    onClick={() => setActiveTab('trade')}
                  >
                    <Icon icon={FinanceIcons.activity} size="sm" />
                    <span>PancakeSwap交易</span>
                  </Button>
                </div>
              </div>
              
              <div>
                <h3 className="text-title-3 font-sf-pro font-semibold text-label mb-4">
                  最近交易
                </h3>
                <div className="space-y-3">
                  {transactions.slice(0, 3).map((tx) => (
                    <div key={tx.id} className="flex items-center justify-between p-3 bg-system-gray-6 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className={cn(
                          'w-8 h-8 rounded-full flex items-center justify-center',
                          tx.type === 'reward_claim' ? 'bg-system-green/20' : 'bg-system-blue/20'
                        )}>
                          <Icon
                            icon={tx.type === 'reward_claim' ? RewardIcons.gift : NotificationIcons.send}
                            size="sm"
                            color={tx.type === 'reward_claim' ? 'success' : 'primary'}
                          />
                        </div>
                        <div>
                          <p className="text-body font-sf-pro font-medium text-label">
                            {tx.type === 'reward_claim' ? '奖励领取' : '转账'}
                          </p>
                          <p className="text-caption-1 text-secondary-label">
                            {tx.timestamp.toLocaleTimeString()}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-body font-sf-pro font-medium text-label">
                          +{tx.amount} HAOX
                        </p>
                        <p className={cn(
                          'text-caption-1',
                          tx.status === 'confirmed' ? 'text-system-green' : 'text-system-orange'
                        )}>
                          {tx.status === 'confirmed' ? '已确认' : '处理中'}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'transfer' && (
            <TransferForm
              onSuccess={() => {
                setActiveTab('overview');
                refreshBalance();
              }}
            />
          )}

          {activeTab === 'receive' && (
            <ReceiveForm
              onClose={() => setActiveTab('overview')}
            />
          )}

          {activeTab === 'withdraw' && (
            <WithdrawForm
              onSuccess={() => {
                setActiveTab('overview');
                refreshBalance();
              }}
              onCancel={() => setActiveTab('overview')}
            />
          )}

          {activeTab === 'trade' && (
            <PancakeSwapIntegration />
          )}

          {activeTab === 'history' && (
            <TransactionHistory
              onClose={() => setActiveTab('overview')}
            />
          )}
        </div>
      </Card>
    </div>
  );
};

export default WalletDashboard;
