'use client';

import React, { useState, useEffect, useCallback, useMemo, memo } from 'react';
import { motion } from 'framer-motion';
import { Button, Icon, Card } from '@/components/ui';
import { ActionIcons, FinanceIcons, FeatureIcons } from '@/config/icons';
import { useTelegramAuth } from '@/hooks/useTelegramAuth';
import { useWallet } from '@/hooks/useWallet';
import { cn } from '@/lib/utils';
import { ethers } from 'ethers';

interface TransferFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

const TransferForm: React.FC<TransferFormProps> = memo(({ onSuccess, onCancel }) => {
  const { user } = useTelegramAuth();
  const {
    balance,
    transfer,
    isTransferring,
    error: transferError
  } = useWallet();

  // 格式化函数
  const formatBalance = useCallback((amount: string, decimals: number = 2): string => {
    const num = parseFloat(amount || '0');
    return num.toFixed(decimals);
  }, []);

  // Gas费估算函数
  const estimateGasFee = useCallback(async (toAddress: string, amount: string): Promise<string> => {
    try {
      // 模拟Gas费估算
      const baseGas = 0.001; // 基础Gas费
      const amountNum = parseFloat(amount);
      const estimatedGas = baseGas + (amountNum * 0.0001);
      return estimatedGas.toFixed(6);
    } catch (error) {
      console.error('Gas estimation failed:', error);
      return '0.001';
    }
  }, []);

  const [formData, setFormData] = useState({
    toAddress: '',
    amount: '',
    tokenType: 'HAOX' as 'HAOX' | 'BNB',
  });
  const [gasFee, setGasFee] = useState<string>('0');
  const [isValidAddress, setIsValidAddress] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  /**
   * 验证钱包地址
   */
  useEffect(() => {
    if (formData.toAddress) {
      const isValid = ethers.isAddress(formData.toAddress);
      setIsValidAddress(isValid);
      
      if (!isValid && formData.toAddress.length > 10) {
        setErrors(prev => ({ ...prev, toAddress: '无效的钱包地址' }));
      } else {
        setErrors(prev => ({ ...prev, toAddress: '' }));
      }
    } else {
      setIsValidAddress(false);
      setErrors(prev => ({ ...prev, toAddress: '' }));
    }
  }, [formData.toAddress]);

  /**
   * 估算Gas费用
   */
  useEffect(() => {
    const estimateGas = async () => {
      if (isValidAddress && formData.amount && parseFloat(formData.amount) > 0) {
        try {
          const estimate = await estimateGasFee({
            to: formData.toAddress,
            amount: formData.amount,
            tokenType: formData.tokenType,
          });
          setGasFee(estimate.standard.cost);
        } catch (error) {
          console.error('Failed to estimate gas fee:', error);
          setGasFee('0.001'); // 默认值
        }
      }
    };

    estimateGas();
  }, [formData.toAddress, formData.amount, formData.tokenType, isValidAddress, estimateGasFee]);

  /**
   * 验证表单
   */
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.toAddress) {
      newErrors.toAddress = '请输入接收地址';
    } else if (!isValidAddress) {
      newErrors.toAddress = '无效的钱包地址';
    }

    if (!formData.amount) {
      newErrors.amount = '请输入转账金额';
    } else {
      const amount = parseFloat(formData.amount);
      if (isNaN(amount) || amount <= 0) {
        newErrors.amount = '请输入有效的金额';
      } else if (balance) {
        const availableBalance = parseFloat(
          formData.tokenType === 'HAOX' ? balance.haoxBalance : balance.bnbBalance
        );
        if (amount > availableBalance) {
          newErrors.amount = `余额不足，可用余额: ${formatBalance(availableBalance.toString())}`;
        }
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  /**
   * 处理表单提交
   */
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateForm()) {
      setShowConfirmation(true);
    }
  };

  /**
   * 确认转账
   */
  const handleConfirmTransfer = async () => {
    try {
      const result = await transfer({
        toAddress: formData.toAddress,
        amount: formData.amount,
        tokenType: formData.tokenType,
      });

      if (result.success) {
        setShowConfirmation(false);
        onSuccess?.();
      }
    } catch (error) {
      console.error('Transfer failed:', error);
    }
  };

  /**
   * 设置最大金额
   */
  const setMaxAmount = () => {
    if (balance) {
      const maxAmount = formData.tokenType === 'HAOX' 
        ? balance.haoxBalance 
        : balance.bnbBalance;
      setFormData(prev => ({ ...prev, amount: maxAmount }));
    }
  };

  if (showConfirmation) {
    return (
      <Card className="p-6">
        <div className="text-center mb-6">
          <div className="w-16 h-16 bg-system-orange/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <Icon icon={FeatureIcons.alert} size="xl" color="warning" />
          </div>
          <h3 className="text-title-2 font-sf-pro font-bold text-label mb-2">
            确认转账
          </h3>
          <p className="text-body text-secondary-label">
            请仔细核对转账信息，转账后无法撤销
          </p>
        </div>

        <div className="space-y-4 mb-6">
          <div className="bg-system-gray-6 rounded-xl p-4">
            <div className="flex justify-between items-center mb-2">
              <span className="text-caption-1 text-secondary-label">接收地址</span>
            </div>
            <p className="text-body font-mono text-label break-all">
              {formData.toAddress}
            </p>
          </div>

          <div className="bg-system-gray-6 rounded-xl p-4">
            <div className="flex justify-between items-center mb-2">
              <span className="text-caption-1 text-secondary-label">转账金额</span>
            </div>
            <p className="text-title-3 font-sf-pro font-bold text-label">
              {formatBalance(formData.amount)} {formData.tokenType}
            </p>
          </div>

          <div className="bg-system-gray-6 rounded-xl p-4">
            <div className="flex justify-between items-center">
              <span className="text-caption-1 text-secondary-label">预估Gas费</span>
              <span className="text-body text-label">
                {formatBalance(gasFee, 6)} BNB
              </span>
            </div>
          </div>
        </div>

        <div className="flex space-x-3">
          <Button
            variant="secondary"
            size="md"
            onClick={() => setShowConfirmation(false)}
            className="flex-1"
          >
            取消
          </Button>
          <Button
            variant="primary"
            size="md"
            onClick={handleConfirmTransfer}
            disabled={isTransferring}
            className="flex-1"
          >
            {isTransferring ? '转账中...' : '确认转账'}
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-title-2 font-sf-pro font-bold text-label">
          发送代币
        </h3>
        {onCancel && (
          <Button variant="ghost" size="sm" onClick={onCancel}>
            <Icon icon={ActionIcons.close} size="sm" />
          </Button>
        )}
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* 代币类型选择 */}
        <div>
          <label className="block text-body font-sf-pro font-medium text-label mb-3">
            选择代币
          </label>
          <div className="grid grid-cols-2 gap-3">
            {['HAOX', 'BNB'].map((token) => (
              <button
                key={token}
                type="button"
                onClick={() => setFormData(prev => ({ ...prev, tokenType: token as 'HAOX' | 'BNB' }))}
                className={cn(
                  'p-4 rounded-xl border-2 transition-colors',
                  formData.tokenType === token
                    ? 'border-system-blue bg-system-blue/10'
                    : 'border-system-gray-4 hover:border-system-gray-3'
                )}
              >
                <div className="flex items-center space-x-3">
                  <Icon 
                    icon={token === 'HAOX' ? FinanceIcons.coins : FinanceIcons.barChart} 
                    size="lg" 
                    color={formData.tokenType === token ? 'primary' : 'muted'} 
                  />
                  <div className="text-left">
                    <p className="text-body font-sf-pro font-medium text-label">
                      {token}
                    </p>
                    <p className="text-caption-1 text-secondary-label">
                      余额: {balance ? formatBalance(
                        token === 'HAOX' ? balance.haoxBalance : balance.bnbBalance,
                        token === 'HAOX' ? 4 : 6
                      ) : '0'}
                    </p>
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* 接收地址 */}
        <div>
          <label className="block text-body font-sf-pro font-medium text-label mb-2">
            接收地址
          </label>
          <div className="relative">
            <input
              type="text"
              value={formData.toAddress}
              onChange={(e) => setFormData(prev => ({ ...prev, toAddress: e.target.value }))}
              placeholder="输入BSC钱包地址 (0x...)"
              className={cn(
                'w-full px-4 py-3 rounded-xl border-2 bg-system-background font-mono text-body transition-colors',
                errors.toAddress
                  ? 'border-system-red focus:border-system-red'
                  : isValidAddress
                  ? 'border-system-green focus:border-system-green'
                  : 'border-system-gray-4 focus:border-system-blue'
              )}
            />
            {isValidAddress && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                <Icon icon={FeatureIcons.checkCircle} size="sm" color="success" />
              </div>
            )}
          </div>
          {errors.toAddress && (
            <p className="text-caption-1 text-system-red mt-1">{errors.toAddress}</p>
          )}
        </div>

        {/* 转账金额 */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="text-body font-sf-pro font-medium text-label">
              转账金额
            </label>
            <button
              type="button"
              onClick={setMaxAmount}
              className="text-caption-1 text-system-blue hover:text-system-blue/80 transition-colors"
            >
              最大金额
            </button>
          </div>
          <div className="relative">
            <input
              type="number"
              value={formData.amount}
              onChange={(e) => setFormData(prev => ({ ...prev, amount: e.target.value }))}
              placeholder="0.00"
              step="any"
              min="0"
              className={cn(
                'w-full px-4 py-3 rounded-xl border-2 bg-system-background text-body transition-colors',
                errors.amount
                  ? 'border-system-red focus:border-system-red'
                  : 'border-system-gray-4 focus:border-system-blue'
              )}
            />
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <span className="text-body text-secondary-label">
                {formData.tokenType}
              </span>
            </div>
          </div>
          {errors.amount && (
            <p className="text-caption-1 text-system-red mt-1">{errors.amount}</p>
          )}
        </div>

        {/* Gas费预估 */}
        {isValidAddress && formData.amount && (
          <div className="bg-system-gray-6 rounded-xl p-4">
            <div className="flex justify-between items-center">
              <span className="text-body text-secondary-label">预估Gas费</span>
              <span className="text-body text-label">
                {formatBalance(gasFee, 6)} BNB
              </span>
            </div>
          </div>
        )}

        {/* 错误信息 */}
        {transferError && (
          <div className="bg-system-red/10 border border-system-red/20 rounded-xl p-4">
            <div className="flex items-center space-x-2">
              <Icon icon={FeatureIcons.alert} size="sm" color="error" />
              <span className="text-body text-system-red">{transferError}</span>
            </div>
          </div>
        )}

        {/* 提交按钮 */}
        <Button
          type="submit"
          variant="primary"
          size="lg"
          disabled={!isValidAddress || !formData.amount || isTransferring}
          className="w-full"
        >
          <Icon icon={ActionIcons.send} size="sm" />
          <span>发送 {formData.tokenType}</span>
        </Button>
      </form>
    </Card>
  );
});

TransferForm.displayName = 'TransferForm';

export default TransferForm;
