'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, Button, Icon, Badge } from '@/components/ui';
import { ActionIcons, RewardIcons, UserIcons } from '@/config/icons';
import { cn } from '@/lib/utils';

interface LeaderboardUser {
  id: string;
  username: string;
  invitations: number;
  avatar: string;
  rank: number;
  reward: number;
  is_virtual?: boolean;
  is_current_user?: boolean;
}

interface RewardsInfo {
  total_pool: number;
  top_20_rewards: number[];
  presale_ended: boolean;
  rewards_claimable: boolean;
}

interface LeaderboardProps {
  userId?: string;
}

const Leaderboard: React.FC<LeaderboardProps> = ({ userId }) => {
  const [leaderboard, setLeaderboard] = useState<LeaderboardUser[]>([]);
  const [userRank, setUserRank] = useState<number | null>(null);
  const [rewardsInfo, setRewardsInfo] = useState<RewardsInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchLeaderboard();
  }, [userId]);

  const fetchLeaderboard = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/invitation/leaderboard?userId=${userId || 'demo'}&limit=50`);
      const data = await response.json();
      
      if (data.success) {
        setLeaderboard(data.data.leaderboard);
        setUserRank(data.data.user_rank);
        setRewardsInfo(data.data.rewards_info);
      }
    } catch (error) {
      console.error('Failed to fetch leaderboard:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return '🥇';
      case 2:
        return '🥈';
      case 3:
        return '🥉';
      default:
        return `#${rank}`;
    }
  };

  const getRankColor = (rank: number) => {
    switch (rank) {
      case 1:
        return 'text-yellow-600';
      case 2:
        return 'text-gray-500';
      case 3:
        return 'text-orange-600';
      default:
        return 'text-gray-700';
    }
  };

  if (isLoading) {
    return (
      <Card title="邀请排行榜">
        <div className="p-6 text-center">
          <p className="text-body text-secondary-label">正在加载排行榜...</p>
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* 排行榜头部信息 */}
      <Card title="邀请排行榜">
        <div className="p-6">
          {/* 奖励池信息 */}
          <div className="bg-gradient-to-r from-purple-500 to-pink-600 rounded-xl p-6 text-white mb-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-xl font-bold mb-2">排行榜奖励池</h3>
                <p className="text-purple-100 text-sm">
                  总奖励池：{rewardsInfo?.total_pool.toLocaleString()} HAOX
                </p>
                <p className="text-purple-200 text-xs mt-1">
                  前20名用户将获得丰厚奖励
                </p>
              </div>
              <Icon icon={RewardIcons.trophy} size="xl" className="text-purple-200" />
            </div>
            
            {rewardsInfo && (
              <div className="mt-4 pt-4 border-t border-purple-400/30">
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <p className="text-2xl font-bold">🥇</p>
                    <p className="text-sm">1,000,000 HAOX</p>
                  </div>
                  <div>
                    <p className="text-2xl font-bold">🥈</p>
                    <p className="text-sm">500,000 HAOX</p>
                  </div>
                  <div>
                    <p className="text-2xl font-bold">🥉</p>
                    <p className="text-sm">200,000 HAOX</p>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* 用户当前排名 */}
          {userRank && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <Icon icon={UserIcons.user} size="sm" color="primary" />
                  </div>
                  <div>
                    <p className="font-medium text-label">您的当前排名</p>
                    <p className="text-sm text-secondary-label">继续邀请好友提升排名</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-2xl font-bold text-blue-600">#{userRank}</p>
                  {userRank <= 20 && rewardsInfo && (
                    <p className="text-sm text-green-600 font-medium">
                      奖励: {rewardsInfo.top_20_rewards[userRank - 1]?.toLocaleString()} HAOX
                    </p>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* 排行榜列表 */}
          <div className="space-y-3">
            {leaderboard.slice(0, 20).map((user, index) => (
              <motion.div
                key={user.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.05 }}
                className={cn(
                  "flex items-center justify-between p-4 rounded-lg border transition-all",
                  user.is_current_user 
                    ? "bg-blue-50 border-blue-200 ring-2 ring-blue-100" 
                    : "bg-white border-gray-200 hover:border-gray-300",
                  user.is_virtual && "opacity-75"
                )}
              >
                <div className="flex items-center space-x-4">
                  {/* 排名 */}
                  <div className={cn(
                    "w-12 h-12 rounded-full flex items-center justify-center font-bold text-lg",
                    user.rank <= 3 ? "bg-gradient-to-br from-yellow-400 to-orange-500 text-white" : "bg-gray-100 text-gray-600"
                  )}>
                    {user.rank <= 3 ? getRankIcon(user.rank) : user.rank}
                  </div>

                  {/* 用户信息 */}
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center text-white text-lg">
                      {user.avatar}
                    </div>
                    <div>
                      <div className="flex items-center space-x-2">
                        <p className="font-medium text-label">{user.username}</p>
                        {user.is_virtual && (
                          <Badge variant="secondary" size="sm">虚拟</Badge>
                        )}
                        {user.is_current_user && (
                          <Badge variant="primary" size="sm">您</Badge>
                        )}
                      </div>
                      <p className="text-sm text-secondary-label">
                        {user.invitations} 次成功邀请
                      </p>
                    </div>
                  </div>
                </div>

                {/* 奖励信息 */}
                <div className="text-right">
                  {user.reward > 0 ? (
                    <>
                      <p className="font-bold text-lg text-green-600">
                        {user.reward.toLocaleString()}
                      </p>
                      <p className="text-sm text-secondary-label">HAOX</p>
                    </>
                  ) : (
                    <p className="text-sm text-secondary-label">无奖励</p>
                  )}
                </div>
              </motion.div>
            ))}
          </div>

          {/* 更多排名 */}
          {leaderboard.length > 20 && (
            <div className="mt-6 text-center">
              <p className="text-sm text-secondary-label mb-4">
                显示前20名，共 {leaderboard.length} 位参与者
              </p>
              <Button variant="outline" size="sm">
                查看完整排行榜
              </Button>
            </div>
          )}
        </div>
      </Card>

      {/* 排行榜规则 */}
      <Card title="排行榜规则">
        <div className="p-6">
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <Icon icon={ActionIcons.info} size="sm" color="primary" className="mt-0.5" />
              <div>
                <p className="text-sm font-medium text-label mb-2">排行榜说明</p>
                <ul className="text-xs text-secondary-label space-y-1">
                  <li>• 排名基于成功邀请参与预售的用户数量（≥0.1 BNB）</li>
                  <li>• 虚拟用户用于激励真实用户，不会获得实际奖励</li>
                  <li>• 预售结束后将确定最终排名并发放奖励</li>
                  <li>• 前20名用户将获得对应的HAOX代币奖励</li>
                  <li>• 排行榜每小时更新一次</li>
                </ul>
              </div>
            </div>
          </div>

          {/* 奖励分配表 */}
          <div className="mt-6">
            <h4 className="font-medium text-label mb-3">奖励分配</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 text-center">
                <p className="text-lg font-bold text-yellow-600">🥇</p>
                <p className="text-sm font-medium">第1名</p>
                <p className="text-xs text-secondary-label">1,000,000 HAOX</p>
              </div>
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-3 text-center">
                <p className="text-lg font-bold text-gray-600">🥈</p>
                <p className="text-sm font-medium">第2名</p>
                <p className="text-xs text-secondary-label">500,000 HAOX</p>
              </div>
              <div className="bg-orange-50 border border-orange-200 rounded-lg p-3 text-center">
                <p className="text-lg font-bold text-orange-600">🥉</p>
                <p className="text-sm font-medium">第3名</p>
                <p className="text-xs text-secondary-label">200,000 HAOX</p>
              </div>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 text-center">
                <p className="text-lg font-bold text-blue-600">🏅</p>
                <p className="text-sm font-medium">4-10名</p>
                <p className="text-xs text-secondary-label">100,000 HAOX</p>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default Leaderboard;
