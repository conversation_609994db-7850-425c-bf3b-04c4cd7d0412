'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, But<PERSON>, Badge, Loading } from '@/components/ui';

interface TestResult {
  scenario: string;
  success: boolean;
  message: string;
  responseTime: number;
  error?: any;
  data?: any;
}

const ErrorHandlingDemoPage: React.FC = () => {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [loading, setLoading] = useState<string | null>(null);

  const scenarios = [
    {
      key: 'success',
      name: '✅ 成功场景',
      description: '正常的API调用，返回成功响应'
    },
    {
      key: 'validation',
      name: '❌ 验证错误',
      description: '参数验证失败的场景'
    },
    {
      key: 'database',
      name: '🗄️ 数据库错误',
      description: '数据库连接或查询失败'
    },
    {
      key: 'blockchain',
      name: '⛓️ 区块链错误',
      description: '区块链服务调用失败'
    },
    {
      key: 'business-logic',
      name: '💼 业务逻辑错误',
      description: '业务规则验证失败'
    },
    {
      key: 'rate-limit',
      name: '🚦 速率限制',
      description: '请求频率超过限制'
    },
    {
      key: 'graceful-degradation',
      name: '🔄 优雅降级',
      description: '主服务失败时的降级处理'
    },
    {
      key: 'unknown',
      name: '❓ 未知错误',
      description: '未分类的错误处理'
    }
  ];

  const testScenario = async (scenario: string) => {
    setLoading(scenario);
    const startTime = Date.now();

    try {
      let url = `/api/demo/error-handling?scenario=${scenario}`;
      
      // 为验证场景添加特殊参数
      if (scenario === 'validation') {
        // 故意不传required参数来触发验证错误
        url += '&other=test';
      }

      const response = await fetch(url);
      const result = await response.json();
      const responseTime = Date.now() - startTime;

      const testResult: TestResult = {
        scenario,
        success: response.ok,
        message: result.error?.message || result.data?.message || '请求完成',
        responseTime,
        data: result.data,
        error: result.error
      };

      setTestResults(prev => [testResult, ...prev.slice(0, 9)]);
    } catch (error) {
      const testResult: TestResult = {
        scenario,
        success: false,
        message: `网络错误: ${(error as Error).message}`,
        responseTime: Date.now() - startTime,
        error: error
      };

      setTestResults(prev => [testResult, ...prev.slice(0, 9)]);
    } finally {
      setLoading(null);
    }
  };

  const testAllScenarios = async () => {
    for (const scenario of scenarios) {
      await testScenario(scenario.key);
      // 在测试之间添加短暂延迟
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-label mb-2">
          🛠️ 错误处理机制演示
        </h1>
        <p className="text-secondary-label">
          测试系统的错误处理、重试、降级和监控功能
        </p>
      </div>

      {/* 控制面板 */}
      <Card className="p-6 mb-8">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold text-label">测试控制面板</h2>
          <div className="flex space-x-3">
            <Button
              onClick={testAllScenarios}
              disabled={loading !== null}
              className="bg-blue-600 text-white"
            >
              {loading ? '测试中...' : '🚀 测试所有场景'}
            </Button>
            <Button
              onClick={clearResults}
              variant="outline"
              disabled={loading !== null}
            >
              🗑️ 清空结果
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {scenarios.map((scenario) => (
            <motion.div
              key={scenario.key}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Card className="p-4 cursor-pointer hover:shadow-md transition-shadow">
                <div className="mb-3">
                  <h3 className="font-semibold text-label text-sm mb-1">
                    {scenario.name}
                  </h3>
                  <p className="text-xs text-secondary-label">
                    {scenario.description}
                  </p>
                </div>
                <Button
                  onClick={() => testScenario(scenario.key)}
                  disabled={loading !== null}
                  size="small"
                  className="w-full"
                  variant={loading === scenario.key ? 'default' : 'outline'}
                >
                  {loading === scenario.key ? (
                    <Loading size="small" />
                  ) : (
                    '测试'
                  )}
                </Button>
              </Card>
            </motion.div>
          ))}
        </div>
      </Card>

      {/* 测试结果 */}
      <Card className="p-6">
        <h2 className="text-xl font-bold text-label mb-6">📊 测试结果</h2>
        
        {testResults.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🧪</div>
            <h3 className="text-xl font-semibold text-label mb-2">暂无测试结果</h3>
            <p className="text-secondary-label">
              点击上方按钮开始测试错误处理机制
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {testResults.map((result, index) => (
              <motion.div
                key={`${result.scenario}-${index}`}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className="border rounded-lg p-4"
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <Badge className={result.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                      {result.success ? '✅ 成功' : '❌ 失败'}
                    </Badge>
                    <span className="font-semibold text-label">
                      {scenarios.find(s => s.key === result.scenario)?.name || result.scenario}
                    </span>
                  </div>
                  
                  <div className="flex items-center space-x-4 text-sm text-secondary-label">
                    <span>⏱️ {result.responseTime}ms</span>
                    <span>🕐 {new Date().toLocaleTimeString()}</span>
                  </div>
                </div>
                
                <div className="mb-3">
                  <p className="text-sm text-label">{result.message}</p>
                </div>

                {/* 响应数据 */}
                {result.data && (
                  <div className="mb-3">
                    <h4 className="text-sm font-semibold text-label mb-2">📄 响应数据:</h4>
                    <pre className="text-xs bg-gray-100 p-3 rounded overflow-x-auto">
                      {JSON.stringify(result.data, null, 2)}
                    </pre>
                  </div>
                )}

                {/* 错误详情 */}
                {result.error && (
                  <div>
                    <h4 className="text-sm font-semibold text-label mb-2">🐛 错误详情:</h4>
                    <pre className="text-xs bg-red-50 p-3 rounded overflow-x-auto text-red-800">
                      {JSON.stringify(result.error, null, 2)}
                    </pre>
                  </div>
                )}
              </motion.div>
            ))}
          </div>
        )}
      </Card>

      {/* 错误处理机制说明 */}
      <Card className="p-6 mt-8">
        <h2 className="text-xl font-bold text-label mb-6">📚 错误处理机制说明</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="font-semibold text-label mb-3">🔧 核心功能</h3>
            <ul className="space-y-2 text-sm text-secondary-label">
              <li>• <strong>统一错误处理</strong>：标准化的错误格式和响应</li>
              <li>• <strong>自动重试机制</strong>：可配置的重试策略和退避算法</li>
              <li>• <strong>优雅降级</strong>：主服务失败时的备用方案</li>
              <li>• <strong>错误分类</strong>：按类型和严重程度分类错误</li>
              <li>• <strong>监控告警</strong>：实时监控和自动告警</li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-semibold text-label mb-3">📊 监控指标</h3>
            <ul className="space-y-2 text-sm text-secondary-label">
              <li>• <strong>响应时间</strong>：API调用的响应时间统计</li>
              <li>• <strong>错误率</strong>：按时间段统计的错误率</li>
              <li>• <strong>重试次数</strong>：自动重试的成功率统计</li>
              <li>• <strong>降级频率</strong>：优雅降级的触发频率</li>
              <li>• <strong>系统健康</strong>：整体系统健康状况</li>
            </ul>
          </div>
        </div>

        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <h4 className="font-semibold text-blue-800 mb-2">💡 最佳实践</h4>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• 使用 withErrorHandling 包装所有API路由</li>
            <li>• 为数据库和区块链操作使用专门的错误处理包装器</li>
            <li>• 实现优雅降级以提高系统可用性</li>
            <li>• 定期检查监控指标和告警</li>
            <li>• 为不同类型的错误设置合适的重试策略</li>
          </ul>
        </div>
      </Card>
    </div>
  );
};

export default ErrorHandlingDemoPage;
