'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, Button, Badge, Loading } from '@/components/ui';
import { LanguageSwitcher } from '@/components/ui/LanguageSwitcher';
import { useI18n } from '@/lib/hooks/useI18n';

interface TestResult {
  scenario: string;
  success: boolean;
  data?: any;
  error?: any;
  responseTime: number;
}

const I18nDemoPage: React.FC = () => {
  const { locale, t, tCommon, formatNumber, formatDate, formatCurrency } = useI18n();
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [loading, setLoading] = useState<string | null>(null);

  const scenarios = [
    {
      key: 'basic',
      name: '🌐 基础国际化',
      nameEn: '🌐 Basic I18n',
      description: '基础的多语言文本翻译',
      descriptionEn: 'Basic multilingual text translation'
    },
    {
      key: 'formatting',
      name: '📊 格式化功能',
      nameEn: '📊 Formatting',
      description: '数字、日期、货币格式化',
      descriptionEn: 'Number, date, and currency formatting'
    },
    {
      key: 'errors',
      name: '❌ 错误消息',
      nameEn: '❌ Error Messages',
      description: '多语言错误和成功消息',
      descriptionEn: 'Multilingual error and success messages'
    },
    {
      key: 'socialbet',
      name: '🎲 Social Bet',
      nameEn: '🎲 Social Bet',
      description: 'Social Bet 相关消息',
      descriptionEn: 'Social Bet related messages'
    },
    {
      key: 'fortune',
      name: '💰 福气中心',
      nameEn: '💰 Fortune Center',
      description: '福气系统相关消息',
      descriptionEn: 'Fortune system related messages'
    }
  ];

  const testScenario = async (scenario: string) => {
    setLoading(scenario);
    const startTime = Date.now();

    try {
      const response = await fetch(`/api/demo/i18n?scenario=${scenario}&lang=${locale}`);
      const result = await response.json();
      const responseTime = Date.now() - startTime;

      const testResult: TestResult = {
        scenario,
        success: response.ok,
        data: result.data,
        error: result.error,
        responseTime
      };

      setTestResults(prev => [testResult, ...prev.slice(0, 9)]);
    } catch (error) {
      const testResult: TestResult = {
        scenario,
        success: false,
        error: { message: (error as Error).message },
        responseTime: Date.now() - startTime
      };

      setTestResults(prev => [testResult, ...prev.slice(0, 9)]);
    } finally {
      setLoading(null);
    }
  };

  const testAllScenarios = async () => {
    for (const scenario of scenarios) {
      await testScenario(scenario.key);
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const getCurrentScenarioName = (key: string) => {
    const scenario = scenarios.find(s => s.key === key);
    if (!scenario) return key;
    return locale === 'zh' ? scenario.name : scenario.nameEn;
  };

  return (
    <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* 页面标题 */}
      <div className="mb-8 flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-label mb-2">
            {locale === 'zh' ? '🌍 国际化功能演示' : '🌍 Internationalization Demo'}
          </h1>
          <p className="text-secondary-label">
            {locale === 'zh' 
              ? '测试系统的多语言支持、格式化和本地化功能'
              : 'Test system multilingual support, formatting and localization features'
            }
          </p>
        </div>
        
        {/* 语言切换器 */}
        <LanguageSwitcher variant="dropdown" />
      </div>

      {/* 当前语言信息 */}
      <Card className="p-6 mb-8">
        <h2 className="text-xl font-bold text-label mb-4">
          {locale === 'zh' ? '📍 当前语言信息' : '📍 Current Language Info'}
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="font-semibold text-blue-800 mb-2">
              {locale === 'zh' ? '语言代码' : 'Language Code'}
            </h3>
            <p className="text-blue-600 font-mono">{locale}</p>
          </div>
          
          <div className="bg-green-50 p-4 rounded-lg">
            <h3 className="font-semibold text-green-800 mb-2">
              {locale === 'zh' ? '示例数字' : 'Sample Number'}
            </h3>
            <p className="text-green-600">{formatNumber(1234567.89)}</p>
          </div>
          
          <div className="bg-purple-50 p-4 rounded-lg">
            <h3 className="font-semibold text-purple-800 mb-2">
              {locale === 'zh' ? '示例日期' : 'Sample Date'}
            </h3>
            <p className="text-purple-600">{formatDate(new Date())}</p>
          </div>
          
          <div className="bg-orange-50 p-4 rounded-lg">
            <h3 className="font-semibold text-orange-800 mb-2">
              {locale === 'zh' ? '示例货币' : 'Sample Currency'}
            </h3>
            <p className="text-orange-600">{formatCurrency(1000.123456, 'HAOX')}</p>
          </div>
        </div>
      </Card>

      {/* 控制面板 */}
      <Card className="p-6 mb-8">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold text-label">
            {locale === 'zh' ? '测试控制面板' : 'Test Control Panel'}
          </h2>
          <div className="flex space-x-3">
            <Button
              onClick={testAllScenarios}
              disabled={loading !== null}
              className="bg-blue-600 text-white"
            >
              {loading ? 
                (locale === 'zh' ? '测试中...' : 'Testing...') : 
                (locale === 'zh' ? '🚀 测试所有场景' : '🚀 Test All Scenarios')
              }
            </Button>
            <Button
              onClick={clearResults}
              variant="outline"
              disabled={loading !== null}
            >
              {locale === 'zh' ? '🗑️ 清空结果' : '🗑️ Clear Results'}
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {scenarios.map((scenario) => (
            <motion.div
              key={scenario.key}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Card className="p-4 cursor-pointer hover:shadow-md transition-shadow">
                <div className="mb-3">
                  <h3 className="font-semibold text-label text-sm mb-1">
                    {locale === 'zh' ? scenario.name : scenario.nameEn}
                  </h3>
                  <p className="text-xs text-secondary-label">
                    {locale === 'zh' ? scenario.description : scenario.descriptionEn}
                  </p>
                </div>
                <Button
                  onClick={() => testScenario(scenario.key)}
                  disabled={loading !== null}
                  size="small"
                  className="w-full"
                  variant={loading === scenario.key ? 'default' : 'outline'}
                >
                  {loading === scenario.key ? (
                    <Loading size="small" />
                  ) : (
                    locale === 'zh' ? '测试' : 'Test'
                  )}
                </Button>
              </Card>
            </motion.div>
          ))}
        </div>
      </Card>

      {/* 测试结果 */}
      <Card className="p-6">
        <h2 className="text-xl font-bold text-label mb-6">
          {locale === 'zh' ? '📊 测试结果' : '📊 Test Results'}
        </h2>
        
        {testResults.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🧪</div>
            <h3 className="text-xl font-semibold text-label mb-2">
              {locale === 'zh' ? '暂无测试结果' : 'No Test Results'}
            </h3>
            <p className="text-secondary-label">
              {locale === 'zh' 
                ? '点击上方按钮开始测试国际化功能'
                : 'Click the buttons above to start testing internationalization features'
              }
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {testResults.map((result, index) => (
              <motion.div
                key={`${result.scenario}-${index}`}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className="border rounded-lg p-4"
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <Badge className={result.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                      {result.success ? 
                        (locale === 'zh' ? '✅ 成功' : '✅ Success') : 
                        (locale === 'zh' ? '❌ 失败' : '❌ Failed')
                      }
                    </Badge>
                    <span className="font-semibold text-label">
                      {getCurrentScenarioName(result.scenario)}
                    </span>
                  </div>
                  
                  <div className="flex items-center space-x-4 text-sm text-secondary-label">
                    <span>⏱️ {result.responseTime}ms</span>
                    <span>🕐 {new Date().toLocaleTimeString()}</span>
                  </div>
                </div>

                {/* 响应数据 */}
                {result.data && (
                  <div className="mb-3">
                    <h4 className="text-sm font-semibold text-label mb-2">
                      {locale === 'zh' ? '📄 响应数据:' : '📄 Response Data:'}
                    </h4>
                    <pre className="text-xs bg-gray-100 p-3 rounded overflow-x-auto max-h-64">
                      {JSON.stringify(result.data, null, 2)}
                    </pre>
                  </div>
                )}

                {/* 错误详情 */}
                {result.error && (
                  <div>
                    <h4 className="text-sm font-semibold text-label mb-2">
                      {locale === 'zh' ? '🐛 错误详情:' : '🐛 Error Details:'}
                    </h4>
                    <pre className="text-xs bg-red-50 p-3 rounded overflow-x-auto text-red-800">
                      {JSON.stringify(result.error, null, 2)}
                    </pre>
                  </div>
                )}
              </motion.div>
            ))}
          </div>
        )}
      </Card>
    </div>
  );
};

export default I18nDemoPage;
