'use client';

/**
 * Window 扩展提供者组件
 * 在客户端安全地扩展 window 对象
 */

import React, { useEffect } from 'react';
import { safeExtendWindow } from '@/lib/window-extensions';

interface WindowExtensionProviderProps {
  children: React.ReactNode;
}

export default function WindowExtensionProvider({ children }: WindowExtensionProviderProps) {
  useEffect(() => {
    // 安全地扩展 window 对象
    safeExtendWindow();
  }, []);

  return <>{children}</>;
}
