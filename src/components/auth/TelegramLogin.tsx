'use client';

import React, { useEffect, useState } from 'react';
import { Button, Icon } from '@/components/ui';
import { SocialIcons, ActionIcons } from '@/config/icons';
import { useTelegramAuth } from '@/hooks/useTelegramAuth';
import { cn } from '@/lib/utils';

interface TelegramLoginProps {
  onSuccess?: () => void;
  onError?: (error: string) => void;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'primary' | 'secondary' | 'outline';
  showText?: boolean;
}

declare global {
  interface Window {
    TelegramLoginWidget: {
      dataOnauth: (user: any) => void;
    };
  }
}

const TelegramLogin: React.FC<TelegramLoginProps> = ({
  onSuccess,
  onError,
  className,
  size = 'md',
  variant = 'primary',
  showText = true,
}) => {
  const { login, isLoading, error } = useTelegramAuth();
  const [isScriptLoaded, setIsScriptLoaded] = useState(false);

  const botUsername = process.env.NEXT_PUBLIC_TELEGRAM_BOT_USERNAME;

  useEffect(() => {
    // 加载Telegram登录脚本
    const script = document.createElement('script');
    script.src = 'https://telegram.org/js/telegram-widget.js?22';
    script.async = true;
    script.onload = () => setIsScriptLoaded(true);
    script.onerror = () => {
      console.error('Failed to load Telegram widget script');
      onError?.('无法加载Telegram登录组件');
    };
    
    document.head.appendChild(script);

    return () => {
      document.head.removeChild(script);
    };
  }, [onError]);

  useEffect(() => {
    if (error) {
      onError?.(error);
    }
  }, [error, onError]);

  /**
   * 处理Telegram登录
   */
  const handleTelegramLogin = async (userData: any) => {
    try {
      await login(userData);
      onSuccess?.();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '登录失败';
      onError?.(errorMessage);
    }
  };

  /**
   * 开发环境模拟登录
   */
  const handleDevelopmentLogin = () => {
    // 模拟Telegram用户数据
    const mockUser = {
      id: 123456789,
      first_name: '测试用户',
      last_name: 'Test',
      username: 'testuser',
      photo_url: '',
      auth_date: Math.floor(Date.now() / 1000),
    };

    handleTelegramLogin(mockUser);
  };

  /**
   * 手动触发Telegram登录
   */
  const handleManualLogin = () => {
    if (!botUsername) {
      onError?.('Telegram Bot配置错误');
      return;
    }

    // 从Bot Token中提取Bot ID（Token格式：bot_id:token）
    const botToken = process.env.NEXT_PUBLIC_TELEGRAM_BOT_TOKEN;
    const botId = botToken?.split(':')[0];

    if (!botId) {
      onError?.('Telegram Bot ID配置错误');
      return;
    }

    // 检查是否为开发环境
    const isDevelopment = process.env.NODE_ENV === 'development';

    if (isDevelopment) {
      // 在开发环境中，使用模拟登录
      handleDevelopmentLogin();
      return;
    }

    // 生产环境使用真实的Telegram OAuth
    const authUrl = `https://oauth.telegram.org/auth?bot_id=${botId}&origin=${encodeURIComponent(window.location.origin)}&return_to=${encodeURIComponent(window.location.href)}`;

    // 在新窗口中打开Telegram登录
    const popup = window.open(
      authUrl,
      'telegram-login',
      'width=400,height=500,scrollbars=yes,resizable=yes'
    );

    // 监听消息
    const handleMessage = (event: MessageEvent) => {
      if (event.origin !== 'https://oauth.telegram.org') {
        return;
      }

      if (event.data && event.data.type === 'telegram-auth') {
        popup?.close();
        handleTelegramLogin(event.data.user);
        window.removeEventListener('message', handleMessage);
      }
    };

    window.addEventListener('message', handleMessage);

    // 检查弹窗是否被关闭
    const checkClosed = setInterval(() => {
      if (popup?.closed) {
        clearInterval(checkClosed);
        window.removeEventListener('message', handleMessage);
      }
    }, 1000);
  };

  /**
   * 使用Telegram Web App登录
   */
  const handleWebAppLogin = () => {
    // 检查是否为开发环境
    const isDevelopment = process.env.NODE_ENV === 'development';

    if (isDevelopment) {
      // 开发环境直接使用模拟登录
      handleDevelopmentLogin();
      return;
    }

    if (typeof window !== 'undefined' && window.Telegram?.WebApp) {
      const webApp = window.Telegram.WebApp;

      // 初始化Web App
      webApp.ready();

      // 获取用户数据
      const user = webApp.initDataUnsafe?.user;
      if (user) {
        handleTelegramLogin({
          id: user.id,
          first_name: user.first_name,
          last_name: user.last_name,
          username: user.username,
          photo_url: user.photo_url,
          auth_date: Math.floor(Date.now() / 1000),
        });
      } else {
        onError?.('无法获取Telegram用户信息');
      }
    } else {
      // 回退到手动登录
      handleManualLogin();
    }
  };

  if (!botUsername) {
    return (
      <div className="text-center p-4 bg-system-red/10 rounded-lg">
        <Icon icon={ActionIcons.alert} size="lg" color="error" className="mx-auto mb-2" />
        <p className="text-body text-system-red">
          Telegram Bot配置错误
        </p>
      </div>
    );
  }

  return (
    <div className={cn('flex flex-col items-center space-y-4', className)}>
      {/* 主登录按钮 */}
      <Button
        onClick={handleWebAppLogin}
        disabled={isLoading}
        variant={variant}
        size={size}
        className="flex items-center space-x-2"
      >
        {isLoading ? (
          <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
        ) : (
          <Icon icon={SocialIcons.telegram} size="sm" />
        )}
        {showText && (
          <span>
            {isLoading ? '登录中...' : '使用Telegram登录'}
          </span>
        )}
      </Button>

      {/* 备用登录方式 */}
      {!isLoading && (
        <button
          onClick={handleManualLogin}
          className="text-caption-1 text-system-blue hover:text-system-blue/80 transition-colors"
        >
          或者在新窗口中登录
        </button>
      )}

      {/* 登录说明 */}
      <div className="text-center max-w-sm">
        <p className="text-caption-1 text-secondary-label">
          点击登录即表示您同意我们的
          <a href="/terms" className="text-system-blue hover:underline">服务条款</a>
          和
          <a href="/privacy" className="text-system-blue hover:underline">隐私政策</a>
        </p>
      </div>

      {/* 隐藏的Telegram登录组件 */}
      {isScriptLoaded && (
        <div style={{ display: 'none' }}>
          <div
            id="telegram-login"
            data-telegram-login={botUsername}
            data-size="large"
            data-auth-url={`${window.location.origin}/api/auth/telegram`}
            data-request-access="write"
          />
        </div>
      )}
    </div>
  );
};

export default TelegramLogin;
