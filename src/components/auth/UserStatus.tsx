'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button, Icon, IconButton } from '@/components/ui';
import { UserIcons, FinanceIcons, ActionIcons, NavigationIcons, FeatureIcons } from '@/config/icons';
import { useTelegramAuth } from '@/hooks/useTelegramAuth';
import TelegramLoginButton from '@/components/auth/TelegramLoginButton';
import { cn } from '@/lib/utils';

interface UserStatusProps {
  className?: string;
  showBalance?: boolean;
  variant?: 'header' | 'sidebar' | 'compact';
}

const UserStatus: React.FC<UserStatusProps> = ({
  className,
  showBalance = false,
  variant = 'header'
}) => {
  const { user, isAuthenticated, logout } = useTelegramAuth();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const handleLogout = () => {
    logout();
    setIsDropdownOpen(false);
  };

  // 未认证状态
  if (!isAuthenticated) {
    return (
      <div className={className}>
        <TelegramLoginButton />
      </div>
    );
  }

  // 紧凑模式
  if (variant === 'compact') {
    return (
      <div className={cn('flex items-center space-x-2', className)}>
        <div className="w-8 h-8 rounded-full bg-system-gray-4 flex items-center justify-center">
          <Icon icon={UserIcons.user} size="sm" color="muted" />
        </div>
        <span className="text-sm font-sf-pro font-medium text-label">
          {user?.firstName}
        </span>
      </div>
    );
  }

  // 侧边栏模式
  if (variant === 'sidebar') {
    return (
      <div className={cn('space-y-4', className)}>
        <div className="flex items-center space-x-3 p-4 bg-system-gray-6 rounded-xl">
          <div className="w-12 h-12 rounded-full bg-system-gray-4 flex items-center justify-center overflow-hidden">
            {user?.photoUrl ? (
              <img
                src={user.photoUrl}
                alt={user.firstName || 'User'}
                className="w-full h-full object-cover"
              />
            ) : (
              <Icon icon={UserIcons.user} size="lg" color="muted" />
            )}
          </div>
          <div className="flex-1">
            <p className="text-body font-sf-pro font-medium text-label">
              {user?.firstName} {user?.lastName}
            </p>
            <p className="text-caption-1 text-secondary-label">
              @{user?.username || `user${user?.id}`}
            </p>
          </div>
        </div>
      </div>
    );
  }

  // 头部模式（默认）
  return (
    <div className={cn('relative', className)}>
      <motion.button
        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
        className="flex items-center space-x-3 p-2 rounded-xl hover:bg-system-gray-6 transition-colors"
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <div className="w-10 h-10 rounded-full bg-system-gray-4 flex items-center justify-center overflow-hidden">
          {user?.photoUrl ? (
            <img
              src={user.photoUrl}
              alt={user.firstName || 'User'}
              className="w-full h-full object-cover"
            />
          ) : (
            <Icon icon={UserIcons.user} size="lg" color="muted" />
          )}
        </div>

        <div className="hidden md:block text-left">
          <p className="text-body font-sf-pro font-medium text-label">
            {user?.firstName} {user?.lastName}
          </p>
          <p className="text-caption-1 text-secondary-label">
            @{user?.username || `user${user?.id}`}
          </p>
        </div>

        <Icon
          icon={isDropdownOpen ? NavigationIcons.chevronUp : NavigationIcons.chevronDown}
          size="sm"
          color="muted"
        />
      </motion.button>

      <AnimatePresence>
        {isDropdownOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="absolute right-0 top-full mt-2 w-64 bg-system-background border border-system-gray-4 rounded-xl shadow-apple z-50"
          >
            <div className="p-4 border-b border-system-gray-4">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 rounded-full bg-system-gray-4 flex items-center justify-center overflow-hidden">
                  {user?.photoUrl ? (
                    <img
                      src={user.photoUrl}
                      alt={user.firstName || 'User'}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <Icon icon={UserIcons.user} size="lg" color="muted" />
                  )}
                </div>
                <div>
                  <p className="text-body font-sf-pro font-medium text-label">
                    {user?.firstName} {user?.lastName}
                  </p>
                  <p className="text-caption-1 text-secondary-label">
                    @{user?.username || `user${user?.id}`}
                  </p>
                </div>
              </div>
            </div>

            <div className="p-2">
              <button
                onClick={() => {
                  setIsDropdownOpen(false);
                  window.location.href = '/profile';
                }}
                className="w-full flex items-center space-x-3 p-3 rounded-lg hover:bg-system-gray-6 transition-colors text-left"
              >
                <Icon icon={UserIcons.user} size="sm" color="muted" />
                <span className="text-body text-label">个人中心</span>
              </button>

              <button
                onClick={() => {
                  setIsDropdownOpen(false);
                  window.location.href = '/settings';
                }}
                className="w-full flex items-center space-x-3 p-3 rounded-lg hover:bg-system-gray-6 transition-colors text-left"
              >
                <Icon icon={FeatureIcons.settings} size="sm" color="muted" />
                <span className="text-body text-label">设置</span>
              </button>

              <div className="border-t border-system-gray-4 my-2" />

              <button
                onClick={handleLogout}
                className="w-full flex items-center space-x-3 p-3 rounded-lg hover:bg-system-red/10 transition-colors text-left"
              >
                <Icon icon={UserIcons.logout} size="sm" color="error" />
                <span className="text-body text-system-red">退出登录</span>
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {isDropdownOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsDropdownOpen(false)}
        />
      )}
    </div>
  );
};

export default UserStatus;