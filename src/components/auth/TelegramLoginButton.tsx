'use client';

import React from 'react';
import Link from 'next/link';
import { Button, Icon } from '@/components/ui';
import { SocialIcons } from '@/config/icons';
import { cn } from '@/lib/utils';

interface TelegramLoginButtonProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'primary' | 'secondary' | 'outline';
  showText?: boolean;
  redirectTo?: string;
}

const TelegramLoginButton: React.FC<TelegramLoginButtonProps> = ({
  className,
  size = 'md',
  variant = 'primary',
  showText = true,
  redirectTo = '/profile',
}) => {
  const loginUrl = `/login${redirectTo ? `?redirect=${encodeURIComponent(redirectTo)}` : ''}`;

  return (
    <Link href={loginUrl}>
      <Button
        variant={variant}
        size={size}
        className={cn('flex items-center space-x-2', className)}
      >
        <Icon icon={SocialIcons.telegram} size="sm" />
        {showText && <span>登录</span>}
      </Button>
    </Link>
  );
};



export default TelegramLoginButton;
