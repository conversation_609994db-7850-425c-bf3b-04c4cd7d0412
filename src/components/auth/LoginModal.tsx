'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON><PERSON>, <PERSON>ton, Card, Icon } from '@/components/ui';
import { FinanceIcons, UserIcons, ActionIcons, MiscIcons } from '@/config/icons';
import { useTelegramAuth } from '@/hooks/useTelegramAuth';
import { cn } from '@/lib/utils';

interface LoginModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const LoginModal: React.FC<LoginModalProps> = ({ isOpen, onClose }) => {
  const { user, isAuthenticated, login, logout } = useTelegramAuth();
  const [isLoading, setIsLoading] = useState(false);

  const handleTelegramLogin = async () => {
    try {
      setIsLoading(true);
      await login();
      onClose();
    } catch (error) {
      console.error('Telegram login failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      onClose();
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const renderLoginContent = () => {
    if (isAuthenticated && user) {
      return (
        <div className="space-y-6">
          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-system-green to-system-blue rounded-2xl flex items-center justify-center mx-auto mb-4">
              <Icon icon={UserIcons.user} size="xl" className="text-white" />
            </div>
            <h2 className="text-title-2 font-sf-pro font-bold text-label mb-2">
              已登录
            </h2>
            <p className="text-body text-secondary-label mb-4">
              欢迎回来，{user.username || user.first_name}！
            </p>
          </div>

          <div className="p-4 bg-system-gray-6 rounded-xl">
            <div className="flex items-center space-x-3 mb-3">
              <span className="text-2xl">📱</span>
              <div>
                <p className="text-body font-sf-pro font-medium text-label">
                  Telegram 账户
                </p>
                <p className="text-caption-1 text-secondary-label">
                  @{user.username || `${user.first_name} ${user.last_name || ''}`.trim()}
                </p>
              </div>
            </div>
          </div>

          <Button
            variant="secondary"
            size="lg"
            onClick={handleLogout}
            className="w-full"
          >
            退出登录
          </Button>
        </div>
      );
    }

    return (
      <div className="space-y-6">
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-br from-system-blue to-system-purple rounded-2xl flex items-center justify-center mx-auto mb-4">
            <span className="text-3xl">📱</span>
          </div>
          <h2 className="text-title-2 font-sf-pro font-bold text-label mb-2">
            Telegram 登录
          </h2>
          <p className="text-body text-secondary-label">
            使用您的 Telegram 账户登录 SocioMint 平台
          </p>
        </div>

        <div className="space-y-4">
          <motion.div
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className="p-4 rounded-xl border border-system-gray-4 hover:border-system-blue hover:bg-system-gray-6 transition-all duration-200"
          >
            <div className="flex items-center space-x-3 mb-3">
              <span className="text-2xl">🔐</span>
              <div className="text-left">
                <p className="text-body font-sf-pro font-medium text-label">
                  安全登录
                </p>
                <p className="text-caption-1 text-secondary-label">
                  通过 Telegram OAuth 安全认证
                </p>
              </div>
            </div>
          </motion.div>

          <Button
            variant="primary"
            size="lg"
            onClick={handleTelegramLogin}
            disabled={isLoading}
            className="w-full"
          >
            {isLoading ? '登录中...' : '通过 Telegram 登录'}
          </Button>

          <div className="bg-system-gray-6 rounded-xl p-4">
            <div className="flex items-start space-x-3">
              <Icon icon={ActionIcons.info} size="sm" color="primary" className="mt-0.5" />
              <div>
                <p className="text-sm font-sf-pro font-medium text-label mb-1">
                  为什么使用 Telegram 登录？
                </p>
                <ul className="text-xs text-secondary-label space-y-1">
                  <li>• 安全的身份验证，无需密码</li>
                  <li>• 自动生成托管钱包地址</li>
                  <li>• 参与平台的所有功能</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="md">
      <div className="p-6">
        {renderLoginContent()}
      </div>
    </Modal>
  );
};

export default LoginModal;
