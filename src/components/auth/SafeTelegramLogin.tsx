'use client';

import React, { useEffect, useState } from 'react';
import dynamic from 'next/dynamic';
import { Button, Icon } from '@/components/ui';
import { SocialIcons, ActionIcons } from '@/config/icons';
import { useTelegramAuth } from '@/hooks/useTelegramAuth';
import { cn } from '@/lib/utils';
import { forceNavigate } from '@/utils/navigation';

interface SafeTelegramLoginProps {
  onSuccess?: () => void;
  onError?: (error: string) => void;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'primary' | 'secondary' | 'outline';
  showText?: boolean;
}

// 禁用SSR的登录组件
const TelegramLoginCore: React.FC<SafeTelegramLoginProps> = ({
  onSuccess,
  onError,
  className,
  size = 'md',
  variant = 'primary',
  showText = true,
}) => {
  const { login, isLoading, error } = useTelegramAuth();
  const [mounted, setMounted] = useState(false);

  // 确保只在客户端渲染
  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (error) {
      onError?.(error);
    }
  }, [error, onError]);

  /**
   * 处理Telegram登录
   */
  const handleTelegramLogin = async (userData: any) => {
    try {
      await login(userData);
      
      // 使用强制跳转而不是router.push
      forceNavigate('/profile', 200);
      
      onSuccess?.();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '登录失败';
      onError?.(errorMessage);
    }
  };

  /**
   * 开发环境模拟登录
   */
  const handleDevelopmentLogin = () => {
    const mockUser = {
      id: Date.now(), // 使用时间戳避免固定ID
      first_name: '测试用户',
      last_name: 'Test',
      username: 'testuser',
      photo_url: '',
      auth_date: Math.floor(Date.now() / 1000),
    };

    handleTelegramLogin(mockUser);
  };

  // 在服务端渲染时返回占位符
  if (!mounted) {
    return (
      <div className={cn('flex flex-col items-center space-y-4', className)}>
        <Button
          disabled
          variant={variant}
          size={size}
          className="flex items-center space-x-2"
        >
          <Icon icon={SocialIcons.telegram} size="sm" />
          {showText && <span>加载中...</span>}
        </Button>
      </div>
    );
  }

  return (
    <div className={cn('flex flex-col items-center space-y-4', className)}>
      <Button
        onClick={handleDevelopmentLogin}
        disabled={isLoading}
        variant={variant}
        size={size}
        className="flex items-center space-x-2"
      >
        {isLoading ? (
          <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
        ) : (
          <Icon icon={SocialIcons.telegram} size="sm" />
        )}
        {showText && (
          <span>
            {isLoading ? '登录中...' : '使用Telegram登录'}
          </span>
        )}
      </Button>

      <div className="text-center max-w-sm">
        <p className="text-caption-1 text-secondary-label">
          点击登录即表示您同意我们的
          <a href="/terms" className="text-system-blue hover:underline">服务条款</a>
          和
          <a href="/privacy" className="text-system-blue hover:underline">隐私政策</a>
        </p>
      </div>
    </div>
  );
};

// 使用dynamic导入禁用SSR
const SafeTelegramLogin = dynamic(
  () => Promise.resolve(TelegramLoginCore),
  { 
    ssr: false,
    loading: () => (
      <div className="flex flex-col items-center space-y-4">
        <Button disabled variant="primary" size="md" className="flex items-center space-x-2">
          <Icon icon={SocialIcons.telegram} size="sm" />
          <span>加载中...</span>
        </Button>
      </div>
    )
  }
);

export default SafeTelegramLogin;
