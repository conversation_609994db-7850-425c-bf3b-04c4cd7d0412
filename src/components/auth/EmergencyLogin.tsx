'use client';

import React, { useEffect, useState } from 'react';
import { Button, Icon } from '@/components/ui';
import { SocialIcons } from '@/config/icons';
import { useAuth } from '@/contexts/AuthContext';
import { LoginRedirect } from './LoginRedirect';

interface EmergencyLoginProps {
  onSuccess?: () => void;
  redirectTo?: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'primary' | 'secondary' | 'outline';
}

/**
 * 紧急登录组件 - 绕过所有SSR问题
 * 这是临时解决方案，用于快速修复登录跳转问题
 */
const EmergencyLogin: React.FC<EmergencyLoginProps> = ({
  onSuccess,
  redirectTo = '/profile',
  className = '',
  size = 'lg',
  variant = 'primary'
}) => {
  const { login } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [mounted, setMounted] = useState(false);
  const [showRedirect, setShowRedirect] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // 监控showRedirect状态变化
  useEffect(() => {
    console.log('🔥 [DEBUG] showRedirect状态变化:', showRedirect);
  }, [showRedirect]);

  const handleEmergencyLogin = async () => {
    console.log('🔥 [DEBUG] 点击登录按钮，开始处理');

    if (typeof window === 'undefined') {
      console.log('🔥 [DEBUG] window未定义，退出');
      return;
    }

    console.log('🔥 [DEBUG] 设置loading状态为true');
    setIsLoading(true);

    try {
      console.log('🔥 [DEBUG] 开始模拟登录过程，等待800ms');
      // 模拟登录过程
      await new Promise(resolve => setTimeout(resolve, 800));

      console.log('🔥 [DEBUG] 模拟登录等待完成，创建用户数据');
      const mockUser = {
        id: Date.now(),
        firstName: '测试用户',
        lastName: 'Test',
        username: 'emergency_user',
        photoUrl: '',
      };

      console.log('🚀 紧急登录成功，用户数据:', mockUser);

      console.log('🔥 [DEBUG] 准备调用login函数');
      // 使用认证上下文登录
      login(mockUser);

      console.log('🔥 [DEBUG] login函数调用完成');
      console.log('✅ 认证状态已更新，显示重定向组件');

      console.log('🔥 [DEBUG] 调用onSuccess回调');
      // 通知父组件
      onSuccess?.();

      // 使用setTimeout确保状态更新不冲突
      console.log('🔥 [DEBUG] 延迟设置showRedirect为true');
      setTimeout(() => {
        console.log('🔥 [DEBUG] 现在设置showRedirect为true');
        setShowRedirect(true);
        console.log('🔥 [DEBUG] showRedirect状态已更新');
      }, 100);

      console.log('🔥 [DEBUG] 所有步骤完成');

    } catch (error) {
      console.error('❌ 紧急登录失败:', error);
      console.log('🔥 [DEBUG] 发生错误，重置loading状态');
      setIsLoading(false);
    }
  };

  // 服务端渲染时显示占位符
  if (!mounted) {
    return (
      <Button 
        disabled 
        variant={variant} 
        size={size} 
        className={`w-full ${className}`}
      >
        <Icon icon={SocialIcons.telegram} size="sm" className="mr-2" />
        加载中...
      </Button>
    );
  }

  // 如果显示重定向组件
  if (showRedirect) {
    return (
      <div className={`space-y-4 ${className}`}>
        <LoginRedirect
          redirectTo={redirectTo}
          onComplete={() => {
            setIsLoading(false);
            setShowRedirect(false);
          }}
        />
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <Button
        onClick={handleEmergencyLogin}
        disabled={isLoading}
        variant={variant}
        size={size}
        className="w-full"
      >
        {isLoading ? (
          <>
            <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2" />
            登录中...
          </>
        ) : (
          <>
            <Icon icon={SocialIcons.telegram} size="sm" className="mr-2" />
            使用Telegram登录
          </>
        )}
      </Button>

      {process.env.NODE_ENV === 'development' && (
        <p className="text-caption-1 text-secondary-label text-center">
          🔧 紧急修复版本 - 绕过水合错误
        </p>
      )}
    </div>
  );
};

export default EmergencyLogin;
