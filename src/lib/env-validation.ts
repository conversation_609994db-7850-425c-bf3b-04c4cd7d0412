/**
 * 环境变量验证中间件
 * 确保必需配置项存在，提供清晰的错误信息
 */

import { log } from './logger';

export interface EnvConfig {
  name: string;
  required: boolean;
  defaultValue?: string;
  validator?: (value: string) => boolean;
  description?: string;
  sensitive?: boolean;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  missingRequired: string[];
  invalidValues: string[];
}

/**
 * 环境变量配置定义
 */
export const ENV_CONFIGS: EnvConfig[] = [
  // 数据库配置
  {
    name: 'NEXT_PUBLIC_SUPABASE_URL',
    required: true,
    validator: (value) => value.startsWith('https://') && value.includes('supabase'),
    description: 'Supabase 项目 URL',
  },
  {
    name: 'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    required: true,
    validator: (value) => value.length > 100,
    description: 'Supabase 匿名密钥',
    sensitive: true,
  },
  {
    name: 'SUPABASE_SERVICE_ROLE_KEY',
    required: true,
    validator: (value) => value.length > 100,
    description: 'Supabase 服务角色密钥',
    sensitive: true,
  },

  // 区块链配置
  {
    name: 'NEXT_PUBLIC_ALCHEMY_API_KEY',
    required: true,
    validator: (value) => value.length > 10,
    description: 'Alchemy API 密钥',
    sensitive: true,
  },
  {
    name: 'NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID',
    required: true,
    validator: (value) => value.length >= 20,
    description: 'WalletConnect 项目 ID',
  },

  // Telegram 配置
  {
    name: 'TELEGRAM_BOT_TOKEN',
    required: true,
    validator: (value) => /^\d+:[A-Za-z0-9_-]+$/.test(value),
    description: 'Telegram Bot Token',
    sensitive: true,
  },
  {
    name: 'TELEGRAM_WEBHOOK_SECRET',
    required: false,
    validator: (value) => value.length >= 16,
    description: 'Telegram Webhook 密钥',
    sensitive: true,
  },

  // JWT 配置
  {
    name: 'JWT_SECRET',
    required: true,
    validator: (value) => value.length >= 32,
    description: 'JWT 签名密钥',
    sensitive: true,
  },

  // BSC 区块链配置
  {
    name: 'NEXT_PUBLIC_CHAIN_ID',
    required: true,
    validator: (value) => ['56', '97'].includes(value),
    description: 'BSC 链 ID (56=主网, 97=测试网)',
  },
  {
    name: 'NEXT_PUBLIC_RPC_URL',
    required: true,
    validator: (value) => value.startsWith('http'),
    description: 'BSC RPC 节点 URL',
  },
  {
    name: 'NEXT_PUBLIC_HAOX_TOKEN_ADDRESS',
    required: true,
    validator: (value) => value.startsWith('0x') && value.length === 42,
    description: 'HAOX 代币合约地址',
  },
  {
    name: 'NEXT_PUBLIC_HAOX_PRESALE_ADDRESS',
    required: true,
    validator: (value) => value.startsWith('0x') && value.length === 42,
    description: 'HAOX 预售合约地址',
  },
  {
    name: 'NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID',
    required: false,
    validator: (value) => value.length > 0,
    description: 'WalletConnect 项目 ID',
    sensitive: true,
  },

  // 应用配置
  {
    name: 'NODE_ENV',
    required: true,
    validator: (value) => ['development', 'production', 'test'].includes(value),
    description: '运行环境',
    defaultValue: 'development',
  },
  {
    name: 'NEXT_PUBLIC_APP_URL',
    required: true,
    validator: (value) => value.startsWith('http'),
    description: '应用 URL',
  },

  // 可选配置
  {
    name: 'LOG_ENDPOINT',
    required: false,
    validator: (value) => value.startsWith('http'),
    description: '远程日志端点',
  },
  {
    name: 'SENTRY_DSN',
    required: false,
    validator: (value) => value.startsWith('https://'),
    description: 'Sentry 错误追踪 DSN',
    sensitive: true,
  },
];

/**
 * 验证环境变量
 */
export function validateEnvironment(): ValidationResult {
  const result: ValidationResult = {
    isValid: true,
    errors: [],
    warnings: [],
    missingRequired: [],
    invalidValues: [],
  };

  for (const config of ENV_CONFIGS) {
    const value = process.env[config.name];

    // 检查必需变量
    if (config.required && !value) {
      result.missingRequired.push(config.name);
      result.errors.push(
        `Missing required environment variable: ${config.name}${
          config.description ? ` (${config.description})` : ''
        }`
      );
      continue;
    }

    // 如果有值，进行验证
    if (value && config.validator && !config.validator(value)) {
      result.invalidValues.push(config.name);
      result.errors.push(
        `Invalid value for environment variable: ${config.name}${
          config.description ? ` (${config.description})` : ''
        }`
      );
    }

    // 检查可选变量的警告
    if (!config.required && !value && config.description) {
      result.warnings.push(
        `Optional environment variable not set: ${config.name} (${config.description})`
      );
    }
  }

  result.isValid = result.errors.length === 0;
  return result;
}

/**
 * 获取环境变量值，带有类型转换和默认值
 */
export function getEnvVar(name: string, defaultValue?: string): string {
  const value = process.env[name];
  if (!value && defaultValue !== undefined) {
    return defaultValue;
  }
  if (!value) {
    throw new Error(`Environment variable ${name} is required but not set`);
  }
  return value;
}

/**
 * 获取布尔类型环境变量
 */
export function getEnvBoolean(name: string, defaultValue: boolean = false): boolean {
  const value = process.env[name];
  if (!value) return defaultValue;
  return ['true', '1', 'yes', 'on'].includes(value.toLowerCase());
}

/**
 * 获取数字类型环境变量
 */
export function getEnvNumber(name: string, defaultValue?: number): number {
  const value = process.env[name];
  if (!value) {
    if (defaultValue !== undefined) return defaultValue;
    throw new Error(`Environment variable ${name} is required but not set`);
  }
  const num = parseInt(value, 10);
  if (isNaN(num)) {
    throw new Error(`Environment variable ${name} must be a valid number`);
  }
  return num;
}

/**
 * 初始化环境验证
 */
export function initializeEnvironment(): void {
  try {
    const result = validateEnvironment();

    // 记录验证结果
    if (result.warnings.length > 0) {
      result.warnings.forEach(warning => log.warn(warning));
    }

    if (result.errors.length > 0) {
      result.errors.forEach(error => log.error(error));

      // 在生产环境中，环境变量错误应该导致应用启动失败
      if (process.env.NODE_ENV === 'production') {
        log.critical('Environment validation failed in production', undefined, {
          missingRequired: result.missingRequired,
          invalidValues: result.invalidValues,
        });
        process.exit(1);
      } else {
        log.warn('Environment validation failed in development mode - continuing with warnings');
      }
    } else {
      log.info('Environment validation passed', {
        configuredVariables: ENV_CONFIGS.filter(c => process.env[c.name]).length,
        totalVariables: ENV_CONFIGS.length,
      });
    }
  } catch (error) {
    // 如果验证过程本身出错，在开发环境中继续运行
    if (process.env.NODE_ENV === 'development') {
      console.warn('Environment validation error (development mode):', error);
    } else {
      console.error('Environment validation error:', error);
      throw error;
    }
  }
}

/**
 * 获取环境变量摘要（用于调试，不包含敏感信息）
 */
export function getEnvironmentSummary(): Record<string, any> {
  const summary: Record<string, any> = {};

  for (const config of ENV_CONFIGS) {
    const value = process.env[config.name];
    
    if (config.sensitive) {
      summary[config.name] = value ? '***SET***' : 'NOT_SET';
    } else {
      summary[config.name] = value || 'NOT_SET';
    }
  }

  return summary;
}

/**
 * 检查关键环境变量是否已设置
 */
export function checkCriticalEnvVars(): boolean {
  const critical = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'NEXT_PUBLIC_CHAIN_ID',
    'NEXT_PUBLIC_RPC_URL',
    'NEXT_PUBLIC_HAOX_TOKEN_ADDRESS',
    'NEXT_PUBLIC_HAOX_PRESALE_ADDRESS',
  ];

  return critical.every(name => !!process.env[name]);
}

// 在模块加载时自动验证环境变量
if (typeof window === 'undefined') {
  // 只在服务器端运行
  try {
    initializeEnvironment();
  } catch (error) {
    // 在开发环境中，即使验证失败也继续运行
    if (process.env.NODE_ENV === 'development') {
      console.warn('Environment validation failed during module load (development mode):', error);
    } else {
      throw error;
    }
  }
}
