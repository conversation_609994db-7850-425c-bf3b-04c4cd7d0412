import { ethers } from 'ethers';

// HAOX代币合约配置
const HAOX_CONTRACT_CONFIG = {
  // 主网配置
  mainnet: {
    contractAddress: '******************************************', // 实际HAOX合约地址
    rpcUrl: 'https://mainnet.infura.io/v3/YOUR_INFURA_KEY',
    chainId: 1
  },
  // 测试网配置
  testnet: {
    contractAddress: '******************************************', // 测试网HAOX合约地址
    rpcUrl: 'https://goerli.infura.io/v3/YOUR_INFURA_KEY',
    chainId: 5
  }
};

// ERC20标准ABI（只需要balanceOf函数）
const ERC20_ABI = [
  {
    "constant": true,
    "inputs": [{"name": "_owner", "type": "address"}],
    "name": "balanceOf",
    "outputs": [{"name": "balance", "type": "uint256"}],
    "type": "function"
  },
  {
    "constant": true,
    "inputs": [],
    "name": "decimals",
    "outputs": [{"name": "", "type": "uint8"}],
    "type": "function"
  },
  {
    "constant": true,
    "inputs": [],
    "name": "symbol",
    "outputs": [{"name": "", "type": "string"}],
    "type": "function"
  }
];

// 认证等级配置
export const CERTIFICATION_LEVELS = {
  X1: { minHaox: 10, dailyQuota: 1, rounds: [1], discount: 0 },
  X2: { minHaox: 10000, dailyQuota: 5, rounds: [1, 2], discount: 5 },
  X3: { minHaox: 1000000, dailyQuota: 20, rounds: [1, 2], discount: 10 },
  X4: { minHaox: 10000000, dailyQuota: 50, rounds: [2, 3], discount: 15 },
  X5: { minHaox: 100000000, dailyQuota: -1, rounds: [2, 3], discount: 20 }
};

export interface HAOXBalance {
  address: string;
  balance: string;
  balanceFormatted: number;
  certificationLevel: string;
  lastUpdated: string;
}

export class HAOXService {
  private provider: ethers.JsonRpcProvider;
  private contract: ethers.Contract;
  private isTestMode: boolean;

  constructor(isTestMode: boolean = process.env.NODE_ENV === 'development') {
    this.isTestMode = isTestMode;
    
    if (isTestMode) {
      // 开发环境使用模拟提供者
      console.log('🔧 开发环境：使用模拟HAOX服务');
      return;
    }

    const config = HAOX_CONTRACT_CONFIG.mainnet; // 生产环境使用主网
    this.provider = new ethers.JsonRpcProvider(config.rpcUrl);
    this.contract = new ethers.Contract(config.contractAddress, ERC20_ABI, this.provider);
  }

  /**
   * 查询用户HAOX余额
   */
  async getHAOXBalance(walletAddress: string): Promise<HAOXBalance> {
    try {
      if (this.isTestMode) {
        return this.getMockBalance(walletAddress);
      }

      // 验证地址格式
      if (!ethers.isAddress(walletAddress)) {
        throw new Error('无效的钱包地址格式');
      }

      // 查询余额
      const balance = await this.contract.balanceOf(walletAddress);
      const decimals = await this.contract.decimals();
      
      // 格式化余额
      const balanceFormatted = parseFloat(ethers.formatUnits(balance, decimals));
      
      // 计算认证等级
      const certificationLevel = this.calculateCertificationLevel(balanceFormatted);

      return {
        address: walletAddress,
        balance: balance.toString(),
        balanceFormatted,
        certificationLevel,
        lastUpdated: new Date().toISOString()
      };
    } catch (error) {
      console.error('查询HAOX余额失败:', error);
      
      // 查询失败时返回最低等级
      return {
        address: walletAddress,
        balance: '0',
        balanceFormatted: 0,
        certificationLevel: 'X1',
        lastUpdated: new Date().toISOString()
      };
    }
  }

  /**
   * 批量查询多个地址的HAOX余额
   */
  async getBatchHAOXBalances(walletAddresses: string[]): Promise<HAOXBalance[]> {
    try {
      const promises = walletAddresses.map(address => this.getHAOXBalance(address));
      return await Promise.all(promises);
    } catch (error) {
      console.error('批量查询HAOX余额失败:', error);
      return [];
    }
  }

  /**
   * 计算认证等级
   */
  calculateCertificationLevel(haoxBalance: number): string {
    if (haoxBalance >= CERTIFICATION_LEVELS.X5.minHaox) return 'X5';
    if (haoxBalance >= CERTIFICATION_LEVELS.X4.minHaox) return 'X4';
    if (haoxBalance >= CERTIFICATION_LEVELS.X3.minHaox) return 'X3';
    if (haoxBalance >= CERTIFICATION_LEVELS.X2.minHaox) return 'X2';
    return 'X1';
  }

  /**
   * 获取认证等级权益信息
   */
  getCertificationBenefits(level: string) {
    const config = CERTIFICATION_LEVELS[level];
    if (!config) return null;

    return {
      level,
      minHaox: config.minHaox,
      dailyJudgmentQuota: config.dailyQuota === -1 ? '无限制' : config.dailyQuota,
      allowedRounds: config.rounds,
      feeDiscount: config.discount,
      benefits: this.getLevelBenefits(level)
    };
  }

  /**
   * 获取等级专属权益
   */
  private getLevelBenefits(level: string): string[] {
    const benefits = {
      X1: [
        '参与第一轮大众评审',
        '每日1次裁定机会',
        '基础福气奖励'
      ],
      X2: [
        '参与第一、二轮裁定',
        '每日5次裁定机会',
        '5%手续费折扣',
        '优先显示赌约'
      ],
      X3: [
        '参与第一、二轮裁定',
        '每日20次裁定机会',
        '10%手续费折扣',
        '专属认证标识',
        '高级统计数据'
      ],
      X4: [
        '参与第二、三轮专业裁定',
        '每日50次裁定机会',
        '15%手续费折扣',
        '终审裁定权限',
        '专属客服支持'
      ],
      X5: [
        '参与第二、三轮终审裁定',
        '无限制裁定机会',
        '20%手续费折扣',
        '最高裁定权限',
        '平台治理投票权',
        '专属VIP服务'
      ]
    };

    return benefits[level] || [];
  }

  /**
   * 开发环境模拟余额
   */
  private getMockBalance(walletAddress: string): HAOXBalance {
    // 根据地址生成模拟余额
    const addressHash = parseInt(walletAddress.slice(-4), 16);
    const mockBalances = [
      { min: 0, max: 100, level: 'X1' },
      { min: 10000, max: 50000, level: 'X2' },
      { min: 1000000, max: 5000000, level: 'X3' },
      { min: 10000000, max: 50000000, level: 'X4' },
      { min: 100000000, max: 500000000, level: 'X5' }
    ];

    const levelIndex = addressHash % mockBalances.length;
    const levelConfig = mockBalances[levelIndex];
    const balance = levelConfig.min + (addressHash % (levelConfig.max - levelConfig.min));

    return {
      address: walletAddress,
      balance: (balance * 1e18).toString(), // 转换为wei
      balanceFormatted: balance,
      certificationLevel: levelConfig.level,
      lastUpdated: new Date().toISOString()
    };
  }

  /**
   * 验证钱包地址格式
   */
  isValidAddress(address: string): boolean {
    return ethers.isAddress(address);
  }

  /**
   * 获取网络信息
   */
  async getNetworkInfo() {
    if (this.isTestMode) {
      return {
        name: 'Mock Network',
        chainId: 1337,
        isTestMode: true
      };
    }

    try {
      const network = await this.provider.getNetwork();
      return {
        name: network.name,
        chainId: Number(network.chainId),
        isTestMode: false
      };
    } catch (error) {
      console.error('获取网络信息失败:', error);
      return null;
    }
  }
}

// 导出单例实例
export const haoxService = new HAOXService();
