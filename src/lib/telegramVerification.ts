/**
 * Telegram 验证相关功能
 */

// Telegram 频道信息
export const TELEGRAM_CONFIG = {
  OFFICIAL_CHANNEL: '@sociomint9',
  CHANNEL_URL: 'https://t.me/sociomint9',
  BOT_TOKEN: process.env.NEXT_PUBLIC_TELEGRAM_BOT_TOKEN || '',
  CHANNEL_ID: process.env.NEXT_PUBLIC_TELEGRAM_CHANNEL_ID || '',
};

// 用户Telegram绑定状态
export interface TelegramBindingStatus {
  isBound: boolean;
  telegramId?: string;
  username?: string;
  joinedAt?: string;
  presaleEligible: boolean;
  verificationCode?: string;
}

// 验证结果
export interface VerificationResult {
  success: boolean;
  message: string;
  data?: TelegramBindingStatus;
}

/**
 * 生成验证码
 */
export function generateVerificationCode(): string {
  const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  let result = '';
  
  if (typeof window !== 'undefined' && window.crypto && window.crypto.getRandomValues) {
    const randomValues = new Uint8Array(6);
    window.crypto.getRandomValues(randomValues);
    
    for (let i = 0; i < 6; i++) {
      result += chars[randomValues[i] % chars.length];
    }
  } else {
    for (let i = 0; i < 6; i++) {
      result += chars[Math.floor(Math.random() * chars.length)];
    }
  }
  
  return result;
}

/**
 * 检查用户是否已绑定Telegram
 */
export async function checkTelegramBinding(walletAddress: string): Promise<TelegramBindingStatus> {
  try {
    // 从本地存储获取绑定状态
    const storageKey = `telegram_binding_${walletAddress}`;
    const stored = localStorage.getItem(storageKey);
    
    if (stored) {
      const binding = JSON.parse(stored) as TelegramBindingStatus;
      // 检查绑定是否过期（24小时）
      if (binding.joinedAt) {
        const joinTime = new Date(binding.joinedAt).getTime();
        const now = Date.now();
        const hoursPassed = (now - joinTime) / (1000 * 60 * 60);
        
        if (hoursPassed < 24) {
          return binding;
        }
      }
    }

    // 如果没有本地记录或已过期，返回未绑定状态
    return {
      isBound: false,
      presaleEligible: false,
    };
  } catch (error) {
    console.error('检查Telegram绑定状态失败:', error);
    return {
      isBound: false,
      presaleEligible: false,
    };
  }
}

/**
 * 保存Telegram绑定状态
 */
export function saveTelegramBinding(
  walletAddress: string, 
  binding: TelegramBindingStatus
): void {
  try {
    const storageKey = `telegram_binding_${walletAddress}`;
    localStorage.setItem(storageKey, JSON.stringify({
      ...binding,
      joinedAt: binding.joinedAt || new Date().toISOString(),
    }));
  } catch (error) {
    console.error('保存Telegram绑定状态失败:', error);
  }
}

/**
 * 验证用户是否加入了Telegram频道
 * 注意：由于浏览器安全限制，无法直接验证用户是否真的加入了频道
 * 这里使用验证码机制来确认用户确实访问了频道
 */
export async function verifyTelegramChannelJoin(
  walletAddress: string,
  verificationCode?: string
): Promise<VerificationResult> {
  try {
    // 如果提供了验证码，验证验证码
    if (verificationCode) {
      const binding = await checkTelegramBinding(walletAddress);
      
      if (binding.verificationCode === verificationCode) {
        // 验证码正确，标记为已加入
        const newBinding: TelegramBindingStatus = {
          isBound: true,
          presaleEligible: true,
          joinedAt: new Date().toISOString(),
        };
        
        saveTelegramBinding(walletAddress, newBinding);
        
        return {
          success: true,
          message: '验证成功！您已获得预售资格',
          data: newBinding,
        };
      } else {
        return {
          success: false,
          message: '验证码错误，请重新获取验证码',
        };
      }
    }

    // 生成新的验证码
    const newVerificationCode = generateVerificationCode();
    const binding: TelegramBindingStatus = {
      isBound: false,
      presaleEligible: false,
      verificationCode: newVerificationCode,
    };
    
    saveTelegramBinding(walletAddress, binding);
    
    return {
      success: true,
      message: '请在Telegram频道中发送验证码完成验证',
      data: binding,
    };
  } catch (error) {
    console.error('验证Telegram频道加入失败:', error);
    return {
      success: false,
      message: '验证失败，请稍后重试',
    };
  }
}

/**
 * 模拟验证（用于开发和测试）
 */
export async function simulateVerification(walletAddress: string): Promise<VerificationResult> {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 1500));
  
  // 70% 概率验证成功
  const success = Math.random() > 0.3;
  
  if (success) {
    const binding: TelegramBindingStatus = {
      isBound: true,
      presaleEligible: true,
      telegramId: 'simulated_user',
      username: 'test_user',
      joinedAt: new Date().toISOString(),
    };
    
    saveTelegramBinding(walletAddress, binding);
    
    return {
      success: true,
      message: '验证成功！您已获得预售资格',
      data: binding,
    };
  } else {
    return {
      success: false,
      message: '验证失败，请确保您已加入官方频道',
    };
  }
}

/**
 * 重置绑定状态（用于测试）
 */
export function resetTelegramBinding(walletAddress: string): void {
  try {
    const storageKey = `telegram_binding_${walletAddress}`;
    localStorage.removeItem(storageKey);
  } catch (error) {
    console.error('重置Telegram绑定状态失败:', error);
  }
}

/**
 * 获取频道加入指引
 */
export function getChannelJoinInstructions(): {
  steps: string[];
  tips: string[];
} {
  return {
    steps: [
      '点击下方按钮打开 Telegram 频道',
      '在 Telegram 中点击"加入频道"按钮',
      '确认加入 @sociomint9 频道',
      '返回此页面点击"验证加入"按钮',
      '等待验证完成获得预售资格',
    ],
    tips: [
      '请确保您已安装 Telegram 应用',
      '加入频道后请不要立即退出',
      '验证过程可能需要几秒钟时间',
      '如果验证失败，请重试或联系客服',
    ],
  };
}

/**
 * 检查Telegram应用是否可用
 */
export function isTelegramAvailable(): boolean {
  // 检查是否在移动设备上
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
    navigator.userAgent
  );
  
  // 检查是否支持Telegram深链接
  const supportsTelegramDeepLink = 'tg' in window || isMobile;
  
  return supportsTelegramDeepLink;
}

/**
 * 打开Telegram频道
 */
export function openTelegramChannel(): void {
  const { CHANNEL_URL } = TELEGRAM_CONFIG;

  if (isTelegramAvailable()) {
    // 尝试使用Telegram深链接
    const telegramDeepLink = `tg://resolve?domain=sociomint9`;

    // 创建一个隐藏的iframe来尝试深链接
    const iframe = document.createElement('iframe');
    iframe.style.display = 'none';
    iframe.src = telegramDeepLink;
    document.body.appendChild(iframe);

    // 立即移除iframe并打开网页版作为备选
    setTimeout(() => {
      document.body.removeChild(iframe);
      window.open(CHANNEL_URL, '_blank', 'noopener,noreferrer');
    }, 500);
  } else {
    // 直接打开网页版
    window.open(CHANNEL_URL, '_blank', 'noopener,noreferrer');
  }
}
