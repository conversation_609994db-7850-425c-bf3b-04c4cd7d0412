/**
 * 图片优化配置和工具
 * 提供图片压缩、格式转换和优化策略
 */

// 支持的图片格式
export const SUPPORTED_IMAGE_FORMATS = {
  webp: 'image/webp',
  avif: 'image/avif',
  jpeg: 'image/jpeg',
  png: 'image/png',
  svg: 'image/svg+xml',
} as const;

// 图片质量配置
export const IMAGE_QUALITY_PRESETS = {
  low: 60,
  medium: 75,
  high: 85,
  lossless: 100,
} as const;

// 图片尺寸预设
export const IMAGE_SIZE_PRESETS = {
  thumbnail: { width: 150, height: 150 },
  small: { width: 300, height: 300 },
  medium: { width: 600, height: 600 },
  large: { width: 1200, height: 1200 },
  hero: { width: 1920, height: 1080 },
} as const;

// 图片优化配置
export interface ImageOptimizationConfig {
  quality?: keyof typeof IMAGE_QUALITY_PRESETS | number;
  format?: keyof typeof SUPPORTED_IMAGE_FORMATS;
  width?: number;
  height?: number;
  fit?: 'cover' | 'contain' | 'fill' | 'inside' | 'outside';
  position?: 'center' | 'top' | 'bottom' | 'left' | 'right';
  blur?: number;
  sharpen?: boolean;
  grayscale?: boolean;
  progressive?: boolean;
  lossless?: boolean;
}

/**
 * 检测浏览器支持的图片格式
 */
export function detectSupportedFormats(): Promise<{
  webp: boolean;
  avif: boolean;
}> {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas');
    canvas.width = 1;
    canvas.height = 1;
    
    const webpSupported = canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
    
    // AVIF 检测
    const avifImage = new Image();
    avifImage.onload = () => resolve({ webp: webpSupported, avif: true });
    avifImage.onerror = () => resolve({ webp: webpSupported, avif: false });
    avifImage.src = 'data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAAB0AAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAIAAAACAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQ0MAAAAABNjb2xybmNseAACAAIAAYAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAACVtZGF0EgAKCBgABogQEAwgMg8f8D///8WfhwB8+ErK42A=';
  });
}

/**
 * 获取最佳图片格式
 */
export async function getBestImageFormat(originalFormat: string): Promise<string> {
  const supportedFormats = await detectSupportedFormats();
  
  // 如果是SVG，保持原格式
  if (originalFormat === 'image/svg+xml') {
    return originalFormat;
  }
  
  // 优先使用AVIF，其次WebP，最后回退到原格式
  if (supportedFormats.avif) {
    return SUPPORTED_IMAGE_FORMATS.avif;
  }
  
  if (supportedFormats.webp) {
    return SUPPORTED_IMAGE_FORMATS.webp;
  }
  
  return originalFormat;
}

/**
 * 构建优化后的图片URL
 */
export function buildOptimizedImageUrl(
  src: string,
  config: ImageOptimizationConfig = {}
): string {
  // 如果是外部URL或SVG，直接返回
  if (src.startsWith('http') || src.includes('.svg')) {
    return src;
  }
  
  const params = new URLSearchParams();
  
  // 质量设置
  if (config.quality) {
    const quality = typeof config.quality === 'number' 
      ? config.quality 
      : IMAGE_QUALITY_PRESETS[config.quality];
    params.set('q', quality.toString());
  }
  
  // 格式设置
  if (config.format) {
    params.set('f', config.format);
  }
  
  // 尺寸设置
  if (config.width) {
    params.set('w', config.width.toString());
  }
  
  if (config.height) {
    params.set('h', config.height.toString());
  }
  
  // 适应方式
  if (config.fit) {
    params.set('fit', config.fit);
  }
  
  // 位置
  if (config.position) {
    params.set('pos', config.position);
  }
  
  // 特效
  if (config.blur) {
    params.set('blur', config.blur.toString());
  }
  
  if (config.sharpen) {
    params.set('sharpen', '1');
  }
  
  if (config.grayscale) {
    params.set('gray', '1');
  }
  
  if (config.progressive) {
    params.set('prog', '1');
  }
  
  if (config.lossless) {
    params.set('lossless', '1');
  }
  
  const queryString = params.toString();
  return queryString ? `${src}?${queryString}` : src;
}

/**
 * 响应式图片URL生成器
 */
export function generateResponsiveImageUrls(
  src: string,
  sizes: Array<keyof typeof IMAGE_SIZE_PRESETS> = ['small', 'medium', 'large']
): Array<{ src: string; width: number; height: number }> {
  return sizes.map(size => {
    const preset = IMAGE_SIZE_PRESETS[size];
    return {
      src: buildOptimizedImageUrl(src, {
        width: preset.width,
        height: preset.height,
        quality: 'high',
        fit: 'cover',
      }),
      width: preset.width,
      height: preset.height,
    };
  });
}

/**
 * 生成srcSet字符串
 */
export function generateSrcSet(
  src: string,
  sizes: Array<keyof typeof IMAGE_SIZE_PRESETS> = ['small', 'medium', 'large']
): string {
  const urls = generateResponsiveImageUrls(src, sizes);
  return urls.map(({ src, width }) => `${src} ${width}w`).join(', ');
}

/**
 * 图片预加载
 */
export function preloadImage(src: string, config?: ImageOptimizationConfig): Promise<void> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    const optimizedSrc = buildOptimizedImageUrl(src, config);
    
    img.onload = () => resolve();
    img.onerror = () => reject(new Error(`Failed to preload image: ${optimizedSrc}`));
    img.src = optimizedSrc;
  });
}

/**
 * 批量预加载图片
 */
export async function preloadImages(
  images: Array<{ src: string; config?: ImageOptimizationConfig }>
): Promise<void> {
  const promises = images.map(({ src, config }) => preloadImage(src, config));
  await Promise.all(promises);
}

/**
 * 图片懒加载观察器
 */
export class ImageLazyLoader {
  private observer: IntersectionObserver;
  private loadedImages = new Set<string>();

  constructor(options: IntersectionObserverInit = {}) {
    this.observer = new IntersectionObserver(
      this.handleIntersection.bind(this),
      {
        rootMargin: '50px',
        threshold: 0.1,
        ...options,
      }
    );
  }

  private handleIntersection(entries: IntersectionObserverEntry[]) {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const img = entry.target as HTMLImageElement;
        const src = img.dataset.src;
        
        if (src && !this.loadedImages.has(src)) {
          this.loadImage(img, src);
          this.observer.unobserve(img);
        }
      }
    });
  }

  private async loadImage(img: HTMLImageElement, src: string) {
    try {
      // 获取最佳格式
      const bestFormat = await getBestImageFormat(img.dataset.format || 'image/jpeg');
      
      // 构建优化URL
      const optimizedSrc = buildOptimizedImageUrl(src, {
        format: bestFormat.split('/')[1] as keyof typeof SUPPORTED_IMAGE_FORMATS,
        quality: 'high',
        width: img.width || undefined,
        height: img.height || undefined,
      });

      // 预加载图片
      await preloadImage(optimizedSrc);
      
      // 设置图片源
      img.src = optimizedSrc;
      img.classList.add('loaded');
      
      this.loadedImages.add(src);
    } catch (error) {
      console.error('Failed to load image:', error);
      img.classList.add('error');
    }
  }

  observe(img: HTMLImageElement) {
    this.observer.observe(img);
  }

  unobserve(img: HTMLImageElement) {
    this.observer.unobserve(img);
  }

  disconnect() {
    this.observer.disconnect();
    this.loadedImages.clear();
  }
}

/**
 * 图片压缩工具
 */
export function compressImage(
  file: File,
  config: {
    maxWidth?: number;
    maxHeight?: number;
    quality?: number;
    format?: string;
  } = {}
): Promise<Blob> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      const { maxWidth = 1920, maxHeight = 1080, quality = 0.8, format = 'image/jpeg' } = config;
      
      // 计算新尺寸
      let { width, height } = img;
      
      if (width > maxWidth) {
        height = (height * maxWidth) / width;
        width = maxWidth;
      }
      
      if (height > maxHeight) {
        width = (width * maxHeight) / height;
        height = maxHeight;
      }
      
      // 设置画布尺寸
      canvas.width = width;
      canvas.height = height;
      
      // 绘制图片
      ctx?.drawImage(img, 0, 0, width, height);
      
      // 转换为Blob
      canvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('Failed to compress image'));
          }
        },
        format,
        quality
      );
    };

    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = URL.createObjectURL(file);
  });
}

/**
 * 获取图片元数据
 */
export function getImageMetadata(file: File): Promise<{
  width: number;
  height: number;
  size: number;
  type: string;
  aspectRatio: number;
}> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    
    img.onload = () => {
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight,
        size: file.size,
        type: file.type,
        aspectRatio: img.naturalWidth / img.naturalHeight,
      });
    };
    
    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = URL.createObjectURL(file);
  });
}

// 导出默认的懒加载实例
export const defaultLazyLoader = new ImageLazyLoader();
