/**
 * 国际化配置
 * 支持中文和英文界面切换
 */

import React, { useState, useEffect } from 'react';

export type Language = 'zh' | 'en';

export interface TranslationKeys {
  // 通用
  common: {
    loading: string;
    error: string;
    success: string;
    cancel: string;
    confirm: string;
    save: string;
    delete: string;
    edit: string;
    close: string;
    back: string;
    next: string;
    previous: string;
    refresh: string;
    search: string;
    filter: string;
    all: string;
    none: string;
    yes: string;
    no: string;
  };

  // 导航
  navigation: {
    home: string;
    trade: string;
    invitation: string;
    leaderboard: string;
    tasks: string;
    profile: string;
  };

  // 认证
  auth: {
    login: string;
    logout: string;
    loginWithTelegram: string;
    welcome: string;
    loginSuccess: string;
    loginFailed: string;
    logoutSuccess: string;
  };

  // 钱包
  wallet: {
    wallet: string;
    balance: string;
    transfer: string;
    receive: string;
    withdraw: string;
    history: string;
    address: string;
    amount: string;
    fee: string;
    gasPrice: string;
    estimatedTime: string;
    transactionHash: string;
    status: string;
    pending: string;
    confirmed: string;
    failed: string;
  };

  // 奖励
  rewards: {
    rewards: string;
    tasks: string;
    claim: string;
    complete: string;
    dailySignin: string;
    referral: string;
    socialTask: string;
    totalEarned: string;
    totalClaimed: string;
    pendingRewards: string;
    completedTasks: string;
  };

  // 帮助
  help: {
    helpCenter: string;
    faq: string;
    contactSupport: string;
    submitFeedback: string;
    searchPlaceholder: string;
    noResults: string;
  };

  // 新手引导
  onboarding: {
    welcome: string;
    skip: string;
    complete: string;
    step: string;
    of: string;
  };
}

export const translations: Record<Language, TranslationKeys> = {
  zh: {
    common: {
      loading: '加载中...',
      error: '错误',
      success: '成功',
      cancel: '取消',
      confirm: '确认',
      save: '保存',
      delete: '删除',
      edit: '编辑',
      close: '关闭',
      back: '返回',
      next: '下一步',
      previous: '上一步',
      refresh: '刷新',
      search: '搜索',
      filter: '筛选',
      all: '全部',
      none: '无',
      yes: '是',
      no: '否',
    },
    navigation: {
      home: '首页',
      trade: '交易',
      invitation: '邀请',
      leaderboard: '排行榜',
      tasks: '任务',
      profile: '个人中心',
    },
    auth: {
      login: '登录',
      logout: '退出登录',
      loginWithTelegram: '使用Telegram登录',
      welcome: '欢迎',
      loginSuccess: '登录成功',
      loginFailed: '登录失败',
      logoutSuccess: '已退出登录',
    },
    wallet: {
      wallet: '钱包',
      balance: '余额',
      transfer: '转账',
      receive: '收款',
      withdraw: '提现',
      history: '历史',
      address: '地址',
      amount: '金额',
      fee: '手续费',
      gasPrice: 'Gas价格',
      estimatedTime: '预估时间',
      transactionHash: '交易哈希',
      status: '状态',
      pending: '处理中',
      confirmed: '已确认',
      failed: '失败',
    },
    rewards: {
      rewards: '奖励',
      tasks: '任务',
      claim: '领取',
      complete: '完成',
      dailySignin: '每日签到',
      referral: '邀请奖励',
      socialTask: '社交任务',
      totalEarned: '总收益',
      totalClaimed: '已领取',
      pendingRewards: '待领取',
      completedTasks: '已完成任务',
    },
    help: {
      helpCenter: '帮助中心',
      faq: '常见问题',
      contactSupport: '联系客服',
      submitFeedback: '提交反馈',
      searchPlaceholder: '搜索问题或关键词...',
      noResults: '未找到相关问题',
    },
    onboarding: {
      welcome: '欢迎来到 SocioMint',
      skip: '跳过引导',
      complete: '完成',
      step: '步骤',
      of: '/',
    },
  },
  en: {
    common: {
      loading: 'Loading...',
      error: 'Error',
      success: 'Success',
      cancel: 'Cancel',
      confirm: 'Confirm',
      save: 'Save',
      delete: 'Delete',
      edit: 'Edit',
      close: 'Close',
      back: 'Back',
      next: 'Next',
      previous: 'Previous',
      refresh: 'Refresh',
      search: 'Search',
      filter: 'Filter',
      all: 'All',
      none: 'None',
      yes: 'Yes',
      no: 'No',
    },
    navigation: {
      home: 'Home',
      trade: 'Trade',
      invitation: 'Invitation',
      leaderboard: 'Leaderboard',
      tasks: 'Tasks',
      profile: 'Profile',
    },
    auth: {
      login: 'Login',
      logout: 'Logout',
      loginWithTelegram: 'Login with Telegram',
      welcome: 'Welcome',
      loginSuccess: 'Login successful',
      loginFailed: 'Login failed',
      logoutSuccess: 'Logged out successfully',
    },
    wallet: {
      wallet: 'Wallet',
      balance: 'Balance',
      transfer: 'Transfer',
      receive: 'Receive',
      withdraw: 'Withdraw',
      history: 'History',
      address: 'Address',
      amount: 'Amount',
      fee: 'Fee',
      gasPrice: 'Gas Price',
      estimatedTime: 'Estimated Time',
      transactionHash: 'Transaction Hash',
      status: 'Status',
      pending: 'Pending',
      confirmed: 'Confirmed',
      failed: 'Failed',
    },
    rewards: {
      rewards: 'Rewards',
      tasks: 'Tasks',
      claim: 'Claim',
      complete: 'Complete',
      dailySignin: 'Daily Sign-in',
      referral: 'Referral Reward',
      socialTask: 'Social Task',
      totalEarned: 'Total Earned',
      totalClaimed: 'Total Claimed',
      pendingRewards: 'Pending Rewards',
      completedTasks: 'Completed Tasks',
    },
    help: {
      helpCenter: 'Help Center',
      faq: 'FAQ',
      contactSupport: 'Contact Support',
      submitFeedback: 'Submit Feedback',
      searchPlaceholder: 'Search questions or keywords...',
      noResults: 'No related questions found',
    },
    onboarding: {
      welcome: 'Welcome to SocioMint',
      skip: 'Skip Guide',
      complete: 'Complete',
      step: 'Step',
      of: ' of ',
    },
  },
};

/**
 * 获取嵌套对象的值
 */
function getNestedValue(obj: any, path: string): string {
  return path.split('.').reduce((current, key) => current?.[key], obj) || path;
}

/**
 * 国际化Hook
 */
export function useTranslation() {
  const [language, setLanguage] = useState<Language>('zh');
  const [isClient, setIsClient] = useState(false);

  // 检测客户端环境
  useEffect(() => {
    setIsClient(true);
  }, []);

  // 初始化语言设置
  useEffect(() => {
    if (!isClient) return;

    try {
      const savedLanguage = localStorage.getItem('language') as Language;
      if (savedLanguage && ['zh', 'en'].includes(savedLanguage)) {
        setLanguage(savedLanguage);
      } else {
        // 根据浏览器语言自动设置
        const browserLanguage = navigator.language.toLowerCase();
        const detectedLanguage = browserLanguage.startsWith('zh') ? 'zh' : 'en';
        setLanguage(detectedLanguage);
      }
    } catch (error) {
      console.warn('Failed to initialize language settings:', error);
    }
  }, [isClient]);

  // 切换语言
  const changeLanguage = (newLanguage: Language) => {
    setLanguage(newLanguage);
    if (isClient) {
      try {
        localStorage.setItem('language', newLanguage);
      } catch (error) {
        console.warn('Failed to save language setting:', error);
      }
    }
  };

  // 翻译函数
  const t = (key: string): string => {
    return getNestedValue(translations[language], key);
  };

  return {
    language,
    changeLanguage,
    t,
  };
}

// 语言切换组件将在单独的 .tsx 文件中实现
