/**
 * 安全配置
 * 集中管理应用的安全设置和策略
 */

export interface SecurityConfig {
  // 限流配置
  rateLimit: {
    auth: { requests: number; window: number };
    api: { requests: number; window: number };
    upload: { requests: number; window: number };
    sensitive: { requests: number; window: number };
  };
  
  // JWT配置
  jwt: {
    secret: string;
    expiresIn: string;
    algorithm: string;
  };
  
  // Cookie配置
  cookie: {
    httpOnly: boolean;
    secure: boolean;
    sameSite: 'strict' | 'lax' | 'none';
    maxAge: number;
  };
  
  // CORS配置
  cors: {
    origins: string[];
    methods: string[];
    headers: string[];
    credentials: boolean;
  };
  
  // 密码策略
  password: {
    minLength: number;
    requireUppercase: boolean;
    requireLowercase: boolean;
    requireNumbers: boolean;
    requireSpecialChars: boolean;
  };
  
  // 文件上传限制
  upload: {
    maxSize: number;
    allowedTypes: string[];
    maxFiles: number;
  };
  
  // 会话配置
  session: {
    timeout: number;
    maxConcurrent: number;
    requireReauth: string[];
  };
}

/**
 * 默认安全配置
 */
export const defaultSecurityConfig: SecurityConfig = {
  rateLimit: {
    auth: { requests: 10, window: 60000 }, // 每分钟10次认证尝试
    api: { requests: 100, window: 60000 }, // 每分钟100次API请求
    upload: { requests: 5, window: 60000 }, // 每分钟5次上传
    sensitive: { requests: 3, window: 60000 }, // 每分钟3次敏感操作
  },
  
  jwt: {
    secret: process.env.JWT_SECRET || 'default-secret',
    expiresIn: '7d',
    algorithm: 'HS256',
  },
  
  cookie: {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: 7 * 24 * 60 * 60, // 7天
  },
  
  cors: {
    origins: process.env.NODE_ENV === 'production' 
      ? ['https://sociomint.app', 'https://www.sociomint.app']
      : ['http://localhost:3000', 'http://127.0.0.1:3000'],
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    headers: ['Content-Type', 'Authorization', 'X-Requested-With'],
    credentials: true,
  },
  
  password: {
    minLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: true,
  },
  
  upload: {
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'application/pdf',
      'text/plain',
    ],
    maxFiles: 5,
  },
  
  session: {
    timeout: 24 * 60 * 60 * 1000, // 24小时
    maxConcurrent: 3, // 最多3个并发会话
    requireReauth: [
      '/api/wallet/withdraw',
      '/api/wallet/transfer',
      '/api/merchant/funds',
      '/api/admin/*',
    ],
  },
};

/**
 * 获取安全配置
 */
export function getSecurityConfig(): SecurityConfig {
  return {
    ...defaultSecurityConfig,
    // 可以在这里添加环境特定的覆盖
    ...(process.env.NODE_ENV === 'production' && {
      rateLimit: {
        ...defaultSecurityConfig.rateLimit,
        auth: { requests: 5, window: 60000 }, // 生产环境更严格
        sensitive: { requests: 2, window: 60000 },
      },
    }),
  };
}

/**
 * 验证密码强度
 */
export function validatePasswordStrength(password: string): {
  isValid: boolean;
  errors: string[];
} {
  const config = getSecurityConfig().password;
  const errors: string[] = [];

  if (password.length < config.minLength) {
    errors.push(`密码长度至少${config.minLength}位`);
  }

  if (config.requireUppercase && !/[A-Z]/.test(password)) {
    errors.push('密码必须包含大写字母');
  }

  if (config.requireLowercase && !/[a-z]/.test(password)) {
    errors.push('密码必须包含小写字母');
  }

  if (config.requireNumbers && !/\d/.test(password)) {
    errors.push('密码必须包含数字');
  }

  if (config.requireSpecialChars && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('密码必须包含特殊字符');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * 验证文件上传
 */
export function validateFileUpload(file: {
  size: number;
  type: string;
}): {
  isValid: boolean;
  error?: string;
} {
  const config = getSecurityConfig().upload;

  if (file.size > config.maxSize) {
    return {
      isValid: false,
      error: `文件大小不能超过${Math.round(config.maxSize / 1024 / 1024)}MB`,
    };
  }

  if (!config.allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: '不支持的文件类型',
    };
  }

  return { isValid: true };
}

/**
 * 检查是否需要重新认证
 */
export function requiresReauth(path: string): boolean {
  const config = getSecurityConfig().session;
  
  return config.requireReauth.some(pattern => {
    if (pattern.endsWith('*')) {
      return path.startsWith(pattern.slice(0, -1));
    }
    return path === pattern;
  });
}

/**
 * 生成安全的随机字符串
 */
export function generateSecureToken(length: number = 32): string {
  const crypto = require('crypto');
  return crypto.randomBytes(length).toString('hex');
}

/**
 * 安全头部配置
 */
export const securityHeaders = {
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
  ...(process.env.NODE_ENV === 'production' && {
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
    'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; frame-src 'none';",
  }),
};

/**
 * IP白名单检查（用于管理员功能）
 */
export function isWhitelistedIP(ip: string): boolean {
  const whitelist = process.env.ADMIN_IP_WHITELIST?.split(',') || [];
  return whitelist.includes(ip) || process.env.NODE_ENV === 'development';
}
