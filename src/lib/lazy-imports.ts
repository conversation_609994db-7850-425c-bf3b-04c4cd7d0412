import { lazy } from 'react';
import { ComponentType } from 'react';

/**
 * 懒加载组件工厂
 * 提供统一的懒加载和错误处理
 */
function createLazyComponent<T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  fallback?: ComponentType
) {
  const LazyComponent = lazy(importFn);
  
  // 预加载函数
  const preload = () => {
    importFn().catch(console.error);
  };

  // 添加预加载方法到组件
  (LazyComponent as any).preload = preload;
  
  return LazyComponent;
}

/**
 * 页面级组件懒加载
 * 注意：只导入实际存在的页面
 */

/**
 * 功能组件懒加载 - 仅包含实际存在的组件
 */
export const LazyWalletConnect = createLazyComponent(
  () => import('@/components/wallet/WalletConnect')
);

export const LazyTransactionHistory = createLazyComponent(
  () => import('@/components/trading/TransactionHistory')
);

export const LazySocialTasks = createLazyComponent(
  () => import('@/components/social/SocialTasks')
);

export const LazyMerchantApplicationForm = createLazyComponent(
  () => import('@/components/merchant/MerchantApplicationForm')
);

export const LazySocialBinding = createLazyComponent(
  () => import('@/components/social/SocialBinding')
);

export const LazyTradingChart = createLazyComponent(
  () => import('@/components/trading/TradingChart')
);

export const LazyPriceChart = createLazyComponent(
  () => import('@/components/charts/PriceChart')
);

export const LazyNotificationCenter = createLazyComponent(
  () => import('@/components/notifications/NotificationCenter')
);

/**
 * 预加载策略 - 简化版本
 */
export const preloadStrategies = {
  immediate: () => {},
  onUserInteraction: () => {},
  onIdle: () => {},
  onHover: () => {},
  onRouteChange: () => {},
};

/**
 * 智能预加载 Hook
 */
export function useSmartPreload() {
  const preloadOnHover = () => {
    return {
      onMouseEnter: () => preloadStrategies.onHover(),
    };
  };

  const preloadOnFocus = () => {
    return {
      onFocus: () => preloadStrategies.onHover(),
    };
  };

  return {
    preloadOnHover,
    preloadOnFocus,
    preloadImmediate: preloadStrategies.immediate,
    preloadOnIdle: preloadStrategies.onIdle,
  };
}

/**
 * 组件预加载指令
 */
export const preloadDirectives = {
  // 在组件挂载时预加载
  usePreloadOnMount: (components: string[]) => {
    const componentMap: Record<string, any> = {
      'wallet': LazyWalletConnect,
      'transaction-history': LazyTransactionHistory,
      'social-tasks': LazySocialTasks,
      'social-binding': LazySocialBinding,
    };

    // components.forEach(name => {
    //   componentMap[name]?.preload?.();
    // });
  },

  // 在用户滚动到特定位置时预加载
  usePreloadOnScroll: (threshold = 0.5) => {
    if (typeof window !== 'undefined') {
      const handleScroll = () => {
        const scrolled = window.scrollY / (document.body.scrollHeight - window.innerHeight);
        if (scrolled > threshold) {
          preloadStrategies.onIdle();
          window.removeEventListener('scroll', handleScroll);
        }
      };

      window.addEventListener('scroll', handleScroll, { passive: true });
      return () => window.removeEventListener('scroll', handleScroll);
    }
  },
};

export default {
  LazyWalletConnect,
  LazyTransactionHistory,
  LazySocialTasks,
  LazyMerchantApplicationForm,
  LazySocialBinding,
  LazyTradingChart,
  LazyPriceChart,
  LazyNotificationCenter,
  preloadStrategies,
  useSmartPreload,
  preloadDirectives,
};
