/**
 * 统一的数据验证工具
 * 使用 Zod 实现类型安全的数据验证
 */

import { z } from 'zod';
import { NextRequest } from 'next/server';
import { ValidationError } from './errors';

/**
 * 以太坊地址验证
 */
export const ethereumAddressSchema = z
  .string()
  .regex(/^0x[a-fA-F0-9]{40}$/, '无效的以太坊地址格式');

/**
 * 金额验证（支持字符串和数字）
 */
export const amountSchema = z
  .union([z.string(), z.number()])
  .refine(
    (val) => {
      const num = typeof val === 'string' ? parseFloat(val) : val;
      return !isNaN(num) && num > 0;
    },
    { message: '金额必须大于0' }
  );

/**
 * 代币类型验证
 */
export const tokenTypeSchema = z.enum(['BNB', 'HAOX'], {
  errorMap: () => ({ message: '不支持的代币类型' }),
});

/**
 * 分页参数验证
 */
export const paginationSchema = z.object({
  page: z.coerce.number().min(1, '页码必须大于0').default(1),
  limit: z.coerce.number().min(1).max(100, '每页最多100条记录').default(20),
});

/**
 * 钱包转账请求验证
 */
export const transferRequestSchema = z.object({
  toAddress: ethereumAddressSchema,
  amount: amountSchema,
  tokenType: tokenTypeSchema,
  memo: z.string().max(200, '备注最多200个字符').optional(),
});

/**
 * 用户注册验证
 */
export const userRegistrationSchema = z.object({
  walletAddress: ethereumAddressSchema,
  username: z
    .string()
    .min(3, '用户名至少3个字符')
    .max(20, '用户名最多20个字符')
    .regex(/^[a-zA-Z0-9_]+$/, '用户名只能包含字母、数字和下划线'),
  email: z.string().email('无效的邮箱格式').optional(),
  telegramUsername: z.string().min(1, 'Telegram用户名不能为空').optional(),
});

/**
 * Telegram 绑定验证
 */
export const telegramBindingSchema = z.object({
  walletAddress: ethereumAddressSchema,
  telegramId: z.string().min(1, 'Telegram ID不能为空'),
  username: z.string().min(1, 'Telegram用户名不能为空'),
  verificationCode: z.string().length(6, '验证码必须是6位'),
});

/**
 * 任务发布验证
 */
export const taskPublishSchema = z.object({
  title: z.string().min(5, '标题至少5个字符').max(100, '标题最多100个字符'),
  description: z.string().min(10, '描述至少10个字符').max(1000, '描述最多1000个字符'),
  taskType: z.string().min(1, '任务类型不能为空'),
  rewardAmount: amountSchema,
  maxCompletions: z.number().min(1, '最大完成次数必须大于0'),
  endTime: z.string().datetime('无效的结束时间格式').optional(),
  requirements: z.array(z.string()).max(10, '最多10个要求').optional(),
});

/**
 * 投票请求验证
 */
export const voteRequestSchema = z.object({
  proposalId: z.string().min(1, '提案ID不能为空'),
  vote: z.enum(['for', 'against', 'abstain'], {
    errorMap: () => ({ message: '无效的投票选项' }),
  }),
  reason: z.string().max(500, '投票理由最多500个字符').optional(),
});



/**
 * 预售参与验证
 */
export const presaleParticipationSchema = z.object({
  amount: amountSchema,
  paymentMethod: z.enum(['BNB', 'USDT'], {
    errorMap: () => ({ message: '不支持的支付方式' }),
  }),
  referralCode: z.string().length(8, '推荐码必须是8位').optional(),
});

/**
 * 邀请码生成验证
 */
export const invitationGenerationSchema = z.object({
  type: z.enum(['general', 'vip', 'merchant'], {
    errorMap: () => ({ message: '无效的邀请类型' }),
  }),
  expiresIn: z.number().min(1).max(365, '有效期最多365天').default(30),
  maxUses: z.number().min(1).max(1000, '最大使用次数不能超过1000').default(1),
});

/**
 * 社交账户绑定验证
 */
export const socialAccountBindingSchema = z.object({
  platform: z.enum(['twitter', 'telegram', 'discord', 'youtube'], {
    errorMap: () => ({ message: '不支持的社交平台' }),
  }),
  accountId: z.string().min(1, '账户ID不能为空'),
  username: z.string().min(1, '用户名不能为空'),
  verificationData: z.record(z.any()).optional(),
});

/**
 * 验证中间件
 */
export function withValidation<T>(schema: z.ZodSchema<T>) {
  return async (request: NextRequest): Promise<T> => {
    try {
      const body = await request.json();
      return schema.parse(body);
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errorMessages = error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message,
        }));
        
        throw new ValidationError('数据验证失败', {
          errors: errorMessages,
          receivedData: error.errors.length > 0 ? 'Invalid data' : undefined,
        });
      }
      throw error;
    }
  };
}

/**
 * 验证查询参数
 */
export function validateQuery<T>(
  searchParams: URLSearchParams,
  schema: z.ZodSchema<T>
): T {
  try {
    const params: Record<string, any> = {};
    searchParams.forEach((value, key) => {
      params[key] = value;
    });
    
    return schema.parse(params);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessages = error.errors.map(err => ({
        field: err.path.join('.'),
        message: err.message,
      }));
      
      throw new ValidationError('查询参数验证失败', {
        errors: errorMessages,
      });
    }
    throw error;
  }
}

/**
 * 验证路径参数
 */
export function validateParams<T>(
  params: Record<string, string | string[]>,
  schema: z.ZodSchema<T>
): T {
  try {
    return schema.parse(params);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessages = error.errors.map(err => ({
        field: err.path.join('.'),
        message: err.message,
      }));
      
      throw new ValidationError('路径参数验证失败', {
        errors: errorMessages,
      });
    }
    throw error;
  }
}

/**
 * 创建验证装饰器
 */
export function createValidator<T>(schema: z.ZodSchema<T>) {
  return {
    body: withValidation(schema),
    query: (searchParams: URLSearchParams) => validateQuery(searchParams, schema),
    params: (params: Record<string, string | string[]>) => validateParams(params, schema),
  };
}

/**
 * 常用验证器
 */
export const validators = {
  transfer: createValidator(transferRequestSchema),
  userRegistration: createValidator(userRegistrationSchema),
  telegramBinding: createValidator(telegramBindingSchema),
  taskPublish: createValidator(taskPublishSchema),
  vote: createValidator(voteRequestSchema),
  merchantApplication: createValidator(merchantApplicationSchema),
  presaleParticipation: createValidator(presaleParticipationSchema),
  invitationGeneration: createValidator(invitationGenerationSchema),
  socialAccountBinding: createValidator(socialAccountBindingSchema),
  pagination: createValidator(paginationSchema),
};

/**
 * 类型导出
 */
export type TransferRequest = z.infer<typeof transferRequestSchema>;
export type UserRegistration = z.infer<typeof userRegistrationSchema>;
export type TelegramBinding = z.infer<typeof telegramBindingSchema>;
export type TaskPublish = z.infer<typeof taskPublishSchema>;
export type VoteRequest = z.infer<typeof voteRequestSchema>;
export type MerchantApplication = z.infer<typeof merchantApplicationSchema>;
export type PresaleParticipation = z.infer<typeof presaleParticipationSchema>;
export type InvitationGeneration = z.infer<typeof invitationGenerationSchema>;
export type SocialAccountBinding = z.infer<typeof socialAccountBindingSchema>;
export type PaginationParams = z.infer<typeof paginationSchema>;
