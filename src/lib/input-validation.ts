/**
 * 输入验证和清理工具
 * 提供安全的输入验证、清理和格式化功能
 */

import DOMPurify from 'isomorphic-dompurify';

export interface ValidationRule {
  required?: boolean;
  type?: 'string' | 'number' | 'email' | 'url' | 'phone' | 'address' | 'amount';
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
  pattern?: RegExp;
  custom?: (value: any) => boolean | string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  sanitizedValue?: any;
}

/**
 * 验证单个字段
 */
export function validateField(
  value: any,
  rules: ValidationRule,
  fieldName: string = 'field'
): ValidationResult {
  const errors: string[] = [];
  let sanitizedValue = value;

  // 必填验证
  if (rules.required && (value === undefined || value === null || value === '')) {
    errors.push(`${fieldName}是必填项`);
    return { isValid: false, errors };
  }

  // 如果值为空且非必填，直接返回有效
  if (!rules.required && (value === undefined || value === null || value === '')) {
    return { isValid: true, errors: [], sanitizedValue: value };
  }

  // 类型验证和清理
  switch (rules.type) {
    case 'string':
      sanitizedValue = sanitizeString(String(value));
      break;
      
    case 'number':
      const num = Number(value);
      if (isNaN(num)) {
        errors.push(`${fieldName}必须是有效数字`);
      } else {
        sanitizedValue = num;
      }
      break;
      
    case 'email':
      sanitizedValue = sanitizeEmail(String(value));
      if (!isValidEmail(sanitizedValue)) {
        errors.push(`${fieldName}必须是有效的邮箱地址`);
      }
      break;
      
    case 'url':
      sanitizedValue = sanitizeUrl(String(value));
      if (!isValidUrl(sanitizedValue)) {
        errors.push(`${fieldName}必须是有效的URL`);
      }
      break;
      
    case 'phone':
      sanitizedValue = sanitizePhone(String(value));
      if (!isValidPhone(sanitizedValue)) {
        errors.push(`${fieldName}必须是有效的手机号码`);
      }
      break;
      
    case 'address':
      sanitizedValue = sanitizeAddress(String(value));
      if (!isValidAddress(sanitizedValue)) {
        errors.push(`${fieldName}必须是有效的区块链地址`);
      }
      break;
      
    case 'amount':
      const amount = parseFloat(String(value));
      if (isNaN(amount) || amount < 0) {
        errors.push(`${fieldName}必须是有效的金额`);
      } else {
        sanitizedValue = amount;
      }
      break;
  }

  // 长度验证
  if (rules.minLength && String(sanitizedValue).length < rules.minLength) {
    errors.push(`${fieldName}长度不能少于${rules.minLength}个字符`);
  }

  if (rules.maxLength && String(sanitizedValue).length > rules.maxLength) {
    errors.push(`${fieldName}长度不能超过${rules.maxLength}个字符`);
  }

  // 数值范围验证
  if (rules.min !== undefined && Number(sanitizedValue) < rules.min) {
    errors.push(`${fieldName}不能小于${rules.min}`);
  }

  if (rules.max !== undefined && Number(sanitizedValue) > rules.max) {
    errors.push(`${fieldName}不能大于${rules.max}`);
  }

  // 正则表达式验证
  if (rules.pattern && !rules.pattern.test(String(sanitizedValue))) {
    errors.push(`${fieldName}格式不正确`);
  }

  // 自定义验证
  if (rules.custom) {
    const customResult = rules.custom(sanitizedValue);
    if (customResult !== true) {
      errors.push(typeof customResult === 'string' ? customResult : `${fieldName}验证失败`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    sanitizedValue,
  };
}

/**
 * 验证对象
 */
export function validateObject(
  data: Record<string, any>,
  schema: Record<string, ValidationRule>
): {
  isValid: boolean;
  errors: Record<string, string[]>;
  sanitizedData: Record<string, any>;
} {
  const errors: Record<string, string[]> = {};
  const sanitizedData: Record<string, any> = {};

  for (const [fieldName, rules] of Object.entries(schema)) {
    const result = validateField(data[fieldName], rules, fieldName);
    
    if (!result.isValid) {
      errors[fieldName] = result.errors;
    }
    
    if (result.sanitizedValue !== undefined) {
      sanitizedData[fieldName] = result.sanitizedValue;
    }
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
    sanitizedData,
  };
}

/**
 * 清理HTML字符串
 */
export function sanitizeHtml(html: string): string {
  return DOMPurify.sanitize(html, {
    ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'a', 'p', 'br'],
    ALLOWED_ATTR: ['href'],
  });
}

/**
 * 清理普通字符串
 */
export function sanitizeString(str: string): string {
  return str
    .trim()
    .replace(/[<>]/g, '') // 移除尖括号
    .replace(/javascript:/gi, '') // 移除javascript协议
    .replace(/on\w+=/gi, ''); // 移除事件处理器
}

/**
 * 清理邮箱地址
 */
export function sanitizeEmail(email: string): string {
  return email.toLowerCase().trim();
}

/**
 * 清理URL
 */
export function sanitizeUrl(url: string): string {
  const trimmed = url.trim();
  
  // 确保URL有协议
  if (!/^https?:\/\//i.test(trimmed)) {
    return `https://${trimmed}`;
  }
  
  return trimmed;
}

/**
 * 清理手机号码
 */
export function sanitizePhone(phone: string): string {
  return phone.replace(/[^\d+\-\s()]/g, '').trim();
}

/**
 * 清理区块链地址
 */
export function sanitizeAddress(address: string): string {
  return address.trim().toLowerCase();
}

/**
 * 验证邮箱格式
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * 验证URL格式
 */
export function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

/**
 * 验证手机号码格式
 */
export function isValidPhone(phone: string): boolean {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  return phoneRegex.test(phone.replace(/[\s\-()]/g, ''));
}

/**
 * 验证区块链地址格式
 */
export function isValidAddress(address: string): boolean {
  // 以太坊地址格式
  const ethRegex = /^0x[a-fA-F0-9]{40}$/;
  return ethRegex.test(address);
}

/**
 * 常用验证规则
 */
export const commonRules = {
  username: {
    required: true,
    type: 'string' as const,
    minLength: 3,
    maxLength: 20,
    pattern: /^[a-zA-Z0-9_]+$/,
  },
  
  email: {
    required: true,
    type: 'email' as const,
    maxLength: 100,
  },
  
  password: {
    required: true,
    type: 'string' as const,
    minLength: 8,
    maxLength: 128,
    custom: (value: string) => {
      if (!/(?=.*[a-z])/.test(value)) return '密码必须包含小写字母';
      if (!/(?=.*[A-Z])/.test(value)) return '密码必须包含大写字母';
      if (!/(?=.*\d)/.test(value)) return '密码必须包含数字';
      if (!/(?=.*[!@#$%^&*])/.test(value)) return '密码必须包含特殊字符';
      return true;
    },
  },
  
  amount: {
    required: true,
    type: 'amount' as const,
    min: 0,
  },
  
  walletAddress: {
    required: true,
    type: 'address' as const,
  },
  
  telegramId: {
    required: true,
    type: 'number' as const,
    min: 1,
  },
};
