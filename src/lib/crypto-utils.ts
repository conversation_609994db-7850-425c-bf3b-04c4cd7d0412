/**
 * 加密安全的随机数生成工具
 * 替换不安全的 Math.random() 实现
 */

import { randomBytes, randomInt } from 'crypto';

/**
 * 生成加密安全的随机字符串
 * @param length 字符串长度
 * @param charset 字符集，默认为数字和大写字母
 * @returns 随机字符串
 */
export function generateSecureCode(length: number = 6, charset?: string): string {
  const defaultCharset = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const chars = charset || defaultCharset;
  let result = '';
  
  const randomValues = randomBytes(length);
  
  for (let i = 0; i < length; i++) {
    result += chars[randomValues[i] % chars.length];
  }
  
  return result;
}

/**
 * 生成加密安全的随机数字
 * @param min 最小值（包含）
 * @param max 最大值（不包含）
 * @returns 随机数字
 */
export function generateSecureNumber(min: number = 0, max: number = 1): number {
  if (min >= max) {
    throw new Error('min must be less than max');
  }
  
  return randomInt(min, max);
}

/**
 * 生成加密安全的随机浮点数
 * @param min 最小值（包含）
 * @param max 最大值（不包含）
 * @returns 随机浮点数
 */
export function generateSecureFloat(min: number = 0, max: number = 1): number {
  if (min >= max) {
    throw new Error('min must be less than max');
  }
  
  const randomValue = randomBytes(4).readUInt32BE(0);
  const normalized = randomValue / 0xFFFFFFFF; // 归一化到 [0, 1)
  
  return min + normalized * (max - min);
}

/**
 * 生成加密安全的随机布尔值
 * @returns 随机布尔值
 */
export function generateSecureBoolean(): boolean {
  return randomBytes(1)[0] >= 128;
}

/**
 * 生成加密安全的随机UUID v4
 * @returns UUID字符串
 */
export function generateSecureUUID(): string {
  const bytes = randomBytes(16);
  
  // 设置版本号 (4) 和变体位
  bytes[6] = (bytes[6] & 0x0f) | 0x40; // 版本 4
  bytes[8] = (bytes[8] & 0x3f) | 0x80; // 变体 10
  
  const hex = bytes.toString('hex');
  return [
    hex.slice(0, 8),
    hex.slice(8, 12),
    hex.slice(12, 16),
    hex.slice(16, 20),
    hex.slice(20, 32)
  ].join('-');
}

/**
 * 生成加密安全的随机十六进制字符串
 * @param length 字节长度
 * @returns 十六进制字符串
 */
export function generateSecureHex(length: number = 16): string {
  return randomBytes(length).toString('hex');
}

/**
 * 生成加密安全的随机Base64字符串
 * @param length 字节长度
 * @returns Base64字符串
 */
export function generateSecureBase64(length: number = 16): string {
  return randomBytes(length).toString('base64');
}

/**
 * 生成加密安全的随机密码
 * @param length 密码长度
 * @param includeSymbols 是否包含特殊符号
 * @returns 随机密码
 */
export function generateSecurePassword(length: number = 12, includeSymbols: boolean = true): string {
  const lowercase = 'abcdefghijklmnopqrstuvwxyz';
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const numbers = '0123456789';
  const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';
  
  let charset = lowercase + uppercase + numbers;
  if (includeSymbols) {
    charset += symbols;
  }
  
  let password = '';
  const randomValues = randomBytes(length);
  
  for (let i = 0; i < length; i++) {
    password += charset[randomValues[i] % charset.length];
  }
  
  // 确保密码包含至少一个小写字母、大写字母和数字
  const hasLower = /[a-z]/.test(password);
  const hasUpper = /[A-Z]/.test(password);
  const hasNumber = /[0-9]/.test(password);
  const hasSymbol = includeSymbols ? /[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/.test(password) : true;
  
  if (!hasLower || !hasUpper || !hasNumber || !hasSymbol) {
    // 如果不满足要求，递归重新生成
    return generateSecurePassword(length, includeSymbols);
  }
  
  return password;
}

/**
 * 从数组中安全随机选择元素
 * @param array 源数组
 * @param count 选择数量，默认为1
 * @returns 选择的元素数组
 */
export function secureRandomChoice<T>(array: T[], count: number = 1): T[] {
  if (array.length === 0) {
    return [];
  }
  
  if (count >= array.length) {
    return [...array];
  }
  
  const result: T[] = [];
  const indices = new Set<number>();
  
  while (result.length < count) {
    const randomIndex = generateSecureNumber(0, array.length);
    if (!indices.has(randomIndex)) {
      indices.add(randomIndex);
      result.push(array[randomIndex]);
    }
  }
  
  return result;
}

/**
 * 安全随机打乱数组
 * @param array 源数组
 * @returns 打乱后的新数组
 */
export function secureRandomShuffle<T>(array: T[]): T[] {
  const result = [...array];
  
  for (let i = result.length - 1; i > 0; i--) {
    const j = generateSecureNumber(0, i + 1);
    [result[i], result[j]] = [result[j], result[i]];
  }
  
  return result;
}

/**
 * 生成加密安全的随机延迟时间（用于防止时序攻击）
 * @param baseMs 基础延迟时间（毫秒）
 * @param varianceMs 变化范围（毫秒）
 * @returns 延迟时间（毫秒）
 */
export function generateSecureDelay(baseMs: number = 100, varianceMs: number = 50): number {
  const variance = generateSecureFloat(-varianceMs, varianceMs);
  return Math.max(0, baseMs + variance);
}

/**
 * 生成加密安全的随机盐值
 * @param length 盐值字节长度
 * @returns 盐值的十六进制字符串
 */
export function generateSecureSalt(length: number = 32): string {
  return randomBytes(length).toString('hex');
}

/**
 * 生成加密安全的随机nonce
 * @param length nonce字节长度
 * @returns nonce的十六进制字符串
 */
export function generateSecureNonce(length: number = 16): string {
  return randomBytes(length).toString('hex');
}

// 导出便捷的替换函数，用于替换 Math.random()
export const secureRandom = {
  /**
   * 替换 Math.random()，返回 [0, 1) 范围内的随机数
   */
  random: (): number => generateSecureFloat(0, 1),
  
  /**
   * 替换 Math.floor(Math.random() * max)
   */
  randomInt: (max: number): number => generateSecureNumber(0, max),
  
  /**
   * 替换 Math.random() > 0.5
   */
  randomBoolean: (): boolean => generateSecureBoolean(),
};
