/**
 * 性能优化Hook集合
 * 提供组件渲染优化、懒加载、虚拟化等功能
 */

import { 
  useCallback, 
  useMemo, 
  useRef, 
  useEffect, 
  useState,
  RefObject,
  DependencyList
} from 'react';

/**
 * 防抖Hook
 */
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

/**
 * 节流Hook
 */
export function useThrottle<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const lastRun = useRef(Date.now());

  return useCallback(
    ((...args) => {
      if (Date.now() - lastRun.current >= delay) {
        callback(...args);
        lastRun.current = Date.now();
      }
    }) as T,
    [callback, delay]
  );
}

/**
 * 稳定化回调Hook - 避免不必要的重新渲染
 */
export function useStableCallback<T extends (...args: any[]) => any>(
  callback: T
): T {
  const callbackRef = useRef(callback);
  
  useEffect(() => {
    callbackRef.current = callback;
  });

  return useCallback(
    ((...args) => callbackRef.current(...args)) as T,
    []
  );
}

/**
 * 深度比较的useMemo
 */
export function useDeepMemo<T>(
  factory: () => T,
  deps: DependencyList
): T {
  const ref = useRef<{ deps: DependencyList; value: T }>();

  if (!ref.current || !deepEqual(ref.current.deps, deps)) {
    ref.current = {
      deps,
      value: factory()
    };
  }

  return ref.current.value;
}

/**
 * 虚拟化列表Hook
 */
export function useVirtualList<T>({
  items,
  itemHeight,
  containerHeight,
  overscan = 5
}: {
  items: T[];
  itemHeight: number;
  containerHeight: number;
  overscan?: number;
}) {
  const [scrollTop, setScrollTop] = useState(0);

  const visibleRange = useMemo(() => {
    const startIndex = Math.floor(scrollTop / itemHeight);
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight),
      items.length - 1
    );

    return {
      start: Math.max(0, startIndex - overscan),
      end: Math.min(items.length - 1, endIndex + overscan)
    };
  }, [scrollTop, itemHeight, containerHeight, items.length, overscan]);

  const visibleItems = useMemo(() => {
    return items.slice(visibleRange.start, visibleRange.end + 1).map((item, index) => ({
      item,
      index: visibleRange.start + index
    }));
  }, [items, visibleRange]);

  const totalHeight = items.length * itemHeight;

  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop);
  }, []);

  return {
    visibleItems,
    totalHeight,
    visibleRange,
    handleScroll
  };
}

/**
 * 交叉观察器Hook - 用于懒加载
 */
export function useIntersectionObserver(
  elementRef: RefObject<Element>,
  options?: IntersectionObserverInit
): boolean {
  const [isIntersecting, setIsIntersecting] = useState(false);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting);
      },
      {
        threshold: 0.1,
        ...options
      }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [elementRef, options]);

  return isIntersecting;
}

/**
 * 懒加载Hook
 */
export function useLazyLoad<T>(
  loader: () => Promise<T>,
  trigger: boolean = true
): {
  data: T | null;
  loading: boolean;
  error: Error | null;
} {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    if (!trigger || data) return;

    setLoading(true);
    setError(null);

    loader()
      .then(setData)
      .catch(setError)
      .finally(() => setLoading(false));
  }, [trigger, loader, data]);

  return { data, loading, error };
}

/**
 * 性能监控Hook
 */
export function usePerformanceMonitor(componentName: string) {
  const renderCount = useRef(0);
  const mountTime = useRef(Date.now());
  const lastRenderTime = useRef(Date.now());

  useEffect(() => {
    renderCount.current++;
    const now = Date.now();
    const renderTime = now - lastRenderTime.current;
    lastRenderTime.current = now;

    if (process.env.NODE_ENV === 'development') {
      console.log(`[Performance] ${componentName}:`, {
        renderCount: renderCount.current,
        renderTime: `${renderTime}ms`,
        totalTime: `${now - mountTime.current}ms`
      });
    }
  });

  return {
    renderCount: renderCount.current,
    totalTime: Date.now() - mountTime.current
  };
}

/**
 * 批量状态更新Hook
 */
export function useBatchedState<T>(initialState: T) {
  const [state, setState] = useState(initialState);
  const pendingUpdates = useRef<Partial<T>[]>([]);
  const timeoutRef = useRef<NodeJS.Timeout>();

  const batchedSetState = useCallback((update: Partial<T>) => {
    pendingUpdates.current.push(update);

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      setState(prevState => {
        let newState = { ...prevState };
        for (const update of pendingUpdates.current) {
          newState = { ...newState, ...update };
        }
        pendingUpdates.current = [];
        return newState;
      });
    }, 0);
  }, []);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return [state, batchedSetState] as const;
}

/**
 * 内存泄漏防护Hook
 */
export function useCleanup(cleanup: () => void) {
  const cleanupRef = useRef(cleanup);
  
  useEffect(() => {
    cleanupRef.current = cleanup;
  });

  useEffect(() => {
    return () => {
      cleanupRef.current();
    };
  }, []);
}

/**
 * 深度比较函数
 */
function deepEqual(a: any, b: any): boolean {
  if (a === b) return true;
  
  if (a == null || b == null) return false;
  
  if (Array.isArray(a) && Array.isArray(b)) {
    if (a.length !== b.length) return false;
    for (let i = 0; i < a.length; i++) {
      if (!deepEqual(a[i], b[i])) return false;
    }
    return true;
  }
  
  if (typeof a === 'object' && typeof b === 'object') {
    const keysA = Object.keys(a);
    const keysB = Object.keys(b);
    
    if (keysA.length !== keysB.length) return false;
    
    for (const key of keysA) {
      if (!keysB.includes(key)) return false;
      if (!deepEqual(a[key], b[key])) return false;
    }
    return true;
  }
  
  return false;
}

/**
 * 组件性能分析器
 */
export class ComponentProfiler {
  private static profiles = new Map<string, {
    renderCount: number;
    totalTime: number;
    averageTime: number;
    lastRender: number;
  }>();

  static startProfile(componentName: string) {
    return Date.now();
  }

  static endProfile(componentName: string, startTime: number) {
    const endTime = Date.now();
    const renderTime = endTime - startTime;

    const existing = this.profiles.get(componentName) || {
      renderCount: 0,
      totalTime: 0,
      averageTime: 0,
      lastRender: 0
    };

    const updated = {
      renderCount: existing.renderCount + 1,
      totalTime: existing.totalTime + renderTime,
      averageTime: (existing.totalTime + renderTime) / (existing.renderCount + 1),
      lastRender: renderTime
    };

    this.profiles.set(componentName, updated);

    if (process.env.NODE_ENV === 'development' && renderTime > 16) {
      console.warn(`[Performance Warning] ${componentName} took ${renderTime}ms to render`);
    }
  }

  static getProfiles() {
    return Object.fromEntries(this.profiles);
  }

  static clearProfiles() {
    this.profiles.clear();
  }
}
