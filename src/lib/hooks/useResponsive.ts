/**
 * 响应式设计Hook
 * 提供设备检测、断点管理、移动端优化
 */

import { useState, useEffect, useCallback } from 'react';

// 断点定义
export const BREAKPOINTS = {
  xs: 0,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536
} as const;

export type Breakpoint = keyof typeof BREAKPOINTS;

// 设备类型
export type DeviceType = 'mobile' | 'tablet' | 'desktop';

// 屏幕方向
export type Orientation = 'portrait' | 'landscape';

interface ResponsiveState {
  width: number;
  height: number;
  breakpoint: Breakpoint;
  deviceType: DeviceType;
  orientation: Orientation;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isTouch: boolean;
  pixelRatio: number;
}

/**
 * 获取当前断点
 */
function getCurrentBreakpoint(width: number): Breakpoint {
  if (width >= BREAKPOINTS['2xl']) return '2xl';
  if (width >= BREAKPOINTS.xl) return 'xl';
  if (width >= BREAKPOINTS.lg) return 'lg';
  if (width >= BREAKPOINTS.md) return 'md';
  if (width >= BREAKPOINTS.sm) return 'sm';
  return 'xs';
}

/**
 * 获取设备类型
 */
function getDeviceType(width: number): DeviceType {
  if (width < BREAKPOINTS.md) return 'mobile';
  if (width < BREAKPOINTS.lg) return 'tablet';
  return 'desktop';
}

/**
 * 获取屏幕方向
 */
function getOrientation(width: number, height: number): Orientation {
  return width > height ? 'landscape' : 'portrait';
}

/**
 * 检测是否为触摸设备
 */
function isTouchDevice(): boolean {
  if (typeof window === 'undefined') return false;
  
  return (
    'ontouchstart' in window ||
    navigator.maxTouchPoints > 0 ||
    // @ts-ignore
    navigator.msMaxTouchPoints > 0
  );
}

/**
 * 响应式Hook
 */
export function useResponsive(): ResponsiveState {
  const [state, setState] = useState<ResponsiveState>(() => {
    if (typeof window === 'undefined') {
      return {
        width: 1024,
        height: 768,
        breakpoint: 'lg' as Breakpoint,
        deviceType: 'desktop' as DeviceType,
        orientation: 'landscape' as Orientation,
        isMobile: false,
        isTablet: false,
        isDesktop: true,
        isTouch: false,
        pixelRatio: 1
      };
    }

    const width = window.innerWidth;
    const height = window.innerHeight;
    const breakpoint = getCurrentBreakpoint(width);
    const deviceType = getDeviceType(width);
    const orientation = getOrientation(width, height);

    return {
      width,
      height,
      breakpoint,
      deviceType,
      orientation,
      isMobile: deviceType === 'mobile',
      isTablet: deviceType === 'tablet',
      isDesktop: deviceType === 'desktop',
      isTouch: isTouchDevice(),
      pixelRatio: window.devicePixelRatio || 1
    };
  });

  const updateState = useCallback(() => {
    if (typeof window === 'undefined') return;

    const width = window.innerWidth;
    const height = window.innerHeight;
    const breakpoint = getCurrentBreakpoint(width);
    const deviceType = getDeviceType(width);
    const orientation = getOrientation(width, height);

    setState({
      width,
      height,
      breakpoint,
      deviceType,
      orientation,
      isMobile: deviceType === 'mobile',
      isTablet: deviceType === 'tablet',
      isDesktop: deviceType === 'desktop',
      isTouch: isTouchDevice(),
      pixelRatio: window.devicePixelRatio || 1
    });
  }, []);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    // 防抖处理
    let timeoutId: NodeJS.Timeout;
    const debouncedUpdate = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(updateState, 100);
    };

    window.addEventListener('resize', debouncedUpdate);
    window.addEventListener('orientationchange', debouncedUpdate);

    return () => {
      window.removeEventListener('resize', debouncedUpdate);
      window.removeEventListener('orientationchange', debouncedUpdate);
      clearTimeout(timeoutId);
    };
  }, [updateState]);

  return state;
}

/**
 * 断点匹配Hook
 */
export function useBreakpoint(breakpoint: Breakpoint): boolean {
  const { width } = useResponsive();
  return width >= BREAKPOINTS[breakpoint];
}

/**
 * 媒体查询Hook
 */
export function useMediaQuery(query: string): boolean {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const mediaQuery = window.matchMedia(query);
    setMatches(mediaQuery.matches);

    const handler = (event: MediaQueryListEvent) => {
      setMatches(event.matches);
    };

    mediaQuery.addEventListener('change', handler);
    return () => mediaQuery.removeEventListener('change', handler);
  }, [query]);

  return matches;
}

/**
 * 移动端优化Hook
 */
export function useMobileOptimization() {
  const { isMobile, isTouch, orientation } = useResponsive();

  // 禁用移动端缩放
  useEffect(() => {
    if (typeof document === 'undefined') return;

    if (isMobile) {
      const viewport = document.querySelector('meta[name="viewport"]');
      if (viewport) {
        viewport.setAttribute(
          'content',
          'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no'
        );
      }
    }
  }, [isMobile]);

  // 移动端滚动优化
  useEffect(() => {
    if (typeof document === 'undefined') return;

    if (isMobile) {
      document.body.style.webkitOverflowScrolling = 'touch';
      document.body.style.overflowScrolling = 'touch';
    }

    return () => {
      document.body.style.webkitOverflowScrolling = '';
      document.body.style.overflowScrolling = '';
    };
  }, [isMobile]);

  // 触摸优化
  const touchOptimizedProps = isTouch ? {
    style: {
      touchAction: 'manipulation',
      WebkitTapHighlightColor: 'transparent'
    }
  } : {};

  return {
    isMobile,
    isTouch,
    orientation,
    touchOptimizedProps,
    
    // 移动端专用样式类
    mobileClasses: isMobile ? 'mobile-optimized' : '',
    
    // 触摸设备专用样式类
    touchClasses: isTouch ? 'touch-optimized' : ''
  };
}

/**
 * 响应式值Hook
 */
export function useResponsiveValue<T>(values: Partial<Record<Breakpoint, T>>): T | undefined {
  const { breakpoint } = useResponsive();
  
  // 按优先级查找值
  const breakpointOrder: Breakpoint[] = ['2xl', 'xl', 'lg', 'md', 'sm', 'xs'];
  const currentIndex = breakpointOrder.indexOf(breakpoint);
  
  for (let i = currentIndex; i < breakpointOrder.length; i++) {
    const bp = breakpointOrder[i];
    if (values[bp] !== undefined) {
      return values[bp];
    }
  }
  
  return undefined;
}

/**
 * 容器查询Hook（实验性）
 */
export function useContainerQuery(containerRef: React.RefObject<HTMLElement>) {
  const [containerWidth, setContainerWidth] = useState(0);

  useEffect(() => {
    if (!containerRef.current) return;

    const resizeObserver = new ResizeObserver(entries => {
      for (const entry of entries) {
        setContainerWidth(entry.contentRect.width);
      }
    });

    resizeObserver.observe(containerRef.current);

    return () => {
      resizeObserver.disconnect();
    };
  }, [containerRef]);

  return {
    width: containerWidth,
    isSmall: containerWidth < 400,
    isMedium: containerWidth >= 400 && containerWidth < 768,
    isLarge: containerWidth >= 768
  };
}

/**
 * 响应式工具函数
 */
export const ResponsiveUtils = {
  // 获取响应式类名
  getResponsiveClasses: (
    base: string,
    responsive: Partial<Record<Breakpoint, string>>
  ): string => {
    const classes = [base];
    
    Object.entries(responsive).forEach(([bp, className]) => {
      if (className) {
        classes.push(`${bp}:${className}`);
      }
    });
    
    return classes.join(' ');
  },

  // 检查是否为移动设备
  isMobileDevice: (): boolean => {
    if (typeof navigator === 'undefined') return false;
    
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent
    );
  },

  // 获取安全区域样式
  getSafeAreaStyles: () => ({
    paddingTop: 'env(safe-area-inset-top)',
    paddingBottom: 'env(safe-area-inset-bottom)',
    paddingLeft: 'env(safe-area-inset-left)',
    paddingRight: 'env(safe-area-inset-right)'
  })
};
