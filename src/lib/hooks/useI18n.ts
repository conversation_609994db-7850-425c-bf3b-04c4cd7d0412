'use client';

import { useLocale, useTranslations } from 'next-intl';
import { useRouter, usePathname } from 'next/navigation';
import { useTransition } from 'react';
import { locales, localeNames, localeFlags, setStoredLocale, type Locale } from '@/lib/i18n/config';

export function useI18n() {
  const locale = useLocale() as Locale;
  const router = useRouter();
  const pathname = usePathname();
  const [isPending, startTransition] = useTransition();

  // 获取翻译函数
  const t = useTranslations();
  const tCommon = useTranslations('common');
  const tNav = useTranslations('navigation');
  const tSocialBet = useTranslations('socialBet');
  const tFortune = useTranslations('fortune');
  const tCertification = useTranslations('certification');
  const tTraceability = useTranslations('traceability');
  const tMonitoring = useTranslations('monitoring');
  const tErrors = useTranslations('errors');
  const tSuccess = useTranslations('success');

  // 切换语言
  const changeLanguage = (newLocale: Locale) => {
    if (newLocale === locale) return;

    startTransition(() => {
      // 保存语言偏好
      setStoredLocale(newLocale);
      
      // 构建新的路径
      const segments = pathname.split('/');
      
      // 如果当前路径包含语言代码，替换它
      if (locales.includes(segments[1] as Locale)) {
        segments[1] = newLocale;
      } else {
        // 如果没有语言代码，添加它
        segments.splice(1, 0, newLocale);
      }
      
      const newPath = segments.join('/');
      router.push(newPath);
    });
  };

  // 格式化数字
  const formatNumber = (value: number, options?: Intl.NumberFormatOptions) => {
    const localeCode = locale === 'zh' ? 'zh-CN' : 'en-US';
    return new Intl.NumberFormat(localeCode, options).format(value);
  };

  // 格式化货币
  const formatCurrency = (value: number, currency: string = 'HAOX') => {
    if (currency === 'HAOX' || currency === 'Fortune') {
      return `${formatNumber(value, { minimumFractionDigits: 2, maximumFractionDigits: 6 })} ${currency}`;
    }
    
    const localeCode = locale === 'zh' ? 'zh-CN' : 'en-US';
    const currencyCode = currency === 'CNY' ? 'CNY' : 'USD';
    
    return new Intl.NumberFormat(localeCode, {
      style: 'currency',
      currency: currencyCode
    }).format(value);
  };

  // 格式化日期
  const formatDate = (date: Date | string, options?: Intl.DateTimeFormatOptions) => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const localeCode = locale === 'zh' ? 'zh-CN' : 'en-US';
    
    const defaultOptions: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    };
    
    return new Intl.DateTimeFormat(localeCode, { ...defaultOptions, ...options }).format(dateObj);
  };

  // 格式化相对时间
  const formatRelativeTime = (date: Date | string) => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);
    
    const localeCode = locale === 'zh' ? 'zh-CN' : 'en-US';
    const rtf = new Intl.RelativeTimeFormat(localeCode, { numeric: 'auto' });
    
    if (diffInSeconds < 60) {
      return rtf.format(-diffInSeconds, 'second');
    } else if (diffInSeconds < 3600) {
      return rtf.format(-Math.floor(diffInSeconds / 60), 'minute');
    } else if (diffInSeconds < 86400) {
      return rtf.format(-Math.floor(diffInSeconds / 3600), 'hour');
    } else if (diffInSeconds < 2592000) {
      return rtf.format(-Math.floor(diffInSeconds / 86400), 'day');
    } else if (diffInSeconds < 31536000) {
      return rtf.format(-Math.floor(diffInSeconds / 2592000), 'month');
    } else {
      return rtf.format(-Math.floor(diffInSeconds / 31536000), 'year');
    }
  };

  // 获取本地化的错误消息
  const getErrorMessage = (errorKey: string, fallback?: string) => {
    try {
      return tErrors(errorKey);
    } catch {
      return fallback || tErrors('general');
    }
  };

  // 获取本地化的成功消息
  const getSuccessMessage = (successKey: string, fallback?: string) => {
    try {
      return tSuccess(successKey);
    } catch {
      return fallback || tSuccess('general');
    }
  };

  // 获取本地化的状态文本
  const getStatusText = (status: string, category: string = 'common') => {
    try {
      return t(`${category}.${status}`);
    } catch {
      return status;
    }
  };

  // 获取语言信息
  const getLanguageInfo = () => ({
    current: locale,
    name: localeNames[locale],
    flag: localeFlags[locale],
    isRTL: false, // 中文和英文都是从左到右
    available: locales.map(loc => ({
      code: loc,
      name: localeNames[loc],
      flag: localeFlags[loc]
    }))
  });

  return {
    // 当前语言
    locale,
    
    // 翻译函数
    t,
    tCommon,
    tNav,
    tSocialBet,
    tFortune,
    tCertification,
    tTraceability,
    tMonitoring,
    tErrors,
    tSuccess,
    
    // 语言切换
    changeLanguage,
    isPending,
    
    // 格式化函数
    formatNumber,
    formatCurrency,
    formatDate,
    formatRelativeTime,
    
    // 消息获取
    getErrorMessage,
    getSuccessMessage,
    getStatusText,
    
    // 语言信息
    getLanguageInfo,
    
    // 常用的翻译
    common: {
      loading: tCommon('loading'),
      error: tCommon('error'),
      success: tCommon('success'),
      cancel: tCommon('cancel'),
      confirm: tCommon('confirm'),
      save: tCommon('save'),
      submit: tCommon('submit'),
      retry: tCommon('retry'),
      back: tCommon('back'),
      next: tCommon('next')
    }
  };
}
