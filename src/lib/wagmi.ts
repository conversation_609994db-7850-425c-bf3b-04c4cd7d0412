/**
 * Wagmi 配置文件
 * 提供 Web3 相关的配置和常量
 */

import { createConfig, http } from 'wagmi';
import { bsc, bscTestnet } from 'wagmi/chains';

// ERC20 标准 ABI
export const ERC20_ABI = [
  {
    "constant": true,
    "inputs": [],
    "name": "name",
    "outputs": [{"name": "", "type": "string"}],
    "type": "function"
  },
  {
    "constant": true,
    "inputs": [],
    "name": "symbol",
    "outputs": [{"name": "", "type": "string"}],
    "type": "function"
  },
  {
    "constant": true,
    "inputs": [],
    "name": "decimals",
    "outputs": [{"name": "", "type": "uint8"}],
    "type": "function"
  },
  {
    "constant": true,
    "inputs": [],
    "name": "totalSupply",
    "outputs": [{"name": "", "type": "uint256"}],
    "type": "function"
  },
  {
    "constant": true,
    "inputs": [{"name": "_owner", "type": "address"}],
    "name": "balanceOf",
    "outputs": [{"name": "balance", "type": "uint256"}],
    "type": "function"
  },
  {
    "constant": false,
    "inputs": [
      {"name": "_to", "type": "address"},
      {"name": "_value", "type": "uint256"}
    ],
    "name": "transfer",
    "outputs": [{"name": "", "type": "bool"}],
    "type": "function"
  },
  {
    "constant": false,
    "inputs": [
      {"name": "_from", "type": "address"},
      {"name": "_to", "type": "address"},
      {"name": "_value", "type": "uint256"}
    ],
    "name": "transferFrom",
    "outputs": [{"name": "", "type": "bool"}],
    "type": "function"
  },
  {
    "constant": false,
    "inputs": [
      {"name": "_spender", "type": "address"},
      {"name": "_value", "type": "uint256"}
    ],
    "name": "approve",
    "outputs": [{"name": "", "type": "bool"}],
    "type": "function"
  },
  {
    "constant": true,
    "inputs": [
      {"name": "_owner", "type": "address"},
      {"name": "_spender", "type": "address"}
    ],
    "name": "allowance",
    "outputs": [{"name": "", "type": "uint256"}],
    "type": "function"
  }
] as const;

// 支持的链
export const chains = [bsc, bscTestnet] as const;

// Wagmi 配置
export const config = createConfig({
  chains,
  transports: {
    [bsc.id]: http(),
    [bscTestnet.id]: http(),
  },
});

// HAOX 合约地址
export const HAOX_CONTRACTS = {
  [bsc.id]: process.env.NEXT_PUBLIC_HAOX_TOKEN_ADDRESS || '******************************************',
  [bscTestnet.id]: process.env.NEXT_PUBLIC_HAOX_TOKEN_ADDRESS || '******************************************',
} as const;

// 获取当前链的合约地址
export function getHAOXContractAddress(chainId: number): string {
  return HAOX_CONTRACTS[chainId as keyof typeof HAOX_CONTRACTS] || HAOX_CONTRACTS[bscTestnet.id];
}

// 网络配置
export const NETWORK_CONFIG = {
  BSC_MAINNET: {
    chainId: bsc.id,
    name: bsc.name,
    rpcUrl: bsc.rpcUrls.default.http[0],
    blockExplorer: bsc.blockExplorers.default.url,
    nativeCurrency: bsc.nativeCurrency,
  },
  BSC_TESTNET: {
    chainId: bscTestnet.id,
    name: bscTestnet.name,
    rpcUrl: bscTestnet.rpcUrls.default.http[0],
    blockExplorer: bscTestnet.blockExplorers.default.url,
    nativeCurrency: bscTestnet.nativeCurrency,
  },
} as const;

export default config;