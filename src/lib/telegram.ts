/**
 * Telegram Bot 集成服务
 * 处理用户绑定、验证和分享检测
 */

interface TelegramUser {
  id: number;
  first_name: string;
  last_name?: string;
  username?: string;
  photo_url?: string;
}

interface TelegramAuthData {
  id: number;
  first_name: string;
  last_name?: string;
  username?: string;
  photo_url?: string;
  auth_date: number;
  hash: string;
}

interface ShareVerificationData {
  userId: number;
  messageId: number;
  chatId: number;
  shareType: 'group' | 'channel' | 'private';
  timestamp: number;
}

class TelegramService {
  private botToken: string;
  private botUsername: string;
  private apiUrl: string;

  constructor() {
    this.botToken = process.env.TELEGRAM_BOT_TOKEN || '';
    this.botUsername = process.env.TELEGRAM_BOT_USERNAME || 'SocioMintBot';
    this.apiUrl = `https://api.telegram.org/bot${this.botToken}`;
  }

  /**
   * 验证 Telegram 登录数据的真实性
   */
  verifyTelegramAuth(authData: TelegramAuthData): boolean {
    if (!this.botToken) {
      throw new Error('Telegram Bot Token not configured');
    }

    const { hash, ...data } = authData;
    
    // 创建数据字符串
    const dataCheckString = Object.keys(data)
      .sort()
      .map(key => `${key}=${data[key as keyof typeof data]}`)
      .join('\n');

    // 使用 Bot Token 创建密钥
    const crypto = require('crypto');
    const secretKey = crypto.createHash('sha256').update(this.botToken).digest();
    
    // 计算 HMAC
    const hmac = crypto.createHmac('sha256', secretKey).update(dataCheckString).digest('hex');
    
    // 验证哈希值
    return hmac === hash;
  }

  /**
   * 生成用户绑定链接
   */
  generateBindingLink(userId: string): string {
    const botUrl = `https://t.me/${this.botUsername}`;
    const startParam = `bind_${userId}_${Date.now()}`;

    return `${botUrl}?start=${startParam}`;
  }

  /**
   * 生成绑定验证码
   */
  generateBindingCode(userId: string): string {
    const timestamp = Date.now();
    const randomStr = Math.random().toString(36).substring(2, 8);
    return `${userId.substring(0, 8)}_${timestamp}_${randomStr}`;
  }

  /**
   * 检查是否为绑定验证码格式
   */
  private isBindingCode(text: string): boolean {
    // 验证码格式：8位用户ID_时间戳_6位随机字符
    const pattern = /^[a-f0-9]{8}_\d{13}_[a-z0-9]{6}$/;
    return pattern.test(text);
  }

  /**
   * 处理绑定验证码
   */
  private async handleBindingCode(chatId: number, bindingCode: string, userInfo: any): Promise<void> {
    try {
      // 验证绑定验证码
      const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/telegram/generate-code?code=${bindingCode}`);

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        // 验证码有效，执行绑定
        await this.handleUserBinding(chatId, data.data.userId, userInfo);
      } else {
        await this.sendMessage(chatId, `❌ 验证码无效或已过期

请确保：
1. 验证码格式正确
2. 验证码未过期（10分钟内有效）
3. 验证码未被使用过

请重新从网站获取验证码。`);
      }
    } catch (error) {
      console.error('Failed to handle binding code:', error);

      // 提供更详细的错误信息
      let errorMessage = '❌ 验证过程中发生错误，请稍后重试。';

      if (error instanceof Error) {
        if (error.message.includes('fetch')) {
          errorMessage = '❌ 网络连接错误，请检查网络后重试。';
        } else if (error.message.includes('API')) {
          errorMessage = '❌ 服务器暂时不可用，请稍后重试。';
        }
      }

      await this.sendMessage(chatId, errorMessage);
    }
  }

  /**
   * 生成专属推广内容
   */
  generatePromotionContent(userId: string, userInfo: TelegramUser): {
    text: string;
    imageUrl: string;
    shareUrl: string;
  } {
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3001';
    const referralCode = this.generateReferralCode(userId);
    
    const text = `🚀 SocioMint (HAOX) 预售火热进行中！

💎 革命性的社交挖矿平台
🎯 完成社交任务即可获得代币奖励
🔥 早期投资者专享优惠价格

✨ 我的专属邀请链接：
${baseUrl}/presale?ref=${referralCode}

📈 项目亮点：
• 社交即挖矿的创新模式
• 多平台任务生态系统  
• 商家认证与激励机制
• BSC 链上安全保障

⏰ 限时预售，机会难得！
立即参与：${baseUrl}

#SocioMint #HAOX #Web3 #社交挖矿 #预售`;

    const imageUrl = `${baseUrl}/api/generate-share-image?user=${encodeURIComponent(userInfo.first_name)}&ref=${referralCode}`;
    const shareUrl = `${baseUrl}/presale?ref=${referralCode}`;

    return { text, imageUrl, shareUrl };
  }

  /**
   * 生成推荐码
   */
  private generateReferralCode(userId: string): string {
    const crypto = require('crypto');
    return crypto.createHash('md5').update(`${userId}_${this.botToken}`).digest('hex').substring(0, 8).toUpperCase();
  }

  /**
   * 发送消息到 Telegram
   */
  async sendMessage(chatId: number, text: string, options: any = {}): Promise<any> {
    try {
      const response = await fetch(`${this.apiUrl}/sendMessage`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          chat_id: chatId,
          text,
          parse_mode: 'Markdown',
          ...options,
        }),
      });

      if (!response.ok) {
        throw new Error(`Telegram API error: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Failed to send Telegram message:', error);
      throw error;
    }
  }

  /**
   * 发送图片消息
   */
  async sendPhoto(chatId: number, photo: string, caption?: string): Promise<any> {
    try {
      const response = await fetch(`${this.apiUrl}/sendPhoto`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          chat_id: chatId,
          photo,
          caption,
          parse_mode: 'Markdown',
        }),
      });

      if (!response.ok) {
        throw new Error(`Telegram API error: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Failed to send Telegram photo:', error);
      throw error;
    }
  }

  /**
   * 获取聊天信息
   */
  async getChat(chatId: number): Promise<any> {
    try {
      const response = await fetch(`${this.apiUrl}/getChat?chat_id=${chatId}`);
      
      if (!response.ok) {
        throw new Error(`Telegram API error: ${response.statusText}`);
      }

      const data = await response.json();
      return data.result;
    } catch (error) {
      console.error('Failed to get chat info:', error);
      throw error;
    }
  }

  /**
   * 验证用户是否完成分享
   */
  async verifyShare(userId: number, messageId: number): Promise<ShareVerificationData | null> {
    try {
      // 这里需要实现分享验证逻辑
      // 可以通过检查消息转发、群组成员等方式验证
      
      // 示例实现：检查消息是否存在
      const response = await fetch(`${this.apiUrl}/getUpdates`);
      const data = await response.json();
      
      // 在实际实现中，这里需要更复杂的逻辑来验证分享
      // 比如检查用户是否转发了特定消息到群组或频道
      
      return {
        userId,
        messageId,
        chatId: userId, // 简化示例
        shareType: 'group',
        timestamp: Date.now(),
      };
    } catch (error) {
      console.error('Failed to verify share:', error);
      return null;
    }
  }

  /**
   * 设置 Webhook
   */
  async setWebhook(webhookUrl: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.apiUrl}/setWebhook`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          url: webhookUrl,
          allowed_updates: ['message', 'callback_query', 'channel_post'],
        }),
      });

      const data = await response.json();
      return data.ok;
    } catch (error) {
      console.error('Failed to set webhook:', error);
      return false;
    }
  }

  /**
   * 处理 Webhook 更新
   */
  async handleWebhookUpdate(update: any): Promise<void> {
    try {
      if (update.message) {
        await this.handleMessage(update.message);
      } else if (update.callback_query) {
        await this.handleCallbackQuery(update.callback_query);
      } else if (update.channel_post) {
        await this.handleChannelPost(update.channel_post);
      }
    } catch (error) {
      console.error('Failed to handle webhook update:', error);
    }
  }

  /**
   * 处理消息
   */
  private async handleMessage(message: any): Promise<void> {
    const chatId = message.chat.id;
    const text = message.text;

    if (text?.startsWith('/start')) {
      const startParam = text.split(' ')[1];

      if (startParam?.startsWith('bind_')) {
        // 发送绑定指引
        await this.sendMessage(chatId, `🔗 账户绑定

请发送您从 SocioMint 网站获得的验证码来完成绑定。

验证码格式类似：abc12345_1234567890_xyz789

请直接发送验证码（不需要任何前缀）。`);
      } else {
        // 发送欢迎消息
        await this.sendWelcomeMessage(chatId);
      }
    } else if (text && this.isBindingCode(text)) {
      // 处理绑定验证码
      await this.handleBindingCode(chatId, text, message.from);
    } else if (message.forward_from_chat || message.forward_from) {
      // 处理转发消息，验证分享
      await this.handleForwardedMessage(message);
    }
  }

  /**
   * 处理用户绑定
   */
  private async handleUserBinding(chatId: number, userId: string, userInfo: any): Promise<void> {
    try {
      // 调用 API 绑定用户
      const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/telegram/bind`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          telegramId: chatId,
          userInfo,
        }),
      });

      if (response.ok) {
        await this.sendMessage(chatId, `✅ 账户绑定成功！

您已成功绑定 SocioMint 账户。现在您可以：

1️⃣ 获取专属推广内容
2️⃣ 分享到群组或频道
3️⃣ 获得预售认购资格

点击下方按钮获取推广内容 👇`, {
          reply_markup: {
            inline_keyboard: [[
              { text: '🎯 获取推广内容', callback_data: `get_content_${userId}` }
            ]]
          }
        });
      } else {
        await this.sendMessage(chatId, '❌ 绑定失败，请稍后重试或联系客服。');
      }
    } catch (error) {
      console.error('Failed to handle user binding:', error);
      await this.sendMessage(chatId, '❌ 绑定过程中发生错误，请稍后重试。');
    }
  }

  /**
   * 处理回调查询
   */
  private async handleCallbackQuery(callbackQuery: any): Promise<void> {
    const chatId = callbackQuery.message.chat.id;
    const data = callbackQuery.data;

    if (data.startsWith('get_content_')) {
      const userId = data.split('_')[2];
      await this.sendPromotionContent(chatId, userId, callbackQuery.from);
    }

    // 回应回调查询
    await fetch(`${this.apiUrl}/answerCallbackQuery`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        callback_query_id: callbackQuery.id,
      }),
    });
  }

  /**
   * 发送推广内容
   */
  private async sendPromotionContent(chatId: number, userId: string, userInfo: any): Promise<void> {
    try {
      const content = this.generatePromotionContent(userId, userInfo);
      
      // 发送推广图片和文字
      await this.sendPhoto(chatId, content.imageUrl, content.text);
      
      await this.sendMessage(chatId, `📤 请将上述内容分享到您的群组或频道中，分享完成后我们会自动检测并为您开通预售资格。

💡 分享提示：
• 可以分享到任何群组或频道
• 分享后请等待几分钟进行验证
• 每个账户只能参与一次预售

🔗 直接访问预售页面：
${content.shareUrl}`, {
        reply_markup: {
          inline_keyboard: [[
            { text: '🌐 访问预售页面', url: content.shareUrl }
          ]]
        }
      });
    } catch (error) {
      console.error('Failed to send promotion content:', error);
      await this.sendMessage(chatId, '❌ 获取推广内容失败，请稍后重试。');
    }
  }

  /**
   * 处理转发消息
   */
  private async handleForwardedMessage(message: any): Promise<void> {
    try {
      // 验证是否是推广内容的转发
      if (message.caption?.includes('SocioMint') || message.text?.includes('SocioMint')) {
        // 调用 API 验证分享
        await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/telegram/verify-share`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            telegramId: message.from.id,
            messageId: message.message_id,
            chatId: message.chat.id,
            forwardInfo: {
              from_chat: message.forward_from_chat,
              from_user: message.forward_from,
            },
          }),
        });

        await this.sendMessage(message.chat.id, '✅ 分享验证成功！您已获得预售认购资格。');
      }
    } catch (error) {
      console.error('Failed to handle forwarded message:', error);
    }
  }

  /**
   * 发送欢迎消息
   */
  private async sendWelcomeMessage(chatId: number): Promise<void> {
    const welcomeText = `🎉 欢迎使用 SocioMint Bot！

SocioMint 是一个革命性的社交挖矿平台，通过完成社交任务获得代币奖励。

🚀 当前功能：
• 预售账户绑定
• 专属推广内容生成
• 分享验证和资格获取

💡 如需绑定账户，请从官网点击"绑定 Telegram"按钮。

🌐 官网：${process.env.NEXT_PUBLIC_APP_URL}`;

    await this.sendMessage(chatId, welcomeText, {
      reply_markup: {
        inline_keyboard: [[
          { text: '🌐 访问官网', url: process.env.NEXT_PUBLIC_APP_URL || 'https://sociomint.com' }
        ]]
      }
    });
  }

  /**
   * 处理频道消息
   */
  private async handleChannelPost(channelPost: any): Promise<void> {
    // 处理频道消息的逻辑
    console.log('Channel post received:', channelPost);
  }
}

// 创建单例实例
const telegramService = new TelegramService();

export default telegramService;
export { TelegramService };
