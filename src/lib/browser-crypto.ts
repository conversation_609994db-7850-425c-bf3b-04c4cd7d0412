'use client';

/**
 * 浏览器兼容的加密工具
 * 解决 React Hydration 错误，替代 Node.js crypto 模块
 */

/**
 * 生成安全的随机 ID
 * @param length ID 长度，默认 8
 * @returns 随机 ID 字符串
 */
export function generateSecureId(length: number = 8): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  
  if (typeof window !== 'undefined' && window.crypto?.getRandomValues) {
    // 使用 Web Crypto API（现代浏览器支持）
    const randomValues = new Uint8Array(length);
    window.crypto.getRandomValues(randomValues);
    for (let i = 0; i < length; i++) {
      result += chars[randomValues[i] % chars.length];
    }
  } else {
    // 降级方案：使用 Math.random
    for (let i = 0; i < length; i++) {
      result += chars[Math.floor(Math.random() * chars.length)];
    }
  }
  
  return result;
}

/**
 * 生成安全的随机字符串
 * @param length 字符串长度
 * @param charset 字符集，默认为数字和大写字母
 * @returns 随机字符串
 */
export function generateSecureCode(length: number = 6, charset?: string): string {
  const defaultCharset = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const chars = charset || defaultCharset;
  let result = '';
  
  if (typeof window !== 'undefined' && window.crypto?.getRandomValues) {
    const randomValues = new Uint8Array(length);
    window.crypto.getRandomValues(randomValues);
    
    for (let i = 0; i < length; i++) {
      result += chars[randomValues[i] % chars.length];
    }
  } else {
    // 降级到 Math.random
    for (let i = 0; i < length; i++) {
      result += chars[Math.floor(Math.random() * chars.length)];
    }
  }
  
  return result;
}

/**
 * 生成安全的随机数字
 * @param min 最小值（包含）
 * @param max 最大值（不包含）
 * @returns 随机数字
 */
export function generateSecureNumber(min: number = 0, max: number = 1): number {
  if (min >= max) {
    throw new Error('min must be less than max');
  }
  
  let randomValue: number;
  
  if (typeof window !== 'undefined' && window.crypto?.getRandomValues) {
    const randomArray = new Uint32Array(1);
    window.crypto.getRandomValues(randomArray);
    randomValue = randomArray[0] / 0xFFFFFFFF;
  } else {
    randomValue = Math.random();
  }
  
  return Math.floor(min + randomValue * (max - min));
}

/**
 * 生成安全的随机浮点数
 * @param min 最小值（包含）
 * @param max 最大值（不包含）
 * @returns 随机浮点数
 */
export function generateSecureFloat(min: number = 0, max: number = 1): number {
  if (min >= max) {
    throw new Error('min must be less than max');
  }
  
  let randomValue: number;
  
  if (typeof window !== 'undefined' && window.crypto?.getRandomValues) {
    const randomArray = new Uint32Array(1);
    window.crypto.getRandomValues(randomArray);
    randomValue = randomArray[0] / 0xFFFFFFFF;
  } else {
    randomValue = Math.random();
  }
  
  return min + randomValue * (max - min);
}

/**
 * 生成安全的随机布尔值
 * @returns 随机布尔值
 */
export function generateSecureBoolean(): boolean {
  if (typeof window !== 'undefined' && window.crypto?.getRandomValues) {
    const randomArray = new Uint8Array(1);
    window.crypto.getRandomValues(randomArray);
    return randomArray[0] >= 128;
  } else {
    return Math.random() >= 0.5;
  }
}

/**
 * 生成安全的随机 UUID v4（简化版本）
 * @returns UUID 字符串
 */
export function generateSecureUUID(): string {
  if (typeof window !== 'undefined' && window.crypto?.getRandomValues) {
    const randomValues = new Uint8Array(16);
    window.crypto.getRandomValues(randomValues);
    
    // 设置版本号 (4) 和变体位
    randomValues[6] = (randomValues[6] & 0x0f) | 0x40; // 版本 4
    randomValues[8] = (randomValues[8] & 0x3f) | 0x80; // 变体 10
    
    const hex = Array.from(randomValues)
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');
    
    return [
      hex.slice(0, 8),
      hex.slice(8, 12),
      hex.slice(12, 16),
      hex.slice(16, 20),
      hex.slice(20, 32)
    ].join('-');
  } else {
    // 降级方案：使用时间戳 + 随机数
    const timestamp = Date.now().toString(16);
    const random1 = Math.random().toString(16).substr(2, 8);
    const random2 = Math.random().toString(16).substr(2, 8);
    const random3 = Math.random().toString(16).substr(2, 8);
    
    return `${timestamp.slice(0, 8)}-${random1.slice(0, 4)}-4${random1.slice(4, 7)}-${random2.slice(0, 4)}-${random2.slice(4)}${random3.slice(0, 4)}`;
  }
}

/**
 * 生成安全的随机十六进制字符串
 * @param length 字节长度
 * @returns 十六进制字符串
 */
export function generateSecureHex(length: number = 16): string {
  if (typeof window !== 'undefined' && window.crypto?.getRandomValues) {
    const randomValues = new Uint8Array(length);
    window.crypto.getRandomValues(randomValues);
    return Array.from(randomValues)
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');
  } else {
    let result = '';
    const hexChars = '0123456789abcdef';
    for (let i = 0; i < length * 2; i++) {
      result += hexChars[Math.floor(Math.random() * 16)];
    }
    return result;
  }
}

/**
 * 生成安全的随机 Base64 字符串
 * @param length 字节长度
 * @returns Base64 字符串
 */
export function generateSecureBase64(length: number = 16): string {
  if (typeof window !== 'undefined' && window.crypto?.getRandomValues) {
    const randomValues = new Uint8Array(length);
    window.crypto.getRandomValues(randomValues);
    return btoa(String.fromCharCode(...randomValues));
  } else {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars[Math.floor(Math.random() * chars.length)];
    }
    return result;
  }
}

/**
 * 从数组中安全随机选择元素
 * @param array 源数组
 * @param count 选择数量，默认为1
 * @returns 选择的元素数组
 */
export function secureRandomChoice<T>(array: T[], count: number = 1): T[] {
  if (array.length === 0) {
    return [];
  }
  
  if (count >= array.length) {
    return [...array];
  }
  
  const result: T[] = [];
  const indices = new Set<number>();
  
  while (result.length < count) {
    const randomIndex = generateSecureNumber(0, array.length);
    if (!indices.has(randomIndex)) {
      indices.add(randomIndex);
      result.push(array[randomIndex]);
    }
  }
  
  return result;
}

/**
 * 安全随机打乱数组
 * @param array 源数组
 * @returns 打乱后的新数组
 */
export function secureRandomShuffle<T>(array: T[]): T[] {
  const result = [...array];
  
  for (let i = result.length - 1; i > 0; i--) {
    const j = generateSecureNumber(0, i + 1);
    [result[i], result[j]] = [result[j], result[i]];
  }
  
  return result;
}

// 导出便捷的替换函数，用于替换 Math.random()
export const secureRandom = {
  /**
   * 替换 Math.random()，返回 [0, 1) 范围内的随机数
   */
  random: (): number => generateSecureFloat(0, 1),
  
  /**
   * 替换 Math.floor(Math.random() * max)
   */
  randomInt: (max: number): number => generateSecureNumber(0, max),
  
  /**
   * 替换 Math.random() > 0.5
   */
  randomBoolean: (): boolean => generateSecureBoolean(),
};
