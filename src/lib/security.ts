// import DOMPurify from 'isomorphic-dompurify'; // 需要安装 isomorphic-dompurify

/**
 * Security utilities for input validation and sanitization
 */

// Input sanitization
export const sanitize = {
  /**
   * Sanitize HTML content to prevent XSS
   */
  html(dirty: string): string {
    // 简单的 HTML 清理，生产环境建议使用 DOMPurify
    return dirty
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '');
  },

  /**
   * Sanitize text input
   */
  text(input: string): string {
    return input
      .trim()
      .replace(/[<>]/g, '') // Remove potential HTML tags
      .slice(0, 1000); // Limit length
  },

  /**
   * Sanitize email input
   */
  email(email: string): string {
    return email.toLowerCase().trim().slice(0, 254);
  },

  /**
   * Sanitize URL input
   */
  url(url: string): string {
    try {
      const parsed = new URL(url);
      // Only allow http and https protocols
      if (!['http:', 'https:'].includes(parsed.protocol)) {
        throw new Error('Invalid protocol');
      }
      return parsed.toString();
    } catch {
      return '';
    }
  },
};

// Input validation
export const validate = {
  /**
   * Validate email format
   */
  email(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email) && email.length <= 254;
  },

  /**
   * Validate password strength
   */
  password(password: string): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];
    
    if (password.length < 8) {
      errors.push('密码至少需要8个字符');
    }
    
    if (!/[a-z]/.test(password)) {
      errors.push('密码需要包含小写字母');
    }
    
    if (!/[A-Z]/.test(password)) {
      errors.push('密码需要包含大写字母');
    }
    
    if (!/\d/.test(password)) {
      errors.push('密码需要包含数字');
    }
    
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('密码需要包含特殊字符');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  },

  /**
   * Validate username
   */
  username(username: string): boolean {
    const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
    return usernameRegex.test(username);
  },

  /**
   * Validate phone number (Chinese format)
   */
  phone(phone: string): boolean {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
  },

  /**
   * Validate wallet address (Ethereum format)
   */
  walletAddress(address: string): boolean {
    const addressRegex = /^0x[a-fA-F0-9]{40}$/;
    return addressRegex.test(address);
  },

  /**
   * Validate transaction hash
   */
  txHash(hash: string): boolean {
    const hashRegex = /^0x[a-fA-F0-9]{64}$/;
    return hashRegex.test(hash);
  },

  /**
   * Validate amount (positive number with max 18 decimals)
   */
  amount(amount: string): boolean {
    const amountRegex = /^\d+(\.\d{1,18})?$/;
    const num = parseFloat(amount);
    return amountRegex.test(amount) && num > 0 && num < Number.MAX_SAFE_INTEGER;
  },
};

// Rate limiting utilities
export const rateLimit = {
  /**
   * Simple in-memory rate limiter
   */
  attempts: new Map<string, { count: number; resetTime: number }>(),

  /**
   * Check if action is rate limited
   */
  isLimited(key: string, maxAttempts: number = 5, windowMs: number = 15 * 60 * 1000): boolean {
    const now = Date.now();
    const record = this.attempts.get(key);

    if (!record || now > record.resetTime) {
      this.attempts.set(key, { count: 1, resetTime: now + windowMs });
      return false;
    }

    if (record.count >= maxAttempts) {
      return true;
    }

    record.count++;
    return false;
  },

  /**
   * Reset rate limit for a key
   */
  reset(key: string): void {
    this.attempts.delete(key);
  },
};

// CSRF protection
export const csrf = {
  /**
   * Generate CSRF token
   */
  generateToken(): string {
    return crypto.randomUUID();
  },

  /**
   * Validate CSRF token
   */
  validateToken(token: string, sessionToken: string): boolean {
    return token === sessionToken && token.length > 0;
  },
};

// Content Security Policy helpers
export const csp = {
  /**
   * Generate nonce for inline scripts
   */
  generateNonce(): string {
    const array = new Uint8Array(16);
    crypto.getRandomValues(array);
    return btoa(String.fromCharCode.apply(null, Array.from(array)));
  },

  /**
   * Get CSP header value
   */
  getHeaderValue(nonce?: string): string {
    const directives = [
      "default-src 'self'",
      "script-src 'self' 'unsafe-eval'",
      "style-src 'self' 'unsafe-inline'",
      "img-src 'self' data: https:",
      "font-src 'self' data:",
      "connect-src 'self' https://api.web3modal.org https://*.alchemy.com https://*.supabase.co",
      "frame-src 'none'",
      "object-src 'none'",
      "base-uri 'self'",
      "form-action 'self'",
      "frame-ancestors 'none'",
    ];

    if (nonce) {
      directives[1] += ` 'nonce-${nonce}'`;
    }

    return directives.join('; ');
  },
};

// Audit logging
export const auditLog = {
  /**
   * Log security events
   */
  log(event: {
    type: 'auth' | 'transaction' | 'admin' | 'error';
    action: string;
    userId?: string;
    ip?: string;
    userAgent?: string;
    metadata?: Record<string, any>;
  }): void {
    const logEntry = {
      ...event,
      timestamp: new Date().toISOString(),
      id: crypto.randomUUID(),
    };

    // In production, send to logging service
    if (process.env.NODE_ENV === 'production') {
      // Example: Send to external logging service
      console.log('[AUDIT]', logEntry);
    } else {
      console.log('[AUDIT]', logEntry);
    }
  },
};
