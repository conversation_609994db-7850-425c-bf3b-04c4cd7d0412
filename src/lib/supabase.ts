import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

// 检查是否为开发环境的占位符
const isPlaceholder = supabaseUrl.includes('placeholder') || supabaseAnonKey.includes('placeholder');

// Client-side Supabase client
export const supabase = isPlaceholder
  ? null // 在占位符环境中返回 null
  : createClient(supabaseUrl, supabaseAnonKey);

// 开发环境标志
export const isDevelopmentMode = isPlaceholder;

// Database Types
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          email: string;
          username: string;
          avatar_url: string | null;
          wallet_address: string | null;
          is_merchant: boolean;
          merchant_status: 'pending' | 'approved' | 'rejected' | null;
          // 福气系统字段
          available_fortune: number;
          locked_fortune: number;
          total_fortune_earned: number;
          total_fortune_spent: number;
          fortune_level: number;
          fortune_level_name: string;
          consecutive_checkin_days: number;
          last_checkin_date: string | null;
          total_checkin_days: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          email: string;
          username: string;
          avatar_url?: string | null;
          wallet_address?: string | null;
          is_merchant?: boolean;
          merchant_status?: 'pending' | 'approved' | 'rejected' | null;
          // 福气系统字段
          available_fortune?: number;
          locked_fortune?: number;
          total_fortune_earned?: number;
          total_fortune_spent?: number;
          fortune_level?: number;
          fortune_level_name?: string;
          consecutive_checkin_days?: number;
          last_checkin_date?: string | null;
          total_checkin_days?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          username?: string;
          avatar_url?: string | null;
          wallet_address?: string | null;
          is_merchant?: boolean;
          merchant_status?: 'pending' | 'approved' | 'rejected' | null;
          // 福气系统字段
          available_fortune?: number;
          locked_fortune?: number;
          total_fortune_earned?: number;
          total_fortune_spent?: number;
          fortune_level?: number;
          fortune_level_name?: string;
          consecutive_checkin_days?: number;
          last_checkin_date?: string | null;
          total_checkin_days?: number;
          created_at?: string;
          updated_at?: string;
        };
      };
      fortune_transactions: {
        Row: {
          id: string;
          user_id: string;
          transaction_type: string;
          amount: number;
          balance_before: number;
          balance_after: number;
          reference_id: string | null;
          reference_type: string | null;
          description: string;
          haox_tx_hash: string | null;
          haox_amount: number | null;
          exchange_rate: number;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          transaction_type: string;
          amount: number;
          balance_before: number;
          balance_after: number;
          reference_id?: string | null;
          reference_type?: string | null;
          description: string;
          haox_tx_hash?: string | null;
          haox_amount?: number | null;
          exchange_rate?: number;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          transaction_type?: string;
          amount?: number;
          balance_before?: number;
          balance_after?: number;
          reference_id?: string | null;
          reference_type?: string | null;
          description?: string;
          haox_tx_hash?: string | null;
          haox_amount?: number | null;
          exchange_rate?: number;
          created_at?: string;
        };
      };
      daily_checkins: {
        Row: {
          id: string;
          user_id: string;
          checkin_date: string;
          consecutive_days: number;
          base_reward: number;
          bonus_reward: number;
          total_reward: number;
          fortune_transaction_id: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          checkin_date: string;
          consecutive_days: number;
          base_reward: number;
          bonus_reward?: number;
          total_reward: number;
          fortune_transaction_id?: string | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          checkin_date?: string;
          consecutive_days?: number;
          base_reward?: number;
          bonus_reward?: number;
          total_reward?: number;
          fortune_transaction_id?: string | null;
          created_at?: string;
        };
      };
      share_rewards: {
        Row: {
          id: string;
          user_id: string;
          shared_content_type: string;
          shared_content_id: string;
          platform: string;
          reward_amount: number;
          fortune_transaction_id: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          shared_content_type: string;
          shared_content_id: string;
          platform: string;
          reward_amount: number;
          fortune_transaction_id?: string | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          shared_content_type?: string;
          shared_content_id?: string;
          platform?: string;
          reward_amount?: number;
          fortune_transaction_id?: string | null;
          created_at?: string;
        };
      };
      fortune_levels: {
        Row: {
          level: number;
          level_name: string;
          min_fortune: number;
          max_fortune: number | null;
          benefits: any | null;
          created_at: string;
        };
        Insert: {
          level: number;
          level_name: string;
          min_fortune: number;
          max_fortune?: number | null;
          benefits?: any | null;
          created_at?: string;
        };
        Update: {
          level?: number;
          level_name?: string;
          min_fortune?: number;
          max_fortune?: number | null;
          benefits?: any | null;
          created_at?: string;
        };
      };
      social_accounts: {
        Row: {
          id: string;
          user_id: string;
          platform: 'twitter' | 'discord' | 'telegram';
          platform_user_id: string;
          platform_username: string;
          is_verified: boolean;
          connected_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          platform: 'twitter' | 'discord' | 'telegram';
          platform_user_id: string;
          platform_username: string;
          is_verified?: boolean;
          connected_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          platform?: 'twitter' | 'discord' | 'telegram';
          platform_user_id?: string;
          platform_username?: string;
          is_verified?: boolean;
          connected_at?: string;
        };
      };
      transactions: {
        Row: {
          id: string;
          user_id: string;
          type: 'buy' | 'sell';
          token_amount: string;
          fiat_amount: number;
          fiat_currency: 'CNY' | 'USD';
          payment_method: 'alipay' | 'wechat' | 'crypto';
          status: 'pending' | 'completed' | 'failed' | 'cancelled';
          tx_hash: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          type: 'buy' | 'sell';
          token_amount: string;
          fiat_amount: number;
          fiat_currency: 'CNY' | 'USD';
          payment_method: 'alipay' | 'wechat' | 'crypto';
          status?: 'pending' | 'completed' | 'failed' | 'cancelled';
          tx_hash?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          type?: 'buy' | 'sell';
          token_amount?: string;
          fiat_amount?: number;
          fiat_currency?: 'CNY' | 'USD';
          payment_method?: 'alipay' | 'wechat' | 'crypto';
          status?: 'pending' | 'completed' | 'failed' | 'cancelled';
          tx_hash?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      merchant_applications: {
        Row: {
          id: string;
          user_id: string;
          business_name: string;
          business_type: string;
          business_license: string;
          contact_person: string;
          contact_phone: string;
          contact_email: string;
          description: string;
          status: 'pending' | 'approved' | 'rejected';
          reviewed_by: string | null;
          reviewed_at: string | null;
          rejection_reason: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          business_name: string;
          business_type: string;
          business_license: string;
          contact_person: string;
          contact_phone: string;
          contact_email: string;
          description: string;
          status?: 'pending' | 'approved' | 'rejected';
          reviewed_by?: string | null;
          reviewed_at?: string | null;
          rejection_reason?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          business_name?: string;
          business_type?: string;
          business_license?: string;
          contact_person?: string;
          contact_phone?: string;
          contact_email?: string;
          description?: string;
          status?: 'pending' | 'approved' | 'rejected';
          reviewed_by?: string | null;
          reviewed_at?: string | null;
          rejection_reason?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      social_tasks: {
        Row: {
          id: string;
          title: string;
          description: string;
          platform: 'twitter' | 'discord' | 'telegram';
          task_type: 'follow' | 'like' | 'retweet' | 'join' | 'share';
          reward_amount: number;
          max_completions: number;
          current_completions: number;
          is_active: boolean;
          expires_at: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          title: string;
          description: string;
          platform: 'twitter' | 'discord' | 'telegram';
          task_type: 'follow' | 'like' | 'retweet' | 'join' | 'share';
          reward_amount: number;
          max_completions: number;
          current_completions?: number;
          is_active?: boolean;
          expires_at?: string | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          title?: string;
          description?: string;
          platform?: 'twitter' | 'discord' | 'telegram';
          task_type?: 'follow' | 'like' | 'retweet' | 'join' | 'share';
          reward_amount?: number;
          max_completions?: number;
          current_completions?: number;
          is_active?: boolean;
          expires_at?: string | null;
          created_at?: string;
        };
      };
      user_task_completions: {
        Row: {
          id: string;
          user_id: string;
          task_id: string;
          completed_at: string;
          reward_claimed: boolean;
          verification_data: any | null;
        };
        Insert: {
          id?: string;
          user_id: string;
          task_id: string;
          completed_at?: string;
          reward_claimed?: boolean;
          verification_data?: any | null;
        };
        Update: {
          id?: string;
          user_id?: string;
          task_id?: string;
          completed_at?: string;
          reward_claimed?: boolean;
          verification_data?: any | null;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
  };
}

// Typed Supabase client
export type SupabaseClient = typeof supabase;
