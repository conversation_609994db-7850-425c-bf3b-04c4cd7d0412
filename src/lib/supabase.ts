import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

// 检查是否为开发环境的占位符
const isPlaceholder = supabaseUrl.includes('placeholder') || supabaseAnonKey.includes('placeholder');

// Client-side Supabase client
export const supabase = isPlaceholder
  ? null // 在占位符环境中返回 null
  : createClient(supabaseUrl, supabaseAnonKey);

// 开发环境标志
export const isDevelopmentMode = isPlaceholder;

// Database Types
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          email: string;
          username: string;
          avatar_url: string | null;
          wallet_address: string | null;
          is_merchant: boolean;
          merchant_status: 'pending' | 'approved' | 'rejected' | null;
          // 福气系统字段
          available_fortune: number;
          locked_fortune: number;
          total_fortune_earned: number;
          total_fortune_spent: number;
          fortune_level: number;
          fortune_level_name: string;
          consecutive_checkin_days: number;
          last_checkin_date: string | null;
          total_checkin_days: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          email: string;
          username: string;
          avatar_url?: string | null;
          wallet_address?: string | null;
          is_merchant?: boolean;
          merchant_status?: 'pending' | 'approved' | 'rejected' | null;
          // 福气系统字段
          available_fortune?: number;
          locked_fortune?: number;
          total_fortune_earned?: number;
          total_fortune_spent?: number;
          fortune_level?: number;
          fortune_level_name?: string;
          consecutive_checkin_days?: number;
          last_checkin_date?: string | null;
          total_checkin_days?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          username?: string;
          avatar_url?: string | null;
          wallet_address?: string | null;
          is_merchant?: boolean;
          merchant_status?: 'pending' | 'approved' | 'rejected' | null;
          // 福气系统字段
          available_fortune?: number;
          locked_fortune?: number;
          total_fortune_earned?: number;
          total_fortune_spent?: number;
          fortune_level?: number;
          fortune_level_name?: string;
          consecutive_checkin_days?: number;
          last_checkin_date?: string | null;
          total_checkin_days?: number;
          created_at?: string;
          updated_at?: string;
        };
      };
      fortune_transactions: {
        Row: {
          id: string;
          user_id: string;
          transaction_type: string;
          amount: number;
          balance_before: number;
          balance_after: number;
          reference_id: string | null;
          reference_type: string | null;
          description: string;
          haox_tx_hash: string | null;
          haox_amount: number | null;
          exchange_rate: number;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          transaction_type: string;
          amount: number;
          balance_before: number;
          balance_after: number;
          reference_id?: string | null;
          reference_type?: string | null;
          description: string;
          haox_tx_hash?: string | null;
          haox_amount?: number | null;
          exchange_rate?: number;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          transaction_type?: string;
          amount?: number;
          balance_before?: number;
          balance_after?: number;
          reference_id?: string | null;
          reference_type?: string | null;
          description?: string;
          haox_tx_hash?: string | null;
          haox_amount?: number | null;
          exchange_rate?: number;
          created_at?: string;
        };
      };
      daily_checkins: {
        Row: {
          id: string;
          user_id: string;
          checkin_date: string;
          consecutive_days: number;
          base_reward: number;
          bonus_reward: number;
          total_reward: number;
          fortune_transaction_id: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          checkin_date: string;
          consecutive_days: number;
          base_reward: number;
          bonus_reward?: number;
          total_reward: number;
          fortune_transaction_id?: string | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          checkin_date?: string;
          consecutive_days?: number;
          base_reward?: number;
          bonus_reward?: number;
          total_reward?: number;
          fortune_transaction_id?: string | null;
          created_at?: string;
        };
      };
      share_rewards: {
        Row: {
          id: string;
          user_id: string;
          shared_content_type: string;
          shared_content_id: string;
          platform: string;
          reward_amount: number;
          fortune_transaction_id: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          shared_content_type: string;
          shared_content_id: string;
          platform: string;
          reward_amount: number;
          fortune_transaction_id?: string | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          shared_content_type?: string;
          shared_content_id?: string;
          platform?: string;
          reward_amount?: number;
          fortune_transaction_id?: string | null;
          created_at?: string;
        };
      };
      fortune_levels: {
        Row: {
          level: number;
          level_name: string;
          min_fortune: number;
          max_fortune: number | null;
          benefits: any | null;
          created_at: string;
        };
        Insert: {
          level: number;
          level_name: string;
          min_fortune: number;
          max_fortune?: number | null;
          benefits?: any | null;
          created_at?: string;
        };
        Update: {
          level?: number;
          level_name?: string;
          min_fortune?: number;
          max_fortune?: number | null;
          benefits?: any | null;
          created_at?: string;
        };
      };
      social_accounts: {
        Row: {
          id: string;
          user_id: string;
          platform: 'twitter' | 'discord' | 'telegram';
          platform_user_id: string;
          platform_username: string;
          is_verified: boolean;
          connected_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          platform: 'twitter' | 'discord' | 'telegram';
          platform_user_id: string;
          platform_username: string;
          is_verified?: boolean;
          connected_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          platform?: 'twitter' | 'discord' | 'telegram';
          platform_user_id?: string;
          platform_username?: string;
          is_verified?: boolean;
          connected_at?: string;
        };
      };
      transactions: {
        Row: {
          id: string;
          user_id: string;
          type: 'buy' | 'sell';
          token_amount: string;
          fiat_amount: number;
          fiat_currency: 'CNY' | 'USD';
          payment_method: 'alipay' | 'wechat' | 'crypto';
          status: 'pending' | 'completed' | 'failed' | 'cancelled';
          tx_hash: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          type: 'buy' | 'sell';
          token_amount: string;
          fiat_amount: number;
          fiat_currency: 'CNY' | 'USD';
          payment_method: 'alipay' | 'wechat' | 'crypto';
          status?: 'pending' | 'completed' | 'failed' | 'cancelled';
          tx_hash?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          type?: 'buy' | 'sell';
          token_amount?: string;
          fiat_amount?: number;
          fiat_currency?: 'CNY' | 'USD';
          payment_method?: 'alipay' | 'wechat' | 'crypto';
          status?: 'pending' | 'completed' | 'failed' | 'cancelled';
          tx_hash?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      merchant_applications: {
        Row: {
          id: string;
          user_id: string;
          business_name: string;
          business_type: string;
          business_license: string;
          contact_person: string;
          contact_phone: string;
          contact_email: string;
          description: string;
          status: 'pending' | 'approved' | 'rejected';
          reviewed_by: string | null;
          reviewed_at: string | null;
          rejection_reason: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          business_name: string;
          business_type: string;
          business_license: string;
          contact_person: string;
          contact_phone: string;
          contact_email: string;
          description: string;
          status?: 'pending' | 'approved' | 'rejected';
          reviewed_by?: string | null;
          reviewed_at?: string | null;
          rejection_reason?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          business_name?: string;
          business_type?: string;
          business_license?: string;
          contact_person?: string;
          contact_phone?: string;
          contact_email?: string;
          description?: string;
          status?: 'pending' | 'approved' | 'rejected';
          reviewed_by?: string | null;
          reviewed_at?: string | null;
          rejection_reason?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      social_tasks: {
        Row: {
          id: string;
          title: string;
          description: string;
          platform: 'twitter' | 'discord' | 'telegram';
          task_type: 'follow' | 'like' | 'retweet' | 'join' | 'share';
          reward_amount: number;
          max_completions: number;
          current_completions: number;
          is_active: boolean;
          expires_at: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          title: string;
          description: string;
          platform: 'twitter' | 'discord' | 'telegram';
          task_type: 'follow' | 'like' | 'retweet' | 'join' | 'share';
          reward_amount: number;
          max_completions: number;
          current_completions?: number;
          is_active?: boolean;
          expires_at?: string | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          title?: string;
          description?: string;
          platform?: 'twitter' | 'discord' | 'telegram';
          task_type?: 'follow' | 'like' | 'retweet' | 'join' | 'share';
          reward_amount?: number;
          max_completions?: number;
          current_completions?: number;
          is_active?: boolean;
          expires_at?: string | null;
          created_at?: string;
        };
      };
      user_task_completions: {
        Row: {
          id: string;
          user_id: string;
          task_id: string;
          completed_at: string;
          reward_claimed: boolean;
          verification_data: any | null;
        };
        Insert: {
          id?: string;
          user_id: string;
          task_id: string;
          completed_at?: string;
          reward_claimed?: boolean;
          verification_data?: any | null;
        };
        Update: {
          id?: string;
          user_id?: string;
          task_id?: string;
          completed_at?: string;
          reward_claimed?: boolean;
          verification_data?: any | null;
        };
      };
      social_bets: {
        Row: {
          id: string;
          title: string;
          description: string;
          category: string;
          bet_type: '1v1' | '1vN';
          template_type: string | null;
          creator_id: string;
          options: any; // JSONB
          min_bet_amount: number;
          max_bet_amount: number | null;
          total_pool: number;
          target_user_id: string | null;
          creator_option: string | null;
          target_option: string | null;
          betting_deadline: string;
          result_deadline: string;
          status: 'open' | 'betting_closed' | 'judging' | 'confirming' | 'settled' | 'cancelled' | 'expired';
          requires_judgment: boolean;
          judgment_start_time: string | null;
          current_judgment_round: number;
          winning_option: string | null;
          result_confirmed_by_creator: boolean;
          result_confirmed_by_participants: boolean;
          result_confirmation_deadline: string | null;
          platform_fee_rate: number;
          referral_reward_rate: number;
          tags: string[] | null;
          is_featured: boolean;
          view_count: number;
          participant_count: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          title: string;
          description: string;
          category: string;
          bet_type: '1v1' | '1vN';
          template_type?: string | null;
          creator_id: string;
          options: any;
          min_bet_amount?: number;
          max_bet_amount?: number | null;
          total_pool?: number;
          target_user_id?: string | null;
          creator_option?: string | null;
          target_option?: string | null;
          betting_deadline: string;
          result_deadline: string;
          status?: 'open' | 'betting_closed' | 'judging' | 'confirming' | 'settled' | 'cancelled' | 'expired';
          requires_judgment?: boolean;
          judgment_start_time?: string | null;
          current_judgment_round?: number;
          winning_option?: string | null;
          result_confirmed_by_creator?: boolean;
          result_confirmed_by_participants?: boolean;
          result_confirmation_deadline?: string | null;
          platform_fee_rate?: number;
          referral_reward_rate?: number;
          tags?: string[] | null;
          is_featured?: boolean;
          view_count?: number;
          participant_count?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          title?: string;
          description?: string;
          category?: string;
          bet_type?: '1v1' | '1vN';
          template_type?: string | null;
          creator_id?: string;
          options?: any;
          min_bet_amount?: number;
          max_bet_amount?: number | null;
          total_pool?: number;
          target_user_id?: string | null;
          creator_option?: string | null;
          target_option?: string | null;
          betting_deadline?: string;
          result_deadline?: string;
          status?: 'open' | 'betting_closed' | 'judging' | 'confirming' | 'settled' | 'cancelled' | 'expired';
          requires_judgment?: boolean;
          judgment_start_time?: string | null;
          current_judgment_round?: number;
          winning_option?: string | null;
          result_confirmed_by_creator?: boolean;
          result_confirmed_by_participants?: boolean;
          result_confirmation_deadline?: string | null;
          platform_fee_rate?: number;
          referral_reward_rate?: number;
          tags?: string[] | null;
          is_featured?: boolean;
          view_count?: number;
          participant_count?: number;
          created_at?: string;
          updated_at?: string;
        };
      };
      bet_participants: {
        Row: {
          id: string;
          bet_id: string;
          user_id: string;
          selected_option: string;
          bet_amount: number;
          status: 'active' | 'withdrawn' | 'settled' | 'refunded';
          payout_amount: number;
          is_winner: boolean | null;
          referrer_id: string | null;
          referral_reward: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          bet_id: string;
          user_id: string;
          selected_option: string;
          bet_amount: number;
          status?: 'active' | 'withdrawn' | 'settled' | 'refunded';
          payout_amount?: number;
          is_winner?: boolean | null;
          referrer_id?: string | null;
          referral_reward?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          bet_id?: string;
          user_id?: string;
          selected_option?: string;
          bet_amount?: number;
          status?: 'active' | 'withdrawn' | 'settled' | 'refunded';
          payout_amount?: number;
          is_winner?: boolean | null;
          referrer_id?: string | null;
          referral_reward?: number;
          created_at?: string;
          updated_at?: string;
        };
      };
      bet_judgments: {
        Row: {
          id: string;
          bet_id: string;
          judge_id: string;
          judgment_round: number;
          selected_option: string;
          confidence_level: number | null;
          reasoning: string | null;
          judge_certification_level: number;
          judge_reputation_score: number;
          reward_amount: number;
          is_correct_judgment: boolean | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          bet_id: string;
          judge_id: string;
          judgment_round: number;
          selected_option: string;
          confidence_level?: number | null;
          reasoning?: string | null;
          judge_certification_level: number;
          judge_reputation_score: number;
          reward_amount?: number;
          is_correct_judgment?: boolean | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          bet_id?: string;
          judge_id?: string;
          judgment_round?: number;
          selected_option?: string;
          confidence_level?: number | null;
          reasoning?: string | null;
          judge_certification_level?: number;
          judge_reputation_score?: number;
          reward_amount?: number;
          is_correct_judgment?: boolean | null;
          created_at?: string;
        };
      };
      user_reputation: {
        Row: {
          user_id: string;
          certification_level: number;
          haox_balance: number;
          last_balance_check: string;
          reputation_score: number;
          total_judgments: number;
          correct_judgments: number;
          accuracy_rate: number;
          daily_judgment_count: number;
          last_judgment_date: string | null;
          current_streak: number;
          best_streak: number;
          fee_discount_rate: number;
          daily_judgment_limit: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          user_id: string;
          certification_level?: number;
          haox_balance?: number;
          last_balance_check?: string;
          reputation_score?: number;
          total_judgments?: number;
          correct_judgments?: number;
          accuracy_rate?: number;
          daily_judgment_count?: number;
          last_judgment_date?: string | null;
          current_streak?: number;
          best_streak?: number;
          fee_discount_rate?: number;
          daily_judgment_limit?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          user_id?: string;
          certification_level?: number;
          haox_balance?: number;
          last_balance_check?: string;
          reputation_score?: number;
          total_judgments?: number;
          correct_judgments?: number;
          accuracy_rate?: number;
          daily_judgment_count?: number;
          last_judgment_date?: string | null;
          current_streak?: number;
          best_streak?: number;
          fee_discount_rate?: number;
          daily_judgment_limit?: number;
          created_at?: string;
          updated_at?: string;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
  };
}

// Typed Supabase client
export type SupabaseClient = typeof supabase;
