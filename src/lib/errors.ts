/**
 * 统一的错误处理系统
 * 提供清晰的错误分类和处理机制
 */

import { NextResponse } from 'next/server';
import { log } from './logger';

export enum ErrorCode {
  // 通用错误
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
  
  // 验证错误
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  INVALID_INPUT = 'INVALID_INPUT',
  MISSING_REQUIRED_FIELD = 'MISSING_REQUIRED_FIELD',
  
  // 认证和授权错误
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  INVALID_TOKEN = 'INVALID_TOKEN',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  
  // 业务逻辑错误
  RESOURCE_NOT_FOUND = 'RESOURCE_NOT_FOUND',
  RESOURCE_ALREADY_EXISTS = 'RESOURCE_ALREADY_EXISTS',
  OPERATION_NOT_ALLOWED = 'OPERATION_NOT_ALLOWED',
  INSUFFICIENT_BALANCE = 'INSUFFICIENT_BALANCE',
  
  // 外部服务错误
  DATABASE_ERROR = 'DATABASE_ERROR',
  BLOCKCHAIN_ERROR = 'BLOCKCHAIN_ERROR',
  TELEGRAM_API_ERROR = 'TELEGRAM_API_ERROR',
  THIRD_PARTY_SERVICE_ERROR = 'THIRD_PARTY_SERVICE_ERROR',
  
  // 网络和连接错误
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  CONNECTION_ERROR = 'CONNECTION_ERROR',
  
  // 限流和配额错误
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  QUOTA_EXCEEDED = 'QUOTA_EXCEEDED',
}

export interface ErrorDetails {
  code: ErrorCode;
  message: string;
  statusCode: number;
  details?: Record<string, any>;
  cause?: Error;
  timestamp?: string;
  requestId?: string;
  userId?: string;
}

/**
 * 应用错误基类
 */
export class AppError extends Error {
  public readonly code: ErrorCode;
  public readonly statusCode: number;
  public readonly details?: Record<string, any>;
  public readonly cause?: Error;
  public readonly timestamp: string;
  public readonly requestId?: string;
  public readonly userId?: string;

  constructor(
    code: ErrorCode,
    message: string,
    statusCode: number = 500,
    details?: Record<string, any>,
    cause?: Error
  ) {
    super(message);
    this.name = 'AppError';
    this.code = code;
    this.statusCode = statusCode;
    this.details = details;
    this.cause = cause;
    this.timestamp = new Date().toISOString();
    
    // 从请求上下文获取 requestId 和 userId（如果可用）
    this.requestId = this.getRequestId();
    this.userId = this.getUserId();

    // 确保堆栈跟踪正确
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, AppError);
    }
  }

  private getRequestId(): string | undefined {
    // 这里可以从请求上下文中获取 requestId
    return undefined;
  }

  private getUserId(): string | undefined {
    // 这里可以从请求上下文中获取 userId
    return undefined;
  }

  /**
   * 转换为 JSON 格式
   */
  toJSON(): ErrorDetails {
    return {
      code: this.code,
      message: this.message,
      statusCode: this.statusCode,
      details: this.details,
      timestamp: this.timestamp,
      requestId: this.requestId,
      userId: this.userId,
    };
  }

  /**
   * 检查是否为特定错误类型
   */
  static isAppError(error: any): error is AppError {
    return error instanceof AppError;
  }
}

/**
 * 验证错误
 */
export class ValidationError extends AppError {
  constructor(message: string, details?: Record<string, any>) {
    super(ErrorCode.VALIDATION_ERROR, message, 400, details);
    this.name = 'ValidationError';
  }
}

/**
 * 认证错误
 */
export class AuthenticationError extends AppError {
  constructor(message: string = '认证失败', details?: Record<string, any>) {
    super(ErrorCode.UNAUTHORIZED, message, 401, details);
    this.name = 'AuthenticationError';
  }
}

/**
 * 授权错误
 */
export class AuthorizationError extends AppError {
  constructor(message: string = '权限不足', details?: Record<string, any>) {
    super(ErrorCode.FORBIDDEN, message, 403, details);
    this.name = 'AuthorizationError';
  }
}

/**
 * 资源未找到错误
 */
export class NotFoundError extends AppError {
  constructor(resource: string, details?: Record<string, any>) {
    super(ErrorCode.RESOURCE_NOT_FOUND, `${resource} 未找到`, 404, details);
    this.name = 'NotFoundError';
  }
}

/**
 * 业务逻辑错误
 */
export class BusinessLogicError extends AppError {
  constructor(message: string, details?: Record<string, any>) {
    super(ErrorCode.OPERATION_NOT_ALLOWED, message, 400, details);
    this.name = 'BusinessLogicError';
  }
}

/**
 * 外部服务错误
 */
export class ExternalServiceError extends AppError {
  constructor(service: string, message: string, details?: Record<string, any>) {
    super(ErrorCode.THIRD_PARTY_SERVICE_ERROR, `${service}: ${message}`, 502, details);
    this.name = 'ExternalServiceError';
  }
}

/**
 * 全局错误处理中间件
 */
export function withErrorHandler<T extends any[], R>(
  fn: (...args: T) => Promise<Response>
) {
  return async (...args: T): Promise<Response> => {
    try {
      return await fn(...args);
    } catch (error) {
      return handleError(error);
    }
  };
}

/**
 * 处理错误并返回适当的响应
 */
export function handleError(error: unknown): Response {
  let appError: AppError;

  if (AppError.isAppError(error)) {
    appError = error;
  } else if (error instanceof Error) {
    // 将普通错误转换为 AppError
    appError = new AppError(
      ErrorCode.INTERNAL_SERVER_ERROR,
      error.message || '内部服务器错误',
      500,
      undefined,
      error
    );
  } else {
    // 处理非 Error 类型的错误
    appError = new AppError(
      ErrorCode.UNKNOWN_ERROR,
      '未知错误',
      500,
      { originalError: error }
    );
  }

  // 记录错误日志
  logError(appError);

  // 返回错误响应
  return NextResponse.json(
    {
      success: false,
      error: {
        code: appError.code,
        message: appError.message,
        ...(process.env.NODE_ENV !== 'production' && {
          details: appError.details,
          stack: appError.stack,
        }),
      },
      timestamp: appError.timestamp,
      requestId: appError.requestId,
    },
    { status: appError.statusCode }
  );
}

/**
 * 记录错误日志
 */
function logError(error: AppError): void {
  const logData = {
    code: error.code,
    statusCode: error.statusCode,
    details: error.details,
    requestId: error.requestId,
    userId: error.userId,
    stack: error.stack,
  };

  if (error.statusCode >= 500) {
    log.error(error.message, error.cause, logData);
  } else if (error.statusCode >= 400) {
    log.warn(error.message, logData);
  } else {
    log.info(error.message, logData);
  }
}

/**
 * 创建常用错误的便捷函数
 */
export const createError = {
  validation: (message: string, details?: Record<string, any>) =>
    new ValidationError(message, details),
  
  unauthorized: (message?: string, details?: Record<string, any>) =>
    new AuthenticationError(message, details),
  
  forbidden: (message?: string, details?: Record<string, any>) =>
    new AuthorizationError(message, details),
  
  notFound: (resource: string, details?: Record<string, any>) =>
    new NotFoundError(resource, details),
  
  business: (message: string, details?: Record<string, any>) =>
    new BusinessLogicError(message, details),
  
  external: (service: string, message: string, details?: Record<string, any>) =>
    new ExternalServiceError(service, message, details),
  
  internal: (message: string, details?: Record<string, any>, cause?: Error) =>
    new AppError(ErrorCode.INTERNAL_SERVER_ERROR, message, 500, details, cause),
};

/**
 * 错误边界组件的错误处理
 */
export function handleComponentError(error: Error, errorInfo: any): void {
  const appError = new AppError(
    ErrorCode.INTERNAL_SERVER_ERROR,
    `组件错误: ${error.message}`,
    500,
    { errorInfo },
    error
  );

  logError(appError);
}
