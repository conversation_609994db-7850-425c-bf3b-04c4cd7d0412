/**
 * 组件属性验证工具
 * 在开发环境中验证组件属性的完整性
 */

import React from 'react';

interface ValidationResult {
  componentName: string;
  missingProps: string[];
  unusedProps: string[];
  isValid: boolean;
}

/**
 * 组件属性验证装饰器
 * 用于在开发环境中验证组件属性实现的完整性
 */
export function validateComponentProps<T extends Record<string, any>>(
  componentName: string,
  expectedProps: (keyof T)[],
  ignoredProps: string[] = ['children', 'className', 'key', 'ref']
) {
  return function <P extends T>(
    WrappedComponent: React.ComponentType<P>
  ): React.ComponentType<P> {
    
    const ValidatedComponent = React.forwardRef<any, P>((props, ref) => {
      // 只在开发环境中进行验证
      if (process.env.NODE_ENV === 'development') {
        const validation = validateProps(componentName, props, expectedProps, ignoredProps);
        
        if (!validation.isValid) {
          console.group(`🚨 Component Props Validation Failed: ${componentName}`);
          
          if (validation.missingProps.length > 0) {
            console.warn('❌ Missing props (declared but not used):', validation.missingProps);
          }
          
          if (validation.unusedProps.length > 0) {
            console.warn('⚠️ Unused props (used but not declared):', validation.unusedProps);
          }
          
          console.groupEnd();
        }
      }
      
      return React.createElement(WrappedComponent, { ...props, ref });
    });
    
    ValidatedComponent.displayName = `Validated(${componentName})`;
    return ValidatedComponent as React.ComponentType<P>;
  };
}

/**
 * 验证组件属性
 */
function validateProps<T>(
  componentName: string,
  props: T,
  expectedProps: (keyof T)[],
  ignoredProps: string[]
): ValidationResult {
  const usedProps = Object.keys(props || {});
  const expectedPropsStr = expectedProps.map(String);
  
  // 过滤掉忽略的属性
  const filteredUsedProps = usedProps.filter(prop => !ignoredProps.includes(prop));
  const filteredExpectedProps = expectedPropsStr.filter(prop => !ignoredProps.includes(prop));
  
  // 找出缺失的属性（声明了但未使用）
  const missingProps = filteredExpectedProps.filter(prop => !filteredUsedProps.includes(prop));
  
  // 找出未声明的属性（使用了但未声明）
  const unusedProps = filteredUsedProps.filter(prop => !filteredExpectedProps.includes(prop));
  
  return {
    componentName,
    missingProps,
    unusedProps,
    isValid: missingProps.length === 0 && unusedProps.length === 0
  };
}

/**
 * 创建类型安全的属性验证器
 */
export function createPropsValidator<T>() {
  return {
    validate: (componentName: string, expectedProps: (keyof T)[]) => 
      validateComponentProps<T>(componentName, expectedProps),
    
    // 运行时属性检查
    check: (props: T, expectedProps: (keyof T)[], componentName: string) => 
      validateProps(componentName, props, expectedProps, ['children', 'className'])
  };
}

/**
 * 常用的属性验证预设
 */
export const CommonValidators = {
  // Button组件验证器
  Button: createPropsValidator<{
    variant?: string;
    size?: string;
    disabled?: boolean;
    loading?: boolean;
    onClick?: () => void;
    href?: string;
    target?: string;
    type?: string;
    id?: string;
    'aria-label'?: string;
  }>(),
  
  // Input组件验证器
  Input: createPropsValidator<{
    label?: string;
    placeholder?: string;
    type?: string;
    value: string;
    onChange: (value: string) => void;
    error?: string;
    disabled?: boolean;
    required?: boolean;
    icon?: React.ReactNode;
    rightIcon?: React.ReactNode;
    helperText?: string;
    maxLength?: number;
    minLength?: number;
    id?: string;
    name?: string;
    autoComplete?: string;
  }>(),
  
  // Card组件验证器
  Card: createPropsValidator<{
    title?: string;
    children: React.ReactNode;
    padding?: string;
  }>()
};

/**
 * 开发环境组件属性检查Hook
 */
export function usePropsValidation<T>(
  componentName: string,
  props: T,
  expectedProps: (keyof T)[]
) {
  React.useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      const validation = validateProps(componentName, props, expectedProps, ['children', 'className']);
      
      if (!validation.isValid) {
        console.warn(`Component ${componentName} has prop validation issues:`, validation);
      }
    }
  }, [componentName, props, expectedProps]);
}

/**
 * 属性完整性测试工具
 */
export class PropsIntegrityTester {
  private static instance: PropsIntegrityTester;
  private validationResults: ValidationResult[] = [];
  
  static getInstance(): PropsIntegrityTester {
    if (!PropsIntegrityTester.instance) {
      PropsIntegrityTester.instance = new PropsIntegrityTester();
    }
    return PropsIntegrityTester.instance;
  }
  
  addValidation(result: ValidationResult) {
    this.validationResults.push(result);
  }
  
  getReport(): string {
    const failedValidations = this.validationResults.filter(r => !r.isValid);
    
    if (failedValidations.length === 0) {
      return '✅ All components have consistent prop definitions!';
    }
    
    let report = `🚨 Found ${failedValidations.length} components with prop issues:\n\n`;
    
    failedValidations.forEach(validation => {
      report += `## ${validation.componentName}\n`;
      if (validation.missingProps.length > 0) {
        report += `❌ Missing: ${validation.missingProps.join(', ')}\n`;
      }
      if (validation.unusedProps.length > 0) {
        report += `⚠️ Unused: ${validation.unusedProps.join(', ')}\n`;
      }
      report += '\n';
    });
    
    return report;
  }
  
  clear() {
    this.validationResults = [];
  }
}
