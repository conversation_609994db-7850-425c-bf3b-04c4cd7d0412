/**
 * 全局反馈系统Context
 * 提供统一的加载状态、成功/错误提示、操作反馈
 */

'use client';

import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

// 反馈类型定义
export type FeedbackType = 'success' | 'error' | 'warning' | 'info';

export interface FeedbackMessage {
  id: string;
  type: FeedbackType;
  title: string;
  message?: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}

export interface LoadingState {
  id: string;
  message: string;
  progress?: number;
}

interface FeedbackContextType {
  // 消息管理
  messages: FeedbackMessage[];
  showMessage: (message: Omit<FeedbackMessage, 'id'>) => string;
  hideMessage: (id: string) => void;
  clearMessages: () => void;
  
  // 加载状态管理
  loadingStates: LoadingState[];
  showLoading: (message: string, progress?: number) => string;
  updateLoading: (id: string, message?: string, progress?: number) => void;
  hideLoading: (id: string) => void;
  clearLoading: () => void;
  
  // 全局加载状态
  isGlobalLoading: boolean;
  setGlobalLoading: (loading: boolean, message?: string) => void;
}

const FeedbackContext = createContext<FeedbackContextType | undefined>(undefined);

export function FeedbackProvider({ children }: { children: ReactNode }) {
  const [messages, setMessages] = useState<FeedbackMessage[]>([]);
  const [loadingStates, setLoadingStates] = useState<LoadingState[]>([]);
  const [isGlobalLoading, setIsGlobalLoading] = useState(false);
  const [globalLoadingMessage, setGlobalLoadingMessage] = useState('');

  // 生成唯一ID
  const generateId = useCallback(() => {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }, []);

  // 显示消息
  const showMessage = useCallback((message: Omit<FeedbackMessage, 'id'>) => {
    const id = generateId();
    const newMessage: FeedbackMessage = {
      id,
      duration: 5000, // 默认5秒
      ...message
    };

    setMessages(prev => [...prev, newMessage]);

    // 自动隐藏
    if (newMessage.duration && newMessage.duration > 0) {
      setTimeout(() => {
        hideMessage(id);
      }, newMessage.duration);
    }

    return id;
  }, [generateId]);

  // 隐藏消息
  const hideMessage = useCallback((id: string) => {
    setMessages(prev => prev.filter(msg => msg.id !== id));
  }, []);

  // 清除所有消息
  const clearMessages = useCallback(() => {
    setMessages([]);
  }, []);

  // 显示加载状态
  const showLoading = useCallback((message: string, progress?: number) => {
    const id = generateId();
    const newLoading: LoadingState = { id, message, progress };
    
    setLoadingStates(prev => [...prev, newLoading]);
    return id;
  }, [generateId]);

  // 更新加载状态
  const updateLoading = useCallback((id: string, message?: string, progress?: number) => {
    setLoadingStates(prev => prev.map(loading => 
      loading.id === id 
        ? { ...loading, ...(message && { message }), ...(progress !== undefined && { progress }) }
        : loading
    ));
  }, []);

  // 隐藏加载状态
  const hideLoading = useCallback((id: string) => {
    setLoadingStates(prev => prev.filter(loading => loading.id !== id));
  }, []);

  // 清除所有加载状态
  const clearLoading = useCallback(() => {
    setLoadingStates([]);
  }, []);

  // 设置全局加载状态
  const setGlobalLoading = useCallback((loading: boolean, message?: string) => {
    setIsGlobalLoading(loading);
    if (message) {
      setGlobalLoadingMessage(message);
    }
  }, []);

  const value: FeedbackContextType = {
    messages,
    showMessage,
    hideMessage,
    clearMessages,
    loadingStates,
    showLoading,
    updateLoading,
    hideLoading,
    clearLoading,
    isGlobalLoading,
    setGlobalLoading
  };

  return (
    <FeedbackContext.Provider value={value}>
      {children}
      
      {/* 消息通知组件 */}
      <div className="fixed top-4 right-4 z-50 space-y-2">
        <AnimatePresence>
          {messages.map(message => (
            <MessageToast
              key={message.id}
              message={message}
              onClose={() => hideMessage(message.id)}
            />
          ))}
        </AnimatePresence>
      </div>

      {/* 加载状态组件 */}
      <div className="fixed bottom-4 right-4 z-50 space-y-2">
        <AnimatePresence>
          {loadingStates.map(loading => (
            <LoadingToast
              key={loading.id}
              loading={loading}
              onClose={() => hideLoading(loading.id)}
            />
          ))}
        </AnimatePresence>
      </div>

      {/* 全局加载遮罩 */}
      <AnimatePresence>
        {isGlobalLoading && (
          <motion.div
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <motion.div
              className="bg-white rounded-lg p-6 shadow-lg max-w-sm mx-4"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
            >
              <div className="flex items-center space-x-3">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-system-blue"></div>
                <span className="text-label font-medium">{globalLoadingMessage || '加载中...'}</span>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </FeedbackContext.Provider>
  );
}

// 消息Toast组件
function MessageToast({ 
  message, 
  onClose 
}: { 
  message: FeedbackMessage; 
  onClose: () => void; 
}) {
  const getTypeStyles = (type: FeedbackType) => {
    switch (type) {
      case 'success':
        return 'bg-green-50 border-green-200 text-green-800';
      case 'error':
        return 'bg-red-50 border-red-200 text-red-800';
      case 'warning':
        return 'bg-yellow-50 border-yellow-200 text-yellow-800';
      case 'info':
        return 'bg-blue-50 border-blue-200 text-blue-800';
      default:
        return 'bg-gray-50 border-gray-200 text-gray-800';
    }
  };

  const getIcon = (type: FeedbackType) => {
    switch (type) {
      case 'success':
        return '✅';
      case 'error':
        return '❌';
      case 'warning':
        return '⚠️';
      case 'info':
        return 'ℹ️';
      default:
        return '📝';
    }
  };

  return (
    <motion.div
      className={`max-w-sm w-full border rounded-lg p-4 shadow-lg ${getTypeStyles(message.type)}`}
      initial={{ opacity: 0, x: 300, scale: 0.3 }}
      animate={{ opacity: 1, x: 0, scale: 1 }}
      exit={{ opacity: 0, x: 300, scale: 0.5, transition: { duration: 0.2 } }}
      whileHover={{ scale: 1.02 }}
    >
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <span className="text-lg">{getIcon(message.type)}</span>
        </div>
        <div className="ml-3 w-0 flex-1">
          <p className="text-sm font-medium">{message.title}</p>
          {message.message && (
            <p className="mt-1 text-sm opacity-90">{message.message}</p>
          )}
          {message.action && (
            <div className="mt-2">
              <button
                onClick={message.action.onClick}
                className="text-sm underline hover:no-underline"
              >
                {message.action.label}
              </button>
            </div>
          )}
        </div>
        <div className="ml-4 flex-shrink-0 flex">
          <button
            onClick={onClose}
            className="inline-flex text-gray-400 hover:text-gray-600 focus:outline-none"
          >
            <span className="sr-only">关闭</span>
            <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
      </div>
    </motion.div>
  );
}

// 加载Toast组件
function LoadingToast({ 
  loading, 
  onClose 
}: { 
  loading: LoadingState; 
  onClose: () => void; 
}) {
  return (
    <motion.div
      className="max-w-sm w-full bg-white border border-gray-200 rounded-lg p-4 shadow-lg"
      initial={{ opacity: 0, y: 50, scale: 0.3 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      exit={{ opacity: 0, y: 50, scale: 0.5, transition: { duration: 0.2 } }}
    >
      <div className="flex items-center">
        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-system-blue"></div>
        <div className="ml-3 flex-1">
          <p className="text-sm font-medium text-label">{loading.message}</p>
          {loading.progress !== undefined && (
            <div className="mt-2">
              <div className="bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-system-blue h-2 rounded-full transition-all duration-300"
                  style={{ width: `${loading.progress}%` }}
                />
              </div>
              <p className="text-xs text-secondary-label mt-1">{loading.progress}%</p>
            </div>
          )}
        </div>
        <button
          onClick={onClose}
          className="ml-4 text-gray-400 hover:text-gray-600"
        >
          <svg className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        </button>
      </div>
    </motion.div>
  );
}

// Hook for using feedback context
export function useFeedback() {
  const context = useContext(FeedbackContext);
  if (context === undefined) {
    throw new Error('useFeedback must be used within a FeedbackProvider');
  }
  return context;
}
