/**
 * 代码分割优化配置
 * 动态导入和懒加载组件配置
 */

import React, { lazy, ComponentType } from 'react';
import dynamic from 'next/dynamic';

// 懒加载组件的通用类型
type LazyComponent<T = {}> = ComponentType<T>;

// 加载状态组件
const LoadingSpinner = () => (
  <div className="flex items-center justify-center p-8">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
  </div>
);

const LoadingCard = () => (
  <div className="animate-pulse">
    <div className="bg-gray-200 rounded-lg h-32 w-full"></div>
  </div>
);

const LoadingPage = () => (
  <div className="min-h-screen flex items-center justify-center">
    <div className="text-center">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
      <p className="text-gray-600">加载中...</p>
    </div>
  </div>
);

// 错误边界组件
const ErrorFallback = ({ error, retry }: { error: Error; retry?: () => void }) => (
  <div className="p-8 text-center">
    <div className="text-red-600 mb-4">
      <svg className="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
      </svg>
    </div>
    <h3 className="text-lg font-semibold text-gray-900 mb-2">加载失败</h3>
    <p className="text-gray-600 mb-4">{error.message}</p>
    {retry && (
      <button
        onClick={retry}
        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
      >
        重试
      </button>
    )}
  </div>
);

// 动态导入配置选项
interface DynamicImportOptions {
  loading?: ComponentType;
  error?: ComponentType<{ error: Error; retry?: () => void }>;
  ssr?: boolean;
  suspense?: boolean;
}

// 创建动态组件的工厂函数
export function createDynamicComponent<T = {}>(
  importFn: () => Promise<{ default: ComponentType<T> }>,
  options: DynamicImportOptions = {}
): ComponentType<T> {
  const {
    loading = LoadingSpinner,
    error = ErrorFallback,
    ssr = false,
    suspense = false
  } = options;

  return dynamic(importFn, {
    loading,
    ssr,
    suspense,
  });
}

// 页面级组件懒加载
export const LazyPages = {
  // 仪表板页面
  Dashboard: createDynamicComponent(
    () => import('@/app/dashboard/page'),
    { loading: LoadingPage, ssr: false }
  ),

  // 钱包页面
  Wallet: createDynamicComponent(
    () => import('@/app/wallet/page'),
    { loading: LoadingPage, ssr: false }
  ),

  // 任务页面
  Tasks: createDynamicComponent(
    () => import('@/app/tasks/page'),
    { loading: LoadingPage, ssr: false }
  ),

  // 预售页面
  Presale: createDynamicComponent(
    () => import('@/app/presale/page'),
    { loading: LoadingPage, ssr: false }
  ),

  // 商户页面
  Merchant: createDynamicComponent(
    () => import('@/app/merchant/page'),
    { loading: LoadingPage, ssr: false }
  ),

  // 个人资料页面
  Profile: createDynamicComponent(
    () => import('@/app/profile/page'),
    { loading: LoadingPage, ssr: false }
  ),
};

// 组件级懒加载
export const LazyComponents = {
  // 图表组件
  Chart: createDynamicComponent(
    () => import('@/components/ui/chart'),
    { loading: LoadingCard, ssr: false }
  ),

  // 数据表格
  DataTable: createDynamicComponent(
    () => import('@/components/ui/data-table'),
    { loading: LoadingCard, ssr: false }
  ),

  // 钱包连接器
  WalletConnector: createDynamicComponent(
    () => import('@/components/wallet/WalletConnector'),
    { loading: LoadingSpinner, ssr: false }
  ),

  // 交易历史
  TransactionHistory: createDynamicComponent(
    () => import('@/components/wallet/TransactionHistory'),
    { loading: LoadingCard, ssr: false }
  ),

  // 任务列表
  TaskList: createDynamicComponent(
    () => import('@/components/tasks/TaskList'),
    { loading: LoadingCard, ssr: false }
  ),

  // 价格图表
  PriceChart: createDynamicComponent(
    () => import('@/components/charts/PriceChart'),
    { loading: LoadingCard, ssr: false }
  ),

  // 邀请统计
  InvitationStats: createDynamicComponent(
    () => import('@/components/invitation/InvitationStats'),
    { loading: LoadingCard, ssr: false }
  ),

  // 性能监控
  PerformanceMonitor: createDynamicComponent(
    () => import('@/components/PerformanceMonitor'),
    { loading: () => null, ssr: false }
  ),

  // 错误边界
  ErrorBoundary: createDynamicComponent(
    () => import('@/components/ErrorBoundary'),
    { loading: () => null, ssr: true }
  ),
};

// 第三方库懒加载
export const LazyLibraries = {
  // 日期选择器
  DatePicker: createDynamicComponent(
    () => import('react-datepicker').then(mod => ({ default: mod.default })),
    { loading: LoadingSpinner, ssr: false }
  ),

  // 代码编辑器
  CodeEditor: createDynamicComponent(
    () => import('@monaco-editor/react').then(mod => ({ default: mod.default })),
    { loading: LoadingSpinner, ssr: false }
  ),

  // 二维码生成器
  QRCode: createDynamicComponent(
    () => import('qrcode.react').then(mod => ({ default: mod.default })),
    { loading: LoadingSpinner, ssr: false }
  ),

  // 图片裁剪器
  ImageCropper: createDynamicComponent(
    () => import('react-image-crop').then(mod => ({ default: mod.default })),
    { loading: LoadingSpinner, ssr: false }
  ),
};

// 路由级代码分割
export const RouteComponents = {
  // 认证相关路由
  Auth: {
    Login: createDynamicComponent(
      () => import('@/app/(auth)/login/page'),
      { loading: LoadingPage, ssr: true }
    ),
    Register: createDynamicComponent(
      () => import('@/app/(auth)/register/page'),
      { loading: LoadingPage, ssr: true }
    ),
  },

  // 管理后台路由
  Admin: {
    Dashboard: createDynamicComponent(
      () => import('@/app/admin/dashboard/page'),
      { loading: LoadingPage, ssr: false }
    ),
    Users: createDynamicComponent(
      () => import('@/app/admin/users/page'),
      { loading: LoadingPage, ssr: false }
    ),
    Tasks: createDynamicComponent(
      () => import('@/app/admin/tasks/page'),
      { loading: LoadingPage, ssr: false }
    ),
  },

  // API 文档路由
  Docs: {
    API: createDynamicComponent(
      () => import('@/app/docs/api/page'),
      { loading: LoadingPage, ssr: true }
    ),
    Guide: createDynamicComponent(
      () => import('@/app/docs/guide/page'),
      { loading: LoadingPage, ssr: true }
    ),
  },
};

// 预加载函数
export const preloadComponent = (componentName: keyof typeof LazyComponents) => {
  if (typeof window !== 'undefined') {
    // 在浏览器环境中预加载组件
    const component = LazyComponents[componentName];
    if (component) {
      // 触发动态导入但不渲染
      component.preload?.();
    }
  }
};

// 预加载页面
export const preloadPage = (pageName: keyof typeof LazyPages) => {
  if (typeof window !== 'undefined') {
    const page = LazyPages[pageName];
    if (page) {
      page.preload?.();
    }
  }
};

// 批量预加载
export const preloadCriticalComponents = () => {
  if (typeof window !== 'undefined') {
    // 预加载关键组件
    const criticalComponents: (keyof typeof LazyComponents)[] = [
      'WalletConnector',
      'PerformanceMonitor',
      'ErrorBoundary'
    ];

    criticalComponents.forEach(componentName => {
      preloadComponent(componentName);
    });
  }
};

// 基于路由的预加载策略
export const preloadByRoute = (pathname: string) => {
  if (typeof window !== 'undefined') {
    switch (pathname) {
      case '/dashboard':
        preloadComponent('Chart');
        preloadComponent('DataTable');
        break;
      case '/wallet':
        preloadComponent('WalletConnector');
        preloadComponent('TransactionHistory');
        break;
      case '/tasks':
        preloadComponent('TaskList');
        break;
      case '/presale':
        preloadComponent('PriceChart');
        break;
      default:
        break;
    }
  }
};

// 智能预加载：基于用户行为
export const smartPreload = () => {
  if (typeof window !== 'undefined') {
    // 监听鼠标悬停事件，预加载可能访问的页面
    document.addEventListener('mouseover', (event) => {
      const target = event.target as HTMLElement;
      const link = target.closest('a[href]') as HTMLAnchorElement;
      
      if (link && link.href.includes(window.location.origin)) {
        const pathname = new URL(link.href).pathname;
        preloadByRoute(pathname);
      }
    });

    // 监听滚动事件，预加载即将进入视口的组件
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const element = entry.target as HTMLElement;
            const preloadAttr = element.getAttribute('data-preload');
            if (preloadAttr && preloadAttr in LazyComponents) {
              preloadComponent(preloadAttr as keyof typeof LazyComponents);
            }
          }
        });
      },
      { rootMargin: '100px' }
    );

    // 观察带有 data-preload 属性的元素
    document.querySelectorAll('[data-preload]').forEach((element) => {
      observer.observe(element);
    });
  }
};

// 初始化代码分割优化
export const initializeCodeSplitting = () => {
  if (typeof window !== 'undefined') {
    // 预加载关键组件
    preloadCriticalComponents();
    
    // 启用智能预加载
    smartPreload();
    
    // 在页面空闲时预加载更多组件
    if ('requestIdleCallback' in window) {
      requestIdleCallback(() => {
        // 预加载非关键组件
        preloadComponent('Chart');
        preloadComponent('DataTable');
      });
    }
  }
};
