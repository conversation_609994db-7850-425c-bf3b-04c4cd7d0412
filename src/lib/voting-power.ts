/**
 * 投票权重计算逻辑
 * 基于用户持币量、持币时间等因素计算投票权重
 */

import { log } from './logger';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export interface VotingPowerFactors {
  tokenBalance: number;
  stakingBalance: number;
  holdingDuration: number; // 持币时间（天）
  participationHistory: number; // 参与历史权重
  delegatedPower: number; // 委托权重
}

export interface VotingPowerResult {
  totalPower: number;
  breakdown: {
    basePower: number;
    stakingBonus: number;
    holdingBonus: number;
    participationBonus: number;
    delegationPower: number;
  };
  factors: VotingPowerFactors;
}

/**
 * 计算用户的投票权重
 */
export async function calculateVotingPower(
  userId: string,
  snapshotBlockHeight?: number
): Promise<VotingPowerResult> {
  try {
    log.info('Calculating voting power', { userId, snapshotBlockHeight });

    // 1. 获取用户基本信息
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (userError || !user) {
      throw new Error(`User not found: ${userId}`);
    }

    // 2. 获取代币余额（如果有快照则使用快照数据）
    const tokenBalance = await getTokenBalance(userId, snapshotBlockHeight);
    
    // 3. 获取质押余额
    const stakingBalance = await getStakingBalance(userId, snapshotBlockHeight);
    
    // 4. 计算持币时间
    const holdingDuration = await getHoldingDuration(userId, snapshotBlockHeight);
    
    // 5. 获取参与历史权重
    const participationHistory = await getParticipationHistory(userId);
    
    // 6. 获取委托权重
    const delegatedPower = await getDelegatedPower(userId, snapshotBlockHeight);

    const factors: VotingPowerFactors = {
      tokenBalance,
      stakingBalance,
      holdingDuration,
      participationHistory,
      delegatedPower,
    };

    // 7. 计算各项权重
    const breakdown = calculatePowerBreakdown(factors);
    
    const result: VotingPowerResult = {
      totalPower: breakdown.basePower + breakdown.stakingBonus + breakdown.holdingBonus + 
                  breakdown.participationBonus + breakdown.delegationPower,
      breakdown,
      factors,
    };

    log.info('Voting power calculated', { userId, result });
    return result;

  } catch (error) {
    log.error('Failed to calculate voting power', error, { userId });
    throw error;
  }
}

/**
 * 获取代币余额
 */
async function getTokenBalance(userId: string, blockHeight?: number): Promise<number> {
  if (blockHeight) {
    // 从快照中获取历史余额
    const { data: snapshot } = await supabase
      .from('balance_snapshots')
      .select('haox_balance')
      .eq('user_id', userId)
      .eq('block_height', blockHeight)
      .single();
    
    if (snapshot) {
      return parseFloat(snapshot.haox_balance) || 0;
    }
  }

  // 获取当前余额（简化实现，实际应该从区块链查询）
  const { data: user } = await supabase
    .from('users')
    .select('haox_balance')
    .eq('id', userId)
    .single();

  return parseFloat(user?.haox_balance || '0');
}

/**
 * 获取质押余额
 */
async function getStakingBalance(userId: string, blockHeight?: number): Promise<number> {
  const { data: stakings } = await supabase
    .from('stakings')
    .select('amount, status')
    .eq('user_id', userId)
    .eq('status', 'active');

  if (!stakings) return 0;

  return stakings.reduce((total, staking) => {
    return total + parseFloat(staking.amount);
  }, 0);
}

/**
 * 计算持币时间（天）
 */
async function getHoldingDuration(userId: string, blockHeight?: number): Promise<number> {
  // 获取用户首次获得代币的时间
  const { data: firstTransaction } = await supabase
    .from('transactions')
    .select('created_at')
    .eq('to_address', userId)
    .eq('token_type', 'HAOX')
    .order('created_at', { ascending: true })
    .limit(1)
    .single();

  if (!firstTransaction) return 0;

  const firstDate = new Date(firstTransaction.created_at);
  const currentDate = new Date();
  const diffTime = Math.abs(currentDate.getTime() - firstDate.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  return diffDays;
}

/**
 * 获取参与历史权重
 */
async function getParticipationHistory(userId: string): Promise<number> {
  // 获取用户的投票历史
  const { data: votes, count: voteCount } = await supabase
    .from('votes')
    .select('*', { count: 'exact' })
    .eq('voter_id', userId);

  // 获取用户的提案历史
  const { data: proposals, count: proposalCount } = await supabase
    .from('proposals')
    .select('*', { count: 'exact' })
    .eq('proposer_id', userId);

  // 获取用户的任务完成历史
  const { data: tasks, count: taskCount } = await supabase
    .from('task_completions')
    .select('*', { count: 'exact' })
    .eq('user_id', userId);

  // 计算参与度分数
  const participationScore = (voteCount || 0) * 2 + (proposalCount || 0) * 5 + (taskCount || 0) * 1;
  
  return Math.min(participationScore, 1000); // 最大1000分
}

/**
 * 获取委托权重
 */
async function getDelegatedPower(userId: string, blockHeight?: number): Promise<number> {
  // 获取委托给该用户的权重
  const { data: delegations } = await supabase
    .from('vote_delegations')
    .select('delegated_power')
    .eq('delegate_id', userId)
    .eq('status', 'active');

  if (!delegations) return 0;

  return delegations.reduce((total, delegation) => {
    return total + parseFloat(delegation.delegated_power);
  }, 0);
}

/**
 * 计算权重分解
 */
function calculatePowerBreakdown(factors: VotingPowerFactors) {
  // 基础权重：1 HAOX = 1 投票权
  const basePower = factors.tokenBalance;

  // 质押奖励：质押代币获得 1.5x 权重
  const stakingBonus = factors.stakingBalance * 0.5;

  // 持币时间奖励：每30天增加5%权重，最大50%
  const holdingMultiplier = Math.min(factors.holdingDuration / 30 * 0.05, 0.5);
  const holdingBonus = basePower * holdingMultiplier;

  // 参与历史奖励：基于参与度给予额外权重
  const participationBonus = factors.participationHistory * 0.1;

  // 委托权重
  const delegationPower = factors.delegatedPower;

  return {
    basePower,
    stakingBonus,
    holdingBonus,
    participationBonus,
    delegationPower,
  };
}

/**
 * 创建投票权重快照
 */
export async function createVotingPowerSnapshot(
  proposalId: string,
  blockHeight: number
): Promise<void> {
  try {
    log.info('Creating voting power snapshot', { proposalId, blockHeight });

    // 获取所有有代币余额的用户
    const { data: users } = await supabase
      .from('users')
      .select('id')
      .not('haox_balance', 'is', null)
      .gt('haox_balance', 0);

    if (!users) {
      log.warn('No users found for snapshot');
      return;
    }

    const snapshots = [];

    // 为每个用户计算投票权重
    for (const user of users) {
      try {
        const votingPower = await calculateVotingPower(user.id, blockHeight);
        
        snapshots.push({
          proposal_id: proposalId,
          user_id: user.id,
          block_height: blockHeight,
          token_balance: votingPower.factors.tokenBalance,
          staking_balance: votingPower.factors.stakingBalance,
          holding_duration: votingPower.factors.holdingDuration,
          participation_score: votingPower.factors.participationHistory,
          delegated_power: votingPower.factors.delegatedPower,
          total_voting_power: votingPower.totalPower,
          created_at: new Date().toISOString(),
        });
      } catch (error) {
        log.warn('Failed to calculate voting power for user', { userId: user.id, error });
      }
    }

    // 批量插入快照数据
    if (snapshots.length > 0) {
      const { error } = await supabase
        .from('voting_power_snapshots')
        .insert(snapshots);

      if (error) {
        throw error;
      }

      log.info('Voting power snapshot created', { 
        proposalId, 
        blockHeight, 
        userCount: snapshots.length 
      });
    }

  } catch (error) {
    log.error('Failed to create voting power snapshot', error, { proposalId, blockHeight });
    throw error;
  }
}

/**
 * 获取用户在特定提案的投票权重
 */
export async function getUserVotingPowerForProposal(
  userId: string,
  proposalId: string
): Promise<number> {
  // 首先尝试从快照中获取
  const { data: snapshot } = await supabase
    .from('voting_power_snapshots')
    .select('total_voting_power')
    .eq('user_id', userId)
    .eq('proposal_id', proposalId)
    .single();

  if (snapshot) {
    return snapshot.total_voting_power;
  }

  // 如果没有快照，计算当前权重
  const votingPower = await calculateVotingPower(userId);
  return votingPower.totalPower;
}
