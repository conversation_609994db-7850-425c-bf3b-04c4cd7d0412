/**
 * 地址验证工具函数
 * 防止ENS错误
 */

import { ethers } from 'ethers';

/**
 * 严格验证以太坊地址
 */
export function isValidEthereumAddress(address: string): boolean {
  if (!address || typeof address !== 'string') {
    return false;
  }
  
  // 检查基本格式
  if (address === '' || address === '0x' || address.length !== 42) {
    return false;
  }
  
  // 检查是否以0x开头
  if (!address.startsWith('0x')) {
    return false;
  }
  
  // 使用ethers验证
  try {
    return ethers.isAddress(address);
  } catch {
    return false;
  }
}

/**
 * 安全创建合约实例
 */
export function safeCreateContract(
  address: string, 
  abi: any[], 
  provider: any
): any | null {
  if (!isValidEthereumAddress(address)) {
    console.warn('Invalid contract address:', address);
    return null;
  }
  
  try {
    return new ethers.Contract(address, abi, provider);
  } catch (error) {
    console.error('Failed to create contract:', error);
    return null;
  }
}

/**
 * 获取有效的合约地址
 */
export function getValidContractAddress(
  envAddress: string | undefined, 
  fallbackAddress: string
): string {
  if (isValidEthereumAddress(envAddress || '')) {
    return envAddress!;
  }
  
  if (isValidEthereumAddress(fallbackAddress)) {
    return fallbackAddress;
  }
  
  throw new Error('No valid contract address available');
}
