/**
 * API中间件
 * 提供常用的API中间件功能，如认证、限流等
 */

import { NextRequest } from 'next/server';
import { cookies } from 'next/headers';
import jwt from 'jsonwebtoken';
import { 
  createApiError, 
  createErrorResponse, 
  ApiErrorCode 
} from './api-error-handler';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

export interface JWTPayload {
  userId: number;
  username?: string;
  firstName: string;
  lastName?: string;
  photoUrl?: string;
  iat: number;
  exp: number;
}

export interface AuthenticatedRequest extends NextRequest {
  user: JWTPayload;
}

/**
 * 认证中间件
 * 验证JWT token并提取用户信息
 */
export async function withAuth<T extends any[]>(
  handler: (request: AuthenticatedRequest, ...args: T) => Promise<Response>
) {
  return async (request: NextRequest, ...args: T): Promise<Response> => {
    try {
      // 从cookie获取token
      const cookieStore = cookies();
      const token = cookieStore.get('telegram-auth-token')?.value;

      if (!token) {
        const error = createApiError(
          ApiErrorCode.UNAUTHORIZED,
          '需要身份验证',
          401
        );
        return createErrorResponse(error);
      }

      // 验证JWT token
      const decoded = jwt.verify(token, JWT_SECRET) as JWTPayload;

      // 将用户信息添加到请求对象
      const authenticatedRequest = request as AuthenticatedRequest;
      authenticatedRequest.user = decoded;

      return await handler(authenticatedRequest, ...args);
    } catch (error) {
      if (error instanceof jwt.JsonWebTokenError) {
        const apiError = createApiError(
          ApiErrorCode.INVALID_TOKEN,
          '无效的认证令牌',
          401
        );
        return createErrorResponse(apiError);
      }

      // 重新抛出其他错误，让外层错误处理器处理
      throw error;
    }
  };
}

/**
 * 限流中间件
 * 简单的内存限流实现（生产环境应使用Redis等）
 */
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

export function withRateLimit(
  maxRequests: number = 100,
  windowMs: number = 60000 // 1分钟
) {
  return function<T extends any[]>(
    handler: (...args: T) => Promise<Response>
  ) {
    return async (...args: T): Promise<Response> => {
      const request = args[0] as NextRequest;
      
      // 获取客户端IP
      const ip = request.ip || 
                 request.headers.get('x-forwarded-for') || 
                 request.headers.get('x-real-ip') || 
                 'unknown';

      const now = Date.now();
      const key = `rate_limit:${ip}`;
      
      const current = rateLimitMap.get(key);
      
      if (!current || now > current.resetTime) {
        // 重置计数器
        rateLimitMap.set(key, {
          count: 1,
          resetTime: now + windowMs
        });
      } else {
        // 增加计数
        current.count++;
        
        if (current.count > maxRequests) {
          const error = createApiError(
            ApiErrorCode.RATE_LIMIT_EXCEEDED,
            '请求过于频繁，请稍后再试',
            429
          );
          return createErrorResponse(error);
        }
      }

      return await handler(...args);
    };
  };
}

/**
 * CORS中间件
 */
export function withCors(
  origins: string[] = ['*'],
  methods: string[] = ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS']
) {
  return function<T extends any[]>(
    handler: (...args: T) => Promise<Response>
  ) {
    return async (...args: T): Promise<Response> => {
      const request = args[0] as NextRequest;
      
      // 处理预检请求
      if (request.method === 'OPTIONS') {
        return new Response(null, {
          status: 200,
          headers: {
            'Access-Control-Allow-Origin': origins.includes('*') ? '*' : origins.join(','),
            'Access-Control-Allow-Methods': methods.join(','),
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
            'Access-Control-Max-Age': '86400',
          },
        });
      }

      const response = await handler(...args);
      
      // 添加CORS头
      const newHeaders = new Headers(response.headers);
      newHeaders.set('Access-Control-Allow-Origin', origins.includes('*') ? '*' : origins.join(','));
      newHeaders.set('Access-Control-Allow-Methods', methods.join(','));
      newHeaders.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');

      return new Response(response.body, {
        status: response.status,
        statusText: response.statusText,
        headers: newHeaders,
      });
    };
  };
}

/**
 * 请求日志中间件
 */
export function withLogging<T extends any[]>(
  handler: (...args: T) => Promise<Response>
) {
  return async (...args: T): Promise<Response> => {
    const request = args[0] as NextRequest;
    const startTime = Date.now();
    
    console.log(`[API] ${request.method} ${request.url} - Started`);
    
    try {
      const response = await handler(...args);
      const duration = Date.now() - startTime;
      
      console.log(
        `[API] ${request.method} ${request.url} - ${response.status} (${duration}ms)`
      );
      
      return response;
    } catch (error) {
      const duration = Date.now() - startTime;
      
      console.error(
        `[API] ${request.method} ${request.url} - Error (${duration}ms):`,
        error
      );
      
      throw error;
    }
  };
}

/**
 * 组合多个中间件
 */
export function compose<T extends any[]>(
  ...middlewares: Array<(handler: (...args: T) => Promise<Response>) => (...args: T) => Promise<Response>>
) {
  return function(handler: (...args: T) => Promise<Response>) {
    return middlewares.reduceRight(
      (acc, middleware) => middleware(acc),
      handler
    );
  };
}

/**
 * 清理过期的限流记录
 */
setInterval(() => {
  const now = Date.now();
  for (const [key, value] of rateLimitMap.entries()) {
    if (now > value.resetTime) {
      rateLimitMap.delete(key);
    }
  }
}, 60000); // 每分钟清理一次
