// 错误类型枚举
export enum ErrorType {
  DATABASE_ERROR = 'DATABASE_ERROR',
  BLOCKCHAIN_ERROR = 'BLOCKCHAIN_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
  RATE_LIMIT_ERROR = 'RATE_LIMIT_ERROR',
  EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR',
  BUSINESS_LOGIC_ERROR = 'BUSINESS_LOGIC_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

// 错误严重级别
export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

// 标准化错误接口
export interface StandardError {
  type: ErrorType;
  severity: ErrorSeverity;
  message: string;
  code: string;
  details?: Record<string, any>;
  timestamp: string;
  requestId?: string;
  userId?: string;
  context?: Record<string, any>;
  stack?: string;
  retryable: boolean;
  retryCount?: number;
  maxRetries?: number;
}

// API响应接口
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    type: string;
    message: string;
    code: string;
    details?: Record<string, any>;
    retryable?: boolean;
  };
  meta?: {
    requestId: string;
    timestamp: string;
    version: string;
  };
}

// 重试配置
export interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
  retryableErrors: ErrorType[];
}

export class ErrorHandlingService {
  private static instance: ErrorHandlingService;
  private errorLog: StandardError[] = [];
  private alertThresholds: Map<ErrorType, number> = new Map();

  constructor() {
    this.initializeAlertThresholds();
  }

  static getInstance(): ErrorHandlingService {
    if (!ErrorHandlingService.instance) {
      ErrorHandlingService.instance = new ErrorHandlingService();
    }
    return ErrorHandlingService.instance;
  }

  /**
   * 创建标准化错误
   */
  createError(
    type: ErrorType,
    message: string,
    options: {
      severity?: ErrorSeverity;
      code?: string;
      details?: Record<string, any>;
      context?: Record<string, any>;
      userId?: string;
      requestId?: string;
      originalError?: Error;
      retryable?: boolean;
    } = {}
  ): StandardError {
    const error: StandardError = {
      type,
      severity: options.severity || this.getDefaultSeverity(type),
      message,
      code: options.code || this.generateErrorCode(type),
      details: options.details,
      timestamp: new Date().toISOString(),
      requestId: options.requestId || this.generateRequestId(),
      userId: options.userId,
      context: options.context,
      stack: options.originalError?.stack,
      retryable: options.retryable ?? this.isRetryableByDefault(type),
      retryCount: 0,
      maxRetries: this.getMaxRetries(type)
    };

    // 记录错误
    this.logError(error);

    // 检查是否需要告警
    this.checkAlertThreshold(error);

    return error;
  }

  /**
   * 数据库错误处理
   */
  handleDatabaseError(error: Error, context?: Record<string, any>): StandardError {
    const isConnectionError = this.isDatabaseConnectionError(error);
    const isTimeoutError = this.isDatabaseTimeoutError(error);

    let errorType = ErrorType.DATABASE_ERROR;
    let severity = ErrorSeverity.MEDIUM;
    let retryable = false;

    if (isConnectionError) {
      severity = ErrorSeverity.HIGH;
      retryable = true;
    } else if (isTimeoutError) {
      errorType = ErrorType.TIMEOUT_ERROR;
      retryable = true;
    }

    return this.createError(errorType, error.message, {
      severity,
      details: {
        originalError: error.name,
        isConnectionError,
        isTimeoutError
      },
      context,
      originalError: error,
      retryable
    });
  }

  /**
   * 区块链错误处理
   */
  handleBlockchainError(error: Error, context?: Record<string, any>): StandardError {
    const isNetworkError = this.isNetworkError(error);
    const isRateLimitError = this.isRateLimitError(error);

    let errorType = ErrorType.BLOCKCHAIN_ERROR;
    let severity = ErrorSeverity.MEDIUM;
    let retryable = true;

    if (isNetworkError) {
      errorType = ErrorType.NETWORK_ERROR;
      severity = ErrorSeverity.HIGH;
    } else if (isRateLimitError) {
      errorType = ErrorType.RATE_LIMIT_ERROR;
      severity = ErrorSeverity.LOW;
    }

    return this.createError(errorType, error.message, {
      severity,
      details: {
        originalError: error.name,
        isNetworkError,
        isRateLimitError
      },
      context,
      originalError: error,
      retryable
    });
  }

  /**
   * 重试机制
   */
  async withRetry<T>(
    operation: () => Promise<T>,
    config: Partial<RetryConfig> = {}
  ): Promise<T> {
    const defaultConfig: RetryConfig = {
      maxRetries: 3,
      baseDelay: 1000,
      maxDelay: 10000,
      backoffMultiplier: 2,
      retryableErrors: [
        ErrorType.DATABASE_ERROR,
        ErrorType.BLOCKCHAIN_ERROR,
        ErrorType.NETWORK_ERROR,
        ErrorType.TIMEOUT_ERROR,
        ErrorType.EXTERNAL_SERVICE_ERROR
      ]
    };

    const finalConfig = { ...defaultConfig, ...config };
    let lastError: StandardError | null = null;

    for (let attempt = 0; attempt <= finalConfig.maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        const standardError = this.standardizeError(error);
        lastError = standardError;

        // 检查是否可重试
        if (!standardError.retryable || !finalConfig.retryableErrors.includes(standardError.type)) {
          throw standardError;
        }

        // 最后一次尝试失败
        if (attempt === finalConfig.maxRetries) {
          standardError.retryCount = attempt;
          throw standardError;
        }

        // 计算延迟时间
        const delay = Math.min(
          finalConfig.baseDelay * Math.pow(finalConfig.backoffMultiplier, attempt),
          finalConfig.maxDelay
        );

        console.warn(`操作失败，${delay}ms后重试 (${attempt + 1}/${finalConfig.maxRetries}):`, standardError.message);
        await this.sleep(delay);
      }
    }

    throw lastError;
  }

  /**
   * 优雅降级处理
   */
  async withGracefulDegradation<T>(
    primaryOperation: () => Promise<T>,
    fallbackOperation: () => Promise<T>,
    context?: Record<string, any>
  ): Promise<T> {
    try {
      return await primaryOperation();
    } catch (error) {
      const standardError = this.standardizeError(error);
      
      console.warn('主要操作失败，使用降级方案:', standardError.message);
      
      // 记录降级事件
      this.logDegradationEvent(standardError, context);

      try {
        return await fallbackOperation();
      } catch (fallbackError) {
        const fallbackStandardError = this.standardizeError(fallbackError);
        
        // 降级方案也失败了
        console.error('降级方案也失败:', fallbackStandardError.message);
        
        // 抛出原始错误
        throw standardError;
      }
    }
  }

  /**
   * 创建标准化API响应
   */
  createApiResponse<T>(
    success: boolean,
    data?: T,
    error?: StandardError,
    requestId?: string
  ): ApiResponse<T> {
    const response: ApiResponse<T> = {
      success,
      meta: {
        requestId: requestId || this.generateRequestId(),
        timestamp: new Date().toISOString(),
        version: '1.0.0'
      }
    };

    if (success && data !== undefined) {
      response.data = data;
    }

    if (!success && error) {
      response.error = {
        type: error.type,
        message: error.message,
        code: error.code,
        details: error.details,
        retryable: error.retryable
      };
    }

    return response;
  }

  /**
   * 标准化错误
   */
  standardizeError(error: any): StandardError {
    if (error instanceof Error) {
      // 检查是否已经是标准化错误
      if ('type' in error && 'severity' in error) {
        return error as StandardError;
      }

      // 根据错误类型进行分类
      if (this.isDatabaseError(error)) {
        return this.handleDatabaseError(error);
      } else if (this.isBlockchainError(error)) {
        return this.handleBlockchainError(error);
      } else if (this.isValidationError(error)) {
        return this.createError(ErrorType.VALIDATION_ERROR, error.message, {
          originalError: error,
          retryable: false
        });
      } else {
        return this.createError(ErrorType.UNKNOWN_ERROR, error.message, {
          originalError: error
        });
      }
    }

    // 处理字符串错误
    if (typeof error === 'string') {
      return this.createError(ErrorType.UNKNOWN_ERROR, error);
    }

    // 处理其他类型的错误
    return this.createError(ErrorType.UNKNOWN_ERROR, '未知错误', {
      details: { originalError: error }
    });
  }

  // 私有方法

  private initializeAlertThresholds() {
    this.alertThresholds.set(ErrorType.DATABASE_ERROR, 5);
    this.alertThresholds.set(ErrorType.BLOCKCHAIN_ERROR, 10);
    this.alertThresholds.set(ErrorType.NETWORK_ERROR, 3);
    this.alertThresholds.set(ErrorType.TIMEOUT_ERROR, 5);
  }

  private getDefaultSeverity(type: ErrorType): ErrorSeverity {
    const severityMap: Record<ErrorType, ErrorSeverity> = {
      [ErrorType.DATABASE_ERROR]: ErrorSeverity.HIGH,
      [ErrorType.BLOCKCHAIN_ERROR]: ErrorSeverity.MEDIUM,
      [ErrorType.VALIDATION_ERROR]: ErrorSeverity.LOW,
      [ErrorType.AUTHENTICATION_ERROR]: ErrorSeverity.MEDIUM,
      [ErrorType.AUTHORIZATION_ERROR]: ErrorSeverity.MEDIUM,
      [ErrorType.RATE_LIMIT_ERROR]: ErrorSeverity.LOW,
      [ErrorType.EXTERNAL_SERVICE_ERROR]: ErrorSeverity.MEDIUM,
      [ErrorType.BUSINESS_LOGIC_ERROR]: ErrorSeverity.MEDIUM,
      [ErrorType.NETWORK_ERROR]: ErrorSeverity.HIGH,
      [ErrorType.TIMEOUT_ERROR]: ErrorSeverity.MEDIUM,
      [ErrorType.UNKNOWN_ERROR]: ErrorSeverity.MEDIUM
    };
    return severityMap[type];
  }

  private generateErrorCode(type: ErrorType): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 5);
    return `${type}_${timestamp}_${random}`.toUpperCase();
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private isRetryableByDefault(type: ErrorType): boolean {
    const retryableTypes = [
      ErrorType.DATABASE_ERROR,
      ErrorType.BLOCKCHAIN_ERROR,
      ErrorType.NETWORK_ERROR,
      ErrorType.TIMEOUT_ERROR,
      ErrorType.EXTERNAL_SERVICE_ERROR,
      ErrorType.RATE_LIMIT_ERROR
    ];
    return retryableTypes.includes(type);
  }

  private getMaxRetries(type: ErrorType): number {
    const retryMap: Record<ErrorType, number> = {
      [ErrorType.DATABASE_ERROR]: 3,
      [ErrorType.BLOCKCHAIN_ERROR]: 5,
      [ErrorType.NETWORK_ERROR]: 3,
      [ErrorType.TIMEOUT_ERROR]: 2,
      [ErrorType.EXTERNAL_SERVICE_ERROR]: 3,
      [ErrorType.RATE_LIMIT_ERROR]: 5,
      [ErrorType.VALIDATION_ERROR]: 0,
      [ErrorType.AUTHENTICATION_ERROR]: 0,
      [ErrorType.AUTHORIZATION_ERROR]: 0,
      [ErrorType.BUSINESS_LOGIC_ERROR]: 0,
      [ErrorType.UNKNOWN_ERROR]: 1
    };
    return retryMap[type] || 0;
  }

  private isDatabaseError(error: Error): boolean {
    return error.message.includes('database') || 
           error.message.includes('connection') ||
           error.message.includes('supabase') ||
           error.name.includes('Database');
  }

  private isBlockchainError(error: Error): boolean {
    return error.message.includes('blockchain') ||
           error.message.includes('ethereum') ||
           error.message.includes('web3') ||
           error.message.includes('ethers');
  }

  private isValidationError(error: Error): boolean {
    return error.message.includes('validation') ||
           error.message.includes('invalid') ||
           error.name.includes('Validation');
  }

  private isDatabaseConnectionError(error: Error): boolean {
    return error.message.includes('connection') ||
           error.message.includes('connect') ||
           error.message.includes('ECONNREFUSED');
  }

  private isDatabaseTimeoutError(error: Error): boolean {
    return error.message.includes('timeout') ||
           error.message.includes('ETIMEDOUT');
  }

  private isNetworkError(error: Error): boolean {
    return error.message.includes('network') ||
           error.message.includes('ENOTFOUND') ||
           error.message.includes('ECONNRESET');
  }

  private isRateLimitError(error: Error): boolean {
    return error.message.includes('rate limit') ||
           error.message.includes('too many requests') ||
           error.message.includes('429');
  }

  private logError(error: StandardError) {
    this.errorLog.push(error);
    
    // 保持错误日志大小
    if (this.errorLog.length > 1000) {
      this.errorLog = this.errorLog.slice(-500);
    }

    // 根据严重级别决定日志级别
    switch (error.severity) {
      case ErrorSeverity.CRITICAL:
        console.error('🚨 CRITICAL ERROR:', error);
        break;
      case ErrorSeverity.HIGH:
        console.error('❌ HIGH ERROR:', error);
        break;
      case ErrorSeverity.MEDIUM:
        console.warn('⚠️ MEDIUM ERROR:', error);
        break;
      case ErrorSeverity.LOW:
        console.info('ℹ️ LOW ERROR:', error);
        break;
    }
  }

  private checkAlertThreshold(error: StandardError) {
    const threshold = this.alertThresholds.get(error.type);
    if (!threshold) return;

    // 计算最近1小时内同类型错误数量
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    const recentErrors = this.errorLog.filter(
      e => e.type === error.type && new Date(e.timestamp) > oneHourAgo
    );

    if (recentErrors.length >= threshold) {
      this.triggerAlert(error.type, recentErrors.length, threshold);
    }
  }

  private triggerAlert(errorType: ErrorType, count: number, threshold: number) {
    console.error(`🚨 ALERT: ${errorType} 错误数量超过阈值 (${count}/${threshold})`);
    
    // 这里可以集成实际的告警系统
    // 例如发送邮件、Slack通知等
  }

  private logDegradationEvent(error: StandardError, context?: Record<string, any>) {
    console.warn('🔄 DEGRADATION EVENT:', {
      error: error.message,
      type: error.type,
      context,
      timestamp: new Date().toISOString()
    });
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 导出单例实例
export const errorHandler = ErrorHandlingService.getInstance();
