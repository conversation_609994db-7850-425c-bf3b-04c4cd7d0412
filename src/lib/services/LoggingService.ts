/**
 * 日志记录分析服务
 * 提供结构化日志记录、日志分析、搜索和聚合功能
 */

import { EventEmitter } from 'events';

// 日志级别
export enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error',
  FATAL = 'fatal'
}

// 日志条目接口
export interface LogEntry {
  id: string;
  timestamp: number;
  level: LogLevel;
  message: string;
  category: string;
  source: string;
  userId?: string;
  sessionId?: string;
  requestId?: string;
  metadata?: Record<string, any>;
  tags?: string[];
  duration?: number;
  stackTrace?: string;
}

// 日志查询条件
export interface LogQuery {
  startTime?: number;
  endTime?: number;
  levels?: LogLevel[];
  categories?: string[];
  sources?: string[];
  userId?: string;
  searchText?: string;
  tags?: string[];
  limit?: number;
  offset?: number;
}

// 日志统计信息
export interface LogStats {
  totalLogs: number;
  byLevel: Record<LogLevel, number>;
  byCategory: Record<string, number>;
  bySource: Record<string, number>;
  timeRange: {
    start: number;
    end: number;
  };
  topErrors: Array<{
    message: string;
    count: number;
    lastOccurrence: number;
  }>;
  performanceMetrics: {
    averageResponseTime: number;
    slowestRequests: Array<{
      requestId: string;
      duration: number;
      endpoint: string;
    }>;
  };
}

// 日志聚合结果
export interface LogAggregation {
  field: string;
  buckets: Array<{
    key: string;
    count: number;
    percentage: number;
  }>;
}

/**
 * 日志记录服务
 */
export class LoggingService extends EventEmitter {
  private static instance: LoggingService;
  private logs: LogEntry[] = [];
  private maxLogs = 10000; // 最大日志条目数
  private logBuffer: LogEntry[] = [];
  private flushInterval?: NodeJS.Timeout;
  private isRunning = false;

  static getInstance(): LoggingService {
    if (!LoggingService.instance) {
      LoggingService.instance = new LoggingService();
    }
    return LoggingService.instance;
  }

  /**
   * 启动日志服务
   */
  start(): void {
    if (this.isRunning) return;

    this.isRunning = true;
    
    // 每5秒刷新一次日志缓冲区
    this.flushInterval = setInterval(() => {
      this.flushLogs();
    }, 5000);

    console.log('Logging service started');
  }

  /**
   * 停止日志服务
   */
  stop(): void {
    if (!this.isRunning) return;

    this.isRunning = false;
    
    if (this.flushInterval) {
      clearInterval(this.flushInterval);
    }

    // 刷新剩余的日志
    this.flushLogs();

    console.log('Logging service stopped');
  }

  /**
   * 记录日志
   */
  log(
    level: LogLevel,
    message: string,
    category: string,
    source: string,
    metadata?: Record<string, any>
  ): string {
    const logEntry: LogEntry = {
      id: this.generateLogId(),
      timestamp: Date.now(),
      level,
      message,
      category,
      source,
      metadata,
      tags: this.extractTags(message, metadata)
    };

    // 添加到缓冲区
    this.logBuffer.push(logEntry);

    // 如果是错误或致命错误，立即刷新
    if (level === LogLevel.ERROR || level === LogLevel.FATAL) {
      this.flushLogs();
    }

    return logEntry.id;
  }

  /**
   * 便捷的日志记录方法
   */
  debug(message: string, category: string = 'general', source: string = 'app', metadata?: Record<string, any>): string {
    return this.log(LogLevel.DEBUG, message, category, source, metadata);
  }

  info(message: string, category: string = 'general', source: string = 'app', metadata?: Record<string, any>): string {
    return this.log(LogLevel.INFO, message, category, source, metadata);
  }

  warn(message: string, category: string = 'general', source: string = 'app', metadata?: Record<string, any>): string {
    return this.log(LogLevel.WARN, message, category, source, metadata);
  }

  error(message: string, category: string = 'general', source: string = 'app', metadata?: Record<string, any>): string {
    return this.log(LogLevel.ERROR, message, category, source, metadata);
  }

  fatal(message: string, category: string = 'general', source: string = 'app', metadata?: Record<string, any>): string {
    return this.log(LogLevel.FATAL, message, category, source, metadata);
  }

  /**
   * 记录API请求日志
   */
  logApiRequest(
    method: string,
    url: string,
    statusCode: number,
    duration: number,
    userId?: string,
    requestId?: string
  ): string {
    return this.log(
      statusCode >= 400 ? LogLevel.ERROR : LogLevel.INFO,
      `${method} ${url} - ${statusCode}`,
      'api',
      'server',
      {
        method,
        url,
        statusCode,
        duration,
        userId,
        requestId
      }
    );
  }

  /**
   * 记录业务操作日志
   */
  logBusinessOperation(
    operation: string,
    userId: string,
    details: Record<string, any>,
    success: boolean = true
  ): string {
    return this.log(
      success ? LogLevel.INFO : LogLevel.ERROR,
      `Business operation: ${operation}`,
      'business',
      'app',
      {
        operation,
        userId,
        success,
        ...details
      }
    );
  }

  /**
   * 记录安全事件日志
   */
  logSecurityEvent(
    event: string,
    severity: 'low' | 'medium' | 'high' | 'critical',
    details: Record<string, any>
  ): string {
    const level = severity === 'critical' ? LogLevel.FATAL : 
                 severity === 'high' ? LogLevel.ERROR :
                 severity === 'medium' ? LogLevel.WARN : LogLevel.INFO;

    return this.log(
      level,
      `Security event: ${event}`,
      'security',
      'security',
      {
        event,
        severity,
        ...details
      }
    );
  }

  /**
   * 刷新日志缓冲区
   */
  private flushLogs(): void {
    if (this.logBuffer.length === 0) return;

    // 将缓冲区的日志添加到主日志数组
    this.logs.push(...this.logBuffer);
    
    // 清空缓冲区
    this.logBuffer = [];

    // 保持日志数量在限制内
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }

    // 发出日志更新事件
    this.emit('logs-updated', this.logs.slice(-100)); // 发送最新100条日志
  }

  /**
   * 查询日志
   */
  queryLogs(query: LogQuery): LogEntry[] {
    let filteredLogs = [...this.logs];

    // 时间范围过滤
    if (query.startTime) {
      filteredLogs = filteredLogs.filter(log => log.timestamp >= query.startTime!);
    }
    if (query.endTime) {
      filteredLogs = filteredLogs.filter(log => log.timestamp <= query.endTime!);
    }

    // 日志级别过滤
    if (query.levels && query.levels.length > 0) {
      filteredLogs = filteredLogs.filter(log => query.levels!.includes(log.level));
    }

    // 分类过滤
    if (query.categories && query.categories.length > 0) {
      filteredLogs = filteredLogs.filter(log => query.categories!.includes(log.category));
    }

    // 来源过滤
    if (query.sources && query.sources.length > 0) {
      filteredLogs = filteredLogs.filter(log => query.sources!.includes(log.source));
    }

    // 用户ID过滤
    if (query.userId) {
      filteredLogs = filteredLogs.filter(log => log.userId === query.userId);
    }

    // 文本搜索
    if (query.searchText) {
      const searchLower = query.searchText.toLowerCase();
      filteredLogs = filteredLogs.filter(log => 
        log.message.toLowerCase().includes(searchLower) ||
        (log.metadata && JSON.stringify(log.metadata).toLowerCase().includes(searchLower))
      );
    }

    // 标签过滤
    if (query.tags && query.tags.length > 0) {
      filteredLogs = filteredLogs.filter(log => 
        log.tags && query.tags!.some(tag => log.tags!.includes(tag))
      );
    }

    // 排序（按时间倒序）
    filteredLogs.sort((a, b) => b.timestamp - a.timestamp);

    // 分页
    const offset = query.offset || 0;
    const limit = query.limit || 100;
    
    return filteredLogs.slice(offset, offset + limit);
  }

  /**
   * 获取日志统计信息
   */
  getLogStats(timeRange?: { start: number; end: number }): LogStats {
    let logs = this.logs;

    // 时间范围过滤
    if (timeRange) {
      logs = logs.filter(log => 
        log.timestamp >= timeRange.start && log.timestamp <= timeRange.end
      );
    }

    // 按级别统计
    const byLevel: Record<LogLevel, number> = {
      [LogLevel.DEBUG]: 0,
      [LogLevel.INFO]: 0,
      [LogLevel.WARN]: 0,
      [LogLevel.ERROR]: 0,
      [LogLevel.FATAL]: 0
    };

    // 按分类统计
    const byCategory: Record<string, number> = {};

    // 按来源统计
    const bySource: Record<string, number> = {};

    // 错误统计
    const errorCounts: Record<string, { count: number; lastOccurrence: number }> = {};

    // 性能统计
    const durations: number[] = [];

    for (const log of logs) {
      // 级别统计
      byLevel[log.level]++;

      // 分类统计
      byCategory[log.category] = (byCategory[log.category] || 0) + 1;

      // 来源统计
      bySource[log.source] = (bySource[log.source] || 0) + 1;

      // 错误统计
      if (log.level === LogLevel.ERROR || log.level === LogLevel.FATAL) {
        const key = log.message;
        if (!errorCounts[key]) {
          errorCounts[key] = { count: 0, lastOccurrence: 0 };
        }
        errorCounts[key].count++;
        errorCounts[key].lastOccurrence = Math.max(errorCounts[key].lastOccurrence, log.timestamp);
      }

      // 性能统计
      if (log.duration) {
        durations.push(log.duration);
      }
    }

    // 排序错误
    const topErrors = Object.entries(errorCounts)
      .map(([message, data]) => ({ message, ...data }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // 性能指标
    const averageResponseTime = durations.length > 0 ? 
      durations.reduce((sum, d) => sum + d, 0) / durations.length : 0;

    const slowestRequests = logs
      .filter(log => log.duration && log.duration > 1000)
      .sort((a, b) => (b.duration || 0) - (a.duration || 0))
      .slice(0, 10)
      .map(log => ({
        requestId: log.requestId || log.id,
        duration: log.duration || 0,
        endpoint: log.metadata?.url || 'unknown'
      }));

    return {
      totalLogs: logs.length,
      byLevel,
      byCategory,
      bySource,
      timeRange: timeRange || {
        start: logs.length > 0 ? Math.min(...logs.map(l => l.timestamp)) : 0,
        end: logs.length > 0 ? Math.max(...logs.map(l => l.timestamp)) : 0
      },
      topErrors,
      performanceMetrics: {
        averageResponseTime,
        slowestRequests
      }
    };
  }

  /**
   * 日志聚合分析
   */
  aggregateLogs(field: string, query?: LogQuery): LogAggregation {
    const logs = query ? this.queryLogs(query) : this.logs;
    const counts: Record<string, number> = {};

    for (const log of logs) {
      let value: string;
      
      switch (field) {
        case 'level':
          value = log.level;
          break;
        case 'category':
          value = log.category;
          break;
        case 'source':
          value = log.source;
          break;
        case 'hour':
          value = new Date(log.timestamp).getHours().toString();
          break;
        case 'day':
          value = new Date(log.timestamp).toDateString();
          break;
        default:
          value = log.metadata?.[field]?.toString() || 'unknown';
      }

      counts[value] = (counts[value] || 0) + 1;
    }

    const total = logs.length;
    const buckets = Object.entries(counts)
      .map(([key, count]) => ({
        key,
        count,
        percentage: (count / total) * 100
      }))
      .sort((a, b) => b.count - a.count);

    return {
      field,
      buckets
    };
  }

  /**
   * 生成日志ID
   */
  private generateLogId(): string {
    return `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 提取标签
   */
  private extractTags(message: string, metadata?: Record<string, any>): string[] {
    const tags: string[] = [];

    // 从消息中提取标签
    const tagMatches = message.match(/#\w+/g);
    if (tagMatches) {
      tags.push(...tagMatches.map(tag => tag.substring(1)));
    }

    // 从元数据中提取标签
    if (metadata?.tags && Array.isArray(metadata.tags)) {
      tags.push(...metadata.tags);
    }

    return [...new Set(tags)]; // 去重
  }

  /**
   * 导出日志
   */
  exportLogs(query?: LogQuery, format: 'json' | 'csv' = 'json'): string {
    const logs = query ? this.queryLogs(query) : this.logs;

    if (format === 'csv') {
      const headers = ['timestamp', 'level', 'category', 'source', 'message', 'userId', 'requestId'];
      const csvRows = [headers.join(',')];
      
      for (const log of logs) {
        const row = [
          new Date(log.timestamp).toISOString(),
          log.level,
          log.category,
          log.source,
          `"${log.message.replace(/"/g, '""')}"`,
          log.userId || '',
          log.requestId || ''
        ];
        csvRows.push(row.join(','));
      }
      
      return csvRows.join('\n');
    }

    return JSON.stringify(logs, null, 2);
  }

  /**
   * 清理旧日志
   */
  cleanupOldLogs(olderThanDays: number = 7): number {
    const cutoffTime = Date.now() - (olderThanDays * 24 * 60 * 60 * 1000);
    const initialCount = this.logs.length;
    
    this.logs = this.logs.filter(log => log.timestamp > cutoffTime);
    
    return initialCount - this.logs.length;
  }

  /**
   * 获取最新日志
   */
  getRecentLogs(limit: number = 100): LogEntry[] {
    return this.logs.slice(-limit).reverse();
  }
}

// 导出单例实例
export const loggingService = LoggingService.getInstance();
