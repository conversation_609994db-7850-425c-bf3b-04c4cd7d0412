import { supabase } from '@/lib/supabase';

// 操作类型枚举
export enum OperationType {
  BET_CREATED = 'bet_created',
  BET_JOINED = 'bet_joined',
  BET_STARTED = 'bet_started',
  JUDGMENT_ROUND_1 = 'judgment_round_1',
  JUDGMENT_ROUND_2 = 'judgment_round_2',
  JUDGMENT_ROUND_3 = 'judgment_round_3',
  JUDGMENT_COMPLETED = 'judgment_completed',
  CONFIRMATION_REQUESTED = 'confirmation_requested',
  CONFIRMATION_SUBMITTED = 'confirmation_submitted',
  DISPUTE_RAISED = 'dispute_raised',
  SETTLEMENT_STARTED = 'settlement_started',
  FORTUNE_DISTRIBUTED = 'fortune_distributed',
  BET_COMPLETED = 'bet_completed',
  BET_CANCELLED = 'bet_cancelled'
}

// 福气流转类型
export enum FortuneFlowType {
  STAKE_LOCKED = 'stake_locked',
  STAKE_UNLOCKED = 'stake_unlocked',
  FEE_DEDUCTED = 'fee_deducted',
  REWARD_DISTRIBUTED = 'reward_distributed',
  JUDGE_REWARD = 'judge_reward',
  REFERRAL_REWARD = 'referral_reward',
  REFUND_PROCESSED = 'refund_processed'
}

// 操作记录接口
export interface OperationRecord {
  id: string;
  betId: string;
  operationType: OperationType;
  operatorId: string;
  operatorUsername: string;
  operatorTelegramId: string;
  description: string;
  metadata: Record<string, any>;
  timestamp: string;
  blockHeight?: number;
  txHash?: string;
}

// 福气流转记录接口
export interface FortuneFlowRecord {
  id: string;
  betId: string;
  flowType: FortuneFlowType;
  fromUserId?: string;
  toUserId?: string;
  amount: number;
  description: string;
  metadata: Record<string, any>;
  timestamp: string;
  txHash?: string;
}

// 赌约生命周期接口
export interface BetLifecycle {
  betId: string;
  betTitle: string;
  createdAt: string;
  currentStatus: string;
  operations: OperationRecord[];
  fortuneFlows: FortuneFlowRecord[];
  participants: Array<{
    userId: string;
    username: string;
    telegramId: string;
    role: 'creator' | 'participant' | 'judge';
    joinedAt: string;
  }>;
  timeline: Array<{
    timestamp: string;
    type: 'operation' | 'fortune_flow';
    data: OperationRecord | FortuneFlowRecord;
  }>;
}

export class TraceabilityService {
  /**
   * 记录操作
   */
  async recordOperation(
    betId: string,
    operationType: OperationType,
    operatorId: string,
    description: string,
    metadata: Record<string, any> = {},
    txHash?: string
  ): Promise<OperationRecord> {
    try {
      // 获取操作者信息
      const operator = await this.getUserInfo(operatorId);
      
      const operationData = {
        bet_id: betId,
        operation_type: operationType,
        operator_id: operatorId,
        operator_username: operator.username,
        operator_telegram_id: operator.telegramId,
        description,
        metadata,
        timestamp: new Date().toISOString(),
        tx_hash: txHash
      };

      const { data: operation, error } = await supabase
        .from('bet_operation_logs')
        .insert(operationData)
        .select()
        .single();

      if (error) throw error;

      return this.formatOperationRecord(operation);
    } catch (error) {
      console.error('记录操作失败:', error);
      // 开发环境返回模拟数据
      if (process.env.NODE_ENV === 'development') {
        return {
          id: `mock-op-${Date.now()}`,
          betId,
          operationType,
          operatorId,
          operatorUsername: '测试用户',
          operatorTelegramId: '@test_user',
          description,
          metadata,
          timestamp: new Date().toISOString(),
          txHash
        };
      }
      throw error;
    }
  }

  /**
   * 记录福气流转
   */
  async recordFortuneFlow(
    betId: string,
    flowType: FortuneFlowType,
    amount: number,
    description: string,
    fromUserId?: string,
    toUserId?: string,
    metadata: Record<string, any> = {},
    txHash?: string
  ): Promise<FortuneFlowRecord> {
    try {
      const flowData = {
        bet_id: betId,
        flow_type: flowType,
        from_user_id: fromUserId,
        to_user_id: toUserId,
        amount,
        description,
        metadata,
        timestamp: new Date().toISOString(),
        tx_hash: txHash
      };

      const { data: flow, error } = await supabase
        .from('bet_fortune_flows')
        .insert(flowData)
        .select()
        .single();

      if (error) throw error;

      return this.formatFortuneFlowRecord(flow);
    } catch (error) {
      console.error('记录福气流转失败:', error);
      // 开发环境返回模拟数据
      if (process.env.NODE_ENV === 'development') {
        return {
          id: `mock-flow-${Date.now()}`,
          betId,
          flowType,
          fromUserId,
          toUserId,
          amount,
          description,
          metadata,
          timestamp: new Date().toISOString(),
          txHash
        };
      }
      throw error;
    }
  }

  /**
   * 获取赌约完整生命周期
   */
  async getBetLifecycle(betId: string): Promise<BetLifecycle> {
    try {
      // 获取赌约基本信息
      const betInfo = await this.getBetInfo(betId);
      
      // 获取操作记录
      const operations = await this.getBetOperations(betId);
      
      // 获取福气流转记录
      const fortuneFlows = await this.getBetFortuneFlows(betId);
      
      // 获取参与者信息
      const participants = await this.getBetParticipants(betId);
      
      // 构建时间线
      const timeline = this.buildTimeline(operations, fortuneFlows);

      return {
        betId,
        betTitle: betInfo.title,
        createdAt: betInfo.createdAt,
        currentStatus: betInfo.status,
        operations,
        fortuneFlows,
        participants,
        timeline
      };
    } catch (error) {
      console.error('获取赌约生命周期失败:', error);
      // 开发环境返回模拟数据
      if (process.env.NODE_ENV === 'development') {
        return this.getMockBetLifecycle(betId);
      }
      throw error;
    }
  }

  /**
   * 获取用户相关的所有操作记录
   */
  async getUserOperationHistory(userId: string, page: number = 1, limit: number = 20): Promise<{
    operations: OperationRecord[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    try {
      const offset = (page - 1) * limit;

      const { data: operations, count } = await supabase
        .from('bet_operation_logs')
        .select('*', { count: 'exact' })
        .eq('operator_id', userId)
        .order('timestamp', { ascending: false })
        .range(offset, offset + limit - 1);

      const formattedOperations = operations?.map(op => this.formatOperationRecord(op)) || [];

      return {
        operations: formattedOperations,
        total: count || 0,
        page,
        totalPages: Math.ceil((count || 0) / limit)
      };
    } catch (error) {
      console.error('获取用户操作历史失败:', error);
      // 开发环境返回模拟数据
      if (process.env.NODE_ENV === 'development') {
        return this.getMockUserOperationHistory(userId, page, limit);
      }
      throw error;
    }
  }

  /**
   * 获取福气流转统计
   */
  async getFortuneFlowStats(betId?: string, userId?: string): Promise<{
    totalInflow: number;
    totalOutflow: number;
    totalFees: number;
    totalRewards: number;
    flowCount: number;
  }> {
    try {
      let query = supabase.from('bet_fortune_flows').select('*');
      
      if (betId) {
        query = query.eq('bet_id', betId);
      }
      
      if (userId) {
        query = query.or(`from_user_id.eq.${userId},to_user_id.eq.${userId}`);
      }

      const { data: flows } = await query;

      if (!flows) {
        return {
          totalInflow: 0,
          totalOutflow: 0,
          totalFees: 0,
          totalRewards: 0,
          flowCount: 0
        };
      }

      const stats = flows.reduce((acc, flow) => {
        acc.flowCount++;
        
        if (flow.flow_type === FortuneFlowType.FEE_DEDUCTED) {
          acc.totalFees += flow.amount;
        } else if (flow.flow_type === FortuneFlowType.REWARD_DISTRIBUTED || 
                   flow.flow_type === FortuneFlowType.JUDGE_REWARD ||
                   flow.flow_type === FortuneFlowType.REFERRAL_REWARD) {
          acc.totalRewards += flow.amount;
        }

        if (userId) {
          if (flow.to_user_id === userId) {
            acc.totalInflow += flow.amount;
          }
          if (flow.from_user_id === userId) {
            acc.totalOutflow += flow.amount;
          }
        }

        return acc;
      }, {
        totalInflow: 0,
        totalOutflow: 0,
        totalFees: 0,
        totalRewards: 0,
        flowCount: 0
      });

      return stats;
    } catch (error) {
      console.error('获取福气流转统计失败:', error);
      return {
        totalInflow: 0,
        totalOutflow: 0,
        totalFees: 0,
        totalRewards: 0,
        flowCount: 0
      };
    }
  }

  // 私有方法

  private async getUserInfo(userId: string) {
    try {
      const { data: user } = await supabase
        .from('users')
        .select('username, telegram_id')
        .eq('id', userId)
        .single();

      return {
        username: user?.username || '未知用户',
        telegramId: user?.telegram_id || '@unknown'
      };
    } catch (error) {
      return {
        username: '未知用户',
        telegramId: '@unknown'
      };
    }
  }

  private async getBetInfo(betId: string) {
    try {
      const { data: bet } = await supabase
        .from('social_bets')
        .select('title, status, created_at')
        .eq('id', betId)
        .single();

      return {
        title: bet?.title || '未知赌约',
        status: bet?.status || 'unknown',
        createdAt: bet?.created_at || new Date().toISOString()
      };
    } catch (error) {
      return {
        title: '未知赌约',
        status: 'unknown',
        createdAt: new Date().toISOString()
      };
    }
  }

  private async getBetOperations(betId: string): Promise<OperationRecord[]> {
    try {
      const { data: operations } = await supabase
        .from('bet_operation_logs')
        .select('*')
        .eq('bet_id', betId)
        .order('timestamp', { ascending: true });

      return operations?.map(op => this.formatOperationRecord(op)) || [];
    } catch (error) {
      return [];
    }
  }

  private async getBetFortuneFlows(betId: string): Promise<FortuneFlowRecord[]> {
    try {
      const { data: flows } = await supabase
        .from('bet_fortune_flows')
        .select('*')
        .eq('bet_id', betId)
        .order('timestamp', { ascending: true });

      return flows?.map(flow => this.formatFortuneFlowRecord(flow)) || [];
    } catch (error) {
      return [];
    }
  }

  private async getBetParticipants(betId: string) {
    try {
      const { data: participants } = await supabase
        .from('bet_participants')
        .select(`
          user_id,
          created_at,
          users!inner(username, telegram_id)
        `)
        .eq('bet_id', betId);

      return participants?.map(p => ({
        userId: p.user_id,
        username: p.users.username,
        telegramId: p.users.telegram_id,
        role: 'participant' as const,
        joinedAt: p.created_at
      })) || [];
    } catch (error) {
      return [];
    }
  }

  private buildTimeline(operations: OperationRecord[], fortuneFlows: FortuneFlowRecord[]) {
    const timeline = [
      ...operations.map(op => ({
        timestamp: op.timestamp,
        type: 'operation' as const,
        data: op
      })),
      ...fortuneFlows.map(flow => ({
        timestamp: flow.timestamp,
        type: 'fortune_flow' as const,
        data: flow
      }))
    ];

    return timeline.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
  }

  private formatOperationRecord(data: any): OperationRecord {
    return {
      id: data.id,
      betId: data.bet_id,
      operationType: data.operation_type,
      operatorId: data.operator_id,
      operatorUsername: data.operator_username,
      operatorTelegramId: data.operator_telegram_id,
      description: data.description,
      metadata: data.metadata || {},
      timestamp: data.timestamp,
      blockHeight: data.block_height,
      txHash: data.tx_hash
    };
  }

  private formatFortuneFlowRecord(data: any): FortuneFlowRecord {
    return {
      id: data.id,
      betId: data.bet_id,
      flowType: data.flow_type,
      fromUserId: data.from_user_id,
      toUserId: data.to_user_id,
      amount: data.amount,
      description: data.description,
      metadata: data.metadata || {},
      timestamp: data.timestamp,
      txHash: data.tx_hash
    };
  }

  private getMockBetLifecycle(betId: string): BetLifecycle {
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
    const twoHoursAgo = new Date(now.getTime() - 2 * 60 * 60 * 1000);

    const operations: OperationRecord[] = [
      {
        id: 'op-1',
        betId,
        operationType: OperationType.BET_CREATED,
        operatorId: 'user-1',
        operatorUsername: '创建者',
        operatorTelegramId: '@creator',
        description: '创建赌约：2024年世界杯冠军预测',
        metadata: { betType: '1vN', options: ['巴西', '阿根廷', '法国'] },
        timestamp: twoHoursAgo.toISOString()
      },
      {
        id: 'op-2',
        betId,
        operationType: OperationType.BET_JOINED,
        operatorId: 'user-2',
        operatorUsername: '参与者A',
        operatorTelegramId: '@participant_a',
        description: '参与赌约，选择：巴西',
        metadata: { selectedOption: 0, stakeAmount: 1000 },
        timestamp: oneHourAgo.toISOString()
      }
    ];

    const fortuneFlows: FortuneFlowRecord[] = [
      {
        id: 'flow-1',
        betId,
        flowType: FortuneFlowType.STAKE_LOCKED,
        fromUserId: 'user-1',
        toUserId: undefined,
        amount: 5000,
        description: '创建者质押锁定',
        metadata: { reason: 'bet_creation' },
        timestamp: twoHoursAgo.toISOString()
      },
      {
        id: 'flow-2',
        betId,
        flowType: FortuneFlowType.STAKE_LOCKED,
        fromUserId: 'user-2',
        toUserId: undefined,
        amount: 1000,
        description: '参与者投注锁定',
        metadata: { selectedOption: 0 },
        timestamp: oneHourAgo.toISOString()
      }
    ];

    const participants = [
      {
        userId: 'user-1',
        username: '创建者',
        telegramId: '@creator',
        role: 'creator' as const,
        joinedAt: twoHoursAgo.toISOString()
      },
      {
        userId: 'user-2',
        username: '参与者A',
        telegramId: '@participant_a',
        role: 'participant' as const,
        joinedAt: oneHourAgo.toISOString()
      }
    ];

    const timeline = this.buildTimeline(operations, fortuneFlows);

    return {
      betId,
      betTitle: '2024年世界杯冠军预测',
      createdAt: twoHoursAgo.toISOString(),
      currentStatus: 'open',
      operations,
      fortuneFlows,
      participants,
      timeline
    };
  }

  private getMockUserOperationHistory(userId: string, page: number, limit: number) {
    const mockOperations: OperationRecord[] = [
      {
        id: 'op-1',
        betId: 'bet-1',
        operationType: OperationType.BET_CREATED,
        operatorId: userId,
        operatorUsername: '测试用户',
        operatorTelegramId: '@test_user',
        description: '创建赌约：Bitcoin价格预测',
        metadata: { betType: '1v1' },
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
      },
      {
        id: 'op-2',
        betId: 'bet-2',
        operationType: OperationType.BET_JOINED,
        operatorId: userId,
        operatorUsername: '测试用户',
        operatorTelegramId: '@test_user',
        description: '参与赌约：世界杯冠军预测',
        metadata: { selectedOption: 0, stakeAmount: 1000 },
        timestamp: new Date(Date.now() - 60 * 60 * 1000).toISOString()
      }
    ];

    return {
      operations: mockOperations,
      total: mockOperations.length,
      page,
      totalPages: 1
    };
  }
}

// 导出单例实例
export const traceabilityService = new TraceabilityService();
