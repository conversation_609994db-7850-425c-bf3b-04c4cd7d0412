import { ErrorType, ErrorSeverity, StandardError } from './errorHandlingService';

// 监控指标接口
export interface SystemMetrics {
  timestamp: string;
  apiRequests: {
    total: number;
    successful: number;
    failed: number;
    averageResponseTime: number;
  };
  errors: {
    total: number;
    byType: Record<ErrorType, number>;
    bySeverity: Record<ErrorSeverity, number>;
  };
  performance: {
    memoryUsage: number;
    cpuUsage: number;
    activeConnections: number;
  };
  business: {
    activeBets: number;
    dailyTransactions: number;
    userSessions: number;
  };
}

// 告警规则接口
export interface AlertRule {
  id: string;
  name: string;
  description: string;
  condition: (metrics: SystemMetrics) => boolean;
  severity: 'low' | 'medium' | 'high' | 'critical';
  cooldownMinutes: number;
  enabled: boolean;
  lastTriggered?: string;
}

// 告警事件接口
export interface AlertEvent {
  id: string;
  ruleId: string;
  ruleName: string;
  severity: string;
  message: string;
  timestamp: string;
  metrics: Partial<SystemMetrics>;
  resolved: boolean;
  resolvedAt?: string;
}

export class MonitoringService {
  private static instance: MonitoringService;
  private metrics: SystemMetrics[] = [];
  private alertRules: AlertRule[] = [];
  private alertEvents: AlertEvent[] = [];
  private metricsRetentionHours = 24;
  private alertRetentionDays = 7;

  constructor() {
    this.initializeDefaultAlertRules();
    this.startMetricsCollection();
  }

  static getInstance(): MonitoringService {
    if (!MonitoringService.instance) {
      MonitoringService.instance = new MonitoringService();
    }
    return MonitoringService.instance;
  }

  /**
   * 记录API请求指标
   */
  recordApiRequest(
    endpoint: string,
    method: string,
    statusCode: number,
    responseTime: number,
    error?: StandardError
  ) {
    // 在实际实现中，这里会更新内存中的指标
    console.log('📊 API Request Recorded:', {
      endpoint,
      method,
      statusCode,
      responseTime,
      error: error?.type
    });
  }

  /**
   * 记录错误指标
   */
  recordError(error: StandardError) {
    console.log('📊 Error Recorded:', {
      type: error.type,
      severity: error.severity,
      message: error.message,
      timestamp: error.timestamp
    });

    // 检查是否触发告警
    this.checkAlertRules();
  }

  /**
   * 记录业务指标
   */
  recordBusinessMetric(metric: string, value: number, tags?: Record<string, string>) {
    console.log('📊 Business Metric Recorded:', {
      metric,
      value,
      tags,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 获取当前系统指标
   */
  getCurrentMetrics(): SystemMetrics {
    // 在实际实现中，这里会从内存或数据库中获取实时指标
    return {
      timestamp: new Date().toISOString(),
      apiRequests: {
        total: 1250,
        successful: 1180,
        failed: 70,
        averageResponseTime: 245
      },
      errors: {
        total: 15,
        byType: {
          [ErrorType.DATABASE_ERROR]: 3,
          [ErrorType.BLOCKCHAIN_ERROR]: 5,
          [ErrorType.VALIDATION_ERROR]: 4,
          [ErrorType.NETWORK_ERROR]: 2,
          [ErrorType.TIMEOUT_ERROR]: 1,
          [ErrorType.AUTHENTICATION_ERROR]: 0,
          [ErrorType.AUTHORIZATION_ERROR]: 0,
          [ErrorType.RATE_LIMIT_ERROR]: 0,
          [ErrorType.EXTERNAL_SERVICE_ERROR]: 0,
          [ErrorType.BUSINESS_LOGIC_ERROR]: 0,
          [ErrorType.UNKNOWN_ERROR]: 0
        },
        bySeverity: {
          [ErrorSeverity.LOW]: 4,
          [ErrorSeverity.MEDIUM]: 8,
          [ErrorSeverity.HIGH]: 3,
          [ErrorSeverity.CRITICAL]: 0
        }
      },
      performance: {
        memoryUsage: 65.5,
        cpuUsage: 23.8,
        activeConnections: 45
      },
      business: {
        activeBets: 28,
        dailyTransactions: 156,
        userSessions: 89
      }
    };
  }

  /**
   * 获取历史指标
   */
  getHistoricalMetrics(hours: number = 1): SystemMetrics[] {
    const cutoffTime = new Date(Date.now() - hours * 60 * 60 * 1000);
    return this.metrics.filter(m => new Date(m.timestamp) > cutoffTime);
  }

  /**
   * 添加告警规则
   */
  addAlertRule(rule: Omit<AlertRule, 'id'>): AlertRule {
    const newRule: AlertRule = {
      ...rule,
      id: `rule_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`
    };
    
    this.alertRules.push(newRule);
    console.log('📋 Alert Rule Added:', newRule.name);
    
    return newRule;
  }

  /**
   * 获取活跃告警
   */
  getActiveAlerts(): AlertEvent[] {
    return this.alertEvents.filter(alert => !alert.resolved);
  }

  /**
   * 获取告警历史
   */
  getAlertHistory(days: number = 1): AlertEvent[] {
    const cutoffTime = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
    return this.alertEvents.filter(alert => new Date(alert.timestamp) > cutoffTime);
  }

  /**
   * 解决告警
   */
  resolveAlert(alertId: string, resolvedBy?: string) {
    const alert = this.alertEvents.find(a => a.id === alertId);
    if (alert && !alert.resolved) {
      alert.resolved = true;
      alert.resolvedAt = new Date().toISOString();
      
      console.log('✅ Alert Resolved:', {
        alertId,
        ruleName: alert.ruleName,
        resolvedBy
      });
    }
  }

  /**
   * 生成监控报告
   */
  generateReport(hours: number = 24): {
    summary: any;
    metrics: SystemMetrics[];
    alerts: AlertEvent[];
    recommendations: string[];
  } {
    const metrics = this.getHistoricalMetrics(hours);
    const alerts = this.getAlertHistory(Math.ceil(hours / 24));
    
    const summary = {
      timeRange: `${hours}小时`,
      totalRequests: metrics.reduce((sum, m) => sum + m.apiRequests.total, 0),
      totalErrors: metrics.reduce((sum, m) => sum + m.errors.total, 0),
      averageResponseTime: metrics.length > 0 
        ? metrics.reduce((sum, m) => sum + m.apiRequests.averageResponseTime, 0) / metrics.length
        : 0,
      alertCount: alerts.length,
      criticalAlerts: alerts.filter(a => a.severity === 'critical').length
    };

    const recommendations = this.generateRecommendations(metrics, alerts);

    return {
      summary,
      metrics,
      alerts,
      recommendations
    };
  }

  // 私有方法

  private initializeDefaultAlertRules() {
    // 高错误率告警
    this.addAlertRule({
      name: '高错误率告警',
      description: 'API错误率超过5%',
      condition: (metrics) => {
        const errorRate = metrics.apiRequests.total > 0 
          ? (metrics.apiRequests.failed / metrics.apiRequests.total) * 100
          : 0;
        return errorRate > 5;
      },
      severity: 'high',
      cooldownMinutes: 15,
      enabled: true
    });

    // 响应时间告警
    this.addAlertRule({
      name: '响应时间过长告警',
      description: '平均响应时间超过1秒',
      condition: (metrics) => metrics.apiRequests.averageResponseTime > 1000,
      severity: 'medium',
      cooldownMinutes: 10,
      enabled: true
    });

    // 内存使用告警
    this.addAlertRule({
      name: '内存使用过高告警',
      description: '内存使用率超过80%',
      condition: (metrics) => metrics.performance.memoryUsage > 80,
      severity: 'high',
      cooldownMinutes: 5,
      enabled: true
    });

    // 数据库错误告警
    this.addAlertRule({
      name: '数据库错误告警',
      description: '数据库错误数量过多',
      condition: (metrics) => metrics.errors.byType[ErrorType.DATABASE_ERROR] > 5,
      severity: 'critical',
      cooldownMinutes: 5,
      enabled: true
    });

    // 业务指标告警
    this.addAlertRule({
      name: '活跃赌约过少告警',
      description: '活跃赌约数量异常低',
      condition: (metrics) => metrics.business.activeBets < 5,
      severity: 'medium',
      cooldownMinutes: 30,
      enabled: true
    });
  }

  private startMetricsCollection() {
    // 每分钟收集一次指标
    setInterval(() => {
      this.collectMetrics();
    }, 60 * 1000);

    // 每小时清理过期数据
    setInterval(() => {
      this.cleanupOldData();
    }, 60 * 60 * 1000);
  }

  private collectMetrics() {
    const currentMetrics = this.getCurrentMetrics();
    this.metrics.push(currentMetrics);
    
    // 检查告警规则
    this.checkAlertRules();
  }

  private checkAlertRules() {
    const currentMetrics = this.getCurrentMetrics();
    
    for (const rule of this.alertRules) {
      if (!rule.enabled) continue;
      
      // 检查冷却时间
      if (rule.lastTriggered) {
        const lastTriggeredTime = new Date(rule.lastTriggered);
        const cooldownEnd = new Date(lastTriggeredTime.getTime() + rule.cooldownMinutes * 60 * 1000);
        if (new Date() < cooldownEnd) continue;
      }
      
      // 检查条件
      if (rule.condition(currentMetrics)) {
        this.triggerAlert(rule, currentMetrics);
      }
    }
  }

  private triggerAlert(rule: AlertRule, metrics: SystemMetrics) {
    const alertEvent: AlertEvent = {
      id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`,
      ruleId: rule.id,
      ruleName: rule.name,
      severity: rule.severity,
      message: `告警触发: ${rule.description}`,
      timestamp: new Date().toISOString(),
      metrics,
      resolved: false
    };
    
    this.alertEvents.push(alertEvent);
    rule.lastTriggered = alertEvent.timestamp;
    
    // 发送告警通知
    this.sendAlertNotification(alertEvent);
    
    console.error(`🚨 ALERT TRIGGERED: ${rule.name}`, {
      severity: rule.severity,
      message: alertEvent.message,
      timestamp: alertEvent.timestamp
    });
  }

  private sendAlertNotification(alert: AlertEvent) {
    // 在实际实现中，这里会发送邮件、Slack通知等
    console.log('📧 Alert Notification Sent:', {
      alertId: alert.id,
      severity: alert.severity,
      message: alert.message
    });
  }

  private generateRecommendations(metrics: SystemMetrics[], alerts: AlertEvent[]): string[] {
    const recommendations: string[] = [];
    
    if (alerts.some(a => a.severity === 'critical')) {
      recommendations.push('🚨 发现严重告警，建议立即检查系统状态');
    }
    
    const avgErrorRate = metrics.length > 0
      ? metrics.reduce((sum, m) => sum + (m.apiRequests.failed / m.apiRequests.total), 0) / metrics.length * 100
      : 0;
    
    if (avgErrorRate > 3) {
      recommendations.push('⚠️ 错误率较高，建议检查API稳定性');
    }
    
    const avgResponseTime = metrics.length > 0
      ? metrics.reduce((sum, m) => sum + m.apiRequests.averageResponseTime, 0) / metrics.length
      : 0;
    
    if (avgResponseTime > 500) {
      recommendations.push('🐌 响应时间较慢，建议优化性能');
    }
    
    if (recommendations.length === 0) {
      recommendations.push('✅ 系统运行正常，无需特别关注');
    }
    
    return recommendations;
  }

  private cleanupOldData() {
    const metricsRetentionTime = new Date(Date.now() - this.metricsRetentionHours * 60 * 60 * 1000);
    const alertRetentionTime = new Date(Date.now() - this.alertRetentionDays * 24 * 60 * 60 * 1000);
    
    this.metrics = this.metrics.filter(m => new Date(m.timestamp) > metricsRetentionTime);
    this.alertEvents = this.alertEvents.filter(a => new Date(a.timestamp) > alertRetentionTime);
    
    console.log('🧹 Old monitoring data cleaned up');
  }
}

// 导出单例实例
export const monitoringService = MonitoringService.getInstance();
