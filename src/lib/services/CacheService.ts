/**
 * 数据库查询缓存服务
 * 提供多层缓存策略和智能失效机制
 */

import { LRUCache } from 'lru-cache';

// 缓存配置
interface CacheConfig {
  maxSize: number;
  ttl: number; // 毫秒
  staleWhileRevalidate?: number; // 毫秒
}

// 缓存层级配置
const CACHE_CONFIGS = {
  // L1: 内存缓存 - 最快，容量小
  memory: {
    maxSize: 500,
    ttl: 1000 * 60 * 5, // 5分钟
    staleWhileRevalidate: 1000 * 60 * 1 // 1分钟
  },
  
  // L2: 用户数据缓存 - 中等速度，中等容量
  user: {
    maxSize: 1000,
    ttl: 1000 * 60 * 15, // 15分钟
    staleWhileRevalidate: 1000 * 60 * 5 // 5分钟
  },
  
  // L3: 静态数据缓存 - 较慢，大容量
  static: {
    maxSize: 2000,
    ttl: 1000 * 60 * 60, // 1小时
    staleWhileRevalidate: 1000 * 60 * 15 // 15分钟
  }
};

// 缓存实例
const caches = {
  memory: new LRUCache<string, any>(CACHE_CONFIGS.memory),
  user: new LRUCache<string, any>(CACHE_CONFIGS.user),
  static: new LRUCache<string, any>(CACHE_CONFIGS.static)
};

// 缓存统计
interface CacheStats {
  hits: number;
  misses: number;
  sets: number;
  deletes: number;
  hitRate: number;
}

const stats: Record<string, CacheStats> = {
  memory: { hits: 0, misses: 0, sets: 0, deletes: 0, hitRate: 0 },
  user: { hits: 0, misses: 0, sets: 0, deletes: 0, hitRate: 0 },
  static: { hits: 0, misses: 0, sets: 0, deletes: 0, hitRate: 0 }
};

/**
 * 缓存服务类
 */
export class CacheService {
  private static instance: CacheService;
  
  static getInstance(): CacheService {
    if (!CacheService.instance) {
      CacheService.instance = new CacheService();
    }
    return CacheService.instance;
  }
  
  /**
   * 获取缓存数据
   */
  async get<T>(key: string, layer: keyof typeof caches = 'memory'): Promise<T | null> {
    try {
      const cache = caches[layer];
      const data = cache.get(key);
      
      if (data !== undefined) {
        stats[layer].hits++;
        this.updateHitRate(layer);
        return data as T;
      }
      
      stats[layer].misses++;
      this.updateHitRate(layer);
      return null;
    } catch (error) {
      console.error(`Cache get error for key ${key}:`, error);
      return null;
    }
  }
  
  /**
   * 设置缓存数据
   */
  async set<T>(
    key: string, 
    value: T, 
    layer: keyof typeof caches = 'memory',
    customTtl?: number
  ): Promise<void> {
    try {
      const cache = caches[layer];
      
      if (customTtl) {
        cache.set(key, value, { ttl: customTtl });
      } else {
        cache.set(key, value);
      }
      
      stats[layer].sets++;
    } catch (error) {
      console.error(`Cache set error for key ${key}:`, error);
    }
  }
  
  /**
   * 删除缓存数据
   */
  async delete(key: string, layer: keyof typeof caches = 'memory'): Promise<void> {
    try {
      const cache = caches[layer];
      cache.delete(key);
      stats[layer].deletes++;
    } catch (error) {
      console.error(`Cache delete error for key ${key}:`, error);
    }
  }
  
  /**
   * 清除指定层级的所有缓存
   */
  async clear(layer: keyof typeof caches): Promise<void> {
    try {
      caches[layer].clear();
      stats[layer] = { hits: 0, misses: 0, sets: 0, deletes: 0, hitRate: 0 };
    } catch (error) {
      console.error(`Cache clear error for layer ${layer}:`, error);
    }
  }
  
  /**
   * 清除所有缓存
   */
  async clearAll(): Promise<void> {
    for (const layer of Object.keys(caches) as Array<keyof typeof caches>) {
      await this.clear(layer);
    }
  }
  
  /**
   * 缓存穿透保护 - 获取或设置
   */
  async getOrSet<T>(
    key: string,
    fetcher: () => Promise<T>,
    layer: keyof typeof caches = 'memory',
    customTtl?: number
  ): Promise<T> {
    // 先尝试从缓存获取
    const cached = await this.get<T>(key, layer);
    if (cached !== null) {
      return cached;
    }
    
    // 缓存未命中，执行获取函数
    try {
      const data = await fetcher();
      
      // 只缓存非空数据
      if (data !== null && data !== undefined) {
        await this.set(key, data, layer, customTtl);
      }
      
      return data;
    } catch (error) {
      console.error(`Fetcher error for key ${key}:`, error);
      throw error;
    }
  }
  
  /**
   * 批量获取缓存
   */
  async mget<T>(keys: string[], layer: keyof typeof caches = 'memory'): Promise<(T | null)[]> {
    const results: (T | null)[] = [];
    
    for (const key of keys) {
      const data = await this.get<T>(key, layer);
      results.push(data);
    }
    
    return results;
  }
  
  /**
   * 批量设置缓存
   */
  async mset<T>(
    entries: Array<{ key: string; value: T }>,
    layer: keyof typeof caches = 'memory',
    customTtl?: number
  ): Promise<void> {
    for (const { key, value } of entries) {
      await this.set(key, value, layer, customTtl);
    }
  }
  
  /**
   * 模式匹配删除
   */
  async deletePattern(pattern: string, layer: keyof typeof caches = 'memory'): Promise<number> {
    const cache = caches[layer];
    const keys = Array.from(cache.keys());
    const regex = new RegExp(pattern);
    let deletedCount = 0;
    
    for (const key of keys) {
      if (regex.test(key)) {
        cache.delete(key);
        deletedCount++;
      }
    }
    
    stats[layer].deletes += deletedCount;
    return deletedCount;
  }
  
  /**
   * 获取缓存统计信息
   */
  getStats(): Record<string, CacheStats> {
    return { ...stats };
  }
  
  /**
   * 获取缓存大小信息
   */
  getSizes(): Record<string, { size: number; maxSize: number; usage: string }> {
    const sizes: Record<string, { size: number; maxSize: number; usage: string }> = {};
    
    for (const [layer, cache] of Object.entries(caches)) {
      const size = cache.size;
      const maxSize = cache.max;
      const usage = `${((size / maxSize) * 100).toFixed(1)}%`;
      
      sizes[layer] = { size, maxSize, usage };
    }
    
    return sizes;
  }
  
  /**
   * 更新命中率
   */
  private updateHitRate(layer: keyof typeof caches): void {
    const stat = stats[layer];
    const total = stat.hits + stat.misses;
    stat.hitRate = total > 0 ? (stat.hits / total) * 100 : 0;
  }
}

/**
 * 数据库查询缓存装饰器
 */
export function cached<T extends any[], R>(
  keyGenerator: (...args: T) => string,
  layer: keyof typeof caches = 'memory',
  ttl?: number
) {
  return function (
    target: any,
    propertyName: string,
    descriptor: PropertyDescriptor
  ) {
    const method = descriptor.value;
    
    descriptor.value = async function (...args: T): Promise<R> {
      const cache = CacheService.getInstance();
      const key = keyGenerator(...args);
      
      // 尝试从缓存获取
      const cached = await cache.get<R>(key, layer);
      if (cached !== null) {
        return cached;
      }
      
      // 执行原方法
      const result = await method.apply(this, args);
      
      // 缓存结果
      if (result !== null && result !== undefined) {
        await cache.set(key, result, layer, ttl);
      }
      
      return result;
    };
    
    return descriptor;
  };
}

/**
 * 常用缓存键生成器
 */
export const CacheKeys = {
  user: (userId: string) => `user:${userId}`,
  userProfile: (userId: string) => `user:profile:${userId}`,
  userFortune: (userId: string) => `user:fortune:${userId}`,
  userCertification: (userId: string) => `user:cert:${userId}`,
  socialBet: (betId: string) => `bet:${betId}`,
  socialBetList: (page: number, limit: number, filter?: string) => 
    `bet:list:${page}:${limit}:${filter || 'all'}`,
  judgment: (judgmentId: string) => `judgment:${judgmentId}`,
  leaderboard: (type: string, period: string) => `leaderboard:${type}:${period}`,
  i18n: (locale: string, namespace: string) => `i18n:${locale}:${namespace}`
};

// 导出单例实例
export const cacheService = CacheService.getInstance();
