/**
 * 增强版监控服务
 * 提供实时监控、异常检测、告警通知等功能
 */

import { EventEmitter } from 'events';
import { monitoringService as baseMonitoring } from './MonitoringService';

// 实时监控数据接口
export interface RealTimeMetrics {
  timestamp: number;
  system: {
    uptime: number;
    memory: { used: number; total: number; percentage: number };
    cpu: { usage: number; loadAverage: number[] };
    disk: { used: number; total: number; percentage: number };
  };
  application: {
    activeUsers: number;
    requestsPerSecond: number;
    responseTime: number;
    errorRate: number;
    cacheHitRate: number;
  };
  business: {
    activeBets: number;
    pendingJudgments: number;
    dailyRevenue: number;
    fortuneCirculation: number;
    userGrowthRate: number;
  };
  security: {
    failedLogins: number;
    suspiciousActivities: number;
    blockedIPs: number;
  };
}

// 异常检测结果
export interface AnomalyDetection {
  id: string;
  type: 'transaction' | 'user_behavior' | 'system' | 'security';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  data: any;
  timestamp: number;
  resolved: boolean;
}

// 告警通知配置
export interface NotificationConfig {
  id: string;
  name: string;
  channels: ('email' | 'slack' | 'webhook' | 'sms')[];
  conditions: {
    severity: ('low' | 'medium' | 'high' | 'critical')[];
    types: string[];
  };
  enabled: boolean;
  recipients: string[];
}

/**
 * 增强版监控服务
 */
export class EnhancedMonitoringService extends EventEmitter {
  private static instance: EnhancedMonitoringService;
  private realTimeMetrics: RealTimeMetrics[] = [];
  private anomalies: AnomalyDetection[] = [];
  private notifications: Map<string, NotificationConfig> = new Map();
  private isRunning = false;
  private metricsInterval?: NodeJS.Timeout;
  private anomalyInterval?: NodeJS.Timeout;

  static getInstance(): EnhancedMonitoringService {
    if (!EnhancedMonitoringService.instance) {
      EnhancedMonitoringService.instance = new EnhancedMonitoringService();
    }
    return EnhancedMonitoringService.instance;
  }

  /**
   * 启动增强监控
   */
  start(): void {
    if (this.isRunning) return;

    this.isRunning = true;
    this.initializeNotifications();

    // 每30秒收集一次实时指标
    this.metricsInterval = setInterval(() => {
      this.collectRealTimeMetrics();
    }, 30000);

    // 每分钟进行一次异常检测
    this.anomalyInterval = setInterval(() => {
      this.performAnomalyDetection();
    }, 60000);

    console.log('Enhanced monitoring service started');
  }

  /**
   * 停止监控
   */
  stop(): void {
    if (!this.isRunning) return;

    this.isRunning = false;
    
    if (this.metricsInterval) {
      clearInterval(this.metricsInterval);
    }
    
    if (this.anomalyInterval) {
      clearInterval(this.anomalyInterval);
    }

    console.log('Enhanced monitoring service stopped');
  }

  /**
   * 收集实时指标
   */
  private async collectRealTimeMetrics(): Promise<void> {
    try {
      const metrics = await this.gatherRealTimeMetrics();
      this.realTimeMetrics.push(metrics);

      // 保留最近24小时的数据（2880条记录，每30秒一条）
      if (this.realTimeMetrics.length > 2880) {
        this.realTimeMetrics = this.realTimeMetrics.slice(-2880);
      }

      this.emit('realtime-metrics', metrics);
    } catch (error) {
      console.error('Failed to collect real-time metrics:', error);
    }
  }

  /**
   * 收集实时指标数据
   */
  private async gatherRealTimeMetrics(): Promise<RealTimeMetrics> {
    const timestamp = Date.now();

    // 系统指标
    const memoryUsage = process.memoryUsage();
    const system = {
      uptime: process.uptime(),
      memory: {
        used: memoryUsage.heapUsed,
        total: memoryUsage.heapTotal,
        percentage: (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100
      },
      cpu: {
        usage: await this.getCPUUsage(),
        loadAverage: [0.5, 0.7, 0.8] // 实际应用中使用os.loadavg()
      },
      disk: {
        used: 50 * 1024 * 1024 * 1024, // 50GB (模拟)
        total: 100 * 1024 * 1024 * 1024, // 100GB (模拟)
        percentage: 50
      }
    };

    // 应用指标
    const application = {
      activeUsers: await this.getActiveUsers(),
      requestsPerSecond: await this.getRequestsPerSecond(),
      responseTime: await this.getAverageResponseTime(),
      errorRate: await this.getErrorRate(),
      cacheHitRate: await this.getCacheHitRate()
    };

    // 业务指标
    const business = {
      activeBets: await this.getActiveBets(),
      pendingJudgments: await this.getPendingJudgments(),
      dailyRevenue: await this.getDailyRevenue(),
      fortuneCirculation: await this.getFortuneCirculation(),
      userGrowthRate: await this.getUserGrowthRate()
    };

    // 安全指标
    const security = {
      failedLogins: await this.getFailedLogins(),
      suspiciousActivities: await this.getSuspiciousActivities(),
      blockedIPs: await this.getBlockedIPs()
    };

    return {
      timestamp,
      system,
      application,
      business,
      security
    };
  }

  /**
   * 执行异常检测
   */
  private async performAnomalyDetection(): Promise<void> {
    try {
      const detections = await this.detectAnomalies();
      
      for (const detection of detections) {
        this.anomalies.push(detection);
        this.emit('anomaly', detection);
        
        // 发送告警通知
        await this.sendNotifications(detection);
      }

      // 保留最近1000条异常记录
      if (this.anomalies.length > 1000) {
        this.anomalies = this.anomalies.slice(-1000);
      }
    } catch (error) {
      console.error('Failed to perform anomaly detection:', error);
    }
  }

  /**
   * 检测异常
   */
  private async detectAnomalies(): Promise<AnomalyDetection[]> {
    const anomalies: AnomalyDetection[] = [];
    const latest = this.getLatestMetrics();
    
    if (!latest) return anomalies;

    // 检测系统异常
    if (latest.system.memory.percentage > 90) {
      anomalies.push({
        id: `anomaly_${Date.now()}_memory`,
        type: 'system',
        severity: 'critical',
        description: `内存使用率过高: ${latest.system.memory.percentage.toFixed(1)}%`,
        data: { memoryUsage: latest.system.memory },
        timestamp: Date.now(),
        resolved: false
      });
    }

    // 检测应用异常
    if (latest.application.errorRate > 10) {
      anomalies.push({
        id: `anomaly_${Date.now()}_error_rate`,
        type: 'system',
        severity: 'high',
        description: `错误率过高: ${latest.application.errorRate.toFixed(1)}%`,
        data: { errorRate: latest.application.errorRate },
        timestamp: Date.now(),
        resolved: false
      });
    }

    // 检测业务异常
    if (latest.business.activeBets === 0) {
      anomalies.push({
        id: `anomaly_${Date.now()}_no_bets`,
        type: 'transaction',
        severity: 'medium',
        description: '当前没有活跃的赌约',
        data: { activeBets: latest.business.activeBets },
        timestamp: Date.now(),
        resolved: false
      });
    }

    // 检测安全异常
    if (latest.security.failedLogins > 100) {
      anomalies.push({
        id: `anomaly_${Date.now()}_failed_logins`,
        type: 'security',
        severity: 'high',
        description: `登录失败次数过多: ${latest.security.failedLogins}`,
        data: { failedLogins: latest.security.failedLogins },
        timestamp: Date.now(),
        resolved: false
      });
    }

    return anomalies;
  }

  /**
   * 发送告警通知
   */
  private async sendNotifications(anomaly: AnomalyDetection): Promise<void> {
    for (const config of this.notifications.values()) {
      if (!config.enabled) continue;
      
      // 检查条件匹配
      if (!config.conditions.severity.includes(anomaly.severity)) continue;
      if (!config.conditions.types.includes(anomaly.type)) continue;

      // 发送通知
      for (const channel of config.channels) {
        await this.sendNotification(channel, anomaly, config);
      }
    }
  }

  /**
   * 发送单个通知
   */
  private async sendNotification(
    channel: string,
    anomaly: AnomalyDetection,
    config: NotificationConfig
  ): Promise<void> {
    try {
      switch (channel) {
        case 'email':
          await this.sendEmailNotification(anomaly, config);
          break;
        case 'slack':
          await this.sendSlackNotification(anomaly, config);
          break;
        case 'webhook':
          await this.sendWebhookNotification(anomaly, config);
          break;
        case 'sms':
          await this.sendSMSNotification(anomaly, config);
          break;
      }
    } catch (error) {
      console.error(`Failed to send ${channel} notification:`, error);
    }
  }

  /**
   * 初始化通知配置
   */
  private initializeNotifications(): void {
    const defaultConfig: NotificationConfig = {
      id: 'default',
      name: '默认告警通知',
      channels: ['email', 'slack'],
      conditions: {
        severity: ['high', 'critical'],
        types: ['system', 'security', 'transaction']
      },
      enabled: true,
      recipients: ['<EMAIL>']
    };

    this.notifications.set(defaultConfig.id, defaultConfig);
  }

  // 模拟的指标获取方法
  private async getCPUUsage(): Promise<number> { return Math.random() * 100; }
  private async getActiveUsers(): Promise<number> { return Math.floor(Math.random() * 1000); }
  private async getRequestsPerSecond(): Promise<number> { return Math.floor(Math.random() * 100); }
  private async getAverageResponseTime(): Promise<number> { return Math.random() * 1000; }
  private async getErrorRate(): Promise<number> { return Math.random() * 5; }
  private async getCacheHitRate(): Promise<number> { return 80 + Math.random() * 20; }
  private async getActiveBets(): Promise<number> { return Math.floor(Math.random() * 50); }
  private async getPendingJudgments(): Promise<number> { return Math.floor(Math.random() * 20); }
  private async getDailyRevenue(): Promise<number> { return Math.random() * 10000; }
  private async getFortuneCirculation(): Promise<number> { return Math.random() * 1000000; }
  private async getUserGrowthRate(): Promise<number> { return Math.random() * 10; }
  private async getFailedLogins(): Promise<number> { return Math.floor(Math.random() * 50); }
  private async getSuspiciousActivities(): Promise<number> { return Math.floor(Math.random() * 10); }
  private async getBlockedIPs(): Promise<number> { return Math.floor(Math.random() * 20); }

  // 模拟的通知发送方法
  private async sendEmailNotification(anomaly: AnomalyDetection, config: NotificationConfig): Promise<void> {
    console.log(`📧 Email notification sent: ${anomaly.description}`);
  }

  private async sendSlackNotification(anomaly: AnomalyDetection, config: NotificationConfig): Promise<void> {
    console.log(`💬 Slack notification sent: ${anomaly.description}`);
  }

  private async sendWebhookNotification(anomaly: AnomalyDetection, config: NotificationConfig): Promise<void> {
    console.log(`🔗 Webhook notification sent: ${anomaly.description}`);
  }

  private async sendSMSNotification(anomaly: AnomalyDetection, config: NotificationConfig): Promise<void> {
    console.log(`📱 SMS notification sent: ${anomaly.description}`);
  }

  // 公共方法
  getLatestMetrics(): RealTimeMetrics | null {
    return this.realTimeMetrics.length > 0 ? this.realTimeMetrics[this.realTimeMetrics.length - 1] : null;
  }

  getHistoricalMetrics(hours: number = 1): RealTimeMetrics[] {
    const limit = hours * 120; // 每小时120条记录（每30秒一条）
    return this.realTimeMetrics.slice(-limit);
  }

  getRecentAnomalies(limit: number = 50): AnomalyDetection[] {
    return this.anomalies.slice(-limit);
  }

  addNotificationConfig(config: NotificationConfig): void {
    this.notifications.set(config.id, config);
  }

  removeNotificationConfig(id: string): void {
    this.notifications.delete(id);
  }
}

// 导出单例实例
export const enhancedMonitoring = EnhancedMonitoringService.getInstance();
