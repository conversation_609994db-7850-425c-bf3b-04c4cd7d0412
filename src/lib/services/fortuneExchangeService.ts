import { supabase } from '@/lib/supabase';
import { haoxService } from '@/lib/blockchain/haoxService';
import { ethers } from 'ethers';

// 充值提现配置
export const EXCHANGE_CONFIG = {
  // 汇率配置
  EXCHANGE_RATE: 1, // 1 HAOX = 1 福气
  
  // 充值配置
  MIN_DEPOSIT: 10, // 最小充值10福气
  MAX_DEPOSIT: 1000000, // 最大充值100万福气
  
  // 提现配置
  MIN_WITHDRAWAL: 100, // 最小提现100福气
  MAX_WITHDRAWAL: 100000, // 最大提现10万福气
  DAILY_WITHDRAWAL_LIMIT: 500000, // 每日提现限额50万福气
  
  // 手续费配置
  DEPOSIT_FEE_RATE: 0, // 充值手续费0%
  WITHDRAWAL_FEE_RATE: 0.01, // 提现手续费1%
  MIN_WITHDRAWAL_FEE: 1, // 最小提现手续费1福气
  
  // 处理时间配置
  SMALL_AMOUNT_THRESHOLD: 10000, // 小额阈值1万福气
  SMALL_AMOUNT_PROCESS_TIME: 5 * 60 * 1000, // 小额处理时间5分钟
  LARGE_AMOUNT_PROCESS_TIME: 24 * 60 * 60 * 1000, // 大额处理时间24小时
};

export interface ExchangeTransaction {
  id: string;
  userId: string;
  type: 'deposit' | 'withdrawal';
  amount: number;
  fee: number;
  netAmount: number;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  txHash?: string;
  walletAddress?: string;
  processedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface DailyWithdrawalLimit {
  userId: string;
  date: string;
  totalWithdrawn: number;
  remainingLimit: number;
}

export class FortuneExchangeService {
  /**
   * 充值福气（HAOX -> 福气）
   */
  async deposit(userId: string, amount: number, txHash: string, walletAddress: string): Promise<ExchangeTransaction> {
    try {
      // 验证充值金额
      if (amount < EXCHANGE_CONFIG.MIN_DEPOSIT || amount > EXCHANGE_CONFIG.MAX_DEPOSIT) {
        throw new Error(`充值金额必须在${EXCHANGE_CONFIG.MIN_DEPOSIT}-${EXCHANGE_CONFIG.MAX_DEPOSIT}福气之间`);
      }

      // 验证交易哈希格式
      if (!this.isValidTxHash(txHash)) {
        throw new Error('无效的交易哈希格式');
      }

      // 检查交易是否已存在
      const existingTx = await this.getTransactionByTxHash(txHash);
      if (existingTx) {
        throw new Error('该交易已经处理过了');
      }

      // 验证区块链交易（开发环境跳过）
      if (process.env.NODE_ENV !== 'development') {
        const isValid = await this.verifyBlockchainTransaction(txHash, walletAddress, amount);
        if (!isValid) {
          throw new Error('区块链交易验证失败');
        }
      }

      // 计算手续费和净额
      const fee = Math.max(amount * EXCHANGE_CONFIG.DEPOSIT_FEE_RATE, 0);
      const netAmount = amount - fee;

      // 创建充值记录
      const transaction = await this.createTransaction({
        userId,
        type: 'deposit',
        amount,
        fee,
        netAmount,
        status: 'processing',
        txHash,
        walletAddress
      });

      // 立即处理充值（充值通常是即时的）
      await this.processDeposit(transaction.id, netAmount, userId);

      return transaction;
    } catch (error) {
      console.error('充值失败:', error);
      throw error;
    }
  }

  /**
   * 提现福气（福气 -> HAOX）
   */
  async withdraw(userId: string, amount: number, walletAddress: string): Promise<ExchangeTransaction> {
    try {
      // 验证提现金额
      if (amount < EXCHANGE_CONFIG.MIN_WITHDRAWAL || amount > EXCHANGE_CONFIG.MAX_WITHDRAWAL) {
        throw new Error(`提现金额必须在${EXCHANGE_CONFIG.MIN_WITHDRAWAL}-${EXCHANGE_CONFIG.MAX_WITHDRAWAL}福气之间`);
      }

      // 验证钱包地址
      if (!haoxService.isValidAddress(walletAddress)) {
        throw new Error('无效的钱包地址格式');
      }

      // 检查每日提现限额
      await this.checkDailyWithdrawalLimit(userId, amount);

      // 检查用户福气余额
      const userBalance = await this.getUserFortuneBalance(userId);
      if (userBalance < amount) {
        throw new Error('福气余额不足');
      }

      // 计算手续费和净额
      const fee = Math.max(amount * EXCHANGE_CONFIG.WITHDRAWAL_FEE_RATE, EXCHANGE_CONFIG.MIN_WITHDRAWAL_FEE);
      const netAmount = amount - fee;

      // 创建提现记录
      const transaction = await this.createTransaction({
        userId,
        type: 'withdrawal',
        amount,
        fee,
        netAmount,
        status: 'pending',
        walletAddress
      });

      // 冻结用户福气
      await this.freezeUserFortune(userId, amount);

      // 根据金额大小决定处理方式
      if (amount <= EXCHANGE_CONFIG.SMALL_AMOUNT_THRESHOLD) {
        // 小额提现：5分钟后自动处理
        setTimeout(() => {
          this.processWithdrawal(transaction.id, netAmount, walletAddress);
        }, EXCHANGE_CONFIG.SMALL_AMOUNT_PROCESS_TIME);
      } else {
        // 大额提现：需要人工审核，24小时后处理
        await this.scheduleManualReview(transaction.id);
      }

      return transaction;
    } catch (error) {
      console.error('提现失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户交易历史
   */
  async getUserTransactions(userId: string, page: number = 1, limit: number = 20): Promise<{
    transactions: ExchangeTransaction[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    try {
      const offset = (page - 1) * limit;

      const { data: transactions, count } = await supabase
        .from('fortune_exchange_transactions')
        .select('*', { count: 'exact' })
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      return {
        transactions: transactions || [],
        total: count || 0,
        page,
        totalPages: Math.ceil((count || 0) / limit)
      };
    } catch (error) {
      console.error('获取交易历史失败:', error);
      // 开发环境返回模拟数据
      if (process.env.NODE_ENV === 'development') {
        return this.getMockTransactions(userId, page, limit);
      }
      throw error;
    }
  }

  /**
   * 获取用户每日提现限额信息
   */
  async getDailyWithdrawalLimit(userId: string): Promise<DailyWithdrawalLimit> {
    try {
      const today = new Date().toISOString().split('T')[0];
      
      const { data: withdrawals } = await supabase
        .from('fortune_exchange_transactions')
        .select('amount')
        .eq('user_id', userId)
        .eq('type', 'withdrawal')
        .gte('created_at', `${today}T00:00:00.000Z`)
        .lt('created_at', `${today}T23:59:59.999Z`)
        .in('status', ['completed', 'processing']);

      const totalWithdrawn = withdrawals?.reduce((sum, tx) => sum + tx.amount, 0) || 0;
      const remainingLimit = Math.max(0, EXCHANGE_CONFIG.DAILY_WITHDRAWAL_LIMIT - totalWithdrawn);

      return {
        userId,
        date: today,
        totalWithdrawn,
        remainingLimit
      };
    } catch (error) {
      console.error('获取每日提现限额失败:', error);
      return {
        userId,
        date: new Date().toISOString().split('T')[0],
        totalWithdrawn: 0,
        remainingLimit: EXCHANGE_CONFIG.DAILY_WITHDRAWAL_LIMIT
      };
    }
  }

  // 私有方法

  private async createTransaction(data: Partial<ExchangeTransaction>): Promise<ExchangeTransaction> {
    try {
      const transactionData = {
        user_id: data.userId,
        type: data.type,
        amount: data.amount,
        fee: data.fee,
        net_amount: data.netAmount,
        status: data.status,
        tx_hash: data.txHash,
        wallet_address: data.walletAddress,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { data: transaction, error } = await supabase
        .from('fortune_exchange_transactions')
        .insert(transactionData)
        .select()
        .single();

      if (error) throw error;

      return this.formatTransaction(transaction);
    } catch (error) {
      console.error('创建交易记录失败:', error);
      // 开发环境返回模拟数据
      if (process.env.NODE_ENV === 'development') {
        return {
          id: `mock-${Date.now()}`,
          userId: data.userId!,
          type: data.type!,
          amount: data.amount!,
          fee: data.fee!,
          netAmount: data.netAmount!,
          status: data.status!,
          txHash: data.txHash,
          walletAddress: data.walletAddress,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
      }
      throw error;
    }
  }

  private async processDeposit(transactionId: string, amount: number, userId: string): Promise<void> {
    try {
      // 增加用户福气余额
      await this.addUserFortune(userId, amount);

      // 更新交易状态
      await this.updateTransactionStatus(transactionId, 'completed');

      console.log(`充值处理完成: 用户${userId}充值${amount}福气`);
    } catch (error) {
      console.error('处理充值失败:', error);
      await this.updateTransactionStatus(transactionId, 'failed');
      throw error;
    }
  }

  private async processWithdrawal(transactionId: string, amount: number, walletAddress: string): Promise<void> {
    try {
      // 发送HAOX到用户钱包（开发环境模拟）
      let txHash = '';
      if (process.env.NODE_ENV === 'development') {
        txHash = `0x${Math.random().toString(16).substr(2, 64)}`;
        console.log(`模拟发送${amount}HAOX到${walletAddress}，交易哈希：${txHash}`);
      } else {
        txHash = await this.sendHAOXToWallet(walletAddress, amount);
      }

      // 更新交易状态和哈希
      await this.updateTransaction(transactionId, {
        status: 'completed',
        txHash,
        processedAt: new Date().toISOString()
      });

      console.log(`提现处理完成: 发送${amount}HAOX到${walletAddress}`);
    } catch (error) {
      console.error('处理提现失败:', error);
      await this.updateTransactionStatus(transactionId, 'failed');
      // 解冻用户福气
      const transaction = await this.getTransactionById(transactionId);
      if (transaction) {
        await this.unfreezeUserFortune(transaction.userId, transaction.amount);
      }
      throw error;
    }
  }

  private async checkDailyWithdrawalLimit(userId: string, amount: number): Promise<void> {
    const limitInfo = await this.getDailyWithdrawalLimit(userId);
    if (amount > limitInfo.remainingLimit) {
      throw new Error(`超出每日提现限额，今日剩余额度：${limitInfo.remainingLimit}福气`);
    }
  }

  private async getUserFortuneBalance(userId: string): Promise<number> {
    // 这里应该调用福气系统的余额查询API
    // 暂时返回模拟数据
    return 100000; // 模拟10万福气余额
  }

  private async addUserFortune(userId: string, amount: number): Promise<void> {
    // 这里应该调用福气系统的余额增加API
    console.log(`为用户${userId}增加${amount}福气`);
  }

  private async freezeUserFortune(userId: string, amount: number): Promise<void> {
    // 这里应该调用福气系统的余额冻结API
    console.log(`为用户${userId}冻结${amount}福气`);
  }

  private async unfreezeUserFortune(userId: string, amount: number): Promise<void> {
    // 这里应该调用福气系统的余额解冻API
    console.log(`为用户${userId}解冻${amount}福气`);
  }

  private isValidTxHash(txHash: string): boolean {
    return /^0x[a-fA-F0-9]{64}$/.test(txHash);
  }

  private async verifyBlockchainTransaction(txHash: string, walletAddress: string, amount: number): Promise<boolean> {
    // 这里应该实现真实的区块链交易验证
    // 暂时返回true
    return true;
  }

  private async sendHAOXToWallet(walletAddress: string, amount: number): Promise<string> {
    // 这里应该实现真实的HAOX转账
    // 暂时返回模拟交易哈希
    return `0x${Math.random().toString(16).substr(2, 64)}`;
  }

  private async getTransactionByTxHash(txHash: string): Promise<ExchangeTransaction | null> {
    try {
      const { data } = await supabase
        .from('fortune_exchange_transactions')
        .select('*')
        .eq('tx_hash', txHash)
        .single();

      return data ? this.formatTransaction(data) : null;
    } catch (error) {
      return null;
    }
  }

  private async getTransactionById(id: string): Promise<ExchangeTransaction | null> {
    try {
      const { data } = await supabase
        .from('fortune_exchange_transactions')
        .select('*')
        .eq('id', id)
        .single();

      return data ? this.formatTransaction(data) : null;
    } catch (error) {
      return null;
    }
  }

  private async updateTransactionStatus(id: string, status: string): Promise<void> {
    await this.updateTransaction(id, { status });
  }

  private async updateTransaction(id: string, updates: Partial<ExchangeTransaction>): Promise<void> {
    try {
      const updateData = {
        ...updates,
        updated_at: new Date().toISOString()
      };

      await supabase
        .from('fortune_exchange_transactions')
        .update(updateData)
        .eq('id', id);
    } catch (error) {
      console.error('更新交易记录失败:', error);
    }
  }

  private async scheduleManualReview(transactionId: string): Promise<void> {
    // 这里应该实现人工审核调度逻辑
    console.log(`大额提现${transactionId}已提交人工审核`);
  }

  private formatTransaction(data: any): ExchangeTransaction {
    return {
      id: data.id,
      userId: data.user_id,
      type: data.type,
      amount: data.amount,
      fee: data.fee,
      netAmount: data.net_amount,
      status: data.status,
      txHash: data.tx_hash,
      walletAddress: data.wallet_address,
      processedAt: data.processed_at,
      createdAt: data.created_at,
      updatedAt: data.updated_at
    };
  }

  private getMockTransactions(userId: string, page: number, limit: number) {
    const mockTransactions: ExchangeTransaction[] = [
      {
        id: 'tx-1',
        userId,
        type: 'deposit',
        amount: 10000,
        fee: 0,
        netAmount: 10000,
        status: 'completed',
        txHash: '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef',
        walletAddress: '******************************************',
        processedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
      },
      {
        id: 'tx-2',
        userId,
        type: 'withdrawal',
        amount: 5000,
        fee: 50,
        netAmount: 4950,
        status: 'processing',
        walletAddress: '******************************************',
        createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 30 * 60 * 1000).toISOString()
      }
    ];

    return {
      transactions: mockTransactions,
      total: mockTransactions.length,
      page,
      totalPages: 1
    };
  }
}

// 导出单例实例
export const fortuneExchangeService = new FortuneExchangeService();
