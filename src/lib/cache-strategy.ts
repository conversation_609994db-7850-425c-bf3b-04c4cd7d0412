/**
 * 缓存策略配置和管理
 * 提供多层缓存解决方案
 */

// 缓存类型定义
export type CacheType = 'memory' | 'localStorage' | 'sessionStorage' | 'indexedDB';

// 缓存配置
export interface CacheConfig {
  type: CacheType;
  ttl?: number; // 生存时间（毫秒）
  maxSize?: number; // 最大缓存大小
  version?: string; // 缓存版本
  compress?: boolean; // 是否压缩
}

// 缓存项
export interface CacheItem<T = any> {
  data: T;
  timestamp: number;
  ttl?: number;
  version?: string;
  size?: number;
}

/**
 * 内存缓存管理器
 */
export class MemoryCache {
  private cache = new Map<string, CacheItem>();
  private maxSize: number;
  private currentSize = 0;

  constructor(maxSize = 100) {
    this.maxSize = maxSize;
  }

  set<T>(key: string, data: T, ttl?: number): void {
    // 清理过期项
    this.cleanup();

    // 如果缓存已满，删除最旧的项
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      if (firstKey) {
        this.delete(firstKey);
      }
    }

    const item: CacheItem<T> = {
      data,
      timestamp: Date.now(),
      ttl,
      size: this.calculateSize(data),
    };

    this.cache.set(key, item);
    this.currentSize += item.size || 0;
  }

  get<T>(key: string): T | null {
    const item = this.cache.get(key);
    
    if (!item) {
      return null;
    }

    // 检查是否过期
    if (item.ttl && Date.now() - item.timestamp > item.ttl) {
      this.delete(key);
      return null;
    }

    return item.data as T;
  }

  delete(key: string): boolean {
    const item = this.cache.get(key);
    if (item) {
      this.currentSize -= item.size || 0;
      return this.cache.delete(key);
    }
    return false;
  }

  clear(): void {
    this.cache.clear();
    this.currentSize = 0;
  }

  has(key: string): boolean {
    return this.cache.has(key) && this.get(key) !== null;
  }

  size(): number {
    return this.cache.size;
  }

  private cleanup(): void {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (item.ttl && now - item.timestamp > item.ttl) {
        this.delete(key);
      }
    }
  }

  private calculateSize(data: any): number {
    try {
      return JSON.stringify(data).length;
    } catch {
      return 1;
    }
  }
}

/**
 * 浏览器存储缓存管理器
 */
export class BrowserStorageCache {
  private storage: Storage;
  private prefix: string;

  constructor(type: 'localStorage' | 'sessionStorage', prefix = 'cache_') {
    this.storage = type === 'localStorage' ? localStorage : sessionStorage;
    this.prefix = prefix;
  }

  set<T>(key: string, data: T, ttl?: number, version?: string): void {
    const item: CacheItem<T> = {
      data,
      timestamp: Date.now(),
      ttl,
      version,
    };

    try {
      this.storage.setItem(this.prefix + key, JSON.stringify(item));
    } catch (error) {
      console.warn('Failed to set cache item:', error);
      // 如果存储空间不足，清理过期项后重试
      this.cleanup();
      try {
        this.storage.setItem(this.prefix + key, JSON.stringify(item));
      } catch {
        console.error('Cache storage is full');
      }
    }
  }

  get<T>(key: string, version?: string): T | null {
    try {
      const itemStr = this.storage.getItem(this.prefix + key);
      if (!itemStr) {
        return null;
      }

      const item: CacheItem<T> = JSON.parse(itemStr);

      // 检查版本
      if (version && item.version !== version) {
        this.delete(key);
        return null;
      }

      // 检查是否过期
      if (item.ttl && Date.now() - item.timestamp > item.ttl) {
        this.delete(key);
        return null;
      }

      return item.data;
    } catch (error) {
      console.warn('Failed to get cache item:', error);
      return null;
    }
  }

  delete(key: string): void {
    this.storage.removeItem(this.prefix + key);
  }

  clear(): void {
    const keys = Object.keys(this.storage).filter(key => 
      key.startsWith(this.prefix)
    );
    keys.forEach(key => this.storage.removeItem(key));
  }

  has(key: string): boolean {
    return this.get(key) !== null;
  }

  private cleanup(): void {
    const keys = Object.keys(this.storage).filter(key => 
      key.startsWith(this.prefix)
    );
    
    const now = Date.now();
    keys.forEach(key => {
      try {
        const itemStr = this.storage.getItem(key);
        if (itemStr) {
          const item: CacheItem = JSON.parse(itemStr);
          if (item.ttl && now - item.timestamp > item.ttl) {
            this.storage.removeItem(key);
          }
        }
      } catch {
        // 删除损坏的缓存项
        this.storage.removeItem(key);
      }
    });
  }
}

/**
 * IndexedDB 缓存管理器
 */
export class IndexedDBCache {
  private dbName: string;
  private storeName: string;
  private version: number;
  private db: IDBDatabase | null = null;

  constructor(dbName = 'AppCache', storeName = 'cache', version = 1) {
    this.dbName = dbName;
    this.storeName = storeName;
    this.version = version;
  }

  private async openDB(): Promise<IDBDatabase> {
    if (this.db) {
      return this.db;
    }

    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        resolve(this.db);
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        if (!db.objectStoreNames.contains(this.storeName)) {
          db.createObjectStore(this.storeName, { keyPath: 'key' });
        }
      };
    });
  }

  async set<T>(key: string, data: T, ttl?: number): Promise<void> {
    const db = await this.openDB();
    const transaction = db.transaction([this.storeName], 'readwrite');
    const store = transaction.objectStore(this.storeName);

    const item = {
      key,
      data,
      timestamp: Date.now(),
      ttl,
    };

    return new Promise((resolve, reject) => {
      const request = store.put(item);
      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve();
    });
  }

  async get<T>(key: string): Promise<T | null> {
    const db = await this.openDB();
    const transaction = db.transaction([this.storeName], 'readonly');
    const store = transaction.objectStore(this.storeName);

    return new Promise((resolve, reject) => {
      const request = store.get(key);
      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        const item = request.result;
        
        if (!item) {
          resolve(null);
          return;
        }

        // 检查是否过期
        if (item.ttl && Date.now() - item.timestamp > item.ttl) {
          this.delete(key);
          resolve(null);
          return;
        }

        resolve(item.data);
      };
    });
  }

  async delete(key: string): Promise<void> {
    const db = await this.openDB();
    const transaction = db.transaction([this.storeName], 'readwrite');
    const store = transaction.objectStore(this.storeName);

    return new Promise((resolve, reject) => {
      const request = store.delete(key);
      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve();
    });
  }

  async clear(): Promise<void> {
    const db = await this.openDB();
    const transaction = db.transaction([this.storeName], 'readwrite');
    const store = transaction.objectStore(this.storeName);

    return new Promise((resolve, reject) => {
      const request = store.clear();
      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve();
    });
  }
}

/**
 * 统一缓存管理器
 */
export class CacheManager {
  private memoryCache: MemoryCache;
  private localStorageCache: BrowserStorageCache;
  private sessionStorageCache: BrowserStorageCache;
  private indexedDBCache: IndexedDBCache;

  constructor() {
    this.memoryCache = new MemoryCache(100);
    this.localStorageCache = new BrowserStorageCache('localStorage');
    this.sessionStorageCache = new BrowserStorageCache('sessionStorage');
    this.indexedDBCache = new IndexedDBCache();
  }

  async set<T>(
    key: string, 
    data: T, 
    config: CacheConfig = { type: 'memory' }
  ): Promise<void> {
    const { type, ttl, version } = config;

    switch (type) {
      case 'memory':
        this.memoryCache.set(key, data, ttl);
        break;
      case 'localStorage':
        this.localStorageCache.set(key, data, ttl, version);
        break;
      case 'sessionStorage':
        this.sessionStorageCache.set(key, data, ttl, version);
        break;
      case 'indexedDB':
        await this.indexedDBCache.set(key, data, ttl);
        break;
    }
  }

  async get<T>(
    key: string, 
    config: CacheConfig = { type: 'memory' }
  ): Promise<T | null> {
    const { type, version } = config;

    switch (type) {
      case 'memory':
        return this.memoryCache.get<T>(key);
      case 'localStorage':
        return this.localStorageCache.get<T>(key, version);
      case 'sessionStorage':
        return this.sessionStorageCache.get<T>(key, version);
      case 'indexedDB':
        return await this.indexedDBCache.get<T>(key);
      default:
        return null;
    }
  }

  async delete(key: string, type: CacheType = 'memory'): Promise<void> {
    switch (type) {
      case 'memory':
        this.memoryCache.delete(key);
        break;
      case 'localStorage':
        this.localStorageCache.delete(key);
        break;
      case 'sessionStorage':
        this.sessionStorageCache.delete(key);
        break;
      case 'indexedDB':
        await this.indexedDBCache.delete(key);
        break;
    }
  }

  async clear(type?: CacheType): Promise<void> {
    if (type) {
      switch (type) {
        case 'memory':
          this.memoryCache.clear();
          break;
        case 'localStorage':
          this.localStorageCache.clear();
          break;
        case 'sessionStorage':
          this.sessionStorageCache.clear();
          break;
        case 'indexedDB':
          await this.indexedDBCache.clear();
          break;
      }
    } else {
      // 清理所有缓存
      this.memoryCache.clear();
      this.localStorageCache.clear();
      this.sessionStorageCache.clear();
      await this.indexedDBCache.clear();
    }
  }
}

// 缓存策略配置
export const CACHE_STRATEGIES = {
  // API 响应缓存
  api: {
    type: 'memory' as CacheType,
    ttl: 5 * 60 * 1000, // 5分钟
  },
  
  // 用户数据缓存
  user: {
    type: 'localStorage' as CacheType,
    ttl: 24 * 60 * 60 * 1000, // 24小时
  },
  
  // 静态资源缓存
  static: {
    type: 'localStorage' as CacheType,
    ttl: 7 * 24 * 60 * 60 * 1000, // 7天
  },
  
  // 会话数据缓存
  session: {
    type: 'sessionStorage' as CacheType,
  },
  
  // 大型数据缓存
  large: {
    type: 'indexedDB' as CacheType,
    ttl: 24 * 60 * 60 * 1000, // 24小时
  },
} as const;

// 创建全局缓存管理器实例
export const cacheManager = new CacheManager();

// 缓存装饰器
export function cached<T extends (...args: any[]) => any>(
  config: CacheConfig & { keyGenerator?: (...args: Parameters<T>) => string }
) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: Parameters<T>) {
      const key = config.keyGenerator 
        ? config.keyGenerator(...args)
        : `${propertyKey}_${JSON.stringify(args)}`;

      // 尝试从缓存获取
      const cached = await cacheManager.get(key, config);
      if (cached !== null) {
        return cached;
      }

      // 执行原方法
      const result = await originalMethod.apply(this, args);

      // 缓存结果
      await cacheManager.set(key, result, config);

      return result;
    };

    return descriptor;
  };
}
