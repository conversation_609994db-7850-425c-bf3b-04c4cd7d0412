/**
 * 安全的 window 对象扩展
 * 解决 Cannot assign to read only property 错误
 */

// 安全地扩展 window 对象
export function safeExtendWindow() {
  if (typeof window === 'undefined') {
    return; // 服务器端渲染时跳过
  }

  try {
    // 安全地设置 ethereum 属性
    if (!window.ethereum) {
      Object.defineProperty(window, 'ethereum', {
        value: undefined,
        writable: true,
        configurable: true
      });
    }

    // 安全地设置 solana 属性
    if (!window.solana) {
      Object.defineProperty(window, 'solana', {
        value: undefined,
        writable: true,
        configurable: true
      });
    }

    // 检测钱包
    detectWallets();
  } catch (error) {
    console.warn('Window 对象扩展失败:', error);
  }
}

/**
 * 检测可用的钱包
 */
function detectWallets() {
  // 检测 MetaMask
  if (window.ethereum?.isMetaMask) {
    console.log('✅ 检测到 MetaMask 钱包');
  }

  // 检测 Phantom (Solana)
  if (window.solana?.isPhantom) {
    console.log('✅ 检测到 Phantom 钱包');
  }

  // 检测其他钱包
  if (window.ethereum && !window.ethereum.isMetaMask) {
    console.log('✅ 检测到其他以太坊钱包');
  }
}

/**
 * 安全地获取钱包实例
 */
export function getEthereumWallet() {
  if (typeof window === 'undefined' || !window.ethereum) {
    return null;
  }
  return window.ethereum;
}

export function getSolanaWallet() {
  if (typeof window === 'undefined' || !window.solana) {
    return null;
  }
  return window.solana;
}

// 在客户端自动初始化
if (typeof window !== 'undefined') {
  // 延迟执行，确保所有脚本加载完成
  setTimeout(safeExtendWindow, 100);
}
