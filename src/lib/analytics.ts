/**
 * 分析和监控配置
 * 集成多种分析服务和错误监控
 */

// 分析事件类型
export interface AnalyticsEvent {
  name: string;
  properties?: Record<string, any>;
  userId?: string;
  sessionId?: string;
  timestamp?: number;
}

// 用户属性
export interface UserProperties {
  userId?: string;
  email?: string;
  username?: string;
  plan?: string;
  signupDate?: string;
  lastActiveDate?: string;
  [key: string]: any;
}

// 页面浏览事件
export interface PageViewEvent {
  page: string;
  title?: string;
  referrer?: string;
  url?: string;
  properties?: Record<string, any>;
}

// 错误事件
export interface ErrorEvent {
  error: Error;
  context?: Record<string, any>;
  userId?: string;
  url?: string;
  userAgent?: string;
  timestamp?: number;
}

/**
 * 分析服务基类
 */
abstract class AnalyticsProvider {
  abstract identify(userId: string, properties?: UserProperties): void;
  abstract track(event: AnalyticsEvent): void;
  abstract page(event: PageViewEvent): void;
  abstract group(groupId: string, properties?: Record<string, any>): void;
  abstract alias(newId: string, previousId?: string): void;
  abstract reset(): void;
}

/**
 * Google Analytics 4 提供者
 */
class GoogleAnalyticsProvider extends AnalyticsProvider {
  private measurementId: string;
  private isInitialized = false;

  constructor(measurementId: string) {
    super();
    this.measurementId = measurementId;
    this.initialize();
  }

  private initialize() {
    if (typeof window === 'undefined' || this.isInitialized) return;

    // 加载 gtag 脚本
    const script = document.createElement('script');
    script.async = true;
    script.src = `https://www.googletagmanager.com/gtag/js?id=${this.measurementId}`;
    document.head.appendChild(script);

    // 初始化 gtag
    window.dataLayer = window.dataLayer || [];
    window.gtag = function() {
      window.dataLayer.push(arguments);
    };

    window.gtag('js', new Date());
    window.gtag('config', this.measurementId, {
      page_title: document.title,
      page_location: window.location.href,
    });

    this.isInitialized = true;
  }

  identify(userId: string, properties?: UserProperties): void {
    if (typeof window === 'undefined' || !window.gtag) return;

    window.gtag('config', this.measurementId, {
      user_id: userId,
      custom_map: properties,
    });
  }

  track(event: AnalyticsEvent): void {
    if (typeof window === 'undefined' || !window.gtag) return;

    window.gtag('event', event.name, {
      event_category: 'engagement',
      event_label: event.name,
      value: 1,
      ...event.properties,
    });
  }

  page(event: PageViewEvent): void {
    if (typeof window === 'undefined' || !window.gtag) return;

    window.gtag('config', this.measurementId, {
      page_title: event.title,
      page_location: event.url || window.location.href,
    });
  }

  group(groupId: string, properties?: Record<string, any>): void {
    // GA4 doesn't have direct group support, use custom dimensions
    this.track({
      name: 'group_identified',
      properties: {
        group_id: groupId,
        ...properties,
      },
    });
  }

  alias(newId: string, previousId?: string): void {
    // GA4 handles user ID changes automatically
    console.log('GA4 alias:', { newId, previousId });
  }

  reset(): void {
    if (typeof window === 'undefined' || !window.gtag) return;
    
    // Clear user ID
    window.gtag('config', this.measurementId, {
      user_id: null,
    });
  }
}

/**
 * Mixpanel 提供者
 */
class MixpanelProvider extends AnalyticsProvider {
  private token: string;
  private isInitialized = false;

  constructor(token: string) {
    super();
    this.token = token;
    this.initialize();
  }

  private async initialize() {
    if (typeof window === 'undefined' || this.isInitialized) return;

    try {
      // 动态导入 Mixpanel
      const mixpanel = await import('mixpanel-browser');
      mixpanel.init(this.token, {
        debug: process.env.NODE_ENV === 'development',
        track_pageview: true,
        persistence: 'localStorage',
      });

      (window as any).mixpanel = mixpanel;
      this.isInitialized = true;
    } catch (error) {
      console.error('Failed to initialize Mixpanel:', error);
    }
  }

  identify(userId: string, properties?: UserProperties): void {
    if (typeof window === 'undefined' || !(window as any).mixpanel) return;

    (window as any).mixpanel.identify(userId);
    if (properties) {
      (window as any).mixpanel.people.set(properties);
    }
  }

  track(event: AnalyticsEvent): void {
    if (typeof window === 'undefined' || !(window as any).mixpanel) return;

    (window as any).mixpanel.track(event.name, {
      ...event.properties,
      timestamp: event.timestamp || Date.now(),
    });
  }

  page(event: PageViewEvent): void {
    if (typeof window === 'undefined' || !(window as any).mixpanel) return;

    (window as any).mixpanel.track('Page Viewed', {
      page: event.page,
      title: event.title,
      url: event.url,
      referrer: event.referrer,
      ...event.properties,
    });
  }

  group(groupId: string, properties?: Record<string, any>): void {
    if (typeof window === 'undefined' || !(window as any).mixpanel) return;

    (window as any).mixpanel.set_group('company', groupId);
    if (properties) {
      (window as any).mixpanel.get_group('company', groupId).set(properties);
    }
  }

  alias(newId: string, previousId?: string): void {
    if (typeof window === 'undefined' || !(window as any).mixpanel) return;

    (window as any).mixpanel.alias(newId, previousId);
  }

  reset(): void {
    if (typeof window === 'undefined' || !(window as any).mixpanel) return;

    (window as any).mixpanel.reset();
  }
}

/**
 * 自定义分析提供者
 */
class CustomAnalyticsProvider extends AnalyticsProvider {
  private endpoint: string;
  private apiKey?: string;

  constructor(endpoint: string, apiKey?: string) {
    super();
    this.endpoint = endpoint;
    this.apiKey = apiKey;
  }

  private async sendEvent(eventType: string, data: any) {
    try {
      await fetch(this.endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(this.apiKey && { 'Authorization': `Bearer ${this.apiKey}` }),
        },
        body: JSON.stringify({
          type: eventType,
          data,
          timestamp: Date.now(),
          url: window.location.href,
          userAgent: navigator.userAgent,
        }),
      });
    } catch (error) {
      console.error('Failed to send analytics event:', error);
    }
  }

  identify(userId: string, properties?: UserProperties): void {
    this.sendEvent('identify', { userId, properties });
  }

  track(event: AnalyticsEvent): void {
    this.sendEvent('track', event);
  }

  page(event: PageViewEvent): void {
    this.sendEvent('page', event);
  }

  group(groupId: string, properties?: Record<string, any>): void {
    this.sendEvent('group', { groupId, properties });
  }

  alias(newId: string, previousId?: string): void {
    this.sendEvent('alias', { newId, previousId });
  }

  reset(): void {
    this.sendEvent('reset', {});
  }
}

/**
 * 分析管理器
 */
class AnalyticsManager {
  private providers: AnalyticsProvider[] = [];
  private isEnabled = true;
  private userId?: string;
  private sessionId: string;

  constructor() {
    this.sessionId = this.generateSessionId();
    this.initialize();
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private initialize() {
    if (typeof window === 'undefined') return;

    // 初始化 Google Analytics
    if (process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID) {
      this.providers.push(
        new GoogleAnalyticsProvider(process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID)
      );
    }

    // 初始化 Mixpanel
    if (process.env.NEXT_PUBLIC_MIXPANEL_TOKEN) {
      this.providers.push(
        new MixpanelProvider(process.env.NEXT_PUBLIC_MIXPANEL_TOKEN)
      );
    }

    // 初始化自定义分析
    if (process.env.NEXT_PUBLIC_ANALYTICS_ENDPOINT) {
      this.providers.push(
        new CustomAnalyticsProvider(
          process.env.NEXT_PUBLIC_ANALYTICS_ENDPOINT,
          process.env.NEXT_PUBLIC_ANALYTICS_API_KEY
        )
      );
    }

    // 监听页面变化
    this.setupPageTracking();
  }

  private setupPageTracking() {
    // 监听路由变化
    let currentPath = window.location.pathname;
    
    const observer = new MutationObserver(() => {
      if (window.location.pathname !== currentPath) {
        currentPath = window.location.pathname;
        this.page({
          page: currentPath,
          title: document.title,
          url: window.location.href,
        });
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });

    // 初始页面浏览
    this.page({
      page: currentPath,
      title: document.title,
      url: window.location.href,
    });
  }

  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }

  identify(userId: string, properties?: UserProperties): void {
    if (!this.isEnabled) return;

    this.userId = userId;
    this.providers.forEach(provider => {
      try {
        provider.identify(userId, properties);
      } catch (error) {
        console.error('Analytics identify error:', error);
      }
    });
  }

  track(name: string, properties?: Record<string, any>): void {
    if (!this.isEnabled) return;

    const event: AnalyticsEvent = {
      name,
      properties,
      userId: this.userId,
      sessionId: this.sessionId,
      timestamp: Date.now(),
    };

    this.providers.forEach(provider => {
      try {
        provider.track(event);
      } catch (error) {
        console.error('Analytics track error:', error);
      }
    });
  }

  page(event: PageViewEvent): void {
    if (!this.isEnabled) return;

    this.providers.forEach(provider => {
      try {
        provider.page(event);
      } catch (error) {
        console.error('Analytics page error:', error);
      }
    });
  }

  group(groupId: string, properties?: Record<string, any>): void {
    if (!this.isEnabled) return;

    this.providers.forEach(provider => {
      try {
        provider.group(groupId, properties);
      } catch (error) {
        console.error('Analytics group error:', error);
      }
    });
  }

  alias(newId: string, previousId?: string): void {
    if (!this.isEnabled) return;

    this.providers.forEach(provider => {
      try {
        provider.alias(newId, previousId);
      } catch (error) {
        console.error('Analytics alias error:', error);
      }
    });
  }

  reset(): void {
    if (!this.isEnabled) return;

    this.userId = undefined;
    this.sessionId = this.generateSessionId();

    this.providers.forEach(provider => {
      try {
        provider.reset();
      } catch (error) {
        console.error('Analytics reset error:', error);
      }
    });
  }

  // 错误跟踪
  trackError(error: ErrorEvent): void {
    if (!this.isEnabled) return;

    this.track('error_occurred', {
      error_message: error.error.message,
      error_stack: error.error.stack,
      error_name: error.error.name,
      context: error.context,
      url: error.url || window.location.href,
      user_agent: error.userAgent || navigator.userAgent,
      timestamp: error.timestamp || Date.now(),
    });
  }

  // 性能跟踪
  trackPerformance(metrics: Record<string, number>): void {
    if (!this.isEnabled) return;

    this.track('performance_metrics', {
      ...metrics,
      url: window.location.href,
      timestamp: Date.now(),
    });
  }

  // 用户行为跟踪
  trackUserAction(action: string, properties?: Record<string, any>): void {
    this.track(`user_${action}`, properties);
  }

  // 业务事件跟踪
  trackBusinessEvent(event: string, properties?: Record<string, any>): void {
    this.track(`business_${event}`, properties);
  }
}

// 创建全局分析实例
export const analytics = new AnalyticsManager();

// 扩展 Window 接口
declare global {
  interface Window {
    dataLayer: any[];
    gtag: (...args: any[]) => void;
  }
}

// 导出常用的跟踪函数
export const track = analytics.track.bind(analytics);
export const identify = analytics.identify.bind(analytics);
export const page = analytics.page.bind(analytics);
export const trackError = analytics.trackError.bind(analytics);
export const trackPerformance = analytics.trackPerformance.bind(analytics);
export const trackUserAction = analytics.trackUserAction.bind(analytics);
export const trackBusinessEvent = analytics.trackBusinessEvent.bind(analytics);
