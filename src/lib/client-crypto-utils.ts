'use client';

/**
 * 浏览器端安全的随机数生成工具
 * 替代 Node.js crypto 模块，避免 hydration 错误
 */

/**
 * 生成随机字符串（浏览器兼容版本）
 */
export function generateSecureCode(length: number = 6, charset?: string): string {
  const defaultCharset = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const chars = charset || defaultCharset;
  let result = '';
  
  // 使用 Web Crypto API（现代浏览器支持）
  if (typeof window !== 'undefined' && window.crypto && window.crypto.getRandomValues) {
    const randomValues = new Uint8Array(length);
    window.crypto.getRandomValues(randomValues);
    
    for (let i = 0; i < length; i++) {
      result += chars[randomValues[i] % chars.length];
    }
  } else {
    // 降级到 Math.random（不够安全，但可用）
    for (let i = 0; i < length; i++) {
      result += chars[Math.floor(Math.random() * chars.length)];
    }
  }
  
  return result;
}

/**
 * 生成随机浮点数（浏览器兼容版本）
 */
export function generateSecureFloat(min: number = 0, max: number = 1): number {
  if (min >= max) {
    throw new Error('min must be less than max');
  }
  
  let randomValue: number;
  
  if (typeof window !== 'undefined' && window.crypto && window.crypto.getRandomValues) {
    const randomArray = new Uint32Array(1);
    window.crypto.getRandomValues(randomArray);
    randomValue = randomArray[0] / 0xFFFFFFFF;
  } else {
    randomValue = Math.random();
  }
  
  return min + randomValue * (max - min);
}

/**
 * 生成随机数字（浏览器兼容版本）
 */
export function generateSecureNumber(min: number = 0, max: number = 1): number {
  if (min >= max) {
    throw new Error('min must be less than max');
  }
  
  let randomValue: number;
  
  if (typeof window !== 'undefined' && window.crypto && window.crypto.getRandomValues) {
    const randomArray = new Uint32Array(1);
    window.crypto.getRandomValues(randomArray);
    randomValue = randomArray[0] / 0xFFFFFFFF;
  } else {
    randomValue = Math.random();
  }
  
  return Math.floor(min + randomValue * (max - min));
}

/**
 * 生成随机布尔值（浏览器兼容版本）
 */
export function generateSecureBoolean(): boolean {
  if (typeof window !== 'undefined' && window.crypto && window.crypto.getRandomValues) {
    const randomArray = new Uint8Array(1);
    window.crypto.getRandomValues(randomArray);
    return randomArray[0] >= 128;
  } else {
    return Math.random() >= 0.5;
  }
}

/**
 * 生成随机UUID v4（浏览器兼容版本）
 */
export function generateSecureUUID(): string {
  if (typeof window !== 'undefined' && window.crypto && window.crypto.getRandomValues) {
    const bytes = new Uint8Array(16);
    window.crypto.getRandomValues(bytes);
    
    // 设置版本号 (4) 和变体位
    bytes[6] = (bytes[6] & 0x0f) | 0x40; // 版本 4
    bytes[8] = (bytes[8] & 0x3f) | 0x80; // 变体 10
    
    const hex = Array.from(bytes).map(b => b.toString(16).padStart(2, '0')).join('');
    return [
      hex.slice(0, 8),
      hex.slice(8, 12),
      hex.slice(12, 16),
      hex.slice(16, 20),
      hex.slice(20, 32)
    ].join('-');
  } else {
    // 降级实现
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }
}

/**
 * 从数组中安全随机选择元素
 */
export function secureRandomChoice<T>(array: T[], count: number = 1): T[] {
  if (array.length === 0) {
    return [];
  }
  
  if (count >= array.length) {
    return [...array];
  }
  
  const result: T[] = [];
  const indices = new Set<number>();
  
  while (result.length < count) {
    const randomIndex = generateSecureNumber(0, array.length);
    if (!indices.has(randomIndex)) {
      indices.add(randomIndex);
      result.push(array[randomIndex]);
    }
  }
  
  return result;
}

/**
 * 安全随机打乱数组
 */
export function secureRandomShuffle<T>(array: T[]): T[] {
  const result = [...array];
  
  for (let i = result.length - 1; i > 0; i--) {
    const j = generateSecureNumber(0, i + 1);
    [result[i], result[j]] = [result[j], result[i]];
  }
  
  return result;
}

// 导出便捷的替换函数，用于替换 Math.random()
export const secureRandom = {
  /**
   * 替换 Math.random()，返回 [0, 1) 范围内的随机数
   */
  random: (): number => generateSecureFloat(0, 1),
  
  /**
   * 替换 Math.floor(Math.random() * max)
   */
  randomInt: (max: number): number => generateSecureNumber(0, max),
  
  /**
   * 替换 Math.random() > 0.5
   */
  randomBoolean: (): boolean => generateSecureBoolean(),
};