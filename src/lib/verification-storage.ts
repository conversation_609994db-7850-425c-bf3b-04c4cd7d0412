/**
 * 验证码存储和管理
 * 实现验证码的持久化存储和验证逻辑
 */

import { createClient } from '@supabase/supabase-js';
import { log } from './logger';
import { generateSecureCode } from './browser-crypto';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export interface VerificationCode {
  id: string;
  code: string;
  type: 'telegram' | 'email' | 'sms' | 'wallet';
  userId?: string;
  walletAddress?: string;
  telegramId?: string;
  email?: string;
  phone?: string;
  expiresAt: string;
  isUsed: boolean;
  attempts: number;
  maxAttempts: number;
  metadata?: Record<string, any>;
  createdAt: string;
  usedAt?: string;
}

export interface CreateVerificationCodeOptions {
  type: VerificationCode['type'];
  userId?: string;
  walletAddress?: string;
  telegramId?: string;
  email?: string;
  phone?: string;
  expirationMinutes?: number;
  maxAttempts?: number;
  metadata?: Record<string, any>;
}

export interface VerifyCodeOptions {
  code: string;
  type: VerificationCode['type'];
  userId?: string;
  walletAddress?: string;
  telegramId?: string;
  email?: string;
  phone?: string;
}

/**
 * 创建验证码
 */
export async function createVerificationCode(
  options: CreateVerificationCodeOptions
): Promise<VerificationCode> {
  try {
    const {
      type,
      userId,
      walletAddress,
      telegramId,
      email,
      phone,
      expirationMinutes = 10,
      maxAttempts = 3,
      metadata = {},
    } = options;

    // 生成安全的验证码
    const code = generateSecureCode(6, '0123456789');
    
    // 计算过期时间
    const expiresAt = new Date(Date.now() + expirationMinutes * 60 * 1000).toISOString();

    // 创建验证码记录
    const verificationData = {
      code,
      type,
      user_id: userId,
      wallet_address: walletAddress,
      telegram_id: telegramId,
      email,
      phone,
      expires_at: expiresAt,
      is_used: false,
      attempts: 0,
      max_attempts: maxAttempts,
      metadata,
      created_at: new Date().toISOString(),
    };

    const { data, error } = await supabase
      .from('verification_codes')
      .insert(verificationData)
      .select()
      .single();

    if (error) {
      throw error;
    }

    log.info('Verification code created', {
      id: data.id,
      type,
      userId,
      walletAddress,
      telegramId,
      email: email ? '***@***' : undefined,
      phone: phone ? '***' : undefined,
    });

    return {
      id: data.id,
      code: data.code,
      type: data.type,
      userId: data.user_id,
      walletAddress: data.wallet_address,
      telegramId: data.telegram_id,
      email: data.email,
      phone: data.phone,
      expiresAt: data.expires_at,
      isUsed: data.is_used,
      attempts: data.attempts,
      maxAttempts: data.max_attempts,
      metadata: data.metadata,
      createdAt: data.created_at,
      usedAt: data.used_at,
    };

  } catch (error) {
    log.error('Failed to create verification code', error, options);
    throw error;
  }
}

/**
 * 验证验证码
 */
export async function verifyCode(options: VerifyCodeOptions): Promise<{
  success: boolean;
  verificationCode?: VerificationCode;
  error?: string;
}> {
  try {
    const { code, type, userId, walletAddress, telegramId, email, phone } = options;

    // 构建查询条件
    let query = supabase
      .from('verification_codes')
      .select('*')
      .eq('code', code)
      .eq('type', type)
      .eq('is_used', false);

    // 添加身份标识条件
    if (userId) query = query.eq('user_id', userId);
    if (walletAddress) query = query.eq('wallet_address', walletAddress);
    if (telegramId) query = query.eq('telegram_id', telegramId);
    if (email) query = query.eq('email', email);
    if (phone) query = query.eq('phone', phone);

    const { data, error } = await query.single();

    if (error || !data) {
      log.warn('Verification code not found', { code, type, userId, walletAddress });
      return { success: false, error: '验证码无效' };
    }

    const verificationCode: VerificationCode = {
      id: data.id,
      code: data.code,
      type: data.type,
      userId: data.user_id,
      walletAddress: data.wallet_address,
      telegramId: data.telegram_id,
      email: data.email,
      phone: data.phone,
      expiresAt: data.expires_at,
      isUsed: data.is_used,
      attempts: data.attempts,
      maxAttempts: data.max_attempts,
      metadata: data.metadata,
      createdAt: data.created_at,
      usedAt: data.used_at,
    };

    // 检查是否过期
    if (new Date() > new Date(verificationCode.expiresAt)) {
      log.warn('Verification code expired', { id: data.id, code });
      return { success: false, error: '验证码已过期' };
    }

    // 检查尝试次数
    if (verificationCode.attempts >= verificationCode.maxAttempts) {
      log.warn('Verification code max attempts exceeded', { id: data.id, code });
      return { success: false, error: '验证码尝试次数过多' };
    }

    // 更新验证码状态
    const { error: updateError } = await supabase
      .from('verification_codes')
      .update({
        is_used: true,
        used_at: new Date().toISOString(),
        attempts: verificationCode.attempts + 1,
      })
      .eq('id', data.id);

    if (updateError) {
      throw updateError;
    }

    log.info('Verification code verified successfully', {
      id: data.id,
      type,
      userId,
      walletAddress,
    });

    return {
      success: true,
      verificationCode: {
        ...verificationCode,
        isUsed: true,
        usedAt: new Date().toISOString(),
        attempts: verificationCode.attempts + 1,
      },
    };

  } catch (error) {
    log.error('Failed to verify code', error, options);
    return { success: false, error: '验证失败' };
  }
}

/**
 * 记录验证失败尝试
 */
export async function recordFailedAttempt(
  code: string,
  type: VerificationCode['type']
): Promise<void> {
  try {
    const { data } = await supabase
      .from('verification_codes')
      .select('id, attempts, max_attempts')
      .eq('code', code)
      .eq('type', type)
      .eq('is_used', false)
      .single();

    if (data) {
      await supabase
        .from('verification_codes')
        .update({
          attempts: data.attempts + 1,
        })
        .eq('id', data.id);

      log.info('Failed verification attempt recorded', {
        id: data.id,
        attempts: data.attempts + 1,
        maxAttempts: data.max_attempts,
      });
    }
  } catch (error) {
    log.warn('Failed to record failed attempt', { error, code, type });
  }
}

/**
 * 清理过期的验证码
 */
export async function cleanupExpiredCodes(): Promise<void> {
  try {
    const { data, error } = await supabase
      .from('verification_codes')
      .delete()
      .lt('expires_at', new Date().toISOString())
      .select('id');

    if (error) {
      throw error;
    }

    log.info('Expired verification codes cleaned up', {
      deletedCount: data?.length || 0,
    });
  } catch (error) {
    log.error('Failed to cleanup expired codes', error);
  }
}

/**
 * 获取用户的验证码历史
 */
export async function getUserVerificationHistory(
  userId: string,
  limit: number = 10
): Promise<VerificationCode[]> {
  try {
    const { data, error } = await supabase
      .from('verification_codes')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      throw error;
    }

    return (data || []).map(item => ({
      id: item.id,
      code: item.code,
      type: item.type,
      userId: item.user_id,
      walletAddress: item.wallet_address,
      telegramId: item.telegram_id,
      email: item.email,
      phone: item.phone,
      expiresAt: item.expires_at,
      isUsed: item.is_used,
      attempts: item.attempts,
      maxAttempts: item.max_attempts,
      metadata: item.metadata,
      createdAt: item.created_at,
      usedAt: item.used_at,
    }));
  } catch (error) {
    log.error('Failed to get user verification history', error, { userId });
    return [];
  }
}

/**
 * 检查验证码是否存在且有效
 */
export async function isCodeValid(
  code: string,
  type: VerificationCode['type']
): Promise<boolean> {
  try {
    const { data } = await supabase
      .from('verification_codes')
      .select('expires_at, is_used, attempts, max_attempts')
      .eq('code', code)
      .eq('type', type)
      .single();

    if (!data) return false;

    const isNotExpired = new Date() <= new Date(data.expires_at);
    const isNotUsed = !data.is_used;
    const hasAttemptsLeft = data.attempts < data.max_attempts;

    return isNotExpired && isNotUsed && hasAttemptsLeft;
  } catch (error) {
    return false;
  }
}
