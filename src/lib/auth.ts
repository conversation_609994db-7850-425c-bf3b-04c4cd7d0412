import { supabase } from './supabase';
import { createClient } from './supabase-server';
import type { User } from '@/types';

// Client-side auth functions
export const auth = {
  // Sign up with email and password
  async signUp(email: string, password: string, username: string) {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          username,
        },
      },
    });

    if (error) throw error;
    return data;
  },

  // Sign in with email and password
  async signIn(email: string, password: string) {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) throw error;
    return data;
  },

  // Sign out
  async signOut() {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
  },

  // Get current user
  async getUser() {
    const { data: { user }, error } = await supabase.auth.getUser();
    if (error) throw error;
    return user;
  },

  // Get current session
  async getSession() {
    const { data: { session }, error } = await supabase.auth.getSession();
    if (error) throw error;
    return session;
  },

  // Reset password
  async resetPassword(email: string) {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/reset-password`,
    });
    if (error) throw error;
  },

  // Update password
  async updatePassword(password: string) {
    const { error } = await supabase.auth.updateUser({
      password,
    });
    if (error) throw error;
  },

  // Update user metadata
  async updateUser(updates: { username?: string; avatar_url?: string }) {
    const { error } = await supabase.auth.updateUser({
      data: updates,
    });
    if (error) throw error;
  },

  // Listen to auth state changes
  onAuthStateChange(callback: (event: string, session: any) => void) {
    if (!supabase) {
      console.warn('Supabase client not available');
      return { data: { subscription: null }, error: null };
    }
    return supabase.auth.onAuthStateChange(callback);
  },
};

// Server-side auth functions
export const serverAuth = {
  // Get user from server
  async getUser() {
    const supabase = createClient();
    const { data: { user }, error } = await supabase.auth.getUser();
    if (error) throw error;
    return user;
  },

  // Get session from server
  async getSession() {
    const supabase = createClient();
    const { data: { session }, error } = await supabase.auth.getSession();
    if (error) throw error;
    return session;
  },

  // Get user profile from database
  async getUserProfile(userId: string): Promise<User | null> {
    const supabase = createClient();
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // User not found
        return null;
      }
      throw error;
    }

    return data;
  },

  // Update user profile
  async updateUserProfile(userId: string, updates: Partial<User>) {
    const supabase = createClient();
    const { data, error } = await supabase
      .from('users')
      .update(updates)
      .eq('id', userId)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Connect wallet address
  async connectWallet(userId: string, walletAddress: string) {
    const supabase = createClient();
    const { data, error } = await supabase
      .from('users')
      .update({ wallet_address: walletAddress })
      .eq('id', userId)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Disconnect wallet
  async disconnectWallet(userId: string) {
    const supabase = createClient();
    const { data, error } = await supabase
      .from('users')
      .update({ wallet_address: null })
      .eq('id', userId)
      .select()
      .single();

    if (error) throw error;
    return data;
  },
};

// Auth validation functions
export const authValidation = {
  isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  isValidPassword(password: string): boolean {
    // At least 8 characters, 1 uppercase, 1 lowercase, 1 number
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/;
    return passwordRegex.test(password);
  },

  isValidUsername(username: string): boolean {
    // 3-20 characters, alphanumeric and underscore only
    const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
    return usernameRegex.test(username);
  },
};

// Auth error handling
export const authErrors = {
  getErrorMessage(error: any): string {
    if (!error) return '未知错误';

    switch (error.message) {
      case 'Invalid login credentials':
        return '邮箱或密码错误';
      case 'Email not confirmed':
        return '请先验证您的邮箱';
      case 'User already registered':
        return '该邮箱已被注册';
      case 'Password should be at least 6 characters':
        return '密码至少需要6个字符';
      case 'Unable to validate email address: invalid format':
        return '邮箱格式不正确';
      case 'signup is disabled':
        return '注册功能暂时关闭';
      default:
        return error.message || '操作失败，请重试';
    }
  },
};
