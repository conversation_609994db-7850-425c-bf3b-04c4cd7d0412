/**
 * API错误处理工具
 * 提供统一的API错误处理和响应格式
 */

import { NextResponse } from 'next/server';
// import { JsonWebTokenError, TokenExpiredError } from 'jsonwebtoken';

export interface ApiError {
  code: string;
  message: string;
  statusCode: number;
  details?: any;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
  requestId?: string;
}

/**
 * 标准化的API错误类型
 */
export enum ApiErrorCode {
  // 认证错误
  UNAUTHORIZED = 'UNAUTHORIZED',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  INVALID_TOKEN = 'INVALID_TOKEN',
  FORBIDDEN = 'FORBIDDEN',
  
  // 请求错误
  BAD_REQUEST = 'BAD_REQUEST',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  NOT_FOUND = 'NOT_FOUND',
  METHOD_NOT_ALLOWED = 'METHOD_NOT_ALLOWED',
  
  // 服务器错误
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR',
  
  // 业务逻辑错误
  INSUFFICIENT_FUNDS = 'INSUFFICIENT_FUNDS',
  INVALID_OPERATION = 'INVALID_OPERATION',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
}

/**
 * 创建标准化的API错误
 */
export function createApiError(
  code: ApiErrorCode,
  message: string,
  statusCode: number,
  details?: any
): ApiError {
  return {
    code,
    message,
    statusCode,
    details,
  };
}

/**
 * 创建成功响应
 */
export function createSuccessResponse<T>(
  data: T,
  message?: string
): NextResponse {
  const response: ApiResponse<T> = {
    success: true,
    data,
    timestamp: new Date().toISOString(),
  };

  return NextResponse.json(response);
}

/**
 * 创建错误响应
 */
export function createErrorResponse(
  error: ApiError,
  requestId?: string
): NextResponse {
  const response: ApiResponse = {
    success: false,
    error: {
      code: error.code,
      message: error.message,
      details: process.env.NODE_ENV === 'development' ? error.details : undefined,
    },
    timestamp: new Date().toISOString(),
    requestId,
  };

  return NextResponse.json(response, { status: error.statusCode });
}

/**
 * 通用API错误处理函数
 */
export function handleApiError(error: unknown): NextResponse {
  const apiError = handleUnknownError(error);
  return createErrorResponse(apiError);
}

/**
 * 处理未知错误并转换为标准格式
 */
export function handleUnknownError(error: unknown): ApiError {
  // JWT错误处理 (动态检查，避免导入问题)
  if (error && typeof error === 'object' && 'name' in error) {
    const errorName = (error as any).name;
    if (errorName === 'TokenExpiredError') {
      return createApiError(
        ApiErrorCode.TOKEN_EXPIRED,
        '认证令牌已过期',
        401,
        { expiredAt: (error as any).expiredAt }
      );
    }
    if (errorName === 'JsonWebTokenError') {
      return createApiError(
        ApiErrorCode.INVALID_TOKEN,
        '无效的认证令牌',
        401,
        { jwtError: (error as Error).message }
      );
    }
  }

  // 标准Error对象
  if (error instanceof Error) {
    // 检查是否是数据库错误
    if (error.message.includes('database') || error.message.includes('supabase')) {
      return createApiError(
        ApiErrorCode.DATABASE_ERROR,
        '数据库操作失败',
        500,
        { originalError: error.message }
      );
    }

    // 检查是否是网络错误
    if (error.message.includes('fetch') || error.message.includes('network')) {
      return createApiError(
        ApiErrorCode.EXTERNAL_SERVICE_ERROR,
        '外部服务调用失败',
        503,
        { originalError: error.message }
      );
    }

    // 通用服务器错误
    return createApiError(
      ApiErrorCode.INTERNAL_SERVER_ERROR,
      error.message || '内部服务器错误',
      500,
      { stack: error.stack }
    );
  }

  // 非Error类型的错误
  return createApiError(
    ApiErrorCode.INTERNAL_SERVER_ERROR,
    '未知错误',
    500,
    { originalError: error }
  );
}

/**
 * API路由错误处理装饰器
 */
export function withErrorHandler<T extends any[], R>(
  handler: (...args: T) => Promise<NextResponse>
) {
  return async (...args: T): Promise<NextResponse> => {
    try {
      return await handler(...args);
    } catch (error) {
      console.error('API Error:', error);
      const apiError = handleUnknownError(error);
      return createErrorResponse(apiError);
    }
  };
}

/**
 * 验证请求体
 */
export function validateRequestBody(
  body: any,
  requiredFields: string[]
): ApiError | null {
  if (!body) {
    return createApiError(
      ApiErrorCode.BAD_REQUEST,
      '请求体不能为空',
      400
    );
  }

  const missingFields = requiredFields.filter(field => 
    body[field] === undefined || body[field] === null || body[field] === ''
  );

  if (missingFields.length > 0) {
    return createApiError(
      ApiErrorCode.VALIDATION_ERROR,
      `缺少必需字段: ${missingFields.join(', ')}`,
      400,
      { missingFields }
    );
  }

  return null;
}

/**
 * 验证查询参数
 */
export function validateQueryParams(
  searchParams: URLSearchParams,
  requiredParams: string[]
): ApiError | null {
  const missingParams = requiredParams.filter(param => 
    !searchParams.has(param) || searchParams.get(param) === ''
  );

  if (missingParams.length > 0) {
    return createApiError(
      ApiErrorCode.VALIDATION_ERROR,
      `缺少必需参数: ${missingParams.join(', ')}`,
      400,
      { missingParams }
    );
  }

  return null;
}

/**
 * 记录API错误日志
 */
export function logApiError(error: ApiError, context?: any): void {
  const logData = {
    timestamp: new Date().toISOString(),
    error: {
      code: error.code,
      message: error.message,
      statusCode: error.statusCode,
      details: error.details,
    },
    context,
  };

  if (error.statusCode >= 500) {
    console.error('API Server Error:', logData);
  } else if (error.statusCode >= 400) {
    console.warn('API Client Error:', logData);
  } else {
    console.info('API Error:', logData);
  }
}
