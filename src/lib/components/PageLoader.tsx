/**
 * 页面加载优化组件
 * 提供预加载、懒加载、骨架屏等功能
 */

'use client';

import React, { useState, useEffect, useRef, ReactNode, Suspense } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useIntersectionObserver } from '@/lib/hooks/usePerformance';

// 骨架屏组件
interface SkeletonProps {
  width?: string | number;
  height?: string | number;
  className?: string;
  variant?: 'text' | 'rectangular' | 'circular';
  animation?: 'pulse' | 'wave' | 'none';
}

export function Skeleton({
  width = '100%',
  height = '1rem',
  className = '',
  variant = 'text',
  animation = 'pulse'
}: SkeletonProps) {
  const getVariantClasses = () => {
    switch (variant) {
      case 'circular':
        return 'rounded-full';
      case 'rectangular':
        return 'rounded';
      case 'text':
      default:
        return 'rounded';
    }
  };

  const getAnimationClasses = () => {
    switch (animation) {
      case 'wave':
        return 'animate-wave';
      case 'pulse':
        return 'animate-pulse';
      case 'none':
      default:
        return '';
    }
  };

  return (
    <div
      className={`bg-gray-200 ${getVariantClasses()} ${getAnimationClasses()} ${className}`}
      style={{ width, height }}
    />
  );
}

// 骨架屏布局组件
export function SkeletonLayout({ children }: { children: ReactNode }) {
  return (
    <div className="animate-pulse space-y-4">
      {children}
    </div>
  );
}

// 预定义骨架屏
export const SkeletonPresets = {
  // 卡片骨架屏
  Card: () => (
    <SkeletonLayout>
      <Skeleton height="200px" className="mb-4" />
      <Skeleton height="1.5rem" width="60%" className="mb-2" />
      <Skeleton height="1rem" width="80%" className="mb-2" />
      <Skeleton height="1rem" width="40%" />
    </SkeletonLayout>
  ),

  // 列表项骨架屏
  ListItem: () => (
    <div className="flex items-center space-x-4 p-4">
      <Skeleton variant="circular" width="40px" height="40px" />
      <div className="flex-1 space-y-2">
        <Skeleton height="1rem" width="60%" />
        <Skeleton height="0.875rem" width="40%" />
      </div>
    </div>
  ),

  // 表格骨架屏
  Table: ({ rows = 5, cols = 4 }: { rows?: number; cols?: number }) => (
    <SkeletonLayout>
      {/* 表头 */}
      <div className="flex space-x-4 mb-4">
        {Array.from({ length: cols }).map((_, i) => (
          <Skeleton key={i} height="1.5rem" className="flex-1" />
        ))}
      </div>
      {/* 表格行 */}
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div key={rowIndex} className="flex space-x-4 mb-2">
          {Array.from({ length: cols }).map((_, colIndex) => (
            <Skeleton key={colIndex} height="1rem" className="flex-1" />
          ))}
        </div>
      ))}
    </SkeletonLayout>
  ),

  // 用户资料骨架屏
  Profile: () => (
    <SkeletonLayout>
      <div className="flex items-center space-x-4 mb-6">
        <Skeleton variant="circular" width="80px" height="80px" />
        <div className="space-y-2">
          <Skeleton height="1.5rem" width="120px" />
          <Skeleton height="1rem" width="200px" />
        </div>
      </div>
      <div className="space-y-3">
        <Skeleton height="1rem" width="100%" />
        <Skeleton height="1rem" width="80%" />
        <Skeleton height="1rem" width="60%" />
      </div>
    </SkeletonLayout>
  )
};

// 懒加载组件
interface LazyLoadProps {
  children: ReactNode;
  fallback?: ReactNode;
  threshold?: number;
  rootMargin?: string;
  once?: boolean;
  className?: string;
}

export function LazyLoad({
  children,
  fallback = <Skeleton height="200px" />,
  threshold = 0.1,
  rootMargin = '50px',
  once = true,
  className = ''
}: LazyLoadProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const elementRef = useRef<HTMLDivElement>(null);
  
  const isIntersecting = useIntersectionObserver(elementRef, {
    threshold,
    rootMargin
  });

  useEffect(() => {
    if (isIntersecting && !isLoaded) {
      setIsLoaded(true);
    }
  }, [isIntersecting, isLoaded]);

  return (
    <div ref={elementRef} className={className}>
      {isLoaded ? children : fallback}
    </div>
  );
}

// 渐进式图片加载
interface ProgressiveImageProps {
  src: string;
  alt: string;
  placeholder?: string;
  className?: string;
  width?: number;
  height?: number;
  onLoad?: () => void;
  onError?: () => void;
}

export function ProgressiveImage({
  src,
  alt,
  placeholder,
  className = '',
  width,
  height,
  onLoad,
  onError
}: ProgressiveImageProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [currentSrc, setCurrentSrc] = useState(placeholder || '');

  useEffect(() => {
    const img = new Image();
    
    img.onload = () => {
      setCurrentSrc(src);
      setIsLoaded(true);
      onLoad?.();
    };
    
    img.onerror = () => {
      setHasError(true);
      onError?.();
    };
    
    img.src = src;
  }, [src, onLoad, onError]);

  if (hasError) {
    return (
      <div 
        className={`bg-gray-200 flex items-center justify-center ${className}`}
        style={{ width, height }}
      >
        <span className="text-gray-500 text-sm">加载失败</span>
      </div>
    );
  }

  return (
    <div className={`relative overflow-hidden ${className}`}>
      {/* 占位符 */}
      {!isLoaded && (
        <div 
          className="absolute inset-0 bg-gray-200 animate-pulse"
          style={{ width, height }}
        />
      )}
      
      {/* 实际图片 */}
      <motion.img
        src={currentSrc}
        alt={alt}
        width={width}
        height={height}
        className={`transition-opacity duration-300 ${isLoaded ? 'opacity-100' : 'opacity-0'}`}
        initial={{ opacity: 0 }}
        animate={{ opacity: isLoaded ? 1 : 0 }}
        transition={{ duration: 0.3 }}
      />
    </div>
  );
}

// 页面加载指示器
interface PageLoaderProps {
  loading: boolean;
  message?: string;
  progress?: number;
  type?: 'spinner' | 'bar' | 'dots' | 'skeleton';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function PageLoader({
  loading,
  message = '加载中...',
  progress,
  type = 'spinner',
  size = 'md',
  className = ''
}: PageLoaderProps) {
  if (!loading) return null;

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'w-4 h-4';
      case 'lg':
        return 'w-8 h-8';
      case 'md':
      default:
        return 'w-6 h-6';
    }
  };

  const renderLoader = () => {
    switch (type) {
      case 'spinner':
        return (
          <div className={`animate-spin rounded-full border-2 border-gray-300 border-t-system-blue ${getSizeClasses()}`} />
        );
      
      case 'dots':
        return (
          <div className="flex space-x-1">
            {[0, 1, 2].map(i => (
              <div
                key={i}
                className={`bg-system-blue rounded-full animate-bounce ${getSizeClasses()}`}
                style={{ animationDelay: `${i * 0.1}s` }}
              />
            ))}
          </div>
        );
      
      case 'bar':
        return (
          <div className="w-full bg-gray-200 rounded-full h-2">
            <motion.div
              className="bg-system-blue h-2 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: progress ? `${progress}%` : '100%' }}
              transition={{ duration: progress ? 0.3 : 2, repeat: progress ? 0 : Infinity }}
            />
          </div>
        );
      
      case 'skeleton':
        return <SkeletonPresets.Card />;
      
      default:
        return null;
    }
  };

  return (
    <motion.div
      className={`flex flex-col items-center justify-center p-8 ${className}`}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      {renderLoader()}
      
      {message && (
        <p className="mt-4 text-sm text-secondary-label text-center">
          {message}
        </p>
      )}
      
      {progress !== undefined && type !== 'bar' && (
        <p className="mt-2 text-xs text-tertiary-label">
          {progress}%
        </p>
      )}
    </motion.div>
  );
}

// 代码分割加载组件
interface CodeSplitLoaderProps {
  children: ReactNode;
  fallback?: ReactNode;
  errorFallback?: ReactNode;
}

export function CodeSplitLoader({
  children,
  fallback = <PageLoader loading={true} type="skeleton" />,
  errorFallback = <div className="text-center p-8 text-red-600">加载失败，请刷新页面重试</div>
}: CodeSplitLoaderProps) {
  return (
    <Suspense fallback={fallback}>
      <ErrorBoundary fallback={errorFallback}>
        {children}
      </ErrorBoundary>
    </Suspense>
  );
}

// 错误边界组件
class ErrorBoundary extends React.Component<
  { children: ReactNode; fallback: ReactNode },
  { hasError: boolean }
> {
  constructor(props: { children: ReactNode; fallback: ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(): { hasError: boolean } {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Code split loading error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback;
    }

    return this.props.children;
  }
}

// 预加载Hook
export function usePreload() {
  const preloadedResources = useRef(new Set<string>());

  const preloadImage = useCallback((src: string) => {
    if (preloadedResources.current.has(src)) return;
    
    const img = new Image();
    img.src = src;
    preloadedResources.current.add(src);
  }, []);

  const preloadComponent = useCallback((importFn: () => Promise<any>) => {
    importFn().catch(error => {
      console.error('Component preload failed:', error);
    });
  }, []);

  const preloadRoute = useCallback((href: string) => {
    if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
      window.requestIdleCallback(() => {
        const link = document.createElement('link');
        link.rel = 'prefetch';
        link.href = href;
        document.head.appendChild(link);
      });
    }
  }, []);

  return {
    preloadImage,
    preloadComponent,
    preloadRoute
  };
}
