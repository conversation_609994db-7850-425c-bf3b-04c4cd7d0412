/**
 * 用户操作指引组件
 * 提供新手引导、操作提示、帮助系统
 */

'use client';

import React, { useState, useEffect, useRef, ReactNode } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { createPortal } from 'react-dom';

// 引导步骤定义
export interface GuideStep {
  id: string;
  target: string; // CSS选择器
  title: string;
  content: string;
  position?: 'top' | 'bottom' | 'left' | 'right' | 'center';
  action?: {
    label: string;
    onClick: () => void;
  };
  skippable?: boolean;
}

// 引导配置
export interface GuideConfig {
  id: string;
  name: string;
  steps: GuideStep[];
  autoStart?: boolean;
  showProgress?: boolean;
  allowSkip?: boolean;
  onComplete?: () => void;
  onSkip?: () => void;
}

// 提示类型
export type TooltipType = 'info' | 'warning' | 'success' | 'error';

interface TooltipProps {
  children: ReactNode;
  content: string;
  type?: TooltipType;
  position?: 'top' | 'bottom' | 'left' | 'right';
  trigger?: 'hover' | 'click' | 'focus';
  delay?: number;
  className?: string;
}

// 用户引导Context
interface UserGuideContextType {
  activeGuide: string | null;
  currentStep: number;
  startGuide: (guideId: string) => void;
  nextStep: () => void;
  prevStep: () => void;
  skipGuide: () => void;
  completeGuide: () => void;
  isGuideCompleted: (guideId: string) => boolean;
  markGuideCompleted: (guideId: string) => void;
}

const UserGuideContext = React.createContext<UserGuideContextType | undefined>(undefined);

// 引导管理器
export class GuideManager {
  private static instance: GuideManager;
  private guides: Map<string, GuideConfig> = new Map();
  private completedGuides: Set<string> = new Set();

  static getInstance(): GuideManager {
    if (!GuideManager.instance) {
      GuideManager.instance = new GuideManager();
    }
    return GuideManager.instance;
  }

  registerGuide(config: GuideConfig) {
    this.guides.set(config.id, config);
  }

  getGuide(id: string): GuideConfig | undefined {
    return this.guides.get(id);
  }

  markCompleted(guideId: string) {
    this.completedGuides.add(guideId);
    // 保存到localStorage
    if (typeof localStorage !== 'undefined') {
      const completed = Array.from(this.completedGuides);
      localStorage.setItem('completed_guides', JSON.stringify(completed));
    }
  }

  isCompleted(guideId: string): boolean {
    return this.completedGuides.has(guideId);
  }

  loadCompletedGuides() {
    if (typeof localStorage !== 'undefined') {
      const saved = localStorage.getItem('completed_guides');
      if (saved) {
        try {
          const completed = JSON.parse(saved);
          this.completedGuides = new Set(completed);
        } catch (error) {
          console.error('Failed to load completed guides:', error);
        }
      }
    }
  }
}

// 用户引导Provider
export function UserGuideProvider({ children }: { children: ReactNode }) {
  const [activeGuide, setActiveGuide] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState(0);
  const guideManager = GuideManager.getInstance();

  useEffect(() => {
    guideManager.loadCompletedGuides();
  }, [guideManager]);

  const startGuide = (guideId: string) => {
    const guide = guideManager.getGuide(guideId);
    if (guide && !guideManager.isCompleted(guideId)) {
      setActiveGuide(guideId);
      setCurrentStep(0);
    }
  };

  const nextStep = () => {
    const guide = guideManager.getGuide(activeGuide!);
    if (guide && currentStep < guide.steps.length - 1) {
      setCurrentStep(prev => prev + 1);
    } else {
      completeGuide();
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const skipGuide = () => {
    const guide = guideManager.getGuide(activeGuide!);
    if (guide?.onSkip) {
      guide.onSkip();
    }
    setActiveGuide(null);
    setCurrentStep(0);
  };

  const completeGuide = () => {
    if (activeGuide) {
      const guide = guideManager.getGuide(activeGuide);
      guideManager.markCompleted(activeGuide);
      if (guide?.onComplete) {
        guide.onComplete();
      }
      setActiveGuide(null);
      setCurrentStep(0);
    }
  };

  const isGuideCompleted = (guideId: string) => {
    return guideManager.isCompleted(guideId);
  };

  const markGuideCompleted = (guideId: string) => {
    guideManager.markCompleted(guideId);
  };

  const value: UserGuideContextType = {
    activeGuide,
    currentStep,
    startGuide,
    nextStep,
    prevStep,
    skipGuide,
    completeGuide,
    isGuideCompleted,
    markGuideCompleted
  };

  return (
    <UserGuideContext.Provider value={value}>
      {children}
      <GuideOverlay />
    </UserGuideContext.Provider>
  );
}

// 引导遮罩组件
function GuideOverlay() {
  const context = React.useContext(UserGuideContext);
  if (!context) return null;

  const { activeGuide, currentStep } = context;
  if (!activeGuide) return null;

  const guideManager = GuideManager.getInstance();
  const guide = guideManager.getGuide(activeGuide);
  if (!guide) return null;

  const step = guide.steps[currentStep];
  if (!step) return null;

  return createPortal(
    <GuideStepOverlay step={step} guide={guide} />,
    document.body
  );
}

// 引导步骤遮罩
function GuideStepOverlay({ step, guide }: { step: GuideStep; guide: GuideConfig }) {
  const context = React.useContext(UserGuideContext)!;
  const [targetRect, setTargetRect] = useState<DOMRect | null>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const updateTargetRect = () => {
      const target = document.querySelector(step.target);
      if (target) {
        setTargetRect(target.getBoundingClientRect());
        setIsVisible(true);
      } else {
        setIsVisible(false);
      }
    };

    updateTargetRect();
    window.addEventListener('resize', updateTargetRect);
    window.addEventListener('scroll', updateTargetRect);

    return () => {
      window.removeEventListener('resize', updateTargetRect);
      window.removeEventListener('scroll', updateTargetRect);
    };
  }, [step.target]);

  if (!isVisible || !targetRect) return null;

  const getTooltipPosition = () => {
    const position = step.position || 'bottom';
    const spacing = 16;

    switch (position) {
      case 'top':
        return {
          top: targetRect.top - spacing,
          left: targetRect.left + targetRect.width / 2,
          transform: 'translate(-50%, -100%)'
        };
      case 'bottom':
        return {
          top: targetRect.bottom + spacing,
          left: targetRect.left + targetRect.width / 2,
          transform: 'translate(-50%, 0)'
        };
      case 'left':
        return {
          top: targetRect.top + targetRect.height / 2,
          left: targetRect.left - spacing,
          transform: 'translate(-100%, -50%)'
        };
      case 'right':
        return {
          top: targetRect.top + targetRect.height / 2,
          left: targetRect.right + spacing,
          transform: 'translate(0, -50%)'
        };
      case 'center':
        return {
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)'
        };
      default:
        return {
          top: targetRect.bottom + spacing,
          left: targetRect.left + targetRect.width / 2,
          transform: 'translate(-50%, 0)'
        };
    }
  };

  return (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 z-50"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
      >
        {/* 背景遮罩 */}
        <div className="absolute inset-0 bg-black bg-opacity-50" />
        
        {/* 高亮区域 */}
        <div
          className="absolute border-4 border-system-blue rounded-lg shadow-lg"
          style={{
            top: targetRect.top - 4,
            left: targetRect.left - 4,
            width: targetRect.width + 8,
            height: targetRect.height + 8,
            boxShadow: '0 0 0 9999px rgba(0, 0, 0, 0.5)'
          }}
        />

        {/* 提示框 */}
        <motion.div
          className="absolute bg-white rounded-lg shadow-xl p-6 max-w-sm"
          style={getTooltipPosition()}
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.8, opacity: 0 }}
        >
          {/* 进度指示器 */}
          {guide.showProgress && (
            <div className="mb-4">
              <div className="flex justify-between text-sm text-secondary-label mb-2">
                <span>步骤 {context.currentStep + 1}</span>
                <span>共 {guide.steps.length} 步</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-system-blue h-2 rounded-full transition-all duration-300"
                  style={{ width: `${((context.currentStep + 1) / guide.steps.length) * 100}%` }}
                />
              </div>
            </div>
          )}

          {/* 标题和内容 */}
          <h3 className="text-lg font-semibold text-label mb-2">{step.title}</h3>
          <p className="text-secondary-label mb-4">{step.content}</p>

          {/* 操作按钮 */}
          <div className="flex justify-between items-center">
            <div className="flex space-x-2">
              {context.currentStep > 0 && (
                <button
                  onClick={context.prevStep}
                  className="px-3 py-1 text-sm text-secondary-label hover:text-label"
                >
                  上一步
                </button>
              )}
              {guide.allowSkip && (
                <button
                  onClick={context.skipGuide}
                  className="px-3 py-1 text-sm text-secondary-label hover:text-label"
                >
                  跳过
                </button>
              )}
            </div>

            <div className="flex space-x-2">
              {step.action && (
                <button
                  onClick={step.action.onClick}
                  className="px-4 py-2 text-sm bg-system-blue text-white rounded hover:bg-blue-600"
                >
                  {step.action.label}
                </button>
              )}
              <button
                onClick={context.nextStep}
                className="px-4 py-2 text-sm bg-system-blue text-white rounded hover:bg-blue-600"
              >
                {context.currentStep === guide.steps.length - 1 ? '完成' : '下一步'}
              </button>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}

// 工具提示组件
export function Tooltip({
  children,
  content,
  type = 'info',
  position = 'top',
  trigger = 'hover',
  delay = 200,
  className = ''
}: TooltipProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [timeoutId, setTimeoutId] = useState<NodeJS.Timeout | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const showTooltip = () => {
    if (timeoutId) clearTimeout(timeoutId);
    const id = setTimeout(() => setIsVisible(true), delay);
    setTimeoutId(id);
  };

  const hideTooltip = () => {
    if (timeoutId) clearTimeout(timeoutId);
    setIsVisible(false);
  };

  const toggleTooltip = () => {
    setIsVisible(prev => !prev);
  };

  const getEventHandlers = () => {
    switch (trigger) {
      case 'hover':
        return {
          onMouseEnter: showTooltip,
          onMouseLeave: hideTooltip
        };
      case 'click':
        return {
          onClick: toggleTooltip
        };
      case 'focus':
        return {
          onFocus: showTooltip,
          onBlur: hideTooltip
        };
      default:
        return {};
    }
  };

  const getTypeStyles = () => {
    switch (type) {
      case 'success':
        return 'bg-green-600 text-white';
      case 'warning':
        return 'bg-yellow-600 text-white';
      case 'error':
        return 'bg-red-600 text-white';
      default:
        return 'bg-gray-800 text-white';
    }
  };

  return (
    <div ref={containerRef} className={`relative inline-block ${className}`} {...getEventHandlers()}>
      {children}
      <AnimatePresence>
        {isVisible && (
          <motion.div
            className={`absolute z-50 px-2 py-1 text-sm rounded shadow-lg whitespace-nowrap ${getTypeStyles()}`}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            style={{
              [position]: '100%',
              ...(position === 'top' || position === 'bottom' ? {
                left: '50%',
                transform: 'translateX(-50%)'
              } : {
                top: '50%',
                transform: 'translateY(-50%)'
              })
            }}
          >
            {content}
            {/* 箭头 */}
            <div
              className={`absolute w-2 h-2 bg-inherit transform rotate-45 ${
                position === 'top' ? 'bottom-0 left-1/2 -translate-x-1/2 translate-y-1/2' :
                position === 'bottom' ? 'top-0 left-1/2 -translate-x-1/2 -translate-y-1/2' :
                position === 'left' ? 'right-0 top-1/2 translate-x-1/2 -translate-y-1/2' :
                'left-0 top-1/2 -translate-x-1/2 -translate-y-1/2'
              }`}
            />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

// Hook for using user guide
export function useUserGuide() {
  const context = React.useContext(UserGuideContext);
  if (context === undefined) {
    throw new Error('useUserGuide must be used within a UserGuideProvider');
  }
  return context;
}
