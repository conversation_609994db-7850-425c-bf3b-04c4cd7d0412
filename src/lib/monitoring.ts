/**
 * 监控和告警系统
 * 提供应用性能监控、错误追踪和告警功能
 */

// Sentry 错误监控集成
let Sentry: any = null;

// 初始化 Sentry
async function initializeSentry() {
  if (typeof window === 'undefined' || Sentry) return;

  try {
    if (process.env.NEXT_PUBLIC_SENTRY_DSN) {
      const { init, captureException, captureMessage, setUser, setTag, setContext } = await import('@sentry/nextjs');

      init({
        dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
        environment: process.env.NODE_ENV,
        tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
        debug: process.env.NODE_ENV === 'development',
        beforeSend(event) {
          // 过滤敏感信息
          if (event.exception) {
            const error = event.exception.values?.[0];
            if (error?.value?.includes('password') || error?.value?.includes('token')) {
              return null;
            }
          }
          return event;
        },
      });

      Sentry = { captureException, captureMessage, setUser, setTag, setContext };
    }
  } catch (error) {
    console.warn('Failed to initialize Sentry:', error);
  }
}

// 初始化错误监控
if (typeof window !== 'undefined') {
  initializeSentry();
}

export interface MetricData {
  name: string;
  value: number;
  timestamp: number;
  tags?: Record<string, string>;
  unit?: string;
}

export interface ErrorData {
  message: string;
  stack?: string;
  url?: string;
  userAgent?: string;
  userId?: string;
  timestamp: number;
  level: 'error' | 'warning' | 'info';
  context?: Record<string, any>;
}

export interface PerformanceData {
  metric: string;
  value: number;
  timestamp: number;
  page?: string;
  userAgent?: string;
}

/**
 * 监控配置
 */
export const MONITORING_CONFIG = {
  // 性能阈值
  performance: {
    pageLoadTime: 3000, // 3秒
    apiResponseTime: 1000, // 1秒
    firstContentfulPaint: 2000, // 2秒
    largestContentfulPaint: 4000, // 4秒
    cumulativeLayoutShift: 0.1,
    firstInputDelay: 100, // 100ms
  },
  
  // 错误率阈值
  errorRate: {
    warning: 0.01, // 1%
    critical: 0.05, // 5%
  },
  
  // 采样率
  sampling: {
    performance: 0.1, // 10%
    errors: 1.0, // 100%
    userActions: 0.05, // 5%
  },
  
  // 批量发送配置
  batch: {
    size: 50,
    timeout: 5000, // 5秒
  },
};

/**
 * 监控客户端
 */
class MonitoringClient {
  private metrics: MetricData[] = [];
  private errors: ErrorData[] = [];
  private performance: PerformanceData[] = [];
  private batchTimer: NodeJS.Timeout | null = null;

  constructor(private config = MONITORING_CONFIG) {
    this.setupErrorHandling();
    this.setupPerformanceMonitoring();
    this.startBatchTimer();
  }

  /**
   * 记录指标
   */
  recordMetric(name: string, value: number, tags?: Record<string, string>, unit?: string) {
    const metric: MetricData = {
      name,
      value,
      timestamp: Date.now(),
      tags,
      unit,
    };

    this.metrics.push(metric);
    this.checkBatchSize();
  }

  /**
   * 记录错误
   */
  recordError(error: Error | string, level: 'error' | 'warning' | 'info' = 'error', context?: Record<string, any>) {
    const errorData: ErrorData = {
      message: typeof error === 'string' ? error : error.message,
      stack: typeof error === 'object' ? error.stack : undefined,
      url: typeof window !== 'undefined' ? window.location.href : undefined,
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : undefined,
      timestamp: Date.now(),
      level,
      context,
    };

    this.errors.push(errorData);
    this.checkBatchSize();

    // 立即发送严重错误
    if (level === 'error') {
      this.sendBatch();
    }
  }

  /**
   * 记录性能数据
   */
  recordPerformance(metric: string, value: number, page?: string) {
    // 采样检查
    if (Math.random() > this.config.sampling.performance) {
      return;
    }

    const perfData: PerformanceData = {
      metric,
      value,
      timestamp: Date.now(),
      page,
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : undefined,
    };

    this.performance.push(perfData);
    this.checkBatchSize();
  }

  /**
   * 设置错误处理
   */
  private setupErrorHandling() {
    if (typeof window === 'undefined') return;

    // 全局错误处理
    window.addEventListener('error', (event) => {
      this.recordError(event.error || event.message, 'error', {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
      });
    });

    // Promise 错误处理
    window.addEventListener('unhandledrejection', (event) => {
      this.recordError(event.reason, 'error', {
        type: 'unhandledrejection',
      });
    });
  }

  /**
   * 设置性能监控
   */
  private setupPerformanceMonitoring() {
    if (typeof window === 'undefined' || !window.performance) return;

    // 页面加载性能
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        
        if (navigation) {
          this.recordPerformance('page_load_time', navigation.loadEventEnd - navigation.fetchStart);
          this.recordPerformance('dom_content_loaded', navigation.domContentLoadedEventEnd - navigation.fetchStart);
          this.recordPerformance('first_byte', navigation.responseStart - navigation.fetchStart);
        }

        // Web Vitals
        this.measureWebVitals();
      }, 0);
    });

    // API 请求监控
    this.interceptFetch();
  }

  /**
   * 测量 Web Vitals
   */
  private measureWebVitals() {
    if (typeof window === 'undefined') return;

    // FCP (First Contentful Paint)
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.name === 'first-contentful-paint') {
          this.recordPerformance('first_contentful_paint', entry.startTime);
        }
      }
    });

    try {
      observer.observe({ entryTypes: ['paint'] });
    } catch (e) {
      // 浏览器不支持
    }

    // LCP (Largest Contentful Paint)
    try {
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        this.recordPerformance('largest_contentful_paint', lastEntry.startTime);
      });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
    } catch (e) {
      // 浏览器不支持
    }

    // FID (First Input Delay)
    try {
      const fidObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.recordPerformance('first_input_delay', (entry as any).processingStart - entry.startTime);
        }
      });
      fidObserver.observe({ entryTypes: ['first-input'] });
    } catch (e) {
      // 浏览器不支持
    }
  }

  /**
   * 拦截 Fetch 请求
   */
  private interceptFetch() {
    if (typeof window === 'undefined' || !window.fetch) return;

    const originalFetch = window.fetch;
    
    window.fetch = async (...args) => {
      const startTime = performance.now();
      const url = typeof args[0] === 'string' ? args[0] : args[0].url;
      
      try {
        const response = await originalFetch(...args);
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        // 记录 API 响应时间
        if (url.includes('/api/')) {
          this.recordPerformance('api_response_time', duration, url);
          
          // 检查响应时间阈值
          if (duration > this.config.performance.apiResponseTime) {
            this.recordError(`Slow API response: ${url} took ${duration}ms`, 'warning', {
              url,
              duration,
              status: response.status,
            });
          }
        }
        
        // 记录错误响应
        if (!response.ok) {
          this.recordError(`HTTP ${response.status}: ${url}`, 'warning', {
            url,
            status: response.status,
            statusText: response.statusText,
          });
        }
        
        return response;
      } catch (error) {
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        this.recordError(error as Error, 'error', {
          url,
          duration,
          type: 'fetch_error',
        });
        
        throw error;
      }
    };
  }

  /**
   * 检查批量大小
   */
  private checkBatchSize() {
    const totalItems = this.metrics.length + this.errors.length + this.performance.length;
    
    if (totalItems >= this.config.batch.size) {
      this.sendBatch();
    }
  }

  /**
   * 启动批量定时器
   */
  private startBatchTimer() {
    this.batchTimer = setInterval(() => {
      this.sendBatch();
    }, this.config.batch.timeout);
  }

  /**
   * 发送批量数据
   */
  private async sendBatch() {
    if (this.metrics.length === 0 && this.errors.length === 0 && this.performance.length === 0) {
      return;
    }

    const batch = {
      metrics: [...this.metrics],
      errors: [...this.errors],
      performance: [...this.performance],
      timestamp: Date.now(),
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : undefined,
      url: typeof window !== 'undefined' ? window.location.href : undefined,
    };

    // 清空缓存
    this.metrics = [];
    this.errors = [];
    this.performance = [];

    try {
      // 发送到监控服务
      await this.sendToMonitoringService(batch);
    } catch (error) {
      console.error('Failed to send monitoring data:', error);
    }
  }

  /**
   * 发送到监控服务
   */
  private async sendToMonitoringService(batch: any) {
    // 发送到多个监控服务
    const promises = [];

    // 发送到自定义监控 API
    promises.push(this.sendToCustomAPI(batch));

    // 发送到 Sentry（如果配置了）
    if (process.env.NEXT_PUBLIC_SENTRY_DSN) {
      promises.push(this.sendToSentry(batch));
    }

    // 发送到 Google Analytics（如果配置了）
    if (process.env.NEXT_PUBLIC_GA_ID) {
      promises.push(this.sendToGA(batch));
    }

    await Promise.allSettled(promises);
  }

  /**
   * 发送到自定义 API
   */
  private async sendToCustomAPI(batch: any) {
    try {
      await fetch('/api/monitoring', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(batch),
      });
    } catch (error) {
      console.error('Failed to send to custom API:', error);
    }
  }

  /**
   * 发送到 Sentry
   */
  private async sendToSentry(batch: any) {
    if (!Sentry) return;

    try {
      // 发送错误数据
      batch.errors?.forEach((error: ErrorData) => {
        if (error.level === 'error') {
          Sentry.captureException(new Error(error.message), {
            tags: {
              url: error.url,
              userAgent: error.userAgent,
            },
            contexts: {
              error: error.context,
            },
            user: error.userId ? { id: error.userId } : undefined,
          });
        } else {
          Sentry.captureMessage(error.message, error.level, {
            tags: {
              url: error.url,
              userAgent: error.userAgent,
            },
            contexts: {
              error: error.context,
            },
            user: error.userId ? { id: error.userId } : undefined,
          });
        }
      });

      // 发送性能数据
      batch.performance?.forEach((perf: PerformanceData) => {
        Sentry.setTag('performance_metric', perf.metric);
        Sentry.setContext('performance', {
          metric: perf.metric,
          value: perf.value,
          page: perf.page,
          timestamp: perf.timestamp,
        });
      });

    } catch (error) {
      console.error('Failed to send to Sentry:', error);
    }
  }

  /**
   * 发送到 Google Analytics
   */
  private async sendToGA(batch: any) {
    // 这里可以集成 Google Analytics
    // 发送自定义事件和指标
  }

  /**
   * 清理资源
   */
  destroy() {
    if (this.batchTimer) {
      clearInterval(this.batchTimer);
      this.batchTimer = null;
    }
    
    // 发送剩余数据
    this.sendBatch();
  }
}

// 全局监控实例
let monitoringInstance: MonitoringClient | null = null;

/**
 * 获取监控实例
 */
export function getMonitoring(): MonitoringClient {
  if (!monitoringInstance) {
    monitoringInstance = new MonitoringClient();
  }
  return monitoringInstance;
}

/**
 * 便捷函数
 */
export const monitoring = {
  metric: (name: string, value: number, tags?: Record<string, string>, unit?: string) => {
    getMonitoring().recordMetric(name, value, tags, unit);
  },
  
  error: (error: Error | string, context?: Record<string, any>) => {
    getMonitoring().recordError(error, 'error', context);
  },
  
  warning: (message: string, context?: Record<string, any>) => {
    getMonitoring().recordError(message, 'warning', context);
  },
  
  info: (message: string, context?: Record<string, any>) => {
    getMonitoring().recordError(message, 'info', context);
  },
  
  performance: (metric: string, value: number, page?: string) => {
    getMonitoring().recordPerformance(metric, value, page);
  },
};

/**
 * React Hook for monitoring
 */
export function useMonitoring() {
  return monitoring;
}
