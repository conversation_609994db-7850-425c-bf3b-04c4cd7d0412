/**
 * 统一的日志工具类
 * 提供生产环境安全的日志记录功能
 */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  CRITICAL = 4,
}

export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  context?: Record<string, any>;
  error?: Error;
  userId?: string;
  sessionId?: string;
  requestId?: string;
}

export interface LoggerConfig {
  level: LogLevel;
  enableConsole: boolean;
  enableRemote: boolean;
  remoteEndpoint?: string;
  maxBufferSize: number;
  flushInterval: number;
  enableSensitiveDataMasking: boolean;
}

class Logger {
  private config: LoggerConfig;
  private buffer: LogEntry[] = [];
  private flushTimer?: NodeJS.Timeout;

  constructor(config?: Partial<LoggerConfig>) {
    this.config = {
      level: process.env.NODE_ENV === 'production' ? LogLevel.WARN : LogLevel.DEBUG,
      enableConsole: process.env.NODE_ENV !== 'production',
      enableRemote: process.env.NODE_ENV === 'production',
      remoteEndpoint: process.env.LOG_ENDPOINT,
      maxBufferSize: 100,
      flushInterval: 5000, // 5 seconds
      enableSensitiveDataMasking: true,
      ...config,
    };

    if (this.config.enableRemote && this.config.flushInterval > 0) {
      this.startFlushTimer();
    }
  }

  /**
   * 调试级别日志
   */
  debug(message: string, context?: Record<string, any>): void {
    this.log(LogLevel.DEBUG, message, context);
  }

  /**
   * 信息级别日志
   */
  info(message: string, context?: Record<string, any>): void {
    this.log(LogLevel.INFO, message, context);
  }

  /**
   * 警告级别日志
   */
  warn(message: string, context?: Record<string, any>): void {
    this.log(LogLevel.WARN, message, context);
  }

  /**
   * 错误级别日志
   */
  error(message: string, error?: Error, context?: Record<string, any>): void {
    this.log(LogLevel.ERROR, message, context, error);
  }

  /**
   * 严重错误级别日志
   */
  critical(message: string, error?: Error, context?: Record<string, any>): void {
    this.log(LogLevel.CRITICAL, message, context, error);
  }

  /**
   * 记录日志
   */
  private log(
    level: LogLevel,
    message: string,
    context?: Record<string, any>,
    error?: Error
  ): void {
    if (level < this.config.level) {
      return;
    }

    const logEntry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message: this.config.enableSensitiveDataMasking ? this.maskSensitiveData(message) : message,
      context: this.config.enableSensitiveDataMasking ? this.maskSensitiveContext(context) : context,
      error,
      userId: this.getCurrentUserId(),
      sessionId: this.getCurrentSessionId(),
      requestId: this.getCurrentRequestId(),
    };

    // 控制台输出
    if (this.config.enableConsole) {
      this.logToConsole(logEntry);
    }

    // 远程日志
    if (this.config.enableRemote) {
      this.addToBuffer(logEntry);
    }
  }

  /**
   * 控制台输出
   */
  private logToConsole(entry: LogEntry): void {
    const prefix = `[${entry.timestamp}] [${LogLevel[entry.level]}]`;
    const message = `${prefix} ${entry.message}`;

    switch (entry.level) {
      case LogLevel.DEBUG:
        // 在生产环境中不输出 debug 日志
        if (process.env.NODE_ENV !== 'production') {
          console.debug(message, entry.context);
        }
        break;
      case LogLevel.INFO:
        if (process.env.NODE_ENV !== 'production') {
          console.info(message, entry.context);
        }
        break;
      case LogLevel.WARN:
        console.warn(message, entry.context);
        break;
      case LogLevel.ERROR:
      case LogLevel.CRITICAL:
        console.error(message, entry.error || entry.context);
        break;
    }
  }

  /**
   * 添加到缓冲区
   */
  private addToBuffer(entry: LogEntry): void {
    this.buffer.push(entry);

    if (this.buffer.length >= this.config.maxBufferSize) {
      this.flush();
    }
  }

  /**
   * 刷新日志到远程服务
   */
  private async flush(): Promise<void> {
    if (this.buffer.length === 0 || !this.config.remoteEndpoint) {
      return;
    }

    const logs = [...this.buffer];
    this.buffer = [];

    try {
      await fetch(this.config.remoteEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ logs }),
      });
    } catch (error) {
      // 如果远程日志失败，将日志重新加入缓冲区
      this.buffer.unshift(...logs);
      
      // 在开发环境中输出错误
      if (process.env.NODE_ENV !== 'production') {
        console.error('Failed to send logs to remote endpoint:', error);
      }
    }
  }

  /**
   * 启动定时刷新
   */
  private startFlushTimer(): void {
    this.flushTimer = setInterval(() => {
      this.flush();
    }, this.config.flushInterval);
  }

  /**
   * 停止日志记录
   */
  async stop(): Promise<void> {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = undefined;
    }
    await this.flush();
  }

  /**
   * 掩码敏感数据
   */
  private maskSensitiveData(message: string): string {
    const sensitivePatterns = [
      /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, // Email
      /\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b/g, // Credit card
      /\b0x[a-fA-F0-9]{40}\b/g, // Ethereum address
      /\b[13][a-km-zA-HJ-NP-Z1-9]{25,34}\b/g, // Bitcoin address
      /password|token|secret|key/gi, // Sensitive keywords
    ];

    let maskedMessage = message;
    sensitivePatterns.forEach(pattern => {
      maskedMessage = maskedMessage.replace(pattern, '***MASKED***');
    });

    return maskedMessage;
  }

  /**
   * 掩码敏感上下文数据
   */
  private maskSensitiveContext(context?: Record<string, any>): Record<string, any> | undefined {
    if (!context) return context;

    const sensitiveKeys = ['password', 'token', 'secret', 'key', 'privateKey', 'mnemonic', 'seed'];
    const masked = { ...context };

    Object.keys(masked).forEach(key => {
      if (sensitiveKeys.some(sensitive => key.toLowerCase().includes(sensitive))) {
        masked[key] = '***MASKED***';
      }
    });

    return masked;
  }

  /**
   * 获取当前用户ID
   */
  private getCurrentUserId(): string | undefined {
    // 这里可以从请求上下文或全局状态中获取用户ID
    return undefined;
  }

  /**
   * 获取当前会话ID
   */
  private getCurrentSessionId(): string | undefined {
    // 这里可以从请求上下文中获取会话ID
    return undefined;
  }

  /**
   * 获取当前请求ID
   */
  private getCurrentRequestId(): string | undefined {
    // 这里可以从请求上下文中获取请求ID
    return undefined;
  }
}

// 创建全局日志实例
export const logger = new Logger();

// 导出便捷方法
export const log = {
  debug: (message: string, context?: Record<string, any>) => logger.debug(message, context),
  info: (message: string, context?: Record<string, any>) => logger.info(message, context),
  warn: (message: string, context?: Record<string, any>) => logger.warn(message, context),
  error: (message: string, error?: Error, context?: Record<string, any>) => logger.error(message, error, context),
  critical: (message: string, error?: Error, context?: Record<string, any>) => logger.critical(message, error, context),
};

// 在应用关闭时清理资源
if (typeof window === 'undefined') {
  process.on('SIGTERM', async () => {
    await logger.stop();
  });

  process.on('SIGINT', async () => {
    await logger.stop();
  });
}
