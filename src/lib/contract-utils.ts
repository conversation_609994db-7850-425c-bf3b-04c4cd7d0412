/**
 * 合约工具函数
 * 提供安全的合约创建和地址验证
 */

import { ethers } from 'ethers';

/**
 * 严格验证以太坊地址
 */
export function isValidEthereumAddress(address: string | undefined | null): boolean {
  if (!address || typeof address !== 'string') {
    return false;
  }
  
  // 检查基本格式
  if (address === '' || address === '0x' || address.length !== 42) {
    return false;
  }
  
  // 检查是否以0x开头
  if (!address.startsWith('0x')) {
    return false;
  }
  
  // 使用ethers验证
  try {
    return ethers.isAddress(address);
  } catch {
    return false;
  }
}

/**
 * 安全创建合约实例
 * 防止ENS错误
 */
export function safeCreateContract(
  address: string | undefined | null, 
  abi: any[], 
  provider: any
): any | null {
  if (!isValidEthereumAddress(address)) {
    console.warn('Invalid contract address, skipping contract creation:', address);
    return null;
  }
  
  try {
    return new ethers.Contract(address!, abi, provider);
  } catch (error) {
    console.error('Failed to create contract:', error);
    return null;
  }
}

/**
 * 获取有效的合约地址
 */
export function getValidContractAddress(
  envAddress: string | undefined, 
  fallbackAddress: string
): string {
  if (isValidEthereumAddress(envAddress)) {
    return envAddress!;
  }
  
  if (isValidEthereumAddress(fallbackAddress)) {
    return fallbackAddress;
  }
  
  throw new Error('No valid contract address available');
}

/**
 * 安全获取环境变量中的合约地址
 */
export function getSafeContractAddress(envVarName: string, fallback: string): string {
  const envValue = process.env[envVarName];
  
  if (isValidEthereumAddress(envValue)) {
    return envValue!;
  }
  
  if (isValidEthereumAddress(fallback)) {
    return fallback;
  }
  
  // 返回一个有效的默认地址，避免空字符串
  return '******************************************';
}

/**
 * 批量验证合约地址
 */
export function validateContractAddresses(addresses: Record<string, string>): {
  valid: Record<string, string>;
  invalid: string[];
} {
  const valid: Record<string, string> = {};
  const invalid: string[] = [];
  
  for (const [name, address] of Object.entries(addresses)) {
    if (isValidEthereumAddress(address)) {
      valid[name] = address;
    } else {
      invalid.push(name);
    }
  }
  
  return { valid, invalid };
}

/**
 * 创建安全的合约配置
 */
export function createSafeContractConfig() {
  const defaultAddress = '******************************************';
  
  return {
    HAOX_TOKEN: getSafeContractAddress('NEXT_PUBLIC_HAOX_TOKEN_ADDRESS', defaultAddress),
    HAOX_PRESALE: getSafeContractAddress('NEXT_PUBLIC_HAOX_PRESALE_ADDRESS', '******************************************'),
    HAOX_INVITATION: getSafeContractAddress('NEXT_PUBLIC_HAOX_INVITATION_ADDRESS', '******************************************'),
    HAOX_PRICE_ORACLE: getSafeContractAddress('NEXT_PUBLIC_HAOX_PRICE_ORACLE_ADDRESS', '******************************************'),
    HAOX_VESTING: getSafeContractAddress('NEXT_PUBLIC_HAOX_VESTING_ADDRESS', '******************************************'),
  };
}
