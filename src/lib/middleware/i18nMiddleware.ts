import { NextRequest } from 'next/server';
import { locales, defaultLocale, isValidLocale, type Locale } from '@/lib/i18n/config';

// 从请求中获取语言偏好
export function getLocaleFromRequest(request: NextRequest): Locale {
  // 1. 检查URL参数中的语言设置
  const url = new URL(request.url);
  const langParam = url.searchParams.get('lang');
  if (langParam && isValidLocale(langParam)) {
    return langParam;
  }

  // 2. 检查路径中的语言代码
  const pathname = url.pathname;
  const segments = pathname.split('/');
  if (segments.length > 1 && isValidLocale(segments[1])) {
    return segments[1];
  }

  // 3. 检查Accept-Language头
  const acceptLanguage = request.headers.get('accept-language');
  if (acceptLanguage) {
    const preferredLocale = parseAcceptLanguage(acceptLanguage);
    if (preferredLocale) {
      return preferredLocale;
    }
  }

  // 4. 检查Cookie中的语言设置
  const cookieLocale = request.cookies.get('locale')?.value;
  if (cookieLocale && isValidLocale(cookieLocale)) {
    return cookieLocale;
  }

  // 5. 返回默认语言
  return defaultLocale;
}

// 解析Accept-Language头
function parseAcceptLanguage(acceptLanguage: string): Locale | null {
  const languages = acceptLanguage
    .split(',')
    .map(lang => {
      const [code, qValue] = lang.trim().split(';q=');
      return {
        code: code.toLowerCase(),
        quality: qValue ? parseFloat(qValue) : 1.0
      };
    })
    .sort((a, b) => b.quality - a.quality);

  for (const lang of languages) {
    // 检查完整的语言代码
    if (isValidLocale(lang.code)) {
      return lang.code;
    }
    
    // 检查语言代码的前缀
    const prefix = lang.code.split('-')[0];
    if (isValidLocale(prefix)) {
      return prefix;
    }
    
    // 特殊处理中文
    if (lang.code.startsWith('zh')) {
      return 'zh';
    }
    
    // 特殊处理英文
    if (lang.code.startsWith('en')) {
      return 'en';
    }
  }

  return null;
}

// 国际化消息加载器
export class I18nMessageLoader {
  private static cache = new Map<string, any>();

  static async loadMessages(locale: Locale): Promise<any> {
    // 检查缓存
    if (this.cache.has(locale)) {
      return this.cache.get(locale);
    }

    try {
      // 动态导入语言包
      const messages = await import(`../i18n/messages/${locale}.json`);
      this.cache.set(locale, messages.default);
      return messages.default;
    } catch (error) {
      console.error(`Failed to load messages for locale ${locale}:`, error);
      
      // 如果加载失败，尝试加载默认语言
      if (locale !== defaultLocale) {
        return this.loadMessages(defaultLocale);
      }
      
      // 返回空对象作为后备
      return {};
    }
  }

  static clearCache() {
    this.cache.clear();
  }
}

// API响应国际化
export class ApiI18n {
  private messages: any;
  private locale: Locale;

  constructor(messages: any, locale: Locale) {
    this.messages = messages;
    this.locale = locale;
  }

  // 获取翻译文本
  t(key: string, params?: Record<string, any>): string {
    const keys = key.split('.');
    let value = this.messages;

    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        return key; // 如果找不到翻译，返回原始key
      }
    }

    if (typeof value !== 'string') {
      return key;
    }

    // 简单的参数替换
    if (params) {
      return value.replace(/\{(\w+)\}/g, (match, paramKey) => {
        return params[paramKey] || match;
      });
    }

    return value;
  }

  // 获取错误消息
  getErrorMessage(errorKey: string, fallback?: string): string {
    const message = this.t(`errors.${errorKey}`);
    if (message === `errors.${errorKey}`) {
      return fallback || this.t('errors.general');
    }
    return message;
  }

  // 获取成功消息
  getSuccessMessage(successKey: string, fallback?: string): string {
    const message = this.t(`success.${successKey}`);
    if (message === `success.${successKey}`) {
      return fallback || this.t('success.general');
    }
    return message;
  }

  // 格式化数字
  formatNumber(value: number, options?: Intl.NumberFormatOptions): string {
    const localeCode = this.locale === 'zh' ? 'zh-CN' : 'en-US';
    return new Intl.NumberFormat(localeCode, options).format(value);
  }

  // 格式化日期
  formatDate(date: Date | string, options?: Intl.DateTimeFormatOptions): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const localeCode = this.locale === 'zh' ? 'zh-CN' : 'en-US';
    
    const defaultOptions: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    };
    
    return new Intl.DateTimeFormat(localeCode, { ...defaultOptions, ...options }).format(dateObj);
  }

  // 获取当前语言
  getLocale(): Locale {
    return this.locale;
  }
}

// 创建API国际化实例
export async function createApiI18n(request: NextRequest): Promise<ApiI18n> {
  const locale = getLocaleFromRequest(request);
  const messages = await I18nMessageLoader.loadMessages(locale);
  return new ApiI18n(messages, locale);
}

// API响应国际化包装器
export function withI18nApi<T = any>(
  handler: (request: NextRequest, i18n: ApiI18n, context?: any) => Promise<Response>
) {
  return async (request: NextRequest, context?: any): Promise<Response> => {
    const i18n = await createApiI18n(request);
    return handler(request, i18n, context);
  };
}
