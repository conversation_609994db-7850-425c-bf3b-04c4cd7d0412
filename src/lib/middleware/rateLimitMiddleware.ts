/**
 * 速率限制和并发控制中间件
 * 实现高并发负载均衡和请求限流
 */

import { NextRequest, NextResponse } from 'next/server';
import { LRUCache } from 'lru-cache';

// 速率限制配置
interface RateLimitConfig {
  windowMs: number; // 时间窗口（毫秒）
  maxRequests: number; // 最大请求数
  message?: string; // 限制消息
  skipSuccessfulRequests?: boolean; // 跳过成功请求
  skipFailedRequests?: boolean; // 跳过失败请求
}

// 并发控制配置
interface ConcurrencyConfig {
  maxConcurrent: number; // 最大并发数
  queueSize: number; // 队列大小
  timeout: number; // 超时时间（毫秒）
}

// 默认配置
const DEFAULT_RATE_LIMIT: RateLimitConfig = {
  windowMs: 15 * 60 * 1000, // 15分钟
  maxRequests: 100,
  message: 'Too many requests, please try again later.'
};

const DEFAULT_CONCURRENCY: ConcurrencyConfig = {
  maxConcurrent: 50,
  queueSize: 100,
  timeout: 30000 // 30秒
};

// 速率限制存储
const rateLimitStore = new LRUCache<string, {
  count: number;
  resetTime: number;
}>({
  max: 10000,
  ttl: 15 * 60 * 1000 // 15分钟
});

// 并发控制
class ConcurrencyController {
  private activeRequests = 0;
  private queue: Array<{
    resolve: (value: any) => void;
    reject: (reason: any) => void;
    timeout: NodeJS.Timeout;
  }> = [];
  
  constructor(private config: ConcurrencyConfig) {}
  
  async acquire(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.activeRequests < this.config.maxConcurrent) {
        this.activeRequests++;
        resolve(undefined);
        return;
      }
      
      if (this.queue.length >= this.config.queueSize) {
        reject(new Error('Queue is full'));
        return;
      }
      
      const timeout = setTimeout(() => {
        const index = this.queue.findIndex(item => item.timeout === timeout);
        if (index !== -1) {
          this.queue.splice(index, 1);
          reject(new Error('Request timeout'));
        }
      }, this.config.timeout);
      
      this.queue.push({ resolve, reject, timeout });
    });
  }
  
  release(): void {
    this.activeRequests--;
    
    if (this.queue.length > 0) {
      const next = this.queue.shift();
      if (next) {
        clearTimeout(next.timeout);
        this.activeRequests++;
        next.resolve(undefined);
      }
    }
  }
  
  getStats() {
    return {
      activeRequests: this.activeRequests,
      queueLength: this.queue.length,
      maxConcurrent: this.config.maxConcurrent
    };
  }
}

// 全局并发控制器
const concurrencyController = new ConcurrencyController(DEFAULT_CONCURRENCY);

/**
 * 获取客户端标识符
 */
function getClientId(request: NextRequest): string {
  // 优先使用用户ID（如果已认证）
  const userId = request.headers.get('x-user-id');
  if (userId) {
    return `user:${userId}`;
  }
  
  // 使用IP地址
  const forwarded = request.headers.get('x-forwarded-for');
  const ip = forwarded ? forwarded.split(',')[0] : 
    request.headers.get('x-real-ip') || 
    request.ip || 
    'unknown';
  
  return `ip:${ip}`;
}

/**
 * 检查速率限制
 */
function checkRateLimit(
  clientId: string, 
  config: RateLimitConfig = DEFAULT_RATE_LIMIT
): {
  allowed: boolean;
  remaining: number;
  resetTime: number;
  retryAfter?: number;
} {
  const now = Date.now();
  const key = `${clientId}:${Math.floor(now / config.windowMs)}`;
  
  let record = rateLimitStore.get(key);
  
  if (!record) {
    record = {
      count: 0,
      resetTime: now + config.windowMs
    };
  }
  
  record.count++;
  rateLimitStore.set(key, record);
  
  const allowed = record.count <= config.maxRequests;
  const remaining = Math.max(0, config.maxRequests - record.count);
  const retryAfter = allowed ? undefined : Math.ceil((record.resetTime - now) / 1000);
  
  return {
    allowed,
    remaining,
    resetTime: record.resetTime,
    retryAfter
  };
}

/**
 * 速率限制中间件
 */
export function rateLimitMiddleware(config?: Partial<RateLimitConfig>) {
  const finalConfig = { ...DEFAULT_RATE_LIMIT, ...config };
  
  return async (
    request: NextRequest,
    handler: (req: NextRequest) => Promise<NextResponse>
  ): Promise<NextResponse> => {
    const clientId = getClientId(request);
    const rateLimit = checkRateLimit(clientId, finalConfig);
    
    // 添加速率限制头信息
    const headers = new Headers();
    headers.set('X-RateLimit-Limit', finalConfig.maxRequests.toString());
    headers.set('X-RateLimit-Remaining', rateLimit.remaining.toString());
    headers.set('X-RateLimit-Reset', rateLimit.resetTime.toString());
    
    if (!rateLimit.allowed) {
      headers.set('Retry-After', rateLimit.retryAfter!.toString());
      
      return new NextResponse(JSON.stringify({
        success: false,
        error: finalConfig.message,
        meta: {
          rateLimit: {
            limit: finalConfig.maxRequests,
            remaining: rateLimit.remaining,
            resetTime: rateLimit.resetTime,
            retryAfter: rateLimit.retryAfter
          }
        }
      }), {
        status: 429,
        headers
      });
    }
    
    // 执行处理器
    const response = await handler(request);
    
    // 添加速率限制头到响应
    for (const [key, value] of headers.entries()) {
      response.headers.set(key, value);
    }
    
    return response;
  };
}

/**
 * 并发控制中间件
 */
export function concurrencyMiddleware(config?: Partial<ConcurrencyConfig>) {
  const finalConfig = { ...DEFAULT_CONCURRENCY, ...config };
  const controller = new ConcurrencyController(finalConfig);
  
  return async (
    request: NextRequest,
    handler: (req: NextRequest) => Promise<NextResponse>
  ): Promise<NextResponse> => {
    try {
      // 获取并发许可
      await controller.acquire();
      
      // 执行处理器
      const response = await handler(request);
      
      // 释放并发许可
      controller.release();
      
      // 添加并发信息头
      response.headers.set('X-Concurrency-Active', controller.getStats().activeRequests.toString());
      response.headers.set('X-Concurrency-Queue', controller.getStats().queueLength.toString());
      
      return response;
      
    } catch (error) {
      // 确保释放许可
      controller.release();
      
      if (error instanceof Error) {
        if (error.message === 'Queue is full') {
          return new NextResponse(JSON.stringify({
            success: false,
            error: 'Server is too busy, please try again later',
            code: 'QUEUE_FULL'
          }), {
            status: 503,
            headers: {
              'Content-Type': 'application/json',
              'Retry-After': '60'
            }
          });
        }
        
        if (error.message === 'Request timeout') {
          return new NextResponse(JSON.stringify({
            success: false,
            error: 'Request timeout',
            code: 'TIMEOUT'
          }), {
            status: 408,
            headers: {
              'Content-Type': 'application/json'
            }
          });
        }
      }
      
      throw error;
    }
  };
}

/**
 * 组合中间件 - 速率限制 + 并发控制
 */
export function loadBalancingMiddleware(
  rateLimitConfig?: Partial<RateLimitConfig>,
  concurrencyConfig?: Partial<ConcurrencyConfig>
) {
  const rateLimit = rateLimitMiddleware(rateLimitConfig);
  const concurrency = concurrencyMiddleware(concurrencyConfig);
  
  return async (
    request: NextRequest,
    handler: (req: NextRequest) => Promise<NextResponse>
  ): Promise<NextResponse> => {
    // 先应用速率限制
    return rateLimit(request, async (req) => {
      // 再应用并发控制
      return concurrency(req, handler);
    });
  };
}

/**
 * 获取系统负载统计
 */
export function getLoadStats() {
  return {
    concurrency: concurrencyController.getStats(),
    rateLimit: {
      activeClients: rateLimitStore.size,
      cacheSize: rateLimitStore.calculatedSize
    },
    timestamp: new Date().toISOString()
  };
}

/**
 * 清除速率限制记录
 */
export function clearRateLimitStore(): void {
  rateLimitStore.clear();
}

/**
 * 健康检查端点数据
 */
export function getHealthCheck() {
  const stats = getLoadStats();
  const isHealthy = 
    stats.concurrency.activeRequests < stats.concurrency.maxConcurrent * 0.8 &&
    stats.concurrency.queueLength < 50;
  
  return {
    status: isHealthy ? 'healthy' : 'degraded',
    load: stats,
    timestamp: new Date().toISOString()
  };
}
