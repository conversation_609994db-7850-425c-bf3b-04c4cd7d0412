import { NextRequest, NextResponse } from 'next/server';
import { errorHandler, ErrorType, ErrorSeverity, ApiResponse } from '@/lib/services/errorHandlingService';

// API路由包装器
export function withErrorHandling<T = any>(
  handler: (request: NextRequest, context?: any) => Promise<NextResponse<ApiResponse<T>>>
) {
  return async (request: NextRequest, context?: any): Promise<NextResponse<ApiResponse<T>>> => {
    const requestId = generateRequestId();
    const startTime = Date.now();

    try {
      // 添加请求ID到headers
      const response = await handler(request, { ...context, requestId });
      
      // 记录成功请求
      logRequest(request, response.status, Date.now() - startTime, requestId);
      
      return response;
    } catch (error) {
      // 处理错误
      const standardError = errorHandler.standardizeError(error);
      standardError.requestId = requestId;
      
      // 记录失败请求
      logRequest(request, 500, Date.now() - startTime, requestId, standardError);
      
      // 创建错误响应
      const errorResponse = errorHandler.createApiResponse<T>(
        false,
        undefined,
        standardError,
        requestId
      );
      
      // 根据错误类型返回适当的HTTP状态码
      const statusCode = getHttpStatusCode(standardError.type);
      
      return NextResponse.json(errorResponse, { status: statusCode });
    }
  };
}

// 数据库操作包装器
export async function withDatabaseErrorHandling<T>(
  operation: () => Promise<T>,
  context?: Record<string, any>
): Promise<T> {
  return errorHandler.withRetry(
    async () => {
      try {
        return await operation();
      } catch (error) {
        throw errorHandler.handleDatabaseError(error as Error, context);
      }
    },
    {
      maxRetries: 3,
      baseDelay: 1000,
      retryableErrors: [ErrorType.DATABASE_ERROR, ErrorType.TIMEOUT_ERROR]
    }
  );
}

// 区块链操作包装器
export async function withBlockchainErrorHandling<T>(
  operation: () => Promise<T>,
  context?: Record<string, any>
): Promise<T> {
  return errorHandler.withRetry(
    async () => {
      try {
        return await operation();
      } catch (error) {
        throw errorHandler.handleBlockchainError(error as Error, context);
      }
    },
    {
      maxRetries: 5,
      baseDelay: 2000,
      maxDelay: 30000,
      retryableErrors: [
        ErrorType.BLOCKCHAIN_ERROR,
        ErrorType.NETWORK_ERROR,
        ErrorType.RATE_LIMIT_ERROR
      ]
    }
  );
}

// 外部服务调用包装器
export async function withExternalServiceErrorHandling<T>(
  operation: () => Promise<T>,
  serviceName: string,
  context?: Record<string, any>
): Promise<T> {
  return errorHandler.withRetry(
    async () => {
      try {
        return await operation();
      } catch (error) {
        const standardError = errorHandler.createError(
          ErrorType.EXTERNAL_SERVICE_ERROR,
          `${serviceName} 服务调用失败: ${(error as Error).message}`,
          {
            details: { serviceName },
            context,
            originalError: error as Error,
            retryable: true
          }
        );
        throw standardError;
      }
    },
    {
      maxRetries: 3,
      baseDelay: 1500,
      retryableErrors: [
        ErrorType.EXTERNAL_SERVICE_ERROR,
        ErrorType.NETWORK_ERROR,
        ErrorType.TIMEOUT_ERROR
      ]
    }
  );
}

// 优雅降级包装器
export async function withGracefulDegradation<T>(
  primaryOperation: () => Promise<T>,
  fallbackOperation: () => Promise<T>,
  operationName: string,
  context?: Record<string, any>
): Promise<T> {
  return errorHandler.withGracefulDegradation(
    primaryOperation,
    fallbackOperation,
    { operationName, ...context }
  );
}

// 验证错误处理
export function createValidationError(
  message: string,
  field?: string,
  value?: any
) {
  return errorHandler.createError(
    ErrorType.VALIDATION_ERROR,
    message,
    {
      severity: ErrorSeverity.LOW,
      details: { field, value },
      retryable: false
    }
  );
}

// 认证错误处理
export function createAuthenticationError(message: string = '认证失败') {
  return errorHandler.createError(
    ErrorType.AUTHENTICATION_ERROR,
    message,
    {
      severity: ErrorSeverity.MEDIUM,
      retryable: false
    }
  );
}

// 授权错误处理
export function createAuthorizationError(message: string = '权限不足') {
  return errorHandler.createError(
    ErrorType.AUTHORIZATION_ERROR,
    message,
    {
      severity: ErrorSeverity.MEDIUM,
      retryable: false
    }
  );
}

// 业务逻辑错误处理
export function createBusinessLogicError(
  message: string,
  code?: string,
  details?: Record<string, any>
) {
  return errorHandler.createError(
    ErrorType.BUSINESS_LOGIC_ERROR,
    message,
    {
      severity: ErrorSeverity.MEDIUM,
      code,
      details,
      retryable: false
    }
  );
}

// 速率限制错误处理
export function createRateLimitError(
  message: string = '请求过于频繁，请稍后重试',
  retryAfter?: number
) {
  return errorHandler.createError(
    ErrorType.RATE_LIMIT_ERROR,
    message,
    {
      severity: ErrorSeverity.LOW,
      details: { retryAfter },
      retryable: true
    }
  );
}

// 成功响应创建器
export function createSuccessResponse<T>(
  data: T,
  requestId?: string
): NextResponse<ApiResponse<T>> {
  const response = errorHandler.createApiResponse(true, data, undefined, requestId);
  return NextResponse.json(response);
}

// 错误响应创建器
export function createErrorResponse(
  error: any,
  requestId?: string
): NextResponse<ApiResponse> {
  const standardError = errorHandler.standardizeError(error);
  const response = errorHandler.createApiResponse(false, undefined, standardError, requestId);
  const statusCode = getHttpStatusCode(standardError.type);
  
  return NextResponse.json(response, { status: statusCode });
}

// 私有函数

function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

function getHttpStatusCode(errorType: ErrorType): number {
  const statusMap: Record<ErrorType, number> = {
    [ErrorType.VALIDATION_ERROR]: 400,
    [ErrorType.AUTHENTICATION_ERROR]: 401,
    [ErrorType.AUTHORIZATION_ERROR]: 403,
    [ErrorType.RATE_LIMIT_ERROR]: 429,
    [ErrorType.DATABASE_ERROR]: 500,
    [ErrorType.BLOCKCHAIN_ERROR]: 502,
    [ErrorType.EXTERNAL_SERVICE_ERROR]: 502,
    [ErrorType.NETWORK_ERROR]: 503,
    [ErrorType.TIMEOUT_ERROR]: 504,
    [ErrorType.BUSINESS_LOGIC_ERROR]: 422,
    [ErrorType.UNKNOWN_ERROR]: 500
  };
  
  return statusMap[errorType] || 500;
}

function logRequest(
  request: NextRequest,
  status: number,
  duration: number,
  requestId: string,
  error?: any
) {
  const logData = {
    requestId,
    method: request.method,
    url: request.url,
    status,
    duration: `${duration}ms`,
    timestamp: new Date().toISOString(),
    userAgent: request.headers.get('user-agent'),
    ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip')
  };

  if (error) {
    console.error('❌ API Request Failed:', { ...logData, error: error.message });
  } else if (status >= 400) {
    console.warn('⚠️ API Request Warning:', logData);
  } else {
    console.info('✅ API Request Success:', logData);
  }
}

// 健康检查端点
export function createHealthCheckResponse(): NextResponse<ApiResponse<any>> {
  const healthData = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: process.env.npm_package_version || '1.0.0',
    environment: process.env.NODE_ENV || 'development'
  };

  return createSuccessResponse(healthData);
}
