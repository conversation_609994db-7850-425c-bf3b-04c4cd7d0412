/**
 * API性能优化中间件
 * 提供缓存、压缩、响应时间优化等功能
 */

import { NextRequest, NextResponse } from 'next/server';
import { LRUCache } from 'lru-cache';

// 内存缓存配置
const cache = new LRUCache<string, any>({
  max: 1000, // 最大缓存条目数
  ttl: 1000 * 60 * 5, // 5分钟TTL
  allowStale: false,
  updateAgeOnGet: false,
  updateAgeOnHas: false,
});

// 性能监控数据
interface PerformanceMetrics {
  requestCount: number;
  totalResponseTime: number;
  averageResponseTime: number;
  cacheHitRate: number;
  slowRequests: number;
}

const metrics: PerformanceMetrics = {
  requestCount: 0,
  totalResponseTime: 0,
  averageResponseTime: 0,
  cacheHitRate: 0,
  slowRequests: 0
};

let cacheHits = 0;
let cacheMisses = 0;

/**
 * 生成缓存键
 */
function generateCacheKey(request: NextRequest): string {
  const url = new URL(request.url);
  const method = request.method;
  const pathname = url.pathname;
  const searchParams = url.searchParams.toString();
  
  // 对于GET请求，包含查询参数
  if (method === 'GET') {
    return `${method}:${pathname}:${searchParams}`;
  }
  
  // 对于其他请求，只使用路径
  return `${method}:${pathname}`;
}

/**
 * 检查请求是否可缓存
 */
function isCacheable(request: NextRequest): boolean {
  // 只缓存GET请求
  if (request.method !== 'GET') {
    return false;
  }
  
  // 不缓存包含认证信息的请求
  if (request.headers.get('authorization')) {
    return false;
  }
  
  // 不缓存实时数据API
  const pathname = new URL(request.url).pathname;
  const nonCacheablePaths = [
    '/api/auth',
    '/api/user/profile',
    '/api/fortune/balance',
    '/api/social-bet/active'
  ];
  
  return !nonCacheablePaths.some(path => pathname.startsWith(path));
}

/**
 * 压缩响应数据
 */
function compressResponse(data: any): string {
  const jsonString = JSON.stringify(data);
  
  // 简单的压缩：移除不必要的空格
  return jsonString.replace(/\s+/g, ' ').trim();
}

/**
 * 性能优化中间件
 */
export async function performanceMiddleware(
  request: NextRequest,
  handler: (req: NextRequest) => Promise<NextResponse>
): Promise<NextResponse> {
  const startTime = Date.now();
  const cacheKey = generateCacheKey(request);
  
  // 更新请求计数
  metrics.requestCount++;
  
  // 检查缓存
  if (isCacheable(request)) {
    const cachedResponse = cache.get(cacheKey);
    if (cachedResponse) {
      cacheHits++;
      
      // 更新缓存命中率
      metrics.cacheHitRate = cacheHits / (cacheHits + cacheMisses);
      
      // 返回缓存的响应
      return new NextResponse(JSON.stringify(cachedResponse), {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'X-Cache': 'HIT',
          'X-Response-Time': '1ms'
        }
      });
    } else {
      cacheMisses++;
    }
  }
  
  try {
    // 执行原始处理器
    const response = await handler(request);
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    // 更新性能指标
    metrics.totalResponseTime += responseTime;
    metrics.averageResponseTime = metrics.totalResponseTime / metrics.requestCount;
    
    if (responseTime > 500) {
      metrics.slowRequests++;
    }
    
    // 如果响应成功且可缓存，存储到缓存
    if (response.ok && isCacheable(request)) {
      try {
        const responseData = await response.json();
        cache.set(cacheKey, responseData);
        
        // 返回新的响应
        return new NextResponse(compressResponse(responseData), {
          status: response.status,
          headers: {
            'Content-Type': 'application/json',
            'X-Cache': 'MISS',
            'X-Response-Time': `${responseTime}ms`
          }
        });
      } catch (error) {
        // 如果解析JSON失败，返回原始响应
        return response;
      }
    }
    
    // 添加性能头信息
    const headers = new Headers(response.headers);
    headers.set('X-Response-Time', `${responseTime}ms`);
    headers.set('X-Cache', 'BYPASS');
    
    return new NextResponse(response.body, {
      status: response.status,
      headers
    });
    
  } catch (error) {
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    console.error('Performance middleware error:', error);
    
    return new NextResponse(JSON.stringify({
      success: false,
      error: 'Internal server error',
      meta: {
        responseTime: `${responseTime}ms`
      }
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'X-Response-Time': `${responseTime}ms`
      }
    });
  }
}

/**
 * 获取性能指标
 */
export function getPerformanceMetrics(): PerformanceMetrics {
  return {
    ...metrics,
    cacheHitRate: cacheHits / (cacheHits + cacheMisses) || 0
  };
}

/**
 * 清除缓存
 */
export function clearCache(): void {
  cache.clear();
  cacheHits = 0;
  cacheMisses = 0;
}

/**
 * 重置性能指标
 */
export function resetMetrics(): void {
  metrics.requestCount = 0;
  metrics.totalResponseTime = 0;
  metrics.averageResponseTime = 0;
  metrics.cacheHitRate = 0;
  metrics.slowRequests = 0;
  cacheHits = 0;
  cacheMisses = 0;
}
