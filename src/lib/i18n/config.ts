// 支持的语言列表
export const locales = ['zh', 'en'] as const;
export type Locale = typeof locales[number];

// 默认语言
export const defaultLocale: Locale = 'zh';

// 语言显示名称
export const localeNames: Record<Locale, string> = {
  zh: '中文',
  en: 'English'
};

// 语言图标
export const localeFlags: Record<Locale, string> = {
  zh: '🇨🇳',
  en: '🇺🇸'
};

// 语言检测配置
export const i18nConfig = {
  locales,
  defaultLocale,
  localeDetection: true,
  domains: [
    {
      domain: 'sociomint.com',
      defaultLocale: 'en'
    },
    {
      domain: 'sociomint.cn',
      defaultLocale: 'zh'
    }
  ]
};

// 获取浏览器语言偏好
export function getBrowserLocale(): Locale {
  if (typeof window === 'undefined') return defaultLocale;
  
  const browserLang = navigator.language.toLowerCase();
  
  if (browserLang.startsWith('zh')) return 'zh';
  if (browserLang.startsWith('en')) return 'en';
  
  return defaultLocale;
}

// 获取存储的语言偏好
export function getStoredLocale(): Locale | null {
  if (typeof window === 'undefined') return null;
  
  const stored = localStorage.getItem('locale');
  if (stored && locales.includes(stored as Locale)) {
    return stored as Locale;
  }
  
  return null;
}

// 存储语言偏好
export function setStoredLocale(locale: Locale): void {
  if (typeof window === 'undefined') return;
  
  localStorage.setItem('locale', locale);
}

// 获取用户首选语言
export function getUserPreferredLocale(): Locale {
  return getStoredLocale() || getBrowserLocale();
}

// 验证语言代码
export function isValidLocale(locale: string): locale is Locale {
  return locales.includes(locale as Locale);
}
