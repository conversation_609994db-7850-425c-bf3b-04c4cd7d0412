{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "delete": "Delete", "edit": "Edit", "view": "View", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "retry": "Retry", "refresh": "Refresh", "close": "Close", "open": "Open", "search": "Search", "filter": "Filter", "sort": "Sort", "export": "Export", "import": "Import", "copy": "Copy", "share": "Share", "more": "More", "less": "Less", "all": "All", "none": "None", "yes": "Yes", "no": "No", "optional": "Optional", "required": "Required", "total": "Total", "amount": "Amount", "balance": "Balance", "status": "Status", "type": "Type", "date": "Date", "time": "Time", "description": "Description", "title": "Title", "name": "Name", "username": "Username", "email": "Email", "phone": "Phone", "address": "Address", "website": "Website", "language": "Language", "theme": "Theme", "settings": "Settings", "profile": "Profile", "account": "Account", "dashboard": "Dashboard", "home": "Home", "about": "About", "contact": "Contact Us", "help": "Help", "faq": "FAQ", "terms": "Terms of Service", "privacy": "Privacy Policy", "login": "<PERSON><PERSON>", "logout": "Logout", "register": "Register", "forgotPassword": "Forgot Password", "resetPassword": "Reset Password", "changePassword": "Change Password"}, "navigation": {"home": "Home", "socialBet": "Social Bet", "fortune": "Fortune Center", "certification": "Certification", "traceability": "Traceability", "monitoring": "Monitoring", "admin": "Admin"}, "socialBet": {"title": "Social Bet", "subtitle": "Decentralized prediction market based on Fortune", "createBet": "Create Bet", "joinBet": "Join <PERSON>", "myBets": "My Bets", "allBets": "All Bets", "activeBets": "Active", "completedBets": "Completed", "judgingBets": "Judging", "betDetails": "Bet Details", "betHistory": "Bet History", "leaderboard": "Leaderboard", "judgmentHistory": "Judgment History", "betType": {"1v1": "1 vs 1", "1vN": "1 vs N"}, "betStatus": {"draft": "Draft", "open": "Open", "active": "Active", "judging": "Judging", "confirming": "Confirming", "settled": "Settled", "cancelled": "Cancelled"}, "createForm": {"title": "Bet Title", "titlePlaceholder": "Enter bet title", "description": "Bet Description", "descriptionPlaceholder": "Describe the bet content and rules in detail", "type": "Bet Type", "options": "Betting Options", "addOption": "Add Option", "removeOption": "Remove Option", "creatorStake": "Creator Stake", "deadline": "Deadline", "creatorOption": "Creator Choice", "transferReward": "Referral Reward", "transferRewardDesc": "Set referral reward ratio to incentivize sharing", "submit": "Create Bet"}, "judgment": {"title": "Bet Judgment", "round1": "Round 1: Public Review", "round2": "Round 2: Professional Review", "round3": "Round 3: Final Review", "voteFor": "Vote For", "voteAgainst": "Vote Against", "submitVote": "Submit <PERSON><PERSON>", "votingProgress": "Voting Progress", "threshold": "<PERSON><PERSON><PERSON><PERSON>", "timeRemaining": "Time Remaining", "judgeInfo": "Judge Information", "votingHistory": "Voting History", "consensus": "Consensus"}, "transparency": {"title": "Transparency", "judgmentProcess": "Judgment Process", "judgeList": "Judge List", "votingDetails": "Voting Details", "timeline": "Timeline", "allPublic": "All information is public and transparent"}}, "fortune": {"title": "Fortune Center", "subtitle": "Manage your Fortune assets", "balance": "Fortune Balance", "dailyCheckin": "Daily Check-in", "invite": "Invite Friends", "share": "Share Rewards", "history": "Fortune History", "exchange": "Deposit & Withdraw", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "withdraw": "Withdraw", "depositForm": {"title": "Deposit HAOX to Fortune", "amount": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "txHash": "Transaction Hash", "walletAddress": "Sender Wallet Address", "submit": "Confirm <PERSON>", "exchangeRate": "Exchange Rate", "fee": "Fee", "netAmount": "Net Amount", "processTime": "Processing Time"}, "withdrawForm": {"title": "Withdraw Fortune to HAOX", "amount": "<PERSON><PERSON><PERSON> Amount", "walletAddress": "Recipient Wallet Address", "submit": "Confirm <PERSON>", "dailyLimit": "Daily Limit", "remainingLimit": "Remaining Limit"}, "transactionHistory": {"title": "Transaction History", "type": "Type", "amount": "Amount", "status": "Status", "time": "Time", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "withdrawal": "<PERSON><PERSON><PERSON>", "pending": "Pending", "processing": "Processing", "completed": "Completed", "failed": "Failed", "cancelled": "Cancelled"}}, "certification": {"title": "HAOX Certification Level", "subtitle": "Certification level system based on HAOX holdings", "currentLevel": "Current Level", "haoxBalance": "HAOX Balance", "benefits": {"dailyQuota": "Daily Judgment Quota", "allowedRounds": "Allowed Rounds", "feeDiscount": "<PERSON><PERSON> Discount", "specialRights": "Special Rights"}, "upgrade": "Upgrade Progress", "updateLevel": "Update Level", "bindWallet": "Bind Wallet", "walletAddress": "Wallet Address", "lastUpdated": "Last Updated", "levels": {"X1": "Basic Certification", "X2": "Advanced Certification", "X3": "Professional Certification", "X4": "Expert Certification", "X5": "Master Certification"}}, "traceability": {"title": "Traceability", "subtitle": "Complete record of all operations, transparent and traceable", "betLifecycle": "Bet Lifecycle", "operationHistory": "Operation History", "fortuneFlow": "Fortune Flow", "participants": "Participants", "timeline": "Timeline", "operations": "Operation Records", "fortuneFlows": "Fortune Flow Records", "stats": {"totalOperations": "Total Operations", "totalInflow": "Total Inflow", "totalOutflow": "Total Outflow", "totalFees": "Total Fees", "totalRewards": "Total Rewards"}, "operationTypes": {"betCreated": "Bet Created", "betJoined": "Bet Joined", "betStarted": "Bet Started", "judgmentRound1": "Judgment Round 1", "judgmentRound2": "Judgment Round 2", "judgmentRound3": "Judgment Round 3", "judgmentCompleted": "Judgment Completed", "confirmationRequested": "Confirmation Requested", "confirmationSubmitted": "Confirmation Submitted", "disputeRaised": "Dispute Raised", "settlementStarted": "Settlement Started", "fortuneDistributed": "Fortune Distributed", "betCompleted": "Bet Completed", "betCancelled": "<PERSON> Cancelled"}}, "monitoring": {"title": "System Monitoring", "subtitle": "Real-time monitoring of system status and performance", "dashboard": "Monitoring Dashboard", "health": "System Health", "metrics": "System Metrics", "alerts": "<PERSON><PERSON><PERSON>", "errorHandling": "Erro<PERSON>", "performance": "Performance Monitoring", "healthStatus": {"healthy": "Healthy", "degraded": "Degraded", "unhealthy": "Unhealthy"}, "components": {"database": "Database", "blockchain": "Blockchain", "memory": "Memory", "disk": "Disk", "environment": "Environment"}}, "errors": {"general": "An unknown error occurred", "network": "Network connection error", "timeout": "Request timeout", "unauthorized": "Unauthorized access", "forbidden": "Insufficient permissions", "notFound": "Resource not found", "validation": "Data validation failed", "database": "Database error", "blockchain": "Blockchain error", "rateLimit": "Too many requests", "insufficientBalance": "Insufficient balance", "invalidAddress": "Invalid wallet address", "transactionFailed": "Transaction failed", "retry": "Please try again later", "contactSupport": "Please contact support"}, "success": {"general": "Operation successful", "saved": "Saved successfully", "created": "Created successfully", "updated": "Updated successfully", "deleted": "Deleted successfully", "submitted": "Submitted successfully", "sent": "<PERSON><PERSON> successfully", "copied": "<PERSON><PERSON>d successfully", "uploaded": "Uploaded successfully", "downloaded": "Downloaded successfully"}}