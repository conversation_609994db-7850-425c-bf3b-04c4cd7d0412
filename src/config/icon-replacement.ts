/**
 * 图标替换配置文件
 * 用于管理不同图标风格的替换方案
 */

import { LucideIcon } from 'lucide-react';
import {
  UserIcon,
  WalletIcon,
  BellIcon,
  CogIcon,
  HomeIcon,
  PencilIcon,
  StarIcon,
  TrophyIcon,
  Bars3Icon,
  XMarkIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  ArrowLeftIcon,
  ArrowRightIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  CheckIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  HeartIcon,
  ShareIcon,
  BookmarkIcon,
  EyeIcon,
  EyeSlashIcon,
  DocumentIcon,
  PhotoIcon,
  VideoCameraIcon,
  MusicalNoteIcon,
  ArrowDownTrayIcon,
  ArrowUpTrayIcon,
  ShoppingCartIcon,
  BuildingStorefrontIcon,
  CreditCardIcon,
  GiftIcon,
  FireIcon,
  AcademicCapIcon
} from '@heroicons/react/24/outline';

// 图标替换方案类型定义
export interface IconReplacementScheme {
  id: string;
  name: string;
  description: string;
  replacements: Record<string, any>;
  features: string[];
  pros: string[];
  cons: string[];
}

// 扁平化风格方案 (Heroicons)
export const flatIconScheme: IconReplacementScheme = {
  id: 'flat',
  name: '扁平化现代风格',
  description: '采用Heroicons图标库，更加现代化和用户友好的设计',
  features: [
    '24x24像素优化',
    '统一的笔画粗细',
    '更好的可读性',
    '现代扁平化设计'
  ],
  pros: [
    '用户友好度更高',
    '视觉层次更清晰',
    '适合移动端显示',
    '符合现代设计趋势'
  ],
  cons: [
    '可能缺乏品牌特色',
    '与当前设计的一致性需要调整'
  ],
  replacements: {
    // 用户相关图标
    'user': UserIcon,
    'users': UserIcon, // 可以后续添加更多用户图标
    
    // 导航图标
    'home': HomeIcon,
    'menu': Bars3Icon,
    'close': XMarkIcon,
    'chevronDown': ChevronDownIcon,
    'chevronUp': ChevronUpIcon,
    'back': ArrowLeftIcon,
    'forward': ArrowRightIcon,
    
    // 金融图标
    'wallet': WalletIcon,
    'card': CreditCardIcon,
    
    // 功能图标
    'settings': CogIcon,
    'search': MagnifyingGlassIcon,
    'filter': FunnelIcon,
    
    // 通知图标
    'bell': BellIcon,
    
    // 操作图标
    'edit': PencilIcon,
    'check': CheckIcon,
    'alert': ExclamationTriangleIcon,
    'info': InformationCircleIcon,
    
    // 奖励图标
    'star': StarIcon,
    'trophy': TrophyIcon,
    'gift': GiftIcon,
    'crown': TrophyIcon, // 使用Trophy代替Crown
    'zap': FireIcon,
    
    // 其他图标
    'heart': HeartIcon,
    'share': ShareIcon,
    'bookmark': BookmarkIcon,
    'eye': EyeIcon,
    'eyeOff': EyeSlashIcon,
    
    // 媒体图标
    'file': DocumentIcon,
    'image': PhotoIcon,
    'video': VideoCameraIcon,
    'music': MusicalNoteIcon,
    'download': ArrowDownTrayIcon,
    'upload': ArrowUpTrayIcon,
    
    // 商业图标
    'cart': ShoppingCartIcon,
    'store': BuildingStorefrontIcon,
  }
};

// 品牌化定制方案
export const brandIconScheme: IconReplacementScheme = {
  id: 'brand',
  name: '品牌化定制风格',
  description: '融入SocioMint品牌元素，使用渐变色和特殊效果',
  features: [
    '品牌色彩应用',
    '渐变效果',
    '独特的视觉识别',
    '社交化元素强调'
  ],
  pros: [
    '强化品牌识别度',
    '视觉冲击力强',
    '差异化竞争优势',
    '用户记忆度高'
  ],
  cons: [
    '开发成本较高',
    '维护复杂度增加',
    '可能影响加载性能'
  ],
  replacements: {
    // 暂时使用当前图标，后续可以创建自定义SVG
    // 这里主要是通过CSS样式来实现品牌化效果
  }
};

// 混合优化方案
export const mixedIconScheme: IconReplacementScheme = {
  id: 'mixed',
  name: '混合优化风格',
  description: '保留优秀的Lucide图标，重点替换核心功能图标',
  features: [
    '渐进式升级',
    '成本效益最优',
    '保持一致性',
    '重点优化核心功能'
  ],
  pros: [
    '实施风险最低',
    '开发成本可控',
    '用户适应性好',
    '可以分阶段实施'
  ],
  cons: [
    '视觉统一性可能不够',
    '长期需要持续优化'
  ],
  replacements: {
    // 只替换最重要的几个图标
    'user': UserIcon,
    'wallet': WalletIcon,
    'settings': CogIcon,
    'bell': BellIcon,
    'home': HomeIcon,
  }
};

// 所有可用的图标方案
export const iconSchemes = {
  flat: flatIconScheme,
  brand: brandIconScheme,
  mixed: mixedIconScheme,
};

// 获取指定方案的图标
export function getIconFromScheme(
  schemeId: keyof typeof iconSchemes,
  iconName: string,
  fallback?: LucideIcon
): any {
  const scheme = iconSchemes[schemeId];
  if (!scheme) return fallback;
  
  return scheme.replacements[iconName] || fallback;
}

// 检查图标是否在指定方案中被替换
export function isIconReplaced(
  schemeId: keyof typeof iconSchemes,
  iconName: string
): boolean {
  const scheme = iconSchemes[schemeId];
  return scheme ? iconName in scheme.replacements : false;
}

// 获取方案的统计信息
export function getSchemeStats(schemeId: keyof typeof iconSchemes) {
  const scheme = iconSchemes[schemeId];
  if (!scheme) return null;
  
  return {
    totalReplacements: Object.keys(scheme.replacements).length,
    features: scheme.features.length,
    pros: scheme.pros.length,
    cons: scheme.cons.length,
  };
}

// 导出类型定义
export type IconSchemeId = keyof typeof iconSchemes;
