/**
 * 统一的图标配置文件
 * 使用 Lucide React 图标库，确保整个项目的图标一致性
 */

// 导入Heroicons作为主要图标库
import {
  // 导航和菜单
  Bars3Icon as Menu,
  XMarkIcon as X,
  HomeIcon as Home,
  ArrowLeftIcon as ArrowLeft,
  ArrowRightIcon as ArrowRight,
  ArrowUpIcon as ArrowUp,
  ArrowDownIcon as ArrowDown,
  ChevronDownIcon as ChevronDown,
  ChevronUpIcon as ChevronUp,
  ChevronLeftIcon as ChevronLeft,
  ChevronRightIcon as ChevronRight,
  EllipsisHorizontalIcon as MoreHorizontal,
  EllipsisVerticalIcon as MoreVertical,
  QrCodeIcon as QrCode,

  // 用户和认证
  UserIcon as User,
  UsersIcon as Users,
  UserPlusIcon as UserPlus,
  UserIcon as UserCheck, // Heroicons没有直接对应，使用UserIcon
  UserIcon as UserX, // Heroicons没有直接对应，使用UserIcon
  ArrowRightEndOnRectangleIcon as LogIn,
  ArrowLeftStartOnRectangleIcon as LogOut,
  ShieldCheckIcon as Shield,
  ShieldCheckIcon as ShieldCheck,
  LockClosedIcon as Lock,
  LockOpenIcon as Unlock,

  // 钱包和金融
  WalletIcon as Wallet,
  CreditCardIcon as CreditCard,
  CurrencyDollarIcon as Coins,
  CurrencyDollarIcon as DollarSign,
  ArrowTrendingUpIcon as TrendingUp,
  ArrowTrendingDownIcon as TrendingDown,
  ChartBarIcon as BarChart3,
  ChartBarIcon as LineChart,
  ChartPieIcon as PieChart,
  BoltIcon as Activity,

  // 社交平台 - 保留一些Lucide图标，因为Heroicons社交图标有限
  ChatBubbleLeftRightIcon as MessageCircle,
  GlobeAltIcon as Globe,
  LinkIcon as Link,
  ShareIcon as Share2,

  // 操作和状态
  PlusIcon as Plus,
  MinusIcon as Minus,
  PencilIcon as Edit,
  PencilIcon as Edit2,
  TrashIcon as Trash2,
  DocumentArrowDownIcon as Save,
  ArrowDownTrayIcon as Download,
  ArrowUpTrayIcon as Upload,
  ClipboardDocumentIcon as Copy,
  CheckIcon as Check,
  CheckCircleIcon as CheckCircle,
  CheckCircleIcon as CheckCircle2,
  XMarkIcon as XIcon,
  XCircleIcon as XCircle,
  ExclamationCircleIcon as AlertCircle,
  ExclamationTriangleIcon as AlertTriangle,
  InformationCircleIcon as Info,
  QuestionMarkCircleIcon as HelpCircle,

  // 搜索和过滤
  MagnifyingGlassIcon as Search,
  FunnelIcon as Filter,
  AdjustmentsHorizontalIcon as SlidersHorizontal,
  CogIcon as Settings,
  Cog6ToothIcon as Settings2,
  AdjustmentsVerticalIcon as Sliders,

  // 时间和日期
  ClockIcon as Clock,
  CalendarIcon as Calendar,
  CalendarDaysIcon as CalendarDays,
  ClockIcon as Timer,
  ClockIcon as History,

  // 通知和消息
  BellIcon as Bell,
  BellAlertIcon as BellRing,
  BellSlashIcon as BellOff,
  EnvelopeIcon as Mail,
  EnvelopeOpenIcon as MailOpen,
  ChatBubbleLeftIcon as MessageSquare,
  PaperAirplaneIcon as Send,

  // 文件和文档
  DocumentIcon as File,
  DocumentTextIcon as FileText,
  PhotoIcon as Image,
  VideoCameraIcon as Video,
  MusicalNoteIcon as Music,
  ArrowDownTrayIcon as DownloadIcon,
  ArrowUpTrayIcon as UploadIcon,

  // 商业和交易
  ShoppingCartIcon as ShoppingCart,
  ShoppingBagIcon as ShoppingBag,
  BuildingStorefrontIcon as Store,
  ArchiveBoxIcon as Package,
  TruckIcon as Truck,
  CreditCardIcon as Card,

  // 奖励和成就
  TrophyIcon as Award,
  TrophyIcon as Trophy,
  StarIcon as Star,
  StarIcon as StarHalf, // Heroicons没有半星，使用完整星星
  GiftIcon as Gift,
  TagIcon as Target, // 使用TagIcon代替TargetIcon
  BoltIcon as Zap,
  TrophyIcon as Crown, // Heroicons没有Crown，使用Trophy代替
  TrophyIcon as Medal,

  // 技术和开发
  CodeBracketIcon as Code,
  CommandLineIcon as Terminal,
  CircleStackIcon as Database,
  ServerIcon as Server,
  WifiIcon as Wifi,
  WifiIcon as WifiOff, // 需要添加样式来表示关闭状态
  DevicePhoneMobileIcon as Smartphone,
  ComputerDesktopIcon as Monitor,
  DeviceTabletIcon as Tablet,

  // 其他常用
  EyeIcon as Eye,
  EyeSlashIcon as EyeOff,
  HeartIcon as Heart,
  HandRaisedIcon as HeartHandshake,
  HandThumbUpIcon as ThumbsUp,
  HandThumbDownIcon as ThumbsDown,
  BookmarkIcon as Bookmark,
  FlagIcon as Flag,
  MapPinIcon as MapPin,
  ArrowTopRightOnSquareIcon as ExternalLink,
  ArrowPathIcon as RotateCcw,
  PowerIcon as Power,
  SpeakerWaveIcon as Volume2,
  SpeakerXMarkIcon as VolumeX,
  ArrowsRightLeftIcon as ArrowLeftRight,
} from '@heroicons/react/24/outline';

// 导入一些仍需要使用的Lucide图标（Heroicons中没有对应的）
import {
  Twitter,
  Youtube,
  Instagram,
  Facebook,
  Linkedin,
  Github,
} from 'lucide-react';

// 图标尺寸配置
export const ICON_SIZES = {
  xs: 12,
  sm: 16,
  md: 20,
  lg: 24,
  xl: 32,
  '2xl': 48,
  '3xl': 64,
} as const;

// 图标尺寸到Tailwind类名的映射
export const ICON_SIZE_CLASSES = {
  xs: 'w-3 h-3',     // 12px
  sm: 'w-4 h-4',     // 16px
  md: 'w-5 h-5',     // 20px
  lg: 'w-6 h-6',     // 24px
  xl: 'w-8 h-8',     // 32px
  '2xl': 'w-12 h-12', // 48px
  '3xl': 'w-16 h-16', // 64px
} as const;

// 图标颜色配置（基于设计系统）
export const ICON_COLORS = {
  primary: 'text-system-blue',
  secondary: 'text-system-purple',
  success: 'text-system-green',
  warning: 'text-system-orange',
  error: 'text-system-red',
  muted: 'text-secondary-label',
  default: 'text-label',
} as const;

// 图标风格配置 - 用于图标替换方案
export const ICON_STYLES = {
  current: 'lucide',     // 当前Lucide React风格
  flat: 'heroicons',     // 扁平化现代风格
  brand: 'custom',       // 品牌化定制风格
  mixed: 'mixed',        // 混合优化风格
} as const;

// 图标替换实验配置 - 用于A/B测试不同图标风格
export const ICON_EXPERIMENT_CONFIG = {
  enabled: false,        // 是否启用图标替换实验
  style: 'current' as keyof typeof ICON_STYLES,
  replacements: {
    // 示例替换映射，可以根据需要配置
    // 'user': 'user-circle',
    // 'wallet': 'wallet-modern',
  }
} as const;

// 导航图标
export const NavigationIcons = {
  menu: Menu,
  close: X,
  home: Home,
  back: ArrowLeft,
  forward: ArrowRight,
  chevronDown: ChevronDown,
  chevronUp: ChevronUp,
  chevronLeft: ChevronLeft,
  chevronRight: ChevronRight,
  more: MoreHorizontal,
  moreVertical: MoreVertical,
} as const;

// 用户相关图标
export const UserIcons = {
  user: User,
  users: Users,
  userAdd: UserPlus,
  userCheck: UserCheck,
  userRemove: UserX,
  login: LogIn,
  logout: LogOut,
  shield: Shield,
  verified: ShieldCheck,
  lock: Lock,
  unlock: Unlock,
} as const;

// 金融相关图标
export const FinanceIcons = {
  wallet: Wallet,
  card: CreditCard,
  coins: Coins,
  dollar: DollarSign,
  trendUp: TrendingUp,
  trendDown: TrendingDown,
  trendingUp: TrendingUp,
  trendingDown: TrendingDown,
  barChart: BarChart3,
  lineChart: LineChart,
  pieChart: PieChart,
  activity: Activity,
} as const;

// 社交平台图标
export const SocialIcons = {
  twitter: Twitter,
  telegram: MessageCircle,
  youtube: Youtube,
  instagram: Instagram,
  facebook: Facebook,
  linkedin: Linkedin,
  github: Github,
  website: Globe,
  link: Link,
  share: Share2,
} as const;

// 操作图标
export const ActionIcons = {
  add: Plus,
  remove: Minus,
  edit: Edit,
  editAlt: Edit2,
  delete: Trash2,
  save: Save,
  download: Download,
  upload: Upload,
  copy: Copy,
  check: Check,
  checkCircle: CheckCircle,
  checkCircle2: CheckCircle2,
  close: XIcon,
  closeCircle: XCircle,
  alert: AlertCircle,
  warning: AlertTriangle,
  info: Info,
  help: HelpCircle,
  helpCircle: HelpCircle,
  refresh: RotateCcw,
  send: Send,
  receive: ArrowDown,
  exchange: ArrowLeftRight,
} as const;

// 功能图标
export const FeatureIcons = {
  search: Search,
  filter: Filter,
  settings: Settings,
  settingsAlt: Settings2,
  sliders: SlidersHorizontal,
  clock: Clock,
  calendar: Calendar,
  calendarDays: CalendarDays,
  timer: Timer,
  history: History,
} as const;

// 通知图标
export const NotificationIcons = {
  bell: Bell,
  bellRing: BellRing,
  bellOff: BellOff,
  mail: Mail,
  mailOpen: MailOpen,
  message: MessageSquare,
  send: Send,
} as const;

// 奖励图标
export const RewardIcons = {
  award: Award,
  trophy: Trophy,
  star: Star,
  starHalf: StarHalf,
  gift: Gift,
  target: Target,
  zap: Zap,
  crown: Crown,
  medal: Medal,
} as const;

// 商业图标
export const BusinessIcons = {
  cart: ShoppingCart,
  bag: ShoppingBag,
  store: Store,
  package: Package,
  truck: Truck,
  card: Card,
} as const;

// 媒体图标
export const MediaIcons = {
  file: File,
  fileText: FileText,
  image: Image,
  video: Video,
  music: Music,
  download: DownloadIcon,
  upload: UploadIcon,
} as const;

// 技术图标
export const TechIcons = {
  code: Code,
  terminal: Terminal,
  database: Database,
  server: Server,
  wifi: Wifi,
  wifiOff: WifiOff,
  smartphone: Smartphone,
  monitor: Monitor,
  tablet: Tablet,
} as const;

// 其他图标
export const MiscIcons = {
  eye: Eye,
  eyeOff: EyeOff,
  heart: Heart,
  handshake: HeartHandshake,
  thumbsUp: ThumbsUp,
  thumbsDown: ThumbsDown,
  bookmark: Bookmark,
  flag: Flag,
  mapPin: MapPin,
  externalLink: ExternalLink,
  rotate: RotateCcw,
  power: Power,
  volume: Volume2,
  volumeOff: VolumeX,
  qrCode: QrCode,
} as const;

// 导出所有图标的统一接口
export const Icons = {
  ...NavigationIcons,
  ...UserIcons,
  ...FinanceIcons,
  ...SocialIcons,
  ...ActionIcons,
  ...FeatureIcons,
  ...NotificationIcons,
  ...RewardIcons,
  ...BusinessIcons,
  ...MediaIcons,
  ...TechIcons,
  ...MiscIcons,
} as const;

// 图标组件的通用属性类型
export interface IconProps {
  size?: keyof typeof ICON_SIZES | number;
  color?: keyof typeof ICON_COLORS | string;
  className?: string;
}

// 获取图标尺寸的辅助函数
export function getIconSize(size: keyof typeof ICON_SIZES | number): number {
  if (typeof size === 'number') return size;
  return ICON_SIZES[size];
}

// 获取图标尺寸CSS类名的辅助函数
export function getIconSizeClass(size: keyof typeof ICON_SIZES | number): string {
  if (typeof size === 'number') {
    // 对于自定义数字尺寸，返回内联样式类名
    return `w-[${size}px] h-[${size}px]`;
  }
  return ICON_SIZE_CLASSES[size];
}

// 获取图标颜色的辅助函数
export function getIconColor(color: keyof typeof ICON_COLORS | string): string {
  if (color in ICON_COLORS) {
    return ICON_COLORS[color as keyof typeof ICON_COLORS];
  }
  return color;
}
