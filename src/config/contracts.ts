/**
 * HAOX智能合约地址配置
 * 部署时间: 2025-01-24
 * 网络: BSC Testnet (Chain ID: 97)
 */

export interface ContractAddresses {
  HAOX_TOKEN: string;
  HAOX_PRESALE: string;
  HAOX_INVITATION: string;
  HAOX_PRICE_ORACLE: string;
  HAOX_VESTING: string;
  CHAINLINK_BNB_USD_FEED: string;
  PANCAKESWAP_FACTORY: string;
  PANCAKESWAP_HAOX_BNB_PAIR?: string;
}

export interface NetworkConfig {
  chainId: number;
  name: string;
  rpcUrl: string;
  blockExplorer: string;
  contracts: ContractAddresses;
}

// HAOX代币解锁配置
export interface HAOXVestingConfig {
  totalRounds: number;
  round1Amount: string; // 第1轮: 5亿枚
  round2to7Amount: string; // 第2-7轮: 每轮2.5亿枚
  round8to13Amount: string; // 第8-13轮: 每轮2.5亿枚
  round14to19Amount: string; // 第14-19轮: 每轮2.5亿枚
  round2to7TriggerPercent: number; // 第2-7轮触发条件: +100%
  round8to13TriggerPercent: number; // 第8-13轮触发条件: +50%
  round14to19TriggerPercent: number; // 第14-19轮触发条件: +20%
  priceMaintainHours: number; // 价格维持时间: 168小时(7天)
  initialPriceRatio: number; // 初始价格比例: 263,111 HAOX per BNB
}

// HAOX解锁配置常量
export const HAOX_VESTING_CONFIG: HAOXVestingConfig = {
  totalRounds: 19,
  round1Amount: '500000000000000000000000000', // 5亿枚 (18位小数)
  round2to7Amount: '250000000000000000000000000', // 2.5亿枚 (18位小数)
  round8to13Amount: '250000000000000000000000000', // 2.5亿枚 (18位小数)
  round14to19Amount: '250000000000000000000000000', // 2.5亿枚 (18位小数)
  round2to7TriggerPercent: 100, // +100%
  round8to13TriggerPercent: 50, // +50%
  round14to19TriggerPercent: 20, // +20%
  priceMaintainHours: 168, // 7天
  initialPriceRatio: 263111, // 1 BNB = 263,111 HAOX
};

// BSC测试网配置
export const BSC_TESTNET_CONFIG: NetworkConfig = {
  chainId: 97,
  name: 'BSC Testnet',
  rpcUrl: process.env.NEXT_PUBLIC_RPC_URL || 'https://data-seed-prebsc-1-s1.binance.org:8545/',
  blockExplorer: process.env.NEXT_PUBLIC_BLOCK_EXPLORER || 'https://testnet.bscscan.com',
  contracts: {
    HAOX_TOKEN: process.env.NEXT_PUBLIC_HAOX_TOKEN_ADDRESS || '0xe8f5D853F689e7798f7d8F4E0ACF3601191e6670',
    HAOX_PRESALE: process.env.NEXT_PUBLIC_HAOX_PRESALE_ADDRESS || '0x440F1E265eF64b9150746b6E41E4276bc27E182a',
    HAOX_INVITATION: process.env.NEXT_PUBLIC_HAOX_INVITATION_ADDRESS || '0xBA105A5809aA8A34D35F91C1C4fd73Cd0fD5C604',
    HAOX_PRICE_ORACLE: process.env.NEXT_PUBLIC_HAOX_PRICE_ORACLE_ADDRESS || '0x1CFE55eB6d141056943c28dF8296f5093135Da2b',
    HAOX_VESTING: process.env.NEXT_PUBLIC_HAOX_VESTING_ADDRESS || '0xfB069d009d4c219B2D7d6aeCe9BbA13bC75cD41A',
    CHAINLINK_BNB_USD_FEED: process.env.NEXT_PUBLIC_CHAINLINK_BNB_USD_FEED || '0x2514895c72f50D8bd4B4F9b1110F0D6bD2c97526', // BSC测试网
    PANCAKESWAP_FACTORY: process.env.NEXT_PUBLIC_PANCAKESWAP_FACTORY || '0x6725F303b657a9451d8BA641348b6761A6CC7a17', // BSC测试网
    PANCAKESWAP_HAOX_BNB_PAIR: process.env.NEXT_PUBLIC_PANCAKESWAP_HAOX_BNB_PAIR, // 待创建
  }
};

// BSC主网配置 (待部署)
export const BSC_MAINNET_CONFIG: NetworkConfig = {
  chainId: 56,
  name: 'BSC Mainnet',
  rpcUrl: process.env.NEXT_PUBLIC_CHAIN_ID === '56'
    ? (process.env.NEXT_PUBLIC_RPC_URL || 'https://bsc-dataseed1.binance.org/')
    : 'https://bsc-dataseed1.binance.org/',
  blockExplorer: process.env.NEXT_PUBLIC_CHAIN_ID === '56'
    ? (process.env.NEXT_PUBLIC_BLOCK_EXPLORER || 'https://bscscan.com')
    : 'https://bscscan.com',
  contracts: {
    HAOX_TOKEN: process.env.NEXT_PUBLIC_CHAIN_ID === '56'
      ? (process.env.NEXT_PUBLIC_HAOX_TOKEN_ADDRESS || '0xe8f5D853F689e7798f7d8F4E0ACF3601191e6670')
      : '0xe8f5D853F689e7798f7d8F4E0ACF3601191e6670', // 使用测试网地址作为占位符
    HAOX_PRESALE: process.env.NEXT_PUBLIC_CHAIN_ID === '56'
      ? (process.env.NEXT_PUBLIC_HAOX_PRESALE_ADDRESS || '0x440F1E265eF64b9150746b6E41E4276bc27E182a')
      : '0x440F1E265eF64b9150746b6E41E4276bc27E182a', // 使用测试网地址作为占位符
    HAOX_INVITATION: process.env.NEXT_PUBLIC_CHAIN_ID === '56'
      ? (process.env.NEXT_PUBLIC_HAOX_INVITATION_ADDRESS || '0xBA105A5809aA8A34D35F91C1C4fd73Cd0fD5C604')
      : '0xBA105A5809aA8A34D35F91C1C4fd73Cd0fD5C604', // 使用测试网地址作为占位符
    HAOX_PRICE_ORACLE: process.env.NEXT_PUBLIC_CHAIN_ID === '56'
      ? (process.env.NEXT_PUBLIC_HAOX_PRICE_ORACLE_ADDRESS || '0x1CFE55eB6d141056943c28dF8296f5093135Da2b')
      : '0x1CFE55eB6d141056943c28dF8296f5093135Da2b', // 使用测试网地址作为占位符
    HAOX_VESTING: process.env.NEXT_PUBLIC_CHAIN_ID === '56'
      ? (process.env.NEXT_PUBLIC_HAOX_VESTING_ADDRESS || '0xfB069d009d4c219B2D7d6aeCe9BbA13bC75cD41A')
      : '0xfB069d009d4c219B2D7d6aeCe9BbA13bC75cD41A', // 使用测试网地址作为占位符
    CHAINLINK_BNB_USD_FEED: process.env.NEXT_PUBLIC_CHAIN_ID === '56'
      ? (process.env.NEXT_PUBLIC_CHAINLINK_BNB_USD_FEED || '0x0567F2323251f0Aab15c8dFb1967E4e8A7D42aeE')
      : '0x0567F2323251f0Aab15c8dFb1967E4e8A7D42aeE', // BSC主网Chainlink BNB/USD
    PANCAKESWAP_FACTORY: process.env.NEXT_PUBLIC_CHAIN_ID === '56'
      ? (process.env.NEXT_PUBLIC_PANCAKESWAP_FACTORY || '0xcA143Ce32Fe78f1f7019d7d551a6402fC5350c73')
      : '0xcA143Ce32Fe78f1f7019d7d551a6402fC5350c73', // BSC主网PancakeSwap V2 Factory
    PANCAKESWAP_HAOX_BNB_PAIR: process.env.NEXT_PUBLIC_PANCAKESWAP_HAOX_BNB_PAIR, // 待创建
  }
};

// 网络配置映射
export const NETWORK_CONFIGS: Record<number, NetworkConfig> = {
  97: BSC_TESTNET_CONFIG,
  56: BSC_MAINNET_CONFIG,
};

/**
 * 获取当前网络配置
 */
export function getCurrentNetworkConfig(): NetworkConfig {
  const chainId = parseInt(process.env.NEXT_PUBLIC_CHAIN_ID || '97');
  return NETWORK_CONFIGS[chainId] || BSC_TESTNET_CONFIG;
}

/**
 * 获取解锁轮次配置
 */
export function getVestingRoundConfig(round: number): {
  amount: string;
  triggerPercent: number;
} {
  if (round === 1) {
    return {
      amount: HAOX_VESTING_CONFIG.round1Amount,
      triggerPercent: 0, // 第1轮已完成
    };
  } else if (round >= 2 && round <= 7) {
    return {
      amount: HAOX_VESTING_CONFIG.round2to7Amount,
      triggerPercent: HAOX_VESTING_CONFIG.round2to7TriggerPercent,
    };
  } else if (round >= 8 && round <= 13) {
    return {
      amount: HAOX_VESTING_CONFIG.round8to13Amount,
      triggerPercent: HAOX_VESTING_CONFIG.round8to13TriggerPercent,
    };
  } else if (round >= 14 && round <= 19) {
    return {
      amount: HAOX_VESTING_CONFIG.round14to19Amount,
      triggerPercent: HAOX_VESTING_CONFIG.round14to19TriggerPercent,
    };
  } else {
    throw new Error(`Invalid vesting round: ${round}`);
  }
}

/**
 * 计算HAOX价格（基于BNB价格）
 */
export function calculateHAOXPrice(bnbPriceUSD: number): number {
  return bnbPriceUSD / HAOX_VESTING_CONFIG.initialPriceRatio;
}

// 获取当前网络配置
export function getCurrentNetworkConfig(): NetworkConfig {
  const chainId = parseInt(process.env.NEXT_PUBLIC_CHAIN_ID || '97');
  return NETWORK_CONFIGS[chainId] || BSC_TESTNET_CONFIG;
}

// 获取合约地址
export function getContractAddresses(): ContractAddresses {
  return getCurrentNetworkConfig().contracts;
}

// 获取特定合约地址
export function getContractAddress(contractName: keyof ContractAddresses): string {
  const addresses = getContractAddresses();
  return addresses[contractName];
}

// 验证合约地址是否有效
export function isValidContractAddress(address: string): boolean {
  return address !== '' && address.startsWith('0x') && address.length === 42;
}

// 获取区块浏览器链接
export function getExplorerUrl(address: string, type: 'address' | 'tx' = 'address'): string {
  const config = getCurrentNetworkConfig();
  return `${config.blockExplorer}/${type}/${address}`;
}

// 合约部署信息
export const DEPLOYMENT_INFO = {
  deploymentDate: '2025-01-24',
  deploymentBlock: 59384400,
  deployer: '0xf39Fd6e51aad88F6F4ce6aB8827279cffFb92266',
  totalGasUsed: '8,647,896',
  totalCostBNB: '0.0008647896',
  totalCostUSD: '$0.52',
  network: 'BSC Testnet',
  chainId: 97,
};

// 导出默认配置
export default getCurrentNetworkConfig();
