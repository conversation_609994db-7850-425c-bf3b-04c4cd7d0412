/**
 * HAOX价格预言机配置
 * 支持多数据源价格聚合和备案机制
 */

export interface PriceSource {
  name: string;
  weight: number; // 权重百分比
  isActive: boolean;
  minLiquidity?: string; // 最小流动性要求
}

export interface PriceOracleConfig {
  sources: PriceSource[];
  updateInterval: number; // 价格更新间隔(秒)
  maxPriceDeviation: number; // 最大价格偏差百分比
  fallbackEnabled: boolean; // 是否启用备案机制
}

// 价格数据源配置
export const PRICE_ORACLE_CONFIG: PriceOracleConfig = {
  sources: [
    {
      name: 'Chainlink',
      weight: 70, // 主要数据源，权重70%
      isActive: true,
    },
    {
      name: 'PancakeSwap',
      weight: 30, // 备用数据源，权重30%
      isActive: true,
      minLiquidity: '1000000000000000000', // 最小1 BNB流动性
    },
  ],
  updateInterval: 3600, // 每小时更新一次
  maxPriceDeviation: 10, // 最大10%价格偏差
  fallbackEnabled: true,
};

// Chainlink价格预言机ABI (简化版)
export const CHAINLINK_PRICE_FEED_ABI = [
  {
    inputs: [],
    name: 'latestRoundData',
    outputs: [
      { internalType: 'uint80', name: 'roundId', type: 'uint80' },
      { internalType: 'int256', name: 'answer', type: 'int256' },
      { internalType: 'uint256', name: 'startedAt', type: 'uint256' },
      { internalType: 'uint256', name: 'updatedAt', type: 'uint256' },
      { internalType: 'uint80', name: 'answeredInRound', type: 'uint80' },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [],
    name: 'decimals',
    outputs: [{ internalType: 'uint8', name: '', type: 'uint8' }],
    stateMutability: 'view',
    type: 'function',
  },
] as const;

// PancakeSwap工厂合约ABI (简化版)
export const PANCAKESWAP_FACTORY_ABI = [
  {
    inputs: [
      { internalType: 'address', name: 'tokenA', type: 'address' },
      { internalType: 'address', name: 'tokenB', type: 'address' },
    ],
    name: 'getPair',
    outputs: [{ internalType: 'address', name: 'pair', type: 'address' }],
    stateMutability: 'view',
    type: 'function',
  },
] as const;

// PancakeSwap交易对合约ABI (简化版)
export const PANCAKESWAP_PAIR_ABI = [
  {
    inputs: [],
    name: 'getReserves',
    outputs: [
      { internalType: 'uint112', name: '_reserve0', type: 'uint112' },
      { internalType: 'uint112', name: '_reserve1', type: 'uint112' },
      { internalType: 'uint32', name: '_blockTimestampLast', type: 'uint32' },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [],
    name: 'token0',
    outputs: [{ internalType: 'address', name: '', type: 'address' }],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [],
    name: 'token1',
    outputs: [{ internalType: 'address', name: '', type: 'address' }],
    stateMutability: 'view',
    type: 'function',
  },
] as const;

/**
 * 价格数据接口
 */
export interface PriceData {
  price: number; // USD价格
  source: string; // 数据源
  timestamp: number; // 时间戳
  confidence: number; // 置信度 (0-100)
}

/**
 * 聚合价格数据接口
 */
export interface AggregatedPriceData {
  price: number; // 加权平均价格
  sources: PriceData[]; // 各数据源价格
  lastUpdated: number; // 最后更新时间
  isValid: boolean; // 价格是否有效
}

/**
 * HAOX解锁状态接口
 */
export interface HAOXVestingStatus {
  currentRound: number; // 当前轮次
  nextRound: number; // 下一轮次
  currentPrice: number; // 当前HAOX价格
  targetPrice: number; // 目标触发价格
  priceGap: number; // 价格差距百分比
  maintainStartTime?: number; // 价格维持开始时间
  maintainDuration: number; // 已维持时间(小时)
  requiredDuration: number; // 需要维持时间(小时)
  canUnlock: boolean; // 是否可以解锁
  nextUnlockAmount: string; // 下一轮解锁数量
}

/**
 * 价格历史数据接口
 */
export interface PriceHistoryData {
  timestamp: number;
  price: number;
  volume24h?: number;
  source: string;
}

/**
 * 获取价格数据源权重
 */
export function getPriceSourceWeight(sourceName: string): number {
  const source = PRICE_ORACLE_CONFIG.sources.find(s => s.name === sourceName);
  return source?.weight || 0;
}

/**
 * 检查价格数据源是否活跃
 */
export function isPriceSourceActive(sourceName: string): boolean {
  const source = PRICE_ORACLE_CONFIG.sources.find(s => s.name === sourceName);
  return source?.isActive || false;
}

/**
 * 计算加权平均价格
 */
export function calculateWeightedPrice(prices: PriceData[]): number {
  let totalWeight = 0;
  let weightedSum = 0;

  for (const priceData of prices) {
    const weight = getPriceSourceWeight(priceData.source);
    if (weight > 0 && isPriceSourceActive(priceData.source)) {
      weightedSum += priceData.price * weight;
      totalWeight += weight;
    }
  }

  return totalWeight > 0 ? weightedSum / totalWeight : 0;
}

/**
 * 验证价格数据有效性
 */
export function validatePriceData(prices: PriceData[]): boolean {
  if (prices.length === 0) return false;

  // 检查价格偏差
  const avgPrice = prices.reduce((sum, p) => sum + p.price, 0) / prices.length;
  const maxDeviation = PRICE_ORACLE_CONFIG.maxPriceDeviation / 100;

  for (const priceData of prices) {
    const deviation = Math.abs(priceData.price - avgPrice) / avgPrice;
    if (deviation > maxDeviation) {
      console.warn(`Price deviation too high for ${priceData.source}: ${deviation * 100}%`);
      return false;
    }
  }

  return true;
}

/**
 * 格式化价格显示
 */
export function formatPrice(price: number, decimals: number = 6): string {
  return price.toFixed(decimals);
}

/**
 * 格式化代币数量显示
 */
export function formatTokenAmount(amount: string, decimals: number = 18): string {
  const num = parseFloat(amount) / Math.pow(10, decimals);
  if (num >= 1e9) {
    return `${(num / 1e9).toFixed(2)}B`;
  } else if (num >= 1e6) {
    return `${(num / 1e6).toFixed(2)}M`;
  } else if (num >= 1e3) {
    return `${(num / 1e3).toFixed(2)}K`;
  } else {
    return num.toFixed(2);
  }
}
