import React from 'react';
import { Metadata } from 'next';
import JudgmentDetailPage from '@/components/social-bet/JudgmentDetailPage';

export const metadata: Metadata = {
  title: '裁定详情 - 社交赌注 - SocioMint',
  description: '参与DAO裁定，查看裁定详情和投票',
  keywords: ['裁定详情', 'DAO裁定', '社交赌注', '福气', 'SocioMint'],
};

interface Props {
  params: {
    id: string;
  };
}

export default function JudgmentDetailPageRoute({ params }: Props) {
  return <JudgmentDetailPage betId={params.id} />;
}
