import React from 'react';
import { Metadata } from 'next';
import EnhancedBetDetail from '@/components/social-bet/EnhancedBetDetail';

interface BetDetailPageProps {
  params: {
    id: string;
  };
}

export async function generateMetadata({ params }: BetDetailPageProps): Promise<Metadata> {
  // 这里可以根据赌约ID获取具体信息来生成元数据
  return {
    title: `赌约详情 - 社交赌注 - SocioMint`,
    description: '查看赌约详情，参与投注或裁定',
    keywords: ['赌约详情', '社交赌注', '福气', '投注', 'SocioMint'],
  };
}

export default function BetDetailPage({ params }: BetDetailPageProps) {
  return <EnhancedBetDetail betId={params.id} />;
}
