import React from 'react';
import { Metadata } from 'next';
import BetTraceabilityPage from '@/components/social-bet/BetTraceabilityPage';

interface Props {
  params: { id: string };
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  return {
    title: `赌约可追溯性 - ${params.id} - SocioMint`,
    description: '查看赌约完整生命周期，所有操作和福气流转记录完全透明可追溯',
    keywords: ['赌约追踪', '可追溯性', '操作记录', '福气流转', 'SocioMint'],
  };
}

export default function BetTraceabilityPageRoute({ params }: Props) {
  return <BetTraceabilityPage betId={params.id} />;
}
