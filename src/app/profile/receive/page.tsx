'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, Button, Icon, Loading } from '@/components/ui';
import { 
  FinanceIcons, 
  ActionIcons, 
  FeatureIcons,
  NavigationIcons 
} from '@/config/icons';
import { useAuth } from '@/contexts/AuthContext';
import Header from '@/components/layout/Header';
import { cn } from '@/lib/utils';

interface PaymentMethod {
  id: string;
  name: string;
  icon: any;
  address: string;
  network: string;
  isActive: boolean;
}

const ReceivePage = () => {
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [selectedMethod, setSelectedMethod] = useState<string>('haox');
  const [copied, setCopied] = useState<string | null>(null);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    // 模拟加载用户钱包信息
    const timer = setTimeout(() => setIsLoading(false), 1000);
    return () => clearTimeout(timer);
  }, []);

  // 如果未认证，重定向到登录页
  useEffect(() => {
    if (mounted && !isAuthenticated && !authLoading) {
      window.location.href = '/login';
    }
  }, [mounted, isAuthenticated, authLoading]);

  const paymentMethods: PaymentMethod[] = [
    {
      id: 'haox',
      name: 'HAOX Token',
      icon: FinanceIcons.coins,
      address: user?.walletAddress || '0x1234...5678',
      network: 'BSC (BEP-20)',
      isActive: true
    },
    {
      id: 'bnb',
      name: 'BNB',
      icon: FinanceIcons.wallet,
      address: user?.walletAddress || '0x1234...5678',
      network: 'BSC',
      isActive: true
    },
    {
      id: 'usdt',
      name: 'USDT',
      icon: FinanceIcons.dollar,
      address: user?.walletAddress || '0x1234...5678',
      network: 'BSC (BEP-20)',
      isActive: true
    }
  ];

  const handleCopyAddress = async (address: string, methodId: string) => {
    try {
      await navigator.clipboard.writeText(address);
      setCopied(methodId);
      setTimeout(() => setCopied(null), 2000);
    } catch (error) {
      console.error('复制失败:', error);
    }
  };

  const generateQRCode = (address: string) => {
    // 这里可以集成真实的二维码生成库
    return `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${address}`;
  };

  if (!mounted || authLoading) {
    return null;
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-system-background">
        <Header />
        <div className="flex items-center justify-center min-h-[60vh]">
          <Loading text="加载收款信息中..." />
        </div>
      </div>
    );
  }

  const selectedPaymentMethod = paymentMethods.find(method => method.id === selectedMethod);

  return (
    <div className="min-h-screen bg-system-background">
      <Header />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面标题 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <div className="flex items-center space-x-4 mb-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => window.history.back()}
              className="p-2"
            >
              <Icon icon={NavigationIcons.back} size="md" />
            </Button>
            <h1 className="text-3xl font-bold text-label">收款</h1>
          </div>
          <p className="text-secondary-label">
            选择收款方式并分享您的钱包地址
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* 收款方式选择 */}
          <div className="lg:col-span-1">
            <Card>
              <h3 className="text-lg font-semibold text-label mb-4">选择币种</h3>
              <div className="space-y-2">
                {paymentMethods.map((method) => (
                  <button
                    key={method.id}
                    onClick={() => setSelectedMethod(method.id)}
                    disabled={!method.isActive}
                    className={cn(
                      'w-full flex items-center space-x-3 p-3 rounded-lg transition-colors',
                      'hover:bg-system-gray-5',
                      selectedMethod === method.id
                        ? 'bg-system-blue text-white'
                        : 'text-secondary-label',
                      !method.isActive && 'opacity-50 cursor-not-allowed'
                    )}
                  >
                    <Icon icon={method.icon} size="md" />
                    <div className="text-left">
                      <div className="font-medium">{method.name}</div>
                      <div className="text-xs opacity-75">{method.network}</div>
                    </div>
                  </button>
                ))}
              </div>
            </Card>
          </div>

          {/* 收款信息 */}
          <div className="lg:col-span-2">
            {selectedPaymentMethod && (
              <motion.div
                key={selectedMethod}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Card>
                  <div className="text-center">
                    <div className="flex items-center justify-center space-x-2 mb-6">
                      <Icon icon={selectedPaymentMethod.icon} size="lg" className="text-system-blue" />
                      <h2 className="text-xl font-semibold text-label">
                        接收 {selectedPaymentMethod.name}
                      </h2>
                    </div>

                    {/* 二维码 */}
                    <div className="mb-6">
                      <div className="w-48 h-48 mx-auto bg-white rounded-lg p-4 shadow-sm">
                        <img
                          src={generateQRCode(selectedPaymentMethod.address)}
                          alt="收款二维码"
                          className="w-full h-full"
                        />
                      </div>
                      <p className="text-sm text-secondary-label mt-2">
                        扫描二维码或复制地址进行转账
                      </p>
                    </div>

                    {/* 钱包地址 */}
                    <div className="mb-6">
                      <label className="block text-sm font-medium text-label mb-2">
                        钱包地址
                      </label>
                      <div className="flex items-center space-x-2">
                        <div className="flex-1 p-3 bg-system-gray-6 rounded-lg font-mono text-sm break-all">
                          {selectedPaymentMethod.address}
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleCopyAddress(selectedPaymentMethod.address, selectedMethod)}
                          className="shrink-0"
                        >
                          <Icon 
                            icon={copied === selectedMethod ? ActionIcons.check : ActionIcons.copy} 
                            size="sm" 
                          />
                        </Button>
                      </div>
                      <p className="text-xs text-secondary-label mt-1">
                        网络: {selectedPaymentMethod.network}
                      </p>
                    </div>

                    {/* 重要提示 */}
                    <div className="bg-system-orange/10 border border-system-orange/20 rounded-lg p-4">
                      <div className="flex items-start space-x-2">
                        <Icon icon={ActionIcons.warning} size="sm" className="text-system-orange mt-0.5" />
                        <div className="text-left">
                          <h4 className="font-medium text-label mb-1">重要提示</h4>
                          <ul className="text-sm text-secondary-label space-y-1">
                            <li>• 请确保发送方使用正确的网络 ({selectedPaymentMethod.network})</li>
                            <li>• 只接收 {selectedPaymentMethod.name} 代币，其他代币可能丢失</li>
                            <li>• 小额测试转账后再进行大额转账</li>
                            <li>• 转账确认通常需要几分钟时间</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </Card>
              </motion.div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReceivePage;
