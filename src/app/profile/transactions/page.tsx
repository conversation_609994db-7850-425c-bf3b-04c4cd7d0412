'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, Button, Icon, Loading, Badge } from '@/components/ui';
import {
  FinanceIcons,
  ActionIcons,
  FeatureIcons,
  NavigationIcons,
  RewardIcons
} from '@/config/icons';
import { useAuth } from '@/contexts/AuthContext';
import Header from '@/components/layout/Header';
import { cn } from '@/lib/utils';

interface Transaction {
  id: string;
  type: 'send' | 'receive' | 'swap' | 'reward';
  amount: string;
  token: string;
  status: 'pending' | 'completed' | 'failed';
  timestamp: Date;
  hash?: string;
  from?: string;
  to?: string;
  description?: string;
}

const TransactionsPage = () => {
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [filter, setFilter] = useState<'all' | 'send' | 'receive' | 'swap' | 'reward'>('all');
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    loadTransactions();
  }, []);

  // 如果未认证，重定向到登录页
  useEffect(() => {
    if (mounted && !isAuthenticated && !authLoading) {
      window.location.href = '/login';
    }
  }, [mounted, isAuthenticated, authLoading]);

  const loadTransactions = async () => {
    setIsLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 模拟交易数据
      const mockTransactions: Transaction[] = [
        {
          id: '1',
          type: 'reward',
          amount: '100',
          token: 'HAOX',
          status: 'completed',
          timestamp: new Date(Date.now() - 1000 * 60 * 30),
          description: '社交任务奖励'
        },
        {
          id: '2',
          type: 'receive',
          amount: '500',
          token: 'HAOX',
          status: 'completed',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2),
          hash: '0xabcd...1234',
          from: '0x1234...5678'
        },
        {
          id: '3',
          type: 'send',
          amount: '50',
          token: 'HAOX',
          status: 'pending',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 6),
          hash: '0xefgh...5678',
          to: '0x9876...4321'
        },
        {
          id: '4',
          type: 'swap',
          amount: '0.1',
          token: 'BNB',
          status: 'completed',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24),
          description: '兑换为 200 HAOX'
        },
        {
          id: '5',
          type: 'reward',
          amount: '50',
          token: 'HAOX',
          status: 'completed',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2),
          description: '邀请奖励'
        }
      ];

      setTransactions(mockTransactions);
    } catch (error) {
      console.error('加载交易记录失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getTransactionIcon = (type: Transaction['type']) => {
    switch (type) {
      case 'send':
        return FinanceIcons.trendDown;
      case 'receive':
        return FinanceIcons.trendUp;
      case 'swap':
        return ActionIcons.exchange;
      case 'reward':
        return RewardIcons.gift;
      default:
        return FinanceIcons.activity;
    }
  };

  const getTransactionColor = (type: Transaction['type']) => {
    switch (type) {
      case 'send':
        return 'text-system-red';
      case 'receive':
        return 'text-system-green';
      case 'swap':
        return 'text-system-blue';
      case 'reward':
        return 'text-system-orange';
      default:
        return 'text-secondary-label';
    }
  };

  const getStatusBadge = (status: Transaction['status']) => {
    switch (status) {
      case 'completed':
        return <Badge variant="success">已完成</Badge>;
      case 'pending':
        return <Badge variant="warning">处理中</Badge>;
      case 'failed':
        return <Badge variant="error">失败</Badge>;
      default:
        return <Badge variant="default">未知</Badge>;
    }
  };

  const formatAmount = (amount: string, type: Transaction['type']) => {
    const prefix = type === 'send' ? '-' : '+';
    return `${prefix}${amount}`;
  };

  const filteredTransactions = filter === 'all' 
    ? transactions 
    : transactions.filter(tx => tx.type === filter);

  const filterOptions = [
    { value: 'all', label: '全部', icon: FeatureIcons.filter },
    { value: 'receive', label: '接收', icon: FinanceIcons.trendUp },
    { value: 'send', label: '发送', icon: FinanceIcons.trendDown },
    { value: 'swap', label: '兑换', icon: ActionIcons.exchange },
    { value: 'reward', label: '奖励', icon: RewardIcons.gift }
  ];

  if (!mounted || authLoading) {
    return null;
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-system-background">
        <Header />
        <div className="flex items-center justify-center min-h-[60vh]">
          <Loading text="加载交易记录中..." />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-system-background">
      <Header />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面标题 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <div className="flex items-center space-x-4 mb-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => window.history.back()}
              className="p-2"
            >
              <Icon icon={NavigationIcons.back} size="md" />
            </Button>
            <h1 className="text-3xl font-bold text-label">交易记录</h1>
          </div>
          <p className="text-secondary-label">
            查看您的所有交易历史和状态
          </p>
        </motion.div>

        {/* 筛选器 */}
        <Card className="mb-6">
          <div className="flex flex-wrap gap-2">
            {filterOptions.map((option) => (
              <Button
                key={option.value}
                variant={filter === option.value ? "default" : "outline"}
                size="sm"
                onClick={() => setFilter(option.value as any)}
                className="flex items-center space-x-2"
              >
                <Icon icon={option.icon} size="sm" />
                <span>{option.label}</span>
              </Button>
            ))}
          </div>
        </Card>

        {/* 交易列表 */}
        <Card>
          {filteredTransactions.length === 0 ? (
            <div className="text-center py-12">
              <Icon icon={FeatureIcons.search} size="2xl" className="text-secondary-label mx-auto mb-4" />
              <h3 className="text-lg font-medium text-label mb-2">暂无交易记录</h3>
              <p className="text-secondary-label">
                {filter === 'all' ? '您还没有任何交易记录' : `没有找到${filterOptions.find(o => o.value === filter)?.label}类型的交易`}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredTransactions.map((transaction, index) => (
                <motion.div
                  key={transaction.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  className="flex items-center justify-between p-4 border border-system-gray-4 rounded-lg hover:bg-system-gray-6 transition-colors"
                >
                  <div className="flex items-center space-x-4">
                    <div className={cn(
                      'w-10 h-10 rounded-full flex items-center justify-center',
                      'bg-system-gray-5'
                    )}>
                      <Icon 
                        icon={getTransactionIcon(transaction.type)} 
                        size="md" 
                        className={getTransactionColor(transaction.type)}
                      />
                    </div>
                    
                    <div>
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className="font-medium text-label">
                          {transaction.description || 
                            (transaction.type === 'send' ? '发送' : 
                             transaction.type === 'receive' ? '接收' : 
                             transaction.type === 'swap' ? '兑换' : '奖励')}
                        </h4>
                        {getStatusBadge(transaction.status)}
                      </div>
                      
                      <div className="text-sm text-secondary-label">
                        {transaction.timestamp.toLocaleString('zh-CN')}
                      </div>
                      
                      {transaction.hash && (
                        <div className="text-xs text-secondary-label font-mono">
                          {transaction.hash}
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="text-right">
                    <div className={cn(
                      'font-medium',
                      getTransactionColor(transaction.type)
                    )}>
                      {formatAmount(transaction.amount, transaction.type)} {transaction.token}
                    </div>
                    
                    {transaction.hash && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="mt-1 text-xs"
                        onClick={() => window.open(`https://bscscan.com/tx/${transaction.hash}`, '_blank')}
                      >
                        <Icon icon={ActionIcons.link} size="xs" className="mr-1" />
                        查看详情
                      </Button>
                    )}
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </Card>

        {/* 刷新按钮 */}
        <div className="text-center mt-6">
          <Button
            variant="outline"
            onClick={loadTransactions}
            disabled={isLoading}
            className="flex items-center space-x-2"
          >
            <Icon icon={ActionIcons.refresh} size="sm" />
            <span>刷新</span>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default TransactionsPage;
