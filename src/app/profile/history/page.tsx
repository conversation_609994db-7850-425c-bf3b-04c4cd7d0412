'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, Button, Icon, Loading, Badge } from '@/components/ui';
import {
  FinanceIcons,
  ActionIcons,
  FeatureIcons,
  NavigationIcons,
  UserIcons,
  RewardIcons
} from '@/config/icons';
import { useAuth } from '@/contexts/AuthContext';
import Header from '@/components/layout/Header';
import { cn } from '@/lib/utils';

interface HistoryItem {
  id: string;
  type: 'task' | 'invitation' | 'login' | 'trade' | 'social' | 'system';
  title: string;
  description: string;
  timestamp: Date;
  reward?: {
    amount: string;
    token: string;
  };
  status: 'completed' | 'pending' | 'failed';
  metadata?: Record<string, any>;
}

const HistoryPage = () => {
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [history, setHistory] = useState<HistoryItem[]>([]);
  const [filter, setFilter] = useState<'all' | 'task' | 'invitation' | 'trade' | 'social'>('all');
  const [dateRange, setDateRange] = useState<'week' | 'month' | 'all'>('month');
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    loadHistory();
  }, [dateRange]);

  // 如果未认证，重定向到登录页
  useEffect(() => {
    if (mounted && !isAuthenticated && !authLoading) {
      window.location.href = '/login';
    }
  }, [mounted, isAuthenticated, authLoading]);

  const loadHistory = async () => {
    setIsLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 模拟历史数据
      const mockHistory: HistoryItem[] = [
        {
          id: '1',
          type: 'task',
          title: '完成每日签到',
          description: '连续签到第7天，获得额外奖励',
          timestamp: new Date(Date.now() - 1000 * 60 * 30),
          reward: { amount: '50', token: 'HAOX' },
          status: 'completed'
        },
        {
          id: '2',
          type: 'social',
          title: 'Twitter 分享任务',
          description: '分享项目推文并获得10个点赞',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2),
          reward: { amount: '100', token: 'HAOX' },
          status: 'completed',
          metadata: { platform: 'twitter', engagement: 15 }
        },
        {
          id: '3',
          type: 'invitation',
          title: '邀请新用户',
          description: '成功邀请用户 @user123 加入平台',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 6),
          reward: { amount: '200', token: 'HAOX' },
          status: 'completed',
          metadata: { invitedUser: 'user123' }
        },
        {
          id: '4',
          type: 'trade',
          title: 'PancakeSwap 交易',
          description: '在 PancakeSwap 上交易 HAOX/BNB',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 12),
          status: 'completed',
          metadata: { amount: '500 HAOX', pair: 'HAOX/BNB' }
        },
        {
          id: '5',
          type: 'task',
          title: 'Telegram 群组任务',
          description: '加入官方 Telegram 群组并发送消息',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24),
          reward: { amount: '75', token: 'HAOX' },
          status: 'completed'
        },
        {
          id: '6',
          type: 'system',
          title: '账户验证',
          description: '完成 KYC 身份验证',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2),
          status: 'completed'
        },
        {
          id: '7',
          type: 'login',
          title: '首次登录',
          description: '欢迎加入 SocioMint 平台',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24 * 3),
          reward: { amount: '100', token: 'HAOX' },
          status: 'completed'
        }
      ];

      // 根据日期范围过滤
      const now = Date.now();
      const filtered = mockHistory.filter(item => {
        const itemTime = item.timestamp.getTime();
        switch (dateRange) {
          case 'week':
            return now - itemTime <= 7 * 24 * 60 * 60 * 1000;
          case 'month':
            return now - itemTime <= 30 * 24 * 60 * 60 * 1000;
          default:
            return true;
        }
      });

      setHistory(filtered);
    } catch (error) {
      console.error('加载历史记录失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getTypeIcon = (type: HistoryItem['type']) => {
    switch (type) {
      case 'task':
        return RewardIcons.target;
      case 'invitation':
        return UserIcons.userAdd;
      case 'login':
        return UserIcons.login;
      case 'trade':
        return FinanceIcons.activity;
      case 'social':
        return FeatureIcons.share;
      case 'system':
        return FeatureIcons.settings;
      default:
        return FeatureIcons.info;
    }
  };

  const getTypeColor = (type: HistoryItem['type']) => {
    switch (type) {
      case 'task':
        return 'text-system-blue';
      case 'invitation':
        return 'text-system-purple';
      case 'login':
        return 'text-system-green';
      case 'trade':
        return 'text-system-orange';
      case 'social':
        return 'text-system-pink';
      case 'system':
        return 'text-system-gray';
      default:
        return 'text-secondary-label';
    }
  };

  const getTypeName = (type: HistoryItem['type']) => {
    switch (type) {
      case 'task':
        return '任务';
      case 'invitation':
        return '邀请';
      case 'login':
        return '登录';
      case 'trade':
        return '交易';
      case 'social':
        return '社交';
      case 'system':
        return '系统';
      default:
        return '其他';
    }
  };

  const filteredHistory = filter === 'all' 
    ? history 
    : history.filter(item => item.type === filter);

  const filterOptions = [
    { value: 'all', label: '全部' },
    { value: 'task', label: '任务' },
    { value: 'invitation', label: '邀请' },
    { value: 'trade', label: '交易' },
    { value: 'social', label: '社交' }
  ];

  const dateRangeOptions = [
    { value: 'week', label: '最近一周' },
    { value: 'month', label: '最近一月' },
    { value: 'all', label: '全部时间' }
  ];

  if (!mounted || authLoading) {
    return null;
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-system-background">
        <Header />
        <div className="flex items-center justify-center min-h-[60vh]">
          <Loading text="加载历史记录中..." />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-system-background">
      <Header />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面标题 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <div className="flex items-center space-x-4 mb-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => window.history.back()}
              className="p-2"
            >
              <Icon icon={NavigationIcons.back} size="md" />
            </Button>
            <h1 className="text-3xl font-bold text-label">活动历史</h1>
          </div>
          <p className="text-secondary-label">
            查看您在平台上的所有活动记录
          </p>
        </motion.div>

        {/* 筛选器 */}
        <Card className="mb-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            {/* 类型筛选 */}
            <div className="flex flex-wrap gap-2">
              {filterOptions.map((option) => (
                <Button
                  key={option.value}
                  variant={filter === option.value ? "default" : "outline"}
                  size="sm"
                  onClick={() => setFilter(option.value as any)}
                >
                  {option.label}
                </Button>
              ))}
            </div>

            {/* 时间范围 */}
            <div className="flex gap-2">
              {dateRangeOptions.map((option) => (
                <Button
                  key={option.value}
                  variant={dateRange === option.value ? "default" : "outline"}
                  size="sm"
                  onClick={() => setDateRange(option.value as any)}
                >
                  {option.label}
                </Button>
              ))}
            </div>
          </div>
        </Card>

        {/* 历史记录列表 */}
        <Card>
          {filteredHistory.length === 0 ? (
            <div className="text-center py-12">
              <Icon icon={FeatureIcons.clock} size="2xl" className="text-secondary-label mx-auto mb-4" />
              <h3 className="text-lg font-medium text-label mb-2">暂无历史记录</h3>
              <p className="text-secondary-label">
                在选定的时间范围内没有找到相关活动记录
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredHistory.map((item, index) => (
                <motion.div
                  key={item.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  className="flex items-start space-x-4 p-4 border border-system-gray-4 rounded-lg hover:bg-system-gray-6 transition-colors"
                >
                  <div className={cn(
                    'w-10 h-10 rounded-full flex items-center justify-center shrink-0',
                    'bg-system-gray-5'
                  )}>
                    <Icon 
                      icon={getTypeIcon(item.type)} 
                      size="md" 
                      className={getTypeColor(item.type)}
                    />
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <h4 className="font-medium text-label truncate">
                        {item.title}
                      </h4>
                      <Badge variant="outline" className="shrink-0">
                        {getTypeName(item.type)}
                      </Badge>
                      {item.status === 'completed' && (
                        <Icon icon={ActionIcons.check} size="sm" className="text-system-green" />
                      )}
                    </div>
                    
                    <p className="text-sm text-secondary-label mb-2">
                      {item.description}
                    </p>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-secondary-label">
                        {item.timestamp.toLocaleString('zh-CN')}
                      </span>
                      
                      {item.reward && (
                        <div className="text-sm font-medium text-system-green">
                          +{item.reward.amount} {item.reward.token}
                        </div>
                      )}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </Card>

        {/* 统计信息 */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
          <Card className="text-center">
            <Icon icon={RewardIcons.target} size="lg" className="text-system-blue mx-auto mb-2" />
            <div className="text-2xl font-bold text-label">
              {history.filter(h => h.type === 'task').length}
            </div>
            <div className="text-sm text-secondary-label">完成任务</div>
          </Card>
          
          <Card className="text-center">
            <Icon icon={UserIcons.userAdd} size="lg" className="text-system-purple mx-auto mb-2" />
            <div className="text-2xl font-bold text-label">
              {history.filter(h => h.type === 'invitation').length}
            </div>
            <div className="text-sm text-secondary-label">邀请用户</div>
          </Card>
          
          <Card className="text-center">
            <Icon icon={FinanceIcons.activity} size="lg" className="text-system-orange mx-auto mb-2" />
            <div className="text-2xl font-bold text-label">
              {history.filter(h => h.type === 'trade').length}
            </div>
            <div className="text-sm text-secondary-label">交易次数</div>
          </Card>
          
          <Card className="text-center">
            <Icon icon={FinanceIcons.coins} size="lg" className="text-system-green mx-auto mb-2" />
            <div className="text-2xl font-bold text-label">
              {history.reduce((sum, h) => sum + (h.reward ? parseInt(h.reward.amount) : 0), 0)}
            </div>
            <div className="text-sm text-secondary-label">总奖励</div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default HistoryPage;
