'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, Button, Icon, Loading } from '@/components/ui';
import { 
  UserIcons, 
  FinanceIcons, 
  ActionIcons, 
  FeatureIcons,
  NavigationIcons 
} from '@/config/icons';
import { useTelegramAuth } from '@/hooks/useTelegramAuth';
import { useAuth } from '@/contexts/AuthContext';
import Header from '@/components/layout/Header';
import UserInfo from '@/components/profile/UserInfo';
import WalletDashboard from '@/components/wallet/WalletDashboard';
import SafeTelegramLogin from '@/components/auth/SafeTelegramLogin';
// import OnboardingTour, { defaultOnboardingSteps, useOnboarding } from '@/components/onboarding/OnboardingTour';
// import HelpCenter from '@/components/help/HelpCenter';
import { cn } from '@/lib/utils';

const ProfilePage = () => {
  const { user: telegramUser, isAuthenticated: telegramAuth, refreshUser } = useTelegramAuth();
  const { user, isAuthenticated, isLoading: authLoading, refreshAuth } = useAuth();
  // const { hasCompletedOnboarding, markOnboardingComplete } = useOnboarding();
  const [activeTab, setActiveTab] = useState<'wallet' | 'profile'>('wallet');
  const [isLoading, setIsLoading] = useState(false);
  const [showHelp, setShowHelp] = useState(false);
  const [mounted, setMounted] = useState(false);

  // 确保只在客户端渲染
  useEffect(() => {
    setMounted(true);
  }, []);

  // 如果未认证，重定向到首页
  useEffect(() => {
    if (mounted && !isAuthenticated && !authLoading) {
      console.log('❌ 用户未认证，重定向到首页');
      window.location.href = '/';
    }
  }, [mounted, isAuthenticated, authLoading]);

  // 刷新数据
  const handleRefresh = async () => {
    setIsLoading(true);
    try {
      // 同时刷新Telegram用户数据和Auth上下文
      await Promise.all([
        refreshUser(),
        refreshAuth()
      ]);
    } catch (error) {
      console.error('Refresh failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const tabs = [
    {
      id: 'wallet' as const,
      name: '我的钱包',
      icon: FinanceIcons.wallet,
      description: '查看余额、转账和交易历史'
    },
    {
      id: 'profile' as const,
      name: '个人信息',
      icon: UserIcons.user,
      description: '管理个人信息和账户设置'
    }
  ];

  // 服务端渲染时显示加载状态
  if (!mounted || authLoading) {
    return (
      <div className="min-h-screen bg-system-background">
        <Header />
        <div className="flex items-center justify-center min-h-[60vh]">
          <Loading size="lg" />
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-system-background">
        <Header />
        <div className="flex items-center justify-center min-h-[60vh]">
          <Loading size="lg" />
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-system-background">
        <Header />
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <Card>
            <div className="text-center py-12">
              <Icon icon={UserIcons.user} size="3xl" color="primary" className="mx-auto mb-4" />
              <h3 className="text-title-3 font-sf-pro font-semibold text-label mb-2">
                请先登录
              </h3>
              <p className="text-body text-secondary-label mb-6">
                通过 Telegram 登录后即可访问您的个人中心
              </p>
              <SafeTelegramLogin
                size="lg"
                variant="primary"
                onSuccess={() => {
                  // 登录成功后刷新页面
                  window.location.reload();
                }}
                onError={(error) => {
                  console.error('个人中心登录失败:', error);
                }}
              />
            </div>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-system-background via-system-gray-6 to-system-background">
      <Header />

      {/* 背景装饰 */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-system-blue/5 rounded-full blur-3xl" />
        <div className="absolute bottom-0 right-1/4 w-80 h-80 bg-system-purple/5 rounded-full blur-3xl" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-system-green/3 rounded-full blur-3xl" />
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面标题和刷新按钮 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="flex items-center justify-between mb-8"
        >
          <div>
            <h1 className="text-title-1 font-sf-pro font-bold text-label">
              个人中心
            </h1>
            <p className="text-body text-secondary-label mt-1">
              管理您的资产、交易和个人信息
            </p>
          </div>

          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Button
              variant="outline"
              size="md"
              onClick={handleRefresh}
              loading={isLoading}
              className="flex items-center space-x-2 bg-system-background/80 backdrop-blur-sm border-system-gray-4/30 hover:bg-system-gray-6/50"
            >
              <Icon icon={ActionIcons.refresh} size="sm" />
              <span>刷新数据</span>
            </Button>
          </motion.div>
        </motion.div>

        {/* 用户信息卡片 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-8"
        >
          <UserInfo user={user} />
        </motion.div>

        {/* 标签页导航 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="mb-8"
        >
          <div className="bg-system-background/80 backdrop-blur-sm rounded-2xl p-2 border border-system-gray-4/30 shadow-apple">
            <nav className="flex space-x-2">
              {tabs.map((tab) => (
                <motion.button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={cn(
                    'relative flex items-center space-x-3 px-6 py-4 rounded-xl font-sf-pro font-medium text-sm transition-all duration-200',
                    activeTab === tab.id
                      ? 'text-system-blue'
                      : 'text-secondary-label hover:text-label'
                  )}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Icon
                    icon={tab.icon}
                    size="sm"
                    className={activeTab === tab.id ? 'text-system-blue' : 'text-secondary-label'}
                  />
                  <div className="text-left">
                    <div className="font-semibold">{tab.name}</div>
                    <div className="text-xs opacity-70">{tab.description}</div>
                  </div>

                  {/* 活跃指示器 */}
                  {activeTab === tab.id && (
                    <motion.div
                      layoutId="activeProfileTab"
                      className="absolute inset-0 bg-system-blue/10 rounded-xl border border-system-blue/20"
                      transition={{ type: 'spring', bounce: 0.2, duration: 0.6 }}
                    />
                  )}
                </motion.button>
              ))}
            </nav>
          </div>
        </motion.div>

        {/* 标签页内容 */}
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, transform: 'translateX(20px) scale(0.95)' }}
          animate={{ opacity: 1, transform: 'translateX(0px) scale(1)' }}
          exit={{ opacity: 0, transform: 'translateX(-20px) scale(0.95)' }}
          transition={{ duration: 0.4, ease: 'easeOut' }}
          className="bg-system-background/80 backdrop-blur-sm rounded-2xl border border-system-gray-4/30 shadow-apple-lg overflow-hidden"
        >
          {activeTab === 'wallet' && <WalletDashboard />}

          {activeTab === 'profile' && <UserInfo user={user} />}
        </motion.div>

        {/* 新手引导 - 暂时禁用 */}
        {/* {!hasCompletedOnboarding && (
          <OnboardingTour
            steps={defaultOnboardingSteps}
            onComplete={markOnboardingComplete}
            onSkip={markOnboardingComplete}
          />
        )} */}

        {/* 帮助中心 - 暂时禁用 */}
        {/* {showHelp && (
          <div className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4">
            <div className="w-full max-w-4xl max-h-[90vh] overflow-y-auto">
              <HelpCenter onClose={() => setShowHelp(false)} />
            </div>
          </div>
        )} */}

        {/* 帮助按钮 */}
        <button
          onClick={() => setShowHelp(true)}
          className="fixed bottom-6 right-6 w-14 h-14 bg-system-blue rounded-full shadow-lg flex items-center justify-center hover:bg-system-blue/90 transition-colors z-40"
        >
          <Icon icon={ActionIcons.helpCircle} size="lg" className="text-white" />
        </button>
      </div>
    </div>
  );
};

export default ProfilePage;
