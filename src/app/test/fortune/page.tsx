'use client';

import React, { useState } from 'react';
import { Card, Button, Icon } from '@/components/ui';
import { ActionIcons, FeatureIcons } from '@/config/icons';
import FortuneCenter from '@/components/fortune/FortuneCenter';
import FortuneSystemStatus from '@/components/fortune/FortuneSystemStatus';
import { useFortune, useFortuneRewards } from '@/hooks/useFortune';
import { useUser } from '@supabase/auth-helpers-react';

/**
 * 福气系统测试页面
 * 用于测试和验证福气系统的各项功能
 */
const FortuneTestPage: React.FC = () => {
  const user = useUser();
  const { fortuneAccount, isLoading: fortuneLoading, refreshFortune } = useFortune();
  const { 
    checkInStatus, 
    shareRewards, 
    inviteStats,
    isLoading: rewardsLoading,
    dailyCheckIn,
    processShareReward,
    processInviteReward
  } = useFortuneRewards();

  const [testResults, setTestResults] = useState<Array<{
    test: string;
    status: 'success' | 'error' | 'pending';
    message: string;
    timestamp: string;
  }>>([]);

  const [isRunningTests, setIsRunningTests] = useState(false);

  /**
   * 添加测试结果
   */
  const addTestResult = (test: string, status: 'success' | 'error', message: string) => {
    setTestResults(prev => [...prev, {
      test,
      status,
      message,
      timestamp: new Date().toLocaleTimeString()
    }]);
  };

  /**
   * 测试API端点
   */
  const testAPIEndpoint = async (endpoint: string, method: string = 'GET', body?: any) => {
    try {
      const options: RequestInit = {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
      };

      if (body && (method === 'POST' || method === 'PUT')) {
        options.body = JSON.stringify(body);
      }

      const response = await fetch(endpoint, options);
      const data = await response.json();

      if (response.ok) {
        addTestResult(`${method} ${endpoint}`, 'success', `状态码: ${response.status}`);
        return data;
      } else {
        addTestResult(`${method} ${endpoint}`, 'error', `错误: ${data.error || response.statusText}`);
        return null;
      }
    } catch (error) {
      addTestResult(`${method} ${endpoint}`, 'error', `异常: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return null;
    }
  };

  /**
   * 运行基础API测试
   */
  const runBasicAPITests = async () => {
    setIsRunningTests(true);
    setTestResults([]);

    try {
      // 测试健康检查
      await testAPIEndpoint('/api/health');

      // 测试福气账户API
      await testAPIEndpoint('/api/fortune/account');

      // 测试每日签到API
      await testAPIEndpoint('/api/fortune/daily-checkin');

      // 测试邀请奖励API
      await testAPIEndpoint('/api/fortune/invite-reward');

      // 测试分享奖励API
      await testAPIEndpoint('/api/fortune/share-reward');

      addTestResult('基础API测试', 'success', '所有API端点测试完成');
    } catch (error) {
      addTestResult('基础API测试', 'error', `测试失败: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsRunningTests(false);
    }
  };

  /**
   * 测试每日签到功能
   */
  const testDailyCheckIn = async () => {
    if (!user) {
      addTestResult('每日签到测试', 'error', '用户未登录');
      return;
    }

    try {
      const result = await dailyCheckIn();
      addTestResult('每日签到测试', 'success', `签到成功，获得${result.totalReward}福气`);
      await refreshFortune();
    } catch (error) {
      addTestResult('每日签到测试', 'error', error instanceof Error ? error.message : '签到失败');
    }
  };

  /**
   * 测试分享奖励功能
   */
  const testShareReward = async () => {
    if (!user) {
      addTestResult('分享奖励测试', 'error', '用户未登录');
      return;
    }

    try {
      const result = await processShareReward('bet', 'test-bet-id', 'telegram');
      addTestResult('分享奖励测试', 'success', `分享成功，获得${result.rewardAmount}福气`);
      await refreshFortune();
    } catch (error) {
      addTestResult('分享奖励测试', 'error', error instanceof Error ? error.message : '分享奖励失败');
    }
  };

  /**
   * 清除测试结果
   */
  const clearTestResults = () => {
    setTestResults([]);
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-title-1 font-sf-pro font-bold text-label mb-2">
          福气系统测试页面
        </h1>
        <p className="text-body text-secondary-label">
          测试和验证福气系统的各项功能
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 左侧：系统状态和测试控制 */}
        <div className="space-y-6">
          {/* 系统状态 */}
          <FortuneSystemStatus showDetails={true} />

          {/* 测试控制面板 */}
          <Card className="p-6">
            <h3 className="text-title-3 font-sf-pro font-bold text-label mb-4">
              测试控制面板
            </h3>
            
            <div className="space-y-3">
              <Button
                variant="primary"
                onClick={runBasicAPITests}
                disabled={isRunningTests}
                className="w-full"
              >
                <Icon icon={FeatureIcons.settings} size="sm" className="mr-2" />
                {isRunningTests ? '运行中...' : '运行基础API测试'}
              </Button>

              {user && (
                <>
                  <Button
                    variant="secondary"
                    onClick={testDailyCheckIn}
                    disabled={checkInStatus?.hasCheckedInToday}
                    className="w-full"
                  >
                    <Icon icon={FeatureIcons.calendar} size="sm" className="mr-2" />
                    {checkInStatus?.hasCheckedInToday ? '今日已签到' : '测试每日签到'}
                  </Button>

                  <Button
                    variant="secondary"
                    onClick={testShareReward}
                    className="w-full"
                  >
                    <Icon icon={ActionIcons.share} size="sm" className="mr-2" />
                    测试分享奖励
                  </Button>
                </>
              )}

              <Button
                variant="ghost"
                onClick={clearTestResults}
                className="w-full"
              >
                <Icon icon={ActionIcons.trash} size="sm" className="mr-2" />
                清除测试结果
              </Button>
            </div>
          </Card>

          {/* 测试结果 */}
          {testResults.length > 0 && (
            <Card className="p-6">
              <h3 className="text-title-3 font-sf-pro font-bold text-label mb-4">
                测试结果
              </h3>
              
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {testResults.map((result, index) => (
                  <div
                    key={index}
                    className="flex items-start justify-between p-3 rounded-lg bg-system-gray-6"
                  >
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <span className={`text-sm ${
                          result.status === 'success' ? 'text-system-green' :
                          result.status === 'error' ? 'text-system-red' :
                          'text-system-orange'
                        }`}>
                          {result.status === 'success' ? '✅' :
                           result.status === 'error' ? '❌' : '⏳'}
                        </span>
                        <span className="text-caption-1 font-sf-pro font-medium text-label">
                          {result.test}
                        </span>
                      </div>
                      <p className="text-caption-2 text-secondary-label mt-1">
                        {result.message}
                      </p>
                    </div>
                    <span className="text-caption-2 text-tertiary-label">
                      {result.timestamp}
                    </span>
                  </div>
                ))}
              </div>
            </Card>
          )}

          {/* 用户状态 */}
          <Card className="p-6">
            <h3 className="text-title-3 font-sf-pro font-bold text-label mb-4">
              用户状态
            </h3>
            
            {user ? (
              <div className="space-y-2">
                <p className="text-caption-1 text-label">
                  <span className="text-secondary-label">用户ID:</span> {user.id}
                </p>
                <p className="text-caption-1 text-label">
                  <span className="text-secondary-label">邮箱:</span> {user.email}
                </p>
                {fortuneAccount && (
                  <>
                    <p className="text-caption-1 text-label">
                      <span className="text-secondary-label">可用福气:</span> {fortuneAccount.available_fortune}
                    </p>
                    <p className="text-caption-1 text-label">
                      <span className="text-secondary-label">福气等级:</span> {fortuneAccount.fortune_level_name}
                    </p>
                  </>
                )}
              </div>
            ) : (
              <p className="text-caption-1 text-secondary-label">
                用户未登录，部分功能测试不可用
              </p>
            )}
          </Card>
        </div>

        {/* 右侧：福气中心组件 */}
        <div>
          <FortuneCenter />
        </div>
      </div>
    </div>
  );
};

export default FortuneTestPage;
