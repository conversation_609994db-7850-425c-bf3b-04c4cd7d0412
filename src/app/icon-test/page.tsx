'use client';

import React from 'react';
import { Card, Icon, IconButton, IconWithLabel, StatusIcon } from '@/components/ui';
import {
  UserIcons,
  FinanceIcons,
  NavigationIcons,
  ActionIcons,
  RewardIcons,
  ICON_SIZES
} from '@/config/icons';

const IconTestPage = () => {
  const iconSizes = Object.keys(ICON_SIZES) as Array<keyof typeof ICON_SIZES>;
  
  const testIcons = [
    { name: '用户', icon: UserIcons.user },
    { name: '钱包', icon: FinanceIcons.wallet },
    { name: '首页', icon: NavigationIcons.home },
    { name: '编辑', icon: ActionIcons.edit },
    { name: '星标', icon: RewardIcons.star },
  ];

  return (
    <div className="min-h-screen bg-system-background p-8">
      <div className="max-w-6xl mx-auto">
        {/* 页面标题 */}
        <div className="text-center mb-8">
          <h1 className="text-title-1 font-sf-pro font-bold text-label mb-4">
            图标尺寸测试页面
          </h1>
          <p className="text-body text-secondary-label">
            验证所有图标尺寸级别的正确显示
          </p>
        </div>

        {/* 尺寸配置表 */}
        <Card className="p-6 mb-8">
          <h2 className="text-title-2 font-sf-pro font-semibold text-label mb-4">
            尺寸配置对照表
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {iconSizes.map((size) => (
              <div key={size} className="flex items-center justify-between p-3 bg-system-gray-6 rounded-lg">
                <span className="font-sf-pro font-medium text-label">{size}</span>
                <span className="text-secondary-label">{ICON_SIZES[size]}px</span>
              </div>
            ))}
          </div>
        </Card>

        {/* 基础Icon组件测试 */}
        <Card className="p-6 mb-8">
          <h2 className="text-title-2 font-sf-pro font-semibold text-label mb-4">
            基础Icon组件
          </h2>
          <div className="space-y-6">
            {iconSizes.map((size) => (
              <div key={size} className="space-y-3">
                <h3 className="text-title-3 font-sf-pro font-medium text-label">
                  尺寸: {size} ({ICON_SIZES[size]}px)
                </h3>
                <div className="flex items-center space-x-4 p-4 bg-system-gray-6 rounded-lg">
                  {testIcons.map((iconData) => (
                    <div key={iconData.name} className="flex flex-col items-center space-y-2">
                      <Icon icon={iconData.icon} size={size} color="primary" />
                      <span className="text-caption-2 text-secondary-label">{iconData.name}</span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* IconButton组件测试 */}
        <Card className="p-6 mb-8">
          <h2 className="text-title-2 font-sf-pro font-semibold text-label mb-4">
            IconButton组件
          </h2>
          <div className="space-y-6">
            {iconSizes.map((size) => (
              <div key={size} className="space-y-3">
                <h3 className="text-title-3 font-sf-pro font-medium text-label">
                  尺寸: {size}
                </h3>
                <div className="flex items-center space-x-4 p-4 bg-system-gray-6 rounded-lg">
                  <IconButton 
                    icon={ActionIcons.edit} 
                    size={size} 
                    variant="ghost"
                    aria-label="编辑"
                  />
                  <IconButton 
                    icon={ActionIcons.save} 
                    size={size} 
                    variant="outline"
                    aria-label="保存"
                  />
                  <IconButton 
                    icon={ActionIcons.check} 
                    size={size} 
                    variant="solid"
                    aria-label="确认"
                  />
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* IconWithLabel组件测试 */}
        <Card className="p-6 mb-8">
          <h2 className="text-title-2 font-sf-pro font-semibold text-label mb-4">
            IconWithLabel组件
          </h2>
          <div className="space-y-6">
            {iconSizes.map((size) => (
              <div key={size} className="space-y-3">
                <h3 className="text-title-3 font-sf-pro font-medium text-label">
                  尺寸: {size}
                </h3>
                <div className="flex items-center space-x-6 p-4 bg-system-gray-6 rounded-lg">
                  <IconWithLabel 
                    icon={UserIcons.user} 
                    label="用户" 
                    size={size}
                    labelPosition="right"
                  />
                  <IconWithLabel 
                    icon={FinanceIcons.wallet} 
                    label="钱包" 
                    size={size}
                    labelPosition="bottom"
                  />
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* StatusIcon组件测试 */}
        <Card className="p-6 mb-8">
          <h2 className="text-title-2 font-sf-pro font-semibold text-label mb-4">
            StatusIcon组件
          </h2>
          <div className="space-y-6">
            {iconSizes.map((size) => (
              <div key={size} className="space-y-3">
                <h3 className="text-title-3 font-sf-pro font-medium text-label">
                  尺寸: {size}
                </h3>
                <div className="flex items-center space-x-4 p-4 bg-system-gray-6 rounded-lg">
                  <StatusIcon icon={ActionIcons.check} status="success" size={size} />
                  <StatusIcon icon={ActionIcons.alert} status="warning" size={size} />
                  <StatusIcon icon={ActionIcons.xCircle} status="error" size={size} />
                  <StatusIcon icon={ActionIcons.info} status="info" size={size} />
                  <StatusIcon icon={ActionIcons.check} status="success" size={size} showBackground />
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* 颜色测试 */}
        <Card className="p-6 mb-8">
          <h2 className="text-title-2 font-sf-pro font-semibold text-label mb-4">
            颜色主题测试
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
            {['default', 'primary', 'secondary', 'success', 'warning', 'error', 'muted'].map((color) => (
              <div key={color} className="flex flex-col items-center space-y-2 p-3 bg-system-gray-6 rounded-lg">
                <Icon 
                  icon={RewardIcons.star} 
                  size="lg" 
                  color={color as any}
                />
                <span className="text-caption-2 text-secondary-label capitalize">{color}</span>
              </div>
            ))}
          </div>
        </Card>

        {/* 移动端适配测试 */}
        <Card className="p-6 mb-8">
          <h2 className="text-title-2 font-sf-pro font-semibold text-label mb-4">
            移动端适配测试
          </h2>
          <div className="space-y-4">
            <div className="p-4 bg-system-gray-6 rounded-lg">
              <h3 className="text-title-3 font-sf-pro font-medium text-label mb-3">
                响应式图标尺寸
              </h3>
              <div className="flex items-center space-x-4">
                <Icon icon={UserIcons.user} size="sm" className="md:w-6 md:h-6" />
                <Icon icon={FinanceIcons.wallet} size="md" className="md:w-8 md:h-8" />
                <Icon icon={NavigationIcons.home} size="lg" className="md:w-10 md:h-10" />
              </div>
            </div>

            <div className="p-4 bg-system-gray-6 rounded-lg">
              <h3 className="text-title-3 font-sf-pro font-medium text-label mb-3">
                触摸友好的按钮
              </h3>
              <div className="flex items-center space-x-4">
                <IconButton
                  icon={ActionIcons.edit}
                  size="lg"
                  variant="ghost"
                  className="min-w-[44px] min-h-[44px]"
                  aria-label="编辑"
                />
                <IconButton
                  icon={ActionIcons.save}
                  size="lg"
                  variant="outline"
                  className="min-w-[44px] min-h-[44px]"
                  aria-label="保存"
                />
              </div>
            </div>
          </div>
        </Card>

        {/* 返回按钮 */}
        <div className="flex justify-center">
          <IconButton
            icon={NavigationIcons.back}
            onClick={() => window.history.back()}
            variant="outline"
            size="lg"
            aria-label="返回"
          />
        </div>
      </div>
    </div>
  );
};

export default IconTestPage;
