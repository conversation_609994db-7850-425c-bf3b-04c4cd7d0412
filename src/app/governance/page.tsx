'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  UserIcons,
  FinanceIcons,
  FeatureIcons,
  ActionIcons,
  RewardIcons,
  MiscIcons,
  NotificationIcons
} from '@/config/icons';
import { HandRaisedIcon } from '@heroicons/react/24/outline';
import { <PERSON><PERSON>, <PERSON> } from '@/components/ui';
import Header from '@/components/layout/Header';
import { formatNumber, cn } from '@/lib/utils';
import { useWallet } from '@/hooks/useWallet';
import { useTelegramAuth } from '@/hooks/useTelegramAuth';
import { useGovernance } from '@/hooks/useGovernance';

const statusColors = {
  active: 'text-system-green',
  passed: 'text-system-blue',
  rejected: 'text-system-red',
  executed: 'text-system-purple',
  cancelled: 'text-secondary-label',
  expired: 'text-system-orange'
};

const statusBgColors = {
  active: 'bg-system-green/10 border-system-green/20',
  passed: 'bg-system-blue/10 border-system-blue/20',
  rejected: 'bg-system-red/10 border-system-red/20',
  executed: 'bg-system-purple/10 border-system-purple/20',
  cancelled: 'bg-system-gray-5/10 border-system-gray-4',
  expired: 'bg-system-orange/10 border-system-orange/20'
};

export default function GovernancePage() {
  const { isConnected } = useWallet();
  const { user, isAuthenticated } = useTelegramAuth();
  const {
    proposals,
    proposalTypes,
    userStats,
    isLoading,
    error,
    fetchProposals,
    castVote,
    hasUserVoted,
    getUserVote,
    governanceStatistics,
    activeProposals,
    passedProposals,
    userProposals
  } = useGovernance();

  const [activeTab, setActiveTab] = useState<'browse' | 'my-votes' | 'create'>('browse');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [showVoteModal, setShowVoteModal] = useState(false);
  const [selectedProposal, setSelectedProposal] = useState<any>(null);

  // 过滤提案
  const filteredProposals = proposals.filter(proposal => {
    const matchesStatus = selectedStatus === 'all' || proposal.status === selectedStatus;
    const matchesSearch = proposal.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         proposal.description.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesStatus && matchesSearch;
  });

  // 处理投票
  const handleVote = async (proposal: any, voteChoice: 'for' | 'against' | 'abstain', reason?: string) => {
    const success = await castVote(proposal.id, voteChoice, reason);
    if (success) {
      setShowVoteModal(false);
      setSelectedProposal(null);
    }
  };

  // 格式化时间剩余
  const formatTimeRemaining = (timeRemaining: number) => {
    if (timeRemaining <= 0) return '已结束';
    
    const days = Math.floor(timeRemaining / (1000 * 60 * 60 * 24));
    const hours = Math.floor((timeRemaining % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    
    if (days > 0) return `${days}天${hours}小时`;
    return `${hours}小时`;
  };

  return (
    <div className="min-h-screen bg-system-background">
      <Header />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面标题 */}
        <div className="text-center mb-8">
          <h1 className="text-title-1 font-sf-pro font-bold text-label mb-2">
            DAO 治理中心
          </h1>
          <p className="text-title-3 text-secondary-label">
            参与社区治理，共同决定平台发展方向
          </p>
        </div>

        {!isConnected ? (
          <Card>
            <div className="text-center py-12">
              <FinanceIcons.wallet className="w-16 h-16 text-system-blue mx-auto mb-4" />
              <h3 className="text-title-3 font-sf-pro font-semibold text-label mb-2">
                请先连接钱包
              </h3>
              <p className="text-body text-secondary-label mb-6">
                连接钱包后即可参与DAO治理
              </p>
              <Button className="bg-system-blue">
                连接钱包
              </Button>
            </div>
          </Card>
        ) : (
          <>
            {/* 统计面板 */}
            <div className="grid md:grid-cols-4 gap-6 mb-8">
              <Card>
                <div className="text-center p-6">
                  <div className="w-12 h-12 bg-system-blue/20 rounded-full flex items-center justify-center mx-auto mb-3">
                    <HandRaisedIcon className="w-6 h-6 text-system-blue" />
                  </div>
                  <p className="text-title-2 font-sf-pro font-bold text-label">
                    {governanceStatistics.total_proposals}
                  </p>
                  <p className="text-caption-1 text-secondary-label">总提案数</p>
                </div>
              </Card>

              <Card>
                <div className="text-center p-6">
                  <div className="w-12 h-12 bg-system-green/20 rounded-full flex items-center justify-center mx-auto mb-3">
                    <FinanceIcons.trendUp className="w-6 h-6 text-system-green" />
                  </div>
                  <p className="text-title-2 font-sf-pro font-bold text-label">
                    {governanceStatistics.active_proposals}
                  </p>
                  <p className="text-caption-1 text-secondary-label">活跃提案</p>
                </div>
              </Card>

              <Card>
                <div className="text-center p-6">
                  <div className="w-12 h-12 bg-system-orange/20 rounded-full flex items-center justify-center mx-auto mb-3">
                    <UserIcons.users className="w-6 h-6 text-system-orange" />
                  </div>
                  <p className="text-title-2 font-sf-pro font-bold text-label">
                    {Math.round(governanceStatistics.avg_participation)}
                  </p>
                  <p className="text-caption-1 text-secondary-label">平均参与人数</p>
                </div>
              </Card>

              <Card>
                <div className="text-center p-6">
                  <div className="w-12 h-12 bg-system-purple/20 rounded-full flex items-center justify-center mx-auto mb-3">
                    <RewardIcons.award className="w-6 h-6 text-system-purple" />
                  </div>
                  <p className="text-title-2 font-sf-pro font-bold text-label">
                    {formatNumber(userStats?.total_rewards || 0)}
                  </p>
                  <p className="text-caption-1 text-secondary-label">我的奖励</p>
                </div>
              </Card>
            </div>

            {/* 标签页 */}
            <div className="flex items-center space-x-1 mb-6">
              {[
                { key: 'browse', label: '浏览提案', icon: FeatureIcons.search },
                { key: 'my-votes', label: '我的投票', icon: HandRaisedIcon },
                { key: 'create', label: '创建提案', icon: ActionIcons.add },
              ].map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setActiveTab(tab.key as any)}
                  className={cn(
                    'flex items-center space-x-2 px-6 py-3 rounded-lg font-sf-pro font-medium transition-colors',
                    activeTab === tab.key
                      ? 'bg-system-blue text-white'
                      : 'text-secondary-label hover:text-label hover:bg-system-gray-6'
                  )}
                >
                  <tab.icon className="w-4 h-4" />
                  <span>{tab.label}</span>
                </button>
              ))}
            </div>

            {/* 过滤器 */}
            {activeTab === 'browse' && (
              <div className="flex flex-col sm:flex-row gap-4 mb-6">
                <div className="flex-1 relative">
                  <FeatureIcons.search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-secondary-label" />
                  <input
                    type="text"
                    placeholder="搜索提案..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-3 bg-system-gray-6 border border-system-gray-4 rounded-lg focus:outline-none focus:ring-2 focus:ring-system-blue focus:border-transparent text-label placeholder-secondary-label"
                  />
                </div>

                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="px-4 py-3 bg-system-gray-6 border border-system-gray-4 rounded-lg focus:outline-none focus:ring-2 focus:ring-system-blue text-label"
                >
                  <option value="all">全部状态</option>
                  <option value="active">进行中</option>
                  <option value="passed">已通过</option>
                  <option value="rejected">已拒绝</option>
                  <option value="executed">已执行</option>
                </select>

                <Button variant="outline" className="flex items-center space-x-2">
                  <FeatureIcons.filter className="w-4 h-4" />
                  <span>筛选</span>
                </Button>
              </div>
            )}

            {/* 提案列表 */}
            {activeTab === 'browse' && (
              <div className="space-y-4">
                {isLoading ? (
                  <div className="text-center py-12">
                    <div className="animate-spin w-8 h-8 border-2 border-system-blue border-t-transparent rounded-full mx-auto mb-4"></div>
                    <p className="text-body text-secondary-label">加载中...</p>
                  </div>
                ) : error ? (
                  <div className="text-center py-12">
                    <ActionIcons.alert className="w-16 h-16 text-system-red mx-auto mb-4" />
                    <h3 className="text-title-3 font-sf-pro font-semibold text-label mb-2">
                      加载失败
                    </h3>
                    <p className="text-body text-secondary-label mb-4">{error}</p>
                    <Button onClick={() => fetchProposals()} variant="outline">
                      重试
                    </Button>
                  </div>
                ) : filteredProposals.length === 0 ? (
                  <div className="text-center py-12">
                    <HandRaisedIcon className="w-16 h-16 text-secondary-label mx-auto mb-4" />
                    <h3 className="text-title-3 font-sf-pro font-semibold text-label mb-2">
                      暂无提案
                    </h3>
                    <p className="text-body text-secondary-label">
                      {searchTerm || selectedStatus !== 'all' ? '没有找到匹配的提案' : '暂时没有提案'}
                    </p>
                  </div>
                ) : (
                  filteredProposals.map((proposal, index) => {
                    const userVote = getUserVote(proposal.id);
                    const hasVoted = hasUserVoted(proposal.id);
                    
                    return (
                      <motion.div
                        key={proposal.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.05 }}
                      >
                        <Card className="hover:shadow-apple-lg transition-all duration-200">
                          <div className="p-6">
                            <div className="flex items-start justify-between mb-4">
                              <div className="flex-1">
                                <div className="flex items-center space-x-3 mb-2">
                                  <h3 className="text-headline font-sf-pro font-semibold text-label">
                                    {proposal.title}
                                  </h3>
                                  <span className={cn(
                                    'px-2 py-1 rounded-md text-caption-2 border',
                                    statusBgColors[proposal.status as keyof typeof statusBgColors]
                                  )}>
                                    {proposal.status === 'active' ? '进行中' :
                                     proposal.status === 'passed' ? '已通过' :
                                     proposal.status === 'rejected' ? '已拒绝' :
                                     proposal.status === 'executed' ? '已执行' : proposal.status}
                                  </span>
                                  <span className="px-2 py-1 bg-system-blue/10 text-system-blue text-caption-2 rounded-md">
                                    {proposal.proposal_type.display_name}
                                  </span>
                                </div>
                                
                                <p className="text-body text-secondary-label mb-3 line-clamp-2">
                                  {proposal.description}
                                </p>
                                
                                <div className="flex items-center space-x-4 text-caption-1 text-secondary-label">
                                  <div className="flex items-center space-x-1">
                                    <UserIcons.users className="w-3 h-3" />
                                    <span>{proposal.voter_count} 人投票</span>
                                  </div>
                                  <div className="flex items-center space-x-1">
                                    <FeatureIcons.clock className="w-3 h-3" />
                                    <span>{formatTimeRemaining(proposal.time_remaining)}</span>
                                  </div>
                                  <div className="flex items-center space-x-1">
                                    <FinanceIcons.barChart className="w-3 h-3" />
                                    <span>通过率 {proposal.approval_rate.toFixed(1)}%</span>
                                  </div>
                                </div>
                              </div>
                              
                              <div className="text-right ml-6">
                                <div className="text-title-3 font-sf-pro font-bold text-label mb-1">
                                  {formatNumber(proposal.total_votes)} 票
                                </div>
                                <div className="text-caption-1 text-secondary-label mb-3">
                                  质押: {formatNumber(proposal.stake_amount)} HAOX
                                </div>
                                
                                {hasVoted ? (
                                  <div className={cn(
                                    'px-3 py-1 rounded-lg text-caption-1 font-sf-pro font-medium',
                                    userVote?.vote_choice === 'for' ? 'bg-system-green/10 text-system-green' :
                                    userVote?.vote_choice === 'against' ? 'bg-system-red/10 text-system-red' :
                                    'bg-system-orange/10 text-system-orange'
                                  )}>
                                    已投票: {userVote?.vote_choice === 'for' ? '赞成' :
                                            userVote?.vote_choice === 'against' ? '反对' : '弃权'}
                                  </div>
                                ) : proposal.can_vote ? (
                                  <Button
                                    onClick={() => {
                                      setSelectedProposal(proposal);
                                      setShowVoteModal(true);
                                    }}
                                    size="sm"
                                    className="bg-system-blue"
                                  >
                                    立即投票
                                  </Button>
                                ) : (
                                  <Button size="sm" variant="outline" disabled>
                                    不可投票
                                  </Button>
                                )}
                              </div>
                            </div>
                            
                            {/* 投票进度条 */}
                            <div className="space-y-2">
                              <div className="flex justify-between text-caption-1 text-secondary-label">
                                <span>赞成 {formatNumber(proposal.votes_for)}</span>
                                <span>反对 {formatNumber(proposal.votes_against)}</span>
                                <span>弃权 {formatNumber(proposal.votes_abstain)}</span>
                              </div>
                              <div className="w-full bg-system-gray-5 rounded-full h-2 overflow-hidden">
                                <div className="h-full flex">
                                  <div 
                                    className="bg-system-green"
                                    style={{ 
                                      width: proposal.total_votes > 0 ? 
                                        `${(proposal.votes_for / proposal.total_votes) * 100}%` : '0%' 
                                    }}
                                  />
                                  <div 
                                    className="bg-system-red"
                                    style={{ 
                                      width: proposal.total_votes > 0 ? 
                                        `${(proposal.votes_against / proposal.total_votes) * 100}%` : '0%' 
                                    }}
                                  />
                                  <div 
                                    className="bg-system-orange"
                                    style={{ 
                                      width: proposal.total_votes > 0 ? 
                                        `${(proposal.votes_abstain / proposal.total_votes) * 100}%` : '0%' 
                                    }}
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        </Card>
                      </motion.div>
                    );
                  })
                )}
              </div>
            )}

            {/* 我的投票标签页 */}
            {activeTab === 'my-votes' && (
              <div className="grid md:grid-cols-2 gap-6">
                <Card title="投票统计">
                  <div className="space-y-4">
                    <div className="text-center">
                      <p className="text-title-2 font-sf-pro font-bold text-system-blue">
                        {userStats?.votes_cast || 0}
                      </p>
                      <p className="text-caption-1 text-secondary-label">总投票次数</p>
                    </div>
                    <div className="grid grid-cols-2 gap-4 text-center">
                      <div>
                        <p className="text-headline font-sf-pro font-bold text-label">
                          {formatNumber(userStats?.voting_power_used || 0)}
                        </p>
                        <p className="text-caption-1 text-secondary-label">使用投票权重</p>
                      </div>
                      <div>
                        <p className="text-headline font-sf-pro font-bold text-label">
                          {userStats?.participation_rate || 0}%
                        </p>
                        <p className="text-caption-1 text-secondary-label">参与率</p>
                      </div>
                    </div>
                  </div>
                </Card>

                <Card title="治理奖励">
                  <div className="space-y-4">
                    <div className="text-center">
                      <p className="text-title-2 font-sf-pro font-bold text-system-green">
                        {formatNumber(userStats?.total_rewards || 0)} HAOX
                      </p>
                      <p className="text-caption-1 text-secondary-label">总治理奖励</p>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-body text-secondary-label">投票奖励</span>
                        <span className="text-body font-sf-pro font-semibold text-system-green">
                          +{formatNumber((userStats?.votes_cast || 0) * 10)} HAOX
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-body text-secondary-label">提案奖励</span>
                        <span className="text-body font-sf-pro font-semibold text-system-green">
                          +{formatNumber((userStats?.proposals_created || 0) * 100)} HAOX
                        </span>
                      </div>
                    </div>
                  </div>
                </Card>
              </div>
            )}

            {/* 创建提案标签页 */}
            {activeTab === 'create' && (
              <Card title="创建新提案">
                <div className="text-center py-12">
                  <ActionIcons.add className="w-16 h-16 text-secondary-label mx-auto mb-4" />
                  <h3 className="text-title-3 font-sf-pro font-semibold text-label mb-2">
                    发起治理提案
                  </h3>
                  <p className="text-body text-secondary-label mb-6">
                    提出改进建议，推动社区发展
                  </p>
                  <Button className="bg-system-blue">
                    开始创建
                  </Button>
                </div>
              </Card>
            )}
          </>
        )}
      </div>
    </div>
  );
}
