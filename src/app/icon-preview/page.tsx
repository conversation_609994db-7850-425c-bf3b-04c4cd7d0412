'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Card, Button, Icon } from '@/components/ui';
import {
  NavigationIcons,
  UserIcons,
  FinanceIcons,
  ActionIcons,
  SocialIcons,
  FeatureIcons,
  NotificationIcons,
  RewardIcons
} from '@/config/icons';

// 导入图标替换配置
import { iconSchemes, getIconFromScheme, type IconSchemeId } from '@/config/icon-replacement';

const IconPreviewPage = () => {
  const [selectedStyle, setSelectedStyle] = useState<'current' | 'flat' | 'brand' | 'mixed'>('current');

  // 图标示例数据
  const iconExamples = [
    { name: '用户', key: 'user', current: UserIcons.user, category: '用户' },
    { name: '钱包', key: 'wallet', current: FinanceIcons.wallet, category: '金融' },
    { name: '首页', key: 'home', current: NavigationIcons.home, category: '导航' },
    { name: '设置', key: 'settings', current: FeatureIcons.settings, category: '功能' },
    { name: '通知', key: 'bell', current: NotificationIcons.bell, category: '通知' },
    { name: '编辑', key: 'edit', current: ActionIcons.edit, category: '操作' },
    { name: '星标', key: 'star', current: RewardIcons.star, category: '奖励' },
    { name: '奖杯', key: 'trophy', current: RewardIcons.trophy, category: '奖励' },
  ];

  const styles = [
    {
      id: 'current' as const,
      name: '当前风格 (Lucide)',
      description: '线性设计，技术感强',
      scheme: null
    },
    {
      id: 'flat' as const,
      name: '扁平化风格 (Heroicons)',
      description: iconSchemes.flat.description,
      scheme: iconSchemes.flat
    },
    {
      id: 'brand' as const,
      name: '品牌化风格',
      description: iconSchemes.brand.description,
      scheme: iconSchemes.brand
    },
    {
      id: 'mixed' as const,
      name: '混合优化风格',
      description: iconSchemes.mixed.description,
      scheme: iconSchemes.mixed
    }
  ];

  const renderIcon = (iconData: any, styleId: string) => {
    if (styleId === 'current') {
      // 当前Lucide图标
      return <Icon icon={iconData.current} size="lg" className="text-system-blue" />;
    }

    // 获取替换方案中的图标
    const replacementIcon = getIconFromScheme(styleId as IconSchemeId, iconData.key, iconData.current);

    if (styleId === 'flat' || styleId === 'mixed') {
      // Heroicons样式
      if (replacementIcon !== iconData.current) {
        const HeroIcon = replacementIcon;
        return <HeroIcon className="w-6 h-6 text-system-blue stroke-2" />;
      } else {
        return <Icon icon={iconData.current} size="lg" className="text-system-blue" />;
      }
    } else if (styleId === 'brand') {
      // 品牌化样式 - 添加渐变色和特殊效果
      return (
        <div className="relative">
          <Icon
            icon={iconData.current}
            size="lg"
            className="text-transparent bg-gradient-to-br from-system-blue to-system-purple bg-clip-text"
          />
          <div className="absolute inset-0 bg-gradient-to-br from-system-blue/20 to-system-purple/20 rounded-lg blur-sm -z-10" />
        </div>
      );
    }

    return <Icon icon={iconData.current} size="lg" className="text-system-blue" />;
  };

  return (
    <div className="min-h-screen bg-system-background p-8">
      <div className="max-w-6xl mx-auto">
        {/* 页面标题 */}
        <div className="text-center mb-8">
          <h1 className="text-title-1 font-sf-pro font-bold text-label mb-4">
            图标风格预览
          </h1>
          <p className="text-body text-secondary-label">
            对比不同图标风格，选择最适合SocioMint的视觉方案
          </p>
        </div>

        {/* 风格选择器 */}
        <div className="flex justify-center mb-8">
          <div className="flex flex-wrap justify-center gap-2 p-2 bg-system-gray-6 rounded-xl">
            {styles.map((style) => (
              <button
                key={style.id}
                onClick={() => setSelectedStyle(style.id)}
                className={`px-4 py-2 rounded-lg font-sf-pro font-medium transition-all text-sm ${
                  selectedStyle === style.id
                    ? 'bg-system-blue text-white shadow-lg'
                    : 'text-secondary-label hover:text-label hover:bg-system-gray-5'
                }`}
              >
                {style.name}
              </button>
            ))}
          </div>
        </div>

        {/* 图标对比展示 */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          {styles.map((style) => (
            <motion.div
              key={style.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              <Card className={`p-6 ${selectedStyle === style.id ? 'ring-2 ring-system-blue' : ''}`}>
                <div className="text-center mb-6">
                  <h3 className="text-title-3 font-sf-pro font-semibold text-label mb-2">
                    {style.name}
                  </h3>
                  <p className="text-caption-1 text-secondary-label">
                    {style.description}
                  </p>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  {iconExamples.slice(0, 8).map((iconData, index) => (
                    <div
                      key={`${style.id}-${index}`}
                      className="flex flex-col items-center p-3 rounded-lg hover:bg-system-gray-6 transition-colors"
                    >
                      <div className="mb-2">
                        {renderIcon(iconData, style.id)}
                      </div>
                      <span className="text-caption-2 text-secondary-label text-center">
                        {iconData.name}
                      </span>
                    </div>
                  ))}
                </div>

                {/* 风格特点 */}
                <div className="mt-6 pt-4 border-t border-system-gray-4">
                  <div className="space-y-2">
                    {style.id === 'current' && (
                      <>
                        <div className="flex items-center text-caption-1">
                          <div className="w-2 h-2 bg-system-green rounded-full mr-2" />
                          <span className="text-secondary-label">技术感强，专业</span>
                        </div>
                        <div className="flex items-center text-caption-1">
                          <div className="w-2 h-2 bg-system-orange rounded-full mr-2" />
                          <span className="text-secondary-label">可能显得单调</span>
                        </div>
                      </>
                    )}
                    {style.scheme && (
                      <>
                        {style.scheme.pros.slice(0, 2).map((pro, index) => (
                          <div key={index} className="flex items-center text-caption-1">
                            <div className="w-2 h-2 bg-system-green rounded-full mr-2" />
                            <span className="text-secondary-label">{pro}</span>
                          </div>
                        ))}
                        {style.scheme.cons.slice(0, 1).map((con, index) => (
                          <div key={index} className="flex items-center text-caption-1">
                            <div className="w-2 h-2 bg-system-orange rounded-full mr-2" />
                            <span className="text-secondary-label">{con}</span>
                          </div>
                        ))}
                      </>
                    )}
                  </div>
                </div>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* 操作按钮 */}
        <div className="flex justify-center space-x-4 mt-8">
          <Button
            variant="outline"
            size="lg"
            onClick={() => window.history.back()}
          >
            返回
          </Button>
          <Button
            variant="primary"
            size="lg"
            onClick={() => {
              alert(`您选择了：${styles.find(s => s.id === selectedStyle)?.name}\n\n接下来我将为您实施这个方案。`);
            }}
          >
            选择此方案
          </Button>
        </div>
      </div>
    </div>
  );
};

export default IconPreviewPage;
