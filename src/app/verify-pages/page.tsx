'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, Button, Icon } from '@/components/ui';
import { ActionIcons, FeatureIcons, NavigationIcons } from '@/config/icons';
import Header from '@/components/layout/Header';

interface PageTest {
  name: string;
  path: string;
  status: 'pending' | 'success' | 'error';
  error?: string;
  responseTime?: number;
}

const VerifyPagesPage = () => {
  const [tests, setTests] = useState<PageTest[]>([
    { name: '白皮书页面', path: '/whitepaper', status: 'pending' },
    { name: '收款页面', path: '/profile/receive', status: 'pending' },
    { name: '交易记录页面', path: '/profile/transactions', status: 'pending' },
    { name: '活动历史页面', path: '/profile/history', status: 'pending' }
  ]);

  const [isRunning, setIsRunning] = useState(false);
  const [currentTest, setCurrentTest] = useState<number>(-1);

  const testPage = async (path: string): Promise<{ success: boolean; error?: string; responseTime: number }> => {
    const startTime = Date.now();
    
    try {
      const response = await fetch(path, {
        method: 'HEAD', // 只检查头部，不下载整个页面
        cache: 'no-cache'
      });
      
      const responseTime = Date.now() - startTime;
      
      if (response.ok) {
        return { success: true, responseTime };
      } else {
        return { 
          success: false, 
          error: `HTTP ${response.status}: ${response.statusText}`,
          responseTime 
        };
      }
    } catch (error) {
      const responseTime = Date.now() - startTime;
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '未知错误',
        responseTime 
      };
    }
  };

  const runTests = async () => {
    setIsRunning(true);
    setCurrentTest(0);

    for (let i = 0; i < tests.length; i++) {
      setCurrentTest(i);
      
      const test = tests[i];
      const result = await testPage(test.path);
      
      setTests(prev => prev.map((t, index) => 
        index === i 
          ? {
              ...t,
              status: result.success ? 'success' : 'error',
              error: result.error,
              responseTime: result.responseTime
            }
          : t
      ));

      // 添加延迟以便用户看到进度
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    setCurrentTest(-1);
    setIsRunning(false);
  };

  const getStatusIcon = (status: PageTest['status']) => {
    switch (status) {
      case 'success':
        return ActionIcons.check;
      case 'error':
        return ActionIcons.alert;
      default:
        return FeatureIcons.clock;
    }
  };

  const getStatusColor = (status: PageTest['status']) => {
    switch (status) {
      case 'success':
        return 'text-system-green';
      case 'error':
        return 'text-system-red';
      default:
        return 'text-secondary-label';
    }
  };

  const successCount = tests.filter(t => t.status === 'success').length;
  const errorCount = tests.filter(t => t.status === 'error').length;
  const averageResponseTime = tests
    .filter(t => t.responseTime)
    .reduce((sum, t) => sum + (t.responseTime || 0), 0) / tests.filter(t => t.responseTime).length;

  return (
    <div className="min-h-screen bg-system-background">
      <Header />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-8"
        >
          <h1 className="text-3xl font-bold text-label mb-4">
            页面验证测试
          </h1>
          <p className="text-lg text-secondary-label">
            自动测试所有新创建页面的可访问性和响应时间
          </p>
        </motion.div>

        {/* 测试控制 */}
        <Card className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-label mb-2">测试控制</h3>
              <p className="text-sm text-secondary-label">
                点击开始测试所有页面的可访问性
              </p>
            </div>
            <Button
              onClick={runTests}
              disabled={isRunning}
              className="flex items-center space-x-2"
            >
              <Icon
                icon={isRunning ? FeatureIcons.clock : ActionIcons.refresh}
                size="sm"
              />
              <span>{isRunning ? '测试中...' : '开始测试'}</span>
            </Button>
          </div>
        </Card>

        {/* 测试结果 */}
        <Card className="mb-8">
          <h3 className="text-lg font-semibold text-label mb-6">测试结果</h3>
          
          <div className="space-y-4">
            {tests.map((test, index) => (
              <motion.div
                key={test.path}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className={`flex items-center justify-between p-4 rounded-lg border ${
                  currentTest === index 
                    ? 'border-system-blue bg-system-blue/5' 
                    : 'border-system-gray-4'
                }`}
              >
                <div className="flex items-center space-x-4">
                  <Icon 
                    icon={getStatusIcon(test.status)} 
                    size="md" 
                    className={getStatusColor(test.status)}
                  />
                  <div>
                    <h4 className="font-medium text-label">{test.name}</h4>
                    <p className="text-sm text-secondary-label font-mono">{test.path}</p>
                    {test.error && (
                      <p className="text-sm text-system-red mt-1">{test.error}</p>
                    )}
                  </div>
                </div>
                
                <div className="text-right">
                  {test.responseTime && (
                    <div className="text-sm text-secondary-label">
                      {test.responseTime}ms
                    </div>
                  )}
                  <a
                    href={test.path}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-system-blue hover:underline"
                  >
                    访问页面
                  </a>
                </div>
              </motion.div>
            ))}
          </div>
        </Card>

        {/* 统计信息 */}
        {(successCount > 0 || errorCount > 0) && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="text-center">
              <Icon icon={ActionIcons.check} size="lg" className="text-system-green mx-auto mb-2" />
              <div className="text-2xl font-bold text-label">{successCount}</div>
              <div className="text-sm text-secondary-label">成功</div>
            </Card>
            
            <Card className="text-center">
              <Icon icon={ActionIcons.alert} size="lg" className="text-system-red mx-auto mb-2" />
              <div className="text-2xl font-bold text-label">{errorCount}</div>
              <div className="text-sm text-secondary-label">失败</div>
            </Card>
            
            <Card className="text-center">
              <Icon icon={FeatureIcons.clock} size="lg" className="text-system-blue mx-auto mb-2" />
              <div className="text-2xl font-bold text-label">
                {averageResponseTime ? Math.round(averageResponseTime) : 0}ms
              </div>
              <div className="text-sm text-secondary-label">平均响应时间</div>
            </Card>
          </div>
        )}

        {/* 返回按钮 */}
        <div className="text-center mt-8">
          <Button
            variant="outline"
            onClick={() => window.history.back()}
            className="flex items-center space-x-2"
          >
            <Icon icon={NavigationIcons.back} size="sm" />
            <span>返回</span>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default VerifyPagesPage;
