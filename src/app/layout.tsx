import type { Metadata, Viewport } from "next";
import "./globals.css";
import { ToastProvider } from "@/components/ui";
import ErrorBoundary from "@/components/ErrorBoundary";
import { AuthProvider } from "@/contexts/AuthContext";
import WindowExtensionProvider from "@/components/WindowExtensionProvider";

export const metadata: Metadata = {
  title: "SocioMint - 社交化数字资产生态系统",
  description: "基于社交平台集成的HAOX代币生态系统，支持社交任务和奖励系统",
  keywords: "加密货币, HAOX, 代币, 社交平台, 区块链, 数字资产",
  authors: [{ name: "SocioMint Team" }],
  openGraph: {
    title: "SocioMint - 社交化数字资产生态系统",
    description: "基于社交平台集成的HAOX代币生态系统",
    type: "website",
    locale: "zh_CN",
  },
  twitter: {
    card: "summary_large_image",
    title: "SocioMint - 社交化数字资产生态系统",
    description: "基于社交平台集成的HAOX代币生态系统",
  },
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  themeColor: "#007AFF",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN">
      <body className="font-sf-pro antialiased">
        <WindowExtensionProvider>
          <ErrorBoundary>
            <AuthProvider>
              <ToastProvider>
                {children}
              </ToastProvider>
            </AuthProvider>
          </ErrorBoundary>
        </WindowExtensionProvider>
      </body>
    </html>
  );
}
