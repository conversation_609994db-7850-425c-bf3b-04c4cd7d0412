'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, Icon, Button } from '@/components/ui';
import {
  RewardIcons,
  UserIcons,
  FinanceIcons,
  ActionIcons
} from '@/config/icons';

import { cn } from '@/lib/utils';

interface LeaderboardEntry {
  rank: number;
  userId: string;
  username: string;
  avatar?: string;
  invitations: number;
  rewards: number;
  level: number;
  badge?: string;
}

const LeaderboardPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'weekly' | 'monthly' | 'all'>('weekly');
  const [leaderboardData, setLeaderboardData] = useState<LeaderboardEntry[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // 模拟数据
  useEffect(() => {
    const mockData: LeaderboardEntry[] = [
      {
        rank: 1,
        userId: '1',
        username: 'CryptoKing',
        invitations: 156,
        rewards: 15600,
        level: 10,
        badge: '👑'
      },
      {
        rank: 2,
        userId: '2',
        username: 'BlockchainMaster',
        invitations: 142,
        rewards: 14200,
        level: 9,
        badge: '🥈'
      },
      {
        rank: 3,
        userId: '3',
        username: 'TokenHunter',
        invitations: 128,
        rewards: 12800,
        level: 8,
        badge: '🥉'
      },
      {
        rank: 4,
        userId: '4',
        username: 'DeFiExplorer',
        invitations: 95,
        rewards: 9500,
        level: 7
      },
      {
        rank: 5,
        userId: '5',
        username: 'Web3Pioneer',
        invitations: 87,
        rewards: 8700,
        level: 6
      }
    ];

    setTimeout(() => {
      setLeaderboardData(mockData);
      setIsLoading(false);
    }, 1000);
  }, [activeTab]);

  const tabs = [
    { id: 'weekly' as const, name: '本周', icon: RewardIcons.trophy },
    { id: 'monthly' as const, name: '本月', icon: RewardIcons.crown },
    { id: 'all' as const, name: '总榜', icon: RewardIcons.star }
  ];

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return '🏆';
      case 2:
        return '🥈';
      case 3:
        return '🥉';
      default:
        return rank.toString();
    }
  };

  const getRankColor = (rank: number) => {
    switch (rank) {
      case 1:
        return 'text-yellow-500';
      case 2:
        return 'text-gray-400';
      case 3:
        return 'text-orange-500';
      default:
        return 'text-label';
    }
  };

  return (
    <div className="p-6">
      {/* 页面标题 */}
      <div className="text-center mb-8">
        <h2 className="text-title-2 font-sf-pro font-bold text-label mb-2">
          邀请排行榜
        </h2>
        <p className="text-body text-secondary-label">
          查看社区中最活跃的邀请者
        </p>
      </div>

        {/* 标签页 */}
        <motion.div
          className="flex justify-center mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.2 }}
        >
          <div className="bg-system-gray-6/80 backdrop-blur-sm rounded-2xl p-1.5 border border-system-gray-4/30 shadow-apple flex flex-wrap sm:flex-nowrap gap-1">
            {tabs.map((tab, index) => (
              <motion.button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={cn(
                  'relative flex items-center space-x-2 px-4 sm:px-6 py-3 rounded-xl font-sf-pro font-semibold text-sm transition-all duration-200',
                  'hover:scale-[1.02] active:scale-[0.98] min-w-0 flex-1 sm:flex-initial',
                  activeTab === tab.id
                    ? 'bg-white text-label shadow-apple border border-system-gray-4/20'
                    : 'text-secondary-label hover:text-label hover:bg-system-gray-5/50'
                )}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <Icon
                  icon={tab.icon}
                  size="sm"
                  className={cn(
                    'transition-colors duration-200',
                    activeTab === tab.id ? 'text-system-blue' : 'text-secondary-label'
                  )}
                />
                <span className="relative z-10">{tab.name}</span>

                {/* 激活状态指示器 */}
                {activeTab === tab.id && (
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-system-blue/5 to-system-purple/5 rounded-xl"
                    layoutId="activeTab"
                    transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                  />
                )}
              </motion.button>
            ))}
          </div>
        </motion.div>

        {/* 排行榜内容 */}
        <div className="max-w-4xl mx-auto">
          {isLoading ? (
            <Card>
              <div className="text-center py-12">
                <div className="w-8 h-8 border-2 border-system-blue border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                <p className="text-secondary-label">加载排行榜数据...</p>
              </div>
            </Card>
          ) : (
            <Card>
              <div className="space-y-4">
                {leaderboardData.map((entry, index) => (
                  <motion.div
                    key={entry.userId}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    className={cn(
                      'flex items-center justify-between p-4 rounded-xl transition-all',
                      entry.rank <= 3
                        ? 'bg-gradient-to-r from-system-blue/5 to-system-purple/5 border border-system-blue/20'
                        : 'bg-system-gray-6'
                    )}
                  >
                    <div className="flex items-center space-x-4">
                      {/* 排名 */}
                      <div className={cn(
                        'flex items-center justify-center w-12 h-12 rounded-full font-bold text-lg',
                        entry.rank <= 3 ? 'bg-white shadow-sm' : 'bg-system-gray-5'
                      )}>
                        <span className={getRankColor(entry.rank)}>
                          {getRankIcon(entry.rank)}
                        </span>
                      </div>

                      {/* 用户信息 */}
                      <div>
                        <div className="flex items-center space-x-2">
                          <h3 className="font-sf-pro font-semibold text-label">
                            {entry.username}
                          </h3>
                          {entry.badge && (
                            <span className="text-lg">{entry.badge}</span>
                          )}
                        </div>
                        <p className="text-sm text-secondary-label">
                          等级 {entry.level}
                        </p>
                      </div>
                    </div>

                    {/* 统计数据 */}
                    <div className="flex items-center space-x-6">
                      <div className="text-center">
                        <div className="flex items-center space-x-1 text-sm text-secondary-label">
                          <Icon icon={UserIcons.users} size="sm" />
                          <span>邀请</span>
                        </div>
                        <p className="font-sf-pro font-bold text-label">
                          {entry.invitations}
                        </p>
                      </div>
                      
                      <div className="text-center">
                        <div className="flex items-center space-x-1 text-sm text-secondary-label">
                          <Icon icon={FinanceIcons.coins} size="sm" />
                          <span>奖励</span>
                        </div>
                        <p className="font-sf-pro font-bold text-label">
                          {entry.rewards.toLocaleString()}
                        </p>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </Card>
          )}
        </div>

        {/* 用户排名信息卡片 */}
        <Card className="mt-8">
          <div className="p-6 text-center">
            <div className="flex items-center justify-center space-x-2 mb-4">
              <Icon icon={UserIcons.user} size="md" className="text-system-blue" />
              <h3 className="text-title-3 font-sf-pro font-semibold text-label">
                我的排名
              </h3>
            </div>

            <div className="bg-system-gray-6 rounded-xl p-4 mb-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-caption-1 text-secondary-label mb-1">当前排名</p>
                  <p className="text-title-2 font-sf-pro font-bold text-system-blue">
                    #--
                  </p>
                </div>
                <div>
                  <p className="text-caption-1 text-secondary-label mb-1">邀请数量</p>
                  <p className="text-title-2 font-sf-pro font-bold text-label">
                    0
                  </p>
                </div>
              </div>
            </div>

            <div className="flex justify-center space-x-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.location.href = '/invitation'}
              >
                <Icon icon={UserIcons.users} size="sm" className="mr-2" />
                邀请好友
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.location.reload()}
              >
                <Icon icon={ActionIcons.refresh} size="sm" className="mr-2" />
                刷新排行榜
              </Button>
            </div>
          </div>
        </Card>

        {/* 底部说明 */}
        <div className="text-center mt-8">
          <p className="text-sm text-secondary-label">
            排行榜每小时更新一次，数据基于成功邀请数量和获得的HAOX奖励
          </p>
        </div>
    </div>
  );
};

export default LeaderboardPage;
