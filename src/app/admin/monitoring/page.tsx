/**
 * 监控告警仪表板页面
 * 提供系统监控、异常检测、日志分析的可视化界面
 */

'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

// 监控数据接口
interface MonitoringData {
  current: any;
  historical: any[];
  anomalies: any[];
  logs: any[];
  stats: any;
}

// 系统状态组件
function SystemStatus({ status, metrics }: { status: string; metrics: any }) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600 bg-green-100';
      case 'degraded': return 'text-yellow-600 bg-yellow-100';
      case 'unhealthy': return 'text-orange-600 bg-orange-100';
      case 'critical': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return '✅';
      case 'degraded': return '⚠️';
      case 'unhealthy': return '🔶';
      case 'critical': return '🚨';
      default: return '❓';
    }
  };

  return (
    <motion.div
      className="bg-white rounded-lg shadow-sm p-6"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold text-label">系统状态</h2>
        <div className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(status)}`}>
          {getStatusIcon(status)} {status.toUpperCase()}
        </div>
      </div>

      {metrics && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-label">
              {Math.round(metrics.system?.uptime / 3600 || 0)}h
            </div>
            <div className="text-sm text-secondary-label">运行时间</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-label">
              {metrics.system?.memory?.percentage?.toFixed(1) || 0}%
            </div>
            <div className="text-sm text-secondary-label">内存使用</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-label">
              {metrics.application?.activeUsers || 0}
            </div>
            <div className="text-sm text-secondary-label">活跃用户</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-label">
              {metrics.application?.responseTime?.toFixed(0) || 0}ms
            </div>
            <div className="text-sm text-secondary-label">响应时间</div>
          </div>
        </div>
      )}
    </motion.div>
  );
}

// 异常告警组件
function AnomalyAlerts({ anomalies }: { anomalies: any[] }) {
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'border-red-500 bg-red-50';
      case 'high': return 'border-orange-500 bg-orange-50';
      case 'medium': return 'border-yellow-500 bg-yellow-50';
      case 'low': return 'border-blue-500 bg-blue-50';
      default: return 'border-gray-500 bg-gray-50';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical': return '🚨';
      case 'high': return '⚠️';
      case 'medium': return '⚡';
      case 'low': return 'ℹ️';
      default: return '📝';
    }
  };

  return (
    <motion.div
      className="bg-white rounded-lg shadow-sm p-6"
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.5, delay: 0.1 }}
    >
      <h2 className="text-xl font-semibold text-label mb-4">异常告警</h2>
      
      {anomalies.length === 0 ? (
        <div className="text-center py-8 text-secondary-label">
          <div className="text-4xl mb-2">✅</div>
          <p>暂无异常告警</p>
        </div>
      ) : (
        <div className="space-y-3 max-h-96 overflow-y-auto">
          <AnimatePresence>
            {anomalies.map((anomaly, index) => (
              <motion.div
                key={anomaly.id}
                className={`border-l-4 p-4 rounded ${getSeverityColor(anomaly.severity)}`}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center mb-1">
                      <span className="mr-2">{getSeverityIcon(anomaly.severity)}</span>
                      <span className="font-medium text-label">{anomaly.type}</span>
                      <span className="ml-2 px-2 py-1 text-xs rounded bg-white">
                        {anomaly.severity}
                      </span>
                    </div>
                    <p className="text-sm text-secondary-label">{anomaly.description}</p>
                    <p className="text-xs text-tertiary-label mt-1">
                      {new Date(anomaly.timestamp).toLocaleString()}
                    </p>
                  </div>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
        </div>
      )}
    </motion.div>
  );
}

// 实时指标图表组件
function MetricsChart({ data, title, color = '#3B82F6' }: { 
  data: any[]; 
  title: string; 
  color?: string; 
}) {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    if (!canvasRef.current || !data.length) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 清除画布
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // 设置画布大小
    const rect = canvas.getBoundingClientRect();
    canvas.width = rect.width * window.devicePixelRatio;
    canvas.height = rect.height * window.devicePixelRatio;
    ctx.scale(window.devicePixelRatio, window.devicePixelRatio);

    const width = rect.width;
    const height = rect.height;
    const padding = 20;

    // 绘制网格
    ctx.strokeStyle = '#E5E7EB';
    ctx.lineWidth = 1;
    
    for (let i = 0; i <= 5; i++) {
      const y = padding + (height - 2 * padding) * i / 5;
      ctx.beginPath();
      ctx.moveTo(padding, y);
      ctx.lineTo(width - padding, y);
      ctx.stroke();
    }

    // 绘制数据线
    if (data.length > 1) {
      ctx.strokeStyle = color;
      ctx.lineWidth = 2;
      ctx.beginPath();

      const maxValue = Math.max(...data);
      const minValue = Math.min(...data);
      const range = maxValue - minValue || 1;

      data.forEach((value, index) => {
        const x = padding + (width - 2 * padding) * index / (data.length - 1);
        const y = height - padding - (height - 2 * padding) * (value - minValue) / range;
        
        if (index === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      });

      ctx.stroke();

      // 绘制数据点
      ctx.fillStyle = color;
      data.forEach((value, index) => {
        const x = padding + (width - 2 * padding) * index / (data.length - 1);
        const y = height - padding - (height - 2 * padding) * (value - minValue) / range;
        
        ctx.beginPath();
        ctx.arc(x, y, 3, 0, 2 * Math.PI);
        ctx.fill();
      });
    }
  }, [data, color]);

  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <h3 className="text-lg font-medium text-label mb-4">{title}</h3>
      <div className="relative h-48">
        <canvas
          ref={canvasRef}
          className="absolute inset-0 w-full h-full"
          style={{ width: '100%', height: '100%' }}
        />
      </div>
    </div>
  );
}

// 日志查看器组件
function LogViewer({ logs }: { logs: any[] }) {
  const [filter, setFilter] = useState('all');
  const [searchText, setSearchText] = useState('');

  const filteredLogs = logs.filter(log => {
    if (filter !== 'all' && log.level !== filter) return false;
    if (searchText && !log.message.toLowerCase().includes(searchText.toLowerCase())) return false;
    return true;
  });

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'error': return 'text-red-600 bg-red-100';
      case 'warn': return 'text-yellow-600 bg-yellow-100';
      case 'info': return 'text-blue-600 bg-blue-100';
      case 'debug': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <motion.div
      className="bg-white rounded-lg shadow-sm p-6"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.3 }}
    >
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold text-label">系统日志</h2>
        <div className="flex space-x-2">
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            className="px-3 py-1 border border-gray-300 rounded text-sm"
          >
            <option value="all">所有级别</option>
            <option value="error">错误</option>
            <option value="warn">警告</option>
            <option value="info">信息</option>
            <option value="debug">调试</option>
          </select>
          <input
            type="text"
            placeholder="搜索日志..."
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            className="px-3 py-1 border border-gray-300 rounded text-sm w-48"
          />
        </div>
      </div>

      <div className="space-y-2 max-h-96 overflow-y-auto">
        {filteredLogs.map((log, index) => (
          <motion.div
            key={log.id}
            className="flex items-start space-x-3 p-3 hover:bg-gray-50 rounded"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.2, delay: index * 0.05 }}
          >
            <span className={`px-2 py-1 text-xs rounded font-medium ${getLevelColor(log.level)}`}>
              {log.level.toUpperCase()}
            </span>
            <div className="flex-1 min-w-0">
              <p className="text-sm text-label truncate">{log.message}</p>
              <p className="text-xs text-tertiary-label">
                {new Date(log.timestamp).toLocaleString()} • {log.category} • {log.source}
              </p>
            </div>
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
}

// 主仪表板组件
export default function MonitoringDashboard() {
  const [data, setData] = useState<MonitoringData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);

  // 获取监控数据
  const fetchData = async () => {
    try {
      const response = await fetch('/api/admin/monitoring?type=overview');
      if (!response.ok) throw new Error('Failed to fetch monitoring data');
      
      const result = await response.json();
      if (result.success) {
        setData(result.data);
        setError(null);
      } else {
        setError(result.error || 'Unknown error');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  // 初始加载和自动刷新
  useEffect(() => {
    fetchData();

    if (autoRefresh) {
      const interval = setInterval(fetchData, 30000); // 30秒刷新一次
      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-system-blue mx-auto mb-4"></div>
          <p className="text-secondary-label">加载监控数据...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="text-4xl mb-4">❌</div>
          <p className="text-red-600 mb-4">加载失败: {error}</p>
          <button
            onClick={fetchData}
            className="px-4 py-2 bg-system-blue text-white rounded hover:bg-blue-600"
          >
            重试
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* 页面标题 */}
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-label">监控告警仪表板</h1>
          <div className="flex items-center space-x-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={autoRefresh}
                onChange={(e) => setAutoRefresh(e.target.checked)}
                className="mr-2"
              />
              <span className="text-sm text-secondary-label">自动刷新</span>
            </label>
            <button
              onClick={fetchData}
              className="px-4 py-2 bg-system-blue text-white rounded hover:bg-blue-600 transition-colors"
            >
              手动刷新
            </button>
          </div>
        </div>

        {/* 系统状态 */}
        <SystemStatus 
          status={data?.current ? 'healthy' : 'unknown'} 
          metrics={data?.current} 
        />

        {/* 指标图表和异常告警 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <MetricsChart
            data={data?.historical?.map(m => m.application?.responseTime || 0) || []}
            title="API响应时间趋势"
            color="#3B82F6"
          />
          <AnomalyAlerts anomalies={data?.anomalies || []} />
        </div>

        {/* 更多指标图表 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <MetricsChart
            data={data?.historical?.map(m => m.system?.memory?.percentage || 0) || []}
            title="内存使用率"
            color="#EF4444"
          />
          <MetricsChart
            data={data?.historical?.map(m => m.application?.activeUsers || 0) || []}
            title="活跃用户数"
            color="#10B981"
          />
        </div>

        {/* 日志查看器 */}
        <LogViewer logs={data?.logs || []} />
      </div>
    </div>
  );
}
