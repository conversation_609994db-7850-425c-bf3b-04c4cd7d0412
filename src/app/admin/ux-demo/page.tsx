/**
 * 用户体验改进演示页面
 * 展示反馈系统、响应式设计、用户引导、加载优化等功能
 */

'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FeedbackProvider, useFeedback } from '@/lib/contexts/FeedbackContext';
import { UserGuideProvider, useUserGuide, GuideManager, Tooltip } from '@/lib/components/UserGuide';
import { useResponsive, useMobileOptimization } from '@/lib/hooks/useResponsive';
import { 
  PageLoader, 
  LazyLoad, 
  ProgressiveImage, 
  Skeleton, 
  SkeletonPresets,
  CodeSplitLoader
} from '@/lib/components/PageLoader';

// 演示组件
function UXDemoContent() {
  const [loading, setLoading] = useState(false);
  const [progress, setProgress] = useState(0);
  const feedback = useFeedback();
  const guide = useUserGuide();
  const responsive = useResponsive();
  const mobile = useMobileOptimization();

  // 注册用户引导
  React.useEffect(() => {
    const guideManager = GuideManager.getInstance();
    
    guideManager.registerGuide({
      id: 'ux-demo-guide',
      name: 'UX演示引导',
      steps: [
        {
          id: 'step1',
          target: '#feedback-demo',
          title: '反馈系统',
          content: '这里展示了全局反馈系统，包括成功、错误、警告和信息提示。',
          position: 'bottom'
        },
        {
          id: 'step2',
          target: '#responsive-demo',
          title: '响应式设计',
          content: '响应式设计确保在不同设备上都有良好的用户体验。',
          position: 'bottom'
        },
        {
          id: 'step3',
          target: '#loading-demo',
          title: '加载状态',
          content: '优雅的加载状态让用户了解系统正在处理他们的请求。',
          position: 'top'
        }
      ],
      showProgress: true,
      allowSkip: true,
      onComplete: () => {
        feedback.showMessage({
          type: 'success',
          title: '引导完成',
          message: '您已完成用户体验演示引导！'
        });
      }
    });
  }, [feedback]);

  // 演示反馈消息
  const showFeedbackDemo = (type: 'success' | 'error' | 'warning' | 'info') => {
    const messages = {
      success: { title: '操作成功', message: '您的操作已成功完成！' },
      error: { title: '操作失败', message: '抱歉，操作过程中出现了错误。' },
      warning: { title: '注意', message: '请注意这个重要提醒。' },
      info: { title: '信息', message: '这是一条信息提示。' }
    };

    feedback.showMessage({
      type,
      ...messages[type],
      action: type === 'error' ? {
        label: '重试',
        onClick: () => showFeedbackDemo('success')
      } : undefined
    });
  };

  // 演示加载状态
  const showLoadingDemo = async () => {
    setLoading(true);
    setProgress(0);

    const loadingId = feedback.showLoading('正在处理您的请求...');

    // 模拟进度更新
    for (let i = 0; i <= 100; i += 10) {
      await new Promise(resolve => setTimeout(resolve, 200));
      setProgress(i);
      feedback.updateLoading(loadingId, `处理中... ${i}%`, i);
    }

    feedback.hideLoading(loadingId);
    setLoading(false);
    setProgress(0);
    
    feedback.showMessage({
      type: 'success',
      title: '加载完成',
      message: '数据已成功加载！'
    });
  };

  return (
    <div className={`min-h-screen bg-background p-4 ${mobile.mobileClasses} ${mobile.touchClasses}`}>
      <div className="max-w-6xl mx-auto space-y-8">
        {/* 页面标题 */}
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <h1 className="text-3xl font-bold text-label mb-4">用户体验改进演示</h1>
          <p className="text-secondary-label">
            展示反馈系统、响应式设计、用户引导和加载优化功能
          </p>
        </motion.div>

        {/* 设备信息 */}
        <motion.div
          className="bg-white rounded-lg shadow-sm p-6"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <h2 className="text-xl font-semibold mb-4">当前设备信息</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="font-medium">屏幕尺寸:</span> {responsive.width} × {responsive.height}
            </div>
            <div>
              <span className="font-medium">断点:</span> {responsive.breakpoint}
            </div>
            <div>
              <span className="font-medium">设备类型:</span> {responsive.deviceType}
            </div>
            <div>
              <span className="font-medium">屏幕方向:</span> {responsive.orientation}
            </div>
            <div>
              <span className="font-medium">触摸设备:</span> {responsive.isTouch ? '是' : '否'}
            </div>
            <div>
              <span className="font-medium">像素比:</span> {responsive.pixelRatio}
            </div>
          </div>
        </motion.div>

        {/* 反馈系统演示 */}
        <motion.div
          id="feedback-demo"
          className="bg-white rounded-lg shadow-sm p-6"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <h2 className="text-xl font-semibold mb-4">反馈系统演示</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <button
              onClick={() => showFeedbackDemo('success')}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
            >
              成功消息
            </button>
            <button
              onClick={() => showFeedbackDemo('error')}
              className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
            >
              错误消息
            </button>
            <button
              onClick={() => showFeedbackDemo('warning')}
              className="px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700 transition-colors"
            >
              警告消息
            </button>
            <button
              onClick={() => showFeedbackDemo('info')}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              信息消息
            </button>
          </div>
        </motion.div>

        {/* 响应式设计演示 */}
        <motion.div
          id="responsive-demo"
          className="bg-white rounded-lg shadow-sm p-6"
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <h2 className="text-xl font-semibold mb-4">响应式设计演示</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {[1, 2, 3, 4, 5, 6].map(i => (
              <div key={i} className="bg-gray-100 p-4 rounded">
                <h3 className="font-medium mb-2">卡片 {i}</h3>
                <p className="text-sm text-secondary-label">
                  这是一个响应式卡片，会根据屏幕大小自动调整布局。
                </p>
              </div>
            ))}
          </div>
        </motion.div>

        {/* 加载状态演示 */}
        <motion.div
          id="loading-demo"
          className="bg-white rounded-lg shadow-sm p-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <h2 className="text-xl font-semibold mb-4">加载状态演示</h2>
          <div className="space-y-4">
            <div className="flex flex-wrap gap-4">
              <button
                onClick={showLoadingDemo}
                disabled={loading}
                className="px-4 py-2 bg-system-blue text-white rounded hover:bg-blue-600 disabled:opacity-50 transition-colors"
              >
                {loading ? '加载中...' : '开始加载演示'}
              </button>
              
              <Tooltip content="这是一个工具提示示例" position="top">
                <button className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors">
                  悬停查看提示
                </button>
              </Tooltip>
            </div>

            {loading && (
              <div className="space-y-4">
                <PageLoader loading={true} message="正在加载数据..." progress={progress} type="bar" />
                <PageLoader loading={true} type="spinner" size="sm" />
                <PageLoader loading={true} type="dots" />
              </div>
            )}
          </div>
        </motion.div>

        {/* 骨架屏演示 */}
        <motion.div
          className="bg-white rounded-lg shadow-sm p-6"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, delay: 0.5 }}
        >
          <h2 className="text-xl font-semibold mb-4">骨架屏演示</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-medium mb-2">卡片骨架屏</h3>
              <SkeletonPresets.Card />
            </div>
            <div>
              <h3 className="font-medium mb-2">用户资料骨架屏</h3>
              <SkeletonPresets.Profile />
            </div>
          </div>
        </motion.div>

        {/* 懒加载演示 */}
        <motion.div
          className="bg-white rounded-lg shadow-sm p-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
        >
          <h2 className="text-xl font-semibold mb-4">懒加载演示</h2>
          <p className="text-secondary-label mb-4">
            向下滚动查看懒加载内容（当内容进入视口时才会加载）
          </p>
          
          {[1, 2, 3].map(i => (
            <LazyLoad
              key={i}
              fallback={<Skeleton height="200px" className="mb-4" />}
              className="mb-4"
            >
              <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-8 rounded-lg">
                <h3 className="text-xl font-semibold mb-2">懒加载内容 {i}</h3>
                <p>这个内容只有在进入视口时才会加载，提高了页面性能。</p>
              </div>
            </LazyLoad>
          ))}
        </motion.div>

        {/* 用户引导控制 */}
        <motion.div
          className="bg-white rounded-lg shadow-sm p-6"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, delay: 0.7 }}
        >
          <h2 className="text-xl font-semibold mb-4">用户引导系统</h2>
          <div className="flex flex-wrap gap-4">
            <button
              onClick={() => guide.startGuide('ux-demo-guide')}
              className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors"
            >
              开始用户引导
            </button>
            
            <button
              onClick={() => guide.markGuideCompleted('ux-demo-guide')}
              className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
            >
              标记为已完成
            </button>
            
            <span className="px-4 py-2 bg-gray-100 rounded text-sm">
              引导状态: {guide.isGuideCompleted('ux-demo-guide') ? '已完成' : '未完成'}
            </span>
          </div>
        </motion.div>
      </div>
    </div>
  );
}

// 主页面组件
export default function UXDemoPage() {
  return (
    <FeedbackProvider>
      <UserGuideProvider>
        <CodeSplitLoader>
          <UXDemoContent />
        </CodeSplitLoader>
      </UserGuideProvider>
    </FeedbackProvider>
  );
}
