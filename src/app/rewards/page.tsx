'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, Button, Icon } from '@/components/ui';
import { RewardIcons, NavigationIcons } from '@/config/icons';
import Header from '@/components/layout/Header';
import InvitationPage from '@/app/invitation/page';
import LeaderboardPage from '@/app/leaderboard/page';

type TabType = 'invitation' | 'leaderboard';

const RewardsPage = () => {
  const [activeTab, setActiveTab] = useState<TabType>('invitation');

  const tabs = [
    {
      id: 'invitation' as TabType,
      name: '邀请奖励',
      icon: RewardIcons.gift,
      description: '邀请好友获得丰厚奖励'
    },
    {
      id: 'leaderboard' as TabType,
      name: '排行榜',
      icon: RewardIcons.trophy,
      description: '查看社区排行榜和奖励'
    }
  ];

  const tabVariants = {
    inactive: {
      backgroundColor: 'rgba(255, 255, 255, 0.05)',
      color: 'rgba(255, 255, 255, 0.7)',
      scale: 1
    },
    active: {
      backgroundColor: 'rgba(59, 130, 246, 0.15)',
      color: 'rgb(59, 130, 246)',
      scale: 1.02
    }
  };

  const contentVariants = {
    hidden: {
      opacity: 0,
      transform: 'translateX(20px) scale(0.95)'
    },
    visible: {
      opacity: 1,
      transform: 'translateX(0px) scale(1)',
      transition: {
        duration: 0.3,
        ease: 'easeOut'
      }
    },
    exit: {
      opacity: 0,
      transform: 'translateX(-20px) scale(0.95)',
      transition: {
        duration: 0.2,
        ease: 'easeIn'
      }
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-system-background via-system-gray-6 to-system-background">
      {/* 添加导航栏 */}
      <Header />
      {/* 页面头部 */}
      <div className="relative overflow-hidden">
        {/* 背景装饰 */}
        <div className="absolute inset-0 bg-gradient-to-r from-system-blue/10 via-system-purple/10 to-system-green/10" />
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-system-blue/5 rounded-full blur-3xl" />
        <div className="absolute top-0 right-1/4 w-96 h-96 bg-system-purple/5 rounded-full blur-3xl" />
        
        <div className="relative z-10 max-w-6xl mx-auto px-4 py-12">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-8"
          >
            <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-system-blue to-system-purple rounded-2xl mb-6 shadow-apple">
              <Icon icon={RewardIcons.star} size="xl" className="text-white" />
            </div>
            <h1 className="text-title-1 font-sf-pro font-bold text-label mb-4">
              奖励中心
            </h1>
            <p className="text-body text-secondary-label max-w-2xl mx-auto">
              通过邀请好友和参与社区活动获得丰厚奖励，查看您在社区中的排名和成就
            </p>
          </motion.div>

          {/* 标签页导航 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="flex justify-center mb-8"
          >
            <div className="inline-flex bg-system-gray-6/50 backdrop-blur-sm rounded-2xl p-2 border border-system-gray-4/30">
              {tabs.map((tab) => (
                <motion.button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className="relative flex items-center space-x-3 px-6 py-4 rounded-xl font-sf-pro font-medium transition-all duration-200 motion-safe hardware-accelerated"
                  variants={tabVariants}
                  animate={activeTab === tab.id ? 'active' : 'inactive'}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Icon 
                    icon={tab.icon} 
                    size="sm" 
                    className={activeTab === tab.id ? 'text-system-blue' : 'text-secondary-label'} 
                  />
                  <div className="text-left">
                    <div className="text-sm font-semibold">{tab.name}</div>
                    <div className="text-xs opacity-70">{tab.description}</div>
                  </div>
                  
                  {/* 活跃指示器 */}
                  {activeTab === tab.id && (
                    <motion.div
                      layoutId="activeTab"
                      className="absolute inset-0 bg-system-blue/10 rounded-xl border border-system-blue/20"
                      transition={{ type: 'spring', bounce: 0.2, duration: 0.6 }}
                    />
                  )}
                </motion.button>
              ))}
            </div>
          </motion.div>
        </div>
      </div>

      {/* 内容区域 */}
      <div className="max-w-6xl mx-auto px-4 pb-12">
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            variants={contentVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            className="relative"
          >
            {/* 内容卡片包装 */}
            <Card className="overflow-hidden border-0 bg-system-background/80 backdrop-blur-sm shadow-apple-lg">
              {activeTab === 'invitation' && (
                <div className="p-0">
                  <InvitationPage />
                </div>
              )}
              {activeTab === 'leaderboard' && (
                <div className="p-0">
                  <LeaderboardPage />
                </div>
              )}
            </Card>
          </motion.div>
        </AnimatePresence>
      </div>

      {/* 底部装饰 */}
      <div className="relative">
        <div className="absolute inset-0 bg-gradient-to-t from-system-gray-6/20 to-transparent" />
        <div className="relative z-10 max-w-6xl mx-auto px-4 py-8">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="text-center space-y-4"
          >
            <div className="inline-flex items-center space-x-2 text-secondary-label">
              <Icon icon={RewardIcons.zap} size="sm" />
              <span className="text-caption-1 font-sf-pro">
                持续参与社区活动，获得更多奖励机会
              </span>
            </div>

            {/* 快捷导航按钮 */}
            <div className="flex justify-center space-x-4 mt-6">
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.location.href = '/'}
                className="bg-system-background/80 backdrop-blur-sm"
              >
                <Icon icon={NavigationIcons.home} size="sm" className="mr-2" />
                返回首页
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.location.href = '/tasks'}
                className="bg-system-background/80 backdrop-blur-sm"
              >
                <Icon icon={RewardIcons.target} size="sm" className="mr-2" />
                查看任务
              </Button>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default RewardsPage;
