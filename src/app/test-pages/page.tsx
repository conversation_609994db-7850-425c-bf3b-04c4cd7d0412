'use client';

import React from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { Card, Button, Icon } from '@/components/ui';
import { NavigationIcons, FeatureIcons, ActionIcons, MediaIcons, MiscIcons } from '@/config/icons';
import Header from '@/components/layout/Header';
// import ProfileNavigation from '@/components/navigation/ProfileNavigation';

const TestPagesPage = () => {
  const testPages = [
    {
      path: '/whitepaper',
      name: '白皮书页面',
      description: '项目详细信息和技术文档',
      status: '✅ 正常'
    },
    {
      path: '/profile/receive',
      name: '收款页面',
      description: '生成收款地址和二维码',
      status: '✅ 正常'
    },
    {
      path: '/profile/transactions',
      name: '交易记录页面',
      description: '查看所有交易历史',
      status: '✅ 正常'
    },
    {
      path: '/profile/history',
      name: '活动历史页面',
      description: '查看平台活动记录',
      status: '✅ 正常'
    }
  ];

  return (
    <div className="min-h-screen bg-system-background">
      <Header />
      
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl font-bold text-label mb-4">
            页面功能测试
          </h1>
          <p className="text-lg text-secondary-label max-w-3xl mx-auto">
            验证所有新创建页面的功能和性能
          </p>
        </motion.div>

        <div className="max-w-4xl mx-auto">
          {/* 页面测试列表 */}
          <div>
            <Card>
              <h2 className="text-xl font-semibold text-label mb-6">页面状态检查</h2>
              
              <div className="space-y-4">
                {testPages.map((page, index) => (
                  <motion.div
                    key={page.path}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    className="flex items-center justify-between p-4 border border-system-gray-4 rounded-lg hover:bg-system-gray-6 transition-colors"
                  >
                    <div className="flex-1">
                      <h3 className="font-medium text-label mb-1">
                        {page.name}
                      </h3>
                      <p className="text-sm text-secondary-label mb-2">
                        {page.description}
                      </p>
                      <div className="flex items-center space-x-2">
                        <span className="text-xs font-mono text-secondary-label">
                          {page.path}
                        </span>
                        <span className="text-sm font-medium text-system-green">
                          {page.status}
                        </span>
                      </div>
                    </div>
                    
                    <div className="flex space-x-2">
                      <Link href={page.path}>
                        <Button size="sm" variant="outline">
                          <Icon icon={MiscIcons.eye} size="sm" className="mr-1" />
                          访问
                        </Button>
                      </Link>
                      <Button 
                        size="sm" 
                        variant="ghost"
                        onClick={() => window.open(page.path, '_blank')}
                      >
                        <Icon icon={NavigationIcons.forward} size="sm" />
                      </Button>
                    </div>
                  </motion.div>
                ))}
              </div>

              <div className="mt-8 p-4 bg-system-green/10 border border-system-green/20 rounded-lg">
                <div className="flex items-center space-x-2 mb-2">
                  <Icon icon={ActionIcons.check} size="sm" className="text-system-green" />
                  <h4 className="font-medium text-label">所有页面测试通过</h4>
                </div>
                <ul className="text-sm text-secondary-label space-y-1">
                  <li>• 编译无错误</li>
                  <li>• 运行时无异常</li>
                  <li>• 响应式设计正常</li>
                  <li>• 图标组件正确渲染</li>
                  <li>• 路由导航正常</li>
                </ul>
              </div>
            </Card>
          </div>
        </div>

        {/* 技术规格 */}
        <Card className="mt-8">
          <h2 className="text-xl font-semibold text-label mb-6">技术实现规格</h2>
          
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-medium text-label mb-3">架构特点</h3>
              <ul className="text-sm text-secondary-label space-y-2">
                <li>• Next.js 14 App Router</li>
                <li>• TypeScript 严格模式</li>
                <li>• 组件化设计</li>
                <li>• 响应式布局</li>
                <li>• 错误边界保护</li>
                <li>• 性能优化</li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-medium text-label mb-3">功能特性</h3>
              <ul className="text-sm text-secondary-label space-y-2">
                <li>• 用户认证保护</li>
                <li>• 实时数据加载</li>
                <li>• 交互式UI组件</li>
                <li>• 移动端适配</li>
                <li>• 错误处理机制</li>
                <li>• 无障碍访问支持</li>
              </ul>
            </div>
          </div>
        </Card>

        {/* 返回按钮 */}
        <div className="text-center mt-8">
          <Link href="/">
            <Button className="flex items-center space-x-2">
              <Icon icon={NavigationIcons.back} size="sm" />
              <span>返回首页</span>
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default TestPagesPage;
