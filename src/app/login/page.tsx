'use client';

import React, { useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, Icon } from '@/components/ui';
import SafeTelegramLogin from '@/components/auth/SafeTelegramLogin';
import EmergencyLogin from '@/components/auth/EmergencyLogin';
import { useTelegramAuth } from '@/hooks/useTelegramAuth';
import { ActionIcons, UserIcons, RewardIcons } from '@/config/icons';

const LoginContent: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user, isAuthenticated, isLoading } = useTelegramAuth();

  const error = searchParams.get('error');
  const redirectTo = searchParams.get('redirect') || '/profile';

  // 如果已经登录，重定向到目标页面
  useEffect(() => {
    if (isAuthenticated && user && !isLoading) {
      router.push(redirectTo);
    }
  }, [isAuthenticated, user, isLoading, router, redirectTo]);

  const handleLoginSuccess = () => {
    // 使用强制跳转避免路由问题
    if (typeof window !== 'undefined') {
      window.location.href = redirectTo;
    }
  };

  const handleLoginError = (errorMessage: string) => {
    console.error('Login error:', errorMessage);
  };

  const getErrorMessage = (errorCode: string) => {
    switch (errorCode) {
      case 'invalid_data':
        return '登录数据无效，请重试';
      case 'auth_failed':
        return '认证失败，请重试';
      case 'server_error':
        return '服务器错误，请稍后重试';
      default:
        return '登录失败，请重试';
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-system-background flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-system-blue border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-body text-secondary-label">检查登录状态...</p>
        </div>
      </div>
    );
  }

  if (isAuthenticated && user) {
    return (
      <div className="min-h-screen bg-system-background flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-system-green border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-body text-secondary-label">登录成功，正在跳转...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-system-background flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <Card className="p-8">
          {/* Logo和标题 */}
          <div className="text-center mb-8">
            <div className="w-16 h-16 bg-gradient-to-br from-system-blue to-system-purple rounded-2xl flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl font-bold text-white">S</span>
            </div>
            <h1 className="text-title-1 font-sf-pro font-bold text-label mb-2">
              欢迎来到 SocioMint
            </h1>
            <p className="text-body text-secondary-label">
              使用Telegram账号登录，开始您的数字资产之旅
            </p>
          </div>

          {/* 错误信息 */}
          {error && (
            <div className="mb-6 p-4 bg-system-red/10 border border-system-red/20 rounded-xl">
              <div className="flex items-center space-x-2">
                <Icon icon={ActionIcons.alert} size="sm" color="error" />
                <span className="text-body text-system-red">
                  {getErrorMessage(error)}
                </span>
              </div>
            </div>
          )}

          {/* Telegram登录组件 - 使用紧急修复版本 */}
          <EmergencyLogin
            onSuccess={handleLoginSuccess}
            redirectTo={redirectTo}
            size="lg"
            variant="primary"
            className="w-full"
          />

          {/* 功能介绍 */}
          <div className="mt-8 space-y-4">
            <h3 className="text-title-3 font-sf-pro font-semibold text-label text-center">
              平台特色
            </h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-system-green/20 rounded-lg flex items-center justify-center">
                  <Icon icon={UserIcons.shield} size="sm" color="success" />
                </div>
                <div>
                  <p className="text-body font-sf-pro font-medium text-label">安全托管</p>
                  <p className="text-caption-1 text-secondary-label">多重签名保护您的数字资产</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-system-blue/20 rounded-lg flex items-center justify-center">
                  <Icon icon={RewardIcons.zap} size="sm" color="primary" />
                </div>
                <div>
                  <p className="text-body font-sf-pro font-medium text-label">快速交易</p>
                  <p className="text-caption-1 text-secondary-label">一键转账，无需复杂操作</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-system-purple/20 rounded-lg flex items-center justify-center">
                  <Icon icon={RewardIcons.gift} size="sm" color="secondary" />
                </div>
                <div>
                  <p className="text-body font-sf-pro font-medium text-label">社交奖励</p>
                  <p className="text-caption-1 text-secondary-label">完成任务获得HAOX奖励</p>
                </div>
              </div>
            </div>
          </div>

          {/* 底部链接 */}
          <div className="mt-8 text-center space-y-2">
            <p className="text-caption-1 text-secondary-label">
              登录即表示您同意我们的
            </p>
            <div className="flex justify-center space-x-4">
              <a 
                href="/terms" 
                className="text-caption-1 text-system-blue hover:underline"
              >
                服务条款
              </a>
              <a 
                href="/privacy" 
                className="text-caption-1 text-system-blue hover:underline"
              >
                隐私政策
              </a>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

const LoginPage: React.FC = () => {
  return (
    <Suspense fallback={<div className="min-h-screen flex items-center justify-center">加载中...</div>}>
      <LoginContent />
    </Suspense>
  );
};

export default LoginPage;
