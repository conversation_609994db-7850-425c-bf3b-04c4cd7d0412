'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function MarketPage() {
  const router = useRouter();

  useEffect(() => {
    // 重定向到交易页面
    router.replace('/trade');
  }, [router]);

  // 显示加载状态，直到重定向完成
  return (
    <div className="min-h-screen bg-system-background flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-system-blue mx-auto mb-4"></div>
        <p className="text-body text-secondary-label">正在跳转到交易页面...</p>
      </div>
    </div>
  );
}
