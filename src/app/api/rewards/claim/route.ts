/**
 * 奖励领取API路由
 * 处理奖励领取请求
 */

import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import jwt from 'jsonwebtoken';
import { rewardService } from '@/services/rewards/RewardService';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

interface JWTPayload {
  userId: number;
  username?: string;
  firstName: string;
  lastName?: string;
  photoUrl?: string;
  iat: number;
  exp: number;
}

interface ClaimRequest {
  rewardIds: string[];
}

export async function POST(request: NextRequest) {
  try {
    // 验证用户认证
    const cookieStore = cookies();
    const token = cookieStore.get('telegram-auth-token')?.value;

    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const decoded = jwt.verify(token, JWT_SECRET) as JWTPayload;
    const userId = decoded.userId;

    // 解析请求数据
    const claimData: ClaimRequest = await request.json();

    // 验证请求数据
    if (!claimData.rewardIds || !Array.isArray(claimData.rewardIds) || claimData.rewardIds.length === 0) {
      return NextResponse.json(
        { error: 'Missing or invalid rewardIds' },
        { status: 400 }
      );
    }

    // 执行奖励领取
    const result = await rewardService.claimRewards(userId, claimData.rewardIds);

    if (!result.success) {
      return NextResponse.json(
        { error: result.error || 'Failed to claim rewards' },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      transactionId: result.transactionId,
      totalAmount: result.totalAmount,
      claimedRewards: result.claimedRewards,
      message: `Successfully claimed ${result.claimedRewards?.length || 0} rewards`,
    });
  } catch (error) {
    console.error('Claim rewards API error:', error);
    
    if (error instanceof jwt.JsonWebTokenError) {
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // 验证用户认证
    const cookieStore = cookies();
    const token = cookieStore.get('telegram-auth-token')?.value;

    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const decoded = jwt.verify(token, JWT_SECRET) as JWTPayload;
    const userId = decoded.userId;

    // 获取用户奖励
    const rewards = await rewardService.getUserRewards(userId);
    const claimHistory = await rewardService.getClaimHistory(userId);

    // 计算待领取奖励
    const pendingRewards = rewards.filter(r => r.status === 'pending');
    const totalPendingAmount = pendingRewards
      .reduce((sum, reward) => sum + parseFloat(reward.amount), 0)
      .toFixed(2);

    return NextResponse.json({
      success: true,
      rewards,
      pendingRewards,
      totalPendingAmount,
      claimHistory,
    });
  } catch (error) {
    console.error('Get rewards API error:', error);
    
    if (error instanceof jwt.JsonWebTokenError) {
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
