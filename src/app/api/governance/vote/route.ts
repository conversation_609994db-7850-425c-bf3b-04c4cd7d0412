import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

// 投票
export async function POST(request: NextRequest) {
  try {
    const {
      proposalId,
      voterId,
      voteChoice,
      reason
    } = await request.json();

    if (!proposalId || !voterId || !voteChoice) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    if (!['for', 'against', 'abstain'].includes(voteChoice)) {
      return NextResponse.json(
        { error: 'Invalid vote choice' },
        { status: 400 }
      );
    }

    // 检查supabase客户端
    if (!supabase) {
      return NextResponse.json(
        { error: 'Database connection not available' },
        { status: 500 }
      );
    }

    // 1. 验证提案是否存在且可投票
    const { data: proposal, error: proposalError } = await supabase
      .from('proposals')
      .select(`
        *,
        proposal_type:proposal_types(*)
      `)
      .eq('id', proposalId)
      .single();

    if (proposalError || !proposal) {
      return NextResponse.json(
        { error: 'Proposal not found' },
        { status: 404 }
      );
    }

    // 2. 检查提案状态
    if (proposal.status !== 'active') {
      return NextResponse.json(
        { error: 'Proposal is not active' },
        { status: 400 }
      );
    }

    // 3. 检查是否已过期
    if (new Date(proposal.end_time) <= new Date()) {
      return NextResponse.json(
        { error: 'Voting period has ended' },
        { status: 400 }
      );
    }

    // 4. 检查用户是否已投票
    const { data: existingVote, error: voteError } = await supabase
      .from('votes')
      .select('id')
      .eq('proposal_id', proposalId)
      .eq('voter_id', voterId)
      .single();

    if (existingVote) {
      return NextResponse.json(
        { error: 'You have already voted on this proposal' },
        { status: 400 }
      );
    }

    // 5. 获取用户的投票权重
    const { getUserVotingPowerForProposal } = await import('@/lib/voting-power');
    const votingPower = await getUserVotingPowerForProposal(voterId, proposalId);

    // 6. 检查最低投票权重要求
    if (votingPower < proposal.proposal_type.min_voting_power) {
      return NextResponse.json(
        { 
          error: `Minimum voting power required: ${proposal.proposal_type.min_voting_power} HAOX`,
          required_power: proposal.proposal_type.min_voting_power,
          current_power: votingPower
        },
        { status: 400 }
      );
    }

    // 7. 创建投票记录
    const { data: vote, error: createError } = await supabase
      .from('votes')
      .insert({
        proposal_id: proposalId,
        voter_id: voterId,
        vote_choice: voteChoice,
        voting_power: votingPower,
        reason
      })
      .select()
      .single();

    if (createError) {
      console.error('Error creating vote:', createError);
      return NextResponse.json(
        { error: 'Failed to cast vote' },
        { status: 500 }
      );
    }

    // 8. 发放投票奖励(异步处理)
    try {
      await supabase
        .from('governance_rewards')
        .insert({
          user_id: voterId,
          reward_type: 'voting_participation',
          proposal_id: proposalId,
          reward_amount: 10.0, // 基础投票奖励
          reason: 'Participation in governance voting'
        });
    } catch (rewardError) {
      console.error('Error creating voting reward:', rewardError);
      // 不阻止投票，只记录错误
    }

    return NextResponse.json({
      success: true,
      vote: {
        id: vote.id,
        vote_choice: vote.vote_choice,
        voting_power: vote.voting_power,
        voted_at: vote.voted_at
      },
      message: 'Vote cast successfully'
    });

  } catch (error) {
    console.error('Vote error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// 获取用户投票记录
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const proposalId = searchParams.get('proposalId');

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    let query = supabase
      .from('votes')
      .select(`
        *,
        proposal:proposals(id, title, status, end_time)
      `)
      .eq('voter_id', userId)
      .order('voted_at', { ascending: false });

    if (proposalId) {
      query = query.eq('proposal_id', proposalId);
    }

    const { data: votes, error } = await query;

    if (error) {
      throw error;
    }

    // 获取用户的治理统计
    const { data: stats, error: statsError } = await supabase
      .from('governance_stats')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (statsError && statsError.code !== 'PGRST116') {
      console.error('Error fetching governance stats:', statsError);
    }

    return NextResponse.json({
      success: true,
      data: {
        votes: votes || [],
        stats: stats || {
          proposals_created: 0,
          votes_cast: 0,
          voting_power_used: 0,
          total_rewards: 0,
          participation_rate: 0
        }
      }
    });

  } catch (error) {
    console.error('Get votes error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
