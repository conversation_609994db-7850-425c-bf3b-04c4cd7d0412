import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

// 获取提案列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status') || 'all';
    const proposalType = searchParams.get('type');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const sortBy = searchParams.get('sortBy') || 'created_at';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    const offset = (page - 1) * limit;

    // 检查supabase客户端
    if (!supabase) {
      return NextResponse.json(
        { error: 'Database connection not available' },
        { status: 500 }
      );
    }

    // 构建查询
    let query = supabase
      .from('proposals')
      .select(`
        *,
        proposal_type:proposal_types(*),
        proposer:users!proposals_proposer_id_fkey(id, username),
        vote_count:votes(count)
      `, { count: 'exact' });

    // 添加过滤条件
    if (status !== 'all') {
      query = query.eq('status', status);
    }

    if (proposalType) {
      query = query.eq('proposal_type_id', proposalType);
    }

    // 排序
    const validSortFields = ['created_at', 'end_time', 'total_votes', 'voter_count'];
    const sortField = validSortFields.includes(sortBy) ? sortBy : 'created_at';
    query = query.order(sortField, { ascending: sortOrder === 'asc' });

    // 分页
    query = query.range(offset, offset + limit - 1);

    const { data: proposals, error, count } = await query;

    if (error) {
      throw error;
    }

    // 处理提案数据
    const processedProposals = (proposals || []).map(proposal => {
      const totalVotes = proposal.votes_for + proposal.votes_against + proposal.votes_abstain;
      const approvalRate = totalVotes > 0 ? (proposal.votes_for / totalVotes) * 100 : 0;
      const isActive = proposal.status === 'active' && new Date(proposal.end_time) > new Date();
      const timeRemaining = isActive ? new Date(proposal.end_time).getTime() - new Date().getTime() : 0;

      return {
        id: proposal.id,
        title: proposal.title,
        description: proposal.description,
        status: proposal.status,
        start_time: proposal.start_time,
        end_time: proposal.end_time,
        execution_time: proposal.execution_time,
        votes_for: proposal.votes_for,
        votes_against: proposal.votes_against,
        votes_abstain: proposal.votes_abstain,
        total_votes: totalVotes,
        voter_count: proposal.voter_count,
        approval_rate: approvalRate,
        stake_amount: proposal.stake_amount,
        tags: proposal.tags,
        created_at: proposal.created_at,
        proposal_type: proposal.proposal_type,
        proposer: proposal.proposer,
        vote_count: proposal.vote_count?.[0]?.count || 0,
        is_active: isActive,
        time_remaining: timeRemaining,
        can_vote: isActive
      };
    });

    return NextResponse.json({
      success: true,
      data: {
        proposals: processedProposals,
        pagination: {
          page,
          limit,
          total: count || 0,
          total_pages: Math.ceil((count || 0) / limit),
          has_next: offset + limit < (count || 0),
          has_prev: page > 1
        }
      }
    });

  } catch (error) {
    console.error('Get proposals error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// 创建新提案
export async function POST(request: NextRequest) {
  try {
    const {
      proposerId,
      proposalTypeId,
      title,
      description,
      content,
      executionData,
      stakeAmount,
      discussionUrl,
      tags
    } = await request.json();

    if (!proposerId || !proposalTypeId || !title || !description || !stakeAmount) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // 检查supabase客户端
    if (!supabase) {
      return NextResponse.json(
        { error: 'Database connection not available' },
        { status: 500 }
      );
    }

    // 1. 验证提案者是否存在
    const { data: proposer, error: proposerError } = await supabase
      .from('users')
      .select('id')
      .eq('id', proposerId)
      .single();

    if (proposerError || !proposer) {
      return NextResponse.json(
        { error: 'Proposer not found' },
        { status: 404 }
      );
    }

    // 2. 验证提案类型是否存在
    const { data: proposalType, error: proposalTypeError } = await supabase
      .from('proposal_types')
      .select('*')
      .eq('id', proposalTypeId)
      .eq('is_active', true)
      .single();

    if (proposalTypeError || !proposalType) {
      return NextResponse.json(
        { error: 'Invalid proposal type' },
        { status: 400 }
      );
    }

    // 3. 验证质押金额是否满足要求
    if (stakeAmount < proposalType.min_proposal_stake) {
      return NextResponse.json(
        { 
          error: `Minimum stake amount is ${proposalType.min_proposal_stake} HAOX`,
          min_stake: proposalType.min_proposal_stake
        },
        { status: 400 }
      );
    }

    // 4. 计算投票结束时间
    const endTime = new Date();
    endTime.setDate(endTime.getDate() + proposalType.voting_duration);

    // 5. 创建提案
    const { data: proposal, error: createError } = await supabase
      .from('proposals')
      .insert({
        proposer_id: proposerId,
        proposal_type_id: proposalTypeId,
        title,
        description,
        content,
        execution_data: executionData,
        stake_amount: stakeAmount,
        end_time: endTime.toISOString(),
        discussion_url: discussionUrl,
        tags: tags || [],
        status: 'active'
      })
      .select(`
        *,
        proposal_type:proposal_types(*),
        proposer:users!proposals_proposer_id_fkey(id, username)
      `)
      .single();

    if (createError) {
      console.error('Error creating proposal:', createError);
      return NextResponse.json(
        { error: 'Failed to create proposal' },
        { status: 500 }
      );
    }

    // 6. 创建投票权重快照（异步处理）
    const { createVotingPowerSnapshot } = await import('@/lib/voting-power');

    // 异步创建快照，不阻塞响应
    setImmediate(async () => {
      try {
        // 获取当前区块高度（简化实现，实际应该从区块链获取）
        const currentBlockHeight = Math.floor(Date.now() / 1000); // 使用时间戳作为简化的区块高度
        await createVotingPowerSnapshot(proposal.id, currentBlockHeight);
      } catch (error) {
        console.error('Failed to create voting power snapshot:', error);
      }
    });

    return NextResponse.json({
      success: true,
      proposal: {
        id: proposal.id,
        title: proposal.title,
        description: proposal.description,
        status: proposal.status,
        end_time: proposal.end_time,
        stake_amount: proposal.stake_amount,
        proposal_type: proposal.proposal_type,
        proposer: proposal.proposer,
        created_at: proposal.created_at
      },
      message: 'Proposal created successfully'
    });

  } catch (error) {
    console.error('Create proposal error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
