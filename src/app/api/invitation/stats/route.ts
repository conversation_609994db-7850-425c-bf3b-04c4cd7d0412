import { NextRequest, NextResponse } from 'next/server';
import { supabase, isDevelopmentMode } from '@/lib/supabase';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    // 开发环境返回模拟数据
    if (isDevelopmentMode || !supabase) {
      return NextResponse.json({
        success: true,
        data: {
          invitation_code: 'DEV123',
          invitation_link: `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3001'}?invite=DEV123`,
          stats: {
            total_invitations: 5,
            successful_invitations: 3,
            presale_conversions: 1,
            total_rewards: 150,
            pending_rewards: 50,
          },
          rewards: [
            {
              id: '1',
              amount: 50,
              type: 'invitation',
              status: 'completed',
              created_at: new Date().toISOString(),
            },
            {
              id: '2',
              amount: 100,
              type: 'milestone',
              status: 'completed',
              created_at: new Date().toISOString(),
            },
          ],
          invitation_history: [
            {
              id: '1',
              invited_user_id: 'user1',
              status: 'completed',
              created_at: new Date().toISOString(),
            },
          ],
          next_milestone: {
            target: 5,
            current: 3,
            reward: 300,
          },
        },
      });
    }

    // 1. 获取用户的邀请统计
    let invitationStats = null;
    try {
      const { data: stats, error: statsError } = await supabase
        .from('invitation_stats')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (statsError && statsError.code !== 'PGRST116') {
        throw statsError;
      }

      invitationStats = stats;
    } catch (error) {
      console.log('Error fetching invitation stats:', error);
    }

    // 2. 如果没有统计数据，从邀请表计算
    if (!invitationStats) {
      try {
        const { data: invitations, error: invitationsError } = await supabase
          .from('invitations')
          .select('*')
          .eq('inviter_id', userId);

        if (invitationsError) {
          throw invitationsError;
        }

        const totalInvitations = invitations?.length || 0;
        const successfulInvitations = invitations?.filter(inv => inv.status === 'accepted').length || 0;

        invitationStats = {
          user_id: userId,
          total_invitations: totalInvitations,
          successful_invitations: successfulInvitations,
          presale_conversions: 0, // 需要额外查询
          total_rewards: 0,
          last_invitation_at: invitations?.[0]?.created_at || null
        };
      } catch (error) {
        console.log('Error calculating stats from invitations:', error);
        // 返回默认统计
        invitationStats = {
          user_id: userId,
          total_invitations: 0,
          successful_invitations: 0,
          presale_conversions: 0,
          total_rewards: 0,
          last_invitation_at: null
        };
      }
    }

    // 3. 获取用户的邀请码
    let invitationCode = null;
    try {
      const { data: codeData, error: codeError } = await supabase
        .from('invitation_codes')
        .select('code')
        .eq('user_id', userId)
        .eq('is_active', true)
        .single();

      if (!codeError) {
        invitationCode = codeData.code;
      }
    } catch (error) {
      console.log('Error fetching invitation code:', error);
    }

    // 4. 获取奖励记录
    let rewards = [];
    try {
      const { data: rewardData, error: rewardError } = await supabase
        .from('user_invitation_rewards')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (!rewardError) {
        rewards = rewardData || [];
      }
    } catch (error) {
      console.log('Error fetching rewards:', error);
    }

    // 5. 获取邀请记录
    let invitationHistory = [];
    try {
      const { data: historyData, error: historyError } = await supabase
        .from('invitations')
        .select(`
          *,
          invitee:users!invitations_invitee_id_fkey(id, username, email)
        `)
        .eq('inviter_id', userId)
        .order('created_at', { ascending: false });

      if (!historyError) {
        invitationHistory = historyData || [];
      }
    } catch (error) {
      console.log('Error fetching invitation history:', error);
    }

    // 6. 计算下一个里程碑
    const milestones = [5, 10, 25, 50, 100];
    const currentCount = invitationStats.successful_invitations;
    const nextMilestone = milestones.find(m => m > currentCount);

    return NextResponse.json({
      success: true,
      data: {
        invitation_code: invitationCode,
        invitation_link: invitationCode ? 
          `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3001'}?invite=${invitationCode}` : 
          null,
        stats: {
          total_invitations: invitationStats.total_invitations,
          successful_invitations: invitationStats.successful_invitations,
          presale_conversions: invitationStats.presale_conversions,
          total_rewards: invitationStats.total_rewards,
          last_invitation_at: invitationStats.last_invitation_at
        },
        milestones: {
          current: currentCount,
          next: nextMilestone,
          progress: nextMilestone ? (currentCount / nextMilestone) * 100 : 100
        },
        rewards: rewards.map(reward => ({
          id: reward.id,
          reward_type: reward.reward_type,
          reward_amount: reward.reward_amount,
          reward_description: reward.reward_description,
          status: reward.status,
          created_at: reward.created_at,
          distributed_at: reward.distributed_at
        })),
        invitation_history: invitationHistory.map(invitation => ({
          id: invitation.id,
          invitee_id: invitation.invitee_id,
          invitee_username: invitation.invitee?.username || 'Unknown',
          status: invitation.status,
          created_at: invitation.created_at,
          accepted_at: invitation.accepted_at
        }))
      }
    });

  } catch (error) {
    console.error('Get invitation stats error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// 获取邀请排行榜
export async function POST(request: NextRequest) {
  try {
    const { period = 'all', limit = 50 } = await request.json();

    // 开发环境返回模拟数据
    if (isDevelopmentMode || !supabase) {
      const mockLeaderboard = Array.from({ length: Math.min(limit, 10) }, (_, i) => ({
        id: `user${i + 1}`,
        user_id: `user${i + 1}`,
        username: `用户${i + 1}`,
        successful_invitations: Math.floor(Math.random() * 50) + 10,
        total_rewards: Math.floor(Math.random() * 1000) + 100,
        rank: i + 1,
        avatar_url: null,
        last_invitation_at: new Date().toISOString(),
      }));

      return NextResponse.json({
        success: true,
        data: {
          leaderboard: mockLeaderboard,
          period,
          total_count: mockLeaderboard.length,
        },
      });
    }

    // 1. 获取排行榜数据 - 移除外键关系查询
    let query = supabase
      .from('invitation_stats')
      .select('*')
      .order('successful_invitations', { ascending: false })
      .limit(limit);

    // 根据时间段过滤
    if (period === 'week') {
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);
      query = query.gte('last_invitation_at', weekAgo.toISOString());
    } else if (period === 'month') {
      const monthAgo = new Date();
      monthAgo.setMonth(monthAgo.getMonth() - 1);
      query = query.gte('last_invitation_at', monthAgo.toISOString());
    }

    const { data: leaderboard, error } = await query;

    if (error) {
      console.error('Get leaderboard error:', error);
      // 如果查询失败，返回空排行榜
      return NextResponse.json({
        success: true,
        data: {
          leaderboard: [],
          global_stats: {
            total_users: 0,
            total_invitations: 0,
            total_rewards: 0
          }
        }
      });
    }

    // 2. 格式化排行榜数据 - 手动获取用户信息
    const formattedLeaderboard = [];

    if (leaderboard && leaderboard.length > 0) {
      for (let index = 0; index < leaderboard.length; index++) {
        const entry = leaderboard[index];

        // 尝试获取用户信息
        let username = 'Anonymous';
        try {
          const { data: userData } = await supabase
            .from('users')
            .select('username')
            .eq('id', entry.user_id)
            .single();

          if (userData?.username) {
            username = userData.username;
          }
        } catch (userError) {
          console.warn('Failed to get user info for:', entry.user_id);
        }

        formattedLeaderboard.push({
          rank: index + 1,
          user_id: entry.user_id,
          username,
          successful_invitations: entry.successful_invitations,
          total_rewards: entry.total_rewards,
          last_invitation_at: entry.last_invitation_at
        });
      }
    }

    // 3. 获取总体统计
    const { data: totalStats, error: totalError } = await supabase
      .from('invitation_stats')
      .select('successful_invitations, total_rewards');

    let globalStats = {
      total_users: 0,
      total_invitations: 0,
      total_rewards: 0
    };

    if (!totalError && totalStats) {
      globalStats = {
        total_users: totalStats.length,
        total_invitations: totalStats.reduce((sum, stat) => sum + stat.successful_invitations, 0),
        total_rewards: totalStats.reduce((sum, stat) => sum + parseFloat(stat.total_rewards || '0'), 0)
      };
    }

    return NextResponse.json({
      success: true,
      data: {
        leaderboard: formattedLeaderboard,
        global_stats: globalStats,
        period,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Get leaderboard error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
