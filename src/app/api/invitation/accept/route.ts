import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

export async function POST(request: NextRequest) {
  try {
    const { invitationCode, inviteeId } = await request.json();

    if (!invitationCode || !inviteeId) {
      return NextResponse.json(
        { error: 'Invitation code and invitee ID are required' },
        { status: 400 }
      );
    }

    // 1. 验证邀请码是否存在且有效
    const { data: codeData, error: codeError } = await supabase
      .from('invitation_codes')
      .select('*')
      .eq('code', invitationCode)
      .eq('is_active', true)
      .single();

    if (codeError || !codeData) {
      return NextResponse.json(
        { error: 'Invalid or expired invitation code' },
        { status: 400 }
      );
    }

    // 2. 检查邀请码是否过期
    if (new Date(codeData.expires_at) < new Date()) {
      return NextResponse.json(
        { error: 'Invitation code has expired' },
        { status: 400 }
      );
    }

    // 3. 检查用户是否已经被邀请过
    const { data: existingInvitation, error: existingError } = await supabase
      .from('invitations')
      .select('*')
      .eq('invitee_id', inviteeId)
      .single();

    if (existingInvitation) {
      return NextResponse.json(
        { error: 'User has already been invited' },
        { status: 400 }
      );
    }

    // 4. 检查用户是否试图邀请自己
    if (codeData.user_id === inviteeId) {
      return NextResponse.json(
        { error: 'Cannot invite yourself' },
        { status: 400 }
      );
    }

    // 5. 创建邀请关系
    const { data: invitationData, error: invitationError } = await supabase
      .from('invitations')
      .insert({
        inviter_id: codeData.user_id,
        invitee_id: inviteeId,
        invitation_code_id: codeData.id,
        invitation_code: invitationCode,
        status: 'accepted',
        accepted_at: new Date().toISOString()
      })
      .select()
      .single();

    if (invitationError) {
      console.error('Error creating invitation:', invitationError);
      return NextResponse.json(
        { error: 'Failed to create invitation relationship' },
        { status: 500 }
      );
    }

    // 6. 更新用户表的邀请信息（如果字段存在）
    try {
      const { error: updateError } = await supabase
        .from('users')
        .update({
          invited_by: codeData.user_id,
          invitation_accepted_at: new Date().toISOString()
        })
        .eq('id', inviteeId);

      if (updateError) {
        console.log('Could not update user invitation info:', updateError.message);
      }
    } catch (error) {
      console.log('Users table may not have invitation fields');
    }

    // 7. 计算并分发邀请奖励
    try {
      await calculateAndDistributeRewards(codeData.user_id, invitationData.id);
    } catch (error) {
      console.error('Error calculating rewards:', error);
      // 不阻止邀请流程，只记录错误
    }

    return NextResponse.json({
      success: true,
      invitation: {
        id: invitationData.id,
        inviter_id: codeData.user_id,
        invitee_id: inviteeId,
        accepted_at: invitationData.accepted_at
      },
      message: 'Invitation accepted successfully'
    });

  } catch (error) {
    console.error('Accept invitation error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// 计算并分发邀请奖励
async function calculateAndDistributeRewards(inviterId: string, invitationId: string) {
  try {
    // 1. 获取邀请者的统计信息
    const { data: stats, error: statsError } = await supabase
      .from('invitation_stats')
      .select('*')
      .eq('user_id', inviterId)
      .single();

    if (statsError && statsError.code !== 'PGRST116') {
      throw statsError;
    }

    const currentInvitations = stats?.successful_invitations || 0;
    const newInvitationCount = currentInvitations + 1;

    // 2. 获取奖励配置（模拟数据，因为表可能不存在）
    const rewardConfigs = [
      {
        id: '1',
        reward_type: 'per_invite',
        condition_type: 'invite_count',
        condition_value: 1,
        reward_amount: 50.0,
        reward_description: '每邀请1人奖励50 HAOX'
      },
      {
        id: '2',
        reward_type: 'milestone',
        condition_type: 'invite_count',
        condition_value: 5,
        reward_amount: 300.0,
        reward_description: '邀请满5人额外奖励300 HAOX'
      },
      {
        id: '3',
        reward_type: 'milestone',
        condition_type: 'invite_count',
        condition_value: 10,
        reward_amount: 800.0,
        reward_description: '邀请满10人额外奖励800 HAOX'
      }
    ];

    // 3. 计算应该获得的奖励
    const rewardsToDistribute = [];

    // 基础邀请奖励
    const perInviteReward = rewardConfigs.find(r => r.reward_type === 'per_invite');
    if (perInviteReward) {
      rewardsToDistribute.push({
        user_id: inviterId,
        invitation_reward_id: perInviteReward.id,
        reward_type: perInviteReward.reward_type,
        reward_amount: perInviteReward.reward_amount,
        reward_description: perInviteReward.reward_description,
        related_invitation_id: invitationId,
        status: 'pending'
      });
    }

    // 里程碑奖励
    const milestoneRewards = rewardConfigs.filter(r => 
      r.reward_type === 'milestone' && 
      r.condition_value === newInvitationCount
    );

    for (const milestone of milestoneRewards) {
      rewardsToDistribute.push({
        user_id: inviterId,
        invitation_reward_id: milestone.id,
        reward_type: milestone.reward_type,
        reward_amount: milestone.reward_amount,
        reward_description: milestone.reward_description,
        related_invitation_id: invitationId,
        status: 'pending'
      });
    }

    // 4. 保存奖励记录
    if (rewardsToDistribute.length > 0) {
      const { error: rewardError } = await supabase
        .from('user_invitation_rewards')
        .insert(rewardsToDistribute);

      if (rewardError) {
        console.error('Error saving rewards:', rewardError);
      }
    }

    console.log(`Calculated ${rewardsToDistribute.length} rewards for user ${inviterId}`);

  } catch (error) {
    console.error('Error in calculateAndDistributeRewards:', error);
    throw error;
  }
}
