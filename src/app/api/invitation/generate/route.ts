import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

// 生成唯一邀请码
async function generateUniqueInvitationCode(): Promise<string> {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let attempts = 0;
  const maxAttempts = 10;

  while (attempts < maxAttempts) {
    let code = '';
    for (let i = 0; i < 8; i++) {
      code += chars.charAt(Math.floor(Math.random() * chars.length));
    }

    // 检查是否已存在
    try {
      const { data, error } = await supabase
        .from('invitation_codes')
        .select('id')
        .eq('code', code)
        .single();

      if (error && error.code === 'PGRST116') {
        // 没有找到，说明代码唯一
        return code;
      }
    } catch (error) {
      // 如果表不存在，直接返回生成的代码
      return code;
    }

    attempts++;
  }

  throw new Error('Failed to generate unique invitation code after multiple attempts');
}

export async function POST(request: NextRequest) {
  try {
    const { userId } = await request.json();

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    // 1. 检查用户是否已经有邀请码
    try {
      const { data: existingUser, error: userError } = await supabase
        .from('users')
        .select('id, invitation_code')
        .eq('id', userId)
        .single();

      if (userError) {
        return NextResponse.json(
          { error: 'User not found' },
          { status: 404 }
        );
      }

      // 如果用户已经有邀请码，直接返回
      if (existingUser.invitation_code) {
        return NextResponse.json({
          success: true,
          invitation_code: existingUser.invitation_code,
          message: 'Invitation code already exists'
        });
      }
    } catch (error) {
      // 如果用户表没有 invitation_code 字段，我们继续处理
      console.log('Users table may not have invitation_code field yet');
    }

    // 2. 生成新的邀请码
    const invitationCode = await generateUniqueInvitationCode();

    // 3. 保存到 invitation_codes 表
    try {
      const { data: codeData, error: codeError } = await supabase
        .from('invitation_codes')
        .insert({
          user_id: userId,
          code: invitationCode,
          is_active: true
        })
        .select()
        .single();

      if (codeError) {
        throw codeError;
      }

      // 4. 更新用户表（如果字段存在）
      try {
        const { error: updateError } = await supabase
          .from('users')
          .update({ invitation_code: invitationCode })
          .eq('id', userId);

        if (updateError) {
          console.log('Could not update user invitation_code:', updateError.message);
        }
      } catch (error) {
        console.log('Users table may not have invitation_code field');
      }

      return NextResponse.json({
        success: true,
        invitation_code: invitationCode,
        invitation_link: `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3001'}?invite=${invitationCode}`,
        message: 'Invitation code generated successfully'
      });

    } catch (error) {
      console.error('Error saving invitation code:', error);
      return NextResponse.json(
        { error: 'Failed to save invitation code' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Generate invitation code error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    // 获取用户的邀请码
    try {
      const { data: codeData, error } = await supabase
        .from('invitation_codes')
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true)
        .single();

      if (error && error.code === 'PGRST116') {
        return NextResponse.json({
          success: true,
          invitation_code: null,
          message: 'No invitation code found'
        });
      }

      if (error) {
        throw error;
      }

      return NextResponse.json({
        success: true,
        invitation_code: codeData.code,
        invitation_link: `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3001'}?invite=${codeData.code}`,
        created_at: codeData.created_at,
        expires_at: codeData.expires_at
      });

    } catch (error) {
      console.error('Error fetching invitation code:', error);
      return NextResponse.json(
        { error: 'Failed to fetch invitation code' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Get invitation code error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
