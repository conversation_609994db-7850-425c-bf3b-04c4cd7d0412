import { NextRequest, NextResponse } from 'next/server';
import { supabase, isDevelopmentMode } from '@/lib/supabase';
import { createApiError, handleApiError, createSuccessResponse } from '@/lib/api-error-handler';
import { ApiErrorCode } from '@/lib/api-error-handler';

// 虚拟用户配置（用于激励真实用户）
const VIRTUAL_USERS = [
  { id: 'virtual_1', username: 'CryptoMaster88', invitations: 47, avatar: '🚀' },
  { id: 'virtual_2', username: 'BlockchainPro', invitations: 43, avatar: '💎' },
  { id: 'virtual_3', username: '<PERSON><PERSON><PERSON>Explorer', invitations: 39, avatar: '🌟' },
  { id: 'virtual_4', username: '<PERSON><PERSON><PERSON>unter', invitations: 35, avatar: '🎯' },
  { id: 'virtual_5', username: '<PERSON>3<PERSON><PERSON>er', invitations: 32, avatar: '🔥' },
  { id: 'virtual_6', username: '<PERSON>pt<PERSON><PERSON><PERSON>', invitations: 28, avatar: '🐋' },
  { id: 'virtual_7', username: '<PERSON><PERSON>rader', invitations: 25, avatar: '📈' },
  { id: 'virtual_8', username: 'NFTCollector', invitations: 23, avatar: '🎨' },
  { id: 'virtual_9', username: 'DeFiYielder', invitations: 21, avatar: '💰' },
  { id: 'virtual_10', username: 'MetaBuilder', invitations: 20, avatar: '🏗️' },
];

// 排行榜奖励配置
const LEADERBOARD_REWARDS = [
  1000000, // 第1名: 1,000,000 HAOX
  500000,  // 第2名: 500,000 HAOX
  200000,  // 第3名: 200,000 HAOX
  100000, 100000, 100000, 100000, 100000, 100000, 100000, // 第4-10名: 100,000 HAOX
  50000, 50000, 50000, 50000, 50000, 50000, 50000, 50000, 50000, 50000 // 第11-20名: 50,000 HAOX
];

/**
 * @api {GET} /api/invitation/leaderboard 获取邀请排行榜
 * @apiDescription 获取邀请排行榜数据，包含虚拟用户和真实用户
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '50');
    const userId = searchParams.get('userId'); // 可选，用于获取用户排名

    // 开发环境返回模拟数据
    if (isDevelopmentMode || !supabase) {
      const mockLeaderboard = [
        ...VIRTUAL_USERS.slice(0, 5),
        {
          id: 'real_user_1',
          username: 'YourUsername',
          invitations: 15,
          avatar: '👤',
          is_current_user: userId === 'real_user_1',
          reward: LEADERBOARD_REWARDS[15] || 0
        },
        ...VIRTUAL_USERS.slice(5),
        {
          id: 'real_user_2',
          username: 'AnotherUser',
          invitations: 12,
          avatar: '👤',
          is_current_user: false,
          reward: 0
        }
      ].sort((a, b) => b.invitations - a.invitations)
       .slice(0, limit)
       .map((user, index) => ({
         ...user,
         rank: index + 1,
         reward: index < 20 ? LEADERBOARD_REWARDS[index] : 0
       }));

      return createSuccessResponse({
        leaderboard: mockLeaderboard,
        total_participants: mockLeaderboard.length,
        user_rank: userId ? mockLeaderboard.findIndex(u => u.id === userId) + 1 : null,
        rewards_info: {
          total_pool: 2700000, // 2.7M HAOX
          top_20_rewards: LEADERBOARD_REWARDS,
          presale_ended: false,
          rewards_claimable: false
        }
      });
    }

    // 获取真实用户数据
    const { data: realUsers, error: usersError } = await supabase
      .from('invitation_stats')
      .select(`
        user_id,
        successful_invitations,
        users!inner(username, avatar_url)
      `)
      .gt('successful_invitations', 0)
      .order('successful_invitations', { ascending: false })
      .limit(limit);

    if (usersError) {
      throw createApiError(
        ApiErrorCode.DATABASE_ERROR,
        'Failed to fetch leaderboard data',
        500,
        { details: usersError }
      );
    }

    // 合并真实用户和虚拟用户
    const allUsers = [
      ...VIRTUAL_USERS.map(user => ({
        id: user.id,
        username: user.username,
        invitations: user.invitations,
        avatar: user.avatar,
        is_virtual: true,
        is_current_user: false
      })),
      ...(realUsers || []).map(user => ({
        id: user.user_id,
        username: user.users?.username || 'Anonymous',
        invitations: user.successful_invitations,
        avatar: user.users?.avatar_url || '👤',
        is_virtual: false,
        is_current_user: user.user_id === userId
      }))
    ];

    // 按邀请数量排序
    const sortedUsers = allUsers
      .sort((a, b) => b.invitations - a.invitations)
      .slice(0, limit)
      .map((user, index) => ({
        ...user,
        rank: index + 1,
        reward: index < 20 ? LEADERBOARD_REWARDS[index] : 0
      }));

    // 获取当前用户排名
    let userRank = null;
    if (userId) {
      const userIndex = sortedUsers.findIndex(user => user.id === userId);
      userRank = userIndex >= 0 ? userIndex + 1 : null;
    }

    // 检查预售是否结束
    const { data: presaleStatus } = await supabase
      .from('system_settings')
      .select('value')
      .eq('key', 'presale_ended')
      .single();

    const presaleEnded = presaleStatus?.value === 'true';

    return createSuccessResponse({
      leaderboard: sortedUsers,
      total_participants: allUsers.length,
      user_rank: userRank,
      rewards_info: {
        total_pool: 2700000, // 2.7M HAOX
        top_20_rewards: LEADERBOARD_REWARDS,
        presale_ended: presaleEnded,
        rewards_claimable: presaleEnded
      }
    });

  } catch (error) {
    return handleApiError(error);
  }
}

/**
 * @api {POST} /api/invitation/leaderboard 更新排行榜（管理员）
 * @apiDescription 预售结束后，管理员确定最终排行榜并分配奖励
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, adminKey } = body;

    // 验证管理员权限
    if (adminKey !== process.env.ADMIN_SECRET_KEY) {
      throw createApiError(
        ApiErrorCode.UNAUTHORIZED,
        'Invalid admin credentials',
        401
      );
    }

    if (action === 'finalize_leaderboard') {
      // 开发环境返回模拟响应
      if (isDevelopmentMode || !supabase) {
        return createSuccessResponse({
          success: true,
          message: 'Leaderboard finalized successfully',
          rewards_distributed: 2700000,
          top_20_users: VIRTUAL_USERS.slice(0, 20).map((user, index) => ({
            ...user,
            rank: index + 1,
            reward: LEADERBOARD_REWARDS[index]
          }))
        });
      }

      // 获取最终排行榜
      const { data: finalUsers, error: usersError } = await supabase
        .from('invitation_stats')
        .select('user_id, successful_invitations')
        .gt('successful_invitations', 0)
        .order('successful_invitations', { ascending: false })
        .limit(20);

      if (usersError) {
        throw createApiError(
          ApiErrorCode.DATABASE_ERROR,
          'Failed to fetch final leaderboard',
          500,
          { details: usersError }
        );
      }

      // 分配排行榜奖励
      const updates = [];
      for (let i = 0; i < Math.min(finalUsers.length, 20); i++) {
        const user = finalUsers[i];
        const reward = LEADERBOARD_REWARDS[i];
        
        updates.push({
          user_id: user.user_id,
          leaderboard_reward: reward,
          leaderboard_position: i + 1,
          claimable_rewards: reward // 添加到可领取奖励中
        });
      }

      // 批量更新用户奖励
      if (updates.length > 0) {
        const { error: updateError } = await supabase
          .from('invitation_stats')
          .upsert(updates, { onConflict: 'user_id' });

        if (updateError) {
          throw createApiError(
            ApiErrorCode.DATABASE_ERROR,
            'Failed to update leaderboard rewards',
            500,
            { details: updateError }
          );
        }
      }

      // 标记预售结束和排行榜确定
      await supabase
        .from('system_settings')
        .upsert([
          { key: 'presale_ended', value: 'true' },
          { key: 'leaderboard_finalized', value: 'true' }
        ], { onConflict: 'key' });

      return createSuccessResponse({
        success: true,
        message: 'Leaderboard finalized successfully',
        rewards_distributed: updates.reduce((sum, u) => sum + u.leaderboard_reward, 0),
        top_20_users: updates.map((user, index) => ({
          user_id: user.user_id,
          rank: index + 1,
          reward: user.leaderboard_reward
        }))
      });
    }

    throw createApiError(
      ApiErrorCode.VALIDATION_ERROR,
      'Invalid action',
      400
    );

  } catch (error) {
    return handleApiError(error);
  }
}

// 配置运行时
export const runtime = 'nodejs';
