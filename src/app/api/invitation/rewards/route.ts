import { NextRequest, NextResponse } from 'next/server';
import { supabase, isDevelopmentMode } from '@/lib/supabase';
import { createApiError, handleApiError, createSuccessResponse } from '@/lib/api-error-handler';
import { ApiErrorCode } from '@/lib/api-error-handler';

// 更新的邀请奖励配置
const INVITATION_REWARDS = {
  BASE_REWARD: 1000, // 每邀请一人的基础奖励（1,000 HAOX）
  MILESTONE_5: 10000, // 邀请满5人的里程碑奖励（10,000 HAOX）
  MILESTONE_10: 50000, // 邀请满10人的里程碑奖励（50,000 HAOX）
  MIN_PURCHASE_AMOUNT: 0.1, // 最低购买金额（BNB）
  TOTAL_REWARD_POOL: 300000000, // 总奖励池（300M HAOX）
} as const;

// 排行榜奖励配置
const LEADERBOARD_REWARDS = [
  1000000, // 第1名
  500000,  // 第2名
  200000,  // 第3名
  100000, 100000, 100000, 100000, 100000, 100000, 100000, // 第4-10名
  50000, 50000, 50000, 50000, 50000, 50000, 50000, 50000, 50000, 50000 // 第11-20名
];

/**
 * @api {GET} /api/invitation/rewards 获取用户邀请奖励信息
 * @apiDescription 获取用户的邀请奖励统计、可领取奖励和排行榜信息
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      throw createApiError(
        ApiErrorCode.VALIDATION_ERROR,
        'User ID is required',
        400
      );
    }

    // 开发环境返回模拟数据
    if (isDevelopmentMode || !supabase) {
      return createSuccessResponse({
        user_stats: {
          successful_invitations: 8,
          total_rewards: 18000,
          claimable_rewards: 5000,
          milestone_5_claimed: true,
          milestone_10_claimed: false,
          leaderboard_reward: 0,
          leaderboard_claimed: false,
        },
        reward_breakdown: {
          base_rewards: 8000, // 8 * 1000
          milestone_rewards: 10000, // 5人里程碑
          leaderboard_rewards: 0,
        },
        leaderboard_position: 15,
        next_milestone: {
          target: 10,
          current: 8,
          reward: INVITATION_REWARDS.MILESTONE_10,
        },
        invitation_history: [
          {
            invitee_address: '0x1234...5678',
            purchase_amount: 2.5,
            reward_earned: 1000,
            timestamp: new Date().toISOString(),
            status: 'completed'
          },
          {
            invitee_address: '0x8765...4321',
            purchase_amount: 1.2,
            reward_earned: 1000,
            timestamp: new Date(Date.now() - 86400000).toISOString(),
            status: 'completed'
          }
        ]
      });
    }

    // 生产环境数据库查询
    const { data: userStats, error: statsError } = await supabase
      .from('invitation_stats')
      .select(`
        *,
        invitation_records (
          id,
          invitee_id,
          purchase_amount,
          reward_amount,
          created_at,
          status
        )
      `)
      .eq('user_id', userId)
      .single();

    if (statsError && statsError.code !== 'PGRST116') {
      throw createApiError(
        ApiErrorCode.DATABASE_ERROR,
        'Failed to fetch user stats',
        500,
        { details: statsError }
      );
    }

    // 如果用户没有邀请记录，创建默认记录
    if (!userStats) {
      const { data: newStats, error: createError } = await supabase
        .from('invitation_stats')
        .insert({
          user_id: userId,
          successful_invitations: 0,
          total_rewards: 0,
          claimable_rewards: 0,
          milestone_5_claimed: false,
          milestone_10_claimed: false,
          leaderboard_reward: 0,
          leaderboard_claimed: false,
        })
        .select()
        .single();

      if (createError) {
        throw createApiError(
          ApiErrorCode.DATABASE_ERROR,
          'Failed to create user stats',
          500,
          { details: createError }
        );
      }

      return createSuccessResponse({
        user_stats: newStats,
        reward_breakdown: {
          base_rewards: 0,
          milestone_rewards: 0,
          leaderboard_rewards: 0,
        },
        leaderboard_position: null,
        next_milestone: {
          target: 5,
          current: 0,
          reward: INVITATION_REWARDS.MILESTONE_5,
        },
        invitation_history: []
      });
    }

    // 计算奖励分解
    const baseRewards = userStats.successful_invitations * INVITATION_REWARDS.BASE_REWARD;
    let milestoneRewards = 0;
    
    if (userStats.milestone_5_claimed) {
      milestoneRewards += INVITATION_REWARDS.MILESTONE_5;
    }
    if (userStats.milestone_10_claimed) {
      milestoneRewards += INVITATION_REWARDS.MILESTONE_10;
    }

    // 获取排行榜位置
    const { data: leaderboard, error: leaderboardError } = await supabase
      .from('invitation_stats')
      .select('user_id, successful_invitations')
      .order('successful_invitations', { ascending: false })
      .limit(100);

    let leaderboardPosition = null;
    if (!leaderboardError && leaderboard) {
      const position = leaderboard.findIndex(user => user.user_id === userId);
      leaderboardPosition = position >= 0 ? position + 1 : null;
    }

    // 确定下一个里程碑
    let nextMilestone = null;
    if (userStats.successful_invitations < 5) {
      nextMilestone = {
        target: 5,
        current: userStats.successful_invitations,
        reward: INVITATION_REWARDS.MILESTONE_5,
      };
    } else if (userStats.successful_invitations < 10) {
      nextMilestone = {
        target: 10,
        current: userStats.successful_invitations,
        reward: INVITATION_REWARDS.MILESTONE_10,
      };
    }

    return createSuccessResponse({
      user_stats: userStats,
      reward_breakdown: {
        base_rewards: baseRewards,
        milestone_rewards: milestoneRewards,
        leaderboard_rewards: userStats.leaderboard_reward || 0,
      },
      leaderboard_position: leaderboardPosition,
      next_milestone: nextMilestone,
      invitation_history: userStats.invitation_records || []
    });

  } catch (error) {
    return handleApiError(error);
  }
}

/**
 * @api {POST} /api/invitation/rewards 领取邀请奖励
 * @apiDescription 用户主动领取可用的邀请奖励
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, rewardType } = body;

    if (!userId) {
      throw createApiError(
        ApiErrorCode.VALIDATION_ERROR,
        'User ID is required',
        400
      );
    }

    if (!rewardType || !['base', 'milestone', 'leaderboard'].includes(rewardType)) {
      throw createApiError(
        ApiErrorCode.VALIDATION_ERROR,
        'Valid reward type is required',
        400
      );
    }

    // 开发环境返回模拟响应
    if (isDevelopmentMode || !supabase) {
      return createSuccessResponse({
        success: true,
        claimed_amount: 5000,
        transaction_hash: '0x1234567890abcdef',
        remaining_claimable: 0,
        message: 'Rewards claimed successfully'
      });
    }

    // 获取用户当前状态
    const { data: userStats, error: statsError } = await supabase
      .from('invitation_stats')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (statsError) {
      throw createApiError(
        ApiErrorCode.DATABASE_ERROR,
        'Failed to fetch user stats',
        500,
        { details: statsError }
      );
    }

    if (!userStats || userStats.claimable_rewards <= 0) {
      throw createApiError(
        ApiErrorCode.VALIDATION_ERROR,
        'No rewards available to claim',
        400
      );
    }

    // 这里应该调用智能合约进行实际的代币转账
    // 目前返回模拟响应
    const claimedAmount = userStats.claimable_rewards;

    // 更新数据库状态
    const { error: updateError } = await supabase
      .from('invitation_stats')
      .update({
        claimable_rewards: 0,
        total_rewards: userStats.total_rewards + claimedAmount,
        updated_at: new Date().toISOString(),
      })
      .eq('user_id', userId);

    if (updateError) {
      throw createApiError(
        ApiErrorCode.DATABASE_ERROR,
        'Failed to update user stats',
        500,
        { details: updateError }
      );
    }

    // 记录奖励领取历史
    const { error: historyError } = await supabase
      .from('reward_claims')
      .insert({
        user_id: userId,
        reward_type: rewardType,
        amount: claimedAmount,
        transaction_hash: '0x' + Math.random().toString(16).substr(2, 40), // 模拟交易哈希
        status: 'completed',
        created_at: new Date().toISOString(),
      });

    if (historyError) {
      console.error('Failed to record claim history:', historyError);
      // 不抛出错误，因为主要操作已完成
    }

    return createSuccessResponse({
      success: true,
      claimed_amount: claimedAmount,
      transaction_hash: '0x' + Math.random().toString(16).substr(2, 40),
      remaining_claimable: 0,
      message: 'Rewards claimed successfully'
    });

  } catch (error) {
    return handleApiError(error);
  }
}

// 配置Edge Runtime
export const runtime = 'nodejs'; // 需要数据库连接，使用Node.js runtime
