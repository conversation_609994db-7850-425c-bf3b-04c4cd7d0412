// 配置Edge Runtime
export const runtime = 'edge';

import { NextRequest, NextResponse } from 'next/server';
import {
  withErrorHandler,
  createSuccessResponse,
  createErrorResponse,
  createApiError,
  ApiErrorCode,
  validateRequestBody
} from '@/lib/api-error-handler';

interface PriceDataPoint {
  time: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

interface PriceResponse {
  symbol: string;
  data: PriceDataPoint[];
  currentPrice: number;
  priceChange24h: number;
  volume24h: number;
  marketCap: number;
}

// 模拟价格数据生成器
function generatePriceData(days: number = 30): PriceDataPoint[] {
  const data: PriceDataPoint[] = [];
  let basePrice = 0.1; // HAOX 基础价格
  const now = new Date();
  
  for (let i = days; i >= 0; i--) {
    const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
    
    // 模拟价格波动
    const volatility = 0.05; // 5% 波动率
    const trend = Math.sin(i / 10) * 0.02; // 添加趋势
    const randomChange = (Math.random() - 0.5) * volatility;
    
    const open = basePrice;
    const close = basePrice * (1 + trend + randomChange);
    const high = Math.max(open, close) * (1 + Math.random() * 0.02);
    const low = Math.min(open, close) * (1 - Math.random() * 0.02);
    const volume = Math.random() * 1000000 + 500000; // 50万到150万交易量
    
    data.push({
      time: date.toISOString().split('T')[0],
      open: Number(open.toFixed(6)),
      high: Number(high.toFixed(6)),
      low: Number(low.toFixed(6)),
      close: Number(close.toFixed(6)),
      volume: Math.floor(volume)
    });
    
    basePrice = close;
  }
  
  return data;
}

// 获取实时价格数据（模拟）
async function fetchRealTimePrice(): Promise<number> {
  // 在实际应用中，这里会调用真实的API
  // 例如：CoinGecko, CoinMarketCap, 或币安API
  
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 100));
  
  // 返回模拟价格
  return 0.1 + (Math.random() - 0.5) * 0.01;
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const symbol = searchParams.get('symbol') || 'HAOX';
    const timeframe = searchParams.get('timeframe') || '1D';
    const limit = parseInt(searchParams.get('limit') || '30');

    // 根据时间周期调整数据点数量
    let dataPoints = limit;
    switch (timeframe) {
      case '1H':
        dataPoints = 24; // 24小时
        break;
      case '4H':
        dataPoints = 30; // 5天
        break;
      case '1D':
        dataPoints = 30; // 30天
        break;
      case '1W':
        dataPoints = 52; // 52周
        break;
      case '1M':
        dataPoints = 12; // 12个月
        break;
    }

    // 生成价格数据
    const priceData = generatePriceData(dataPoints);
    
    // 获取当前价格
    const currentPrice = await fetchRealTimePrice();
    
    // 计算24小时变化
    const price24hAgo = priceData[priceData.length - 2]?.close || priceData[0].close;
    const priceChange24h = ((currentPrice - price24hAgo) / price24hAgo) * 100;
    
    // 计算24小时交易量
    const volume24h = priceData.slice(-1)[0]?.volume || 0;
    
    // 模拟市值（价格 * 总供应量）
    const totalSupply = 1000000000; // 10亿代币
    const marketCap = currentPrice * totalSupply;

    const response: PriceResponse = {
      symbol: symbol.toUpperCase(),
      data: priceData,
      currentPrice,
      priceChange24h: Number(priceChange24h.toFixed(2)),
      volume24h,
      marketCap
    };

    return NextResponse.json(response);
    
  } catch (error) {
    console.error('Price data API error:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch price data',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// 支持 POST 请求用于设置价格预警
export const POST = withErrorHandler(async (request: NextRequest) => {
  const body = await request.json();

  // 验证请求体
  const validationError = validateRequestBody(body, ['symbol', 'targetPrice', 'alertType']);
  if (validationError) {
    return createErrorResponse(validationError);
  }

  const { symbol, targetPrice, alertType } = body;

  // 验证价格格式
  if (isNaN(parseFloat(targetPrice)) || parseFloat(targetPrice) <= 0) {
    const error = createApiError(
      ApiErrorCode.VALIDATION_ERROR,
      '目标价格必须是大于0的数字',
      400
    );
    return createErrorResponse(error);
  }

  // 在实际应用中，这里会保存到数据库
  console.log('Price alert set:', { symbol, targetPrice, alertType });

  const alertData = {
    symbol,
    targetPrice,
    alertType,
    createdAt: new Date().toISOString()
  };

  return createSuccessResponse(alertData, '价格预警设置成功');
});

// 支持 WebSocket 连接的实时价格推送
export async function PATCH(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const symbol = searchParams.get('symbol') || 'HAOX';

    // 模拟实时价格更新
    const currentPrice = await fetchRealTimePrice();
    const timestamp = new Date().toISOString();

    return NextResponse.json({
      symbol: symbol.toUpperCase(),
      price: currentPrice,
      timestamp,
      change24h: (Math.random() - 0.5) * 10 // 模拟24小时变化
    });

  } catch (error) {
    console.error('Real-time price API error:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch real-time price',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
