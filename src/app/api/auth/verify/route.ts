/**
 * 用户验证API路由
 * 验证JWT token并返回用户信息
 */

import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import jwt from 'jsonwebtoken';
import {
  withError<PERSON>and<PERSON>,
  createSuccessResponse,
  createErrorResponse,
  createApiError,
  ApiErrorCode,
  validateRequestBody
} from '@/lib/api-error-handler';

// 使用Node.js Runtime (需要crypto模块)
// export const runtime = 'edge';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

interface JWTPayload {
  userId: number;
  username?: string;
  firstName: string;
  lastName?: string;
  photoUrl?: string;
  iat: number;
  exp: number;
}

export const GET = withErrorHandler(async (request: NextRequest) => {
  const cookieStore = cookies();
  const token = cookieStore.get('telegram-auth-token')?.value;

  if (!token) {
    const error = createApiError(
      ApiErrorCode.UNAUTHORIZED,
      '未找到认证令牌',
      401
    );
    return createErrorResponse(error);
  }

  // 验证JWT token
  const decoded = jwt.verify(token, JWT_SECRET) as JWTPayload;

  const userData = {
    id: decoded.userId,
    username: decoded.username,
    firstName: decoded.firstName,
    lastName: decoded.lastName,
    photoUrl: decoded.photoUrl,
  };

  return createSuccessResponse(userData);
});

export const POST = withErrorHandler(async (request: NextRequest) => {
  const body = await request.json();

  // 验证请求体
  const validationError = validateRequestBody(body, ['token']);
  if (validationError) {
    return createErrorResponse(validationError);
  }

  const { token } = body;

  // 验证JWT token
  const decoded = jwt.verify(token, JWT_SECRET) as JWTPayload;

  const userData = {
    id: decoded.userId,
    username: decoded.username,
    firstName: decoded.firstName,
    lastName: decoded.lastName,
    photoUrl: decoded.photoUrl,
  };

  return createSuccessResponse(userData);
});
