/**
 * Telegram OAuth API路由
 * 处理Telegram登录认证
 */

// 使用Node.js Runtime (需要crypto模块)
// export const runtime = 'edge';

import { NextRequest, NextResponse } from 'next/server';
import { telegramBotService } from '@/services/telegram/TelegramBotService';
import { cookies } from 'next/headers';
import jwt from 'jsonwebtoken';
import {
  withErrorHandler,
  createSuccessResponse,
  createErrorResponse,
  createApiError,
  ApiErrorCode,
  validateRequestBody
} from '@/lib/api-error-handler';
import {
  withRateLimit,
  withLogging,
  compose
} from '@/lib/api-middleware';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

const telegramLogin = async (request: NextRequest) => {
  const authData = await request.json();

  // 验证请求体
  const validationError = validateRequestBody(authData, ['id', 'first_name', 'auth_date', 'hash']);
  if (validationError) {
    return createErrorResponse(validationError);
  }

  // 验证Telegram认证数据
  const user = await telegramBotService.handleLoginCallback(authData);

  if (!user) {
    const error = createApiError(
      ApiErrorCode.UNAUTHORIZED,
      '无效的Telegram认证数据',
      401
    );
    return createErrorResponse(error);
  }

  // 创建JWT token
  const token = jwt.sign(
    {
      userId: user.id,
      username: user.username,
      firstName: user.first_name,
      lastName: user.last_name,
      photoUrl: user.photo_url,
    },
    JWT_SECRET,
    { expiresIn: '7d' }
  );

  // 设置cookie
  const cookieStore = cookies();
  cookieStore.set('telegram-auth-token', token, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: 7 * 24 * 60 * 60, // 7天
  });

  const userData = {
    id: user.id,
    username: user.username,
    firstName: user.first_name,
    lastName: user.last_name,
    photoUrl: user.photo_url,
  };

  return createSuccessResponse(userData, 'Telegram认证成功');
};

// 应用中间件
export const POST = compose(
  withErrorHandler,
  withLogging,
  withRateLimit(10, 60000) // 每分钟最多10次登录尝试
)(telegramLogin);

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // 从URL参数中获取Telegram认证数据
    const authData = {
      id: searchParams.get('id') || '',
      first_name: searchParams.get('first_name') || '',
      last_name: searchParams.get('last_name') || '',
      username: searchParams.get('username') || '',
      photo_url: searchParams.get('photo_url') || '',
      auth_date: searchParams.get('auth_date') || '',
      hash: searchParams.get('hash') || '',
    };

    // 验证必需字段
    if (!authData.id || !authData.first_name || !authData.auth_date || !authData.hash) {
      return NextResponse.redirect(new URL('/login?error=invalid_data', request.url));
    }

    // 验证Telegram认证数据
    const user = await telegramBotService.handleLoginCallback(authData);
    
    if (!user) {
      return NextResponse.redirect(new URL('/login?error=auth_failed', request.url));
    }

    // 创建JWT token
    const token = jwt.sign(
      {
        userId: user.id,
        username: user.username,
        firstName: user.first_name,
        lastName: user.last_name,
        photoUrl: user.photo_url,
      },
      JWT_SECRET,
      { expiresIn: '7d' }
    );

    // 创建响应并设置cookie
    const response = NextResponse.redirect(new URL('/profile', request.url));
    response.cookies.set('telegram-auth-token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 7 * 24 * 60 * 60, // 7天
    });

    return response;
  } catch (error) {
    console.error('Telegram auth GET error:', error);
    return NextResponse.redirect(new URL('/login?error=server_error', request.url));
  }
}
