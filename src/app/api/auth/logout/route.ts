/**
 * 登出API路由
 * 清除认证cookie并登出用户
 */

import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    // 创建响应并清除认证cookie
    const response = NextResponse.json({
      success: true,
      message: 'Logged out successfully',
    });

    // 清除认证cookie
    response.cookies.delete('telegram-auth-token');

    return response;
  } catch (error) {
    console.error('Logout error:', error);
    return NextResponse.json(
      { error: 'Logout failed' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // 创建重定向响应并清除认证cookie
    const response = NextResponse.redirect(new URL('/', request.url));

    // 清除认证cookie
    response.cookies.delete('telegram-auth-token');

    return response;
  } catch (error) {
    console.error('Logout error:', error);
    return NextResponse.redirect(new URL('/', request.url));
  }
}
