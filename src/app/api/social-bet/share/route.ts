/**
 * 社交赌约分享奖励API
 */

import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

/**
 * @api {POST} /api/social-bet/share 记录分享行为
 * @apiDescription 记录用户分享赌约的行为，用于后续奖励计算
 */
export async function POST(request: NextRequest) {
  try {
    const { betId, platform, userId } = await request.json();

    if (!betId || !platform) {
      return NextResponse.json(
        { success: false, message: '缺少必要参数' },
        { status: 400 }
      );
    }

    // 模拟分享处理，返回成功响应
    const rewardAmount = 5; // 每次分享奖励5福气
    const dailyShareCount = Math.floor(Math.random() * 3) + 1; // 模拟今日分享次数
    const maxDailyShares = 5;

    // 模拟分享处理，返回成功响应
    const rewardAmount = 5; // 每次分享奖励5福气
    const dailyShareCount = Math.floor(Math.random() * 3) + 1; // 模拟今日分享次数
    const maxDailyShares = 5;

    return NextResponse.json({
      success: true,
      message: dailyShareCount <= maxDailyShares
        ? `分享成功！获得 ${rewardAmount} 福气奖励`
        : '分享成功！今日分享奖励已达上限',
      data: {
        shareId: `share-${Date.now()}`,
        rewardAmount: dailyShareCount <= maxDailyShares ? rewardAmount : 0,
        dailyShareCount,
        maxDailyShares,
        remainingShares: Math.max(0, maxDailyShares - dailyShareCount)
      }
    });

  } catch (error) {
    console.error('分享处理失败:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: error instanceof Error ? error.message : '分享处理失败' 
      },
      { status: 500 }
    );
  }
}

/**
 * @api {GET} /api/social-bet/share 获取分享统计
 * @apiDescription 获取用户的分享统计信息
 */
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const userId = url.searchParams.get('userId') || 'default-user';

    // 返回模拟分享统计数据
    const todayShareCount = Math.floor(Math.random() * 3) + 1;
    const totalShares = Math.floor(Math.random() * 50) + 10;
    const monthlyShares = Math.floor(Math.random() * 20) + 5;

    return NextResponse.json({
      success: true,
      data: {
        // 今日统计
        todayShareCount,
        maxDailyShares: 5,
        remainingShares: Math.max(0, 5 - todayShareCount),

        // 总体统计
        totalShares,
        monthlyShares,

        // 平台分布
        platformStats: {
          wechat: Math.floor(totalShares * 0.4),
          weibo: Math.floor(totalShares * 0.3),
          qq: Math.floor(totalShares * 0.2),
          link: Math.floor(totalShares * 0.1)
        },

        // 最近分享记录
        recentShares: [
          {
            id: 'share-1',
            betId: 'bet-1',
            betTitle: '2024年世界杯冠军预测',
            platform: 'wechat',
            sharedAt: new Date().toISOString(),
            reward: 5
          }
        ],

        // 奖励信息
        shareReward: 5,
        totalEarnings: Math.min(totalShares, 5) * 5, // 每日最多5次奖励

        // 激励信息
        encouragement: todayShareCount >= 5
          ? '今日分享奖励已达上限，明天继续分享获得更多福气！'
          : `今日还可分享 ${5 - todayShareCount} 次获得福气奖励！`
      }
    });

  } catch (error) {
    console.error('获取分享统计失败:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: error instanceof Error ? error.message : '获取分享统计失败' 
      },
      { status: 500 }
    );
  }
}

// 配置运行时
export const runtime = 'nodejs';
