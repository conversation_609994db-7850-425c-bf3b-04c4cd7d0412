/**
 * 社交赌约分享奖励API
 */

import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

/**
 * @api {POST} /api/social-bet/share 记录分享行为
 * @apiDescription 记录用户分享赌约的行为，用于后续奖励计算
 */
export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // 验证用户身份
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { betId, platform, userId } = await request.json();

    if (!betId || !platform) {
      return NextResponse.json(
        { success: false, message: '缺少必要参数' },
        { status: 400 }
      );
    }

    // 验证赌约是否存在
    const { data: bet, error: betError } = await supabase
      .from('social_bets')
      .select('id, title, status')
      .eq('id', betId)
      .single();

    if (betError || !bet) {
      return NextResponse.json(
        { success: false, message: '赌约不存在' },
        { status: 404 }
      );
    }

    // 记录分享行为
    const { data: shareRecord, error: shareError } = await supabase
      .from('bet_shares')
      .insert({
        bet_id: betId,
        sharer_id: user.id,
        platform: platform,
        shared_at: new Date().toISOString(),
        share_url: `${process.env.NEXT_PUBLIC_APP_URL}/social-bet/${betId}?ref=${user.id}`
      })
      .select()
      .single();

    if (shareError) {
      console.error('记录分享失败:', shareError);
      return NextResponse.json(
        { success: false, message: '记录分享失败' },
        { status: 500 }
      );
    }

    // 发放分享奖励（每日限制）
    const today = new Date().toISOString().split('T')[0];
    
    // 检查今日分享次数
    const { data: todayShares, error: countError } = await supabase
      .from('bet_shares')
      .select('id')
      .eq('sharer_id', user.id)
      .gte('shared_at', `${today}T00:00:00.000Z`)
      .lt('shared_at', `${today}T23:59:59.999Z`);

    if (countError) {
      console.error('查询分享次数失败:', countError);
    }

    const dailyShareCount = todayShares?.length || 0;
    const maxDailyShares = 5; // 每日最多5次分享奖励
    const shareReward = 5; // 每次分享奖励5福气

    let rewardAmount = 0;
    if (dailyShareCount <= maxDailyShares) {
      rewardAmount = shareReward;
      
      // 发放福气奖励
      const { error: rewardError } = await supabase.rpc('add_user_fortune', {
        p_user_id: user.id,
        p_amount: rewardAmount,
        p_transaction_type: 'share_reward',
        p_description: `分享赌约奖励: ${bet.title}`,
        p_reference_id: betId
      });

      if (rewardError) {
        console.error('发放分享奖励失败:', rewardError);
      }
    }

    return NextResponse.json({
      success: true,
      message: rewardAmount > 0 
        ? `分享成功！获得 ${rewardAmount} 福气奖励` 
        : '分享成功！今日分享奖励已达上限',
      data: {
        shareId: shareRecord.id,
        rewardAmount,
        dailyShareCount,
        maxDailyShares,
        remainingShares: Math.max(0, maxDailyShares - dailyShareCount)
      }
    });

  } catch (error) {
    console.error('分享处理失败:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: error instanceof Error ? error.message : '分享处理失败' 
      },
      { status: 500 }
    );
  }
}

/**
 * @api {GET} /api/social-bet/share 获取分享统计
 * @apiDescription 获取用户的分享统计信息
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // 验证用户身份
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const url = new URL(request.url);
    const userId = url.searchParams.get('userId') || user.id;

    // 获取用户分享统计
    const { data: shareStats, error: statsError } = await supabase
      .from('bet_shares')
      .select(`
        id,
        bet_id,
        platform,
        shared_at,
        social_bets!inner(title, status)
      `)
      .eq('sharer_id', userId)
      .order('shared_at', { ascending: false })
      .limit(50);

    if (statsError) {
      console.error('获取分享统计失败:', statsError);
      return NextResponse.json(
        { success: false, message: '获取分享统计失败' },
        { status: 500 }
      );
    }

    // 统计今日分享次数
    const today = new Date().toISOString().split('T')[0];
    const todayShares = shareStats?.filter(share => 
      share.shared_at.startsWith(today)
    ) || [];

    // 统计平台分布
    const platformStats = {};
    shareStats?.forEach(share => {
      platformStats[share.platform] = (platformStats[share.platform] || 0) + 1;
    });

    // 统计本月分享次数
    const thisMonth = new Date().toISOString().substring(0, 7);
    const monthlyShares = shareStats?.filter(share => 
      share.shared_at.startsWith(thisMonth)
    ) || [];

    return NextResponse.json({
      success: true,
      data: {
        // 今日统计
        todayShareCount: todayShares.length,
        maxDailyShares: 5,
        remainingShares: Math.max(0, 5 - todayShares.length),
        
        // 总体统计
        totalShares: shareStats?.length || 0,
        monthlyShares: monthlyShares.length,
        
        // 平台分布
        platformStats,
        
        // 最近分享记录
        recentShares: shareStats?.slice(0, 10).map(share => ({
          id: share.id,
          betId: share.bet_id,
          betTitle: share.social_bets?.title,
          platform: share.platform,
          sharedAt: share.shared_at,
          reward: 5 // 固定奖励金额
        })) || [],
        
        // 奖励信息
        shareReward: 5,
        totalEarnings: Math.min(shareStats?.length || 0, 5) * 5, // 每日最多5次奖励
        
        // 激励信息
        encouragement: todayShares.length >= 5 
          ? '今日分享奖励已达上限，明天继续分享获得更多福气！'
          : `今日还可分享 ${5 - todayShares.length} 次获得福气奖励！`
      }
    });

  } catch (error) {
    console.error('获取分享统计失败:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: error instanceof Error ? error.message : '获取分享统计失败' 
      },
      { status: 500 }
    );
  }
}

// 配置运行时
export const runtime = 'nodejs';
