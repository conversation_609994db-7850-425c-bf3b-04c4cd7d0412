/**
 * 用户赌约列表API
 */

import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

/**
 * @api {GET} /api/social-bet/user-bets 获取用户赌约列表
 * @apiDescription 获取用户创建和参与的赌约列表
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    const url = new URL(request.url);
    const userId = url.searchParams.get('userId');
    const filter = url.searchParams.get('filter') || 'all';
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '20');

    if (!userId) {
      return NextResponse.json(
        { success: false, message: '用户ID必需' },
        { status: 400 }
      );
    }

    const offset = (page - 1) * limit;

    // 获取用户创建的赌约
    let createdBetsQuery = supabase
      .from('social_bets')
      .select(`
        id,
        title,
        description,
        category,
        status,
        bet_type,
        min_bet_amount,
        max_bet_amount,
        total_fortune_pool,
        participant_count,
        created_at,
        betting_deadline,
        result_deadline,
        options,
        winning_option,
        finalized_at
      `)
      .eq('creator_id', userId);

    // 获取用户参与的赌约
    let participatedBetsQuery = supabase
      .from('bet_participants')
      .select(`
        bet_amount,
        selected_option,
        participated_at,
        earnings,
        social_bets!inner(
          id,
          title,
          description,
          category,
          status,
          bet_type,
          min_bet_amount,
          max_bet_amount,
          total_fortune_pool,
          participant_count,
          created_at,
          betting_deadline,
          result_deadline,
          options,
          winning_option,
          finalized_at
        )
      `)
      .eq('user_id', userId);

    // 应用筛选条件
    if (filter === 'active') {
      createdBetsQuery = createdBetsQuery.in('status', ['active', 'judging']);
      participatedBetsQuery = participatedBetsQuery.in('social_bets.status', ['active', 'judging']);
    } else if (filter === 'completed') {
      createdBetsQuery = createdBetsQuery.eq('status', 'ended');
      participatedBetsQuery = participatedBetsQuery.eq('social_bets.status', 'ended');
    }

    // 执行查询
    const [createdResult, participatedResult] = await Promise.all([
      filter === 'participated' ? { data: [], error: null } : createdBetsQuery,
      filter === 'created' ? { data: [], error: null } : participatedBetsQuery
    ]);

    if (createdResult.error) {
      console.error('获取创建的赌约失败:', createdResult.error);
    }

    if (participatedResult.error) {
      console.error('获取参与的赌约失败:', participatedResult.error);
    }

    // 处理创建的赌约数据
    const createdBets = (createdResult.data || []).map(bet => ({
      ...bet,
      role: 'creator' as const,
      myBetAmount: undefined,
      mySelectedOption: undefined,
      result: bet.status === 'ended' && bet.winning_option ? {
        winningOption: bet.winning_option,
        finalizedAt: bet.finalized_at
      } : undefined
    }));

    // 处理参与的赌约数据
    const participatedBets = (participatedResult.data || []).map(participation => ({
      ...participation.social_bets,
      role: 'participant' as const,
      myBetAmount: participation.bet_amount,
      mySelectedOption: participation.selected_option,
      result: participation.social_bets.status === 'ended' && participation.social_bets.winning_option ? {
        winningOption: participation.social_bets.winning_option,
        finalizedAt: participation.social_bets.finalized_at,
        myEarnings: participation.earnings || 0,
        isWinner: participation.selected_option === participation.social_bets.winning_option
      } : undefined
    }));

    // 合并并排序
    const allBets = [...createdBets, ...participatedBets]
      .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

    // 分页
    const paginatedBets = allBets.slice(offset, offset + limit);

    return NextResponse.json({
      success: true,
      data: paginatedBets,
      pagination: {
        page,
        limit,
        total: allBets.length,
        totalPages: Math.ceil(allBets.length / limit)
      }
    });

  } catch (error) {
    console.error('获取用户赌约失败:', error);
    
    // 开发环境返回模拟数据
    const mockBets = [
      {
        id: 'mock-bet-1',
        title: '2024年世界杯冠军预测',
        description: '预测2024年世界杯的冠军队伍',
        category: 'sports',
        status: 'active',
        role: 'creator',
        totalPool: 50000,
        participants: 25,
        createdAt: '2024-01-15T10:00:00Z',
        endTime: '2024-06-15T20:00:00Z',
        resultDeadline: '2024-07-15T20:00:00Z',
        options: [
          { id: 'A', text: '巴西', odds: 2.5, bets: 20000 },
          { id: 'B', text: '阿根廷', odds: 3.0, bets: 15000 },
          { id: 'C', text: '法国', odds: 2.8, bets: 15000 }
        ]
      },
      {
        id: 'mock-bet-2',
        title: 'Bitcoin价格预测',
        description: '预测比特币在月底的价格走势',
        category: 'finance',
        status: 'ended',
        role: 'participant',
        myBetAmount: 500,
        mySelectedOption: 'A',
        totalPool: 20000,
        participants: 40,
        createdAt: '2024-01-10T15:30:00Z',
        endTime: '2024-01-31T23:59:59Z',
        resultDeadline: '2024-02-01T23:59:59Z',
        options: [
          { id: 'A', text: '上涨', odds: 1.8, bets: 12000 },
          { id: 'B', text: '下跌', odds: 2.2, bets: 8000 }
        ],
        result: {
          winningOption: 'A',
          finalizedAt: '2024-02-01T12:00:00Z',
          myEarnings: 900,
          isWinner: true
        }
      },
      {
        id: 'mock-bet-3',
        title: '苹果新品发布会预测',
        description: '预测苹果春季发布会是否会发布新iPad',
        category: 'technology',
        status: 'judging',
        role: 'participant',
        myBetAmount: 200,
        mySelectedOption: 'B',
        totalPool: 15000,
        participants: 30,
        createdAt: '2024-01-08T09:00:00Z',
        endTime: '2024-03-15T18:00:00Z',
        resultDeadline: '2024-03-20T18:00:00Z',
        options: [
          { id: 'A', text: '会发布', odds: 1.5, bets: 10000 },
          { id: 'B', text: '不会发布', odds: 2.5, bets: 5000 }
        ]
      }
    ];

    return NextResponse.json({
      success: true,
      data: mockBets,
      pagination: {
        page: 1,
        limit: 20,
        total: mockBets.length,
        totalPages: 1
      }
    });
  }
}

// 配置运行时
export const runtime = 'nodejs';
