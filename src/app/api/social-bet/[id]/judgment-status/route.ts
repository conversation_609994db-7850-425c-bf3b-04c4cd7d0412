import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const betId = params.id;
    const url = new URL(request.url);
    const userId = url.searchParams.get('userId');

    // 获取赌约基本信息
    const betInfo = await getBetInfo(betId);
    if (!betInfo) {
      return NextResponse.json(
        { success: false, message: '赌约不存在' },
        { status: 404 }
      );
    }

    // 获取裁定历史
    const judgmentHistory = await getJudgmentHistory(betId);
    
    // 获取当前轮次状态
    const currentRoundStatus = await getCurrentRoundStatus(betId, betInfo.current_round || 1);
    
    // 如果提供了用户ID，检查用户的参与状态
    let userStatus = null;
    if (userId) {
      userStatus = await getUserJudgmentStatus(userId, betId);
    }

    return NextResponse.json({
      success: true,
      data: {
        betId,
        betInfo,
        judgmentHistory,
        currentRoundStatus,
        userStatus
      }
    });

  } catch (error) {
    console.error('获取裁定状态失败:', error);
    return NextResponse.json(
      { success: false, message: '获取裁定状态失败' },
      { status: 500 }
    );
  }
}

// 获取赌约基本信息
async function getBetInfo(betId: string) {
  try {
    const { data: bet } = await supabase
      .from('social_bets')
      .select(`
        id,
        title,
        description,
        options,
        status,
        winning_option,
        current_round,
        round_deadline,
        judging_started_at,
        judging_completed_at,
        confirmation_deadline
      `)
      .eq('id', betId)
      .single();

    return bet;
  } catch (error) {
    console.error('获取赌约信息失败:', error);
    // 开发环境返回模拟数据
    return {
      id: betId,
      title: '2024年世界杯冠军预测',
      description: '预测2024年世界杯的冠军队伍',
      options: ['巴西', '阿根廷', '法国'],
      status: 'judging',
      winning_option: null,
      current_round: 1,
      round_deadline: new Date(Date.now() + 12 * 60 * 60 * 1000).toISOString(),
      judging_started_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      judging_completed_at: null,
      confirmation_deadline: null
    };
  }
}

// 获取裁定历史
async function getJudgmentHistory(betId: string) {
  try {
    const { data: judgments } = await supabase
      .from('bet_judgments')
      .select(`
        id,
        round_number,
        vote_option,
        reputation_score,
        certification_level,
        voted_at,
        is_correct,
        judge_id,
        users!inner(
          telegram_id,
          username
        )
      `)
      .eq('bet_id', betId)
      .order('round_number', { ascending: true })
      .order('voted_at', { ascending: true });

    // 按轮次分组
    const groupedByRound = {};
    judgments?.forEach(judgment => {
      const round = judgment.round_number;
      if (!groupedByRound[round]) {
        groupedByRound[round] = [];
      }
      groupedByRound[round].push({
        id: judgment.id,
        voteOption: judgment.vote_option,
        reputationScore: judgment.reputation_score,
        certificationLevel: judgment.certification_level,
        votedAt: judgment.voted_at,
        isCorrect: judgment.is_correct,
        judge: {
          id: judgment.judge_id,
          telegramId: judgment.users?.telegram_id,
          username: judgment.users?.username
        }
      });
    });

    return groupedByRound;
  } catch (error) {
    console.error('获取裁定历史失败:', error);
    // 开发环境返回模拟数据
    return {
      1: [
        {
          id: 'judgment-1',
          voteOption: 0,
          reputationScore: 95,
          certificationLevel: 'X2',
          votedAt: new Date(Date.now() - 60 * 60 * 1000).toISOString(),
          isCorrect: null,
          judge: {
            id: 'judge-1',
            telegramId: '@judge_user1',
            username: '预测大师'
          }
        },
        {
          id: 'judgment-2',
          voteOption: 0,
          reputationScore: 88,
          certificationLevel: 'X1',
          votedAt: new Date(Date.now() - 45 * 60 * 1000).toISOString(),
          isCorrect: null,
          judge: {
            id: 'judge-2',
            telegramId: '@judge_user2',
            username: '智慧投资者'
          }
        }
      ]
    };
  }
}

// 获取当前轮次状态
async function getCurrentRoundStatus(betId: string, currentRound: number) {
  try {
    // 投票阈值配置
    const thresholds = {
      1: { required: 11, total: 20, timeLimit: 24 },
      2: { required: 6, total: 10, timeLimit: 12 },
      3: { required: 3, total: 5, timeLimit: 6 }
    };

    const threshold = thresholds[currentRound];
    if (!threshold) {
      return null;
    }

    // 获取当前轮次的投票
    const { data: votes } = await supabase
      .from('bet_judgments')
      .select('vote_option')
      .eq('bet_id', betId)
      .eq('round_number', currentRound);

    const currentVotes = votes?.length || 0;
    
    // 统计各选项票数
    const voteDistribution = {};
    votes?.forEach(vote => {
      voteDistribution[vote.vote_option] = (voteDistribution[vote.vote_option] || 0) + 1;
    });

    // 检查是否达到阈值
    let leadingOption = null;
    let maxVotes = 0;
    let thresholdMet = false;

    for (const [option, count] of Object.entries(voteDistribution)) {
      if (count > maxVotes) {
        maxVotes = count;
        leadingOption = parseInt(option);
      }
      if (count >= threshold.required) {
        thresholdMet = true;
      }
    }

    return {
      round: currentRound,
      currentVotes,
      requiredVotes: threshold.required,
      totalSlots: threshold.total,
      timeLimit: threshold.timeLimit,
      voteDistribution,
      leadingOption,
      maxVotes,
      thresholdMet,
      progress: Math.min((currentVotes / threshold.required) * 100, 100)
    };
  } catch (error) {
    console.error('获取当前轮次状态失败:', error);
    // 开发环境返回模拟数据
    return {
      round: 1,
      currentVotes: 2,
      requiredVotes: 11,
      totalSlots: 20,
      timeLimit: 24,
      voteDistribution: { 0: 2 },
      leadingOption: 0,
      maxVotes: 2,
      thresholdMet: false,
      progress: 18.2
    };
  }
}

// 获取用户裁定状态
async function getUserJudgmentStatus(userId: string, betId: string) {
  try {
    // 检查用户是否为参与者
    const { data: participation } = await supabase
      .from('bet_participants')
      .select('id, option_choice, fortune_amount')
      .eq('bet_id', betId)
      .eq('user_id', userId)
      .single();

    // 获取用户在各轮的投票记录
    const { data: userVotes } = await supabase
      .from('bet_judgments')
      .select('round_number, vote_option, voted_at')
      .eq('bet_id', betId)
      .eq('judge_id', userId)
      .order('round_number');

    // 获取用户信誉信息
    const { data: reputation } = await supabase
      .from('user_reputation')
      .select('current_score, certification_level, daily_judgments_used')
      .eq('user_id', userId)
      .single();

    return {
      isParticipant: !!participation,
      participation: participation ? {
        optionChoice: participation.option_choice,
        fortuneAmount: participation.fortune_amount
      } : null,
      votes: userVotes?.reduce((acc, vote) => {
        acc[vote.round_number] = {
          voteOption: vote.vote_option,
          votedAt: vote.voted_at
        };
        return acc;
      }, {}) || {},
      reputation: reputation || {
        current_score: 90,
        certification_level: 'X2',
        daily_judgments_used: 0
      }
    };
  } catch (error) {
    console.error('获取用户裁定状态失败:', error);
    // 开发环境返回模拟数据
    return {
      isParticipant: false,
      participation: null,
      votes: {},
      reputation: {
        current_score: 90,
        certification_level: 'X2',
        daily_judgments_used: 0
      }
    };
  }
}
