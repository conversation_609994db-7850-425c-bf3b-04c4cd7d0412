import { NextRequest, NextResponse } from 'next/server';
import { traceabilityService } from '@/lib/services/traceabilityService';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const betId = params.id;

    if (!betId) {
      return NextResponse.json(
        { success: false, message: '赌约ID必需' },
        { status: 400 }
      );
    }

    // 获取赌约完整生命周期
    const lifecycle = await traceabilityService.getBetLifecycle(betId);

    // 获取福气流转统计
    const fortuneStats = await traceabilityService.getFortuneFlowStats(betId);

    // 格式化操作类型显示文本
    const formattedLifecycle = {
      ...lifecycle,
      operations: lifecycle.operations.map(op => ({
        ...op,
        typeText: getOperationTypeText(op.operationType),
        typeIcon: getOperationTypeIcon(op.operationType),
        typeColor: getOperationTypeColor(op.operationType)
      })),
      fortuneFlows: lifecycle.fortuneFlows.map(flow => ({
        ...flow,
        typeText: getFortuneFlowTypeText(flow.flowType),
        typeIcon: getFortuneFlowTypeIcon(flow.flowType),
        typeColor: getFortuneFlowTypeColor(flow.flowType),
        formattedAmount: formatFortuneAmount(flow.amount)
      })),
      timeline: lifecycle.timeline.map(item => ({
        ...item,
        formattedTime: formatTimestamp(item.timestamp),
        relativeTime: getRelativeTime(item.timestamp)
      }))
    };

    return NextResponse.json({
      success: true,
      data: {
        lifecycle: formattedLifecycle,
        stats: {
          ...fortuneStats,
          operationCount: lifecycle.operations.length,
          participantCount: lifecycle.participants.length,
          timelineEvents: lifecycle.timeline.length
        }
      }
    });

  } catch (error) {
    console.error('获取赌约可追溯性信息失败:', error);
    
    return NextResponse.json(
      { success: false, message: '获取可追溯性信息失败' },
      { status: 500 }
    );
  }
}

// 获取操作类型显示文本
function getOperationTypeText(type: string): string {
  const typeMap: Record<string, string> = {
    'bet_created': '创建赌约',
    'bet_joined': '参与赌约',
    'bet_started': '赌约开始',
    'judgment_round_1': '第一轮裁定',
    'judgment_round_2': '第二轮裁定',
    'judgment_round_3': '第三轮裁定',
    'judgment_completed': '裁定完成',
    'confirmation_requested': '请求确认',
    'confirmation_submitted': '提交确认',
    'dispute_raised': '提出争议',
    'settlement_started': '开始结算',
    'fortune_distributed': '福气分配',
    'bet_completed': '赌约完成',
    'bet_cancelled': '赌约取消'
  };
  return typeMap[type] || type;
}

// 获取操作类型图标
function getOperationTypeIcon(type: string): string {
  const iconMap: Record<string, string> = {
    'bet_created': '🎯',
    'bet_joined': '🤝',
    'bet_started': '🚀',
    'judgment_round_1': '👥',
    'judgment_round_2': '🎓',
    'judgment_round_3': '⚖️',
    'judgment_completed': '✅',
    'confirmation_requested': '❓',
    'confirmation_submitted': '✔️',
    'dispute_raised': '⚠️',
    'settlement_started': '💰',
    'fortune_distributed': '💸',
    'bet_completed': '🏆',
    'bet_cancelled': '❌'
  };
  return iconMap[type] || '📝';
}

// 获取操作类型颜色
function getOperationTypeColor(type: string): string {
  const colorMap: Record<string, string> = {
    'bet_created': 'bg-blue-100 text-blue-800',
    'bet_joined': 'bg-green-100 text-green-800',
    'bet_started': 'bg-purple-100 text-purple-800',
    'judgment_round_1': 'bg-yellow-100 text-yellow-800',
    'judgment_round_2': 'bg-orange-100 text-orange-800',
    'judgment_round_3': 'bg-red-100 text-red-800',
    'judgment_completed': 'bg-green-100 text-green-800',
    'confirmation_requested': 'bg-blue-100 text-blue-800',
    'confirmation_submitted': 'bg-green-100 text-green-800',
    'dispute_raised': 'bg-red-100 text-red-800',
    'settlement_started': 'bg-purple-100 text-purple-800',
    'fortune_distributed': 'bg-green-100 text-green-800',
    'bet_completed': 'bg-green-100 text-green-800',
    'bet_cancelled': 'bg-gray-100 text-gray-800'
  };
  return colorMap[type] || 'bg-gray-100 text-gray-800';
}

// 获取福气流转类型显示文本
function getFortuneFlowTypeText(type: string): string {
  const typeMap: Record<string, string> = {
    'stake_locked': '质押锁定',
    'stake_unlocked': '质押解锁',
    'fee_deducted': '手续费扣除',
    'reward_distributed': '奖励分配',
    'judge_reward': '裁判奖励',
    'referral_reward': '推荐奖励',
    'refund_processed': '退款处理'
  };
  return typeMap[type] || type;
}

// 获取福气流转类型图标
function getFortuneFlowTypeIcon(type: string): string {
  const iconMap: Record<string, string> = {
    'stake_locked': '🔒',
    'stake_unlocked': '🔓',
    'fee_deducted': '💳',
    'reward_distributed': '🎁',
    'judge_reward': '⚖️',
    'referral_reward': '👥',
    'refund_processed': '↩️'
  };
  return iconMap[type] || '💰';
}

// 获取福气流转类型颜色
function getFortuneFlowTypeColor(type: string): string {
  const colorMap: Record<string, string> = {
    'stake_locked': 'bg-red-100 text-red-800',
    'stake_unlocked': 'bg-green-100 text-green-800',
    'fee_deducted': 'bg-orange-100 text-orange-800',
    'reward_distributed': 'bg-green-100 text-green-800',
    'judge_reward': 'bg-blue-100 text-blue-800',
    'referral_reward': 'bg-purple-100 text-purple-800',
    'refund_processed': 'bg-yellow-100 text-yellow-800'
  };
  return colorMap[type] || 'bg-gray-100 text-gray-800';
}

// 格式化福气金额
function formatFortuneAmount(amount: number): string {
  if (amount >= 1000000) {
    return `${(amount / 1000000).toFixed(1)}M`;
  } else if (amount >= 1000) {
    return `${(amount / 1000).toFixed(1)}K`;
  }
  return amount.toString();
}

// 格式化时间戳
function formatTimestamp(timestamp: string): string {
  return new Date(timestamp).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
}

// 获取相对时间
function getRelativeTime(timestamp: string): string {
  const now = new Date();
  const time = new Date(timestamp);
  const diffMs = now.getTime() - time.getTime();
  
  const diffMinutes = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  
  if (diffMinutes < 1) {
    return '刚刚';
  } else if (diffMinutes < 60) {
    return `${diffMinutes}分钟前`;
  } else if (diffHours < 24) {
    return `${diffHours}小时前`;
  } else if (diffDays < 30) {
    return `${diffDays}天前`;
  } else {
    return formatTimestamp(timestamp);
  }
}
