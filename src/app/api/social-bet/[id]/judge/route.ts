import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

// 认证等级配置
const CERTIFICATION_LEVELS = {
  X1: { minHaox: 10, dailyQuota: 1, rounds: [1] },
  X2: { minHaox: 10000, dailyQuota: 5, rounds: [1, 2] },
  X3: { minHaox: 1000000, dailyQuota: 20, rounds: [1, 2] },
  X4: { minHaox: 10000000, dailyQuota: 50, rounds: [2, 3] },
  X5: { minHaox: 100000000, dailyQuota: -1, rounds: [2, 3] }
};

// 投票阈值配置
const VOTING_THRESHOLDS = {
  1: { required: 11, total: 20 }, // 第一轮：11/20票
  2: { required: 6, total: 10 },  // 第二轮：6/10票
  3: { required: 3, total: 5 }    // 第三轮：3/5票
};

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { round, voteOption, userId } = await request.json();
    const betId = params.id;

    if (!userId) {
      return NextResponse.json(
        { success: false, message: '用户未登录' },
        { status: 401 }
      );
    }

    // 1. 验证裁定资格
    const eligibilityCheck = await validateJudgeEligibility(userId, betId, round);
    if (!eligibilityCheck.eligible) {
      return NextResponse.json(
        { success: false, message: eligibilityCheck.reason },
        { status: 403 }
      );
    }

    // 2. 检查是否已投票
    const existingVote = await checkExistingVote(userId, betId, round);
    if (existingVote) {
      return NextResponse.json(
        { success: false, message: '您已在此轮投票' },
        { status: 400 }
      );
    }

    // 3. 提交投票
    const voteResult = await submitVote(userId, betId, round, voteOption, eligibilityCheck.userInfo);
    
    // 4. 检查是否达到投票阈值
    const thresholdResult = await checkVotingThreshold(betId, round);
    
    // 5. 如果达到阈值，处理轮次结果
    if (thresholdResult.thresholdMet) {
      await processRoundResult(betId, round, thresholdResult);
    }

    return NextResponse.json({
      success: true,
      message: '投票提交成功',
      data: {
        voteId: voteResult.id,
        betId,
        round,
        voteOption,
        thresholdStatus: thresholdResult,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('裁定投票失败:', error);
    return NextResponse.json(
      { success: false, message: '投票失败，请稍后重试' },
      { status: 500 }
    );
  }
}

// 验证裁定资格
async function validateJudgeEligibility(userId: string, betId: string, round: number) {
  try {
    // 检查用户是否为赌约参与者（利益冲突）
    const { data: participation } = await supabase
      .from('bet_participants')
      .select('id')
      .eq('bet_id', betId)
      .eq('user_id', userId)
      .single();

    if (participation) {
      return { eligible: false, reason: '赌约参与者不能担任裁判' };
    }

    // 获取用户信誉和认证信息
    const { data: userInfo } = await supabase
      .from('user_reputation')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (!userInfo) {
      return { eligible: false, reason: '用户信誉信息不存在' };
    }

    // 检查信誉分数门槛
    if (userInfo.current_score < 70) {
      return { eligible: false, reason: '信誉分数不足70分，无法参与裁定' };
    }

    // 检查认证等级权限
    const levelConfig = CERTIFICATION_LEVELS[userInfo.certification_level];
    if (!levelConfig || !levelConfig.rounds.includes(round)) {
      return { 
        eligible: false, 
        reason: `${userInfo.certification_level}认证等级无权参与第${round}轮裁定` 
      };
    }

    // 检查每日裁定次数限制
    if (levelConfig.dailyQuota !== -1) {
      const today = new Date().toISOString().split('T')[0];
      const { data: todayVotes } = await supabase
        .from('bet_judgments')
        .select('id')
        .eq('judge_id', userId)
        .gte('voted_at', `${today}T00:00:00Z`)
        .lt('voted_at', `${today}T23:59:59Z`);

      if (todayVotes && todayVotes.length >= levelConfig.dailyQuota) {
        return { 
          eligible: false, 
          reason: `今日裁定次数已达上限(${levelConfig.dailyQuota}次)` 
        };
      }
    }

    return { eligible: true, userInfo };
  } catch (error) {
    console.error('验证裁定资格失败:', error);
    // 开发环境返回模拟验证结果
    return { 
      eligible: true, 
      userInfo: {
        current_score: 90,
        certification_level: 'X2',
        total_judgments: 10,
        correct_judgments: 8
      }
    };
  }
}

// 检查是否已投票
async function checkExistingVote(userId: string, betId: string, round: number) {
  try {
    const { data: existingVote } = await supabase
      .from('bet_judgments')
      .select('id')
      .eq('bet_id', betId)
      .eq('judge_id', userId)
      .eq('round_number', round)
      .single();

    return existingVote;
  } catch (error) {
    // 开发环境：假设没有重复投票
    return null;
  }
}

// 提交投票
async function submitVote(userId: string, betId: string, round: number, voteOption: number, userInfo: any) {
  try {
    const voteData = {
      bet_id: betId,
      judge_id: userId,
      round_number: round,
      vote_option: voteOption,
      reputation_score: userInfo.current_score,
      certification_level: userInfo.certification_level,
      voted_at: new Date().toISOString()
    };

    const { data: vote, error } = await supabase
      .from('bet_judgments')
      .insert(voteData)
      .select()
      .single();

    if (error) throw error;

    return vote;
  } catch (error) {
    console.error('提交投票失败:', error);
    // 开发环境返回模拟结果
    return {
      id: `mock-vote-${Date.now()}`,
      bet_id: betId,
      judge_id: userId,
      round_number: round,
      vote_option: voteOption,
      voted_at: new Date().toISOString()
    };
  }
}

// 检查投票阈值
async function checkVotingThreshold(betId: string, round: number) {
  try {
    const threshold = VOTING_THRESHOLDS[round];
    if (!threshold) {
      throw new Error(`无效的轮次: ${round}`);
    }

    // 获取当前轮次的所有投票
    const { data: votes } = await supabase
      .from('bet_judgments')
      .select('vote_option')
      .eq('bet_id', betId)
      .eq('round_number', round);

    const currentVotes = votes?.length || 0;
    
    // 统计各选项的票数
    const voteCount = {};
    votes?.forEach(vote => {
      voteCount[vote.vote_option] = (voteCount[vote.vote_option] || 0) + 1;
    });

    // 检查是否有选项达到阈值
    let winningOption = null;
    let maxVotes = 0;
    
    for (const [option, count] of Object.entries(voteCount)) {
      if (count >= threshold.required) {
        if (count > maxVotes) {
          maxVotes = count;
          winningOption = parseInt(option);
        }
      }
    }

    return {
      thresholdMet: winningOption !== null,
      winningOption,
      currentVotes,
      requiredVotes: threshold.required,
      totalSlots: threshold.total,
      voteDistribution: voteCount
    };
  } catch (error) {
    console.error('检查投票阈值失败:', error);
    // 开发环境返回模拟结果
    return {
      thresholdMet: false,
      winningOption: null,
      currentVotes: 1,
      requiredVotes: VOTING_THRESHOLDS[round].required,
      totalSlots: VOTING_THRESHOLDS[round].total,
      voteDistribution: { [round]: 1 }
    };
  }
}

// 处理轮次结果
async function processRoundResult(betId: string, round: number, thresholdResult: any) {
  try {
    if (round === 3) {
      // 第三轮结束，最终裁定
      await finalizeBetResult(betId, thresholdResult.winningOption);
    } else {
      // 进入下一轮裁定
      await startNextRound(betId, round + 1);
    }
  } catch (error) {
    console.error('处理轮次结果失败:', error);
  }
}

// 最终裁定结果
async function finalizeBetResult(betId: string, winningOption: number) {
  try {
    const { error } = await supabase
      .from('social_bets')
      .update({
        status: 'confirming',
        winning_option: winningOption,
        judging_completed_at: new Date().toISOString(),
        confirmation_deadline: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24小时确认期
      })
      .eq('id', betId);

    if (error) throw error;

    console.log(`赌约 ${betId} 裁定完成，获胜选项: ${winningOption}`);
  } catch (error) {
    console.error('最终裁定失败:', error);
  }
}

// 开始下一轮裁定
async function startNextRound(betId: string, nextRound: number) {
  try {
    const deadline = new Date();
    deadline.setHours(deadline.getHours() + (nextRound === 2 ? 12 : 6)); // 第二轮12小时，第三轮6小时

    const { error } = await supabase
      .from('social_bets')
      .update({
        current_round: nextRound,
        round_deadline: deadline.toISOString()
      })
      .eq('id', betId);

    if (error) throw error;

    console.log(`赌约 ${betId} 进入第${nextRound}轮裁定`);
  } catch (error) {
    console.error('开始下一轮失败:', error);
  }
}
