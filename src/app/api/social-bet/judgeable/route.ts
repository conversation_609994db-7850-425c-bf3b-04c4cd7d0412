import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

// 认证等级配置
const CERTIFICATION_LEVELS = {
  X1: { rounds: [1] },
  X2: { rounds: [1, 2] },
  X3: { rounds: [1, 2] },
  X4: { rounds: [2, 3] },
  X5: { rounds: [2, 3] }
};

export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const userId = url.searchParams.get('userId');
    const round = parseInt(url.searchParams.get('round') || '1');
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '20');

    if (!userId) {
      return NextResponse.json(
        { success: false, message: '用户ID必需' },
        { status: 400 }
      );
    }

    // 获取用户认证信息
    const userInfo = await getUserCertificationInfo(userId);
    if (!userInfo) {
      return NextResponse.json(
        { success: false, message: '用户信息不存在' },
        { status: 404 }
      );
    }

    // 检查用户是否有权限参与指定轮次
    const levelConfig = CERTIFICATION_LEVELS[userInfo.certification_level];
    if (!levelConfig || !levelConfig.rounds.includes(round)) {
      return NextResponse.json({
        success: true,
        data: {
          bets: [],
          pagination: {
            page,
            limit,
            total: 0,
            totalPages: 0
          },
          userInfo,
          message: `${userInfo.certification_level}认证等级无权参与第${round}轮裁定`
        }
      });
    }

    // 获取可裁定的赌约列表
    const judgeableBets = await getJudgeableBets(userId, round, page, limit);

    return NextResponse.json({
      success: true,
      data: {
        bets: judgeableBets.bets,
        pagination: judgeableBets.pagination,
        userInfo,
        round
      }
    });

  } catch (error) {
    console.error('获取可裁定赌约失败:', error);
    return NextResponse.json(
      { success: false, message: '获取可裁定赌约失败' },
      { status: 500 }
    );
  }
}

// 获取用户认证信息
async function getUserCertificationInfo(userId: string) {
  try {
    const { data: userInfo } = await supabase
      .from('user_reputation')
      .select(`
        current_score,
        certification_level,
        daily_judgments_used,
        total_judgments,
        correct_judgments
      `)
      .eq('user_id', userId)
      .single();

    return userInfo;
  } catch (error) {
    console.error('获取用户认证信息失败:', error);
    // 开发环境返回模拟数据
    return {
      current_score: 90,
      certification_level: 'X2',
      daily_judgments_used: 2,
      total_judgments: 15,
      correct_judgments: 12
    };
  }
}

// 获取可裁定的赌约列表
async function getJudgeableBets(userId: string, round: number, page: number, limit: number) {
  try {
    const offset = (page - 1) * limit;

    // 构建查询条件
    let query = supabase
      .from('social_bets')
      .select(`
        id,
        title,
        description,
        options,
        status,
        current_round,
        round_deadline,
        judging_started_at,
        total_fortune_pool,
        creator_id
      `, { count: 'exact' })
      .eq('status', 'judging')
      .eq('current_round', round)
      .gt('round_deadline', new Date().toISOString()) // 未过期
      .range(offset, offset + limit - 1)
      .order('judging_started_at', { ascending: false });

    const { data: bets, count, error } = await query;

    if (error) throw error;

    // 过滤掉用户已参与的赌约和已投票的赌约
    const filteredBets = [];
    
    for (const bet of bets || []) {
      // 检查是否为参与者
      const { data: participation } = await supabase
        .from('bet_participants')
        .select('id')
        .eq('bet_id', bet.id)
        .eq('user_id', userId)
        .single();

      if (participation) continue; // 跳过已参与的赌约

      // 检查是否已在当前轮投票
      const { data: existingVote } = await supabase
        .from('bet_judgments')
        .select('id')
        .eq('bet_id', bet.id)
        .eq('judge_id', userId)
        .eq('round_number', round)
        .single();

      if (existingVote) continue; // 跳过已投票的赌约

      // 获取当前轮次的投票统计
      const votingStats = await getCurrentRoundVotingStats(bet.id, round);

      filteredBets.push({
        ...bet,
        votingStats
      });
    }

    return {
      bets: filteredBets,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    };

  } catch (error) {
    console.error('获取可裁定赌约失败:', error);
    // 开发环境返回模拟数据
    return {
      bets: [
        {
          id: 'mock-bet-1',
          title: '2024年世界杯冠军预测',
          description: '预测2024年世界杯的冠军队伍',
          options: ['巴西', '阿根廷', '法国'],
          status: 'judging',
          current_round: round,
          round_deadline: new Date(Date.now() + 12 * 60 * 60 * 1000).toISOString(),
          judging_started_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          total_fortune_pool: 50000,
          creator_id: 'creator-1',
          votingStats: {
            currentVotes: 3,
            requiredVotes: round === 1 ? 11 : round === 2 ? 6 : 3,
            totalSlots: round === 1 ? 20 : round === 2 ? 10 : 5,
            voteDistribution: { 0: 2, 1: 1 },
            timeRemaining: 12 * 60 * 60 * 1000
          }
        },
        {
          id: 'mock-bet-2',
          title: 'Bitcoin价格预测',
          description: '预测比特币在月底的价格走势',
          options: ['上涨', '下跌'],
          status: 'judging',
          current_round: round,
          round_deadline: new Date(Date.now() + 6 * 60 * 60 * 1000).toISOString(),
          judging_started_at: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
          total_fortune_pool: 20000,
          creator_id: 'creator-2',
          votingStats: {
            currentVotes: 1,
            requiredVotes: round === 1 ? 11 : round === 2 ? 6 : 3,
            totalSlots: round === 1 ? 20 : round === 2 ? 10 : 5,
            voteDistribution: { 0: 1 },
            timeRemaining: 6 * 60 * 60 * 1000
          }
        }
      ],
      pagination: {
        page,
        limit,
        total: 2,
        totalPages: 1
      }
    };
  }
}

// 获取当前轮次投票统计
async function getCurrentRoundVotingStats(betId: string, round: number) {
  try {
    // 投票阈值配置
    const thresholds = {
      1: { required: 11, total: 20 },
      2: { required: 6, total: 10 },
      3: { required: 3, total: 5 }
    };

    const threshold = thresholds[round];
    
    // 获取当前轮次的投票
    const { data: votes } = await supabase
      .from('bet_judgments')
      .select('vote_option, voted_at')
      .eq('bet_id', betId)
      .eq('round_number', round);

    const currentVotes = votes?.length || 0;
    
    // 统计各选项票数
    const voteDistribution = {};
    votes?.forEach(vote => {
      voteDistribution[vote.vote_option] = (voteDistribution[vote.vote_option] || 0) + 1;
    });

    // 获取赌约的轮次截止时间
    const { data: bet } = await supabase
      .from('social_bets')
      .select('round_deadline')
      .eq('id', betId)
      .single();

    const timeRemaining = bet?.round_deadline 
      ? new Date(bet.round_deadline).getTime() - Date.now()
      : 0;

    return {
      currentVotes,
      requiredVotes: threshold.required,
      totalSlots: threshold.total,
      voteDistribution,
      timeRemaining: Math.max(0, timeRemaining),
      progress: Math.min((currentVotes / threshold.required) * 100, 100)
    };
  } catch (error) {
    console.error('获取投票统计失败:', error);
    return {
      currentVotes: 0,
      requiredVotes: round === 1 ? 11 : round === 2 ? 6 : 3,
      totalSlots: round === 1 ? 20 : round === 2 ? 10 : 5,
      voteDistribution: {},
      timeRemaining: 12 * 60 * 60 * 1000,
      progress: 0
    };
  }
}
