/**
 * 认证等级权益API
 * 处理不同等级的手续费折扣、裁定权限、每日限额等权益
 */

import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { CertificationService } from '@/services/socialbet/CertificationService';

/**
 * @api {GET} /api/social-bet/certification/benefits 获取用户权益信息
 * @apiDescription 获取用户当前等级的所有权益和使用情况
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // 验证用户身份
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // 获取用户认证信息
    const certification = await CertificationService.getUserCertification(user.id);

    // 获取用户今日使用情况
    const { data: reputation } = await supabase
      .from('user_reputation')
      .select('daily_judgment_count, last_judgment_date')
      .eq('user_id', user.id)
      .single();

    const today = new Date().toISOString().split('T')[0];
    const todayJudgmentCount = reputation?.last_judgment_date === today 
      ? reputation.daily_judgment_count 
      : 0;

    // 获取本月手续费节省统计
    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    startOfMonth.setHours(0, 0, 0, 0);

    const { data: monthlyTransactions } = await supabase
      .from('fortune_transactions')
      .select('amount, description')
      .eq('user_id', user.id)
      .eq('transaction_type', 'bet_participation')
      .gte('created_at', startOfMonth.toISOString());

    // 计算手续费节省
    const totalBetAmount = monthlyTransactions?.reduce((sum, tx) => sum + Math.abs(tx.amount), 0) || 0;
    const standardFee = totalBetAmount * 0.05; // 标准5%手续费
    const actualFee = totalBetAmount * (0.05 - certification.levelInfo.benefits.feeDiscount);
    const savedAmount = standardFee - actualFee;

    // 获取特殊功能使用统计
    const specialFeatures = certification.levelInfo.benefits.specialFeatures;
    const featureUsage = await this.getFeatureUsage(user.id, specialFeatures);

    return NextResponse.json({
      success: true,
      data: {
        // 当前等级信息
        currentLevel: certification.levelInfo,
        
        // 权益使用情况
        benefitUsage: {
          // 手续费折扣
          feeDiscount: {
            rate: certification.levelInfo.benefits.feeDiscount,
            monthlySaved: Math.round(savedAmount * 100) / 100,
            description: `本月已节省${Math.round(savedAmount)}福气手续费`
          },
          
          // 每日裁定限额
          dailyJudgment: {
            limit: certification.levelInfo.benefits.dailyJudgmentLimit,
            used: todayJudgmentCount,
            remaining: Math.max(0, certification.levelInfo.benefits.dailyJudgmentLimit - todayJudgmentCount),
            resetTime: this.getNextDayResetTime()
          },
          
          // 特殊功能
          specialFeatures: specialFeatures.map(feature => ({
            name: feature,
            displayName: this.getFeatureDisplayName(feature),
            description: this.getFeatureDescription(feature),
            usage: featureUsage[feature] || 0,
            available: true
          }))
        },
        
        // 升级信息
        upgradeInfo: certification.nextLevelInfo ? {
          nextLevel: certification.nextLevelInfo,
          benefits: this.compareBenefits(certification.levelInfo.benefits, certification.nextLevelInfo.benefits),
          requiredHaox: certification.nextLevelInfo.minHaox - certification.haoxBalance,
          progress: certification.haoxBalance / certification.nextLevelInfo.minHaox
        } : null
      }
    });

  } catch (error) {
    console.error('Get benefits error:', error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Internal server error',
        code: 'GET_BENEFITS_ERROR'
      },
      { status: 500 }
    );
  }
}

/**
 * @api {GET} /api/social-bet/certification/benefits/stats 获取权益统计
 * @apiDescription 获取所有等级的权益对比和统计信息
 */
export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // 验证用户身份（可选，游客也可以查看）
    const { data: { user } } = await supabase.auth.getUser();

    // 获取所有等级信息
    const allLevels = CertificationService.getAllLevels();

    // 获取认证统计
    const certificationStats = await CertificationService.getCertificationStats();

    // 计算权益对比
    const benefitComparison = allLevels.map(level => ({
      level: level.level,
      name: level.name,
      color: level.color,
      requirements: {
        minHaox: level.minHaox,
        maxHaox: level.maxHaox === Infinity ? '无上限' : level.maxHaox
      },
      benefits: {
        feeDiscount: `${(level.benefits.feeDiscount * 100).toFixed(0)}%`,
        dailyJudgmentLimit: level.benefits.dailyJudgmentLimit,
        specialFeatures: level.benefits.specialFeatures.length,
        description: level.benefits.description
      },
      userCount: certificationStats?.levelDistribution.find(d => d.level === level.level)?.count || 0,
      userPercentage: certificationStats?.levelDistribution.find(d => d.level === level.level)?.percentage || 0
    }));

    return NextResponse.json({
      success: true,
      data: {
        levelComparison: benefitComparison,
        statistics: certificationStats,
        totalUsers: certificationStats?.totalUsers || 0,
        
        // 权益亮点
        highlights: [
          {
            title: '手续费折扣',
            description: '最高可享受25%手续费折扣',
            maxBenefit: '25%',
            icon: 'discount'
          },
          {
            title: '裁定权限',
            description: '钻石用户每日可裁定30次',
            maxBenefit: '30次/日',
            icon: 'judgment'
          },
          {
            title: '专属功能',
            description: '高等级用户享受定制功能',
            maxBenefit: '5项特权',
            icon: 'features'
          }
        ]
      }
    });

  } catch (error) {
    console.error('Get benefits stats error:', error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Internal server error',
        code: 'GET_BENEFITS_STATS_ERROR'
      },
      { status: 500 }
    );
  }
}

/**
 * 获取功能使用统计
 */
async function getFeatureUsage(userId: string, features: string[]): Promise<Record<string, number>> {
  // 这里应该根据具体功能统计使用次数
  // 目前返回模拟数据
  const usage: Record<string, number> = {};
  
  features.forEach(feature => {
    usage[feature] = Math.floor(Math.random() * 10);
  });
  
  return usage;
}

/**
 * 获取功能显示名称
 */
function getFeatureDisplayName(feature: string): string {
  const names: Record<string, string> = {
    'priority_support': '优先客服',
    'advanced_analytics': '高级分析',
    'exclusive_bets': '专属赌约',
    'vip_events': 'VIP活动',
    'custom_features': '定制功能'
  };
  
  return names[feature] || feature;
}

/**
 * 获取功能描述
 */
function getFeatureDescription(feature: string): string {
  const descriptions: Record<string, string> = {
    'priority_support': '享受优先客服支持，快速解决问题',
    'advanced_analytics': '查看详细的投注和收益分析报告',
    'exclusive_bets': '参与高等级用户专属的特殊赌约',
    'vip_events': '受邀参加VIP专属活动和比赛',
    'custom_features': '享受个性化定制功能和界面'
  };
  
  return descriptions[feature] || '特殊功能权限';
}

/**
 * 对比权益差异
 */
function compareBenefits(current: any, next: any): any {
  return {
    feeDiscountIncrease: next.feeDiscount - current.feeDiscount,
    judgmentLimitIncrease: next.dailyJudgmentLimit - current.dailyJudgmentLimit,
    newFeatures: next.specialFeatures.filter((f: string) => !current.specialFeatures.includes(f))
  };
}

/**
 * 获取下一天重置时间
 */
function getNextDayResetTime(): string {
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);
  tomorrow.setHours(0, 0, 0, 0);
  return tomorrow.toISOString();
}

// 配置运行时
export const runtime = 'nodejs';
