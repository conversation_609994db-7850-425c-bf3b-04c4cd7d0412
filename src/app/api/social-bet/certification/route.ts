/**
 * 认证等级API
 * 处理用户HAOX持币认证和等级管理
 */

import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { CertificationService } from '@/services/socialbet/CertificationService';

/**
 * @api {GET} /api/social-bet/certification 获取用户认证信息
 * @apiDescription 获取用户当前认证等级和权益信息
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // 验证用户身份
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const targetUserId = searchParams.get('userId');
    const userId = targetUserId || user.id;

    // 如果查询其他用户，检查权限
    if (targetUserId && targetUserId !== user.id) {
      // 这里可以添加管理员权限检查
      // 目前允许查看其他用户的公开认证信息
    }

    // 获取用户认证信息
    const certification = await CertificationService.getUserCertification(userId);

    // 获取所有等级信息（用于展示升级路径）
    const allLevels = CertificationService.getAllLevels();

    return NextResponse.json({
      success: true,
      data: {
        userCertification: certification,
        allLevels,
        upgradeInfo: certification.nextLevelInfo ? {
          nextLevel: certification.nextLevelInfo,
          requiredHaox: certification.nextLevelInfo.minHaox - certification.haoxBalance,
          canUpgrade: certification.canUpgrade
        } : null
      }
    });

  } catch (error) {
    console.error('Get certification error:', error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Internal server error',
        code: 'GET_CERTIFICATION_ERROR'
      },
      { status: 500 }
    );
  }
}

/**
 * @api {POST} /api/social-bet/certification/update 手动更新认证等级
 * @apiDescription 手动触发用户认证等级更新
 */
export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // 验证用户身份
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // 更新用户认证等级
    const updatedReputation = await CertificationService.updateUserBalance(user.id);
    const certification = await CertificationService.getUserCertification(user.id);

    return NextResponse.json({
      success: true,
      message: '认证等级更新成功！',
      data: {
        previousLevel: updatedReputation.certification_level,
        currentCertification: certification,
        updated: true,
        updateTime: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Update certification error:', error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Internal server error',
        code: 'UPDATE_CERTIFICATION_ERROR'
      },
      { status: 500 }
    );
  }
}

// 配置运行时
export const runtime = 'nodejs';
