/**
 * Social Bet参与API
 * 处理用户参与赌约投注
 */

import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { SocialBetService } from '@/services/socialbet/SocialBetService';
import { rateLimitMiddleware } from '@/middleware/rateLimiter';

/**
 * @api {POST} /api/social-bet/participate 参与赌约
 * @apiDescription 用户参与赌约投注，锁定福气
 */
export async function POST(request: NextRequest) {
  try {
    // 应用频率限制
    const rateLimitResponse = await rateLimitMiddleware(request, '/api/social-bet/participate');
    if (rateLimitResponse) {
      return rateLimitResponse;
    }

    const supabase = createRouteHandlerClient({ cookies });
    
    // 验证用户身份
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { betId, selectedOption, betAmount, referrerId } = await request.json();

    // 验证必需参数
    if (!betId || !selectedOption || !betAmount) {
      return NextResponse.json({ 
        error: 'Missing required parameters: betId, selectedOption, betAmount' 
      }, { status: 400 });
    }

    // 验证投注金额
    if (betAmount <= 0) {
      return NextResponse.json({ 
        error: 'Bet amount must be positive' 
      }, { status: 400 });
    }

    // 验证转发人（如果提供）
    if (referrerId) {
      if (referrerId === user.id) {
        return NextResponse.json({ 
          error: 'Cannot refer yourself' 
        }, { status: 400 });
      }

      // 验证转发人是否存在
      const { data: referrer } = await supabase
        .from('users')
        .select('id')
        .eq('id', referrerId)
        .single();

      if (!referrer) {
        return NextResponse.json({ 
          error: 'Invalid referrer' 
        }, { status: 400 });
      }
    }

    // 参与赌约
    const participation = await SocialBetService.participateInBet(betId, user.id, {
      selectedOption,
      betAmount,
      referrerId
    });

    // 获取更新后的赌约信息
    const bet = await SocialBetService.getBetById(betId);

    return NextResponse.json({
      success: true,
      message: `成功参与赌约！已投注${betAmount}福气`,
      data: {
        participationId: participation.id,
        betId: betId,
        selectedOption: selectedOption,
        betAmount: betAmount,
        status: participation.status,
        bet: {
          title: bet?.title,
          totalPool: bet?.totalPool,
          participantCount: bet?.participantCount,
          bettingDeadline: bet?.bettingDeadline
        },
        createdAt: participation.createdAt
      }
    });

  } catch (error) {
    console.error('Participate in bet error:', error);
    
    // 处理特定错误
    if (error instanceof Error) {
      if (error.message.includes('福气余额不足')) {
        return NextResponse.json({
          error: error.message,
          code: 'INSUFFICIENT_FORTUNE'
        }, { status: 400 });
      }
      
      if (error.message.includes('已经参与过')) {
        return NextResponse.json({
          error: error.message,
          code: 'ALREADY_PARTICIPATED'
        }, { status: 400 });
      }
      
      if (error.message.includes('已关闭') || error.message.includes('已截止')) {
        return NextResponse.json({
          error: error.message,
          code: 'BET_CLOSED'
        }, { status: 400 });
      }
      
      if (error.message.includes('已满员')) {
        return NextResponse.json({
          error: error.message,
          code: 'BET_FULL'
        }, { status: 400 });
      }
      
      if (error.message.includes('仅限指定用户')) {
        return NextResponse.json({
          error: error.message,
          code: 'RESTRICTED_BET'
        }, { status: 403 });
      }
    }

    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Internal server error',
        code: 'PARTICIPATE_ERROR'
      },
      { status: 500 }
    );
  }
}

/**
 * @api {GET} /api/social-bet/participate/my-bets 获取我的投注
 * @apiDescription 获取用户参与的所有赌约投注记录
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // 验证用户身份
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status'); // 'active', 'settled', 'all'
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = (page - 1) * limit;

    // 构建查询
    let query = supabase
      .from('bet_participants')
      .select(`
        *,
        social_bets (
          id,
          title,
          description,
          category,
          bet_type,
          status,
          betting_deadline,
          result_deadline,
          winning_option,
          total_pool,
          participant_count,
          created_at
        )
      `, { count: 'exact' })
      .eq('user_id', user.id);

    // 状态筛选
    if (status && status !== 'all') {
      query = query.eq('status', status);
    }

    // 排序和分页
    query = query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    const { data: participations, error, count } = await query;

    if (error) {
      throw new Error(`Failed to fetch participations: ${error.message}`);
    }

    // 计算统计信息
    const { data: stats } = await supabase
      .from('bet_participants')
      .select('status, bet_amount, payout_amount, is_winner')
      .eq('user_id', user.id);

    const totalParticipations = stats?.length || 0;
    const totalBetAmount = stats?.reduce((sum, p) => sum + p.bet_amount, 0) || 0;
    const totalPayoutAmount = stats?.reduce((sum, p) => sum + (p.payout_amount || 0), 0) || 0;
    const winCount = stats?.filter(p => p.is_winner === true).length || 0;
    const loseCount = stats?.filter(p => p.is_winner === false).length || 0;
    const pendingCount = stats?.filter(p => p.is_winner === null).length || 0;

    return NextResponse.json({
      success: true,
      data: {
        participations: participations?.map(p => ({
          id: p.id,
          betId: p.bet_id,
          selectedOption: p.selected_option,
          betAmount: p.bet_amount,
          status: p.status,
          payoutAmount: p.payout_amount,
          isWinner: p.is_winner,
          referralReward: p.referral_reward,
          createdAt: p.created_at,
          bet: p.social_bets ? {
            id: p.social_bets.id,
            title: p.social_bets.title,
            description: p.social_bets.description,
            category: p.social_bets.category,
            betType: p.social_bets.bet_type,
            status: p.social_bets.status,
            bettingDeadline: p.social_bets.betting_deadline,
            resultDeadline: p.social_bets.result_deadline,
            winningOption: p.social_bets.winning_option,
            totalPool: p.social_bets.total_pool,
            participantCount: p.social_bets.participant_count,
            createdAt: p.social_bets.created_at
          } : null
        })) || [],
        
        pagination: {
          page,
          limit,
          total: count || 0,
          totalPages: Math.ceil((count || 0) / limit)
        },
        
        statistics: {
          totalParticipations,
          totalBetAmount,
          totalPayoutAmount,
          netProfit: totalPayoutAmount - totalBetAmount,
          winCount,
          loseCount,
          pendingCount,
          winRate: totalParticipations > 0 ? (winCount / (winCount + loseCount)) * 100 : 0
        }
      }
    });

  } catch (error) {
    console.error('Get my bets error:', error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Internal server error',
        code: 'FETCH_MY_BETS_ERROR'
      },
      { status: 500 }
    );
  }
}

// 配置运行时
export const runtime = 'nodejs';
