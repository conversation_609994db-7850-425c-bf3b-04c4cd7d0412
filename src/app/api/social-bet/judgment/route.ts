/**
 * Social Bet裁定API
 * 处理三轮DAO裁定机制
 */

import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { JudgmentService } from '@/services/socialbet/JudgmentService';

/**
 * @api {POST} /api/social-bet/judgment 提交裁定投票
 * @apiDescription 裁判提交对赌约的裁定投票
 */
export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // 验证用户身份
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { betId, selectedOption, confidenceLevel, reasoning } = await request.json();

    // 验证必需参数
    if (!betId || !selectedOption || !confidenceLevel) {
      return NextResponse.json({ 
        error: 'Missing required parameters: betId, selectedOption, confidenceLevel' 
      }, { status: 400 });
    }

    // 验证信心等级
    if (confidenceLevel < 1 || confidenceLevel > 5) {
      return NextResponse.json({ 
        error: 'Confidence level must be between 1 and 5' 
      }, { status: 400 });
    }

    // 获取赌约当前轮次
    const { data: bet } = await supabase
      .from('social_bets')
      .select('current_judgment_round, status')
      .eq('id', betId)
      .single();

    if (!bet) {
      return NextResponse.json({ error: 'Bet not found' }, { status: 404 });
    }

    if (bet.status !== 'judging') {
      return NextResponse.json({ error: 'Bet is not in judging status' }, { status: 400 });
    }

    // 提交裁定投票
    const judgmentId = await JudgmentService.submitJudgment({
      betId,
      judgeId: user.id,
      round: bet.current_judgment_round,
      selectedOption,
      confidenceLevel,
      reasoning
    });

    // 获取当前轮次统计
    const stats = await JudgmentService.getJudgmentStats(betId);

    return NextResponse.json({
      success: true,
      message: `第${bet.current_judgment_round}轮裁定投票提交成功！`,
      data: {
        judgmentId,
        betId,
        round: bet.current_judgment_round,
        selectedOption,
        confidenceLevel,
        rewardAmount: 5, // 基础奖励
        stats: stats?.roundStats?.find((r: any) => r.round === bet.current_judgment_round)
      }
    });

  } catch (error) {
    console.error('Submit judgment error:', error);
    
    // 处理特定错误
    if (error instanceof Error) {
      if (error.message.includes('认证')) {
        return NextResponse.json({
          error: error.message,
          code: 'INSUFFICIENT_CERTIFICATION'
        }, { status: 403 });
      }
      
      if (error.message.includes('次数已达上限')) {
        return NextResponse.json({
          error: error.message,
          code: 'DAILY_LIMIT_EXCEEDED'
        }, { status: 429 });
      }
      
      if (error.message.includes('已在此轮投票')) {
        return NextResponse.json({
          error: error.message,
          code: 'ALREADY_VOTED'
        }, { status: 400 });
      }
      
      if (error.message.includes('不能裁定自己参与的赌约')) {
        return NextResponse.json({
          error: error.message,
          code: 'CONFLICT_OF_INTEREST'
        }, { status: 403 });
      }
    }

    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Internal server error',
        code: 'JUDGMENT_ERROR'
      },
      { status: 500 }
    );
  }
}

/**
 * @api {GET} /api/social-bet/judgment/eligibility 检查裁定资格
 * @apiDescription 检查用户是否有资格参与指定赌约的裁定
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // 验证用户身份
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const betId = searchParams.get('betId');

    if (!betId) {
      return NextResponse.json({ 
        error: 'Missing required parameter: betId' 
      }, { status: 400 });
    }

    // 获取赌约信息
    const { data: bet } = await supabase
      .from('social_bets')
      .select('*')
      .eq('id', betId)
      .single();

    if (!bet) {
      return NextResponse.json({ error: 'Bet not found' }, { status: 404 });
    }

    // 检查裁定资格
    const eligibility = await JudgmentService.checkJudgmentEligibility(
      user.id,
      betId,
      bet.current_judgment_round
    );

    // 获取用户信誉信息
    const { data: reputation } = await supabase
      .from('user_reputation')
      .select('*')
      .eq('user_id', user.id)
      .single();

    // 获取当前轮次统计
    const stats = await JudgmentService.getJudgmentStats(betId);

    return NextResponse.json({
      success: true,
      data: {
        eligible: eligibility.eligible,
        reason: eligibility.reason,
        bet: {
          id: bet.id,
          title: bet.title,
          status: bet.status,
          currentRound: bet.current_judgment_round,
          options: bet.options
        },
        userReputation: reputation ? {
          certificationLevel: reputation.certification_level,
          reputationScore: reputation.reputation_score,
          accuracyRate: reputation.accuracy_rate,
          dailyJudgmentCount: reputation.daily_judgment_count,
          dailyLimit: reputation.daily_judgment_limit,
          currentStreak: reputation.current_streak
        } : null,
        judgmentStats: stats
      }
    });

  } catch (error) {
    console.error('Check judgment eligibility error:', error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Internal server error',
        code: 'ELIGIBILITY_CHECK_ERROR'
      },
      { status: 500 }
    );
  }
}
