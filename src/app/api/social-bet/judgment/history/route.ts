/**
 * Social Bet裁定历史API
 * 获取用户裁定历史和统计信息
 */

import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

/**
 * @api {GET} /api/social-bet/judgment/history 获取裁定历史
 * @apiDescription 获取用户的裁定历史记录和统计信息
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // 验证用户身份
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100);
    const status = searchParams.get('status'); // 'correct', 'incorrect', 'pending'
    const offset = (page - 1) * limit;

    // 获取用户信誉信息
    const { data: reputation } = await supabase
      .from('user_reputation')
      .select('*')
      .eq('user_id', user.id)
      .single();

    // 构建裁定历史查询
    let query = supabase
      .from('bet_judgments')
      .select(`
        *,
        social_bets (
          id,
          title,
          description,
          category,
          status,
          winning_option,
          total_pool,
          created_at
        )
      `, { count: 'exact' })
      .eq('judge_id', user.id);

    // 状态筛选
    if (status === 'correct') {
      query = query.eq('is_correct_judgment', true);
    } else if (status === 'incorrect') {
      query = query.eq('is_correct_judgment', false);
    } else if (status === 'pending') {
      query = query.is('is_correct_judgment', null);
    }

    // 排序和分页
    query = query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    const { data: judgments, error, count } = await query;

    if (error) {
      throw new Error(`Failed to fetch judgment history: ${error.message}`);
    }

    // 计算统计信息
    const { data: allJudgments } = await supabase
      .from('bet_judgments')
      .select('is_correct_judgment, reward_amount, judgment_round')
      .eq('judge_id', user.id);

    const totalJudgments = allJudgments?.length || 0;
    const correctJudgments = allJudgments?.filter(j => j.is_correct_judgment === true).length || 0;
    const incorrectJudgments = allJudgments?.filter(j => j.is_correct_judgment === false).length || 0;
    const pendingJudgments = allJudgments?.filter(j => j.is_correct_judgment === null).length || 0;
    const totalRewards = allJudgments?.reduce((sum, j) => sum + (j.reward_amount || 0), 0) || 0;

    // 按轮次统计
    const roundStats = allJudgments?.reduce((stats, j) => {
      const round = j.judgment_round;
      if (!stats[round]) {
        stats[round] = { total: 0, correct: 0, incorrect: 0, pending: 0 };
      }
      stats[round].total++;
      if (j.is_correct_judgment === true) stats[round].correct++;
      else if (j.is_correct_judgment === false) stats[round].incorrect++;
      else stats[round].pending++;
      return stats;
    }, {} as Record<number, any>) || {};

    // 最近30天的裁定活动
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();
    const { data: recentJudgments } = await supabase
      .from('bet_judgments')
      .select('created_at, is_correct_judgment')
      .eq('judge_id', user.id)
      .gte('created_at', thirtyDaysAgo)
      .order('created_at');

    // 按日期分组统计最近活动
    const dailyActivity = recentJudgments?.reduce((activity, j) => {
      const date = j.created_at.split('T')[0];
      if (!activity[date]) {
        activity[date] = { total: 0, correct: 0, incorrect: 0 };
      }
      activity[date].total++;
      if (j.is_correct_judgment === true) activity[date].correct++;
      else if (j.is_correct_judgment === false) activity[date].incorrect++;
      return activity;
    }, {} as Record<string, any>) || {};

    return NextResponse.json({
      success: true,
      data: {
        // 裁定历史记录
        judgments: judgments?.map(j => ({
          id: j.id,
          betId: j.bet_id,
          round: j.judgment_round,
          selectedOption: j.selected_option,
          confidenceLevel: j.confidence_level,
          reasoning: j.reasoning,
          rewardAmount: j.reward_amount,
          isCorrect: j.is_correct_judgment,
          createdAt: j.created_at,
          bet: j.social_bets ? {
            id: j.social_bets.id,
            title: j.social_bets.title,
            description: j.social_bets.description,
            category: j.social_bets.category,
            status: j.social_bets.status,
            winningOption: j.social_bets.winning_option,
            totalPool: j.social_bets.total_pool,
            createdAt: j.social_bets.created_at
          } : null
        })) || [],

        // 分页信息
        pagination: {
          page,
          limit,
          total: count || 0,
          totalPages: Math.ceil((count || 0) / limit)
        },

        // 用户信誉信息
        reputation: reputation ? {
          certificationLevel: reputation.certification_level,
          reputationScore: reputation.reputation_score,
          totalJudgments: reputation.total_judgments,
          correctJudgments: reputation.correct_judgments,
          accuracyRate: reputation.accuracy_rate,
          currentStreak: reputation.current_streak,
          bestStreak: reputation.best_streak,
          dailyJudgmentCount: reputation.daily_judgment_count,
          dailyLimit: reputation.daily_judgment_limit,
          feeDiscountRate: reputation.fee_discount_rate
        } : null,

        // 统计信息
        statistics: {
          total: totalJudgments,
          correct: correctJudgments,
          incorrect: incorrectJudgments,
          pending: pendingJudgments,
          accuracyRate: totalJudgments > 0 ? (correctJudgments / (correctJudgments + incorrectJudgments)) * 100 : 0,
          totalRewards,
          averageReward: totalJudgments > 0 ? totalRewards / totalJudgments : 0,
          roundStats: Object.entries(roundStats).map(([round, stats]) => ({
            round: parseInt(round),
            ...stats,
            accuracyRate: stats.total > 0 ? (stats.correct / (stats.correct + stats.incorrect)) * 100 : 0
          }))
        },

        // 最近活动
        recentActivity: {
          dailyActivity: Object.entries(dailyActivity).map(([date, activity]) => ({
            date,
            ...activity,
            accuracyRate: activity.total > 0 ? (activity.correct / (activity.correct + activity.incorrect)) * 100 : 0
          })).sort((a, b) => a.date.localeCompare(b.date)),
          totalRecentJudgments: recentJudgments?.length || 0
        }
      }
    });

  } catch (error) {
    console.error('Get judgment history error:', error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Internal server error',
        code: 'JUDGMENT_HISTORY_ERROR'
      },
      { status: 500 }
    );
  }
}

// 配置运行时
export const runtime = 'nodejs';
