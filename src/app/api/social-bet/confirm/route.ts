/**
 * Social Bet确认API
 * 处理赌约结果确认和争议
 */

import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { ConfirmationService } from '@/services/socialbet/ConfirmationService';

/**
 * @api {POST} /api/social-bet/confirm 确认赌约结果
 * @apiDescription 创建者或参与者确认赌约裁定结果
 */
export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // 验证用户身份
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { betId, confirmed, disputeReason } = await request.json();

    // 验证必需参数
    if (!betId || confirmed === undefined) {
      return NextResponse.json({ 
        error: 'Missing required parameters: betId, confirmed' 
      }, { status: 400 });
    }

    // 如果是争议，必须提供理由
    if (!confirmed && !disputeReason) {
      return NextResponse.json({ 
        error: 'Dispute reason is required when not confirming' 
      }, { status: 400 });
    }

    // 检查用户确认权限
    const permission = await ConfirmationService.getUserConfirmationPermission(betId, user.id);
    
    if (!permission.canConfirm) {
      return NextResponse.json({ 
        error: permission.reason || 'Cannot confirm this bet',
        code: permission.alreadyConfirmed ? 'ALREADY_CONFIRMED' : 'NO_PERMISSION'
      }, { status: 403 });
    }

    // 提交确认
    const result = await ConfirmationService.confirmResult(
      betId,
      user.id,
      confirmed,
      disputeReason
    );

    // 获取更新后的确认状态
    const status = await ConfirmationService.getConfirmationStatus(betId);

    return NextResponse.json({
      success: true,
      message: confirmed ? '结果确认成功！' : '争议已提交！',
      data: {
        betId,
        userType: result.userType,
        confirmed,
        disputeReason,
        confirmationStatus: status,
        createdAt: result.createdAt
      }
    });

  } catch (error) {
    console.error('Confirm bet result error:', error);
    
    // 处理特定错误
    if (error instanceof Error) {
      if (error.message.includes('不存在')) {
        return NextResponse.json({
          error: error.message,
          code: 'BET_NOT_FOUND'
        }, { status: 404 });
      }
      
      if (error.message.includes('不在确认状态')) {
        return NextResponse.json({
          error: error.message,
          code: 'INVALID_STATUS'
        }, { status: 400 });
      }
      
      if (error.message.includes('期限已过')) {
        return NextResponse.json({
          error: error.message,
          code: 'DEADLINE_EXPIRED'
        }, { status: 400 });
      }
      
      if (error.message.includes('已确认过')) {
        return NextResponse.json({
          error: error.message,
          code: 'ALREADY_CONFIRMED'
        }, { status: 400 });
      }
    }

    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Internal server error',
        code: 'CONFIRMATION_ERROR'
      },
      { status: 500 }
    );
  }
}

/**
 * @api {GET} /api/social-bet/confirm/status 获取确认状态
 * @apiDescription 获取指定赌约的确认状态和用户权限
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // 验证用户身份（可选）
    const { data: { user } } = await supabase.auth.getUser();

    const { searchParams } = new URL(request.url);
    const betId = searchParams.get('betId');

    if (!betId) {
      return NextResponse.json({ 
        error: 'Missing required parameter: betId' 
      }, { status: 400 });
    }

    // 获取确认状态
    const status = await ConfirmationService.getConfirmationStatus(betId);
    
    if (!status) {
      return NextResponse.json({ 
        error: 'Bet not found or not in confirmation status' 
      }, { status: 404 });
    }

    // 获取用户权限（如果已登录）
    let userPermission = null;
    if (user) {
      userPermission = await ConfirmationService.getUserConfirmationPermission(betId, user.id);
    }

    // 获取确认历史
    const history = await ConfirmationService.getConfirmationHistory(betId);

    // 计算时间信息
    const now = new Date();
    const deadline = new Date(status.confirmationDeadline);
    const autoConfirmAt = status.autoConfirmAt ? new Date(status.autoConfirmAt) : null;
    
    const timeLeft = deadline.getTime() - now.getTime();
    const hoursLeft = Math.max(0, Math.floor(timeLeft / (1000 * 60 * 60)));
    const minutesLeft = Math.max(0, Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60)));

    return NextResponse.json({
      success: true,
      data: {
        confirmationStatus: status,
        userPermission,
        confirmationHistory: history,
        timeInfo: {
          deadline: status.confirmationDeadline,
          autoConfirmAt: status.autoConfirmAt,
          hoursLeft,
          minutesLeft,
          expired: timeLeft <= 0,
          timeLeftText: timeLeft > 0 
            ? `${hoursLeft}小时${minutesLeft}分钟`
            : '已过期'
        }
      }
    });

  } catch (error) {
    console.error('Get confirmation status error:', error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Internal server error',
        code: 'GET_STATUS_ERROR'
      },
      { status: 500 }
    );
  }
}

// 配置运行时
export const runtime = 'nodejs';
