/**
 * Social Bet用户统计API
 * 获取用户的投注统计信息
 */

import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

/**
 * @api {GET} /api/social-bet/user-stats 获取用户统计
 * @apiDescription 获取用户的投注统计信息
 */
export async function GET(request: NextRequest) {
  try {
    // 检查Supabase是否可用
    if (!supabase) {
      // 开发模式：返回模拟数据
      return NextResponse.json({
        success: true,
        data: {
          totalBets: 12,
          totalWins: 8,
          totalEarnings: 2500,
          winRate: 66.7,
          currentStreak: 3,
          certificationLevel: 2,
          reputationScore: 850
        },
        message: '开发模式：返回模拟用户统计数据'
      });
    }

    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json({
        success: false,
        error: '缺少用户ID参数'
      }, { status: 400 });
    }

    // 这里应该从数据库获取真实的用户统计数据
    // 目前返回模拟数据
    const mockStats = {
      // 基础统计
      fortuneBalance: 1250,
      totalBets: 23,
      winRate: 68.5,
      totalEarnings: 3420,
      rank: 156,

      // 认证和信誉
      certificationLevel: 'X2',
      reputationScore: 125,

      // 签到信息
      dailyCheckInStreak: 7,
      canCheckInToday: true,

      // 详细统计
      fortuneStats: {
        balance: 1250,
        totalEarned: 5000,
        totalSpent: 3750,
        consecutiveCheckInDays: 7,
        totalCheckInDays: 25,
        canCheckInToday: true
      },

      betStats: {
        totalCreated: 5,
        totalParticipated: 18,
        activeBets: 8,
        completedBets: 15,
        totalEarnings: 3420,
        winRate: 68.5
      },

      judgmentStats: {
        certificationLevel: 'X2',
        reputationScore: 125,
        dailyJudgmentsUsed: 2,
        dailyJudgmentLimit: 5,
        totalJudgments: 45,
        correctJudgments: 38,
        accuracy: 84.4,
        consecutiveCorrect: 3
      }
    };

    return NextResponse.json({
      success: true,
      data: mockStats
    });

  } catch (error) {
    console.error('获取用户统计失败:', error);
    return NextResponse.json({
      success: false,
      error: '获取用户统计失败'
    }, { status: 500 });
  }
}
