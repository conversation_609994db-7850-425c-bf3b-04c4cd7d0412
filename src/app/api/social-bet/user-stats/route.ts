/**
 * Social Bet用户统计API
 * 获取用户的投注统计信息
 */

import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

/**
 * @api {GET} /api/social-bet/user-stats 获取用户统计
 * @apiDescription 获取用户的投注统计信息
 */
export async function GET(request: NextRequest) {
  try {
    // 检查Supabase是否可用
    if (!supabase) {
      // 开发模式：返回模拟数据
      return NextResponse.json({
        success: true,
        data: {
          totalBets: 12,
          totalWins: 8,
          totalEarnings: 2500,
          winRate: 66.7,
          currentStreak: 3,
          certificationLevel: 2,
          reputationScore: 850
        },
        message: '开发模式：返回模拟用户统计数据'
      });
    }

    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json({
        success: false,
        error: '缺少用户ID参数'
      }, { status: 400 });
    }

    // 这里应该从数据库获取真实的用户统计数据
    // 目前返回模拟数据
    const mockStats = {
      totalBets: Math.floor(Math.random() * 50) + 1,
      totalWins: Math.floor(Math.random() * 30) + 1,
      totalEarnings: Math.floor(Math.random() * 5000) + 100,
      winRate: Math.random() * 100,
      currentStreak: Math.floor(Math.random() * 10),
      certificationLevel: Math.floor(Math.random() * 6),
      reputationScore: Math.floor(Math.random() * 1000) + 100
    };

    return NextResponse.json({
      success: true,
      data: mockStats
    });

  } catch (error) {
    console.error('获取用户统计失败:', error);
    return NextResponse.json({
      success: false,
      error: '获取用户统计失败'
    }, { status: 500 });
  }
}
