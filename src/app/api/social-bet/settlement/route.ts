/**
 * Social Bet结算API
 * 处理赌约结算和统计查询
 */

import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { SettlementService } from '@/services/socialbet/SettlementService';

/**
 * @api {POST} /api/social-bet/settlement 手动触发结算
 * @apiDescription 管理员或创建者手动触发赌约结算
 */
export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // 验证用户身份
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { betId } = await request.json();

    if (!betId) {
      return NextResponse.json({ 
        error: 'Missing required parameter: betId' 
      }, { status: 400 });
    }

    // 获取赌约信息验证权限
    const { data: bet } = await supabase
      .from('social_bets')
      .select('creator_id, status, winning_option')
      .eq('id', betId)
      .single();

    if (!bet) {
      return NextResponse.json({ error: 'Bet not found' }, { status: 404 });
    }

    // 验证权限（只有创建者或管理员可以触发结算）
    const isCreator = bet.creator_id === user.id;
    const isAdmin = false; // 这里可以添加管理员验证逻辑

    if (!isCreator && !isAdmin) {
      return NextResponse.json({ 
        error: 'Only bet creator or admin can trigger settlement' 
      }, { status: 403 });
    }

    // 验证赌约状态
    if (bet.status !== 'settled') {
      return NextResponse.json({ 
        error: 'Bet is not ready for settlement' 
      }, { status: 400 });
    }

    if (!bet.winning_option) {
      return NextResponse.json({ 
        error: 'Winning option not determined' 
      }, { status: 400 });
    }

    // 执行结算
    const settlementResult = await SettlementService.settleBet(betId);

    return NextResponse.json({
      success: true,
      message: '赌约结算完成！',
      data: {
        betId,
        settlement: {
          totalPool: settlementResult.totalPool,
          platformFee: settlementResult.platformFee,
          winnerCount: settlementResult.winners.length,
          totalWinnerPayouts: settlementResult.winnerPayouts,
          referralRewards: settlementResult.referralRewards,
          judgmentRewards: settlementResult.judgmentRewards,
          settlementDate: settlementResult.settlementDate
        },
        summary: {
          winners: settlementResult.winners.map(w => ({
            userId: w.userId,
            payoutAmount: w.payoutAmount,
            netProfit: w.netProfit
          })),
          referralCount: settlementResult.referralPayouts.length,
          judgmentCount: settlementResult.judgmentPayouts.length
        }
      }
    });

  } catch (error) {
    console.error('Settlement error:', error);
    
    // 处理特定错误
    if (error instanceof Error) {
      if (error.message.includes('不存在')) {
        return NextResponse.json({
          error: error.message,
          code: 'BET_NOT_FOUND'
        }, { status: 404 });
      }
      
      if (error.message.includes('状态不正确')) {
        return NextResponse.json({
          error: error.message,
          code: 'INVALID_STATUS'
        }, { status: 400 });
      }
      
      if (error.message.includes('已经结算过')) {
        return NextResponse.json({
          error: error.message,
          code: 'ALREADY_SETTLED'
        }, { status: 400 });
      }
    }

    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Internal server error',
        code: 'SETTLEMENT_ERROR'
      },
      { status: 500 }
    );
  }
}

/**
 * @api {GET} /api/social-bet/settlement/stats 获取结算统计
 * @apiDescription 获取指定赌约的结算统计信息
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // 验证用户身份（可选）
    const { data: { user } } = await supabase.auth.getUser();

    const { searchParams } = new URL(request.url);
    const betId = searchParams.get('betId');

    if (!betId) {
      return NextResponse.json({ 
        error: 'Missing required parameter: betId' 
      }, { status: 400 });
    }

    // 获取赌约基本信息
    const { data: bet } = await supabase
      .from('social_bets')
      .select('*')
      .eq('id', betId)
      .single();

    if (!bet) {
      return NextResponse.json({ error: 'Bet not found' }, { status: 404 });
    }

    // 获取参与者信息
    const { data: participants } = await supabase
      .from('bet_participants')
      .select(`
        *,
        users (
          id,
          username,
          email
        )
      `)
      .eq('bet_id', betId);

    // 获取结算统计
    const settlementStats = await SettlementService.getSettlementStats(betId);

    // 计算各选项统计
    const optionStats = participants?.reduce((stats, p) => {
      if (!stats[p.selected_option]) {
        stats[p.selected_option] = {
          participants: 0,
          totalBetAmount: 0,
          totalPayoutAmount: 0,
          winners: 0
        };
      }
      
      stats[p.selected_option].participants++;
      stats[p.selected_option].totalBetAmount += p.bet_amount;
      stats[p.selected_option].totalPayoutAmount += p.payout_amount || 0;
      
      if (p.is_winner) {
        stats[p.selected_option].winners++;
      }
      
      return stats;
    }, {} as Record<string, any>) || {};

    // 获取获胜者详情
    const winners = participants?.filter(p => p.is_winner === true).map(p => ({
      user: {
        id: p.users?.id,
        username: p.users?.username,
        email: p.users?.email
      },
      betAmount: p.bet_amount,
      payoutAmount: p.payout_amount,
      netProfit: (p.payout_amount || 0) - p.bet_amount,
      roi: p.bet_amount > 0 ? ((p.payout_amount || 0) - p.bet_amount) / p.bet_amount * 100 : 0
    })) || [];

    // 获取失败者详情
    const losers = participants?.filter(p => p.is_winner === false).map(p => ({
      user: {
        id: p.users?.id,
        username: p.users?.username,
        email: p.users?.email
      },
      betAmount: p.bet_amount,
      loss: p.bet_amount
    })) || [];

    return NextResponse.json({
      success: true,
      data: {
        bet: {
          id: bet.id,
          title: bet.title,
          status: bet.status,
          winningOption: bet.winning_option,
          totalPool: bet.total_pool,
          participantCount: bet.participant_count,
          platformFeeRate: bet.platform_fee_rate,
          referralRewardRate: bet.referral_reward_rate
        },
        
        optionStats,
        
        participants: {
          total: participants?.length || 0,
          winners: winners.length,
          losers: losers.length
        },
        
        winners,
        losers,
        
        settlement: settlementStats || {
          totalPayouts: 0,
          winnerPayouts: 0,
          referralRewards: 0,
          judgmentRewards: 0,
          transactionCount: 0
        },
        
        financial: {
          totalPool: bet.total_pool,
          platformFee: bet.total_pool * bet.platform_fee_rate,
          judgmentRewards: bet.total_pool * 0.02, // 2%
          referralRewards: bet.total_pool * bet.referral_reward_rate,
          availableForWinners: bet.total_pool * (1 - bet.platform_fee_rate - 0.02 - bet.referral_reward_rate)
        }
      }
    });

  } catch (error) {
    console.error('Get settlement stats error:', error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Internal server error',
        code: 'GET_SETTLEMENT_STATS_ERROR'
      },
      { status: 500 }
    );
  }
}

// 配置运行时
export const runtime = 'nodejs';
