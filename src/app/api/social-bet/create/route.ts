/**
 * Social Bet创建API
 * 处理赌约创建请求
 */

import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { SocialBetService, CreateBetParams } from '@/services/socialbet/SocialBetService';
import { rateLimitMiddleware } from '@/middleware/rateLimiter';

/**
 * @api {POST} /api/social-bet/create 创建赌约
 * @apiDescription 创建新的Social Bet赌约，支持1v1和1vN模式
 */
export async function POST(request: NextRequest) {
  try {
    // 应用频率限制
    const rateLimitResponse = await rateLimitMiddleware(request, '/api/social-bet/create');
    if (rateLimitResponse) {
      return rateLimitResponse;
    }

    // 检查Supabase是否可用
    if (!supabase) {
      return NextResponse.json({
        success: false,
        error: '开发模式：数据库服务不可用'
      }, { status: 503 });
    }
    
    // 验证用户身份
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    
    // 验证必需参数
    const requiredFields = ['title', 'description', 'category', 'betType', 'options', 'minBetAmount', 'bettingDeadline', 'resultDeadline'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json({ 
          error: `Missing required field: ${field}` 
        }, { status: 400 });
      }
    }

    // 验证赌约类型
    if (!['1v1', '1vN'].includes(body.betType)) {
      return NextResponse.json({ 
        error: 'Invalid bet type. Must be "1v1" or "1vN"' 
      }, { status: 400 });
    }

    // 验证选项
    if (!Array.isArray(body.options) || body.options.length < 2) {
      return NextResponse.json({ 
        error: 'At least 2 options are required' 
      }, { status: 400 });
    }

    // 验证选项格式
    for (const option of body.options) {
      if (!option.id || !option.text) {
        return NextResponse.json({ 
          error: 'Each option must have id and text' 
        }, { status: 400 });
      }
    }

    // 验证投注金额
    if (body.minBetAmount <= 0) {
      return NextResponse.json({ 
        error: 'Minimum bet amount must be positive' 
      }, { status: 400 });
    }

    if (body.maxBetAmount && body.maxBetAmount < body.minBetAmount) {
      return NextResponse.json({ 
        error: 'Maximum bet amount must be greater than minimum' 
      }, { status: 400 });
    }

    // 验证时间
    const bettingDeadline = new Date(body.bettingDeadline);
    const resultDeadline = new Date(body.resultDeadline);
    const now = new Date();

    if (bettingDeadline <= now) {
      return NextResponse.json({ 
        error: 'Betting deadline must be in the future' 
      }, { status: 400 });
    }

    if (resultDeadline <= bettingDeadline) {
      return NextResponse.json({ 
        error: 'Result deadline must be after betting deadline' 
      }, { status: 400 });
    }

    // 1v1模式特殊验证
    if (body.betType === '1v1') {
      if (body.targetUserId && body.targetUserId === user.id) {
        return NextResponse.json({ 
          error: 'Cannot create 1v1 bet with yourself' 
        }, { status: 400 });
      }

      if (body.creatorOption && !body.options.find((opt: any) => opt.id === body.creatorOption)) {
        return NextResponse.json({ 
          error: 'Invalid creator option' 
        }, { status: 400 });
      }
    }

    // 构建创建参数
    const createParams: CreateBetParams = {
      title: body.title.trim(),
      description: body.description.trim(),
      category: body.category,
      betType: body.betType,
      templateType: body.templateType,
      options: body.options.map((opt: any) => ({
        id: opt.id,
        text: opt.text.trim(),
        participants: 0,
        totalAmount: 0
      })),
      minBetAmount: body.minBetAmount,
      maxBetAmount: body.maxBetAmount,
      targetUserId: body.targetUserId,
      creatorOption: body.creatorOption,
      bettingDeadline: body.bettingDeadline,
      resultDeadline: body.resultDeadline,
      requiresJudgment: body.requiresJudgment ?? true,
      tags: body.tags ? body.tags.filter((tag: string) => tag.trim()) : [],
      referralRewardRate: body.referralRewardRate || 0.1
    };

    // 创建赌约
    const bet = await SocialBetService.createBet(user.id, createParams);

    return NextResponse.json({
      success: true,
      message: '赌约创建成功！',
      data: {
        betId: bet.id,
        title: bet.title,
        betType: bet.betType,
        status: bet.status,
        bettingDeadline: bet.bettingDeadline,
        minBetAmount: bet.minBetAmount,
        options: bet.options,
        createdAt: bet.createdAt
      }
    });

  } catch (error) {
    console.error('Create bet error:', error);
    
    // 处理特定错误
    if (error instanceof Error) {
      if (error.message.includes('福气余额不足')) {
        return NextResponse.json({
          error: error.message,
          code: 'INSUFFICIENT_FORTUNE'
        }, { status: 400 });
      }
      
      if (error.message.includes('时间')) {
        return NextResponse.json({
          error: error.message,
          code: 'INVALID_TIME'
        }, { status: 400 });
      }
    }

    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Internal server error',
        code: 'CREATE_BET_ERROR'
      },
      { status: 500 }
    );
  }
}

/**
 * @api {GET} /api/social-bet/create/templates 获取赌约模板
 * @apiDescription 获取可用的赌约模板列表
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // 验证用户身份
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // 获取模板列表
    const { data: templates, error } = await supabase
      .from('bet_templates')
      .select('*')
      .eq('is_active', true)
      .order('usage_count', { ascending: false });

    if (error) {
      throw new Error(`Failed to fetch templates: ${error.message}`);
    }

    // 预定义模板（如果数据库中没有）
    const defaultTemplates = [
      {
        id: 'sports_match',
        name: '体育比赛',
        description: '预测体育比赛结果',
        category: 'sports',
        template_config: {
          defaultOptions: [
            { id: 'home_win', text: '主队获胜' },
            { id: 'away_win', text: '客队获胜' },
            { id: 'draw', text: '平局' }
          ]
        },
        usage_count: 0
      },
      {
        id: 'price_prediction',
        name: '价格预测',
        description: '预测加密货币或股票价格走势',
        category: 'crypto',
        template_config: {
          defaultOptions: [
            { id: 'price_up', text: '价格上涨' },
            { id: 'price_down', text: '价格下跌' }
          ]
        },
        usage_count: 0
      },
      {
        id: 'yes_no',
        name: '是否问题',
        description: '简单的是/否问题',
        category: 'other',
        template_config: {
          defaultOptions: [
            { id: 'yes', text: '是' },
            { id: 'no', text: '否' }
          ]
        },
        usage_count: 0
      },
      {
        id: 'multiple_choice',
        name: '多选题',
        description: '多个选项的预测',
        category: 'other',
        template_config: {
          defaultOptions: [
            { id: 'option_a', text: '选项A' },
            { id: 'option_b', text: '选项B' },
            { id: 'option_c', text: '选项C' },
            { id: 'option_d', text: '选项D' }
          ]
        },
        usage_count: 0
      }
    ];

    const allTemplates = templates && templates.length > 0 ? templates : defaultTemplates;

    return NextResponse.json({
      success: true,
      data: {
        templates: allTemplates.map(template => ({
          id: template.id,
          name: template.name,
          description: template.description,
          category: template.category,
          defaultOptions: template.template_config?.defaultOptions || template.default_options,
          usageCount: template.usage_count
        }))
      }
    });

  } catch (error) {
    console.error('Get templates error:', error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Internal server error',
        code: 'FETCH_TEMPLATES_ERROR'
      },
      { status: 500 }
    );
  }
}

// 配置运行时
export const runtime = 'nodejs';
