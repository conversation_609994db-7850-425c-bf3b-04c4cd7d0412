/**
 * Social Bet查询API
 * 处理赌约列表查询和详情获取
 */

import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { SocialBetService } from '@/services/socialbet/SocialBetService';

/**
 * @api {GET} /api/social-bet/bets 获取赌约列表
 * @apiDescription 获取赌约列表，支持筛选、排序、分页
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // 验证用户身份（可选，游客也可以查看）
    const { data: { user } } = await supabase.auth.getUser();

    const { searchParams } = new URL(request.url);
    
    // 解析查询参数
    const category = searchParams.get('category');
    const status = searchParams.get('status');
    const creatorId = searchParams.get('creatorId');
    const betType = searchParams.get('betType'); // '1v1' or '1vN'
    const featured = searchParams.get('featured') === 'true';
    const search = searchParams.get('search'); // 搜索关键词
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100); // 最大100条
    const orderBy = searchParams.get('orderBy') as 'created_at' | 'betting_deadline' | 'total_pool' | 'participant_count' || 'created_at';
    const orderDirection = searchParams.get('orderDirection') as 'asc' | 'desc' || 'desc';

    const offset = (page - 1) * limit;

    // 构建查询
    let query = supabase
      .from('social_bets')
      .select(`
        *,
        users!creator_id (
          id,
          username,
          email
        )
      `, { count: 'exact' });

    // 应用筛选条件
    if (category) {
      query = query.eq('category', category);
    }

    if (status) {
      query = query.eq('status', status);
    }

    if (creatorId) {
      query = query.eq('creator_id', creatorId);
    }

    if (betType) {
      query = query.eq('bet_type', betType);
    }

    if (featured) {
      query = query.eq('is_featured', true);
    }

    // 搜索功能
    if (search) {
      query = query.or(`title.ilike.%${search}%, description.ilike.%${search}%`);
    }

    // 排序
    query = query.order(orderBy, { ascending: orderDirection === 'asc' });

    // 分页
    query = query.range(offset, offset + limit - 1);

    const { data: bets, error, count } = await query;

    if (error) {
      throw new Error(`Failed to fetch bets: ${error.message}`);
    }

    // 格式化数据
    const formattedBets = bets?.map(bet => ({
      id: bet.id,
      title: bet.title,
      description: bet.description,
      category: bet.category,
      betType: bet.bet_type,
      templateType: bet.template_type,
      creator: {
        id: bet.users?.id,
        username: bet.users?.username,
        email: bet.users?.email
      },
      options: bet.options || [],
      minBetAmount: bet.min_bet_amount,
      maxBetAmount: bet.max_bet_amount,
      totalPool: bet.total_pool,
      targetUserId: bet.target_user_id,
      bettingDeadline: bet.betting_deadline,
      resultDeadline: bet.result_deadline,
      status: bet.status,
      requiresJudgment: bet.requires_judgment,
      currentJudgmentRound: bet.current_judgment_round,
      winningOption: bet.winning_option,
      platformFeeRate: bet.platform_fee_rate,
      referralRewardRate: bet.referral_reward_rate,
      tags: bet.tags,
      isFeatured: bet.is_featured,
      viewCount: bet.view_count,
      participantCount: bet.participant_count,
      createdAt: bet.created_at,
      updatedAt: bet.updated_at,
      
      // 计算时间状态
      timeStatus: getTimeStatus(bet.betting_deadline, bet.result_deadline, bet.status),
      
      // 用户参与状态（如果已登录）
      userParticipation: null // 将在下面填充
    })) || [];

    // 如果用户已登录，获取用户参与状态
    if (user && formattedBets.length > 0) {
      const betIds = formattedBets.map(bet => bet.id);
      const { data: participations } = await supabase
        .from('bet_participants')
        .select('bet_id, selected_option, bet_amount, status')
        .eq('user_id', user.id)
        .in('bet_id', betIds);

      // 将参与状态添加到赌约数据中
      formattedBets.forEach(bet => {
        const participation = participations?.find(p => p.bet_id === bet.id);
        if (participation) {
          bet.userParticipation = {
            selectedOption: participation.selected_option,
            betAmount: participation.bet_amount,
            status: participation.status
          };
        }
      });
    }

    // 获取分类统计
    const { data: categoryStats } = await supabase
      .from('social_bets')
      .select('category')
      .eq('status', 'open');

    const categories = categoryStats?.reduce((stats, bet) => {
      stats[bet.category] = (stats[bet.category] || 0) + 1;
      return stats;
    }, {} as Record<string, number>) || {};

    return NextResponse.json({
      success: true,
      data: {
        bets: formattedBets,
        pagination: {
          page,
          limit,
          total: count || 0,
          totalPages: Math.ceil((count || 0) / limit)
        },
        filters: {
          categories: Object.keys(categories).map(cat => ({
            name: cat,
            count: categories[cat]
          })),
          availableStatuses: ['open', 'betting_closed', 'judging', 'confirming', 'settled'],
          availableBetTypes: ['1v1', '1vN']
        }
      }
    });

  } catch (error) {
    console.error('Get bets error:', error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Internal server error',
        code: 'FETCH_BETS_ERROR'
      },
      { status: 500 }
    );
  }
}

/**
 * 获取时间状态
 */
function getTimeStatus(bettingDeadline: string, resultDeadline: string, status: string) {
  const now = new Date();
  const bettingEnd = new Date(bettingDeadline);
  const resultEnd = new Date(resultDeadline);

  if (status === 'settled' || status === 'cancelled') {
    return status;
  }

  if (now < bettingEnd) {
    const timeLeft = bettingEnd.getTime() - now.getTime();
    const hoursLeft = Math.floor(timeLeft / (1000 * 60 * 60));
    const daysLeft = Math.floor(hoursLeft / 24);
    
    if (daysLeft > 0) {
      return `${daysLeft}天后截止投注`;
    } else if (hoursLeft > 0) {
      return `${hoursLeft}小时后截止投注`;
    } else {
      const minutesLeft = Math.floor(timeLeft / (1000 * 60));
      return `${minutesLeft}分钟后截止投注`;
    }
  } else if (now < resultEnd) {
    return '等待结果公布';
  } else {
    return '等待裁定';
  }
}

// 配置运行时
export const runtime = 'nodejs';
