/**
 * Social Bet详情API
 * 处理单个赌约的详细信息获取和更新
 */

import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { SocialBetService } from '@/services/socialbet/SocialBetService';

/**
 * @api {GET} /api/social-bet/bets/[id] 获取赌约详情
 * @apiDescription 获取指定赌约的详细信息，包括参与者列表
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 检查Supabase是否可用
    if (!supabase) {
      return NextResponse.json({
        success: false,
        error: '开发模式：数据库服务不可用'
      }, { status: 503 });
    }

    // 验证用户身份（可选）
    const { data: { user } } = await supabase.auth.getUser();

    const betId = params.id;

    // 获取赌约详情
    const bet = await SocialBetService.getBetById(betId);
    if (!bet) {
      return NextResponse.json({ error: 'Bet not found' }, { status: 404 });
    }

    // 获取创建者信息
    const { data: creator } = await supabase
      .from('users')
      .select('id, username, email, avatar_url')
      .eq('id', bet.creatorId)
      .single();

    // 获取参与者列表
    const { data: participants } = await supabase
      .from('bet_participants')
      .select(`
        *,
        users (
          id,
          username,
          email,
          avatar_url
        )
      `)
      .eq('bet_id', betId)
      .order('created_at', { ascending: false });

    // 获取裁定记录（如果有）
    const { data: judgments } = await supabase
      .from('bet_judgments')
      .select(`
        *,
        users (
          id,
          username,
          email
        )
      `)
      .eq('bet_id', betId)
      .order('judgment_round', { ascending: true })
      .order('created_at', { ascending: false });

    // 更新浏览次数
    await supabase
      .from('social_bets')
      .update({ 
        view_count: bet.viewCount + 1,
        updated_at: new Date().toISOString()
      })
      .eq('id', betId);

    // 计算各选项统计
    const optionStats = participants?.reduce((stats, p) => {
      if (!stats[p.selected_option]) {
        stats[p.selected_option] = {
          participants: 0,
          totalAmount: 0,
          percentage: 0
        };
      }
      stats[p.selected_option].participants++;
      stats[p.selected_option].totalAmount += p.bet_amount;
      return stats;
    }, {} as Record<string, { participants: number; totalAmount: number; percentage: number }>) || {};

    // 计算百分比
    const totalPool = bet.totalPool;
    Object.keys(optionStats).forEach(option => {
      optionStats[option].percentage = totalPool > 0 
        ? (optionStats[option].totalAmount / totalPool) * 100 
        : 0;
    });

    // 用户参与状态
    let userParticipation = null;
    if (user) {
      const userParticipant = participants?.find(p => p.user_id === user.id);
      if (userParticipant) {
        userParticipation = {
          selectedOption: userParticipant.selected_option,
          betAmount: userParticipant.bet_amount,
          status: userParticipant.status,
          payoutAmount: userParticipant.payout_amount,
          isWinner: userParticipant.is_winner,
          createdAt: userParticipant.created_at
        };
      }
    }

    // 裁定统计
    const judgmentStats = judgments?.reduce((stats, j) => {
      const round = j.judgment_round;
      if (!stats[round]) {
        stats[round] = {};
      }
      if (!stats[round][j.selected_option]) {
        stats[round][j.selected_option] = 0;
      }
      stats[round][j.selected_option]++;
      return stats;
    }, {} as Record<number, Record<string, number>>) || {};

    return NextResponse.json({
      success: true,
      data: {
        // 赌约基本信息
        bet: {
          ...bet,
          creator: creator ? {
            id: creator.id,
            username: creator.username,
            email: creator.email,
            avatarUrl: creator.avatar_url
          } : null,
          
          // 时间状态
          timeStatus: getTimeStatus(bet.bettingDeadline, bet.resultDeadline, bet.status),
          
          // 是否可以参与
          canParticipate: canUserParticipate(bet, user?.id, userParticipation),
          
          // 用户参与状态
          userParticipation
        },
        
        // 选项统计
        optionStats,
        
        // 参与者列表
        participants: participants?.map(p => ({
          id: p.id,
          user: {
            id: p.users?.id,
            username: p.users?.username,
            email: p.users?.email,
            avatarUrl: p.users?.avatar_url
          },
          selectedOption: p.selected_option,
          betAmount: p.bet_amount,
          status: p.status,
          payoutAmount: p.payout_amount,
          isWinner: p.is_winner,
          createdAt: p.created_at
        })) || [],
        
        // 裁定信息
        judgments: {
          stats: judgmentStats,
          records: judgments?.map(j => ({
            id: j.id,
            round: j.judgment_round,
            judge: {
              id: j.users?.id,
              username: j.users?.username,
              email: j.users?.email
            },
            selectedOption: j.selected_option,
            confidenceLevel: j.confidence_level,
            reasoning: j.reasoning,
            certificationLevel: j.judge_certification_level,
            reputationScore: j.judge_reputation_score,
            rewardAmount: j.reward_amount,
            isCorrect: j.is_correct_judgment,
            createdAt: j.created_at
          })) || []
        }
      }
    });

  } catch (error) {
    console.error('Get bet details error:', error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Internal server error',
        code: 'FETCH_BET_DETAILS_ERROR'
      },
      { status: 500 }
    );
  }
}

/**
 * @api {PATCH} /api/social-bet/bets/[id] 更新赌约状态
 * @apiDescription 更新赌约状态（仅创建者或管理员）
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // 验证用户身份
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const betId = params.id;
    const { status, winningOption } = await request.json();

    // 获取赌约信息
    const bet = await SocialBetService.getBetById(betId);
    if (!bet) {
      return NextResponse.json({ error: 'Bet not found' }, { status: 404 });
    }

    // 验证权限（只有创建者可以更新）
    if (bet.creatorId !== user.id) {
      return NextResponse.json({ error: 'Only bet creator can update status' }, { status: 403 });
    }

    // 验证状态转换
    const validTransitions = getValidStatusTransitions(bet.status);
    if (!validTransitions.includes(status)) {
      return NextResponse.json({ 
        error: `Invalid status transition from ${bet.status} to ${status}` 
      }, { status: 400 });
    }

    // 如果设置获胜选项，验证选项有效性
    if (winningOption) {
      const validOptions = bet.options.map(opt => opt.id);
      if (!validOptions.includes(winningOption)) {
        return NextResponse.json({ error: 'Invalid winning option' }, { status: 400 });
      }
    }

    // 更新赌约状态
    const { error } = await supabase
      .from('social_bets')
      .update({
        status,
        winning_option: winningOption,
        updated_at: new Date().toISOString()
      })
      .eq('id', betId);

    if (error) {
      throw new Error(`Failed to update bet status: ${error.message}`);
    }

    return NextResponse.json({
      success: true,
      message: '赌约状态更新成功',
      data: {
        betId,
        status,
        winningOption
      }
    });

  } catch (error) {
    console.error('Update bet status error:', error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Internal server error',
        code: 'UPDATE_BET_ERROR'
      },
      { status: 500 }
    );
  }
}

/**
 * 获取时间状态
 */
function getTimeStatus(bettingDeadline: string, resultDeadline: string, status: string) {
  const now = new Date();
  const bettingEnd = new Date(bettingDeadline);
  const resultEnd = new Date(resultDeadline);

  if (status === 'settled' || status === 'cancelled') {
    return status;
  }

  if (now < bettingEnd) {
    const timeLeft = bettingEnd.getTime() - now.getTime();
    const hoursLeft = Math.floor(timeLeft / (1000 * 60 * 60));
    const daysLeft = Math.floor(hoursLeft / 24);
    
    if (daysLeft > 0) {
      return `${daysLeft}天后截止投注`;
    } else if (hoursLeft > 0) {
      return `${hoursLeft}小时后截止投注`;
    } else {
      const minutesLeft = Math.floor(timeLeft / (1000 * 60));
      return `${minutesLeft}分钟后截止投注`;
    }
  } else if (now < resultEnd) {
    return '等待结果公布';
  } else {
    return '等待裁定';
  }
}

/**
 * 判断用户是否可以参与
 */
function canUserParticipate(bet: any, userId?: string, userParticipation?: any) {
  if (!userId) return false;
  if (userParticipation) return false; // 已经参与过
  if (bet.status !== 'open') return false;
  if (new Date() > new Date(bet.bettingDeadline)) return false;
  if (bet.creatorId === userId && bet.betType === '1vN') return false; // 1vN模式创建者不能参与
  if (bet.targetUserId && bet.targetUserId !== userId) return false; // 1v1指定用户
  
  return true;
}

/**
 * 获取有效的状态转换
 */
function getValidStatusTransitions(currentStatus: string): string[] {
  const transitions: Record<string, string[]> = {
    'open': ['betting_closed', 'cancelled'],
    'betting_closed': ['judging', 'confirming', 'settled'],
    'judging': ['confirming', 'settled'],
    'confirming': ['settled'],
    'settled': [],
    'cancelled': [],
    'expired': []
  };

  return transitions[currentStatus] || [];
}

// 配置运行时
export const runtime = 'nodejs';
