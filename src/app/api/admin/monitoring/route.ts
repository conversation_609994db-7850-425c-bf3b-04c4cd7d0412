/**
 * 监控告警API
 * 提供系统监控数据查询和告警管理功能
 */

import { NextRequest, NextResponse } from 'next/server';
import { enhancedMonitoring } from '@/lib/services/EnhancedMonitoringService';
import { loggingService } from '@/lib/services/LoggingService';

/**
 * 获取监控数据
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'overview';
    const hours = parseInt(searchParams.get('hours') || '1');

    const response: any = {
      success: true,
      timestamp: new Date().toISOString()
    };

    switch (type) {
      case 'overview':
        response.data = {
          current: enhancedMonitoring.getLatestMetrics(),
          anomalies: enhancedMonitoring.getRecentAnomalies(10),
          logs: loggingService.getRecentLogs(50)
        };
        break;

      case 'metrics':
        response.data = {
          current: enhancedMonitoring.getLatestMetrics(),
          historical: enhancedMonitoring.getHistoricalMetrics(hours)
        };
        break;

      case 'anomalies':
        const limit = parseInt(searchParams.get('limit') || '50');
        response.data = {
          anomalies: enhancedMonitoring.getRecentAnomalies(limit)
        };
        break;

      case 'logs':
        const logQuery = {
          startTime: searchParams.get('startTime') ? parseInt(searchParams.get('startTime')!) : undefined,
          endTime: searchParams.get('endTime') ? parseInt(searchParams.get('endTime')!) : undefined,
          levels: searchParams.get('levels')?.split(',') as any,
          categories: searchParams.get('categories')?.split(','),
          searchText: searchParams.get('search') || undefined,
          limit: parseInt(searchParams.get('limit') || '100'),
          offset: parseInt(searchParams.get('offset') || '0')
        };

        response.data = {
          logs: loggingService.queryLogs(logQuery),
          stats: loggingService.getLogStats()
        };
        break;

      case 'stats':
        const timeRange = {
          start: Date.now() - (hours * 60 * 60 * 1000),
          end: Date.now()
        };
        
        response.data = {
          logs: loggingService.getLogStats(timeRange),
          system: enhancedMonitoring.getLatestMetrics()
        };
        break;

      case 'health':
        const latest = enhancedMonitoring.getLatestMetrics();
        const anomalies = enhancedMonitoring.getRecentAnomalies(5);
        
        response.data = {
          status: anomalies.length === 0 ? 'healthy' : 
                 anomalies.some(a => a.severity === 'critical') ? 'critical' :
                 anomalies.some(a => a.severity === 'high') ? 'unhealthy' : 'degraded',
          metrics: latest,
          issues: anomalies,
          uptime: latest?.system.uptime || 0
        };
        break;

      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid monitoring type'
        }, { status: 400 });
    }

    return NextResponse.json(response);

  } catch (error) {
    console.error('Monitoring API error:', error);
    
    // 记录错误日志
    loggingService.error(
      `Monitoring API error: ${error instanceof Error ? error.message : 'Unknown error'}`,
      'api',
      'monitoring-api',
      { error: error instanceof Error ? error.stack : error }
    );

    return NextResponse.json({
      success: false,
      error: 'Failed to fetch monitoring data',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

/**
 * 监控管理操作
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, data } = body;

    const response: any = {
      success: true,
      timestamp: new Date().toISOString()
    };

    switch (action) {
      case 'start-monitoring':
        enhancedMonitoring.start();
        loggingService.start();
        response.message = 'Monitoring services started';
        
        loggingService.info(
          'Monitoring services started via API',
          'system',
          'monitoring-api'
        );
        break;

      case 'stop-monitoring':
        enhancedMonitoring.stop();
        loggingService.stop();
        response.message = 'Monitoring services stopped';
        
        loggingService.info(
          'Monitoring services stopped via API',
          'system',
          'monitoring-api'
        );
        break;

      case 'add-notification':
        if (!data || !data.config) {
          return NextResponse.json({
            success: false,
            error: 'Notification config is required'
          }, { status: 400 });
        }
        
        enhancedMonitoring.addNotificationConfig(data.config);
        response.message = 'Notification config added';
        
        loggingService.info(
          `Notification config added: ${data.config.name}`,
          'system',
          'monitoring-api',
          { config: data.config }
        );
        break;

      case 'remove-notification':
        if (!data || !data.id) {
          return NextResponse.json({
            success: false,
            error: 'Notification ID is required'
          }, { status: 400 });
        }
        
        enhancedMonitoring.removeNotificationConfig(data.id);
        response.message = 'Notification config removed';
        
        loggingService.info(
          `Notification config removed: ${data.id}`,
          'system',
          'monitoring-api'
        );
        break;

      case 'cleanup-logs':
        const days = data?.days || 7;
        const cleaned = loggingService.cleanupOldLogs(days);
        response.message = `Cleaned up ${cleaned} old log entries`;
        response.data = { cleanedCount: cleaned };
        
        loggingService.info(
          `Log cleanup completed: ${cleaned} entries removed`,
          'system',
          'monitoring-api',
          { days, cleanedCount: cleaned }
        );
        break;

      case 'export-logs':
        const query = data?.query || {};
        const format = data?.format || 'json';
        const exportData = loggingService.exportLogs(query, format);
        
        response.data = {
          format,
          content: exportData,
          size: exportData.length
        };
        
        loggingService.info(
          `Logs exported: ${format} format, ${exportData.length} bytes`,
          'system',
          'monitoring-api',
          { format, size: exportData.length }
        );
        break;

      case 'test-alert':
        // 创建测试异常
        const testAnomaly = {
          id: `test_${Date.now()}`,
          type: 'system' as const,
          severity: 'medium' as const,
          description: 'Test alert triggered via API',
          data: { test: true },
          timestamp: Date.now(),
          resolved: false
        };
        
        enhancedMonitoring.emit('anomaly', testAnomaly);
        response.message = 'Test alert triggered';
        response.data = testAnomaly;
        
        loggingService.info(
          'Test alert triggered via API',
          'system',
          'monitoring-api',
          { anomaly: testAnomaly }
        );
        break;

      case 'aggregate-logs':
        const field = data?.field || 'level';
        const aggregateQuery = data?.query || {};
        const aggregation = loggingService.aggregateLogs(field, aggregateQuery);
        
        response.data = aggregation;
        
        loggingService.debug(
          `Log aggregation performed: ${field}`,
          'system',
          'monitoring-api',
          { field, buckets: aggregation.buckets.length }
        );
        break;

      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid action'
        }, { status: 400 });
    }

    return NextResponse.json(response);

  } catch (error) {
    console.error('Monitoring management error:', error);
    
    // 记录错误日志
    loggingService.error(
      `Monitoring management error: ${error instanceof Error ? error.message : 'Unknown error'}`,
      'api',
      'monitoring-api',
      { error: error instanceof Error ? error.stack : error }
    );

    return NextResponse.json({
      success: false,
      error: 'Failed to execute monitoring action',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

/**
 * 实时监控数据流（Server-Sent Events）
 */
export async function PUT(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'metrics';

    // 创建SSE响应
    const stream = new ReadableStream({
      start(controller) {
        const encoder = new TextEncoder();

        // 发送初始数据
        const sendData = (data: any) => {
          const message = `data: ${JSON.stringify(data)}\n\n`;
          controller.enqueue(encoder.encode(message));
        };

        // 监听事件
        const handleMetrics = (metrics: any) => {
          if (type === 'metrics' || type === 'all') {
            sendData({ type: 'metrics', data: metrics });
          }
        };

        const handleAnomaly = (anomaly: any) => {
          if (type === 'anomalies' || type === 'all') {
            sendData({ type: 'anomaly', data: anomaly });
          }
        };

        const handleLogs = (logs: any) => {
          if (type === 'logs' || type === 'all') {
            sendData({ type: 'logs', data: logs });
          }
        };

        // 注册事件监听器
        enhancedMonitoring.on('realtime-metrics', handleMetrics);
        enhancedMonitoring.on('anomaly', handleAnomaly);
        loggingService.on('logs-updated', handleLogs);

        // 发送初始数据
        sendData({
          type: 'connected',
          data: {
            timestamp: new Date().toISOString(),
            message: 'Real-time monitoring connected'
          }
        });

        // 清理函数
        const cleanup = () => {
          enhancedMonitoring.off('realtime-metrics', handleMetrics);
          enhancedMonitoring.off('anomaly', handleAnomaly);
          loggingService.off('logs-updated', handleLogs);
        };

        // 30秒后自动关闭连接
        setTimeout(() => {
          cleanup();
          controller.close();
        }, 30000);

        // 处理客户端断开连接
        request.signal.addEventListener('abort', () => {
          cleanup();
          controller.close();
        });
      }
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control'
      }
    });

  } catch (error) {
    console.error('Real-time monitoring error:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to establish real-time monitoring connection'
    }, { status: 500 });
  }
}
