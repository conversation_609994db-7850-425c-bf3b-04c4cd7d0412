/**
 * 性能监控API
 * 提供系统性能指标查询和管理功能
 */

import { NextRequest, NextResponse } from 'next/server';
import { getPerformanceMetrics, clearCache, resetMetrics } from '@/lib/middleware/performanceMiddleware';
import { getLoadStats, getHealthCheck, clearRateLimitStore } from '@/lib/middleware/rateLimitMiddleware';
import { cacheService } from '@/lib/services/CacheService';

/**
 * 获取性能指标
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'all';
    
    const response: any = {
      success: true,
      timestamp: new Date().toISOString()
    };
    
    switch (type) {
      case 'api':
        response.data = {
          api: getPerformanceMetrics()
        };
        break;
        
      case 'load':
        response.data = {
          load: getLoadStats()
        };
        break;
        
      case 'cache':
        response.data = {
          cache: {
            stats: cacheService.getStats(),
            sizes: cacheService.getSizes()
          }
        };
        break;
        
      case 'health':
        response.data = getHealthCheck();
        break;
        
      case 'all':
      default:
        response.data = {
          api: getPerformanceMetrics(),
          load: getLoadStats(),
          cache: {
            stats: cacheService.getStats(),
            sizes: cacheService.getSizes()
          },
          health: getHealthCheck(),
          system: await getSystemMetrics()
        };
        break;
    }
    
    return NextResponse.json(response);
    
  } catch (error) {
    console.error('Performance API error:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch performance metrics',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

/**
 * 性能管理操作
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, target } = body;
    
    const response: any = {
      success: true,
      timestamp: new Date().toISOString()
    };
    
    switch (action) {
      case 'clear-cache':
        if (target === 'all') {
          await cacheService.clearAll();
          clearCache();
          response.message = 'All caches cleared successfully';
        } else if (target === 'api') {
          clearCache();
          response.message = 'API cache cleared successfully';
        } else if (target === 'db') {
          await cacheService.clearAll();
          response.message = 'Database cache cleared successfully';
        } else {
          await cacheService.clear(target);
          response.message = `Cache layer '${target}' cleared successfully`;
        }
        break;
        
      case 'reset-metrics':
        resetMetrics();
        response.message = 'Performance metrics reset successfully';
        break;
        
      case 'clear-rate-limit':
        clearRateLimitStore();
        response.message = 'Rate limit store cleared successfully';
        break;
        
      case 'optimize':
        // 执行系统优化
        await performSystemOptimization();
        response.message = 'System optimization completed';
        break;
        
      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid action'
        }, { status: 400 });
    }
    
    return NextResponse.json(response);
    
  } catch (error) {
    console.error('Performance management error:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to execute performance action',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

/**
 * 获取系统指标
 */
async function getSystemMetrics() {
  const memoryUsage = process.memoryUsage();
  const uptime = process.uptime();
  
  return {
    memory: {
      rss: formatBytes(memoryUsage.rss),
      heapTotal: formatBytes(memoryUsage.heapTotal),
      heapUsed: formatBytes(memoryUsage.heapUsed),
      external: formatBytes(memoryUsage.external),
      arrayBuffers: formatBytes(memoryUsage.arrayBuffers)
    },
    uptime: {
      seconds: uptime,
      formatted: formatUptime(uptime)
    },
    node: {
      version: process.version,
      platform: process.platform,
      arch: process.arch
    },
    environment: process.env.NODE_ENV || 'unknown'
  };
}

/**
 * 执行系统优化
 */
async function performSystemOptimization() {
  // 清理过期缓存
  await cacheService.clearAll();
  
  // 重置性能指标
  resetMetrics();
  
  // 强制垃圾回收（如果可用）
  if (global.gc) {
    global.gc();
  }
  
  // 清理速率限制存储
  clearRateLimitStore();
}

/**
 * 格式化字节数
 */
function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 格式化运行时间
 */
function formatUptime(seconds: number): string {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  const parts = [];
  if (days > 0) parts.push(`${days}d`);
  if (hours > 0) parts.push(`${hours}h`);
  if (minutes > 0) parts.push(`${minutes}m`);
  if (secs > 0) parts.push(`${secs}s`);
  
  return parts.join(' ') || '0s';
}
