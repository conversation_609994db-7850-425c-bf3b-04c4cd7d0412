import { NextRequest, NextResponse } from 'next/server';

/**
 * 生成分享图片 API
 * 为用户生成个性化的推广分享图片
 */

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userName = searchParams.get('user') || 'Friend';
    const referralCode = searchParams.get('ref') || 'DEMO';

    // 生成 SVG 图片
    const svg = generateShareImageSVG(userName, referralCode);
    
    return new NextResponse(svg, {
      headers: {
        'Content-Type': 'image/svg+xml',
        'Cache-Control': 'public, max-age=3600',
      },
    });

  } catch (error) {
    console.error('Generate share image error:', error);
    return NextResponse.json(
      { error: 'Failed to generate image' },
      { status: 500 }
    );
  }
}

function generateShareImageSVG(userName: string, referralCode: string): string {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://sociomint.com';
  
  return `
<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.95" />
      <stop offset="100%" style="stop-color:#f8fafc;stop-opacity:0.95" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="10" stdDeviation="20" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="800" height="600" fill="url(#bgGradient)"/>
  
  <!-- Decorative circles -->
  <circle cx="100" cy="100" r="60" fill="#ffffff" opacity="0.1"/>
  <circle cx="700" cy="500" r="80" fill="#ffffff" opacity="0.1"/>
  <circle cx="650" cy="150" r="40" fill="#ffffff" opacity="0.15"/>
  
  <!-- Main card -->
  <rect x="50" y="80" width="700" height="440" rx="20" ry="20" fill="url(#cardGradient)" filter="url(#shadow)"/>
  
  <!-- Logo area -->
  <rect x="80" y="110" width="60" height="60" rx="30" ry="30" fill="#667eea"/>
  <text x="110" y="150" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="24" font-weight="bold">S</text>
  
  <!-- Title -->
  <text x="160" y="135" fill="#1a202c" font-family="Arial, sans-serif" font-size="32" font-weight="bold">SocioMint</text>
  <text x="160" y="160" fill="#4a5568" font-family="Arial, sans-serif" font-size="18">(HAOX) 预售火热进行中！</text>
  
  <!-- Invitation text -->
  <text x="400" y="220" text-anchor="middle" fill="#2d3748" font-family="Arial, sans-serif" font-size="24" font-weight="bold">
    ${userName} 邀请您参与预售
  </text>
  
  <!-- Features -->
  <g transform="translate(100, 250)">
    <!-- Feature 1 -->
    <circle cx="20" cy="20" r="8" fill="#48bb78"/>
    <text x="40" y="26" fill="#2d3748" font-family="Arial, sans-serif" font-size="16">💎 革命性社交挖矿平台</text>
    
    <!-- Feature 2 -->
    <circle cx="20" cy="50" r="8" fill="#48bb78"/>
    <text x="40" y="56" fill="#2d3748" font-family="Arial, sans-serif" font-size="16">🎯 完成任务获得代币奖励</text>
    
    <!-- Feature 3 -->
    <circle cx="20" cy="80" r="8" fill="#48bb78"/>
    <text x="40" y="86" fill="#2d3748" font-family="Arial, sans-serif" font-size="16">🔥 早期投资者专享优惠</text>
  </g>
  
  <!-- Referral code -->
  <rect x="250" y="370" width="300" height="50" rx="25" ry="25" fill="#667eea"/>
  <text x="400" y="400" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="18" font-weight="bold">
    邀请码: ${referralCode}
  </text>
  
  <!-- Call to action -->
  <text x="400" y="450" text-anchor="middle" fill="#2d3748" font-family="Arial, sans-serif" font-size="16">
    立即访问: ${baseUrl}
  </text>
  
  <!-- QR Code placeholder -->
  <rect x="600" y="350" width="120" height="120" rx="10" ry="10" fill="#f7fafc" stroke="#e2e8f0" stroke-width="2"/>
  <text x="660" y="415" text-anchor="middle" fill="#a0aec0" font-family="Arial, sans-serif" font-size="12">扫码访问</text>
  
  <!-- Bottom decoration -->
  <rect x="0" y="550" width="800" height="50" fill="#667eea" opacity="0.1"/>
  <text x="400" y="580" text-anchor="middle" fill="#4a5568" font-family="Arial, sans-serif" font-size="14">
    SocioMint - 社交即挖矿 | 机会难得，立即参与！
  </text>
</svg>`.trim();
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userName, referralCode, format = 'svg' } = body;

    if (format === 'svg') {
      const svg = generateShareImageSVG(userName, referralCode);
      
      return new NextResponse(svg, {
        headers: {
          'Content-Type': 'image/svg+xml',
          'Cache-Control': 'public, max-age=3600',
        },
      });
    }

    // 如果需要其他格式（PNG, JPEG），可以在这里添加转换逻辑
    // 需要安装 sharp 或其他图片处理库
    
    return NextResponse.json(
      { error: 'Unsupported format' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Generate share image error:', error);
    return NextResponse.json(
      { error: 'Failed to generate image' },
      { status: 500 }
    );
  }
}
