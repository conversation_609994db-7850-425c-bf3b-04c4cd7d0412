import { NextRequest, NextResponse } from 'next/server';
import { readFile, access } from 'fs/promises';
import { join } from 'path';
import { constants } from 'fs';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const lang = searchParams.get('lang') || 'zh';

    // 验证语言参数
    if (!['zh', 'en'].includes(lang)) {
      return NextResponse.json(
        { error: 'Invalid language parameter' },
        { status: 400 }
      );
    }

    const filename = lang === 'en'
      ? 'HAOX_WHITEPAPER_V2_EN.md'
      : 'HAOX_WHITEPAPER_V2.md';

    const filePath = join(process.cwd(), 'docs', filename);

    // 检查文件是否存在
    try {
      await access(filePath, constants.F_OK);
    } catch (accessError) {
      console.error(`Whitepaper file not found: ${filePath}`, accessError);
      return NextResponse.json(
        { error: `Whitepaper file not found: ${filename}` },
        { status: 404 }
      );
    }

    // 读取文件内容
    const content = await readFile(filePath, 'utf-8');

    if (!content || content.trim().length === 0) {
      return NextResponse.json(
        { error: 'Whitepaper file is empty' },
        { status: 404 }
      );
    }

    return new NextResponse(content, {
      headers: {
        'Content-Type': 'text/markdown; charset=utf-8',
        'Content-Disposition': `inline; filename="${filename}"`,
        'Cache-Control': 'public, max-age=3600', // 缓存1小时
      },
    });
  } catch (error) {
    console.error('Error reading whitepaper:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
