import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

// 获取任务列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const platform = searchParams.get('platform');
    const status = searchParams.get('status') || 'active';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const sortBy = searchParams.get('sortBy') || 'created_at';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    const offset = (page - 1) * limit;

    // 构建查询
    let query = supabase
      .from('tasks')
      .select(`
        *,
        task_type:task_types(*),
        publisher:users!tasks_publisher_id_fkey(id, username),
        completions_count:task_completions(count),
        user_completion:task_completions!left(id, verification_status, submitted_at)
      `, { count: 'exact' });

    // 添加过滤条件
    if (status !== 'all') {
      query = query.eq('status', status);
    }

    if (platform) {
      query = query.eq('task_types.platform', platform);
    }

    // 如果指定了用户ID，添加用户完成状态过滤
    if (userId) {
      query = query.or(`user_completion.user_id.is.null,user_completion.user_id.eq.${userId}`);
    }

    // 排序
    const validSortFields = ['created_at', 'reward_amount', 'priority', 'end_time'];
    const sortField = validSortFields.includes(sortBy) ? sortBy : 'created_at';
    query = query.order(sortField, { ascending: sortOrder === 'asc' });

    // 分页
    query = query.range(offset, offset + limit - 1);

    const { data: tasks, error, count } = await query;

    if (error) {
      throw error;
    }

    // 处理任务数据
    const processedTasks = (tasks || []).map(task => {
      const userHasCompleted = userId && task.user_completion && 
        task.user_completion.some((completion: any) => completion.user_id === userId);

      return {
        id: task.id,
        title: task.title,
        description: task.description,
        target_url: task.target_url,
        reward_amount: task.reward_amount,
        remaining_budget: task.remaining_budget,
        max_completions: task.max_completions,
        current_completions: task.current_completions,
        end_time: task.end_time,
        status: task.status,
        priority: task.priority,
        tags: task.tags,
        created_at: task.created_at,
        task_type: task.task_type,
        publisher: task.publisher,
        completions_count: task.completions_count?.[0]?.count || 0,
        user_has_completed: userHasCompleted,
        user_completion_status: userHasCompleted ? 
          task.user_completion.find((c: any) => c.user_id === userId)?.verification_status : null,
        can_complete: !userHasCompleted && 
          task.status === 'active' && 
          task.remaining_budget >= task.reward_amount &&
          (!task.max_completions || task.current_completions < task.max_completions) &&
          (!task.end_time || new Date(task.end_time) > new Date())
      };
    });

    return NextResponse.json({
      success: true,
      data: {
        tasks: processedTasks,
        pagination: {
          page,
          limit,
          total: count || 0,
          total_pages: Math.ceil((count || 0) / limit),
          has_next: offset + limit < (count || 0),
          has_prev: page > 1
        }
      }
    });

  } catch (error) {
    console.error('Get tasks error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// 完成任务
export async function POST(request: NextRequest) {
  try {
    const {
      taskId,
      userId,
      submissionData
    } = await request.json();

    if (!taskId || !userId || !submissionData) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // 1. 验证任务是否存在且可完成
    const { data: task, error: taskError } = await supabase
      .from('tasks')
      .select(`
        *,
        task_type:task_types(*)
      `)
      .eq('id', taskId)
      .single();

    if (taskError || !task) {
      return NextResponse.json(
        { error: 'Task not found' },
        { status: 404 }
      );
    }

    // 2. 检查任务状态
    if (task.status !== 'active') {
      return NextResponse.json(
        { error: 'Task is not active' },
        { status: 400 }
      );
    }

    // 3. 检查是否已过期
    if (task.end_time && new Date(task.end_time) <= new Date()) {
      return NextResponse.json(
        { error: 'Task has expired' },
        { status: 400 }
      );
    }

    // 4. 检查预算是否足够
    if (task.remaining_budget < task.reward_amount) {
      return NextResponse.json(
        { error: 'Insufficient task budget' },
        { status: 400 }
      );
    }

    // 5. 检查是否达到最大完成次数
    if (task.max_completions && task.current_completions >= task.max_completions) {
      return NextResponse.json(
        { error: 'Task has reached maximum completions' },
        { status: 400 }
      );
    }

    // 6. 检查用户是否已完成过此任务
    const { data: existingCompletion, error: completionError } = await supabase
      .from('task_completions')
      .select('id')
      .eq('task_id', taskId)
      .eq('user_id', userId)
      .single();

    if (existingCompletion) {
      return NextResponse.json(
        { error: 'You have already completed this task' },
        { status: 400 }
      );
    }

    // 7. 验证提交数据
    const requiredFields = task.task_type.required_fields || {};
    for (const [fieldName, fieldConfig] of Object.entries(requiredFields)) {
      const config = fieldConfig as any;
      if (config.required && !submissionData[fieldName]) {
        return NextResponse.json(
          { error: `Missing required field: ${config.label || fieldName}` },
          { status: 400 }
        );
      }
    }

    // 8. 创建任务完成记录
    const { data: completion, error: createError } = await supabase
      .from('task_completions')
      .insert({
        task_id: taskId,
        user_id: userId,
        submission_data: submissionData,
        reward_amount: task.reward_amount,
        verification_status: task.task_type.verification_method === 'api' ? 'approved' : 'pending'
      })
      .select()
      .single();

    if (createError) {
      console.error('Error creating task completion:', createError);
      return NextResponse.json(
        { error: 'Failed to submit task completion' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      completion: {
        id: completion.id,
        verification_status: completion.verification_status,
        reward_amount: completion.reward_amount,
        submitted_at: completion.submitted_at
      },
      message: completion.verification_status === 'approved' ? 
        'Task completed and reward granted!' : 
        'Task submitted for verification'
    });

  } catch (error) {
    console.error('Complete task error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
