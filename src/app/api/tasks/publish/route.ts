import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

export async function POST(request: NextRequest) {
  try {
    const {
      publisherId,
      taskTypeId,
      title,
      description,
      targetUrl,
      targetIdentifier,
      rewardAmount,
      totalBudget,
      maxCompletions,
      endTime,
      verificationRequirements,
      tags
    } = await request.json();

    if (!publisherId || !taskTypeId || !title || !description || !targetUrl || !rewardAmount || !totalBudget) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // 1. 验证发布者是否存在
    const { data: publisher, error: publisherError } = await supabase
      .from('users')
      .select('id')
      .eq('id', publisherId)
      .single();

    if (publisherError || !publisher) {
      return NextResponse.json(
        { error: 'Publisher not found' },
        { status: 404 }
      );
    }

    // 2. 验证任务类型是否存在
    const { data: taskType, error: taskTypeError } = await supabase
      .from('task_types')
      .select('*')
      .eq('id', taskTypeId)
      .eq('is_active', true)
      .single();

    if (taskTypeError || !taskType) {
      return NextResponse.json(
        { error: 'Invalid task type' },
        { status: 400 }
      );
    }

    // 3. 验证奖励金额是否在允许范围内
    if (rewardAmount < taskType.min_reward || rewardAmount > taskType.max_reward) {
      return NextResponse.json(
        { 
          error: `Reward amount must be between ${taskType.min_reward} and ${taskType.max_reward} HAOX`,
          min_reward: taskType.min_reward,
          max_reward: taskType.max_reward
        },
        { status: 400 }
      );
    }

    // 4. 验证总预算是否足够
    const estimatedCompletions = maxCompletions || Math.floor(totalBudget / rewardAmount);
    if (totalBudget < rewardAmount) {
      return NextResponse.json(
        { error: 'Total budget must be at least equal to reward amount' },
        { status: 400 }
      );
    }

    // 5. 创建任务
    const { data: task, error: createError } = await supabase
      .from('tasks')
      .insert({
        publisher_id: publisherId,
        task_type_id: taskTypeId,
        title,
        description,
        target_url: targetUrl,
        target_identifier: targetIdentifier,
        reward_amount: rewardAmount,
        total_budget: totalBudget,
        remaining_budget: totalBudget,
        max_completions: maxCompletions,
        end_time: endTime,
        verification_requirements: verificationRequirements,
        tags: tags || [],
        status: 'active'
      })
      .select(`
        *,
        task_type:task_types(*)
      `)
      .single();

    if (createError) {
      console.error('Error creating task:', createError);
      return NextResponse.json(
        { error: 'Failed to create task' },
        { status: 500 }
      );
    }

    // 6. 创建资金托管记录
    const { error: escrowError } = await supabase
      .from('task_escrows')
      .insert({
        task_id: task.id,
        publisher_id: publisherId,
        escrow_amount: totalBudget,
        status: 'locked'
      });

    if (escrowError) {
      console.error('Error creating escrow:', escrowError);
      // 不阻止任务创建，只记录错误
    }

    return NextResponse.json({
      success: true,
      task: {
        id: task.id,
        title: task.title,
        description: task.description,
        reward_amount: task.reward_amount,
        total_budget: task.total_budget,
        max_completions: task.max_completions,
        estimated_completions: estimatedCompletions,
        task_type: task.task_type,
        status: task.status,
        created_at: task.created_at
      },
      message: 'Task published successfully'
    });

  } catch (error) {
    console.error('Publish task error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// 获取任务类型列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const platform = searchParams.get('platform');

    let query = supabase
      .from('task_types')
      .select('*')
      .eq('is_active', true)
      .order('platform', { ascending: true });

    if (platform) {
      query = query.eq('platform', platform);
    }

    const { data: taskTypes, error } = await query;

    if (error) {
      throw error;
    }

    // 按平台分组
    const groupedTaskTypes = (taskTypes || []).reduce((acc, taskType) => {
      const platform = taskType.platform;
      if (!acc[platform]) {
        acc[platform] = [];
      }
      acc[platform].push({
        id: taskType.id,
        type_name: taskType.type_name,
        display_name: taskType.display_name,
        description: taskType.description,
        base_reward: taskType.base_reward,
        min_reward: taskType.min_reward,
        max_reward: taskType.max_reward,
        verification_method: taskType.verification_method,
        required_fields: taskType.required_fields
      });
      return acc;
    }, {} as Record<string, any[]>);

    return NextResponse.json({
      success: true,
      data: {
        task_types: taskTypes || [],
        grouped_task_types: groupedTaskTypes,
        platforms: Object.keys(groupedTaskTypes)
      }
    });

  } catch (error) {
    console.error('Get task types error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
