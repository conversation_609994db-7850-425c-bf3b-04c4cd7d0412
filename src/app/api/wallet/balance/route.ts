/**
 * 钱包余额API路由
 * 获取用户钱包余额信息
 */

import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import jwt from 'jsonwebtoken';
import {
  with<PERSON>rror<PERSON>and<PERSON>,
  createSuccessResponse,
  createErrorResponse,
  createApiError,
  ApiErrorCode
} from '@/lib/api-error-handler';
import { custodyWalletService } from '@/services/wallet/CustodyWalletService';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

interface JWTPayload {
  userId: number;
  username?: string;
  firstName: string;
  lastName?: string;
  photoUrl?: string;
  iat: number;
  exp: number;
}

export async function GET(request: NextRequest) {
  try {
    // 验证用户认证
    const cookieStore = cookies();
    const token = cookieStore.get('telegram-auth-token')?.value;

    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const decoded = jwt.verify(token, JWT_SECRET) as JWTPayload;
    const userId = decoded.userId;

    // 获取钱包余额
    const result = await custodyWalletService.getBalance(userId);

    if (!result.success) {
      return NextResponse.json(
        { error: result.error || 'Failed to get balance' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      balance: result.balance,
      pendingRewards: result.pendingRewards || [],
    });
  } catch (error) {
    console.error('Balance API error:', error);
    
    if (error instanceof jwt.JsonWebTokenError) {
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // 验证用户认证
    const cookieStore = cookies();
    const token = cookieStore.get('telegram-auth-token')?.value;

    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const decoded = jwt.verify(token, JWT_SECRET) as JWTPayload;
    const userId = decoded.userId;

    // 刷新余额
    const result = await custodyWalletService.refreshBalance(userId);

    if (!result.success) {
      return NextResponse.json(
        { error: result.error || 'Failed to refresh balance' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      balance: result.balance,
      message: 'Balance refreshed successfully',
    });
  } catch (error) {
    console.error('Balance refresh API error:', error);
    
    if (error instanceof jwt.JsonWebTokenError) {
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
