/**
 * 钱包转账API路由
 * 处理代币转账请求
 */

import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import jwt from 'jsonwebtoken';
import { custodyWalletService } from '@/services/wallet/CustodyWalletService';
import { validateBSCAddress } from '@/services/wallet/utils';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

interface JWTPayload {
  userId: number;
  username?: string;
  firstName: string;
  lastName?: string;
  photoUrl?: string;
  iat: number;
  exp: number;
}

interface TransferRequest {
  toAddress: string;
  amount: string;
  tokenType: 'HAOX' | 'BNB';
  memo?: string;
  verificationCode?: string;
}

export async function POST(request: NextRequest) {
  try {
    // 验证用户认证
    const cookieStore = cookies();
    const token = cookieStore.get('telegram-auth-token')?.value;

    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const decoded = jwt.verify(token, JWT_SECRET) as JWTPayload;
    const userId = decoded.userId;

    // 解析请求数据
    const transferData: TransferRequest = await request.json();

    // 验证请求数据
    if (!transferData.toAddress || !transferData.amount || !transferData.tokenType) {
      return NextResponse.json(
        { error: 'Missing required fields: toAddress, amount, tokenType' },
        { status: 400 }
      );
    }

    // 验证地址格式
    if (!validateBSCAddress(transferData.toAddress)) {
      return NextResponse.json(
        { error: 'Invalid BSC address format' },
        { status: 400 }
      );
    }

    // 验证金额
    const amount = parseFloat(transferData.amount);
    if (isNaN(amount) || amount <= 0) {
      return NextResponse.json(
        { error: 'Invalid amount' },
        { status: 400 }
      );
    }

    // 验证代币类型
    if (!['HAOX', 'BNB'].includes(transferData.tokenType)) {
      return NextResponse.json(
        { error: 'Invalid token type' },
        { status: 400 }
      );
    }

    // 执行转账
    const result = await custodyWalletService.transfer(userId, {
      toAddress: transferData.toAddress,
      amount: transferData.amount,
      tokenType: transferData.tokenType,
      memo: transferData.memo,
      verificationCode: transferData.verificationCode,
    });

    if (!result.success) {
      return NextResponse.json(
        { error: result.error || 'Transfer failed' },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      transactionId: result.transactionId,
      txHash: result.txHash,
      estimatedConfirmationTime: result.estimatedConfirmationTime || 30,
      message: 'Transfer initiated successfully',
    });
  } catch (error) {
    console.error('Transfer API error:', error);
    
    if (error instanceof jwt.JsonWebTokenError) {
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // 验证用户认证
    const cookieStore = cookies();
    const token = cookieStore.get('telegram-auth-token')?.value;

    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const decoded = jwt.verify(token, JWT_SECRET) as JWTPayload;
    const userId = decoded.userId;

    // 获取转账历史
    const result = await custodyWalletService.getTransactionHistory(userId, {
      type: 'transfer',
      limit: 50,
    });

    if (!result.success) {
      return NextResponse.json(
        { error: result.error || 'Failed to get transfer history' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      transactions: result.transactions,
      totalCount: result.totalCount,
      hasMore: result.hasMore,
    });
  } catch (error) {
    console.error('Transfer history API error:', error);
    
    if (error instanceof jwt.JsonWebTokenError) {
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
