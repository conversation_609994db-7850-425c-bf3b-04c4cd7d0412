/**
 * 钱包提现API路由
 * 处理外部钱包提现请求
 */

import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import jwt from 'jsonwebtoken';
import { custodyWalletService } from '@/services/wallet/CustodyWalletService';
import { validateBSCAddress } from '@/services/wallet/utils';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

interface JWTPayload {
  userId: number;
  username?: string;
  firstName: string;
  lastName?: string;
  photoUrl?: string;
  iat: number;
  exp: number;
}

interface WithdrawRequest {
  toAddress: string;
  amount: string;
  verificationCode: string;
  emailConfirmationCode?: string;
}

export async function POST(request: NextRequest) {
  try {
    // 验证用户认证
    const cookieStore = cookies();
    const token = cookieStore.get('telegram-auth-token')?.value;

    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const decoded = jwt.verify(token, JWT_SECRET) as JWTPayload;
    const userId = decoded.userId;

    // 解析请求数据
    const withdrawData: WithdrawRequest = await request.json();

    // 验证请求数据
    if (!withdrawData.toAddress || !withdrawData.amount || !withdrawData.verificationCode) {
      return NextResponse.json(
        { error: 'Missing required fields: toAddress, amount, verificationCode' },
        { status: 400 }
      );
    }

    // 验证地址格式
    if (!validateBSCAddress(withdrawData.toAddress)) {
      return NextResponse.json(
        { error: 'Invalid BSC address format' },
        { status: 400 }
      );
    }

    // 验证金额
    const amount = parseFloat(withdrawData.amount);
    if (isNaN(amount) || amount <= 0) {
      return NextResponse.json(
        { error: 'Invalid amount' },
        { status: 400 }
      );
    }

    // 验证最小提现金额
    if (amount < 10) {
      return NextResponse.json(
        { error: 'Minimum withdrawal amount is 10 HAOX' },
        { status: 400 }
      );
    }

    // 执行提现
    const result = await custodyWalletService.withdraw(userId, {
      toAddress: withdrawData.toAddress,
      amount: withdrawData.amount,
      verificationCode: withdrawData.verificationCode,
      emailConfirmationCode: withdrawData.emailConfirmationCode,
    });

    if (!result.success) {
      return NextResponse.json(
        { error: result.error || 'Withdrawal failed' },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      transactionId: result.transactionId,
      requiresAdditionalVerification: result.requiresAdditionalVerification || false,
      message: result.requiresAdditionalVerification 
        ? 'Withdrawal requires additional verification'
        : 'Withdrawal initiated successfully',
    });
  } catch (error) {
    console.error('Withdraw API error:', error);
    
    if (error instanceof jwt.JsonWebTokenError) {
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // 验证用户认证
    const cookieStore = cookies();
    const token = cookieStore.get('telegram-auth-token')?.value;

    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const decoded = jwt.verify(token, JWT_SECRET) as JWTPayload;
    const userId = decoded.userId;

    // 获取提现历史
    const result = await custodyWalletService.getTransactionHistory(userId, {
      type: 'withdrawal',
      limit: 50,
    });

    if (!result.success) {
      return NextResponse.json(
        { error: result.error || 'Failed to get withdrawal history' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      transactions: result.transactions,
      totalCount: result.totalCount,
      hasMore: result.hasMore,
    });
  } catch (error) {
    console.error('Withdrawal history API error:', error);
    
    if (error instanceof jwt.JsonWebTokenError) {
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
