import { NextRequest, NextResponse } from 'next/server';
import { fortuneExchangeService, EXCHANGE_CONFIG } from '@/lib/services/fortuneExchangeService';

export async function POST(request: NextRequest) {
  try {
    const { userId, amount, walletAddress } = await request.json();

    // 验证必需参数
    if (!userId) {
      return NextResponse.json(
        { success: false, message: '用户ID必需' },
        { status: 400 }
      );
    }

    if (!amount || typeof amount !== 'number' || amount <= 0) {
      return NextResponse.json(
        { success: false, message: '提现金额必须为正数' },
        { status: 400 }
      );
    }

    if (!walletAddress) {
      return NextResponse.json(
        { success: false, message: '钱包地址必需' },
        { status: 400 }
      );
    }

    // 验证提现金额范围
    if (amount < EXCHANGE_CONFIG.MIN_WITHDRAWAL || amount > EXCHANGE_CONFIG.MAX_WITHDRAWAL) {
      return NextResponse.json(
        { 
          success: false, 
          message: `提现金额必须在${EXCHANGE_CONFIG.MIN_WITHDRAWAL}-${EXCHANGE_CONFIG.MAX_WITHDRAWAL}福气之间` 
        },
        { status: 400 }
      );
    }

    // 执行提现
    const transaction = await fortuneExchangeService.withdraw(userId, amount, walletAddress);

    // 计算预计处理时间
    const isSmallAmount = amount <= EXCHANGE_CONFIG.SMALL_AMOUNT_THRESHOLD;
    const estimatedProcessTime = isSmallAmount ? '5分钟内' : '24小时内（需人工审核）';

    return NextResponse.json({
      success: true,
      message: '提现请求提交成功',
      data: {
        transactionId: transaction.id,
        amount: transaction.amount,
        fee: transaction.fee,
        netAmount: transaction.netAmount,
        status: transaction.status,
        walletAddress: transaction.walletAddress,
        estimatedProcessTime,
        isSmallAmount,
        createdAt: transaction.createdAt
      }
    });

  } catch (error) {
    console.error('提现失败:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        message: error instanceof Error ? error.message : '提现失败，请稍后重试' 
      },
      { status: 500 }
    );
  }
}

// 获取提现配置信息
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const userId = url.searchParams.get('userId');

    let dailyLimit = null;
    if (userId) {
      dailyLimit = await fortuneExchangeService.getDailyWithdrawalLimit(userId);
    }

    return NextResponse.json({
      success: true,
      data: {
        exchangeRate: EXCHANGE_CONFIG.EXCHANGE_RATE,
        minWithdrawal: EXCHANGE_CONFIG.MIN_WITHDRAWAL,
        maxWithdrawal: EXCHANGE_CONFIG.MAX_WITHDRAWAL,
        withdrawalFeeRate: EXCHANGE_CONFIG.WITHDRAWAL_FEE_RATE,
        minWithdrawalFee: EXCHANGE_CONFIG.MIN_WITHDRAWAL_FEE,
        dailyWithdrawalLimit: EXCHANGE_CONFIG.DAILY_WITHDRAWAL_LIMIT,
        smallAmountThreshold: EXCHANGE_CONFIG.SMALL_AMOUNT_THRESHOLD,
        smallAmountProcessTime: '5分钟内',
        largeAmountProcessTime: '24小时内（需人工审核）',
        dailyLimit,
        instructions: [
          '1. 输入提现金额和钱包地址',
          '2. 系统自动计算手续费',
          '3. 小额提现5分钟内到账',
          '4. 大额提现需人工审核，24小时内处理'
        ]
      }
    });
  } catch (error) {
    console.error('获取提现配置失败:', error);
    
    return NextResponse.json(
      { success: false, message: '获取提现配置失败' },
      { status: 500 }
    );
  }
}
