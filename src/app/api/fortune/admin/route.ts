/**
 * 福气系统管理API
 * 仅供管理员使用，用于系统管理和测试
 */

import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { FortuneService } from '@/services/fortune/FortuneService';

/**
 * @api {POST} /api/fortune/admin/adjust 管理员调整用户福气
 * @apiDescription 管理员手动调整用户福气余额（仅用于测试和特殊情况）
 */
export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // 验证用户身份
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // 简单的管理员验证（在生产环境中应该有更严格的验证）
    const { data: userData } = await supabase
      .from('users')
      .select('email')
      .eq('id', user.id)
      .single();

    // 这里可以配置管理员邮箱列表
    const adminEmails = ['<EMAIL>', '<EMAIL>'];
    if (!userData?.email || !adminEmails.includes(userData.email)) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    const { targetUserId, amount, reason, type = 'admin_adjust' } = await request.json();

    if (!targetUserId || !amount || !reason) {
      return NextResponse.json({ 
        error: 'Missing required parameters: targetUserId, amount, reason' 
      }, { status: 400 });
    }

    // 验证目标用户存在
    const { data: targetUser } = await supabase
      .from('users')
      .select('id, email, username')
      .eq('id', targetUserId)
      .single();

    if (!targetUser) {
      return NextResponse.json({ error: 'Target user not found' }, { status: 404 });
    }

    let transactionId;
    const description = `管理员调整: ${reason}`;

    if (amount > 0) {
      // 增加福气
      transactionId = await FortuneService.addFortune(
        targetUserId,
        amount,
        type,
        user.id, // 管理员ID作为reference
        description
      );
    } else {
      // 扣除福气
      transactionId = await FortuneService.deductFortune(
        targetUserId,
        Math.abs(amount),
        type,
        user.id,
        description
      );
    }

    // 获取调整后的账户信息
    const updatedAccount = await FortuneService.getUserFortune(targetUserId);

    return NextResponse.json({
      success: true,
      message: `成功调整用户 ${targetUser.username || targetUser.email} 的福气`,
      data: {
        transactionId,
        targetUser: {
          id: targetUser.id,
          email: targetUser.email,
          username: targetUser.username
        },
        adjustment: {
          amount,
          reason,
          type
        },
        newBalance: updatedAccount?.available_fortune,
        newLevel: updatedAccount?.fortune_level,
        newLevelName: updatedAccount?.fortune_level_name
      }
    });

  } catch (error) {
    console.error('Admin fortune adjustment error:', error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Internal server error',
        code: 'ADMIN_ERROR'
      },
      { status: 500 }
    );
  }
}

/**
 * @api {GET} /api/fortune/admin/stats 获取福气系统统计信息
 * @apiDescription 获取整个福气系统的统计数据
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // 验证用户身份
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // 验证管理员权限
    const { data: userData } = await supabase
      .from('users')
      .select('email')
      .eq('id', user.id)
      .single();

    const adminEmails = ['<EMAIL>', '<EMAIL>'];
    if (!userData?.email || !adminEmails.includes(userData.email)) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    // 获取用户总数和福气账户统计
    const { data: userStats } = await supabase
      .from('users')
      .select('id, available_fortune, fortune_level, created_at')
      .not('available_fortune', 'is', null);

    // 计算基本统计
    const totalUsers = userStats?.length || 0;
    const totalFortune = userStats?.reduce((sum, user) => sum + (user.available_fortune || 0), 0) || 0;
    const averageFortune = totalUsers > 0 ? totalFortune / totalUsers : 0;

    // 按等级统计用户分布
    const levelDistribution = userStats?.reduce((dist, user) => {
      const level = user.fortune_level || 1;
      dist[level] = (dist[level] || 0) + 1;
      return dist;
    }, {} as Record<number, number>) || {};

    // 获取交易统计
    const { data: transactionStats } = await supabase
      .from('fortune_transactions')
      .select('transaction_type, amount, created_at');

    // 按交易类型统计
    const transactionTypeStats = transactionStats?.reduce((stats, tx) => {
      const type = tx.transaction_type;
      if (!stats[type]) {
        stats[type] = { count: 0, totalAmount: 0 };
      }
      stats[type].count++;
      stats[type].totalAmount += tx.amount;
      return stats;
    }, {} as Record<string, { count: number; totalAmount: number }>) || {};

    // 获取今日统计
    const today = new Date().toISOString().split('T')[0];
    const { data: todayTransactions } = await supabase
      .from('fortune_transactions')
      .select('amount')
      .gte('created_at', `${today}T00:00:00.000Z`)
      .lt('created_at', `${today}T23:59:59.999Z`);

    const todayTotalAmount = todayTransactions?.reduce((sum, tx) => sum + tx.amount, 0) || 0;

    // 获取签到统计
    const { data: checkinStats } = await supabase
      .from('daily_checkins')
      .select('user_id, checkin_date')
      .eq('checkin_date', today);

    return NextResponse.json({
      success: true,
      data: {
        // 用户统计
        userStats: {
          totalUsers,
          totalFortune,
          averageFortune: Math.round(averageFortune * 100) / 100,
          levelDistribution
        },
        
        // 交易统计
        transactionStats: {
          totalTransactions: transactionStats?.length || 0,
          transactionTypeStats,
          todayTransactions: todayTransactions?.length || 0,
          todayTotalAmount
        },
        
        // 今日活跃统计
        todayStats: {
          checkins: checkinStats?.length || 0,
          activeUsers: new Set(todayTransactions?.map(tx => tx.user_id)).size
        },
        
        // 系统健康状态
        systemHealth: {
          status: 'healthy',
          lastUpdated: new Date().toISOString()
        }
      }
    });

  } catch (error) {
    console.error('Get fortune admin stats error:', error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Internal server error',
        code: 'ADMIN_STATS_ERROR'
      },
      { status: 500 }
    );
  }
}

// 配置运行时
export const runtime = 'nodejs';
