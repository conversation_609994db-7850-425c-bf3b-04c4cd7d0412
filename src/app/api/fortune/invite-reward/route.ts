/**
 * 福气系统邀请奖励API
 * 处理基于福气系统的邀请奖励发放和查询
 */

import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { FortuneService } from '@/services/fortune/FortuneService';

/**
 * @api {POST} /api/fortune/invite-reward 发放邀请奖励
 * @apiDescription 处理邀请成功后的福气奖励发放
 */
export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // 验证用户身份
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { inviteeId, inviteCode } = await request.json();

    if (!inviteeId) {
      return NextResponse.json({ error: 'Invitee ID is required' }, { status: 400 });
    }

    // 验证被邀请人是否存在且有效
    const { data: invitee, error: inviteeError } = await supabase
      .from('users')
      .select('id, email')
      .eq('id', inviteeId)
      .single();

    if (inviteeError || !invitee) {
      return NextResponse.json({ error: 'Invalid invitee' }, { status: 400 });
    }

    // 检查是否已经处理过此邀请
    const { data: existingReward } = await supabase
      .from('fortune_transactions')
      .select('id')
      .eq('user_id', user.id)
      .eq('transaction_type', 'invite_reward')
      .eq('reference_id', inviteeId)
      .single();

    if (existingReward) {
      return NextResponse.json({ 
        error: 'Invitation reward already processed',
        code: 'ALREADY_PROCESSED'
      }, { status: 400 });
    }

    // 防止自己邀请自己
    if (user.id === inviteeId) {
      return NextResponse.json({ 
        error: 'Cannot invite yourself',
        code: 'SELF_INVITATION'
      }, { status: 400 });
    }

    // 处理邀请奖励（使用福气系统）
    const result = await FortuneService.processInviteReward(user.id, inviteeId);

    // 记录邀请关系（如果需要）
    await supabase
      .from('user_invitations')
      .upsert({
        inviter_id: user.id,
        invitee_id: inviteeId,
        invite_code: inviteCode,
        status: 'completed',
        reward_processed: true,
        processed_at: new Date().toISOString()
      });

    return NextResponse.json({
      success: true,
      message: '邀请奖励发放成功！福气满满！',
      data: {
        baseReward: result.baseReward,
        milestoneReward: result.milestoneReward,
        totalReward: result.baseReward + result.milestoneReward,
        totalInvitations: result.totalInvitations,
        nextMilestone: result.nextMilestone,
        fortuneUnit: '福气'
      }
    });

  } catch (error) {
    console.error('Fortune invite reward error:', error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Internal server error',
        code: 'PROCESSING_ERROR'
      },
      { status: 500 }
    );
  }
}

/**
 * @api {GET} /api/fortune/invite-reward 获取邀请奖励统计
 * @apiDescription 获取用户的邀请奖励统计和福气账户信息
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // 验证用户身份
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // 获取用户福气账户信息
    const userFortune = await FortuneService.getUserFortune(user.id);

    if (!userFortune) {
      // 如果用户没有福气账户，创建一个
      const newFortune = await FortuneService.createUserFortune(user.id);
      
      return NextResponse.json({
        success: true,
        data: {
          totalInvitations: 0,
          successfulInvitations: 0,
          totalRewards: 0,
          availableFortune: 0,
          fortuneLevel: 1,
          fortuneLevelName: '初来乍到',
          nextMilestone: { target: 5, reward: 10000 },
          recentInvitations: []
        }
      });
    }

    // 计算总邀请奖励
    const { data: inviteRewards } = await supabase
      .from('fortune_transactions')
      .select('amount, created_at, description')
      .eq('user_id', user.id)
      .in('transaction_type', ['invite_reward', 'invite_milestone_5', 'invite_milestone_10'])
      .order('created_at', { ascending: false });

    const totalRewards = inviteRewards?.reduce((sum, reward) => sum + reward.amount, 0) || 0;

    // 获取最近的邀请记录
    const { data: recentInvitations } = await supabase
      .from('user_invitations')
      .select(`
        invitee_id,
        invite_code,
        status,
        created_at,
        users!invitee_id (
          email,
          telegram_username
        )
      `)
      .eq('inviter_id', user.id)
      .eq('reward_processed', true)
      .order('created_at', { ascending: false })
      .limit(10);

    // 确定下一个里程碑
    let nextMilestone;
    if (userFortune.successful_invitations < 5) {
      nextMilestone = { target: 5, reward: 10000 };
    } else if (userFortune.successful_invitations < 10) {
      nextMilestone = { target: 10, reward: 50000 };
    }

    return NextResponse.json({
      success: true,
      data: {
        // 邀请统计
        totalInvitations: userFortune.total_invitations,
        successfulInvitations: userFortune.successful_invitations,
        totalRewards: totalRewards,
        
        // 福气账户信息
        availableFortune: userFortune.available_fortune,
        lockedFortune: userFortune.locked_fortune,
        totalFortune: userFortune.available_fortune + userFortune.locked_fortune,
        fortuneLevel: userFortune.fortune_level,
        fortuneLevelName: userFortune.fortune_level_name,
        
        // 里程碑信息
        nextMilestone,
        milestoneProgress: {
          current: userFortune.successful_invitations,
          target: nextMilestone?.target || 10,
          percentage: nextMilestone ? (userFortune.successful_invitations / nextMilestone.target) * 100 : 100
        },
        
        // 最近邀请记录
        recentInvitations: recentInvitations?.map(inv => ({
          inviteeId: inv.invitee_id,
          inviteCode: inv.invite_code,
          status: inv.status,
          createdAt: inv.created_at,
          inviteeInfo: {
            email: inv.users?.email,
            telegramUsername: inv.users?.telegram_username
          }
        })) || [],
        
        // 奖励历史
        rewardHistory: inviteRewards?.slice(0, 5).map(reward => ({
          amount: reward.amount,
          description: reward.description,
          createdAt: reward.created_at
        })) || []
      }
    });

  } catch (error) {
    console.error('Get fortune invite stats error:', error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Internal server error',
        code: 'FETCH_ERROR'
      },
      { status: 500 }
    );
  }
}

// 配置运行时
export const runtime = 'nodejs';
