/**
 * 福气账户管理API
 * 处理福气账户的查询、初始化和基本操作
 */

import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { FortuneService } from '@/services/fortune/FortuneService';

/**
 * @api {GET} /api/fortune/account 获取用户福气账户信息
 * @apiDescription 获取用户的完整福气账户信息，包括余额、等级、统计等
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // 验证用户身份
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // 获取用户福气账户信息
    let fortuneAccount = await FortuneService.getUserFortune(user.id);

    // 如果用户没有福气账户，初始化一个
    if (!fortuneAccount || fortuneAccount.available_fortune === undefined) {
      fortuneAccount = await FortuneService.initializeUserFortune(user.id);
    }

    // 获取最近的交易记录
    const recentTransactions = await FortuneService.getFortuneTransactions(user.id, 10);

    // 计算总福气
    const totalFortune = fortuneAccount.available_fortune + fortuneAccount.locked_fortune;

    // 获取福气等级信息
    const { data: levelInfo } = await supabase
      .from('fortune_levels')
      .select('*')
      .eq('level', fortuneAccount.fortune_level)
      .single();

    // 获取下一等级信息
    const { data: nextLevelInfo } = await supabase
      .from('fortune_levels')
      .select('*')
      .eq('level', fortuneAccount.fortune_level + 1)
      .single();

    return NextResponse.json({
      success: true,
      data: {
        // 基本账户信息
        userId: fortuneAccount.id,
        availableFortune: fortuneAccount.available_fortune,
        lockedFortune: fortuneAccount.locked_fortune,
        totalFortune: totalFortune,
        
        // 统计信息
        totalEarned: fortuneAccount.total_fortune_earned,
        totalSpent: fortuneAccount.total_fortune_spent,
        
        // 等级信息
        currentLevel: {
          level: fortuneAccount.fortune_level,
          name: fortuneAccount.fortune_level_name,
          minFortune: levelInfo?.min_fortune || 0,
          maxFortune: levelInfo?.max_fortune,
          benefits: levelInfo?.benefits || []
        },
        
        // 下一等级信息
        nextLevel: nextLevelInfo ? {
          level: nextLevelInfo.level,
          name: nextLevelInfo.level_name,
          minFortune: nextLevelInfo.min_fortune,
          requiredFortune: nextLevelInfo.min_fortune - totalFortune,
          progress: totalFortune / nextLevelInfo.min_fortune * 100
        } : null,
        
        // 签到信息
        checkinInfo: {
          consecutiveDays: fortuneAccount.consecutive_checkin_days,
          totalDays: fortuneAccount.total_checkin_days,
          lastCheckinDate: fortuneAccount.last_checkin_date
        },
        
        // 最近交易记录
        recentTransactions: recentTransactions.map(tx => ({
          id: tx.id,
          type: tx.transaction_type,
          amount: tx.amount,
          description: tx.description,
          createdAt: tx.created_at
        })),
        
        // 账户创建时间
        createdAt: fortuneAccount.created_at,
        updatedAt: fortuneAccount.updated_at
      }
    });

  } catch (error) {
    console.error('Get fortune account error:', error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Internal server error',
        code: 'ACCOUNT_ERROR'
      },
      { status: 500 }
    );
  }
}

/**
 * @api {POST} /api/fortune/account/initialize 初始化用户福气账户
 * @apiDescription 为新用户初始化福气账户，发放注册奖励
 */
export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // 验证用户身份
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // 检查是否已经初始化过
    const existingAccount = await FortuneService.getUserFortune(user.id);
    if (existingAccount && existingAccount.available_fortune !== undefined) {
      return NextResponse.json({
        success: true,
        message: '福气账户已存在',
        data: existingAccount
      });
    }

    // 初始化福气账户
    const fortuneAccount = await FortuneService.initializeUserFortune(user.id);

    // 发放注册奖励
    const registrationReward = 50; // 注册奖励50福气
    await FortuneService.addFortune(
      user.id,
      registrationReward,
      'registration_bonus',
      undefined,
      `新用户注册奖励 +${registrationReward}福气`
    );

    // 获取更新后的账户信息
    const updatedAccount = await FortuneService.getUserFortune(user.id);

    return NextResponse.json({
      success: true,
      message: `福气账户初始化成功！获得${registrationReward}福气注册奖励！`,
      data: {
        userId: updatedAccount?.id,
        availableFortune: updatedAccount?.available_fortune,
        registrationReward: registrationReward,
        fortuneLevel: updatedAccount?.fortune_level,
        fortuneLevelName: updatedAccount?.fortune_level_name
      }
    });

  } catch (error) {
    console.error('Initialize fortune account error:', error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Internal server error',
        code: 'INITIALIZATION_ERROR'
      },
      { status: 500 }
    );
  }
}

/**
 * @api {GET} /api/fortune/account/transactions 获取福气交易历史
 * @apiDescription 获取用户的福气交易历史记录，支持分页和筛选
 */
export async function PUT(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // 验证用户身份
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const type = searchParams.get('type'); // 交易类型筛选

    const offset = (page - 1) * limit;

    // 获取交易记录
    const transactions = await FortuneService.getFortuneTransactions(
      user.id,
      limit,
      offset,
      type || undefined
    );

    // 获取总数（用于分页）
    let query = supabase
      .from('fortune_transactions')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id);

    if (type) {
      query = query.eq('transaction_type', type);
    }

    const { count } = await query;

    return NextResponse.json({
      success: true,
      data: {
        transactions: transactions.map(tx => ({
          id: tx.id,
          type: tx.transaction_type,
          amount: tx.amount,
          balanceBefore: tx.balance_before,
          balanceAfter: tx.balance_after,
          description: tx.description,
          referenceId: tx.reference_id,
          referenceType: tx.reference_type,
          createdAt: tx.created_at
        })),
        pagination: {
          page,
          limit,
          total: count || 0,
          totalPages: Math.ceil((count || 0) / limit)
        }
      }
    });

  } catch (error) {
    console.error('Get fortune transactions error:', error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Internal server error',
        code: 'TRANSACTIONS_ERROR'
      },
      { status: 500 }
    );
  }
}

// 配置运行时
export const runtime = 'nodejs';
