/**
 * 福气系统每日签到API
 */

import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { FortuneRewardService } from '@/services/fortune/FortuneRewardService';

/**
 * @api {POST} /api/fortune/daily-checkin 每日签到
 * @apiDescription 处理用户每日签到，发放福气奖励
 */
export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // 验证用户身份
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const rewardService = new FortuneRewardService();
    
    // 处理每日签到
    const result = await rewardService.processDailyCheckIn(user.id);

    return NextResponse.json({
      success: true,
      message: `签到成功！获得${result.totalReward}福气！`,
      data: {
        baseReward: result.baseReward,
        bonusReward: result.bonusReward,
        totalReward: result.totalReward,
        consecutiveDays: result.consecutiveDays,
        nextCheckIn: result.nextCheckIn,
        fortuneUnit: '福气',
        encouragement: result.consecutiveDays >= 7 
          ? '连续签到7天，获得额外奖励！继续保持！' 
          : `再连续签到${7 - result.consecutiveDays}天可获得额外奖励！`
      }
    });

  } catch (error) {
    console.error('Daily check-in error:', error);
    
    // 处理特定错误
    if (error instanceof Error) {
      if (error.message.includes('今日已签到')) {
        return NextResponse.json({
          error: error.message,
          code: 'ALREADY_CHECKED_IN'
        }, { status: 400 });
      }
    }

    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Internal server error',
        code: 'CHECKIN_ERROR'
      },
      { status: 500 }
    );
  }
}

/**
 * @api {GET} /api/fortune/daily-checkin 获取签到状态
 * @apiDescription 获取用户的签到状态和统计信息
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // 验证用户身份
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const today = new Date().toISOString().split('T')[0];

    // 检查今日是否已签到
    const { data: todayCheckIn } = await supabase
      .from('daily_checkins')
      .select('*')
      .eq('user_id', user.id)
      .eq('checkin_date', today)
      .single();

    // 获取用户福气账户信息
    const { data: userFortune } = await supabase
      .from('user_fortune')
      .select('consecutive_checkin_days, last_checkin_date, total_checkin_days')
      .eq('user_id', user.id)
      .single();

    // 获取最近7天的签到记录
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    
    const { data: recentCheckIns } = await supabase
      .from('daily_checkins')
      .select('checkin_date, total_reward, consecutive_days')
      .eq('user_id', user.id)
      .gte('checkin_date', sevenDaysAgo.toISOString().split('T')[0])
      .order('checkin_date', { ascending: false });

    // 计算下次签到时间
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);

    // 计算连续签到奖励进度
    const consecutiveDays = userFortune?.consecutive_checkin_days || 0;
    const nextBonusAt = 7;
    const bonusProgress = Math.min(consecutiveDays, nextBonusAt);

    return NextResponse.json({
      success: true,
      data: {
        // 今日签到状态
        hasCheckedInToday: !!todayCheckIn,
        todayReward: todayCheckIn?.total_reward || 0,
        
        // 签到统计
        consecutiveDays: consecutiveDays,
        totalCheckInDays: userFortune?.total_checkin_days || 0,
        lastCheckInDate: userFortune?.last_checkin_date,
        
        // 奖励信息
        baseReward: 10, // 基础奖励
        bonusReward: 5, // 连续奖励
        nextBonusAt: nextBonusAt,
        bonusProgress: bonusProgress,
        bonusProgressPercentage: (bonusProgress / nextBonusAt) * 100,
        
        // 时间信息
        nextCheckIn: tomorrow.toISOString(),
        canCheckIn: !todayCheckIn,
        
        // 最近签到记录
        recentCheckIns: recentCheckIns?.map(checkIn => ({
          date: checkIn.checkin_date,
          reward: checkIn.total_reward,
          consecutiveDays: checkIn.consecutive_days
        })) || [],
        
        // 激励信息
        encouragement: consecutiveDays >= 7 
          ? '连续签到达成！继续保持每日签到习惯！' 
          : consecutiveDays > 0 
            ? `已连续签到${consecutiveDays}天，再坚持${7 - consecutiveDays}天可获得额外奖励！`
            : '开始您的签到之旅，连续签到7天可获得额外福气奖励！'
      }
    });

  } catch (error) {
    console.error('Get check-in status error:', error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Internal server error',
        code: 'FETCH_ERROR'
      },
      { status: 500 }
    );
  }
}

// 配置运行时
export const runtime = 'nodejs';
