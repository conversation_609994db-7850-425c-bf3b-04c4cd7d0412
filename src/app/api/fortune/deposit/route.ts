import { NextRequest, NextResponse } from 'next/server';
import { fortuneExchangeService, EXCHANGE_CONFIG } from '@/lib/services/fortuneExchangeService';

export async function POST(request: NextRequest) {
  try {
    const { userId, amount, txHash, walletAddress } = await request.json();

    // 验证必需参数
    if (!userId) {
      return NextResponse.json(
        { success: false, message: '用户ID必需' },
        { status: 400 }
      );
    }

    if (!amount || typeof amount !== 'number' || amount <= 0) {
      return NextResponse.json(
        { success: false, message: '充值金额必须为正数' },
        { status: 400 }
      );
    }

    if (!txHash) {
      return NextResponse.json(
        { success: false, message: '交易哈希必需' },
        { status: 400 }
      );
    }

    if (!walletAddress) {
      return NextResponse.json(
        { success: false, message: '钱包地址必需' },
        { status: 400 }
      );
    }

    // 验证充值金额范围
    if (amount < EXCHANGE_CONFIG.MIN_DEPOSIT || amount > EXCHANGE_CONFIG.MAX_DEPOSIT) {
      return NextResponse.json(
        { 
          success: false, 
          message: `充值金额必须在${EXCHANGE_CONFIG.MIN_DEPOSIT}-${EXCHANGE_CONFIG.MAX_DEPOSIT}福气之间` 
        },
        { status: 400 }
      );
    }

    // 执行充值
    const transaction = await fortuneExchangeService.deposit(userId, amount, txHash, walletAddress);

    return NextResponse.json({
      success: true,
      message: '充值请求提交成功',
      data: {
        transactionId: transaction.id,
        amount: transaction.amount,
        fee: transaction.fee,
        netAmount: transaction.netAmount,
        status: transaction.status,
        txHash: transaction.txHash,
        estimatedProcessTime: '即时到账',
        createdAt: transaction.createdAt
      }
    });

  } catch (error) {
    console.error('充值失败:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        message: error instanceof Error ? error.message : '充值失败，请稍后重试' 
      },
      { status: 500 }
    );
  }
}

// 获取充值配置信息
export async function GET(request: NextRequest) {
  try {
    return NextResponse.json({
      success: true,
      data: {
        exchangeRate: EXCHANGE_CONFIG.EXCHANGE_RATE,
        minDeposit: EXCHANGE_CONFIG.MIN_DEPOSIT,
        maxDeposit: EXCHANGE_CONFIG.MAX_DEPOSIT,
        depositFeeRate: EXCHANGE_CONFIG.DEPOSIT_FEE_RATE,
        processTime: '即时到账',
        instructions: [
          '1. 发送HAOX到指定地址',
          '2. 复制交易哈希',
          '3. 填写充值表单提交',
          '4. 系统验证后即时到账'
        ]
      }
    });
  } catch (error) {
    console.error('获取充值配置失败:', error);
    
    return NextResponse.json(
      { success: false, message: '获取充值配置失败' },
      { status: 500 }
    );
  }
}
