import { NextRequest, NextResponse } from 'next/server';
import { fortuneExchangeService } from '@/lib/services/fortuneExchangeService';

export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const userId = params.userId;
    const url = new URL(request.url);
    
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const type = url.searchParams.get('type'); // 'deposit' | 'withdrawal' | null

    if (!userId) {
      return NextResponse.json(
        { success: false, message: '用户ID必需' },
        { status: 400 }
      );
    }

    // 获取交易历史
    const result = await fortuneExchangeService.getUserTransactions(userId, page, limit);

    // 如果指定了类型，进行过滤
    let filteredTransactions = result.transactions;
    if (type && (type === 'deposit' || type === 'withdrawal')) {
      filteredTransactions = result.transactions.filter(tx => tx.type === type);
    }

    // 格式化交易数据
    const formattedTransactions = filteredTransactions.map(tx => ({
      id: tx.id,
      type: tx.type,
      amount: tx.amount,
      fee: tx.fee,
      netAmount: tx.netAmount,
      status: tx.status,
      statusText: getStatusText(tx.status),
      statusColor: getStatusColor(tx.status),
      txHash: tx.txHash,
      walletAddress: tx.walletAddress,
      processedAt: tx.processedAt,
      createdAt: tx.createdAt,
      updatedAt: tx.updatedAt,
      typeText: tx.type === 'deposit' ? '充值' : '提现',
      typeIcon: tx.type === 'deposit' ? '💰' : '💸'
    }));

    // 计算统计信息
    const stats = calculateTransactionStats(result.transactions);

    return NextResponse.json({
      success: true,
      data: {
        transactions: formattedTransactions,
        pagination: {
          page: result.page,
          limit,
          total: result.total,
          totalPages: result.totalPages
        },
        stats,
        filters: {
          type: type || 'all'
        }
      }
    });

  } catch (error) {
    console.error('获取交易历史失败:', error);
    
    return NextResponse.json(
      { success: false, message: '获取交易历史失败' },
      { status: 500 }
    );
  }
}

// 获取交易统计信息
function calculateTransactionStats(transactions: any[]) {
  const stats = {
    totalDeposits: 0,
    totalWithdrawals: 0,
    totalDepositAmount: 0,
    totalWithdrawalAmount: 0,
    totalFees: 0,
    pendingTransactions: 0,
    completedTransactions: 0,
    failedTransactions: 0
  };

  transactions.forEach(tx => {
    if (tx.type === 'deposit') {
      stats.totalDeposits++;
      stats.totalDepositAmount += tx.amount;
    } else if (tx.type === 'withdrawal') {
      stats.totalWithdrawals++;
      stats.totalWithdrawalAmount += tx.amount;
    }

    stats.totalFees += tx.fee;

    switch (tx.status) {
      case 'pending':
      case 'processing':
        stats.pendingTransactions++;
        break;
      case 'completed':
        stats.completedTransactions++;
        break;
      case 'failed':
      case 'cancelled':
        stats.failedTransactions++;
        break;
    }
  });

  return stats;
}

// 获取状态文本
function getStatusText(status: string): string {
  const statusMap = {
    'pending': '待处理',
    'processing': '处理中',
    'completed': '已完成',
    'failed': '失败',
    'cancelled': '已取消'
  };
  return statusMap[status] || '未知';
}

// 获取状态颜色
function getStatusColor(status: string): string {
  const colorMap = {
    'pending': 'bg-yellow-100 text-yellow-800',
    'processing': 'bg-blue-100 text-blue-800',
    'completed': 'bg-green-100 text-green-800',
    'failed': 'bg-red-100 text-red-800',
    'cancelled': 'bg-gray-100 text-gray-800'
  };
  return colorMap[status] || 'bg-gray-100 text-gray-800';
}
