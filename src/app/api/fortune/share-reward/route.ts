/**
 * 福气系统分享奖励API
 */

import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { FortuneRewardService } from '@/services/fortune/FortuneRewardService';

/**
 * @api {POST} /api/fortune/share-reward 分享奖励
 * @apiDescription 处理用户分享内容后的福气奖励发放
 */
export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // 验证用户身份
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { contentType, contentId, platform } = await request.json();

    // 验证必需参数
    if (!contentType || !contentId || !platform) {
      return NextResponse.json({ 
        error: 'Missing required parameters: contentType, contentId, platform' 
      }, { status: 400 });
    }

    // 验证内容类型
    const validContentTypes = ['bet', 'invitation', 'achievement', 'news', 'tutorial'];
    if (!validContentTypes.includes(contentType)) {
      return NextResponse.json({ 
        error: 'Invalid content type' 
      }, { status: 400 });
    }

    // 验证平台
    const validPlatforms = ['telegram', 'twitter', 'facebook', 'wechat', 'weibo'];
    if (!validPlatforms.includes(platform)) {
      return NextResponse.json({ 
        error: 'Invalid platform' 
      }, { status: 400 });
    }

    const rewardService = new FortuneRewardService();
    
    // 处理分享奖励
    const rewardAmount = await rewardService.processShareReward(
      user.id,
      contentType,
      contentId,
      platform
    );

    // 获取内容标题（如果可能）
    let contentTitle = '内容';
    try {
      if (contentType === 'bet') {
        const { data: bet } = await supabase
          .from('social_bets')
          .select('title')
          .eq('id', contentId)
          .single();
        contentTitle = bet?.title || '赌约';
      }
    } catch (error) {
      // 忽略获取标题的错误
    }

    return NextResponse.json({
      success: true,
      message: `分享成功！获得${rewardAmount}福气奖励！`,
      data: {
        rewardAmount,
        contentType,
        contentTitle,
        platform,
        fortuneUnit: '福气',
        encouragement: '感谢您的分享，让更多人了解我们的平台！'
      }
    });

  } catch (error) {
    console.error('Share reward error:', error);
    
    // 处理特定错误
    if (error instanceof Error) {
      if (error.message.includes('已分享过')) {
        return NextResponse.json({
          error: error.message,
          code: 'ALREADY_SHARED'
        }, { status: 400 });
      }
    }

    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Internal server error',
        code: 'SHARE_REWARD_ERROR'
      },
      { status: 500 }
    );
  }
}

/**
 * @api {GET} /api/fortune/share-reward 获取分享奖励统计
 * @apiDescription 获取用户的分享奖励统计信息
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // 验证用户身份
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // 获取分享奖励统计
    const { data: shareRewards } = await supabase
      .from('share_rewards')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    // 计算总分享奖励
    const totalShareReward = shareRewards?.reduce((sum, reward) => sum + reward.reward_amount, 0) || 0;

    // 按平台统计
    const platformStats = shareRewards?.reduce((stats, reward) => {
      const platform = reward.platform;
      if (!stats[platform]) {
        stats[platform] = { count: 0, totalReward: 0 };
      }
      stats[platform].count++;
      stats[platform].totalReward += reward.reward_amount;
      return stats;
    }, {} as Record<string, { count: number; totalReward: number }>) || {};

    // 按内容类型统计
    const contentTypeStats = shareRewards?.reduce((stats, reward) => {
      const contentType = reward.shared_content_type;
      if (!stats[contentType]) {
        stats[contentType] = { count: 0, totalReward: 0 };
      }
      stats[contentType].count++;
      stats[contentType].totalReward += reward.reward_amount;
      return stats;
    }, {} as Record<string, { count: number; totalReward: number }>) || {};

    // 获取今日分享统计
    const today = new Date().toISOString().split('T')[0];
    const todayShares = shareRewards?.filter(reward => 
      reward.created_at.startsWith(today)
    ) || [];

    return NextResponse.json({
      success: true,
      data: {
        // 总体统计
        totalShares: shareRewards?.length || 0,
        totalShareReward,
        
        // 今日统计
        todayShares: todayShares.length,
        todayReward: todayShares.reduce((sum, reward) => sum + reward.reward_amount, 0),
        
        // 平台统计
        platformStats,
        
        // 内容类型统计
        contentTypeStats,
        
        // 最近分享记录
        recentShares: shareRewards?.slice(0, 10).map(reward => ({
          contentType: reward.shared_content_type,
          platform: reward.platform,
          rewardAmount: reward.reward_amount,
          createdAt: reward.created_at
        })) || [],
        
        // 奖励配置
        rewardPerShare: 20, // 每次分享奖励
        
        // 激励信息
        encouragement: shareRewards && shareRewards.length > 0
          ? '继续分享，传播福气！'
          : '开始分享内容，获得福气奖励！'
      }
    });

  } catch (error) {
    console.error('Get share reward stats error:', error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Internal server error',
        code: 'FETCH_ERROR'
      },
      { status: 500 }
    );
  }
}

// 配置运行时
export const runtime = 'nodejs';
