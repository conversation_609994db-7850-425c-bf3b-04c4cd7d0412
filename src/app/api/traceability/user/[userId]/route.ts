import { NextRequest, NextResponse } from 'next/server';
import { traceabilityService } from '@/lib/services/traceabilityService';

export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const userId = params.userId;
    const url = new URL(request.url);
    
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const type = url.searchParams.get('type'); // 'operation' | 'fortune_flow' | null

    if (!userId) {
      return NextResponse.json(
        { success: false, message: '用户ID必需' },
        { status: 400 }
      );
    }

    // 获取用户操作历史
    const operationHistory = await traceabilityService.getUserOperationHistory(userId, page, limit);

    // 获取用户福气流转统计
    const fortuneStats = await traceabilityService.getFortuneFlowStats(undefined, userId);

    // 格式化操作记录
    const formattedOperations = operationHistory.operations.map(op => ({
      ...op,
      typeText: getOperationTypeText(op.operationType),
      typeIcon: getOperationTypeIcon(op.operationType),
      typeColor: getOperationTypeColor(op.operationType),
      formattedTime: formatTimestamp(op.timestamp),
      relativeTime: getRelativeTime(op.timestamp)
    }));

    // 计算用户活跃度统计
    const activityStats = calculateActivityStats(operationHistory.operations);

    return NextResponse.json({
      success: true,
      data: {
        operations: formattedOperations,
        pagination: {
          page: operationHistory.page,
          limit,
          total: operationHistory.total,
          totalPages: operationHistory.totalPages
        },
        stats: {
          ...fortuneStats,
          ...activityStats
        }
      }
    });

  } catch (error) {
    console.error('获取用户可追溯性信息失败:', error);
    
    return NextResponse.json(
      { success: false, message: '获取用户可追溯性信息失败' },
      { status: 500 }
    );
  }
}

// 获取操作类型显示文本
function getOperationTypeText(type: string): string {
  const typeMap: Record<string, string> = {
    'bet_created': '创建赌约',
    'bet_joined': '参与赌约',
    'bet_started': '赌约开始',
    'judgment_round_1': '第一轮裁定',
    'judgment_round_2': '第二轮裁定',
    'judgment_round_3': '第三轮裁定',
    'judgment_completed': '裁定完成',
    'confirmation_requested': '请求确认',
    'confirmation_submitted': '提交确认',
    'dispute_raised': '提出争议',
    'settlement_started': '开始结算',
    'fortune_distributed': '福气分配',
    'bet_completed': '赌约完成',
    'bet_cancelled': '赌约取消'
  };
  return typeMap[type] || type;
}

// 获取操作类型图标
function getOperationTypeIcon(type: string): string {
  const iconMap: Record<string, string> = {
    'bet_created': '🎯',
    'bet_joined': '🤝',
    'bet_started': '🚀',
    'judgment_round_1': '👥',
    'judgment_round_2': '🎓',
    'judgment_round_3': '⚖️',
    'judgment_completed': '✅',
    'confirmation_requested': '❓',
    'confirmation_submitted': '✔️',
    'dispute_raised': '⚠️',
    'settlement_started': '💰',
    'fortune_distributed': '💸',
    'bet_completed': '🏆',
    'bet_cancelled': '❌'
  };
  return iconMap[type] || '📝';
}

// 获取操作类型颜色
function getOperationTypeColor(type: string): string {
  const colorMap: Record<string, string> = {
    'bet_created': 'bg-blue-100 text-blue-800',
    'bet_joined': 'bg-green-100 text-green-800',
    'bet_started': 'bg-purple-100 text-purple-800',
    'judgment_round_1': 'bg-yellow-100 text-yellow-800',
    'judgment_round_2': 'bg-orange-100 text-orange-800',
    'judgment_round_3': 'bg-red-100 text-red-800',
    'judgment_completed': 'bg-green-100 text-green-800',
    'confirmation_requested': 'bg-blue-100 text-blue-800',
    'confirmation_submitted': 'bg-green-100 text-green-800',
    'dispute_raised': 'bg-red-100 text-red-800',
    'settlement_started': 'bg-purple-100 text-purple-800',
    'fortune_distributed': 'bg-green-100 text-green-800',
    'bet_completed': 'bg-green-100 text-green-800',
    'bet_cancelled': 'bg-gray-100 text-gray-800'
  };
  return colorMap[type] || 'bg-gray-100 text-gray-800';
}

// 格式化时间戳
function formatTimestamp(timestamp: string): string {
  return new Date(timestamp).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
}

// 获取相对时间
function getRelativeTime(timestamp: string): string {
  const now = new Date();
  const time = new Date(timestamp);
  const diffMs = now.getTime() - time.getTime();
  
  const diffMinutes = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  
  if (diffMinutes < 1) {
    return '刚刚';
  } else if (diffMinutes < 60) {
    return `${diffMinutes}分钟前`;
  } else if (diffHours < 24) {
    return `${diffHours}小时前`;
  } else if (diffDays < 30) {
    return `${diffDays}天前`;
  } else {
    return formatTimestamp(timestamp);
  }
}

// 计算用户活跃度统计
function calculateActivityStats(operations: any[]) {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const thisWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
  const thisMonth = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

  const stats = {
    totalOperations: operations.length,
    todayOperations: 0,
    weekOperations: 0,
    monthOperations: 0,
    betsCreated: 0,
    betsJoined: 0,
    judgmentsParticipated: 0,
    confirmationsSubmitted: 0,
    disputesRaised: 0,
    lastActivityTime: null as string | null,
    mostActiveDay: null as string | null,
    averageOperationsPerDay: 0
  };

  const dailyOperations: Record<string, number> = {};

  operations.forEach(op => {
    const opTime = new Date(op.timestamp);
    const opDate = opTime.toISOString().split('T')[0];

    // 统计每日操作数
    dailyOperations[opDate] = (dailyOperations[opDate] || 0) + 1;

    // 时间范围统计
    if (opTime >= today) {
      stats.todayOperations++;
    }
    if (opTime >= thisWeek) {
      stats.weekOperations++;
    }
    if (opTime >= thisMonth) {
      stats.monthOperations++;
    }

    // 操作类型统计
    switch (op.operationType) {
      case 'bet_created':
        stats.betsCreated++;
        break;
      case 'bet_joined':
        stats.betsJoined++;
        break;
      case 'judgment_round_1':
      case 'judgment_round_2':
      case 'judgment_round_3':
        stats.judgmentsParticipated++;
        break;
      case 'confirmation_submitted':
        stats.confirmationsSubmitted++;
        break;
      case 'dispute_raised':
        stats.disputesRaised++;
        break;
    }

    // 最后活跃时间
    if (!stats.lastActivityTime || opTime > new Date(stats.lastActivityTime)) {
      stats.lastActivityTime = op.timestamp;
    }
  });

  // 找出最活跃的一天
  let maxOperations = 0;
  Object.entries(dailyOperations).forEach(([date, count]) => {
    if (count > maxOperations) {
      maxOperations = count;
      stats.mostActiveDay = date;
    }
  });

  // 计算平均每日操作数
  const totalDays = Object.keys(dailyOperations).length;
  if (totalDays > 0) {
    stats.averageOperationsPerDay = Math.round(stats.totalOperations / totalDays * 100) / 100;
  }

  return stats;
}
