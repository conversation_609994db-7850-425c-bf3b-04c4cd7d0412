import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { haoxService } from '@/lib/blockchain/haoxService';

export async function POST(request: NextRequest) {
  try {
    const { userId, walletAddress } = await request.json();

    if (!userId) {
      return NextResponse.json(
        { success: false, message: '用户ID必需' },
        { status: 400 }
      );
    }

    if (!walletAddress) {
      return NextResponse.json(
        { success: false, message: '钱包地址必需' },
        { status: 400 }
      );
    }

    // 验证钱包地址格式
    if (!haoxService.isValidAddress(walletAddress)) {
      return NextResponse.json(
        { success: false, message: '无效的钱包地址格式' },
        { status: 400 }
      );
    }

    // 查询HAOX余额
    const balanceInfo = await haoxService.getHAOXBalance(walletAddress);
    
    // 更新用户认证信息
    const updateResult = await updateUserCertification(userId, walletAddress, balanceInfo);

    return NextResponse.json({
      success: true,
      data: {
        userId,
        walletAddress,
        haoxBalance: balanceInfo.balanceFormatted,
        certificationLevel: balanceInfo.certificationLevel,
        benefits: haoxService.getCertificationBenefits(balanceInfo.certificationLevel),
        lastUpdated: balanceInfo.lastUpdated,
        updateResult
      }
    });

  } catch (error) {
    console.error('更新认证等级失败:', error);
    return NextResponse.json(
      { success: false, message: '更新认证等级失败' },
      { status: 500 }
    );
  }
}

// 批量更新认证等级（定时任务使用）
export async function PUT(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    
    // 验证定时任务权限
    if (authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
      return NextResponse.json(
        { success: false, message: '无权限执行批量更新' },
        { status: 401 }
      );
    }

    // 获取所有需要更新的用户
    const usersToUpdate = await getUsersForCertificationUpdate();
    
    if (usersToUpdate.length === 0) {
      return NextResponse.json({
        success: true,
        message: '没有需要更新的用户',
        data: { updated: 0, total: 0 }
      });
    }

    // 批量更新认证等级
    const updateResults = await batchUpdateCertifications(usersToUpdate);

    return NextResponse.json({
      success: true,
      message: `批量更新完成，成功更新${updateResults.successful}个用户`,
      data: {
        total: usersToUpdate.length,
        successful: updateResults.successful,
        failed: updateResults.failed,
        errors: updateResults.errors
      }
    });

  } catch (error) {
    console.error('批量更新认证等级失败:', error);
    return NextResponse.json(
      { success: false, message: '批量更新认证等级失败' },
      { status: 500 }
    );
  }
}

// 更新单个用户认证信息
async function updateUserCertification(userId: string, walletAddress: string, balanceInfo: any) {
  try {
    // 检查用户是否存在
    const { data: existingUser } = await supabase
      .from('users')
      .select('id, wallet_address')
      .eq('id', userId)
      .single();

    if (!existingUser) {
      throw new Error('用户不存在');
    }

    // 更新用户钱包地址（如果不同）
    if (existingUser.wallet_address !== walletAddress) {
      await supabase
        .from('users')
        .update({ wallet_address: walletAddress })
        .eq('id', userId);
    }

    // 更新或创建用户认证记录
    const certificationData = {
      user_id: userId,
      wallet_address: walletAddress,
      haox_balance: balanceInfo.balance,
      haox_balance_formatted: balanceInfo.balanceFormatted,
      certification_level: balanceInfo.certificationLevel,
      last_updated: balanceInfo.lastUpdated,
      updated_at: new Date().toISOString()
    };

    const { data: existingCert } = await supabase
      .from('user_certifications')
      .select('id')
      .eq('user_id', userId)
      .single();

    if (existingCert) {
      // 更新现有记录
      const { error } = await supabase
        .from('user_certifications')
        .update(certificationData)
        .eq('user_id', userId);

      if (error) throw error;
    } else {
      // 创建新记录
      const { error } = await supabase
        .from('user_certifications')
        .insert({
          ...certificationData,
          created_at: new Date().toISOString()
        });

      if (error) throw error;
    }

    // 更新用户信誉表中的认证等级
    await updateUserReputationCertification(userId, balanceInfo.certificationLevel);

    return { success: true, action: existingCert ? 'updated' : 'created' };
  } catch (error) {
    console.error('更新用户认证信息失败:', error);
    // 开发环境返回模拟成功
    if (process.env.NODE_ENV === 'development') {
      return { success: true, action: 'mocked' };
    }
    throw error;
  }
}

// 更新用户信誉表中的认证等级
async function updateUserReputationCertification(userId: string, certificationLevel: string) {
  try {
    const { data: existingReputation } = await supabase
      .from('user_reputation')
      .select('id')
      .eq('user_id', userId)
      .single();

    if (existingReputation) {
      // 更新现有记录
      await supabase
        .from('user_reputation')
        .update({ 
          certification_level: certificationLevel,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId);
    } else {
      // 创建新记录
      await supabase
        .from('user_reputation')
        .insert({
          user_id: userId,
          current_score: 100, // 新用户默认100分
          certification_level: certificationLevel,
          total_judgments: 0,
          correct_judgments: 0,
          daily_judgments_used: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
    }
  } catch (error) {
    console.error('更新用户信誉认证等级失败:', error);
    // 不抛出错误，避免影响主流程
  }
}

// 获取需要更新认证等级的用户
async function getUsersForCertificationUpdate() {
  try {
    // 获取1小时前更新的用户，或从未更新的用户
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000).toISOString();
    
    const { data: users } = await supabase
      .from('users')
      .select(`
        id,
        wallet_address,
        user_certifications (
          last_updated
        )
      `)
      .not('wallet_address', 'is', null)
      .or(`user_certifications.last_updated.is.null,user_certifications.last_updated.lt.${oneHourAgo}`);

    return users?.filter(user => user.wallet_address) || [];
  } catch (error) {
    console.error('获取需要更新的用户失败:', error);
    return [];
  }
}

// 批量更新认证等级
async function batchUpdateCertifications(users: any[]) {
  let successful = 0;
  let failed = 0;
  const errors: string[] = [];

  // 提取钱包地址
  const walletAddresses = users.map(user => user.wallet_address);
  
  try {
    // 批量查询HAOX余额
    const balanceInfos = await haoxService.getBatchHAOXBalances(walletAddresses);
    
    // 逐个更新用户认证信息
    for (let i = 0; i < users.length; i++) {
      const user = users[i];
      const balanceInfo = balanceInfos[i];
      
      try {
        await updateUserCertification(user.id, user.wallet_address, balanceInfo);
        successful++;
      } catch (error) {
        failed++;
        errors.push(`用户${user.id}: ${error.message}`);
      }
    }
  } catch (error) {
    console.error('批量查询HAOX余额失败:', error);
    errors.push(`批量查询失败: ${error.message}`);
    failed = users.length;
  }

  return { successful, failed, errors };
}
