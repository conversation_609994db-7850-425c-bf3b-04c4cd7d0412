import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { haoxService } from '@/lib/blockchain/haoxService';

export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const userId = params.userId;

    if (!userId) {
      return NextResponse.json(
        { success: false, message: '用户ID必需' },
        { status: 400 }
      );
    }

    // 获取用户认证信息
    const certificationInfo = await getUserCertificationInfo(userId);

    if (!certificationInfo) {
      return NextResponse.json(
        { success: false, message: '用户认证信息不存在' },
        { status: 404 }
      );
    }

    // 获取认证等级权益信息
    const benefits = haoxService.getCertificationBenefits(certificationInfo.certification_level);

    // 检查是否需要更新认证等级
    const needsUpdate = checkIfNeedsUpdate(certificationInfo.last_updated);

    return NextResponse.json({
      success: true,
      data: {
        userId,
        walletAddress: certificationInfo.wallet_address,
        haoxBalance: certificationInfo.haox_balance_formatted,
        certificationLevel: certificationInfo.certification_level,
        benefits,
        lastUpdated: certificationInfo.last_updated,
        needsUpdate,
        reputation: certificationInfo.reputation
      }
    });

  } catch (error) {
    console.error('获取认证信息失败:', error);
    return NextResponse.json(
      { success: false, message: '获取认证信息失败' },
      { status: 500 }
    );
  }
}

// 更新指定用户的认证等级
export async function PUT(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const userId = params.userId;
    const { walletAddress } = await request.json();

    if (!userId) {
      return NextResponse.json(
        { success: false, message: '用户ID必需' },
        { status: 400 }
      );
    }

    // 获取用户当前信息
    const userInfo = await getUserInfo(userId);
    if (!userInfo) {
      return NextResponse.json(
        { success: false, message: '用户不存在' },
        { status: 404 }
      );
    }

    // 使用提供的钱包地址或用户现有的钱包地址
    const addressToUse = walletAddress || userInfo.wallet_address;
    
    if (!addressToUse) {
      return NextResponse.json(
        { success: false, message: '用户未绑定钱包地址' },
        { status: 400 }
      );
    }

    // 验证钱包地址格式
    if (!haoxService.isValidAddress(addressToUse)) {
      return NextResponse.json(
        { success: false, message: '无效的钱包地址格式' },
        { status: 400 }
      );
    }

    // 查询最新的HAOX余额
    const balanceInfo = await haoxService.getHAOXBalance(addressToUse);
    
    // 更新认证信息
    const updateResult = await updateUserCertification(userId, addressToUse, balanceInfo);

    return NextResponse.json({
      success: true,
      message: '认证等级更新成功',
      data: {
        userId,
        walletAddress: addressToUse,
        previousLevel: userInfo.certification_level,
        newLevel: balanceInfo.certificationLevel,
        haoxBalance: balanceInfo.balanceFormatted,
        benefits: haoxService.getCertificationBenefits(balanceInfo.certificationLevel),
        lastUpdated: balanceInfo.lastUpdated,
        updateResult
      }
    });

  } catch (error) {
    console.error('更新用户认证等级失败:', error);
    return NextResponse.json(
      { success: false, message: '更新认证等级失败' },
      { status: 500 }
    );
  }
}

// 获取用户认证信息
async function getUserCertificationInfo(userId: string) {
  try {
    const { data: certInfo } = await supabase
      .from('user_certifications')
      .select(`
        *,
        users!inner(
          id,
          username,
          wallet_address
        ),
        user_reputation(
          current_score,
          total_judgments,
          correct_judgments,
          daily_judgments_used
        )
      `)
      .eq('user_id', userId)
      .single();

    if (certInfo) {
      return {
        ...certInfo,
        reputation: certInfo.user_reputation?.[0] || null
      };
    }

    // 如果没有认证记录，检查用户是否存在
    const { data: user } = await supabase
      .from('users')
      .select(`
        id,
        username,
        wallet_address,
        user_reputation(
          current_score,
          total_judgments,
          correct_judgments,
          daily_judgments_used
        )
      `)
      .eq('id', userId)
      .single();

    if (!user) return null;

    // 返回默认认证信息
    return {
      user_id: userId,
      wallet_address: user.wallet_address,
      haox_balance: '0',
      haox_balance_formatted: 0,
      certification_level: 'X1',
      last_updated: null,
      users: user,
      reputation: user.user_reputation?.[0] || null
    };
  } catch (error) {
    console.error('获取用户认证信息失败:', error);
    // 开发环境返回模拟数据
    if (process.env.NODE_ENV === 'development') {
      return {
        user_id: userId,
        wallet_address: '******************************************',
        haox_balance: '50000000000000000000000',
        haox_balance_formatted: 50000,
        certification_level: 'X2',
        last_updated: new Date().toISOString(),
        users: {
          id: userId,
          username: '测试用户',
          wallet_address: '******************************************'
        },
        reputation: {
          current_score: 90,
          total_judgments: 15,
          correct_judgments: 12,
          daily_judgments_used: 2
        }
      };
    }
    return null;
  }
}

// 获取用户基本信息
async function getUserInfo(userId: string) {
  try {
    const { data: user } = await supabase
      .from('users')
      .select(`
        id,
        username,
        wallet_address,
        user_certifications(certification_level)
      `)
      .eq('id', userId)
      .single();

    if (user) {
      return {
        ...user,
        certification_level: user.user_certifications?.[0]?.certification_level || 'X1'
      };
    }

    return null;
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return null;
  }
}

// 更新用户认证信息
async function updateUserCertification(userId: string, walletAddress: string, balanceInfo: any) {
  try {
    // 更新或创建认证记录
    const certificationData = {
      user_id: userId,
      wallet_address: walletAddress,
      haox_balance: balanceInfo.balance,
      haox_balance_formatted: balanceInfo.balanceFormatted,
      certification_level: balanceInfo.certificationLevel,
      last_updated: balanceInfo.lastUpdated,
      updated_at: new Date().toISOString()
    };

    const { data: existingCert } = await supabase
      .from('user_certifications')
      .select('id')
      .eq('user_id', userId)
      .single();

    if (existingCert) {
      // 更新现有记录
      const { error } = await supabase
        .from('user_certifications')
        .update(certificationData)
        .eq('user_id', userId);

      if (error) throw error;
    } else {
      // 创建新记录
      const { error } = await supabase
        .from('user_certifications')
        .insert({
          ...certificationData,
          created_at: new Date().toISOString()
        });

      if (error) throw error;
    }

    // 更新用户钱包地址
    await supabase
      .from('users')
      .update({ wallet_address: walletAddress })
      .eq('id', userId);

    // 更新用户信誉表中的认证等级
    await updateUserReputationCertification(userId, balanceInfo.certificationLevel);

    return { success: true, action: existingCert ? 'updated' : 'created' };
  } catch (error) {
    console.error('更新用户认证信息失败:', error);
    // 开发环境返回模拟成功
    if (process.env.NODE_ENV === 'development') {
      return { success: true, action: 'mocked' };
    }
    throw error;
  }
}

// 更新用户信誉表中的认证等级
async function updateUserReputationCertification(userId: string, certificationLevel: string) {
  try {
    const { data: existingReputation } = await supabase
      .from('user_reputation')
      .select('id')
      .eq('user_id', userId)
      .single();

    if (existingReputation) {
      await supabase
        .from('user_reputation')
        .update({ 
          certification_level: certificationLevel,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId);
    } else {
      await supabase
        .from('user_reputation')
        .insert({
          user_id: userId,
          current_score: 100,
          certification_level: certificationLevel,
          total_judgments: 0,
          correct_judgments: 0,
          daily_judgments_used: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
    }
  } catch (error) {
    console.error('更新用户信誉认证等级失败:', error);
  }
}

// 检查是否需要更新认证等级
function checkIfNeedsUpdate(lastUpdated: string | null): boolean {
  if (!lastUpdated) return true;
  
  const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
  const lastUpdateTime = new Date(lastUpdated);
  
  return lastUpdateTime < oneHourAgo;
}
