/**
 * 监控数据收集 API
 * 接收客户端发送的监控数据并进行处理
 */

// 使用Node.js Runtime (需要JWT验证)
// export const runtime = 'edge';

import { NextRequest } from 'next/server';
import { 
  withErrorHandler, 
  createSuccessResponse, 
  createErrorResponse, 
  createApiError, 
  ApiErrorCode,
  validateRequestBody 
} from '@/lib/api-error-handler';
import { 
  withRateLimit, 
  withLogging, 
  compose 
} from '@/lib/api-middleware';

interface MonitoringBatch {
  metrics: Array<{
    name: string;
    value: number;
    timestamp: number;
    tags?: Record<string, string>;
    unit?: string;
  }>;
  errors: Array<{
    message: string;
    stack?: string;
    url?: string;
    userAgent?: string;
    userId?: string;
    timestamp: number;
    level: 'error' | 'warning' | 'info';
    context?: Record<string, any>;
  }>;
  performance: Array<{
    metric: string;
    value: number;
    timestamp: number;
    page?: string;
    userAgent?: string;
  }>;
  timestamp: number;
  userAgent?: string;
  url?: string;
}

/**
 * 处理监控数据
 */
const handleMonitoringData = async (request: NextRequest) => {
  const body = await request.json();
  
  // 验证请求体结构
  const validationError = validateRequestBody(body, ['timestamp']);
  if (validationError) {
    return createErrorResponse(validationError);
  }

  const batch: MonitoringBatch = body;
  
  // 验证数据格式
  if (!Array.isArray(batch.metrics) || 
      !Array.isArray(batch.errors) || 
      !Array.isArray(batch.performance)) {
    const error = createApiError(
      ApiErrorCode.VALIDATION_ERROR,
      '监控数据格式错误',
      400
    );
    return createErrorResponse(error);
  }

  try {
    // 处理指标数据
    await processMetrics(batch.metrics);
    
    // 处理错误数据
    await processErrors(batch.errors);
    
    // 处理性能数据
    await processPerformance(batch.performance);
    
    // 检查告警条件
    await checkAlerts(batch);
    
    return createSuccessResponse({
      processed: {
        metrics: batch.metrics.length,
        errors: batch.errors.length,
        performance: batch.performance.length,
      },
      timestamp: Date.now(),
    }, '监控数据处理成功');
    
  } catch (error) {
    console.error('监控数据处理失败:', error);
    const apiError = createApiError(
      ApiErrorCode.INTERNAL_SERVER_ERROR,
      '监控数据处理失败',
      500,
      { error: error instanceof Error ? error.message : String(error) }
    );
    return createErrorResponse(apiError);
  }
};

/**
 * 处理指标数据
 */
async function processMetrics(metrics: MonitoringBatch['metrics']) {
  if (metrics.length === 0) return;
  
  // 聚合指标
  const aggregated = aggregateMetrics(metrics);
  
  // 存储到时序数据库（这里使用模拟）
  await storeMetrics(aggregated);
  
  // 发送到外部监控服务
  await sendToExternalServices('metrics', aggregated);
}

/**
 * 处理错误数据
 */
async function processErrors(errors: MonitoringBatch['errors']) {
  if (errors.length === 0) return;
  
  // 过滤和分类错误
  const criticalErrors = errors.filter(e => e.level === 'error');
  const warnings = errors.filter(e => e.level === 'warning');
  
  // 存储错误日志
  await storeErrors(errors);
  
  // 发送严重错误告警
  if (criticalErrors.length > 0) {
    await sendErrorAlerts(criticalErrors);
  }
  
  // 发送到外部监控服务
  await sendToExternalServices('errors', errors);
}

/**
 * 处理性能数据
 */
async function processPerformance(performance: MonitoringBatch['performance']) {
  if (performance.length === 0) return;
  
  // 分析性能趋势
  const analysis = analyzePerformance(performance);
  
  // 存储性能数据
  await storePerformance(performance);
  
  // 检查性能阈值
  await checkPerformanceThresholds(analysis);
  
  // 发送到外部监控服务
  await sendToExternalServices('performance', performance);
}

/**
 * 聚合指标数据
 */
function aggregateMetrics(metrics: MonitoringBatch['metrics']) {
  const aggregated = new Map<string, {
    count: number;
    sum: number;
    min: number;
    max: number;
    avg: number;
    tags?: Record<string, string>;
  }>();
  
  metrics.forEach(metric => {
    const key = `${metric.name}:${JSON.stringify(metric.tags || {})}`;
    
    if (!aggregated.has(key)) {
      aggregated.set(key, {
        count: 0,
        sum: 0,
        min: metric.value,
        max: metric.value,
        avg: 0,
        tags: metric.tags,
      });
    }
    
    const agg = aggregated.get(key)!;
    agg.count++;
    agg.sum += metric.value;
    agg.min = Math.min(agg.min, metric.value);
    agg.max = Math.max(agg.max, metric.value);
    agg.avg = agg.sum / agg.count;
  });
  
  return Array.from(aggregated.entries()).map(([key, data]) => ({
    metric: key.split(':')[0],
    tags: data.tags,
    ...data,
  }));
}

/**
 * 分析性能数据
 */
function analyzePerformance(performance: MonitoringBatch['performance']) {
  const analysis = {
    pageLoadTimes: [] as number[],
    apiResponseTimes: [] as number[],
    webVitals: {
      fcp: [] as number[],
      lcp: [] as number[],
      fid: [] as number[],
    },
  };
  
  performance.forEach(perf => {
    switch (perf.metric) {
      case 'page_load_time':
        analysis.pageLoadTimes.push(perf.value);
        break;
      case 'api_response_time':
        analysis.apiResponseTimes.push(perf.value);
        break;
      case 'first_contentful_paint':
        analysis.webVitals.fcp.push(perf.value);
        break;
      case 'largest_contentful_paint':
        analysis.webVitals.lcp.push(perf.value);
        break;
      case 'first_input_delay':
        analysis.webVitals.fid.push(perf.value);
        break;
    }
  });
  
  return analysis;
}

/**
 * 检查告警条件
 */
async function checkAlerts(batch: MonitoringBatch) {
  const alerts = [];
  
  // 错误率检查
  const errorRate = batch.errors.filter(e => e.level === 'error').length / 
                   Math.max(batch.metrics.length + batch.errors.length + batch.performance.length, 1);
  
  if (errorRate > 0.05) { // 5%
    alerts.push({
      type: 'high_error_rate',
      severity: 'critical',
      message: `错误率过高: ${(errorRate * 100).toFixed(2)}%`,
      value: errorRate,
    });
  }
  
  // 性能检查
  const slowRequests = batch.performance.filter(p => 
    p.metric === 'api_response_time' && p.value > 5000
  );
  
  if (slowRequests.length > 0) {
    alerts.push({
      type: 'slow_api_requests',
      severity: 'warning',
      message: `发现 ${slowRequests.length} 个慢请求`,
      value: slowRequests.length,
    });
  }
  
  // 发送告警
  if (alerts.length > 0) {
    await sendAlerts(alerts);
  }
}

/**
 * 存储指标数据（模拟）
 */
async function storeMetrics(metrics: any[]) {
  // 在实际应用中，这里会存储到时序数据库如 InfluxDB 或 CloudWatch
  console.log(`存储 ${metrics.length} 个聚合指标`);
}

/**
 * 存储错误数据（模拟）
 */
async function storeErrors(errors: MonitoringBatch['errors']) {
  // 在实际应用中，这里会存储到日志系统如 ELK 或 Splunk
  console.log(`存储 ${errors.length} 个错误记录`);
}

/**
 * 存储性能数据（模拟）
 */
async function storePerformance(performance: MonitoringBatch['performance']) {
  // 在实际应用中，这里会存储到性能监控系统
  console.log(`存储 ${performance.length} 个性能记录`);
}

/**
 * 发送到外部监控服务
 */
async function sendToExternalServices(type: string, data: any[]) {
  // 发送到 Sentry
  if (process.env.SENTRY_DSN && type === 'errors') {
    // 集成 Sentry SDK
  }
  
  // 发送到 DataDog
  if (process.env.DATADOG_API_KEY) {
    // 集成 DataDog SDK
  }
  
  // 发送到 New Relic
  if (process.env.NEW_RELIC_LICENSE_KEY) {
    // 集成 New Relic SDK
  }
}

/**
 * 发送错误告警
 */
async function sendErrorAlerts(errors: MonitoringBatch['errors']) {
  // 发送到 Slack、邮件或其他告警渠道
  console.log(`发送 ${errors.length} 个错误告警`);
}

/**
 * 检查性能阈值
 */
async function checkPerformanceThresholds(analysis: any) {
  // 检查性能指标是否超过阈值
  const thresholds = {
    pageLoadTime: 3000,
    apiResponseTime: 1000,
    fcp: 2000,
    lcp: 4000,
    fid: 100,
  };
  
  // 实现阈值检查逻辑
}

/**
 * 发送告警
 */
async function sendAlerts(alerts: any[]) {
  // 发送到告警系统
  console.log(`发送 ${alerts.length} 个告警`);
  
  // 这里可以集成各种告警渠道：
  // - Slack Webhook
  // - 邮件通知
  // - 短信通知
  // - PagerDuty
  // - 企业微信
}

// 应用中间件
export const POST = compose(
  withErrorHandler,
  withLogging,
  withRateLimit(1000, 60000) // 每分钟最多1000次监控数据上报
)(handleMonitoringData);
