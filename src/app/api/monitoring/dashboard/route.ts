import { NextRequest, NextResponse } from 'next/server';
import { withErrorHandling, createSuccessResponse, createAuthorizationError } from '@/lib/middleware/errorMiddleware';
import { monitoringService } from '@/lib/services/monitoringService';

export const GET = withErrorHandling(async (request: NextRequest) => {
  try {
    // 简单的权限检查（在实际应用中应该有更严格的认证）
    const authHeader = request.headers.get('authorization');
    const adminToken = process.env.ADMIN_TOKEN;
    
    if (adminToken && authHeader !== `Bearer ${adminToken}`) {
      throw createAuthorizationError('需要管理员权限访问监控仪表板');
    }

    const url = new URL(request.url);
    const hours = parseInt(url.searchParams.get('hours') || '1');
    const includeAlerts = url.searchParams.get('alerts') === 'true';
    const includeReport = url.searchParams.get('report') === 'true';

    // 获取当前指标
    const currentMetrics = monitoringService.getCurrentMetrics();
    
    // 获取历史指标
    const historicalMetrics = monitoringService.getHistoricalMetrics(hours);
    
    const dashboardData: any = {
      current: currentMetrics,
      historical: historicalMetrics,
      timeRange: {
        hours,
        from: new Date(Date.now() - hours * 60 * 60 * 1000).toISOString(),
        to: new Date().toISOString()
      }
    };

    // 包含告警信息
    if (includeAlerts) {
      dashboardData.alerts = {
        active: monitoringService.getActiveAlerts(),
        recent: monitoringService.getAlertHistory(1)
      };
    }

    // 包含详细报告
    if (includeReport) {
      dashboardData.report = monitoringService.generateReport(hours);
    }

    return createSuccessResponse(dashboardData);
  } catch (error) {
    throw error;
  }
});

// 获取告警信息
export const POST = withErrorHandling(async (request: NextRequest) => {
  try {
    const authHeader = request.headers.get('authorization');
    const adminToken = process.env.ADMIN_TOKEN;
    
    if (adminToken && authHeader !== `Bearer ${adminToken}`) {
      throw createAuthorizationError('需要管理员权限');
    }

    const { action, alertId, resolvedBy } = await request.json();

    if (action === 'resolve' && alertId) {
      monitoringService.resolveAlert(alertId, resolvedBy);
      
      return createSuccessResponse({
        message: '告警已解决',
        alertId,
        resolvedBy,
        resolvedAt: new Date().toISOString()
      });
    }

    throw new Error('无效的操作');
  } catch (error) {
    throw error;
  }
});
