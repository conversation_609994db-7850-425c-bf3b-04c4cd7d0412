import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import {
  withErrorHandler,
  createSuccessResponse,
  createErrorResponse,
  createApiError,
  ApiErrorCode
} from '@/lib/api-error-handler';

/**
 * 数据库关系修复 API
 * 修复 invitation_stats 和 users 表之间的外键关系
 */

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

// 使用服务角色密钥创建管理员客户端
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

const handleDatabaseFix = async (request: NextRequest) => {
  console.log('开始修复数据库关系...');

  // 1. 检查表是否存在
  const { data: tables, error: tablesError } = await supabaseAdmin
    .from('information_schema.tables')
    .select('table_name')
    .eq('table_schema', 'public')
    .in('table_name', ['users', 'invitation_stats']);

  if (tablesError) {
    console.error('检查表存在性失败:', tablesError);
    throw createApiError(
      ApiErrorCode.DATABASE_ERROR,
      '检查表存在性失败',
      500,
      { details: tablesError }
    );
  }

    const existingTables = tables?.map(t => t.table_name) || [];
    console.log('现有表:', existingTables);

    // 2. 创建缺失的表
    const sqlCommands = [];

    if (!existingTables.includes('users')) {
      sqlCommands.push(`
        CREATE TABLE IF NOT EXISTS public.users (
          id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
          email TEXT UNIQUE NOT NULL,
          username TEXT UNIQUE NOT NULL,
          avatar_url TEXT,
          wallet_address TEXT UNIQUE,
          is_merchant BOOLEAN DEFAULT FALSE,
          merchant_status TEXT CHECK (merchant_status IN ('pending', 'approved', 'rejected')),
          
          -- Telegram 相关字段
          telegram_id BIGINT UNIQUE,
          telegram_username TEXT,
          telegram_first_name TEXT,
          telegram_last_name TEXT,
          telegram_photo_url TEXT,
          
          -- 预售相关字段
          presale_eligible BOOLEAN DEFAULT FALSE,
          presale_qualified_at TIMESTAMP WITH TIME ZONE,
          
          -- 时间戳
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `);
    }

    if (!existingTables.includes('invitation_stats')) {
      sqlCommands.push(`
        CREATE TABLE IF NOT EXISTS public.invitation_stats (
          id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
          user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
          total_invitations INTEGER DEFAULT 0,
          successful_invitations INTEGER DEFAULT 0,
          total_rewards DECIMAL(20, 8) DEFAULT 0,
          current_month_invitations INTEGER DEFAULT 0,
          last_invitation_at TIMESTAMP WITH TIME ZONE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `);
    }

    // 3. 添加索引
    sqlCommands.push(`
      CREATE INDEX IF NOT EXISTS idx_users_wallet_address ON public.users(wallet_address);
      CREATE INDEX IF NOT EXISTS idx_users_telegram_id ON public.users(telegram_id);
      CREATE INDEX IF NOT EXISTS idx_invitation_stats_user_id ON public.invitation_stats(user_id);
    `);

    // 4. 启用RLS
    sqlCommands.push(`
      ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
      ALTER TABLE public.invitation_stats ENABLE ROW LEVEL SECURITY;
    `);

    // 5. 创建安全策略
    sqlCommands.push(`
      DROP POLICY IF EXISTS "Allow all operations on users" ON public.users;
      CREATE POLICY "Allow all operations on users" ON public.users FOR ALL USING (true);
      
      DROP POLICY IF EXISTS "Allow all operations on invitation_stats" ON public.invitation_stats;
      CREATE POLICY "Allow all operations on invitation_stats" ON public.invitation_stats FOR ALL USING (true);
    `);

    // 6. 确保 uuid-ossp 扩展已启用
    sqlCommands.push(`
      CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
    `);

    // 执行所有SQL命令
    for (const sql of sqlCommands) {
      if (sql.trim()) {
        console.log('执行SQL:', sql.substring(0, 100) + '...');
        const { error } = await supabaseAdmin.rpc('exec_sql', { sql_query: sql });
        
        if (error) {
          console.error('SQL执行失败:', error);
          // 继续执行其他命令，不中断
        }
      }
    }

    // 7. 验证修复结果
    const { data: usersTest, error: usersTestError } = await supabaseAdmin
      .from('users')
      .select('count')
      .limit(1);

    const { data: statsTest, error: statsTestError } = await supabaseAdmin
      .from('invitation_stats')
      .select('count')
      .limit(1);

    const results = {
      usersTableAccessible: !usersTestError,
      invitationStatsTableAccessible: !statsTestError,
      usersError: usersTestError?.message,
      statsError: statsTestError?.message,
    };

    console.log('修复结果:', results);

    return createSuccessResponse({
      message: '数据库关系修复完成',
      results,
      executedCommands: sqlCommands.length,
    });
};

const handleGetDatabaseStatus = async (request: NextRequest) => {
  // 检查数据库状态
  const { data: tables, error: tablesError } = await supabaseAdmin
    .from('information_schema.tables')
    .select('table_name')
    .eq('table_schema', 'public');

  if (tablesError) {
    throw createApiError(
      ApiErrorCode.DATABASE_ERROR,
      '检查数据库状态失败',
      500,
      { details: tablesError }
    );
  }

  const tableNames = tables?.map(t => t.table_name) || [];

  // 检查关键表是否存在
  const requiredTables = ['users', 'invitation_stats', 'user_activities', 'social_accounts'];
  const missingTables = requiredTables.filter(table => !tableNames.includes(table));

  // 检查外键关系
  const { data: constraints, error: constraintsError } = await supabaseAdmin
    .from('information_schema.table_constraints')
    .select('constraint_name, table_name, constraint_type')
    .eq('table_schema', 'public')
    .eq('constraint_type', 'FOREIGN KEY');

  return createSuccessResponse({
    tables: tableNames,
    missingTables,
    foreignKeys: constraints || [],
    constraintsError: constraintsError?.message,
  });
};

export const POST = withErrorHandler(handleDatabaseFix);
export const GET = withErrorHandler(handleGetDatabaseStatus);
