// 配置Edge Runtime
export const runtime = 'edge';

import { NextRequest, NextResponse } from 'next/server';
import {
  with<PERSON>rror<PERSON>and<PERSON>,
  createSuccessResponse,
  createErrorResponse,
  createApiError,
  ApiErrorCode
} from '@/lib/api-error-handler';

// 配置Edge Runtime兼容性

export async function GET(request: NextRequest) {
  try {
    // Basic health check (Edge Runtime compatible)
    const healthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0', // 静态版本号，避免process.env.npm_package_version
      environment: process.env.NODE_ENV || 'development',
      // 移除 uptime 和 memory，因为Edge Runtime不支持
      checks: {
        database: await checkDatabase(),
        external_apis: await checkExternalAPIs(),
        environment_variables: checkEnvironmentVariables(),
      },
    };

    // Determine overall health status
    const hasUnhealthyChecks = Object.values(healthData.checks).some(
      check => check.status === 'unhealthy'
    );
    const allChecksHealthy = Object.values(healthData.checks).every(
      check => check.status === 'healthy'
    );

    const status = hasUnhealthyChecks ? 503 : 200;
    const overallStatus = hasUnhealthyChecks ? 'unhealthy' : allChecksHealthy ? 'healthy' : 'degraded';

    return NextResponse.json(
      {
        ...healthData,
        status: overallStatus,
      },
      { status }
    );
  } catch (error) {
    console.error('Health check failed:', error);
    
    return NextResponse.json(
      {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: 'Health check failed',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 503 }
    );
  }
}

async function checkDatabase() {
  try {
    // Check Supabase connection
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseKey || supabaseUrl.includes('demo.supabase.co')) {
      return {
        status: 'degraded',
        message: 'Supabase not configured (using demo mode)',
      };
    }

    // Simple connectivity test
    const response = await fetch(`${supabaseUrl}/rest/v1/`, {
      method: 'HEAD',
      headers: {
        'apikey': supabaseKey,
        'Authorization': `Bearer ${supabaseKey}`,
      },
    });

    if (response.ok) {
      return {
        status: 'healthy',
        message: 'Database connection successful',
        response_time: response.headers.get('x-response-time') || 'unknown',
      };
    } else {
      return {
        status: 'unhealthy',
        message: `Database connection failed: ${response.status}`,
      };
    }
  } catch (error) {
    return {
      status: 'unhealthy',
      message: `Database check failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
    };
  }
}

async function checkExternalAPIs() {
  const checks = {
    alchemy: await checkAlchemy(),
    walletconnect: checkWalletConnect(),
  };

  const allHealthy = Object.values(checks).every(check => check.status === 'healthy');

  return {
    status: allHealthy ? 'healthy' : 'degraded',
    details: checks,
  };
}

async function checkAlchemy() {
  try {
    const alchemyKey = process.env.NEXT_PUBLIC_ALCHEMY_API_KEY;
    
    if (!alchemyKey || alchemyKey === 'your_alchemy_api_key') {
      return {
        status: 'degraded',
        message: 'Alchemy API key not configured (using demo mode)',
      };
    }

    // Test Alchemy connection with Ethereum endpoint (most common)
    const response = await fetch(`https://eth-mainnet.g.alchemy.com/v2/${alchemyKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        method: 'eth_blockNumber',
        params: [],
        id: 1,
      }),
    });

    if (response.ok) {
      const data = await response.json();
      if (data.error) {
        return {
          status: 'degraded',
          message: `Alchemy API error: ${data.error.message} (check API key and network permissions)`,
        };
      }
      return {
        status: 'healthy',
        message: 'Alchemy API accessible',
        latest_block: data.result,
      };
    } else {
      return {
        status: 'degraded',
        message: `Alchemy API error: ${response.status} (check API key configuration)`,
      };
    }
  } catch (error) {
    return {
      status: 'degraded',
      message: `Alchemy check failed: ${error instanceof Error ? error.message : 'Unknown error'} (non-blocking)`,
    };
  }
}

function checkWalletConnect() {
  const projectId = process.env.NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID;
  
  if (!projectId) {
    return {
      status: 'unhealthy',
      message: 'WalletConnect project ID missing',
    };
  }

  return {
    status: 'healthy',
    message: 'WalletConnect configuration present',
    project_id: projectId.substring(0, 8) + '...',
  };
}

function checkEnvironmentVariables() {
  const requiredVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID',
    'NEXT_PUBLIC_ALCHEMY_API_KEY',
  ];

  const missingVars = requiredVars.filter(varName => !process.env[varName]);

  if (missingVars.length === 0) {
    return {
      status: 'healthy',
      message: 'All required environment variables present',
      total_vars: requiredVars.length,
    };
  } else {
    return {
      status: 'unhealthy',
      message: 'Missing required environment variables',
      missing_vars: missingVars,
      total_required: requiredVars.length,
      total_missing: missingVars.length,
    };
  }
}
