import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { getContractAddresses } from '@/config/contracts';

// 获取用户HAOX余额和统计信息
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const walletAddress = searchParams.get('walletAddress');

    if (!userId && !walletAddress) {
      return NextResponse.json(
        { error: 'User ID or wallet address is required' },
        { status: 400 }
      );
    }

    // 构建查询条件
    let query = supabase
      .from('user_haox_stats')
      .select('*');

    if (userId) {
      query = query.eq('id', userId);
    } else if (walletAddress) {
      query = query.eq('wallet_address', walletAddress.toLowerCase());
    }

    const { data: userStats, error: statsError } = await query.single();

    if (statsError) {
      console.error('Error fetching user stats:', statsError);
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // 获取最近的交易记录
    const { data: recentTransactions, error: txError } = await supabase
      .from('haox_transactions')
      .select('*')
      .eq('user_id', userStats.id)
      .order('block_timestamp', { ascending: false })
      .limit(10);

    if (txError) {
      console.error('Error fetching transactions:', txError);
    }

    // 获取预售投资记录
    const { data: presaleInvestments, error: presaleError } = await supabase
      .from('presale_investments')
      .select('*')
      .eq('user_id', userStats.id)
      .order('block_timestamp', { ascending: false });

    if (presaleError) {
      console.error('Error fetching presale investments:', presaleError);
    }

    // 获取邀请奖励记录
    const { data: invitationRewards, error: rewardsError } = await supabase
      .from('invitation_rewards_onchain')
      .select('*')
      .eq('inviter_id', userStats.id)
      .order('recorded_at', { ascending: false });

    if (rewardsError) {
      console.error('Error fetching invitation rewards:', rewardsError);
    }

    // 计算额外统计信息
    const totalInvested = parseFloat(userStats.total_invested_bnb || '0');
    const totalPurchased = parseFloat(userStats.total_haox_purchased || '0');
    const averagePrice = totalInvested > 0 && totalPurchased > 0 ? totalInvested / totalPurchased : 0;

    return NextResponse.json({
      success: true,
      data: {
        user: {
          id: userStats.id,
          username: userStats.username,
          wallet_address: userStats.wallet_address,
        },
        balance: {
          haox_balance: userStats.haox_balance || '0',
          total_invested_bnb: userStats.total_invested_bnb || '0',
          total_haox_purchased: userStats.total_haox_purchased || '0',
          average_purchase_price: averagePrice.toFixed(8),
        },
        presale: {
          transaction_count: userStats.presale_transactions || 0,
          investments: presaleInvestments || [],
        },
        invitation_rewards: {
          total_rewards: userStats.total_invitation_rewards || '0',
          claimed_rewards: userStats.claimed_invitation_rewards || '0',
          pending_rewards: userStats.pending_invitation_rewards || '0',
          rewards_history: invitationRewards || [],
        },
        recent_transactions: recentTransactions || [],
      }
    });

  } catch (error) {
    console.error('Get HAOX balance error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// 更新用户HAOX余额（由区块链事件监听器调用）
export async function POST(request: NextRequest) {
  try {
    const {
      walletAddress,
      balance,
      transactionHash,
      blockNumber,
      blockTimestamp
    } = await request.json();

    if (!walletAddress || balance === undefined) {
      return NextResponse.json(
        { error: 'Wallet address and balance are required' },
        { status: 400 }
      );
    }

    // 更新用户余额
    const { data: user, error: updateError } = await supabase
      .from('users')
      .update({
        haox_balance: balance,
        balance_updated_at: new Date().toISOString()
      })
      .eq('wallet_address', walletAddress.toLowerCase())
      .select()
      .single();

    if (updateError) {
      console.error('Error updating user balance:', updateError);
      return NextResponse.json(
        { error: 'Failed to update balance' },
        { status: 500 }
      );
    }

    // 如果提供了交易信息，记录交易
    if (transactionHash && blockNumber && blockTimestamp) {
      const { error: txError } = await supabase
        .from('haox_transactions')
        .insert({
          user_id: user.id,
          transaction_hash: transactionHash,
          transaction_type: 'transfer',
          to_address: walletAddress.toLowerCase(),
          amount: balance,
          block_number: blockNumber,
          block_timestamp: blockTimestamp,
          status: 'confirmed'
        });

      if (txError) {
        console.error('Error recording transaction:', txError);
        // 不阻止余额更新，只记录错误
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        user_id: user.id,
        wallet_address: walletAddress,
        new_balance: balance,
        updated_at: user.balance_updated_at
      }
    });

  } catch (error) {
    console.error('Update HAOX balance error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
