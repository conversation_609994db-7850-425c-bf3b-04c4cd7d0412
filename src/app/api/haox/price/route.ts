import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

// 获取HAOX价格信息
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || '24h';
    const limit = parseInt(searchParams.get('limit') || '100');

    // 获取最新价格
    const { data: latestPrice, error: latestError } = await supabase
      .from('price_history')
      .select('*')
      .order('timestamp', { ascending: false })
      .limit(1)
      .single();

    if (latestError) {
      console.error('Error fetching latest price:', latestError);
    }

    // 计算时间范围
    const now = new Date();
    let startTime: Date;
    
    switch (period) {
      case '1h':
        startTime = new Date(now.getTime() - 60 * 60 * 1000);
        break;
      case '24h':
        startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case '7d':
        startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      default:
        startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    }

    // 获取历史价格数据
    const { data: priceHistory, error: historyError } = await supabase
      .from('price_history')
      .select('*')
      .gte('timestamp', startTime.toISOString())
      .order('timestamp', { ascending: true })
      .limit(limit);

    if (historyError) {
      console.error('Error fetching price history:', historyError);
    }

    // 计算价格统计
    let priceStats = {
      current: 0,
      high_24h: 0,
      low_24h: 0,
      change_24h: 0,
      change_24h_percent: 0,
      volume_24h: 0,
      market_cap: 0,
    };

    if (priceHistory && priceHistory.length > 0) {
      const prices = priceHistory.map(p => parseFloat(p.aggregated_price));
      const volumes = priceHistory.map(p => parseFloat(p.volume_24h || '0'));
      
      priceStats.current = parseFloat(latestPrice?.aggregated_price || '0');
      priceStats.high_24h = Math.max(...prices);
      priceStats.low_24h = Math.min(...prices);
      priceStats.volume_24h = volumes.reduce((sum, vol) => sum + vol, 0);
      priceStats.market_cap = parseFloat(latestPrice?.market_cap || '0');
      
      // 计算24小时变化
      if (priceHistory.length > 1) {
        const firstPrice = prices[0];
        const lastPrice = prices[prices.length - 1];
        priceStats.change_24h = lastPrice - firstPrice;
        priceStats.change_24h_percent = firstPrice > 0 ? (priceStats.change_24h / firstPrice) * 100 : 0;
      }
    }

    // 获取预售统计
    const { data: presaleStats, error: presaleError } = await supabase
      .from('presale_investments')
      .select('bnb_amount, token_amount, stage')
      .eq('status', 'confirmed');

    let presaleInfo = {
      total_raised: 0,
      total_sold: 0,
      current_stage: 0,
      participants: 0,
    };

    if (!presaleError && presaleStats) {
      presaleInfo.total_raised = presaleStats.reduce((sum, inv) => sum + parseFloat(inv.bnb_amount), 0);
      presaleInfo.total_sold = presaleStats.reduce((sum, inv) => sum + parseFloat(inv.token_amount), 0);
      presaleInfo.current_stage = Math.max(...presaleStats.map(inv => inv.stage), 0);
      presaleInfo.participants = new Set(presaleStats.map(inv => inv.user_address)).size;
    }

    return NextResponse.json({
      success: true,
      data: {
        price: priceStats,
        presale: presaleInfo,
        history: priceHistory || [],
        latest_update: latestPrice?.timestamp || null,
        confidence: latestPrice?.confidence || 0,
      }
    });

  } catch (error) {
    console.error('Get HAOX price error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// 更新价格数据（由价格预言机或管理员调用）
export async function POST(request: NextRequest) {
  try {
    const {
      chainlinkPrice,
      pancakeswapPrice,
      aggregatedPrice,
      confidence,
      volume24h,
      marketCap,
      timestamp
    } = await request.json();

    if (!aggregatedPrice || !timestamp) {
      return NextResponse.json(
        { error: 'Aggregated price and timestamp are required' },
        { status: 400 }
      );
    }

    // 插入新的价格记录
    const { data: priceRecord, error: insertError } = await supabase
      .from('price_history')
      .insert({
        timestamp: new Date(timestamp).toISOString(),
        chainlink_price: chainlinkPrice || null,
        pancakeswap_price: pancakeswapPrice || null,
        aggregated_price: aggregatedPrice,
        confidence: confidence || 100,
        volume_24h: volume24h || null,
        market_cap: marketCap || null,
      })
      .select()
      .single();

    if (insertError) {
      console.error('Error inserting price record:', insertError);
      return NextResponse.json(
        { error: 'Failed to save price data' },
        { status: 500 }
      );
    }

    // 清理旧的价格记录（保留最近30天）
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const { error: cleanupError } = await supabase
      .from('price_history')
      .delete()
      .lt('timestamp', thirtyDaysAgo.toISOString());

    if (cleanupError) {
      console.error('Error cleaning up old price records:', cleanupError);
      // 不阻止价格更新，只记录错误
    }

    return NextResponse.json({
      success: true,
      data: {
        id: priceRecord.id,
        timestamp: priceRecord.timestamp,
        aggregated_price: priceRecord.aggregated_price,
        confidence: priceRecord.confidence,
      }
    });

  } catch (error) {
    console.error('Update HAOX price error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
