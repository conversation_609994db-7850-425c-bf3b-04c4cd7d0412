import { NextRequest, NextResponse } from 'next/server';
import {
  withErrorHandler,
  createSuccessResponse,
  createErrorResponse,
  createApiError,
  ApiErrorCode
} from '@/lib/api-error-handler';

interface PerformanceMetrics {
  fcp: number;
  lcp: number;
  fid: number;
  cls: number;
  ttfb: number;
  memoryUsage?: number;
  connectionType?: string;
}

interface PerformanceData {
  metrics: PerformanceMetrics;
  url: string;
  userAgent: string;
  timestamp: number;
  sessionId?: string;
  userId?: string;
}

/**
 * 性能数据收集 API
 * 收集和存储用户的性能指标数据
 */
const handlePerformanceData = async (request: NextRequest) => {
    const data: PerformanceData = await request.json();
    
    // 验证数据
    if (!data.metrics || !data.url || !data.userAgent || !data.timestamp) {
      throw createApiError(
        ApiErrorCode.VALIDATION_ERROR,
        'Missing required fields: metrics, url, userAgent, timestamp',
        400
      );
    }

    // 获取客户端信息
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';
    
    const country = request.headers.get('cf-ipcountry') || 'unknown';
    
    // 构建性能记录
    const performanceRecord = {
      ...data,
      clientIP,
      country,
      receivedAt: new Date().toISOString(),
    };

    // 在生产环境中，这里应该保存到数据库
    if (process.env.NODE_ENV === 'production') {
      // 示例：保存到 Supabase
      // const { supabase } = await import('@/lib/supabase');
      // await supabase.from('performance_metrics').insert(performanceRecord);
      
      // 或者发送到分析服务
      // await sendToAnalyticsService(performanceRecord);
    }

    // 开发环境下记录到控制台
    if (process.env.NODE_ENV === 'development') {
      console.log('Performance Metrics Received:', {
        url: data.url,
        metrics: data.metrics,
        userAgent: data.userAgent.substring(0, 50) + '...',
        timestamp: new Date(data.timestamp).toISOString(),
      });
    }

    // 计算性能评分
    const score = calculatePerformanceScore(data.metrics);
    
    // 生成性能建议
    const recommendations = generateRecommendations(data.metrics);

    return createSuccessResponse({
      score,
      recommendations,
      message: 'Performance data recorded successfully',
    });
};

export const POST = withErrorHandler(handlePerformanceData);

/**
 * 获取性能统计数据
 */
const handleGetPerformanceStats = async (request: NextRequest) => {
    const { searchParams } = new URL(request.url);
    const timeRange = searchParams.get('timeRange') || '24h';
    const url = searchParams.get('url');

    // 在实际应用中，这里应该从数据库查询数据
    const mockStats = {
      timeRange,
      url,
      averageMetrics: {
        fcp: 1200,
        lcp: 2100,
        fid: 50,
        cls: 0.05,
        ttfb: 300,
      },
      p75Metrics: {
        fcp: 1800,
        lcp: 3200,
        fid: 100,
        cls: 0.1,
        ttfb: 500,
      },
      p95Metrics: {
        fcp: 2500,
        lcp: 4500,
        fid: 200,
        cls: 0.2,
        ttfb: 800,
      },
      totalSamples: 1000,
      performanceScore: 85,
      trends: {
        fcp: { change: -5, trend: 'improving' },
        lcp: { change: -8, trend: 'improving' },
        fid: { change: 2, trend: 'degrading' },
        cls: { change: -1, trend: 'stable' },
      },
    };

    return createSuccessResponse(mockStats);
};

export const GET = withErrorHandler(handleGetPerformanceStats);

/**
 * 计算性能评分
 */
function calculatePerformanceScore(metrics: PerformanceMetrics): number {
  let score = 100;
  
  // FCP 评分 (好: <1.8s, 需要改进: 1.8s-3s, 差: >3s)
  if (metrics.fcp > 3000) score -= 25;
  else if (metrics.fcp > 1800) score -= 10;
  
  // LCP 评分 (好: <2.5s, 需要改进: 2.5s-4s, 差: >4s)
  if (metrics.lcp > 4000) score -= 25;
  else if (metrics.lcp > 2500) score -= 10;
  
  // FID 评分 (好: <100ms, 需要改进: 100ms-300ms, 差: >300ms)
  if (metrics.fid > 300) score -= 25;
  else if (metrics.fid > 100) score -= 10;
  
  // CLS 评分 (好: <0.1, 需要改进: 0.1-0.25, 差: >0.25)
  if (metrics.cls > 0.25) score -= 25;
  else if (metrics.cls > 0.1) score -= 10;

  // TTFB 评分 (好: <600ms, 需要改进: 600ms-1s, 差: >1s)
  if (metrics.ttfb > 1000) score -= 10;
  else if (metrics.ttfb > 600) score -= 5;

  return Math.max(0, score);
}

/**
 * 生成性能优化建议
 */
function generateRecommendations(metrics: PerformanceMetrics): string[] {
  const recommendations: string[] = [];

  if (metrics.fcp > 1800) {
    recommendations.push('优化首次内容绘制 (FCP)：减少阻塞渲染的资源，优化关键渲染路径');
  }

  if (metrics.lcp > 2500) {
    recommendations.push('优化最大内容绘制 (LCP)：优化图片加载，使用预加载，减少服务器响应时间');
  }

  if (metrics.fid > 100) {
    recommendations.push('优化首次输入延迟 (FID)：减少主线程阻塞，优化 JavaScript 执行');
  }

  if (metrics.cls > 0.1) {
    recommendations.push('优化累积布局偏移 (CLS)：为图片和广告设置尺寸，避免动态内容插入');
  }

  if (metrics.ttfb > 600) {
    recommendations.push('优化首字节时间 (TTFB)：优化服务器响应时间，使用 CDN，启用缓存');
  }

  if (metrics.memoryUsage && metrics.memoryUsage > 0.8) {
    recommendations.push('优化内存使用：检查内存泄漏，优化大型对象的使用');
  }

  if (recommendations.length === 0) {
    recommendations.push('性能表现良好！继续保持当前的优化策略');
  }

  return recommendations;
}

/**
 * 发送数据到外部分析服务
 */
async function sendToAnalyticsService(data: any) {
  // 示例：发送到 Google Analytics
  if (process.env.GA_MEASUREMENT_ID) {
    try {
      await fetch(`https://www.google-analytics.com/mp/collect?measurement_id=${process.env.GA_MEASUREMENT_ID}&api_secret=${process.env.GA_API_SECRET}`, {
        method: 'POST',
        body: JSON.stringify({
          client_id: data.sessionId || 'anonymous',
          events: [{
            name: 'web_vitals',
            params: {
              metric_name: 'core_web_vitals',
              metric_value: data.metrics,
              page_location: data.url,
            }
          }]
        })
      });
    } catch (error) {
      console.error('Failed to send to Google Analytics:', error);
    }
  }

  // 示例：发送到自定义分析服务
  if (process.env.CUSTOM_ANALYTICS_ENDPOINT) {
    try {
      await fetch(process.env.CUSTOM_ANALYTICS_ENDPOINT, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.ANALYTICS_API_KEY}`,
        },
        body: JSON.stringify(data),
      });
    } catch (error) {
      console.error('Failed to send to custom analytics:', error);
    }
  }
}
