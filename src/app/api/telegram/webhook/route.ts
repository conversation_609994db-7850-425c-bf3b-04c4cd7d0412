import { NextRequest, NextResponse } from 'next/server';
import telegramService from '@/lib/telegram';

/**
 * Telegram Webhook API
 * 处理来自 Telegram Bot 的 Webhook 更新
 */

export async function POST(request: NextRequest) {
  try {
    // 验证请求来源（可选：验证 Telegram 的 secret token）
    const secretToken = request.headers.get('X-Telegram-Bot-Api-Secret-Token');
    const expectedToken = process.env.TELEGRAM_WEBHOOK_SECRET;
    
    if (expectedToken && secretToken !== expectedToken) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const update = await request.json();
    
    // 处理 Webhook 更新
    await telegramService.handleWebhookUpdate(update);
    
    return NextResponse.json({ ok: true });

  } catch (error) {
    console.error('Telegram webhook error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // 获取 Webhook 信息（用于调试）
    const botToken = process.env.TELEGRAM_BOT_TOKEN;
    
    if (!botToken) {
      return NextResponse.json(
        { error: 'Bot token not configured' },
        { status: 500 }
      );
    }

    const response = await fetch(`https://api.telegram.org/bot${botToken}/getWebhookInfo`);
    const data = await response.json();
    
    return NextResponse.json(data);

  } catch (error) {
    console.error('Get webhook info error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
