import { NextRequest, NextResponse } from 'next/server';
import telegramService from '@/lib/telegram';

/**
 * 生成 Telegram 绑定链接 API
 */

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId } = body;

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    // 生成绑定链接
    const bindingUrl = telegramService.generateBindingLink(userId);

    return NextResponse.json({
      success: true,
      bindingUrl,
      message: 'Binding link generated successfully',
    });

  } catch (error) {
    console.error('Generate link error:', error);
    return NextResponse.json(
      { error: 'Failed to generate binding link' },
      { status: 500 }
    );
  }
}
