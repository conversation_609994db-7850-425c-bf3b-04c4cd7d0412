import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import telegramService from '@/lib/telegram';

/**
 * 生成 Telegram 绑定验证码 API
 */

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId } = body;

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    // 检查是否为钱包地址格式
    const isWalletAddress = /^0x[a-fA-F0-9]{40}$/.test(userId);

    if (!isWalletAddress) {
      return NextResponse.json(
        { error: 'Invalid wallet address format' },
        { status: 400 }
      );
    }

    // 创建一个临时的用户对象
    const user = {
      id: userId, // 临时使用钱包地址作为ID
      wallet_address: userId.toLowerCase(),
      telegram_id: null
    };

    // 检查用户是否已经绑定
    if (user.telegram_id) {
      return NextResponse.json(
        { error: 'User already bound to Telegram' },
        { status: 409 }
      );
    }

    // 生成绑定验证码
    const bindingCode = telegramService.generateBindingCode(userId);
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10分钟后过期

    // 临时解决方案：暂时不保存到数据库，直接返回验证码
    // 保存验证码到数据库
    const { createVerificationCode } = await import('@/lib/verification-storage');

    const verificationRecord = await createVerificationCode({
      type: 'telegram',
      walletAddress,
      telegramId: telegramId.toString(),
      expirationMinutes: 10,
      maxAttempts: 3,
      metadata: {
        purpose: 'telegram_binding',
        userAgent: request.headers.get('user-agent'),
        ip: request.headers.get('x-forwarded-for') || 'unknown',
      },
    });
    console.log('Generated binding code for user:', userId, 'Code:', verificationRecord.code);

    // 生成绑定链接
    const bindingUrl = telegramService.generateBindingLink(userId);

    return NextResponse.json({
      success: true,
      bindingCode: verificationRecord.code,
      bindingUrl,
      expiresAt: verificationRecord.expiresAt,
      message: 'Binding code generated successfully',
    });

  } catch (error) {
    console.error('Generate binding code error:', error);
    return NextResponse.json(
      { error: 'Failed to generate binding code' },
      { status: 500 }
    );
  }
}

/**
 * 验证绑定验证码
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const bindingCode = searchParams.get('code');

    if (!bindingCode) {
      return NextResponse.json(
        { error: 'Binding code is required' },
        { status: 400 }
      );
    }

    // 查找验证码
    const { data: activities, error: codeError } = await supabase
      .from('user_activities')
      .select('*')
      .eq('activity_type', 'telegram_binding_code')
      .order('created_at', { ascending: false });

    if (codeError || !activities) {
      return NextResponse.json(
        { error: 'Failed to verify binding code' },
        { status: 500 }
      );
    }

    // 查找匹配的验证码
    const codeRecord = activities.find(activity =>
      activity.activity_data?.binding_code === bindingCode &&
      !activity.activity_data?.used
    );

    if (!codeRecord) {
      return NextResponse.json(
        { error: 'Invalid or expired binding code' },
        { status: 404 }
      );
    }

    // 检查是否过期
    const now = new Date();
    const expiresAt = new Date(codeRecord.activity_data.expires_at);

    if (now > expiresAt) {
      return NextResponse.json(
        { error: 'Binding code has expired' },
        { status: 410 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        userId: codeRecord.user_id,
        bindingCode: codeRecord.activity_data.binding_code,
        expiresAt: codeRecord.activity_data.expires_at,
        createdAt: codeRecord.created_at,
      },
    });

  } catch (error) {
    console.error('Verify binding code error:', error);
    return NextResponse.json(
      { error: 'Failed to verify binding code' },
      { status: 500 }
    );
  }
}
