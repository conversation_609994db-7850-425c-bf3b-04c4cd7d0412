import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

/**
 * Telegram 用户绑定 API
 * 处理用户账户与 Telegram 的绑定
 */

interface BindRequest {
  userId: string;
  telegramId: number;
  userInfo: {
    id: number;
    first_name: string;
    last_name?: string;
    username?: string;
    photo_url?: string;
  };
}

export async function POST(request: NextRequest) {
  try {
    const body: BindRequest = await request.json();
    const { userId, telegramId, userInfo } = body;

    // 验证请求数据
    if (!userId || !telegramId || !userInfo) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // 临时解决方案：直接创建一个模拟用户记录
    const isWalletAddress = /^0x[a-fA-F0-9]{40}$/.test(userId);

    if (!isWalletAddress) {
      return NextResponse.json(
        { error: 'Invalid wallet address format' },
        { status: 400 }
      );
    }

    // 创建一个临时的用户对象
    const existingUser = {
      id: userId, // 临时使用钱包地址作为ID
      wallet_address: userId.toLowerCase(),
      telegram_id: null,
      presale_eligible: false
    };

    // 检查是否已经绑定了其他 Telegram 账户
    if (existingUser.telegram_id && existingUser.telegram_id !== telegramId) {
      return NextResponse.json(
        { error: 'User already bound to another Telegram account' },
        { status: 409 }
      );
    }

    // 检查 Telegram 账户是否已被其他用户绑定
    const { data: existingBinding, error: bindingError } = await supabase
      .from('users')
      .select('id')
      .eq('telegram_id', telegramId)
      .neq('id', userId)
      .single();

    if (existingBinding) {
      return NextResponse.json(
        { error: 'Telegram account already bound to another user' },
        { status: 409 }
      );
    }

    // 更新用户信息，绑定 Telegram 并直接授予预售资格
    const { error: updateError } = await supabase
      .from('users')
      .update({
        telegram_id: telegramId,
        telegram_username: userInfo.username,
        telegram_first_name: userInfo.first_name,
        telegram_last_name: userInfo.last_name,
        telegram_photo_url: userInfo.photo_url,
        presale_eligible: true, // 绑定成功即获得预售资格
        presale_qualified_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .eq('id', existingUser.id);

    if (updateError) {
      console.error('Failed to update user:', updateError);
      return NextResponse.json(
        { error: 'Failed to bind Telegram account' },
        { status: 500 }
      );
    }

    // 记录绑定日志
    await supabase
      .from('user_activities')
      .insert({
        user_id: existingUser.id,
        activity_type: 'telegram_bind',
        activity_data: {
          telegram_id: telegramId,
          telegram_username: userInfo.username,
          timestamp: new Date().toISOString(),
        },
      });

    return NextResponse.json({
      success: true,
      message: 'Telegram account bound successfully',
      data: {
        userId,
        telegramId,
        username: userInfo.username,
      },
    });

  } catch (error) {
    console.error('Telegram bind error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    // 检查是否为钱包地址格式（以0x开头的42位字符串）
    const isWalletAddress = /^0x[a-fA-F0-9]{40}$/.test(userId);

    if (!isWalletAddress) {
      return NextResponse.json(
        { error: 'Invalid wallet address format' },
        { status: 400 }
      );
    }

    // 尝试从数据库获取用户信息，如果不存在则创建临时对象
    const { data: existingUser } = await supabase
      .from('users')
      .select('*')
      .eq('wallet_address', userId.toLowerCase())
      .single();

    const user = existingUser || {
      id: userId, // 临时使用钱包地址作为ID
      wallet_address: userId.toLowerCase(),
      telegram_id: null,
      telegram_username: null,
      telegram_first_name: null,
      presale_eligible: false
    };

    return NextResponse.json({
      success: true,
      data: {
        userId: user.id,
        walletAddress: user.wallet_address,
        isBound: !!user.telegram_id,
        telegramId: user.telegram_id,
        telegramUsername: user.telegram_username,
        telegramFirstName: user.telegram_first_name,
        presaleEligible: user.presale_eligible || false,
      },
    });

  } catch (error) {
    console.error('Get binding status error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
