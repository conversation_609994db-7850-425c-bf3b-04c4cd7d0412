import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

/**
 * Telegram 分享验证 API
 * 验证用户是否完成了推广内容分享
 */

interface VerifyShareRequest {
  telegramId: number;
  messageId: number;
  chatId: number;
  forwardInfo?: {
    from_chat?: any;
    from_user?: any;
  };
}

export async function POST(request: NextRequest) {
  try {
    const body: VerifyShareRequest = await request.json();
    const { telegramId, messageId, chatId, forwardInfo } = body;

    // 验证请求数据
    if (!telegramId || !messageId) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // 查找绑定的用户
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, telegram_id, presale_eligible, presale_participated')
      .eq('telegram_id', telegramId)
      .single();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'User not found or not bound' },
        { status: 404 }
      );
    }

    // 检查用户是否已经参与过预售
    if (user.presale_participated) {
      return NextResponse.json(
        { error: 'User has already participated in presale' },
        { status: 409 }
      );
    }

    // 检查是否已经验证过这个分享
    const { data: existingShare, error: shareError } = await supabase
      .from('telegram_shares')
      .select('id')
      .eq('user_id', user.id)
      .eq('message_id', messageId)
      .single();

    if (existingShare) {
      return NextResponse.json(
        { error: 'Share already verified' },
        { status: 409 }
      );
    }

    // 验证分享的有效性
    const shareValidation = validateShare(forwardInfo, chatId);
    
    if (!shareValidation.isValid) {
      return NextResponse.json(
        { error: shareValidation.reason },
        { status: 400 }
      );
    }

    // 记录分享信息
    const { error: insertError } = await supabase
      .from('telegram_shares')
      .insert({
        user_id: user.id,
        telegram_id: telegramId,
        message_id: messageId,
        chat_id: chatId,
        share_type: shareValidation.shareType,
        forward_info: forwardInfo,
        verified_at: new Date().toISOString(),
        created_at: new Date().toISOString(),
      });

    if (insertError) {
      console.error('Failed to record share:', insertError);
      return NextResponse.json(
        { error: 'Failed to record share' },
        { status: 500 }
      );
    }

    // 更新用户预售资格
    const { error: updateError } = await supabase
      .from('users')
      .update({
        presale_eligible: true,
        presale_qualified_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .eq('id', user.id);

    if (updateError) {
      console.error('Failed to update user eligibility:', updateError);
      return NextResponse.json(
        { error: 'Failed to update eligibility' },
        { status: 500 }
      );
    }

    // 记录活动日志
    await supabase
      .from('user_activities')
      .insert({
        user_id: user.id,
        activity_type: 'share_verified',
        activity_data: {
          telegram_id: telegramId,
          message_id: messageId,
          chat_id: chatId,
          share_type: shareValidation.shareType,
          timestamp: new Date().toISOString(),
        },
      });

    return NextResponse.json({
      success: true,
      message: 'Share verified successfully',
      data: {
        userId: user.id,
        presaleEligible: true,
        shareType: shareValidation.shareType,
        verifiedAt: new Date().toISOString(),
      },
    });

  } catch (error) {
    console.error('Verify share error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * 验证分享的有效性
 */
function validateShare(forwardInfo: any, chatId: number): {
  isValid: boolean;
  reason?: string;
  shareType?: 'group' | 'channel' | 'private';
} {
  // 如果有转发信息，说明是转发消息
  if (forwardInfo) {
    if (forwardInfo.from_chat) {
      // 转发自频道或群组
      const chat = forwardInfo.from_chat;
      
      if (chat.type === 'channel') {
        return {
          isValid: true,
          shareType: 'channel',
        };
      } else if (chat.type === 'group' || chat.type === 'supergroup') {
        return {
          isValid: true,
          shareType: 'group',
        };
      }
    } else if (forwardInfo.from_user) {
      // 转发自用户
      return {
        isValid: true,
        shareType: 'private',
      };
    }
  }

  // 检查聊天类型
  if (chatId < 0) {
    // 负数 ID 通常表示群组或频道
    return {
      isValid: true,
      shareType: 'group',
    };
  }

  // 默认认为是有效的私人分享
  return {
    isValid: true,
    shareType: 'private',
  };
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const telegramId = searchParams.get('telegramId');

    if (!telegramId) {
      return NextResponse.json(
        { error: 'Telegram ID is required' },
        { status: 400 }
      );
    }

    // 获取用户的分享记录
    const { data: shares, error } = await supabase
      .from('telegram_shares')
      .select('*')
      .eq('telegram_id', parseInt(telegramId))
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Failed to get shares:', error);
      return NextResponse.json(
        { error: 'Failed to get share history' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        shares: shares || [],
        totalShares: shares?.length || 0,
        hasVerifiedShare: shares && shares.length > 0,
      },
    });

  } catch (error) {
    console.error('Get shares error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
