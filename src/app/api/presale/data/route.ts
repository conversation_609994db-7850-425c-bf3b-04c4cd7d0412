import { NextRequest, NextResponse } from 'next/server';
import { createApiError, handleApiError, createSuccessResponse } from '@/lib/api-error-handler';

// 更新的预售配置
const PRESALE_CONFIG = {
  TOTAL_TOKENS: 200_000_000, // 2亿代币
  TARGET_BNB: 320, // 目标募集320 BNB
  STAGES: 100, // 100个阶段
  TOKENS_PER_STAGE: 2_000_000, // 每阶段200万代币
  INITIAL_RATE: 1_912_125, // 初始汇率：1 BNB = 1,912,125 HAOX
  FINAL_RATE: 273_732, // 最终汇率：1 BNB = 273,732 HAOX
  PRICE_DECREASE_RATE: 0.98, // 每阶段汇率降低到98%（价格上涨2%）
  MIN_INVESTMENT: 0.1, // 最低投资0.1 BNB
  MAX_INVESTMENT: 20, // 最高投资20 BNB
} as const;

/**
 * 计算指定阶段的汇率
 */
function getStageRate(stage: number): number {
  if (stage < 0 || stage >= PRESALE_CONFIG.STAGES) {
    throw new Error('Invalid stage number');
  }

  let rate = PRESALE_CONFIG.INITIAL_RATE;
  for (let i = 0; i < stage; i++) {
    rate = rate * PRESALE_CONFIG.PRICE_DECREASE_RATE;
  }
  return Math.floor(rate);
}

/**
 * 计算HAOX的USD价格（基于BNB价格）
 */
function calculateHaoxUsdPrice(haoxPerBnb: number, bnbUsdPrice: number = 850): number {
  return bnbUsdPrice / haoxPerBnb;
}

export async function GET(request: NextRequest) {
  try {
    // 模拟当前预售状态
    const currentStage = 15; // 当前阶段
    const tokensRemainingInStage = 1_200_000; // 当前阶段剩余代币
    const totalBNBRaised = 48.5; // 已募集BNB
    const totalTokensSold = 30_000_000; // 已售出代币

    // 计算当前和下一阶段的汇率
    const currentRate = getStageRate(currentStage);
    const nextRate = currentStage < PRESALE_CONFIG.STAGES - 1 ? getStageRate(currentStage + 1) : PRESALE_CONFIG.FINAL_RATE;

    // 计算USD价格
    const bnbUsdPrice = 850; // 可以从价格预言机获取
    const currentUsdPrice = calculateHaoxUsdPrice(currentRate, bnbUsdPrice);
    const nextUsdPrice = calculateHaoxUsdPrice(nextRate, bnbUsdPrice);

    const presaleData = {
      // 基本信息
      participantCount: 1247 + Math.floor(Math.random() * 50),
      isActive: true,

      // 阶段信息
      currentStage: currentStage + 1, // 显示为1-based
      totalStages: PRESALE_CONFIG.STAGES,
      tokensRemainingInStage,
      tokensPerStage: PRESALE_CONFIG.TOKENS_PER_STAGE,

      // 价格信息
      currentRate, // HAOX per BNB
      nextRate, // HAOX per BNB
      currentPrice: currentUsdPrice, // USD per HAOX
      nextPrice: nextUsdPrice, // USD per HAOX
      bnbUsdPrice,

      // 进度信息
      totalRaised: totalBNBRaised,
      targetAmount: PRESALE_CONFIG.TARGET_BNB,
      totalTokensSold,
      totalTokensForSale: PRESALE_CONFIG.TOTAL_TOKENS,
      progressPercentage: (totalBNBRaised / PRESALE_CONFIG.TARGET_BNB) * 100,

      // 投资限制
      minInvestment: PRESALE_CONFIG.MIN_INVESTMENT,
      maxInvestment: PRESALE_CONFIG.MAX_INVESTMENT,

      // 合约信息
      walletAddress: process.env.NEXT_PUBLIC_HAOX_CONTRACT_ADDRESS_BSC || '******************************************',

      // 时间信息
      endTime: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(), // 15天后结束
      // 最近购买记录
      recentPurchases: [
        {
          amount: 1_500_000 + Math.floor(Math.random() * 500_000), // HAOX数量
          bnbAmount: 0.8,
          timestamp: new Date(Date.now() - 2 * 60 * 1000).toISOString(),
          address: `0x${Math.random().toString(16).substr(2, 4)}...${Math.random().toString(16).substr(2, 4)}`,
          txHash: `0x${Math.random().toString(16).substr(2, 8)}...`,
          stage: currentStage
        },
        {
          amount: 3_200_000 + Math.floor(Math.random() * 800_000),
          bnbAmount: 1.7,
          timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
          address: `0x${Math.random().toString(16).substr(2, 4)}...${Math.random().toString(16).substr(2, 4)}`,
          txHash: `0x${Math.random().toString(16).substr(2, 8)}...`,
          stage: currentStage - 1
        },
        {
          amount: 950_000 + Math.floor(Math.random() * 200_000),
          bnbAmount: 0.5,
          timestamp: new Date(Date.now() - 8 * 60 * 1000).toISOString(),
          address: `0x${Math.random().toString(16).substr(2, 4)}...${Math.random().toString(16).substr(2, 4)}`,
          txHash: `0x${Math.random().toString(16).substr(2, 8)}...`,
          stage: currentStage - 1
        }
      ],

      // 阶段价格历史
      stageHistory: Array.from({ length: Math.min(currentStage + 1, 10) }, (_, i) => {
        const stage = Math.max(0, currentStage - 9 + i);
        return {
          stage: stage + 1,
          rate: getStageRate(stage),
          usdPrice: calculateHaoxUsdPrice(getStageRate(stage), bnbUsdPrice),
          completed: stage < currentStage
        };
      })
    };

    return createSuccessResponse(presaleData);

  } catch (error) {
    console.error('Presale data fetch error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch presale data',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * @api {POST} /api/presale/data 计算购买预览
 * @apiDescription 计算用户投入指定BNB数量能获得的HAOX代币数量
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { bnbAmount } = body;

    if (!bnbAmount || bnbAmount <= 0) {
      throw createApiError(
        'VALIDATION_ERROR' as any,
        'Valid BNB amount is required',
        400
      );
    }

    if (bnbAmount < PRESALE_CONFIG.MIN_INVESTMENT || bnbAmount > PRESALE_CONFIG.MAX_INVESTMENT) {
      throw createApiError(
        'VALIDATION_ERROR' as any,
        `Investment must be between ${PRESALE_CONFIG.MIN_INVESTMENT} and ${PRESALE_CONFIG.MAX_INVESTMENT} BNB`,
        400
      );
    }

    // 模拟跨阶段购买计算
    const currentStage = 15;
    const tokensRemainingInStage = 1_200_000;

    let remainingBNB = bnbAmount;
    let totalTokens = 0;
    let stage = currentStage;
    let stageTokensRemaining = tokensRemainingInStage;
    const purchaseBreakdown = [];

    while (remainingBNB > 0 && stage < PRESALE_CONFIG.STAGES) {
      const stageRate = getStageRate(stage);
      const maxTokensFromBNB = remainingBNB * stageRate;

      if (maxTokensFromBNB <= stageTokensRemaining) {
        // 当前阶段可以满足剩余需求
        totalTokens += maxTokensFromBNB;
        purchaseBreakdown.push({
          stage: stage + 1,
          bnbAmount: remainingBNB,
          tokenAmount: maxTokensFromBNB,
          rate: stageRate,
          usdPrice: calculateHaoxUsdPrice(stageRate)
        });
        remainingBNB = 0;
      } else {
        // 需要进入下一阶段
        const bnbForThisStage = stageTokensRemaining / stageRate;
        totalTokens += stageTokensRemaining;
        purchaseBreakdown.push({
          stage: stage + 1,
          bnbAmount: bnbForThisStage,
          tokenAmount: stageTokensRemaining,
          rate: stageRate,
          usdPrice: calculateHaoxUsdPrice(stageRate)
        });
        remainingBNB -= bnbForThisStage;

        // 进入下一阶段
        stage++;
        stageTokensRemaining = PRESALE_CONFIG.TOKENS_PER_STAGE;
      }
    }

    if (remainingBNB > 0) {
      throw createApiError(
        'VALIDATION_ERROR' as any,
        'Insufficient tokens available for this purchase amount',
        400
      );
    }

    // 计算平均价格
    const avgRate = totalTokens / bnbAmount;
    const avgUsdPrice = calculateHaoxUsdPrice(avgRate);

    return createSuccessResponse({
      bnbAmount,
      totalTokens: Math.floor(totalTokens),
      avgRate: Math.floor(avgRate),
      avgUsdPrice,
      purchaseBreakdown,
      estimatedGasFee: 0.001, // 估算Gas费
      finalStage: stage + 1
    });

  } catch (error) {
    return handleApiError(error);
  }
}

// 配置运行时
export const runtime = 'nodejs';
