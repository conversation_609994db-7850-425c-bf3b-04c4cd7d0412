import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { amount, paymentMethod, walletAddress } = await request.json();

    // Validate input
    if (!amount || amount <= 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid amount',
          message: 'Amount must be greater than 0'
        },
        { status: 400 }
      );
    }

    if (!paymentMethod || !['crypto', 'fiat'].includes(paymentMethod)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid payment method',
          message: 'Payment method must be either "crypto" or "fiat"'
        },
        { status: 400 }
      );
    }

    if (!walletAddress) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing wallet address',
          message: 'Wallet address is required'
        },
        { status: 400 }
      );
    }

    // Simulate presale participation logic
    const currentPrice = 0.0008; // USD per HAOX
    const totalCost = amount * currentPrice;
    const transactionId = `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // In a real implementation, you would:
    // 1. Verify user authentication
    // 2. Check presale availability and limits
    // 3. Process payment (crypto or fiat)
    // 4. Update database with purchase record
    // 5. Send confirmation email
    // 6. Update presale statistics

    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Mock response
    const participationResult = {
      transactionId,
      amount,
      totalCost,
      paymentMethod,
      walletAddress,
      status: 'confirmed',
      estimatedDelivery: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours
      blockchainTxHash: paymentMethod === 'crypto' ? `0x${Math.random().toString(16).substr(2, 64)}` : null
    };

    return NextResponse.json({
      success: true,
      message: 'Presale participation successful',
      data: participationResult,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Presale participation error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Presale participation failed',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get presale participation status/history for a user
    const { searchParams } = new URL(request.url);
    const walletAddress = searchParams.get('wallet');

    if (!walletAddress) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing wallet address',
          message: 'Wallet address parameter is required'
        },
        { status: 400 }
      );
    }

    // In a real implementation, fetch from database
    const mockParticipations = [
      {
        id: 'participation_1',
        amount: 50000,
        totalCost: 40,
        paymentMethod: 'crypto',
        status: 'confirmed',
        timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        transactionId: 'tx_1234567890'
      },
      {
        id: 'participation_2',
        amount: 25000,
        totalCost: 20,
        paymentMethod: 'fiat',
        status: 'pending',
        timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
        transactionId: 'tx_0987654321'
      }
    ];

    return NextResponse.json({
      success: true,
      data: {
        walletAddress,
        participations: mockParticipations,
        totalAmount: mockParticipations.reduce((sum, p) => sum + p.amount, 0),
        totalSpent: mockParticipations.reduce((sum, p) => sum + p.totalCost, 0)
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Presale participation history error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch participation history',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
