import { NextRequest, NextResponse } from 'next/server';

// 定时任务：每小时更新用户认证等级
export async function GET(request: NextRequest) {
  try {
    // 验证定时任务权限
    const authHeader = request.headers.get('authorization');
    const cronSecret = process.env.CRON_SECRET;
    
    if (!cronSecret || authHeader !== `Bearer ${cronSecret}`) {
      return NextResponse.json(
        { success: false, message: '无权限执行定时任务' },
        { status: 401 }
      );
    }

    console.log('🕐 开始执行认证等级定时更新任务');

    // 调用批量更新API
    const updateResponse = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/certification/update`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${cronSecret}`,
        'Content-Type': 'application/json'
      }
    });

    const updateResult = await updateResponse.json();

    if (updateResult.success) {
      console.log(`✅ 认证等级更新完成: ${updateResult.data.successful}/${updateResult.data.total} 成功`);
      
      return NextResponse.json({
        success: true,
        message: '认证等级定时更新完成',
        data: updateResult.data,
        timestamp: new Date().toISOString()
      });
    } else {
      console.error('❌ 认证等级更新失败:', updateResult.message);
      
      return NextResponse.json(
        { success: false, message: updateResult.message },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('❌ 定时任务执行失败:', error);
    
    return NextResponse.json(
      { success: false, message: '定时任务执行失败' },
      { status: 500 }
    );
  }
}

// 手动触发定时任务（开发环境使用）
export async function POST(request: NextRequest) {
  try {
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json(
        { success: false, message: '仅开发环境可手动触发' },
        { status: 403 }
      );
    }

    console.log('🔧 开发环境：手动触发认证等级更新');

    // 模拟定时任务执行
    const mockResult = {
      success: true,
      message: '开发环境模拟更新完成',
      data: {
        total: 10,
        successful: 8,
        failed: 2,
        errors: ['用户A: 钱包地址无效', '用户B: 网络超时']
      },
      timestamp: new Date().toISOString()
    };

    return NextResponse.json(mockResult);

  } catch (error) {
    console.error('手动触发定时任务失败:', error);
    
    return NextResponse.json(
      { success: false, message: '手动触发失败' },
      { status: 500 }
    );
  }
}
