/**
 * 示例受保护的API路由
 * 展示如何使用中间件组合
 */

// 配置Edge Runtime
// export const runtime = 'edge'; // 禁用Edge Runtime，因为需要JWT验证

import { NextRequest } from 'next/server';
import { 
  withErrorHandler, 
  createSuccessResponse,
  createErrorResponse,
  createApiError,
  ApiErrorCode
} from '@/lib/api-error-handler';
import { 
  withAuth, 
  withRateLimit, 
  withLogging, 
  compose,
  AuthenticatedRequest 
} from '@/lib/api-middleware';

/**
 * 获取用户数据的受保护端点
 */
const getUserData = async (request: AuthenticatedRequest) => {
  // 这里可以安全地访问 request.user
  const userData = {
    id: request.user.userId,
    username: request.user.username,
    firstName: request.user.firstName,
    lastName: request.user.lastName,
    photoUrl: request.user.photoUrl,
    lastAccess: new Date().toISOString(),
  };

  return createSuccessResponse(userData, '用户数据获取成功');
};

/**
 * 更新用户设置的受保护端点
 */
const updateUserSettings = async (request: AuthenticatedRequest) => {
  const body = await request.json();
  
  // 模拟更新用户设置
  const updatedSettings = {
    userId: request.user.userId,
    settings: body,
    updatedAt: new Date().toISOString(),
  };

  return createSuccessResponse(updatedSettings, '设置更新成功');
};

// 组合中间件：错误处理 + 日志 + 限流 + 认证
export const GET = compose(
  withErrorHandler,
  withLogging,
  withRateLimit(60, 60000), // 每分钟最多60次请求
  withAuth
)(getUserData);

export const POST = compose(
  withErrorHandler,
  withLogging,
  withRateLimit(30, 60000), // 每分钟最多30次请求
  withAuth
)(updateUserSettings);
