import { NextRequest } from 'next/server';
import { withI18nErrorHandling, createSuccessResponse, createValidationError } from '@/lib/middleware/errorMiddleware';
import { ApiI18n } from '@/lib/middleware/i18nMiddleware';

// 演示国际化API功能
export const GET = withI18nErrorHandling(async (request: NextRequest, i18n: ApiI18n) => {
  const url = new URL(request.url);
  const scenario = url.searchParams.get('scenario') || 'basic';

  switch (scenario) {
    case 'basic':
      return handleBasicI18n(i18n);
    
    case 'formatting':
      return handleFormatting(i18n);
    
    case 'errors':
      return handleErrors(i18n);
    
    case 'socialbet':
      return handleSocialBetMessages(i18n);
    
    case 'fortune':
      return handleFortuneMessages(i18n);
    
    default:
      throw createValidationError(
        i18n.getErrorMessage('validation', 'Invalid scenario parameter'),
        'scenario',
        scenario
      );
  }
});

// 基础国际化演示
async function handleBasicI18n(i18n: ApiI18n) {
  const data = {
    locale: i18n.getLocale(),
    messages: {
      welcome: i18n.t('common.loading'),
      navigation: {
        home: i18n.t('navigation.home'),
        socialBet: i18n.t('navigation.socialBet'),
        fortune: i18n.t('navigation.fortune'),
        certification: i18n.t('navigation.certification')
      },
      common: {
        loading: i18n.t('common.loading'),
        success: i18n.t('common.success'),
        error: i18n.t('common.error'),
        cancel: i18n.t('common.cancel'),
        confirm: i18n.t('common.confirm'),
        save: i18n.t('common.save'),
        submit: i18n.t('common.submit')
      }
    },
    timestamp: new Date().toISOString()
  };

  return createSuccessResponse(data);
}

// 格式化功能演示
async function handleFormatting(i18n: ApiI18n) {
  const now = new Date();
  const testNumber = 1234567.89;
  const testAmount = 1000.123456;

  const data = {
    locale: i18n.getLocale(),
    formatting: {
      numbers: {
        basic: i18n.formatNumber(testNumber),
        currency: i18n.formatNumber(testNumber, { 
          style: 'currency', 
          currency: i18n.getLocale() === 'zh' ? 'CNY' : 'USD' 
        }),
        percentage: i18n.formatNumber(0.1234, { style: 'percent' }),
        compact: i18n.formatNumber(testNumber, { notation: 'compact' })
      },
      dates: {
        full: i18n.formatDate(now),
        short: i18n.formatDate(now, { 
          year: 'numeric', 
          month: 'short', 
          day: 'numeric' 
        }),
        time: i18n.formatDate(now, { 
          hour: '2-digit', 
          minute: '2-digit' 
        }),
        relative: getRelativeTime(now, i18n.getLocale())
      },
      amounts: {
        haox: `${i18n.formatNumber(testAmount, { minimumFractionDigits: 2, maximumFractionDigits: 6 })} HAOX`,
        fortune: `${i18n.formatNumber(testAmount, { minimumFractionDigits: 2, maximumFractionDigits: 6 })} Fortune`
      }
    },
    timestamp: now.toISOString()
  };

  return createSuccessResponse(data);
}

// 错误消息演示
async function handleErrors(i18n: ApiI18n) {
  const data = {
    locale: i18n.getLocale(),
    errorMessages: {
      general: i18n.getErrorMessage('general'),
      network: i18n.getErrorMessage('network'),
      timeout: i18n.getErrorMessage('timeout'),
      unauthorized: i18n.getErrorMessage('unauthorized'),
      forbidden: i18n.getErrorMessage('forbidden'),
      notFound: i18n.getErrorMessage('notFound'),
      validation: i18n.getErrorMessage('validation'),
      database: i18n.getErrorMessage('database'),
      blockchain: i18n.getErrorMessage('blockchain'),
      rateLimit: i18n.getErrorMessage('rateLimit'),
      insufficientBalance: i18n.getErrorMessage('insufficientBalance'),
      invalidAddress: i18n.getErrorMessage('invalidAddress'),
      transactionFailed: i18n.getErrorMessage('transactionFailed')
    },
    successMessages: {
      general: i18n.getSuccessMessage('general'),
      saved: i18n.getSuccessMessage('saved'),
      created: i18n.getSuccessMessage('created'),
      updated: i18n.getSuccessMessage('updated'),
      deleted: i18n.getSuccessMessage('deleted'),
      submitted: i18n.getSuccessMessage('submitted')
    },
    timestamp: new Date().toISOString()
  };

  return createSuccessResponse(data);
}

// Social Bet 消息演示
async function handleSocialBetMessages(i18n: ApiI18n) {
  const data = {
    locale: i18n.getLocale(),
    socialBet: {
      title: i18n.t('socialBet.title'),
      subtitle: i18n.t('socialBet.subtitle'),
      actions: {
        createBet: i18n.t('socialBet.createBet'),
        joinBet: i18n.t('socialBet.joinBet'),
        myBets: i18n.t('socialBet.myBets'),
        allBets: i18n.t('socialBet.allBets')
      },
      betTypes: {
        '1v1': i18n.t('socialBet.betType.1v1'),
        '1vN': i18n.t('socialBet.betType.1vN')
      },
      betStatus: {
        draft: i18n.t('socialBet.betStatus.draft'),
        open: i18n.t('socialBet.betStatus.open'),
        active: i18n.t('socialBet.betStatus.active'),
        judging: i18n.t('socialBet.betStatus.judging'),
        confirming: i18n.t('socialBet.betStatus.confirming'),
        settled: i18n.t('socialBet.betStatus.settled'),
        cancelled: i18n.t('socialBet.betStatus.cancelled')
      },
      judgment: {
        title: i18n.t('socialBet.judgment.title'),
        round1: i18n.t('socialBet.judgment.round1'),
        round2: i18n.t('socialBet.judgment.round2'),
        round3: i18n.t('socialBet.judgment.round3'),
        voteFor: i18n.t('socialBet.judgment.voteFor'),
        voteAgainst: i18n.t('socialBet.judgment.voteAgainst'),
        submitVote: i18n.t('socialBet.judgment.submitVote')
      }
    },
    timestamp: new Date().toISOString()
  };

  return createSuccessResponse(data);
}

// Fortune 消息演示
async function handleFortuneMessages(i18n: ApiI18n) {
  const data = {
    locale: i18n.getLocale(),
    fortune: {
      title: i18n.t('fortune.title'),
      subtitle: i18n.t('fortune.subtitle'),
      balance: i18n.t('fortune.balance'),
      actions: {
        dailyCheckin: i18n.t('fortune.dailyCheckin'),
        invite: i18n.t('fortune.invite'),
        share: i18n.t('fortune.share'),
        history: i18n.t('fortune.history'),
        exchange: i18n.t('fortune.exchange'),
        deposit: i18n.t('fortune.deposit'),
        withdraw: i18n.t('fortune.withdraw')
      },
      depositForm: {
        title: i18n.t('fortune.depositForm.title'),
        amount: i18n.t('fortune.depositForm.amount'),
        txHash: i18n.t('fortune.depositForm.txHash'),
        walletAddress: i18n.t('fortune.depositForm.walletAddress'),
        submit: i18n.t('fortune.depositForm.submit'),
        exchangeRate: i18n.t('fortune.depositForm.exchangeRate'),
        fee: i18n.t('fortune.depositForm.fee'),
        netAmount: i18n.t('fortune.depositForm.netAmount')
      },
      transactionHistory: {
        title: i18n.t('fortune.transactionHistory.title'),
        type: i18n.t('fortune.transactionHistory.type'),
        amount: i18n.t('fortune.transactionHistory.amount'),
        status: i18n.t('fortune.transactionHistory.status'),
        time: i18n.t('fortune.transactionHistory.time'),
        deposit: i18n.t('fortune.transactionHistory.deposit'),
        withdrawal: i18n.t('fortune.transactionHistory.withdrawal'),
        pending: i18n.t('fortune.transactionHistory.pending'),
        processing: i18n.t('fortune.transactionHistory.processing'),
        completed: i18n.t('fortune.transactionHistory.completed'),
        failed: i18n.t('fortune.transactionHistory.failed')
      }
    },
    timestamp: new Date().toISOString()
  };

  return createSuccessResponse(data);
}

// 获取相对时间（简化版本）
function getRelativeTime(date: Date, locale: string): string {
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
  
  const localeCode = locale === 'zh' ? 'zh-CN' : 'en-US';
  const rtf = new Intl.RelativeTimeFormat(localeCode, { numeric: 'auto' });
  
  if (Math.abs(diffInSeconds) < 60) {
    return rtf.format(-diffInSeconds, 'second');
  } else if (Math.abs(diffInSeconds) < 3600) {
    return rtf.format(-Math.floor(diffInSeconds / 60), 'minute');
  } else {
    return rtf.format(-Math.floor(diffInSeconds / 3600), 'hour');
  }
}
