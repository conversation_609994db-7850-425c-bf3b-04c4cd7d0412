/**
 * 优化版国际化演示API
 * 集成性能中间件，缓存和压缩
 */

import { NextRequest, NextResponse } from 'next/server';
import { performanceMiddleware } from '@/lib/middleware/performanceMiddleware';
import { i18nMiddleware } from '@/lib/middleware/i18nMiddleware';

// 预编译的消息缓存
const messageCache = new Map<string, any>();

// 初始化消息缓存
function initializeMessageCache() {
  if (messageCache.size === 0) {
    // 中文消息
    messageCache.set('zh', {
      welcome: "欢迎使用SocioMint",
      navigation: {
        home: "首页",
        socialBet: "社交赌注",
        fortune: "福气中心",
        certification: "认证等级"
      },
      common: {
        loading: "加载中...",
        success: "成功",
        error: "错误",
        cancel: "取消",
        confirm: "确认",
        save: "保存",
        submit: "提交"
      },
      socialBet: {
        title: "社交赌注",
        create: "创建赌约",
        myBets: "我的赌约",
        judgment: "参与裁定",
        leaderboard: "排行榜",
        history: "裁定历史"
      },
      fortune: {
        title: "福气中心",
        balance: "福气余额",
        exchange: "充值提现",
        rewards: "奖励记录",
        daily: "每日签到"
      }
    });

    // 英文消息
    messageCache.set('en', {
      welcome: "Welcome to SocioMint",
      navigation: {
        home: "Home",
        socialBet: "Social Bet",
        fortune: "Fortune Center",
        certification: "Certification"
      },
      common: {
        loading: "Loading...",
        success: "Success",
        error: "Error",
        cancel: "Cancel",
        confirm: "Confirm",
        save: "Save",
        submit: "Submit"
      },
      socialBet: {
        title: "Social Bet",
        create: "Create Bet",
        myBets: "My Bets",
        judgment: "Join Judgment",
        leaderboard: "Leaderboard",
        history: "Judgment History"
      },
      fortune: {
        title: "Fortune Center",
        balance: "Fortune Balance",
        exchange: "Exchange",
        rewards: "Reward Records",
        daily: "Daily Check-in"
      }
    });
  }
}

// 快速响应处理器
async function fastHandler(request: NextRequest): Promise<NextResponse> {
  const startTime = Date.now();
  
  // 初始化缓存
  initializeMessageCache();
  
  // 解析查询参数
  const { searchParams } = new URL(request.url);
  const scenario = searchParams.get('scenario') || 'basic';
  const lang = searchParams.get('lang') || 'zh';
  
  // 验证语言
  const supportedLangs = ['zh', 'en'];
  const locale = supportedLangs.includes(lang) ? lang : 'zh';
  
  // 从缓存获取消息
  const messages = messageCache.get(locale);
  
  // 根据场景返回不同的数据
  let responseData: any;
  
  switch (scenario) {
    case 'basic':
      responseData = {
        locale,
        messages: {
          welcome: messages.welcome,
          navigation: messages.navigation,
          common: messages.common
        }
      };
      break;
      
    case 'socialbet':
      responseData = {
        locale,
        messages: {
          ...messages.common,
          socialBet: messages.socialBet
        }
      };
      break;
      
    case 'fortune':
      responseData = {
        locale,
        messages: {
          ...messages.common,
          fortune: messages.fortune
        }
      };
      break;
      
    case 'full':
      responseData = {
        locale,
        messages
      };
      break;
      
    default:
      responseData = {
        locale,
        messages: messages.common
      };
  }
  
  const endTime = Date.now();
  const responseTime = endTime - startTime;
  
  // 构建响应
  const response = {
    success: true,
    meta: {
      requestId: `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
      version: "1.0.0",
      responseTime: `${responseTime}ms`,
      cached: false
    },
    data: responseData,
    timestamp: new Date().toISOString()
  };
  
  return new NextResponse(JSON.stringify(response), {
    status: 200,
    headers: {
      'Content-Type': 'application/json',
      'Cache-Control': 'public, max-age=300', // 5分钟浏览器缓存
      'X-Response-Time': `${responseTime}ms`
    }
  });
}

// GET请求处理
export async function GET(request: NextRequest) {
  return performanceMiddleware(request, fastHandler);
}

// 性能指标端点
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    if (body.action === 'metrics') {
      const { getPerformanceMetrics } = await import('@/lib/middleware/performanceMiddleware');
      const metrics = getPerformanceMetrics();
      
      return NextResponse.json({
        success: true,
        data: metrics,
        timestamp: new Date().toISOString()
      });
    }
    
    if (body.action === 'clear-cache') {
      const { clearCache } = await import('@/lib/middleware/performanceMiddleware');
      clearCache();
      messageCache.clear();
      
      return NextResponse.json({
        success: true,
        message: 'Cache cleared successfully',
        timestamp: new Date().toISOString()
      });
    }
    
    return NextResponse.json({
      success: false,
      error: 'Invalid action'
    }, { status: 400 });
    
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'Invalid request body'
    }, { status: 400 });
  }
}
