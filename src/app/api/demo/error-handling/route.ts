import { NextRequest } from 'next/server';
import { 
  withErrorHandling, 
  withDatabaseErrorHandling, 
  withBlockchainErrorHandling,
  withGracefulDegradation,
  createSuccessResponse, 
  createValidationError,
  createBusinessLogicError,
  createRateLimitError
} from '@/lib/middleware/errorMiddleware';
import { monitoringService } from '@/lib/services/monitoringService';
import { supabase } from '@/lib/supabase';
import { haoxService } from '@/lib/blockchain/haoxService';

// 演示各种错误处理场景
export const GET = withErrorHandling(async (request: NextRequest, context) => {
  const startTime = Date.now();
  const url = new URL(request.url);
  const scenario = url.searchParams.get('scenario') || 'success';

  try {
    // 记录API请求开始
    monitoringService.recordApiRequest(
      '/api/demo/error-handling',
      'GET',
      200,
      0
    );

    switch (scenario) {
      case 'success':
        return await handleSuccessScenario();
      
      case 'validation':
        return await handleValidationScenario(url);
      
      case 'database':
        return await handleDatabaseScenario();
      
      case 'blockchain':
        return await handleBlockchainScenario();
      
      case 'business-logic':
        return await handleBusinessLogicScenario();
      
      case 'rate-limit':
        return await handleRateLimitScenario();
      
      case 'graceful-degradation':
        return await handleGracefulDegradationScenario();
      
      case 'unknown':
        return await handleUnknownErrorScenario();
      
      default:
        throw createValidationError('无效的场景参数', 'scenario', scenario);
    }
  } finally {
    // 记录API请求完成时间
    const responseTime = Date.now() - startTime;
    monitoringService.recordApiRequest(
      '/api/demo/error-handling',
      'GET',
      200,
      responseTime
    );
  }
});

// 成功场景
async function handleSuccessScenario() {
  const data = {
    message: '✅ 成功场景演示',
    timestamp: new Date().toISOString(),
    features: [
      '统一错误处理',
      '自动重试机制',
      '优雅降级',
      '监控和告警',
      '标准化响应格式'
    ]
  };

  return createSuccessResponse(data);
}

// 验证错误场景
async function handleValidationScenario(url: URL) {
  const requiredParam = url.searchParams.get('required');
  
  if (!requiredParam) {
    throw createValidationError(
      '缺少必需参数: required',
      'required',
      null
    );
  }

  if (requiredParam.length < 3) {
    throw createValidationError(
      'required参数长度必须至少3个字符',
      'required',
      requiredParam
    );
  }

  return createSuccessResponse({
    message: '✅ 验证通过',
    required: requiredParam
  });
}

// 数据库错误场景
async function handleDatabaseScenario() {
  return await withDatabaseErrorHandling(async () => {
    // 模拟数据库操作
    const { data, error } = await supabase
      .from('non_existent_table')
      .select('*')
      .limit(1);

    if (error) {
      throw new Error(`数据库操作失败: ${error.message}`);
    }

    return createSuccessResponse({
      message: '✅ 数据库操作成功',
      data
    });
  }, { operation: 'demo_database_query' });
}

// 区块链错误场景
async function handleBlockchainScenario() {
  return await withBlockchainErrorHandling(async () => {
    // 模拟区块链操作
    const balance = await haoxService.getHAOXBalance('0xinvalid_address');
    
    return createSuccessResponse({
      message: '✅ 区块链操作成功',
      balance
    });
  }, { operation: 'demo_blockchain_query' });
}

// 业务逻辑错误场景
async function handleBusinessLogicScenario() {
  const userBalance = 100;
  const requestedAmount = 150;

  if (requestedAmount > userBalance) {
    throw createBusinessLogicError(
      '余额不足，无法完成操作',
      'INSUFFICIENT_BALANCE',
      {
        userBalance,
        requestedAmount,
        shortfall: requestedAmount - userBalance
      }
    );
  }

  return createSuccessResponse({
    message: '✅ 业务逻辑验证通过',
    userBalance,
    requestedAmount
  });
}

// 速率限制错误场景
async function handleRateLimitScenario() {
  // 模拟速率限制检查
  const isRateLimited = Math.random() > 0.5;
  
  if (isRateLimited) {
    throw createRateLimitError(
      '请求过于频繁，请稍后重试',
      60 // 60秒后重试
    );
  }

  return createSuccessResponse({
    message: '✅ 速率限制检查通过'
  });
}

// 优雅降级场景
async function handleGracefulDegradationScenario() {
  return await withGracefulDegradation(
    // 主要操作（可能失败）
    async () => {
      // 模拟主要服务失败
      throw new Error('主要服务不可用');
    },
    // 降级操作
    async () => {
      return createSuccessResponse({
        message: '⚠️ 使用降级服务',
        source: 'fallback',
        note: '主要服务不可用，已切换到备用方案'
      });
    },
    'demo_graceful_degradation'
  );
}

// 未知错误场景
async function handleUnknownErrorScenario() {
  // 抛出一个未分类的错误
  throw new Error('这是一个未知类型的错误');
}

// POST方法演示错误恢复
export const POST = withErrorHandling(async (request: NextRequest) => {
  const body = await request.json();
  const { operation, shouldFail } = body;

  if (!operation) {
    throw createValidationError('缺少operation参数');
  }

  if (shouldFail) {
    // 根据操作类型抛出不同的错误
    switch (operation) {
      case 'database':
        throw new Error('数据库连接失败');
      case 'blockchain':
        throw new Error('区块链网络错误');
      case 'validation':
        throw createValidationError('数据验证失败');
      default:
        throw new Error('操作失败');
    }
  }

  // 记录业务指标
  monitoringService.recordBusinessMetric('demo_operations', 1, {
    operation,
    status: 'success'
  });

  return createSuccessResponse({
    message: `✅ ${operation} 操作成功`,
    operation,
    timestamp: new Date().toISOString()
  });
});
