'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, Button, Icon, Loading } from '@/components/ui';
import {
  FeatureIcons,
  ActionIcons,
  NavigationIcons,
  FinanceIcons,
  MediaIcons
} from '@/config/icons';
import Header from '@/components/layout/Header';
import TableOfContents from '@/components/whitepaper/TableOfContents';
import WhitepaperSection from '@/components/whitepaper/WhitepaperSection';
import ReadingProgress from '@/components/whitepaper/ReadingProgress';
import { parseMarkdownContent } from '@/utils/markdownParser';
import { WhitepaperSection as SectionType, WhitepaperTOC, LANGUAGE_CONFIG } from '@/types/whitepaper';
import { cn } from '@/lib/utils';

interface Language {
  code: 'zh' | 'en';
  name: string;
  flag: string;
}

const WhitepaperPage = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [content, setContent] = useState<string>('');
  const [selectedLanguage, setSelectedLanguage] = useState<'zh' | 'en'>('zh');
  const [mounted, setMounted] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [parsedContent, setParsedContent] = useState<{
    sections: SectionType[];
    toc: WhitepaperTOC[];
    metadata: { totalSections: number; estimatedReadTime: number };
  } | null>(null);
  const [activeSection, setActiveSection] = useState<string>('');

  const languages: Language[] = [
    { code: 'zh', name: '中文', flag: '🇨🇳' },
    { code: 'en', name: 'English', flag: '🇺🇸' }
  ];

  const t = LANGUAGE_CONFIG[selectedLanguage];

  // 加载白皮书内容
  const loadWhitepaper = async (lang: 'zh' | 'en') => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/whitepaper?lang=${lang}`);

      if (!response.ok) {
        throw new Error(`Failed to load whitepaper: ${response.status}`);
      }

      const text = await response.text();
      setContent(text);

      // 解析内容
      const parsed = parseMarkdownContent(text);
      setParsedContent(parsed);

      // 设置第一个章节为活跃章节
      if (parsed.sections.length > 0) {
        setActiveSection(parsed.sections[0].id);
      }
    } catch (err) {
      console.error('Error loading whitepaper:', err);
      setError(err instanceof Error ? err.message : 'Failed to load whitepaper');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    setMounted(true);
    loadWhitepaper(selectedLanguage);
  }, [selectedLanguage]);

  // 处理内部链接点击事件
  useEffect(() => {
    const handleInternalLinkClick = (event: Event) => {
      const target = event.target as HTMLElement;
      if (target.classList.contains('internal-link')) {
        event.preventDefault();
        const anchor = target.getAttribute('data-anchor');
        if (anchor && parsedContent) {
          // 查找对应的章节 - 使用更精确的匹配策略
          const section = parsedContent.sections.find(s => {
            // 提取锚点中的章节编号和关键词
            const anchorMatch = anchor.match(/^(\d+)-(.+)$/);
            if (anchorMatch) {
              const [, sectionNum, keywords] = anchorMatch;

              // 检查章节编号是否匹配
              const titleMatch = s.title.match(/^(\d+)\./);
              if (titleMatch && titleMatch[1] === sectionNum) {
                return true;
              }
            }

            // 备用匹配：标准化文本匹配
            const normalizeText = (text: string) => {
              return text.toLowerCase()
                .replace(/[^\w\u4e00-\u9fff]/g, '') // 只保留字母、数字和中文
                .replace(/\s+/g, '');
            };

            const normalizedTitle = normalizeText(s.title);
            const normalizedAnchor = normalizeText(anchor);

            // 检查是否包含关键词
            return normalizedTitle.includes(normalizedAnchor) ||
                   normalizedAnchor.includes(normalizedTitle);
          });

          if (section) {
            setActiveSection(section.id);
            // 滚动到对应章节
            const element = document.getElementById(`section-${section.id}`);
            if (element) {
              const headerOffset = 100;
              const elementPosition = element.getBoundingClientRect().top;
              const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

              window.scrollTo({
                top: offsetPosition,
                behavior: 'smooth'
              });
            }
          }
        }
      }
    };

    // 添加事件委托
    document.addEventListener('click', handleInternalLinkClick);

    return () => {
      document.removeEventListener('click', handleInternalLinkClick);
    };
  }, [parsedContent]);

  // 语言切换处理
  const handleLanguageChange = (lang: 'zh' | 'en') => {
    setSelectedLanguage(lang);
  };

  // 章节点击处理
  const handleSectionClick = (sectionId: string) => {
    setActiveSection(sectionId);
  };



  // 章节可见性处理
  const handleSectionVisible = (sectionId: string) => {
    setActiveSection(sectionId);
  };

  // 下载白皮书
  const handleDownload = () => {
    const link = document.createElement('a');
    link.href = `/api/whitepaper?lang=${selectedLanguage}`;
    link.download = `HAOX_WHITEPAPER_V2${selectedLanguage === 'en' ? '_EN' : ''}.md`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // 返回顶部
  const handleBackToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  if (!mounted) {
    return null;
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-system-background via-system-gray-6 to-system-background">
        <Header />
        <div className="flex items-center justify-center min-h-[60vh]">
          <Loading text={selectedLanguage === 'zh' ? '加载白皮书中...' : 'Loading whitepaper...'} />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-system-background via-system-gray-6 to-system-background">
        <Header />
        <div className="flex items-center justify-center min-h-[60vh]">
          <Card className="p-8 text-center">
            <Icon icon={ActionIcons.alert} size="3xl" color="error" className="mx-auto mb-4" />
            <h3 className="text-title-3 font-sf-pro font-semibold text-label mb-2">
              {selectedLanguage === 'zh' ? '加载失败' : 'Loading Failed'}
            </h3>
            <p className="text-body text-secondary-label mb-4">{error}</p>
            <Button onClick={() => loadWhitepaper(selectedLanguage)}>
              {selectedLanguage === 'zh' ? '重试' : 'Retry'}
            </Button>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-system-background via-system-gray-6 to-system-background">
      <Header />

      {/* 背景装饰 */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-system-blue/5 rounded-full blur-3xl" />
        <div className="absolute bottom-0 right-1/4 w-80 h-80 bg-system-purple/5 rounded-full blur-3xl" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-system-green/3 rounded-full blur-3xl" />
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面标题和语言切换 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <div className="inline-flex items-center space-x-2 bg-system-blue/10 backdrop-blur-sm rounded-full px-4 py-2 mb-6">
            <Icon icon={MediaIcons.document} size="sm" className="text-system-blue" />
            <span className="text-sm font-sf-pro font-medium text-system-blue">
              {t.executiveSummary}
            </span>
          </div>

          <h1 className="text-5xl font-bold text-label mb-4 bg-gradient-to-r from-system-blue to-system-purple bg-clip-text text-transparent">
            {selectedLanguage === 'zh' ? 'HAOX 白皮书' : 'HAOX Whitepaper'}
          </h1>
          <p className="text-xl text-secondary-label max-w-4xl mx-auto mb-8 leading-relaxed">
            {selectedLanguage === 'zh'
              ? '连接下一个十亿用户，让Web3像呼吸一样自然'
              : 'Connecting the Next Billion Users, Making Web3 as Natural as Breathing'
            }
          </p>

          {/* 元数据信息 */}
          {parsedContent && (
            <div className="flex items-center justify-center space-x-6 text-sm text-secondary-label mb-8">
              <div className="flex items-center space-x-1">
                <Icon icon={NavigationIcons.list} size="xs" />
                <span>{parsedContent.metadata.totalSections} {t.section}</span>
              </div>
              <div className="flex items-center space-x-1">
                <Icon icon={ActionIcons.clock} size="xs" />
                <span>{parsedContent.metadata.estimatedReadTime} {t.minutes} {t.readingTime}</span>
              </div>
              <div className="flex items-center space-x-1">
                <Icon icon={FeatureIcons.security} size="xs" />
                <span>V2.0</span>
              </div>
            </div>
          )}

          {/* 语言切换和操作按钮 */}
          <div className="flex items-center justify-center space-x-4">
            {/* 语言切换 */}
            <div className="flex bg-system-background/80 backdrop-blur-sm rounded-xl p-1 border border-system-gray-4/30 shadow-apple">
              {languages.map((lang) => (
                <motion.button
                  key={lang.code}
                  onClick={() => handleLanguageChange(lang.code)}
                  className={cn(
                    'flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-sf-pro font-medium transition-all duration-200',
                    selectedLanguage === lang.code
                      ? 'bg-system-blue text-white shadow-apple'
                      : 'text-secondary-label hover:text-label hover:bg-system-gray-5'
                  )}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <span>{lang.flag}</span>
                  <span>{lang.name}</span>
                </motion.button>
              ))}
            </div>

            {/* 下载按钮 */}
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                variant="outline"
                size="md"
                onClick={handleDownload}
                className="bg-system-background/80 backdrop-blur-sm border-system-gray-4/30 shadow-apple"
              >
                <Icon icon={ActionIcons.download} size="sm" className="mr-2" />
                {selectedLanguage === 'zh' ? '下载白皮书' : 'Download'}
              </Button>
            </motion.div>
          </div>
        </motion.div>

        {/* 主要内容区域 */}
        {parsedContent && (
          <div className="grid lg:grid-cols-4 gap-8">
            {/* 目录导航 */}
            <div className="lg:col-span-1" data-toc>
              <TableOfContents
                toc={parsedContent.toc}
                activeSection={activeSection}
                onSectionClick={handleSectionClick}
                language={selectedLanguage}
                isSticky={true}
                showProgress={true}
              />
            </div>

            {/* 白皮书内容 */}
            <div className="lg:col-span-3">
              <AnimatePresence mode="wait">
                <motion.div
                  key={selectedLanguage}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.3 }}
                  className="space-y-8"
                >
                  {parsedContent.sections.map((section, index) => (
                    <WhitepaperSection
                      key={`${selectedLanguage}-${section.id}`}
                      section={section}
                      index={index}
                      isActive={activeSection === section.id}
                      language={selectedLanguage}
                      onSectionVisible={handleSectionVisible}
                    />
                  ))}
                </motion.div>
              </AnimatePresence>

              {/* 底部操作区域 */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.6, delay: 0.6 }}
                className="mt-16 text-center"
              >
                <Card className="bg-gradient-to-r from-system-blue/10 to-system-purple/10 backdrop-blur-sm border-system-gray-4/30">
                  <div className="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0">
                    <div className="flex items-center space-x-3">
                      <Icon icon={FeatureIcons.security} size="md" className="text-system-green" />
                      <div className="text-left">
                        <h4 className="text-body font-sf-pro font-semibold text-label">
                          {selectedLanguage === 'zh' ? '内容保证' : 'Content Guarantee'}
                        </h4>
                        <p className="text-sm text-secondary-label">
                          {selectedLanguage === 'zh'
                            ? '本白皮书内容经过严格审核，确保信息准确性'
                            : 'This whitepaper has been thoroughly reviewed to ensure accuracy'
                          }
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-3">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleDownload}
                        className="bg-system-background/50"
                      >
                        <Icon icon={ActionIcons.download} size="sm" className="mr-2" />
                        {selectedLanguage === 'zh' ? '下载PDF' : 'Download PDF'}
                      </Button>

                      <Button
                        variant="primary"
                        size="sm"
                        onClick={handleBackToTop}
                      >
                        <Icon icon={ActionIcons.arrowUp} size="sm" className="mr-2" />
                        {t.backToTop}
                      </Button>
                    </div>
                  </div>
                </Card>
              </motion.div>
            </div>
          </div>
        )}

        {/* 无内容状态 */}
        {!parsedContent && !isLoading && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center py-16"
          >
            <Card className="max-w-md mx-auto p-8">
              <Icon icon={MediaIcons.document} size="3xl" color="muted" className="mx-auto mb-4" />
              <h3 className="text-title-3 font-sf-pro font-semibold text-label mb-2">
                {selectedLanguage === 'zh' ? '暂无内容' : 'No Content Available'}
              </h3>
              <p className="text-body text-secondary-label mb-4">
                {selectedLanguage === 'zh'
                  ? '白皮书内容正在加载中，请稍后重试'
                  : 'Whitepaper content is loading, please try again later'
                }
              </p>
              <Button onClick={() => loadWhitepaper(selectedLanguage)}>
                {selectedLanguage === 'zh' ? '重新加载' : 'Reload'}
              </Button>
            </Card>
          </motion.div>
        )}
      </div>

      {/* 阅读进度指示器 */}
      {parsedContent && (
        <ReadingProgress
          language={selectedLanguage}
          estimatedReadTime={parsedContent.metadata.estimatedReadTime}
          currentSection={parsedContent.sections.find(s => s.id === activeSection)?.title}
          totalSections={parsedContent.metadata.totalSections}
          onBackToTop={handleBackToTop}
        />
      )}
    </div>
  );
};

export default WhitepaperPage;
