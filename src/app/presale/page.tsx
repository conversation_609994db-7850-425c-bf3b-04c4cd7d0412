'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button, Card, Icon } from '@/components/ui';
import {
  FinanceIcons,
  UserIcons,
  FeatureIcons,
  RewardIcons,
  ActionIcons
} from '@/config/icons';
import Header from '@/components/layout/Header';
import { formatNumber, cn } from '@/lib/utils';
import { useWallet } from '@/hooks/useWallet';
import { useWalletAuth } from '@/hooks/useWalletAuth';
import { useHAOXContract } from '@/hooks/useHAOXContract';

export default function PresalePage() {
  const { isConnected, connect } = useWallet();
  const { user, isAuthenticated } = useWalletAuth();
  const {
    tokenInfo,
    presaleInfo,
    userPresaleInfo,
    priceInfo,
    isLoading,
    error,
    buyTokens,
    calculateTokenAmount,
    formatPrice,
    getPresaleProgress,
    isContractsAvailable
  } = useHAOXContract();

  const [investmentAmount, setInvestmentAmount] = useState('');
  const [calculatedTokens, setCalculatedTokens] = useState('0');
  const [isCalculating, setIsCalculating] = useState(false);
  const [isPurchasing, setIsPurchasing] = useState(false);

  // Calculate tokens when investment amount changes
  useEffect(() => {
    const calculateTokens = async () => {
      if (!investmentAmount || parseFloat(investmentAmount) <= 0) {
        setCalculatedTokens('0');
        return;
      }

      setIsCalculating(true);
      try {
        const tokens = await calculateTokenAmount(investmentAmount);
        setCalculatedTokens(tokens);
      } catch (error) {
        console.error('Error calculating tokens:', error);
        setCalculatedTokens('0');
      } finally {
        setIsCalculating(false);
      }
    };

    const timeoutId = setTimeout(calculateTokens, 300); // Debounce
    return () => clearTimeout(timeoutId);
  }, [investmentAmount, calculateTokenAmount]);

  // Handle token purchase
  const handlePurchase = async () => {
    if (!investmentAmount || parseFloat(investmentAmount) <= 0) {
      return;
    }

    setIsPurchasing(true);
    try {
      const success = await buyTokens(investmentAmount);
      if (success) {
        setInvestmentAmount('');
        setCalculatedTokens('0');
      }
    } catch (error) {
      console.error('Purchase failed:', error);
    } finally {
      setIsPurchasing(false);
    }
  };

  // Format time remaining
  const formatTimeRemaining = (endTime: number) => {
    if (!endTime) return 'Not started';
    
    const now = Math.floor(Date.now() / 1000);
    const remaining = endTime - now;
    
    if (remaining <= 0) return 'Ended';
    
    const days = Math.floor(remaining / (24 * 60 * 60));
    const hours = Math.floor((remaining % (24 * 60 * 60)) / (60 * 60));
    const minutes = Math.floor((remaining % (60 * 60)) / 60);
    
    if (days > 0) return `${days}d ${hours}h ${minutes}m`;
    if (hours > 0) return `${hours}h ${minutes}m`;
    return `${minutes}m`;
  };

  const presaleProgress = getPresaleProgress();

  return (
    <div className="min-h-screen bg-system-background">
      <Header />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h1 className="text-title-1 font-sf-pro font-bold text-label mb-4">
              HAOX Token Presale
            </h1>
            <p className="text-title-3 text-secondary-label mb-6">
              Join the SocioMint ecosystem with exclusive presale pricing
            </p>
            
            {/* Presale Status */}
            <div className="flex items-center justify-center space-x-6 mb-8">
              <div className="flex items-center space-x-2">
                <div className={cn(
                  "w-3 h-3 rounded-full",
                  presaleInfo?.active ? "bg-system-green animate-pulse" : "bg-system-red"
                )} />
                <span className="text-body font-sf-pro font-medium text-label">
                  {presaleInfo?.active ? 'Live Now' : 'Not Active'}
                </span>
              </div>
              
              {presaleInfo?.endTime && (
                <div className="flex items-center space-x-2">
                  <Icon icon={FeatureIcons.clock} size="sm" color="warning" />
                  <span className="text-body font-sf-pro font-medium text-label">
                    {formatTimeRemaining(presaleInfo.endTime)}
                  </span>
                </div>
              )}
            </div>
          </motion.div>
        </div>

        {!isConnected ? (
          <Card>
            <div className="text-center py-12">
              <Icon icon={FinanceIcons.wallet} size="3xl" color="primary" className="mx-auto mb-4" />
              <h3 className="text-title-3 font-sf-pro font-semibold text-label mb-2">
                Connect Your Wallet
              </h3>
              <p className="text-body text-secondary-label mb-6">
                Connect your wallet to participate in the HAOX token presale
              </p>
              <Button onClick={connect} className="bg-system-blue">
                Connect Wallet
              </Button>
            </div>
          </Card>
        ) : !isContractsAvailable ? (
          <Card>
            <div className="text-center py-12">
              <Icon icon={ActionIcons.alert} size="3xl" color="warning" className="mx-auto mb-4" />
              <h3 className="text-title-3 font-sf-pro font-semibold text-label mb-2">
                Contracts Not Available
              </h3>
              <p className="text-body text-secondary-label">
                Smart contracts are not deployed or configured properly
              </p>
            </div>
          </Card>
        ) : (
          <div className="grid lg:grid-cols-3 gap-8">
            {/* Main Presale Panel */}
            <div className="lg:col-span-2 space-y-6">
              {/* Presale Progress */}
              <Card title="Presale Progress">
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-body text-secondary-label">Progress</span>
                    <span className="text-body font-sf-pro font-semibold text-label">
                      {presaleProgress.percentage.toFixed(1)}%
                    </span>
                  </div>
                  
                  <div className="w-full bg-system-gray-5 rounded-full h-3">
                    <div 
                      className="bg-gradient-to-r from-system-blue to-system-green h-3 rounded-full transition-all duration-500"
                      style={{ width: `${presaleProgress.percentage}%` }}
                    />
                  </div>
                  
                  <div className="flex justify-between text-caption-1 text-secondary-label">
                    <span>{presaleProgress.raised} BNB raised</span>
                    <span>{presaleProgress.target} BNB target</span>
                  </div>
                </div>
              </Card>

              {/* Current Stage Info */}
              {presaleInfo && (
                <Card title="Current Stage">
                  <div className="grid md:grid-cols-3 gap-4">
                    <div className="text-center">
                      <p className="text-title-2 font-sf-pro font-bold text-system-blue">
                        {presaleInfo.currentStage + 1}
                      </p>
                      <p className="text-caption-1 text-secondary-label">Stage</p>
                    </div>
                    
                    <div className="text-center">
                      <p className="text-title-2 font-sf-pro font-bold text-system-green">
                        {formatNumber(parseInt(presaleInfo.currentRate))}
                      </p>
                      <p className="text-caption-1 text-secondary-label">HAOX per BNB</p>
                    </div>
                    
                    <div className="text-center">
                      <p className="text-title-2 font-sf-pro font-bold text-system-orange">
                        {formatNumber(parseFloat(presaleInfo.stageTokensRemaining))}
                      </p>
                      <p className="text-caption-1 text-secondary-label">Tokens Left</p>
                    </div>
                  </div>
                </Card>
              )}

              {/* Purchase Panel */}
              <Card title="Purchase HAOX Tokens">
                {userPresaleInfo?.isWhitelisted ? (
                  <div className="space-y-6">
                    {/* Investment Amount Input */}
                    <div>
                      <label className="block text-body font-sf-pro font-medium text-label mb-2">
                        Investment Amount (BNB)
                      </label>
                      <div className="relative">
                        <input
                          type="number"
                          value={investmentAmount}
                          onChange={(e) => setInvestmentAmount(e.target.value)}
                          placeholder="0.1"
                          min="0.1"
                          max="20"
                          step="0.1"
                          className="w-full px-4 py-3 bg-system-gray-6 border border-system-gray-4 rounded-lg focus:outline-none focus:ring-2 focus:ring-system-blue focus:border-transparent text-label placeholder-secondary-label"
                        />
                        <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                          <span className="text-body font-sf-pro font-medium text-secondary-label">BNB</span>
                        </div>
                      </div>
                      <div className="flex justify-between mt-2 text-caption-1 text-secondary-label">
                        <span>Min: 0.1 BNB</span>
                        <span>Max: 20 BNB</span>
                      </div>
                    </div>

                    {/* Token Calculation */}
                    <div className="bg-system-gray-6 rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <span className="text-body text-secondary-label">You will receive:</span>
                        <div className="flex items-center space-x-2">
                          {isCalculating ? (
                            <div className="animate-spin w-4 h-4 border-2 border-system-blue border-t-transparent rounded-full" />
                          ) : (
                            <Icon icon={FeatureIcons.calculator} size="sm" color="primary" />
                          )}
                          <span className="text-headline font-sf-pro font-bold text-system-green">
                            {formatNumber(parseFloat(calculatedTokens))} HAOX
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* User Investment Info */}
                    <div className="grid md:grid-cols-2 gap-4 p-4 bg-system-gray-6 rounded-lg">
                      <div>
                        <p className="text-caption-1 text-secondary-label">Your Investment</p>
                        <p className="text-body font-sf-pro font-semibold text-label">
                          {parseFloat(userPresaleInfo.bnbInvested).toFixed(2)} BNB
                        </p>
                      </div>
                      <div>
                        <p className="text-caption-1 text-secondary-label">Remaining Limit</p>
                        <p className="text-body font-sf-pro font-semibold text-label">
                          {parseFloat(userPresaleInfo.remainingInvestment).toFixed(2)} BNB
                        </p>
                      </div>
                    </div>

                    {/* Purchase Button */}
                    <Button
                      onClick={handlePurchase}
                      disabled={
                        !investmentAmount || 
                        parseFloat(investmentAmount) <= 0 || 
                        parseFloat(investmentAmount) < 0.1 ||
                        parseFloat(investmentAmount) > parseFloat(userPresaleInfo.remainingInvestment) ||
                        isPurchasing ||
                        !presaleInfo?.active
                      }
                      className="w-full bg-system-green"
                    >
                      {isPurchasing ? (
                        <div className="flex items-center space-x-2">
                          <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full" />
                          <span>Processing...</span>
                        </div>
                      ) : (
                        'Purchase HAOX Tokens'
                      )}
                    </Button>

                    {error && (
                      <div className="p-4 bg-system-red/10 border border-system-red/20 rounded-lg">
                        <div className="flex items-center space-x-2">
                          <Icon icon={ActionIcons.alert} size="sm" color="error" />
                          <span className="text-body text-system-red">{error}</span>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Icon icon={UserIcons.shield} size="3xl" color="warning" className="mx-auto mb-4" />
                    <h3 className="text-title-3 font-sf-pro font-semibold text-label mb-2">
                      Whitelist Required
                    </h3>
                    <p className="text-body text-secondary-label mb-4">
                      You need to be whitelisted to participate in the presale. 
                      Complete Telegram verification to get whitelisted.
                    </p>
                    <Button variant="outline">
                      Complete Verification
                    </Button>
                  </div>
                )}
              </Card>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Token Info */}
              <Card title="Token Information">
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-body text-secondary-label">Symbol</span>
                    <span className="text-body font-sf-pro font-semibold text-label">HAOX</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-body text-secondary-label">Network</span>
                    <span className="text-body font-sf-pro font-semibold text-label">BSC</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-body text-secondary-label">Total Supply</span>
                    <span className="text-body font-sf-pro font-semibold text-label">5B HAOX</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-body text-secondary-label">Presale Supply</span>
                    <span className="text-body font-sf-pro font-semibold text-label">180M HAOX</span>
                  </div>
                </div>
              </Card>

              {/* Current Price */}
              {priceInfo && (
                <Card title="Current Price">
                  <div className="text-center">
                    <p className="text-title-2 font-sf-pro font-bold text-system-green mb-2">
                      ${formatPrice(priceInfo.price)}
                    </p>
                    <p className="text-caption-1 text-secondary-label">
                      Last updated: {new Date(priceInfo.timestamp * 1000).toLocaleTimeString()}
                    </p>
                  </div>
                </Card>
              )}

              {/* Your Balance */}
              {tokenInfo && (
                <Card title="Your HAOX Balance">
                  <div className="text-center">
                    <p className="text-title-2 font-sf-pro font-bold text-system-blue mb-2">
                      {formatNumber(parseFloat(tokenInfo.balance))}
                    </p>
                    <p className="text-caption-1 text-secondary-label">HAOX Tokens</p>
                  </div>
                </Card>
              )}

              {/* Features */}
              <Card title="Why HAOX?">
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <Icon icon={FinanceIcons.trendUp} size="md" color="success" />
                    <span className="text-body text-label">Dynamic pricing model</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Icon icon={UserIcons.users} size="md" color="primary" />
                    <span className="text-body text-label">Community governance</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Icon icon={RewardIcons.award} size="md" color="warning" />
                    <span className="text-body text-label">Invitation rewards</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Icon icon={RewardIcons.zap} size="md" color="secondary" />
                    <span className="text-body text-label">Task marketplace</span>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
