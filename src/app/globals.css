@tailwind base;
@tailwind components;
@tailwind utilities;

/* 性能优化样式 */
@import '../styles/performance.css';

/* 移动端优化 */
@media (max-width: 768px) {
  /* 确保移动端导航不会覆盖内容 */
  body {
    padding-top: env(safe-area-inset-top);
  }

  /* 防止水平滚动 */
  html, body {
    overflow-x: hidden;
  }

  /* 优化触摸体验 */
  * {
    -webkit-tap-highlight-color: transparent;
  }
}

:root {
  /* Apple Design System Colors */
  --system-blue: #007AFF;
  --system-green: #34C759;
  --system-red: #FF3B30;
  --system-orange: #FF9500;
  --system-yellow: #FFCC00;
  --system-pink: #FF2D92;
  --system-purple: #AF52DE;
  --system-indigo: #5856D6;
  --system-teal: #5AC8FA;
  --system-gray: #8E8E93;

  /* Semantic Colors */
  --label: #000000;
  --secondary-label: #3C3C43;
  --tertiary-label: #3C3C43;
  --quaternary-label: #3C3C43;
  --system-background: #FFFFFF;
  --secondary-system-background: #F2F2F7;
  --tertiary-system-background: #FFFFFF;

  /* App specific */
  --background: var(--system-background);
  --foreground: var(--label);
}

@media (prefers-color-scheme: dark) {
  :root {
    --label: #FFFFFF;
    --secondary-label: #EBEBF5;
    --tertiary-label: #EBEBF5;
    --quaternary-label: #EBEBF5;
    --system-background: #000000;
    --secondary-system-background: #1C1C1E;
    --tertiary-system-background: #2C2C2E;

    --background: var(--system-background);
    --foreground: var(--label);
  }
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', system-ui, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  color: var(--foreground);
  background: var(--background);
  line-height: 1.6;
}

/* Apple-style focus states */
button:focus-visible,
input:focus-visible,
textarea:focus-visible,
select:focus-visible {
  outline: 2px solid var(--system-blue);
  outline-offset: 2px;
}

/* Smooth transitions */
* {
  transition: color 0.15s ease, background-color 0.15s ease, border-color 0.15s ease;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--system-gray);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--secondary-label);
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  /* Apple-style glass effect */
  .glass {
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.8);
  }

  .glass-dark {
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    background: rgba(0, 0, 0, 0.8);
  }

  /* Touch-friendly tap targets */
  .tap-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Apple-style button press effect */
  .press-effect {
    transform: scale(1);
    transition: transform 0.1s ease;
  }

  .press-effect:active {
    transform: scale(0.96);
  }
}
