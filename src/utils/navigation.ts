/**
 * 导航工具函数 - 解决水合错误导致的路由问题
 */

/**
 * 强制客户端跳转，避免 Next.js 路由问题
 */
export const forceNavigate = (url: string, delay: number = 100) => {
  if (typeof window === 'undefined') {
    return;
  }

  console.log(`🔄 强制跳转到: ${url}`);

  // 使用延迟跳转确保状态更新完成
  setTimeout(() => {
    try {
      // 首先尝试使用 window.location.href
      window.location.href = url;
    } catch (error) {
      console.warn('window.location.href 失败，尝试 replace:', error);
      try {
        // 如果失败，尝试使用 replace
        window.location.replace(url);
      } catch (replaceError) {
        console.error('所有跳转方法都失败，强制刷新页面:', replaceError);
        // 最后的备选方案：刷新到目标页面
        window.location.assign(url);
      }
    }
  }, delay);
};

/**
 * 安全的路由跳转，带重试机制
 */
export const safeNavigate = (url: string, maxRetries: number = 3) => {
  if (typeof window === 'undefined') {
    return;
  }
  
  let retries = 0;
  
  const attemptNavigation = () => {
    try {
      window.location.href = url;
    } catch (error) {
      console.error('导航失败:', error);
      retries++;
      
      if (retries < maxRetries) {
        console.log(`🔄 重试导航 (${retries}/${maxRetries})`);
        setTimeout(attemptNavigation, 500 * retries);
      } else {
        console.error('❌ 导航失败，已达到最大重试次数');
        // 最后尝试刷新页面
        window.location.reload();
      }
    }
  };
  
  attemptNavigation();
};

/**
 * 清除认证状态并跳转
 */
export const clearAuthAndNavigate = (url: string = '/') => {
  if (typeof window === 'undefined') {
    return;
  }
  
  // 清除所有可能的认证数据
  localStorage.removeItem('telegram-user');
  sessionStorage.clear();
  
  // 强制跳转
  forceNavigate(url);
};
