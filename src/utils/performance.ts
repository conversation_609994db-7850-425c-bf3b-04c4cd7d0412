// 性能监控和优化工具

// 性能指标接口
interface PerformanceMetrics {
  // 首次内容绘制
  fcp?: number;
  // 最大内容绘制
  lcp?: number;
  // 首次输入延迟
  fid?: number;
  // 累积布局偏移
  cls?: number;
  // 首次字节时间
  ttfb?: number;
}

// 性能监控类
class PerformanceMonitor {
  private metrics: PerformanceMetrics = {};
  private observers: PerformanceObserver[] = [];

  constructor() {
    this.initializeObservers();
  }

  private initializeObservers() {
    // 监控 LCP (Largest Contentful Paint)
    if ('PerformanceObserver' in window) {
      try {
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1] as any;
          this.metrics.lcp = lastEntry.startTime;
        });
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
        this.observers.push(lcpObserver);
      } catch (e) {
        console.warn('LCP observer not supported');
      }

      // 监控 FID (First Input Delay)
      try {
        const fidObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry: any) => {
            this.metrics.fid = entry.processingStart - entry.startTime;
          });
        });
        fidObserver.observe({ entryTypes: ['first-input'] });
        this.observers.push(fidObserver);
      } catch (e) {
        console.warn('FID observer not supported');
      }

      // 监控 CLS (Cumulative Layout Shift)
      try {
        let clsValue = 0;
        const clsObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry: any) => {
            if (!entry.hadRecentInput) {
              clsValue += entry.value;
              this.metrics.cls = clsValue;
            }
          });
        });
        clsObserver.observe({ entryTypes: ['layout-shift'] });
        this.observers.push(clsObserver);
      } catch (e) {
        console.warn('CLS observer not supported');
      }
    }

    // 监控 FCP (First Contentful Paint)
    if ('performance' in window && 'getEntriesByType' in performance) {
      const paintEntries = performance.getEntriesByType('paint');
      const fcpEntry = paintEntries.find(entry => entry.name === 'first-contentful-paint');
      if (fcpEntry) {
        this.metrics.fcp = fcpEntry.startTime;
      }
    }

    // 监控 TTFB (Time to First Byte)
    if ('performance' in window && 'timing' in performance) {
      const timing = performance.timing as any;
      this.metrics.ttfb = timing.responseStart - timing.navigationStart;
    }
  }

  getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  // 记录自定义性能指标
  mark(name: string) {
    if ('performance' in window && 'mark' in performance) {
      performance.mark(name);
    }
  }

  // 测量两个标记之间的时间
  measure(name: string, startMark: string, endMark?: string) {
    if ('performance' in window && 'measure' in performance) {
      try {
        performance.measure(name, startMark, endMark);
        const measures = performance.getEntriesByName(name, 'measure');
        return measures[measures.length - 1]?.duration || 0;
      } catch (e) {
        console.warn('Performance measure failed:', e);
        return 0;
      }
    }
    return 0;
  }

  // 清理观察器
  disconnect() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
  }
}

// 全局性能监控实例
export const performanceMonitor = new PerformanceMonitor();

// 组件渲染性能监控 Hook
export function useRenderPerformance(componentName: string) {
  const startTime = performance.now();

  return {
    markRenderStart: () => {
      performanceMonitor.mark(`${componentName}-render-start`);
    },
    markRenderEnd: () => {
      performanceMonitor.mark(`${componentName}-render-end`);
      const duration = performanceMonitor.measure(
        `${componentName}-render`,
        `${componentName}-render-start`,
        `${componentName}-render-end`
      );
      
      if (duration > 16) { // 超过一帧的时间
        console.warn(`${componentName} render took ${duration.toFixed(2)}ms`);
      }
      
      return duration;
    },
    getComponentRenderTime: () => performance.now() - startTime
  };
}

// 内存使用监控
export function getMemoryUsage() {
  if ('memory' in performance) {
    const memory = (performance as any).memory;
    return {
      used: Math.round(memory.usedJSHeapSize / 1048576), // MB
      total: Math.round(memory.totalJSHeapSize / 1048576), // MB
      limit: Math.round(memory.jsHeapSizeLimit / 1048576) // MB
    };
  }
  return null;
}

// 网络状态监控
export function getNetworkInfo() {
  if ('connection' in navigator) {
    const connection = (navigator as any).connection;
    return {
      effectiveType: connection.effectiveType,
      downlink: connection.downlink,
      rtt: connection.rtt,
      saveData: connection.saveData
    };
  }
  return null;
}

// 资源加载性能分析
export function analyzeResourcePerformance() {
  if (!('performance' in window)) return [];

  const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
  
  return resources.map(resource => ({
    name: resource.name,
    duration: resource.duration,
    size: resource.transferSize || 0,
    type: getResourceType(resource.name),
    cached: resource.transferSize === 0 && resource.decodedBodySize > 0
  })).sort((a, b) => b.duration - a.duration);
}

function getResourceType(url: string): string {
  if (url.includes('.js')) return 'script';
  if (url.includes('.css')) return 'stylesheet';
  if (url.includes('.png') || url.includes('.jpg') || url.includes('.jpeg') || url.includes('.webp')) return 'image';
  if (url.includes('.woff') || url.includes('.woff2') || url.includes('.ttf')) return 'font';
  return 'other';
}

// 防抖函数
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate?: boolean
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null;
  
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null;
      if (!immediate) func(...args);
    };
    
    const callNow = immediate && !timeout;
    
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    
    if (callNow) func(...args);
  };
}

// 节流函数
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

// 图片懒加载优化
export function optimizeImageLoading(img: HTMLImageElement, options: {
  quality?: number;
  format?: 'webp' | 'avif' | 'auto';
  sizes?: string;
} = {}) {
  const { quality = 80, format = 'auto', sizes } = options;
  
  // 检查浏览器支持
  const supportsWebP = () => {
    const canvas = document.createElement('canvas');
    return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
  };
  
  const supportsAvif = () => {
    const canvas = document.createElement('canvas');
    return canvas.toDataURL('image/avif').indexOf('data:image/avif') === 0;
  };
  
  // 根据浏览器支持选择最佳格式
  let optimalFormat = format;
  if (format === 'auto') {
    if (supportsAvif()) {
      optimalFormat = 'avif';
    } else if (supportsWebP()) {
      optimalFormat = 'webp';
    }
  }
  
  // 设置响应式尺寸
  if (sizes) {
    img.sizes = sizes;
  }
  
  // 添加加载优化属性
  img.loading = 'lazy';
  img.decoding = 'async';
  
  return {
    format: optimalFormat,
    quality
  };
}
