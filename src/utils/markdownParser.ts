import { WhitepaperSection, WhitepaperTOC, SECTION_TYPES, WHITEPAPER_STRUCTURE } from '@/types/whitepaper';

export interface ParsedContent {
  sections: WhitepaperSection[];
  toc: WhitepaperTOC[];
  metadata: {
    totalSections: number;
    estimatedReadTime: number;
  };
}

// 生成锚点ID
export const generateAnchor = (text: string): string => {
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // 移除特殊字符
    .replace(/\s+/g, '-') // 空格替换为连字符
    .replace(/^-+|-+$/g, ''); // 移除首尾连字符
};

// 提取章节编号
export const extractSectionNumber = (title: string): string | null => {
  const match = title.match(/^(\d+\.?\d*\.?)\s+/);
  return match ? match[1] : null;
};

// 确定章节类型
export const determineSectionType = (title: string, sectionNumber: string | null): keyof typeof SECTION_TYPES => {
  if (!sectionNumber) return 'default';
  
  const mainNumber = sectionNumber.split('.')[0];
  const structureKey = mainNumber === '0' ? '0' : mainNumber;
  
  if (title.includes('附录') || title.includes('Appendix')) return 'appendix';
  
  return WHITEPAPER_STRUCTURE[structureKey]?.type || 'default';
};

// 解析Markdown内容
export const parseMarkdownContent = (markdown: string): ParsedContent => {
  const lines = markdown.split('\n');
  const sections: WhitepaperSection[] = [];
  const toc: WhitepaperTOC[] = [];
  
  let currentSection: WhitepaperSection | null = null;
  let currentContent: string[] = [];
  let sectionCounter = 0;
  let totalEstimatedTime = 0;

  const processCurrentSection = () => {
    if (currentSection && currentContent.length > 0) {
      currentSection.content = currentContent.join('\n').trim();
      sections.push(currentSection);
      
      // 添加到TOC
      const tocItem: WhitepaperTOC = {
        id: currentSection.id,
        title: currentSection.title,
        level: currentSection.level,
        anchor: `section-${currentSection.id}`, // 与WhitepaperSection组件中的ID保持一致
        type: currentSection.type,
        icon: SECTION_TYPES[currentSection.type || 'default'].icon
      };
      
      if (currentSection.level === 1) {
        toc.push(tocItem);
      } else {
        // 找到父级TOC项并添加为子项
        const parentToc = findParentTOC(toc, currentSection.level);
        if (parentToc) {
          if (!parentToc.children) parentToc.children = [];
          parentToc.children.push(tocItem);
        }
      }
      
      currentContent = [];
    }
  };

  const findParentTOC = (tocList: WhitepaperTOC[], currentLevel: number): WhitepaperTOC | null => {
    for (let i = tocList.length - 1; i >= 0; i--) {
      const item = tocList[i];
      if (item.level < currentLevel) {
        return item;
      }
      if (item.children) {
        const parent = findParentTOC(item.children, currentLevel);
        if (parent) return parent;
      }
    }
    return null;
  };

  lines.forEach((line, index) => {
    const trimmedLine = line.trim();
    
    // 检测标题
    const headerMatch = trimmedLine.match(/^(#{1,4})\s+(.+)$/);
    if (headerMatch) {
      // 处理前一个章节
      processCurrentSection();
      
      const level = headerMatch[1].length;
      const title = headerMatch[2];
      const sectionNumber = extractSectionNumber(title);
      const sectionType = determineSectionType(title, sectionNumber);
      
      sectionCounter++;
      
      currentSection = {
        id: `${sectionCounter}`, // 只使用数字ID，前缀在使用时添加
        title: title,
        level: level,
        content: '',
        type: sectionType,
        icon: SECTION_TYPES[sectionType].icon,
        color: SECTION_TYPES[sectionType].color
      };
      
      // 估算阅读时间（只对主要章节计算，即## 级别的章节）
      if (level === 2) { // 只对主要章节（## 级别）计算阅读时间
        const structureKey = sectionNumber?.split('.')[0] || 'default';
        const estimatedTime = WHITEPAPER_STRUCTURE[structureKey]?.estimatedReadTime || 0;
        totalEstimatedTime += estimatedTime;
      }
      
    } else if (trimmedLine !== '' || currentContent.length > 0) {
      // 添加内容行
      currentContent.push(line);
    }
  });

  // 处理最后一个章节
  processCurrentSection();

  return {
    sections,
    toc,
    metadata: {
      totalSections: sections.filter(s => s.level === 1).length,
      estimatedReadTime: totalEstimatedTime
    }
  };
};

// 渲染单个段落
export const renderParagraph = (text: string): string => {
  return text
    // 粗体文本
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    // 斜体文本
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    // 代码片段
    .replace(/`(.*?)`/g, '<code class="bg-system-gray-5 px-1 py-0.5 rounded text-sm">$1</code>')
    // 链接 - 区分内部锚点链接和外部链接
    .replace(/\[([^\]]+)\]\(([^)]+)\)/g, (match, text, url) => {
      if (url.startsWith('#')) {
        // 内部锚点链接 - 使用data属性存储目标，避免页面跳转
        const cleanAnchor = url.substring(1); // 移除#号
        return `<span class="text-system-blue hover:underline cursor-pointer internal-link" data-anchor="${cleanAnchor}">${text}</span>`;
      } else {
        // 外部链接 - 在新窗口打开
        return `<a href="${url}" class="text-system-blue hover:underline" target="_blank" rel="noopener noreferrer">${text}</a>`;
      }
    });
};

// 渲染列表项
export const renderListItem = (text: string, ordered: boolean = false, index: number = 0): string => {
  const content = renderParagraph(text.replace(/^[-*+]\s+/, ''));
  const marker = ordered ? `${index + 1}.` : '•';
  
  return `<li class="flex items-start space-x-2 mb-2">
    <span class="text-system-blue font-medium mt-0.5 min-w-[1.5rem]">${marker}</span>
    <span class="flex-1">${content}</span>
  </li>`;
};

// 检测内容类型
export const detectContentType = (line: string): 'header' | 'list' | 'paragraph' | 'separator' | 'empty' => {
  const trimmed = line.trim();
  
  if (trimmed === '') return 'empty';
  if (trimmed === '---') return 'separator';
  if (/^#{1,4}\s+/.test(trimmed)) return 'header';
  if (/^[-*+]\s+/.test(trimmed)) return 'list';
  
  return 'paragraph';
};

// 生成章节锚点
export const generateSectionAnchor = (section: WhitepaperSection): string => {
  return `section-${section.id}`;
};

// 处理表格内容
export const renderTable = (lines: string[]): string => {
  if (lines.length < 2) return '';

  const headers = lines[0].split('|').map(h => h.trim()).filter(h => h);
  const rows = lines.slice(2).map(line =>
    line.split('|').map(cell => cell.trim()).filter(cell => cell)
  );

  let tableHtml = '<div class="overflow-x-auto my-6">';
  tableHtml += '<table class="min-w-full border border-system-gray-4 rounded-lg">';

  // 表头
  tableHtml += '<thead class="bg-system-gray-6">';
  tableHtml += '<tr>';
  headers.forEach(header => {
    tableHtml += `<th class="px-4 py-2 text-left font-semibold text-label border-b border-system-gray-4">${renderParagraph(header)}</th>`;
  });
  tableHtml += '</tr>';
  tableHtml += '</thead>';

  // 表体
  tableHtml += '<tbody>';
  rows.forEach((row, index) => {
    tableHtml += `<tr class="${index % 2 === 0 ? 'bg-system-background' : 'bg-system-gray-6/30'}">`;
    row.forEach(cell => {
      tableHtml += `<td class="px-4 py-2 text-secondary-label border-b border-system-gray-4/50">${renderParagraph(cell)}</td>`;
    });
    tableHtml += '</tr>';
  });
  tableHtml += '</tbody>';
  tableHtml += '</table>';
  tableHtml += '</div>';

  return tableHtml;
};

// 处理代码块
export const renderCodeBlock = (code: string, language: string = ''): string => {
  return `<div class="my-6">
    <div class="bg-system-gray-6 rounded-t-lg px-4 py-2 border-b border-system-gray-4">
      <span class="text-xs text-secondary-label font-mono">${language || 'code'}</span>
    </div>
    <pre class="bg-system-gray-5 rounded-b-lg p-4 overflow-x-auto">
      <code class="text-sm font-mono text-label">${code}</code>
    </pre>
  </div>`;
};

// 处理引用块
export const renderBlockquote = (text: string): string => {
  return `<blockquote class="border-l-4 border-system-blue pl-4 py-2 my-4 bg-system-blue/5 rounded-r-lg">
    <p class="text-secondary-label italic">${renderParagraph(text)}</p>
  </blockquote>`;
};
