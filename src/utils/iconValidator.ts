/**
 * 图标验证工具
 * 用于检查项目中使用的图标是否都正确定义
 */

import { 
  NavigationIcons,
  FinanceIcons,
  UserIcons,
  ActionIcons,
  FeatureIcons,
  RewardIcons,
  SocialIcons,
  MediaIcons,
  DeviceIcons,
  MiscIcons,
  NotificationIcons
} from '@/config/icons';

// 所有图标组的映射
const iconGroups = {
  NavigationIcons,
  FinanceIcons,
  UserIcons,
  ActionIcons,
  FeatureIcons,
  RewardIcons,
  SocialIcons,
  MediaIcons,
  DeviceIcons,
  MiscIcons,
  NotificationIcons
} as const;

/**
 * 验证图标是否存在
 */
export function validateIcon(groupName: keyof typeof iconGroups, iconName: string): boolean {
  const group = iconGroups[groupName];
  if (!group) {
    console.error(`图标组 ${groupName} 不存在`);
    return false;
  }

  if (!(iconName in group)) {
    console.error(`图标 ${groupName}.${iconName} 不存在`);
    console.log(`可用的图标:`, Object.keys(group));
    return false;
  }

  return true;
}

/**
 * 获取图标组中的所有图标名称
 */
export function getAvailableIcons(groupName: keyof typeof iconGroups): string[] {
  const group = iconGroups[groupName];
  return group ? Object.keys(group) : [];
}

/**
 * 获取所有可用的图标组
 */
export function getAvailableIconGroups(): string[] {
  return Object.keys(iconGroups);
}

/**
 * 安全获取图标，如果不存在则返回默认图标
 */
export function safeGetIcon(groupName: keyof typeof iconGroups, iconName: string, fallback?: any) {
  const group = iconGroups[groupName];
  if (!group || !(iconName in group)) {
    console.warn(`图标 ${groupName}.${iconName} 不存在，使用默认图标`);
    return fallback || ActionIcons.alert;
  }
  
  return (group as any)[iconName];
}

/**
 * 开发环境下的图标使用检查
 */
export function checkIconUsage() {
  if (process.env.NODE_ENV !== 'development') {
    return;
  }

  console.log('🔍 图标使用检查:');
  console.log('可用的图标组:', getAvailableIconGroups());
  
  Object.entries(iconGroups).forEach(([groupName, group]) => {
    console.log(`${groupName}:`, Object.keys(group));
  });
}

// 在开发环境下自动运行检查
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  checkIconUsage();
}
