/**
 * 认证验证API集成测试
 */

import { createMocks } from 'node-mocks-http';
import { GET, POST } from '@/app/api/auth/verify/route';
import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'test-secret';

// Mock cookies
jest.mock('next/headers', () => ({
  cookies: () => ({
    get: jest.fn(),
    set: jest.fn(),
    delete: jest.fn(),
  }),
}));

describe('/api/auth/verify', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/auth/verify', () => {
    it('应该在没有token时返回401', async () => {
      const { cookies } = require('next/headers');
      cookies().get.mockReturnValue(undefined);

      const { req } = createMocks({
        method: 'GET',
      });

      const response = await GET(req);
      expect(response.status).toBe(401);

      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error.code).toBe('UNAUTHORIZED');
    });

    it('应该在有效token时返回用户信息', async () => {
      const userData = {
        userId: 123,
        username: 'testuser',
        firstName: 'Test',
        lastName: 'User',
        photoUrl: 'https://example.com/photo.jpg',
      };

      const token = jwt.sign(userData, JWT_SECRET, { expiresIn: '1h' });

      const { cookies } = require('next/headers');
      cookies().get.mockReturnValue({ value: token });

      const { req } = createMocks({
        method: 'GET',
      });

      const response = await GET(req);
      expect(response.status).toBe(200);

      const data = await response.json();
      expect(data.success).toBe(true);
      expect(data.data.id).toBe(userData.userId);
      expect(data.data.username).toBe(userData.username);
    });

    it('应该在无效token时返回401', async () => {
      const { cookies } = require('next/headers');
      cookies().get.mockReturnValue({ value: 'invalid-token' });

      const { req } = createMocks({
        method: 'GET',
      });

      const response = await GET(req);
      expect(response.status).toBe(401);

      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error.code).toBe('INVALID_TOKEN');
    });

    it('应该在过期token时返回401', async () => {
      const userData = {
        userId: 123,
        username: 'testuser',
        firstName: 'Test',
        lastName: 'User',
      };

      const expiredToken = jwt.sign(userData, JWT_SECRET, { expiresIn: '-1h' });

      const { cookies } = require('next/headers');
      cookies().get.mockReturnValue({ value: expiredToken });

      const { req } = createMocks({
        method: 'GET',
      });

      const response = await GET(req);
      expect(response.status).toBe(401);

      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error.code).toBe('TOKEN_EXPIRED');
    });
  });

  describe('POST /api/auth/verify', () => {
    it('应该验证请求体中的token', async () => {
      const userData = {
        userId: 123,
        username: 'testuser',
        firstName: 'Test',
        lastName: 'User',
      };

      const token = jwt.sign(userData, JWT_SECRET, { expiresIn: '1h' });

      const { req } = createMocks({
        method: 'POST',
        body: { token },
      });

      // Mock request.json()
      req.json = jest.fn().mockResolvedValue({ token });

      const response = await POST(req);
      expect(response.status).toBe(200);

      const data = await response.json();
      expect(data.success).toBe(true);
      expect(data.data.id).toBe(userData.userId);
    });

    it('应该在缺少token时返回400', async () => {
      const { req } = createMocks({
        method: 'POST',
        body: {},
      });

      req.json = jest.fn().mockResolvedValue({});

      const response = await POST(req);
      expect(response.status).toBe(400);

      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error.code).toBe('VALIDATION_ERROR');
    });

    it('应该在无效token时返回401', async () => {
      const { req } = createMocks({
        method: 'POST',
        body: { token: 'invalid-token' },
      });

      req.json = jest.fn().mockResolvedValue({ token: 'invalid-token' });

      const response = await POST(req);
      expect(response.status).toBe(401);

      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error.code).toBe('INVALID_TOKEN');
    });
  });
});
