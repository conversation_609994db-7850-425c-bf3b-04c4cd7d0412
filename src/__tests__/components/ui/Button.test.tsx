/**
 * Button组件测试
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { Button } from '@/components/ui/button';

describe('Button组件', () => {
  it('应该渲染基本按钮', () => {
    render(<Button>点击我</Button>);
    const button = screen.getByRole('button', { name: '点击我' });
    expect(button).toBeInTheDocument();
  });

  it('应该处理点击事件', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>点击我</Button>);
    
    const button = screen.getByRole('button', { name: '点击我' });
    fireEvent.click(button);
    
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('应该在禁用时不响应点击', () => {
    const handleClick = jest.fn();
    render(
      <Button onClick={handleClick} disabled>
        禁用按钮
      </Button>
    );
    
    const button = screen.getByRole('button', { name: '禁用按钮' });
    fireEvent.click(button);
    
    expect(handleClick).not.toHaveBeenCalled();
    expect(button).toBeDisabled();
  });

  it('应该应用不同的变体样式', () => {
    const { rerender } = render(<Button variant="default">默认</Button>);
    let button = screen.getByRole('button');
    expect(button).toHaveClass('bg-primary');

    rerender(<Button variant="destructive">危险</Button>);
    button = screen.getByRole('button');
    expect(button).toHaveClass('bg-destructive');

    rerender(<Button variant="outline">轮廓</Button>);
    button = screen.getByRole('button');
    expect(button).toHaveClass('border-input');

    rerender(<Button variant="secondary">次要</Button>);
    button = screen.getByRole('button');
    expect(button).toHaveClass('bg-secondary');

    rerender(<Button variant="ghost">幽灵</Button>);
    button = screen.getByRole('button');
    expect(button).toHaveClass('hover:bg-accent');

    rerender(<Button variant="link">链接</Button>);
    button = screen.getByRole('button');
    expect(button).toHaveClass('text-primary');
  });

  it('应该应用不同的尺寸', () => {
    const { rerender } = render(<Button size="default">默认</Button>);
    let button = screen.getByRole('button');
    expect(button).toHaveClass('h-10');

    rerender(<Button size="sm">小</Button>);
    button = screen.getByRole('button');
    expect(button).toHaveClass('h-9');

    rerender(<Button size="lg">大</Button>);
    button = screen.getByRole('button');
    expect(button).toHaveClass('h-11');

    rerender(<Button size="icon">图标</Button>);
    button = screen.getByRole('button');
    expect(button).toHaveClass('h-10', 'w-10');
  });

  it('应该支持自定义className', () => {
    render(<Button className="custom-class">自定义</Button>);
    const button = screen.getByRole('button');
    expect(button).toHaveClass('custom-class');
  });

  it('应该支持asChild属性', () => {
    render(
      <Button asChild>
        <a href="/test">链接按钮</a>
      </Button>
    );
    
    const link = screen.getByRole('link');
    expect(link).toBeInTheDocument();
    expect(link).toHaveAttribute('href', '/test');
  });

  it('应该传递其他HTML属性', () => {
    render(
      <Button 
        type="submit" 
        data-testid="submit-button"
        aria-label="提交表单"
      >
        提交
      </Button>
    );
    
    const button = screen.getByTestId('submit-button');
    expect(button).toHaveAttribute('type', 'submit');
    expect(button).toHaveAttribute('aria-label', '提交表单');
  });

  it('应该在加载状态下显示加载指示器', () => {
    // 注意：这需要Button组件支持loading属性
    // 如果组件不支持，可以跳过这个测试或修改组件
    render(<Button disabled>加载中...</Button>);
    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
  });

  it('应该支持键盘导航', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>键盘测试</Button>);
    
    const button = screen.getByRole('button');
    button.focus();
    expect(button).toHaveFocus();
    
    fireEvent.keyDown(button, { key: 'Enter' });
    expect(handleClick).toHaveBeenCalledTimes(1);
    
    fireEvent.keyDown(button, { key: ' ' });
    expect(handleClick).toHaveBeenCalledTimes(2);
  });
});
