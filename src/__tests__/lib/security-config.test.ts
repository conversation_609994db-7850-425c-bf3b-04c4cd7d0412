/**
 * 安全配置测试
 */

import {
  getSecurityConfig,
  validatePasswordStrength,
  validateFileUpload,
  requiresReauth,
  generateSecureToken,
  isWhitelistedIP,
} from '@/lib/security-config';

describe('安全配置', () => {
  describe('getSecurityConfig', () => {
    it('应该返回默认安全配置', () => {
      const config = getSecurityConfig();
      
      expect(config.rateLimit).toBeDefined();
      expect(config.jwt).toBeDefined();
      expect(config.cookie).toBeDefined();
      expect(config.cors).toBeDefined();
      expect(config.password).toBeDefined();
      expect(config.upload).toBeDefined();
      expect(config.session).toBeDefined();
    });

    it('应该包含正确的限流配置', () => {
      const config = getSecurityConfig();
      
      expect(config.rateLimit.auth.requests).toBeGreaterThan(0);
      expect(config.rateLimit.api.requests).toBeGreaterThan(0);
      expect(config.rateLimit.upload.requests).toBeGreaterThan(0);
      expect(config.rateLimit.sensitive.requests).toBeGreaterThan(0);
    });
  });

  describe('validatePasswordStrength', () => {
    it('应该拒绝过短的密码', () => {
      const result = validatePasswordStrength('123');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('密码长度至少8位');
    });

    it('应该拒绝缺少大写字母的密码', () => {
      const result = validatePasswordStrength('password123!');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('密码必须包含大写字母');
    });

    it('应该拒绝缺少小写字母的密码', () => {
      const result = validatePasswordStrength('PASSWORD123!');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('密码必须包含小写字母');
    });

    it('应该拒绝缺少数字的密码', () => {
      const result = validatePasswordStrength('Password!');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('密码必须包含数字');
    });

    it('应该拒绝缺少特殊字符的密码', () => {
      const result = validatePasswordStrength('Password123');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('密码必须包含特殊字符');
    });

    it('应该接受强密码', () => {
      const result = validatePasswordStrength('StrongPassword123!');
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });
  });

  describe('validateFileUpload', () => {
    it('应该拒绝过大的文件', () => {
      const file = {
        size: 20 * 1024 * 1024, // 20MB
        type: 'image/jpeg'
      };
      
      const result = validateFileUpload(file);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('文件大小不能超过');
    });

    it('应该拒绝不支持的文件类型', () => {
      const file = {
        size: 1024, // 1KB
        type: 'application/exe'
      };
      
      const result = validateFileUpload(file);
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('不支持的文件类型');
    });

    it('应该接受有效的文件', () => {
      const file = {
        size: 1024, // 1KB
        type: 'image/jpeg'
      };
      
      const result = validateFileUpload(file);
      expect(result.isValid).toBe(true);
      expect(result.error).toBeUndefined();
    });
  });

  describe('requiresReauth', () => {
    it('应该要求敏感路径重新认证', () => {
      expect(requiresReauth('/api/wallet/withdraw')).toBe(true);
      expect(requiresReauth('/api/wallet/transfer')).toBe(true);
      expect(requiresReauth('/api/merchant/funds')).toBe(true);
    });

    it('应该匹配通配符路径', () => {
      expect(requiresReauth('/api/admin/users')).toBe(true);
      expect(requiresReauth('/api/admin/settings')).toBe(true);
    });

    it('应该不要求普通路径重新认证', () => {
      expect(requiresReauth('/api/user/profile')).toBe(false);
      expect(requiresReauth('/api/public/data')).toBe(false);
    });
  });

  describe('generateSecureToken', () => {
    it('应该生成指定长度的令牌', () => {
      const token = generateSecureToken(16);
      expect(token).toHaveLength(32); // hex编码后长度翻倍
    });

    it('应该生成不同的令牌', () => {
      const token1 = generateSecureToken();
      const token2 = generateSecureToken();
      expect(token1).not.toBe(token2);
    });

    it('应该只包含十六进制字符', () => {
      const token = generateSecureToken();
      expect(token).toMatch(/^[a-f0-9]+$/);
    });
  });

  describe('isWhitelistedIP', () => {
    const originalEnv = process.env;

    beforeEach(() => {
      jest.resetModules();
      process.env = { ...originalEnv };
    });

    afterEach(() => {
      process.env = originalEnv;
    });

    it('应该在开发环境中允许所有IP', () => {
      process.env.NODE_ENV = 'development';
      expect(isWhitelistedIP('***********')).toBe(true);
    });

    it('应该检查白名单中的IP', () => {
      process.env.NODE_ENV = 'production';
      process.env.ADMIN_IP_WHITELIST = '***********,********';
      
      expect(isWhitelistedIP('***********')).toBe(true);
      expect(isWhitelistedIP('********')).toBe(true);
      expect(isWhitelistedIP('***********')).toBe(false);
    });

    it('应该在没有白名单时拒绝所有IP', () => {
      process.env.NODE_ENV = 'production';
      delete process.env.ADMIN_IP_WHITELIST;
      
      expect(isWhitelistedIP('***********')).toBe(false);
    });
  });
});
