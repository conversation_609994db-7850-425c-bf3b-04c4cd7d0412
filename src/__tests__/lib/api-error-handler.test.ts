/**
 * API错误处理工具测试
 */

import {
  createApiError,
  createSuccessResponse,
  createErrorResponse,
  handleUnknownError,
  validateRequestBody,
  validateQueryParams,
  ApiErrorCode,
} from '@/lib/api-error-handler';
import { JsonWebTokenError, TokenExpiredError } from 'jsonwebtoken';

describe('API错误处理工具', () => {
  describe('createApiError', () => {
    it('应该创建标准化的API错误', () => {
      const error = createApiError(
        ApiErrorCode.BAD_REQUEST,
        '请求参数错误',
        400,
        { field: 'username' }
      );

      expect(error.code).toBe(ApiErrorCode.BAD_REQUEST);
      expect(error.message).toBe('请求参数错误');
      expect(error.statusCode).toBe(400);
      expect(error.details).toEqual({ field: 'username' });
    });
  });

  describe('createSuccessResponse', () => {
    it('应该创建成功响应', () => {
      const data = { id: 1, name: 'test' };
      const response = createSuccessResponse(data, '操作成功');

      expect(response.status).toBe(200);
      
      // 测试响应体
      response.json().then(body => {
        expect(body.success).toBe(true);
        expect(body.data).toEqual(data);
        expect(body.timestamp).toBeDefined();
      });
    });
  });

  describe('createErrorResponse', () => {
    it('应该创建错误响应', () => {
      const error = createApiError(
        ApiErrorCode.NOT_FOUND,
        '资源未找到',
        404
      );
      
      const response = createErrorResponse(error);

      expect(response.status).toBe(404);
      
      // 测试响应体
      response.json().then(body => {
        expect(body.success).toBe(false);
        expect(body.error.code).toBe(ApiErrorCode.NOT_FOUND);
        expect(body.error.message).toBe('资源未找到');
        expect(body.timestamp).toBeDefined();
      });
    });
  });

  describe('handleUnknownError', () => {
    it('应该处理JWT过期错误', () => {
      const jwtError = new TokenExpiredError('jwt expired', new Date());
      const apiError = handleUnknownError(jwtError);

      expect(apiError.code).toBe(ApiErrorCode.TOKEN_EXPIRED);
      expect(apiError.statusCode).toBe(401);
    });

    it('应该处理一般JWT错误', () => {
      const jwtError = new JsonWebTokenError('invalid token');
      const apiError = handleUnknownError(jwtError);

      expect(apiError.code).toBe(ApiErrorCode.INVALID_TOKEN);
      expect(apiError.statusCode).toBe(401);
    });

    it('应该处理数据库错误', () => {
      const dbError = new Error('database connection failed');
      const apiError = handleUnknownError(dbError);

      expect(apiError.code).toBe(ApiErrorCode.DATABASE_ERROR);
      expect(apiError.statusCode).toBe(500);
    });

    it('应该处理网络错误', () => {
      const networkError = new Error('fetch failed');
      const apiError = handleUnknownError(networkError);

      expect(apiError.code).toBe(ApiErrorCode.EXTERNAL_SERVICE_ERROR);
      expect(apiError.statusCode).toBe(503);
    });

    it('应该处理一般错误', () => {
      const generalError = new Error('something went wrong');
      const apiError = handleUnknownError(generalError);

      expect(apiError.code).toBe(ApiErrorCode.INTERNAL_SERVER_ERROR);
      expect(apiError.statusCode).toBe(500);
    });

    it('应该处理非Error类型的错误', () => {
      const unknownError = 'string error';
      const apiError = handleUnknownError(unknownError);

      expect(apiError.code).toBe(ApiErrorCode.INTERNAL_SERVER_ERROR);
      expect(apiError.statusCode).toBe(500);
    });
  });

  describe('validateRequestBody', () => {
    it('应该验证空请求体', () => {
      const error = validateRequestBody(null, ['username']);
      expect(error).not.toBeNull();
      expect(error!.code).toBe(ApiErrorCode.BAD_REQUEST);
    });

    it('应该验证缺失字段', () => {
      const body = { username: 'test' };
      const error = validateRequestBody(body, ['username', 'email']);
      
      expect(error).not.toBeNull();
      expect(error!.code).toBe(ApiErrorCode.VALIDATION_ERROR);
      expect(error!.details.missingFields).toContain('email');
    });

    it('应该通过完整字段验证', () => {
      const body = { username: 'test', email: '<EMAIL>' };
      const error = validateRequestBody(body, ['username', 'email']);
      
      expect(error).toBeNull();
    });

    it('应该拒绝空字符串字段', () => {
      const body = { username: '', email: '<EMAIL>' };
      const error = validateRequestBody(body, ['username', 'email']);
      
      expect(error).not.toBeNull();
      expect(error!.details.missingFields).toContain('username');
    });
  });

  describe('validateQueryParams', () => {
    it('应该验证缺失的查询参数', () => {
      const searchParams = new URLSearchParams('?page=1');
      const error = validateQueryParams(searchParams, ['page', 'limit']);
      
      expect(error).not.toBeNull();
      expect(error!.code).toBe(ApiErrorCode.VALIDATION_ERROR);
      expect(error!.details.missingParams).toContain('limit');
    });

    it('应该通过完整参数验证', () => {
      const searchParams = new URLSearchParams('?page=1&limit=10');
      const error = validateQueryParams(searchParams, ['page', 'limit']);
      
      expect(error).toBeNull();
    });

    it('应该拒绝空参数值', () => {
      const searchParams = new URLSearchParams('?page=&limit=10');
      const error = validateQueryParams(searchParams, ['page', 'limit']);
      
      expect(error).not.toBeNull();
      expect(error!.details.missingParams).toContain('page');
    });
  });
});
