/**
 * 输入验证工具测试
 */

import {
  validateField,
  validateObject,
  sanitizeString,
  sanitizeEmail,
  sanitizeUrl,
  isValidEmail,
  isValidUrl,
  isValidAddress,
  commonRules,
} from '@/lib/input-validation';

describe('输入验证工具', () => {
  describe('validateField', () => {
    it('应该验证必填字段', () => {
      const result = validateField('', { required: true }, 'username');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('username是必填项');
    });

    it('应该验证字符串长度', () => {
      const result = validateField('ab', { 
        type: 'string', 
        minLength: 3 
      }, 'username');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('username长度不能少于3个字符');
    });

    it('应该验证数字类型', () => {
      const result = validateField('abc', { type: 'number' }, 'age');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('age必须是有效数字');
    });

    it('应该验证邮箱格式', () => {
      const result = validateField('invalid-email', { type: 'email' }, 'email');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('email必须是有效的邮箱地址');
    });

    it('应该通过有效的邮箱验证', () => {
      const result = validateField('<EMAIL>', { type: 'email' }, 'email');
      expect(result.isValid).toBe(true);
      expect(result.sanitizedValue).toBe('<EMAIL>');
    });

    it('应该验证自定义规则', () => {
      const result = validateField('test', {
        custom: (value) => value === 'valid' || '值必须是valid'
      }, 'field');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('值必须是valid');
    });
  });

  describe('validateObject', () => {
    it('应该验证整个对象', () => {
      const data = {
        username: 'ab',
        email: 'invalid-email',
        age: 'not-a-number'
      };

      const schema = {
        username: { type: 'string' as const, minLength: 3 },
        email: { type: 'email' as const },
        age: { type: 'number' as const }
      };

      const result = validateObject(data, schema);
      expect(result.isValid).toBe(false);
      expect(Object.keys(result.errors)).toHaveLength(3);
    });

    it('应该返回清理后的数据', () => {
      const data = {
        username: '  validuser  ',
        email: '  <EMAIL>  '
      };

      const schema = {
        username: { type: 'string' as const },
        email: { type: 'email' as const }
      };

      const result = validateObject(data, schema);
      expect(result.isValid).toBe(true);
      expect(result.sanitizedData.username).toBe('validuser');
      expect(result.sanitizedData.email).toBe('<EMAIL>');
    });
  });

  describe('sanitizeString', () => {
    it('应该移除危险字符', () => {
      const input = '  <script>alert("xss")</script>  ';
      const result = sanitizeString(input);
      expect(result).not.toContain('<script>');
      expect(result).not.toContain('</script>');
    });

    it('应该移除JavaScript协议', () => {
      const input = 'javascript:alert("xss")';
      const result = sanitizeString(input);
      expect(result).not.toContain('javascript:');
    });
  });

  describe('sanitizeEmail', () => {
    it('应该转换为小写并去除空格', () => {
      const input = '  <EMAIL>  ';
      const result = sanitizeEmail(input);
      expect(result).toBe('<EMAIL>');
    });
  });

  describe('sanitizeUrl', () => {
    it('应该添加https协议', () => {
      const input = 'example.com';
      const result = sanitizeUrl(input);
      expect(result).toBe('https://example.com');
    });

    it('应该保留现有的协议', () => {
      const input = 'http://example.com';
      const result = sanitizeUrl(input);
      expect(result).toBe('http://example.com');
    });
  });

  describe('isValidEmail', () => {
    it('应该验证有效的邮箱', () => {
      expect(isValidEmail('<EMAIL>')).toBe(true);
      expect(isValidEmail('<EMAIL>')).toBe(true);
    });

    it('应该拒绝无效的邮箱', () => {
      expect(isValidEmail('invalid-email')).toBe(false);
      expect(isValidEmail('@example.com')).toBe(false);
      expect(isValidEmail('test@')).toBe(false);
    });
  });

  describe('isValidUrl', () => {
    it('应该验证有效的URL', () => {
      expect(isValidUrl('https://example.com')).toBe(true);
      expect(isValidUrl('http://localhost:3000')).toBe(true);
    });

    it('应该拒绝无效的URL', () => {
      expect(isValidUrl('not-a-url')).toBe(false);
      expect(isValidUrl('ftp://example.com')).toBe(true); // ftp是有效协议
    });
  });

  describe('isValidAddress', () => {
    it('应该验证有效的以太坊地址', () => {
      const validAddress = '0x742d35Cc6634C0532925a3b8D4C9db96C4b4d8b1';
      expect(isValidAddress(validAddress)).toBe(true);
    });

    it('应该拒绝无效的地址', () => {
      expect(isValidAddress('invalid-address')).toBe(false);
      expect(isValidAddress('0x123')).toBe(false);
      expect(isValidAddress('742d35Cc6634C0532925a3b8D4C9db96C4b4d8b1')).toBe(false);
    });
  });

  describe('commonRules', () => {
    it('应该验证用户名规则', () => {
      const result = validateField('validuser123', commonRules.username, 'username');
      expect(result.isValid).toBe(true);
    });

    it('应该拒绝包含特殊字符的用户名', () => {
      const result = validateField('invalid-user!', commonRules.username, 'username');
      expect(result.isValid).toBe(false);
    });

    it('应该验证密码强度', () => {
      const weakPassword = validateField('weak', commonRules.password, 'password');
      expect(weakPassword.isValid).toBe(false);

      const strongPassword = validateField('StrongPass123!', commonRules.password, 'password');
      expect(strongPassword.isValid).toBe(true);
    });

    it('应该验证金额', () => {
      const validAmount = validateField('123.45', commonRules.amount, 'amount');
      expect(validAmount.isValid).toBe(true);
      expect(validAmount.sanitizedValue).toBe(123.45);

      const invalidAmount = validateField('-10', commonRules.amount, 'amount');
      expect(invalidAmount.isValid).toBe(false);
    });
  });
});
