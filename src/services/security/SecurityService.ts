/**
 * 安全防护服务
 * 防刷机制、异常检测、安全验证
 */

import { supabase } from '@/lib/supabase';

// 安全配置
export const SECURITY_CONFIG = {
  // 防作弊配置
  ANTI_CHEAT: {
    MAX_ACCOUNTS_PER_IP: 3,           // 每IP最大账户数
    MAX_BETS_PER_HOUR: 20,            // 每小时最大投注次数
    MAX_JUDGMENTS_PER_HOUR: 10,       // 每小时最大裁定次数
    SUSPICIOUS_PATTERN_THRESHOLD: 5,   // 可疑模式阈值
    COORDINATED_VOTING_THRESHOLD: 0.8  // 协调投票阈值
  },
  
  // 资金安全配置
  FUND_SECURITY: {
    MAX_SINGLE_BET: 10000,            // 单次最大投注金额
    MAX_DAILY_BET: 50000,             // 每日最大投注金额
    LARGE_TRANSACTION_THRESHOLD: 5000, // 大额交易阈值
    SUSPICIOUS_VELOCITY_THRESHOLD: 100 // 可疑交易速度阈值
  },
  
  // 用户行为监控
  BEHAVIOR_MONITORING: {
    MAX_LOGIN_ATTEMPTS: 5,            // 最大登录尝试次数
    ACCOUNT_LOCKOUT_DURATION: 30,     // 账户锁定时长（分钟）
    RAPID_ACTION_THRESHOLD: 10,       // 快速操作阈值（秒）
    SUSPICIOUS_ACTIVITY_SCORE: 100    // 可疑活动分数阈值
  },
  
  // 数据完整性
  DATA_INTEGRITY: {
    BALANCE_CHECK_INTERVAL: 60,       // 余额检查间隔（分钟）
    TRANSACTION_AUDIT_BATCH: 1000,    // 交易审计批次大小
    INCONSISTENCY_THRESHOLD: 0.01     // 不一致性阈值
  }
};

// 安全事件类型
export type SecurityEventType = 
  | 'suspicious_login'
  | 'coordinated_voting'
  | 'rapid_betting'
  | 'large_transaction'
  | 'balance_inconsistency'
  | 'ip_clustering'
  | 'unusual_pattern';

// 安全事件接口
export interface SecurityEvent {
  id: string;
  type: SecurityEventType;
  userId: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  metadata: any;
  status: 'open' | 'investigating' | 'resolved' | 'false_positive';
  createdAt: string;
  resolvedAt?: string;
}

// 用户风险评分接口
export interface UserRiskScore {
  userId: string;
  totalScore: number;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  factors: Array<{
    factor: string;
    score: number;
    description: string;
  }>;
  lastUpdated: string;
}

export class SecurityService {
  /**
   * 检测协调投票
   */
  static async detectCoordinatedVoting(betId: string): Promise<SecurityEvent[]> {
    const events: SecurityEvent[] = [];

    // 获取裁定投票数据
    const { data: judgments } = await supabase
      .from('bet_judgments')
      .select(`
        judge_id,
        selected_option,
        created_at,
        user_reputation!judge_id (
          user_id
        )
      `)
      .eq('bet_id', betId);

    if (!judgments || judgments.length < 3) return events;

    // 分析投票模式
    const votingPatterns = this.analyzeVotingPatterns(judgments);
    
    // 检测时间聚集
    const timeClusteringScore = this.calculateTimeClusteringScore(judgments);
    
    // 检测选项聚集
    const optionClusteringScore = this.calculateOptionClusteringScore(judgments);
    
    // 检测IP聚集（需要IP数据）
    const ipClusteringScore = await this.calculateIPClusteringScore(judgments.map(j => j.judge_id));

    const totalSuspiciousScore = timeClusteringScore + optionClusteringScore + ipClusteringScore;

    if (totalSuspiciousScore > SECURITY_CONFIG.ANTI_CHEAT.COORDINATED_VOTING_THRESHOLD) {
      events.push({
        id: this.generateEventId(),
        type: 'coordinated_voting',
        userId: 'system',
        severity: totalSuspiciousScore > 0.9 ? 'critical' : 'high',
        description: `检测到赌约 ${betId} 存在协调投票行为`,
        metadata: {
          betId,
          suspiciousScore: totalSuspiciousScore,
          timeClusteringScore,
          optionClusteringScore,
          ipClusteringScore,
          judgmentCount: judgments.length
        },
        status: 'open',
        createdAt: new Date().toISOString()
      });
    }

    return events;
  }

  /**
   * 检测快速投注行为
   */
  static async detectRapidBetting(userId: string): Promise<SecurityEvent[]> {
    const events: SecurityEvent[] = [];
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);

    // 获取用户最近一小时的投注
    const { data: recentBets } = await supabase
      .from('bet_participants')
      .select('created_at, bet_amount')
      .eq('user_id', userId)
      .gte('created_at', oneHourAgo.toISOString())
      .order('created_at', { ascending: false });

    if (!recentBets || recentBets.length === 0) return events;

    const betCount = recentBets.length;
    const totalAmount = recentBets.reduce((sum, bet) => sum + bet.bet_amount, 0);

    // 检查投注频率
    if (betCount > SECURITY_CONFIG.ANTI_CHEAT.MAX_BETS_PER_HOUR) {
      events.push({
        id: this.generateEventId(),
        type: 'rapid_betting',
        userId,
        severity: betCount > SECURITY_CONFIG.ANTI_CHEAT.MAX_BETS_PER_HOUR * 2 ? 'high' : 'medium',
        description: `用户在一小时内进行了 ${betCount} 次投注`,
        metadata: {
          betCount,
          totalAmount,
          timeWindow: '1hour'
        },
        status: 'open',
        createdAt: new Date().toISOString()
      });
    }

    // 检查大额交易
    if (totalAmount > SECURITY_CONFIG.FUND_SECURITY.LARGE_TRANSACTION_THRESHOLD) {
      events.push({
        id: this.generateEventId(),
        type: 'large_transaction',
        userId,
        severity: totalAmount > SECURITY_CONFIG.FUND_SECURITY.LARGE_TRANSACTION_THRESHOLD * 2 ? 'high' : 'medium',
        description: `用户在一小时内投注总额达到 ${totalAmount} 福气`,
        metadata: {
          totalAmount,
          betCount,
          timeWindow: '1hour'
        },
        status: 'open',
        createdAt: new Date().toISOString()
      });
    }

    return events;
  }

  /**
   * 检测余额不一致
   */
  static async detectBalanceInconsistency(userId: string): Promise<SecurityEvent[]> {
    const events: SecurityEvent[] = [];

    // 获取用户当前福气余额
    const { data: userFortune } = await supabase
      .from('user_fortune')
      .select('total_fortune, available_fortune, locked_fortune')
      .eq('user_id', userId)
      .single();

    if (!userFortune) return events;

    // 计算交易总和
    const { data: transactions } = await supabase
      .from('fortune_transactions')
      .select('amount, transaction_type')
      .eq('user_id', userId);

    if (!transactions) return events;

    const calculatedBalance = transactions.reduce((balance, tx) => {
      return tx.transaction_type.includes('add') || tx.transaction_type.includes('reward')
        ? balance + tx.amount
        : balance - tx.amount;
    }, 0);

    const actualBalance = userFortune.total_fortune;
    const difference = Math.abs(calculatedBalance - actualBalance);
    const discrepancyRate = actualBalance > 0 ? difference / actualBalance : 0;

    if (discrepancyRate > SECURITY_CONFIG.DATA_INTEGRITY.INCONSISTENCY_THRESHOLD) {
      events.push({
        id: this.generateEventId(),
        type: 'balance_inconsistency',
        userId,
        severity: discrepancyRate > 0.1 ? 'critical' : 'high',
        description: `用户余额不一致：计算值 ${calculatedBalance}，实际值 ${actualBalance}`,
        metadata: {
          calculatedBalance,
          actualBalance,
          difference,
          discrepancyRate,
          transactionCount: transactions.length
        },
        status: 'open',
        createdAt: new Date().toISOString()
      });
    }

    return events;
  }

  /**
   * 计算用户风险评分
   */
  static async calculateUserRiskScore(userId: string): Promise<UserRiskScore> {
    const factors: Array<{ factor: string; score: number; description: string }> = [];
    let totalScore = 0;

    // 1. 账户年龄因子
    const { data: user } = await supabase
      .from('users')
      .select('created_at')
      .eq('id', userId)
      .single();

    if (user) {
      const accountAge = (Date.now() - new Date(user.created_at).getTime()) / (1000 * 60 * 60 * 24);
      const ageScore = Math.max(0, 20 - accountAge); // 新账户风险更高
      factors.push({
        factor: 'account_age',
        score: ageScore,
        description: `账户年龄 ${Math.floor(accountAge)} 天`
      });
      totalScore += ageScore;
    }

    // 2. 投注行为因子
    const recentBetEvents = await this.detectRapidBetting(userId);
    const bettingScore = recentBetEvents.length * 15;
    factors.push({
      factor: 'betting_behavior',
      score: bettingScore,
      description: `最近检测到 ${recentBetEvents.length} 个投注异常`
    });
    totalScore += bettingScore;

    // 3. 裁定行为因子
    const { data: judgmentStats } = await supabase
      .from('user_reputation')
      .select('accuracy_rate, total_judgments')
      .eq('user_id', userId)
      .single();

    if (judgmentStats) {
      const judgmentScore = judgmentStats.total_judgments > 10 && judgmentStats.accuracy_rate < 0.3 ? 25 : 0;
      factors.push({
        factor: 'judgment_accuracy',
        score: judgmentScore,
        description: `裁定准确率 ${(judgmentStats.accuracy_rate * 100).toFixed(1)}%`
      });
      totalScore += judgmentScore;
    }

    // 4. 余额一致性因子
    const balanceEvents = await this.detectBalanceInconsistency(userId);
    const balanceScore = balanceEvents.length * 30;
    factors.push({
      factor: 'balance_consistency',
      score: balanceScore,
      description: `检测到 ${balanceEvents.length} 个余额异常`
    });
    totalScore += balanceScore;

    // 确定风险等级
    let riskLevel: 'low' | 'medium' | 'high' | 'critical';
    if (totalScore < 20) riskLevel = 'low';
    else if (totalScore < 50) riskLevel = 'medium';
    else if (totalScore < 80) riskLevel = 'high';
    else riskLevel = 'critical';

    return {
      userId,
      totalScore,
      riskLevel,
      factors,
      lastUpdated: new Date().toISOString()
    };
  }

  /**
   * 紧急暂停机制
   */
  static async emergencyPause(
    reason: string,
    affectedUsers?: string[],
    affectedBets?: string[]
  ): Promise<void> {
    console.log(`EMERGENCY PAUSE TRIGGERED: ${reason}`);
    
    // 暂停受影响的用户
    if (affectedUsers) {
      for (const userId of affectedUsers) {
        await supabase
          .from('users')
          .update({ 
            is_suspended: true,
            suspension_reason: reason,
            suspended_at: new Date().toISOString()
          })
          .eq('id', userId);
      }
    }

    // 暂停受影响的赌约
    if (affectedBets) {
      for (const betId of affectedBets) {
        await supabase
          .from('social_bets')
          .update({ 
            status: 'cancelled',
            updated_at: new Date().toISOString()
          })
          .eq('id', betId);
      }
    }

    // 记录紧急暂停事件
    console.log(`Emergency pause completed. Users: ${affectedUsers?.length || 0}, Bets: ${affectedBets?.length || 0}`);
  }

  /**
   * 分析投票模式
   */
  private static analyzeVotingPatterns(judgments: any[]): any {
    // 简化的投票模式分析
    const optionCounts = judgments.reduce((counts, j) => {
      counts[j.selected_option] = (counts[j.selected_option] || 0) + 1;
      return counts;
    }, {} as Record<string, number>);

    return {
      optionDistribution: optionCounts,
      dominantOption: Object.keys(optionCounts).reduce((a, b) => 
        optionCounts[a] > optionCounts[b] ? a : b
      )
    };
  }

  /**
   * 计算时间聚集分数
   */
  private static calculateTimeClusteringScore(judgments: any[]): number {
    if (judgments.length < 3) return 0;

    const timestamps = judgments.map(j => new Date(j.created_at).getTime());
    timestamps.sort((a, b) => a - b);

    let clusterScore = 0;
    for (let i = 1; i < timestamps.length; i++) {
      const timeDiff = timestamps[i] - timestamps[i - 1];
      if (timeDiff < 60000) { // 1分钟内
        clusterScore += 0.2;
      }
    }

    return Math.min(clusterScore, 1.0);
  }

  /**
   * 计算选项聚集分数
   */
  private static calculateOptionClusteringScore(judgments: any[]): number {
    const optionCounts = judgments.reduce((counts, j) => {
      counts[j.selected_option] = (counts[j.selected_option] || 0) + 1;
      return counts;
    }, {} as Record<string, number>);

    const maxCount = Math.max(...Object.values(optionCounts));
    const totalCount = judgments.length;
    
    return maxCount / totalCount > 0.8 ? 0.5 : 0;
  }

  /**
   * 计算IP聚集分数
   */
  private static async calculateIPClusteringScore(userIds: string[]): Promise<number> {
    // 这里应该检查用户IP地址的聚集情况
    // 目前返回模拟分数
    return Math.random() * 0.3;
  }

  /**
   * 生成事件ID
   */
  private static generateEventId(): string {
    return `sec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 批量安全检查
   */
  static async runSecurityScan(): Promise<SecurityEvent[]> {
    const allEvents: SecurityEvent[] = [];

    // 检查最近的赌约
    const { data: recentBets } = await supabase
      .from('social_bets')
      .select('id')
      .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString())
      .limit(50);

    if (recentBets) {
      for (const bet of recentBets) {
        const events = await this.detectCoordinatedVoting(bet.id);
        allEvents.push(...events);
      }
    }

    // 检查活跃用户
    const { data: activeUsers } = await supabase
      .from('bet_participants')
      .select('user_id')
      .gte('created_at', new Date(Date.now() - 60 * 60 * 1000).toISOString())
      .limit(100);

    if (activeUsers) {
      const uniqueUsers = [...new Set(activeUsers.map(u => u.user_id))];
      for (const userId of uniqueUsers) {
        const events = await this.detectRapidBetting(userId);
        allEvents.push(...events);
      }
    }

    return allEvents;
  }
}
