/**
 * 密钥管理服务 (Key Management Service)
 * 管理加密密钥和敏感数据的加密/解密
 */

import { createHash, randomBytes, createCipheriv, createDecipheriv } from 'crypto';

export interface EncryptedData {
  data: string;
  iv: string;
  keyId: string;
  algorithm: string;
}

export class KMSService {
  private readonly algorithm = 'aes-256-gcm';
  private readonly keyLength = 32; // 256 bits
  private masterKey: Buffer;

  constructor() {
    // 在真实环境中，主密钥应该从安全的密钥管理系统获取
    const masterKeyString = process.env.KMS_MASTER_KEY || 'default-master-key-for-development-only';
    this.masterKey = createHash('sha256').update(masterKeyString).digest();
  }

  /**
   * 加密数据
   */
  async encryptData(data: string, keyId?: string): Promise<string> {
    try {
      const iv = randomBytes(16);
      const cipher = createCipheriv(this.algorithm, this.masterKey, iv);
      
      let encrypted = cipher.update(data, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      const authTag = cipher.getAuthTag();
      
      const encryptedData: EncryptedData = {
        data: encrypted,
        iv: iv.toString('hex'),
        keyId: keyId || 'default',
        algorithm: this.algorithm,
      };

      // 将加密数据和认证标签组合
      const result = {
        ...encryptedData,
        authTag: authTag.toString('hex'),
      };

      return Buffer.from(JSON.stringify(result)).toString('base64');
    } catch (error) {
      console.error('Encryption failed:', error);
      throw new Error('数据加密失败');
    }
  }

  /**
   * 解密数据
   */
  async decryptData(encryptedData: string): Promise<string> {
    try {
      const dataObj = JSON.parse(Buffer.from(encryptedData, 'base64').toString('utf8'));
      
      const iv = Buffer.from(dataObj.iv, 'hex');
      const authTag = Buffer.from(dataObj.authTag, 'hex');
      
      const decipher = createDecipheriv(this.algorithm, this.masterKey, iv);
      decipher.setAuthTag(authTag);
      
      let decrypted = decipher.update(dataObj.data, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      return decrypted;
    } catch (error) {
      console.error('Decryption failed:', error);
      throw new Error('数据解密失败');
    }
  }

  /**
   * 生成新的加密密钥
   */
  async generateKey(keyId: string): Promise<string> {
    try {
      const key = randomBytes(this.keyLength);
      const keyData = {
        keyId,
        key: key.toString('hex'),
        createdAt: new Date().toISOString(),
        algorithm: this.algorithm,
      };

      // 在真实环境中，这里会将密钥安全地存储到密钥管理系统
      console.log(`Generated new key: ${keyId}`);
      
      return key.toString('hex');
    } catch (error) {
      console.error('Key generation failed:', error);
      throw new Error('密钥生成失败');
    }
  }

  /**
   * 轮换密钥
   */
  async rotateKey(keyId: string): Promise<string> {
    try {
      // 生成新密钥
      const newKey = await this.generateKey(`${keyId}_rotated_${Date.now()}`);
      
      // 在真实环境中，这里会：
      // 1. 使用新密钥重新加密所有数据
      // 2. 安全地删除旧密钥
      // 3. 更新密钥引用
      
      console.log(`Rotated key: ${keyId}`);
      
      return newKey;
    } catch (error) {
      console.error('Key rotation failed:', error);
      throw new Error('密钥轮换失败');
    }
  }

  /**
   * 验证密钥完整性
   */
  async verifyKeyIntegrity(keyId: string): Promise<boolean> {
    try {
      // 在真实环境中，这里会验证密钥的完整性
      // 例如检查密钥是否被篡改、是否过期等
      
      console.log(`Verifying key integrity: ${keyId}`);
      return true;
    } catch (error) {
      console.error('Key integrity verification failed:', error);
      return false;
    }
  }

  /**
   * 安全删除密钥
   */
  async deleteKey(keyId: string): Promise<boolean> {
    try {
      // 在真实环境中，这里会安全地删除密钥
      // 确保密钥数据被完全清除，无法恢复
      
      console.log(`Securely deleted key: ${keyId}`);
      return true;
    } catch (error) {
      console.error('Key deletion failed:', error);
      return false;
    }
  }

  /**
   * 获取密钥元数据
   */
  async getKeyMetadata(keyId: string): Promise<{
    keyId: string;
    algorithm: string;
    createdAt: Date;
    lastUsedAt?: Date;
    status: 'active' | 'rotated' | 'deleted';
  } | null> {
    // 模拟返回密钥元数据
    return {
      keyId,
      algorithm: this.algorithm,
      createdAt: new Date(),
      lastUsedAt: new Date(),
      status: 'active',
    };
  }
}

// 导出单例实例
export const kmsService = new KMSService();
