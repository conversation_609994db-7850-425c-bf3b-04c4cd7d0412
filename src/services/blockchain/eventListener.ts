import { ethers } from 'ethers';
import { supabase } from '@/lib/supabase';
import { createSafeContractConfig, safeCreateContract } from '@/lib/contract-utils';

// Contract ABIs (simplified for event listening)
const HAOX_TOKEN_ABI = [
  "event Transfer(address indexed from, address indexed to, uint256 value)",
  "event TokensMinted(address indexed to, uint256 amount, string purpose)"
];

const HAOX_PRESALE_ABI = [
  "event TokensPurchased(address indexed buyer, uint256 bnbAmount, uint256 tokenAmount, uint256 currentRate, uint256 stage)",
  "event PresaleStarted(uint256 startTime, uint256 endTime)",
  "event PresaleEnded(uint256 totalBNBRaised, uint256 totalTokensSold)",
  "event WhitelistAdded(address indexed user)",
  "event StageAdvanced(uint256 newStage, uint256 newRate)"
];

const HAOX_INVITATION_ABI = [
  "event InvitationRecorded(address indexed inviter, address indexed invitee, uint256 investmentAmount, uint256 timestamp)",
  "event RewardCalculated(address indexed user, uint256 amount, string rewardType)",
  "event RewardClaimed(address indexed user, uint256 amount)",
  "event MilestoneReached(address indexed user, uint256 milestone, uint256 reward)"
];

const HAOX_PRICE_ORACLE_ABI = [
  "event PriceUpdated(uint256 indexed timestamp, uint256 chainlinkPrice, uint256 pancakeswapPrice, uint256 aggregatedPrice, uint256 confidence)"
];

const HAOX_VESTING_ABI = [
  "event UnlockTriggered(uint256 indexed cycle, uint256 amount, uint256 timestamp, uint256 triggerPrice)",
  "event PriceStabilityStarted(uint256 timestamp, uint256 price)"
];

// Contract addresses - 使用安全配置
const CONTRACT_ADDRESSES = createSafeContractConfig();

interface EventListenerConfig {
  rpcUrl: string;
  startBlock?: number;
  batchSize: number;
  pollInterval: number;
}

export class BlockchainEventListener {
  private provider: ethers.JsonRpcProvider;
  private contracts: Record<string, ethers.Contract>;
  private isListening: boolean = false;
  private lastProcessedBlock: number = 0;
  private config: EventListenerConfig;

  constructor(config: EventListenerConfig) {
    this.config = config;
    this.provider = new ethers.JsonRpcProvider(config.rpcUrl);
    this.contracts = this.initializeContracts();
  }

  private isValidAddress(address: string): boolean {
    return address &&
           address !== '' &&
           address !== '0x' &&
           address.length === 42 &&
           address.startsWith('0x') &&
           ethers.isAddress(address);
  }

  private initializeContracts(): Record<string, ethers.Contract> {
    const contracts: Record<string, ethers.Contract> = {};

    const tokenContract = safeCreateContract(CONTRACT_ADDRESSES.HAOX_TOKEN, HAOX_TOKEN_ABI, this.provider);
    if (tokenContract) contracts.token = tokenContract;

    const presaleContract = safeCreateContract(CONTRACT_ADDRESSES.HAOX_PRESALE, HAOX_PRESALE_ABI, this.provider);
    if (presaleContract) contracts.presale = presaleContract;

    const invitationContract = safeCreateContract(CONTRACT_ADDRESSES.HAOX_INVITATION, HAOX_INVITATION_ABI, this.provider);
    if (invitationContract) contracts.invitation = invitationContract;

    const priceOracleContract = safeCreateContract(CONTRACT_ADDRESSES.HAOX_PRICE_ORACLE, HAOX_PRICE_ORACLE_ABI, this.provider);
    if (priceOracleContract) contracts.priceOracle = priceOracleContract;

    const vestingContract = safeCreateContract(CONTRACT_ADDRESSES.HAOX_VESTING, HAOX_VESTING_ABI, this.provider);
    if (vestingContract) contracts.vesting = vestingContract;

    return contracts;
  }

  async start(): Promise<void> {
    if (this.isListening) {
      console.log('Event listener is already running');
      return;
    }

    console.log('🚀 Starting blockchain event listener...');
    
    // Get starting block
    if (!this.lastProcessedBlock) {
      this.lastProcessedBlock = this.config.startBlock || await this.provider.getBlockNumber() - 100;
    }

    this.isListening = true;
    this.setupEventListeners();
    this.startPolling();
    
    console.log(`✅ Event listener started from block ${this.lastProcessedBlock}`);
  }

  async stop(): Promise<void> {
    console.log('🛑 Stopping blockchain event listener...');
    this.isListening = false;
    
    // Remove all listeners
    Object.values(this.contracts).forEach(contract => {
      contract.removeAllListeners();
    });
    
    console.log('✅ Event listener stopped');
  }

  private setupEventListeners(): void {
    // Token events
    this.contracts.token.on('Transfer', this.handleTokenTransfer.bind(this));
    this.contracts.token.on('TokensMinted', this.handleTokensMinted.bind(this));

    // Presale events
    this.contracts.presale.on('TokensPurchased', this.handleTokensPurchased.bind(this));
    this.contracts.presale.on('PresaleStarted', this.handlePresaleStarted.bind(this));
    this.contracts.presale.on('PresaleEnded', this.handlePresaleEnded.bind(this));
    this.contracts.presale.on('WhitelistAdded', this.handleWhitelistAdded.bind(this));
    this.contracts.presale.on('StageAdvanced', this.handleStageAdvanced.bind(this));

    // Invitation events
    this.contracts.invitation.on('InvitationRecorded', this.handleInvitationRecorded.bind(this));
    this.contracts.invitation.on('RewardCalculated', this.handleRewardCalculated.bind(this));
    this.contracts.invitation.on('RewardClaimed', this.handleRewardClaimed.bind(this));
    this.contracts.invitation.on('MilestoneReached', this.handleMilestoneReached.bind(this));

    // Price oracle events
    this.contracts.priceOracle.on('PriceUpdated', this.handlePriceUpdated.bind(this));

    // Vesting events
    this.contracts.vesting.on('UnlockTriggered', this.handleUnlockTriggered.bind(this));
    this.contracts.vesting.on('PriceStabilityStarted', this.handlePriceStabilityStarted.bind(this));
  }

  private startPolling(): void {
    const poll = async () => {
      if (!this.isListening) return;

      try {
        await this.processHistoricalEvents();
      } catch (error) {
        console.error('Error processing events:', error);
      }

      setTimeout(poll, this.config.pollInterval);
    };

    poll();
  }

  private async processHistoricalEvents(): Promise<void> {
    const currentBlock = await this.provider.getBlockNumber();
    const fromBlock = this.lastProcessedBlock + 1;
    const toBlock = Math.min(fromBlock + this.config.batchSize - 1, currentBlock);

    if (fromBlock > currentBlock) return;

    console.log(`📊 Processing blocks ${fromBlock} to ${toBlock}`);

    // Process events for each contract
    for (const [contractName, contract] of Object.entries(this.contracts)) {
      try {
        const events = await contract.queryFilter('*', fromBlock, toBlock);
        
        for (const event of events) {
          await this.processEvent(contractName, event);
        }
      } catch (error) {
        console.error(`Error processing ${contractName} events:`, error);
      }
    }

    this.lastProcessedBlock = toBlock;
    
    // Save last processed block to database
    await this.saveLastProcessedBlock(toBlock);
  }

  private async processEvent(contractName: string, event: ethers.EventLog): Promise<void> {
    try {
      // Save raw event to database
      await this.saveEventToDatabase(contractName, event);
      
      // Process specific event types
      switch (event.eventName) {
        case 'TokensPurchased':
          await this.handleTokensPurchased(...event.args);
          break;
        case 'InvitationRecorded':
          await this.handleInvitationRecorded(...event.args);
          break;
        case 'RewardClaimed':
          await this.handleRewardClaimed(...event.args);
          break;
        // Add more event handlers as needed
      }
    } catch (error) {
      console.error(`Error processing event ${event.eventName}:`, error);
    }
  }

  // Event handlers
  private async handleTokenTransfer(from: string, to: string, value: bigint): Promise<void> {
    console.log(`💸 Token Transfer: ${ethers.formatEther(value)} HAOX from ${from} to ${to}`);
    
    // Update user balances in database
    await this.updateUserBalance(to);
    if (from !== ethers.ZeroAddress) {
      await this.updateUserBalance(from);
    }
  }

  private async handleTokensMinted(to: string, amount: bigint, purpose: string): Promise<void> {
    console.log(`🪙 Tokens Minted: ${ethers.formatEther(amount)} HAOX to ${to} for ${purpose}`);
    
    await this.updateUserBalance(to);
  }

  private async handleTokensPurchased(
    buyer: string, 
    bnbAmount: bigint, 
    tokenAmount: bigint, 
    currentRate: bigint, 
    stage: bigint
  ): Promise<void> {
    console.log(`🛒 Tokens Purchased: ${buyer} bought ${ethers.formatEther(tokenAmount)} HAOX for ${ethers.formatEther(bnbAmount)} BNB`);
    
    // Save presale investment record
    const { error } = await supabase
      .from('presale_investments')
      .insert({
        user_address: buyer.toLowerCase(),
        bnb_amount: ethers.formatEther(bnbAmount),
        token_amount: ethers.formatEther(tokenAmount),
        rate: currentRate.toString(),
        stage: Number(stage),
        transaction_hash: '', // Will be filled by the event processing
        block_number: 0, // Will be filled by the event processing
        created_at: new Date().toISOString()
      });

    if (error) {
      console.error('Error saving presale investment:', error);
    }

    // Update user balance
    await this.updateUserBalance(buyer);
    
    // Process invitation rewards if applicable
    await this.processInvitationReward(buyer, ethers.formatEther(bnbAmount));
  }

  private async handleInvitationRecorded(
    inviter: string, 
    invitee: string, 
    investmentAmount: bigint, 
    timestamp: bigint
  ): Promise<void> {
    console.log(`🤝 Invitation Recorded: ${inviter} invited ${invitee}`);
    
    // Update invitation relationship in database
    const { error } = await supabase
      .from('invitation_rewards_onchain')
      .insert({
        inviter_address: inviter.toLowerCase(),
        invitee_address: invitee.toLowerCase(),
        investment_amount: ethers.formatEther(investmentAmount),
        recorded_at: new Date(Number(timestamp) * 1000).toISOString(),
        status: 'recorded'
      });

    if (error) {
      console.error('Error saving invitation record:', error);
    }
  }

  private async handleRewardClaimed(user: string, amount: bigint): Promise<void> {
    console.log(`🎁 Reward Claimed: ${user} claimed ${ethers.formatEther(amount)} HAOX`);
    
    // Update reward claim in database
    const { error } = await supabase
      .from('invitation_rewards_onchain')
      .update({
        claimed_amount: ethers.formatEther(amount),
        claimed_at: new Date().toISOString(),
        status: 'claimed'
      })
      .eq('inviter_address', user.toLowerCase())
      .eq('status', 'pending');

    if (error) {
      console.error('Error updating reward claim:', error);
    }

    await this.updateUserBalance(user);
  }

  private async handleMilestoneReached(user: string, milestone: bigint, reward: bigint): Promise<void> {
    console.log(`🏆 Milestone Reached: ${user} reached ${milestone} invitations, reward: ${ethers.formatEther(reward)} HAOX`);
  }

  private async handlePriceUpdated(
    timestamp: bigint,
    chainlinkPrice: bigint,
    pancakeswapPrice: bigint,
    aggregatedPrice: bigint,
    confidence: bigint
  ): Promise<void> {
    console.log(`📈 Price Updated: $${ethers.formatUnits(aggregatedPrice, 8)} (confidence: ${confidence}%)`);
    
    // Save price history
    const { error } = await supabase
      .from('price_history')
      .insert({
        timestamp: new Date(Number(timestamp) * 1000).toISOString(),
        chainlink_price: ethers.formatUnits(chainlinkPrice, 8),
        pancakeswap_price: ethers.formatUnits(pancakeswapPrice, 8),
        aggregated_price: ethers.formatUnits(aggregatedPrice, 8),
        confidence: Number(confidence)
      });

    if (error) {
      console.error('Error saving price history:', error);
    }
  }

  private async handlePresaleStarted(startTime: bigint, endTime: bigint): Promise<void> {
    console.log(`🚀 Presale Started: ${new Date(Number(startTime) * 1000)} - ${new Date(Number(endTime) * 1000)}`);
  }

  private async handlePresaleEnded(totalBNBRaised: bigint, totalTokensSold: bigint): Promise<void> {
    console.log(`🏁 Presale Ended: ${ethers.formatEther(totalBNBRaised)} BNB raised, ${ethers.formatEther(totalTokensSold)} HAOX sold`);
  }

  private async handleWhitelistAdded(user: string): Promise<void> {
    console.log(`✅ Whitelist Added: ${user}`);
  }

  private async handleStageAdvanced(newStage: bigint, newRate: bigint): Promise<void> {
    console.log(`📊 Stage Advanced: Stage ${newStage}, Rate: ${newRate} HAOX per BNB`);
  }

  private async handleUnlockTriggered(cycle: bigint, amount: bigint, timestamp: bigint, triggerPrice: bigint): Promise<void> {
    console.log(`🔓 Unlock Triggered: Cycle ${cycle}, ${ethers.formatEther(amount)} HAOX unlocked`);
  }

  private async handlePriceStabilityStarted(timestamp: bigint, price: bigint): Promise<void> {
    console.log(`⏰ Price Stability Started: $${ethers.formatUnits(price, 8)} at ${new Date(Number(timestamp) * 1000)}`);
  }

  // Helper methods
  private async updateUserBalance(address: string): Promise<void> {
    try {
      const balance = await this.contracts.token.balanceOf(address);
      
      const { error } = await supabase
        .from('users')
        .update({
          haox_balance: ethers.formatEther(balance),
          balance_updated_at: new Date().toISOString()
        })
        .eq('wallet_address', address.toLowerCase());

      if (error) {
        console.error('Error updating user balance:', error);
      }
    } catch (error) {
      console.error('Error fetching balance:', error);
    }
  }

  private async processInvitationReward(buyer: string, bnbAmount: string): Promise<void> {
    // Check if buyer was invited
    const { data: invitation } = await supabase
      .from('invitations')
      .select('inviter_id')
      .eq('invitee_wallet_address', buyer.toLowerCase())
      .single();

    if (invitation) {
      // Trigger invitation reward processing
      console.log(`🎯 Processing invitation reward for ${buyer}`);
      // This would trigger the invitation contract to process the reward
    }
  }

  private async saveEventToDatabase(contractName: string, event: ethers.EventLog): Promise<void> {
    const { error } = await supabase
      .from('contract_events')
      .insert({
        contract_name: contractName,
        event_name: event.eventName,
        transaction_hash: event.transactionHash,
        block_number: event.blockNumber,
        log_index: event.index,
        event_data: JSON.stringify(event.args),
        created_at: new Date().toISOString()
      });

    if (error) {
      console.error('Error saving event to database:', error);
    }
  }

  private async saveLastProcessedBlock(blockNumber: number): Promise<void> {
    const { error } = await supabase
      .from('blockchain_sync_status')
      .upsert({
        id: 'haox_events',
        last_processed_block: blockNumber,
        updated_at: new Date().toISOString()
      });

    if (error) {
      console.error('Error saving last processed block:', error);
    }
  }

  async getLastProcessedBlock(): Promise<number> {
    const { data } = await supabase
      .from('blockchain_sync_status')
      .select('last_processed_block')
      .eq('id', 'haox_events')
      .single();

    return data?.last_processed_block || this.config.startBlock || 0;
  }

  // Health check method
  async healthCheck(): Promise<{ isHealthy: boolean; lastBlock: number; lag: number }> {
    try {
      const currentBlock = await this.provider.getBlockNumber();
      const lag = currentBlock - this.lastProcessedBlock;

      return {
        isHealthy: this.isListening && lag < 100, // Consider healthy if less than 100 blocks behind
        lastBlock: this.lastProcessedBlock,
        lag
      };
    } catch (error) {
      return {
        isHealthy: false,
        lastBlock: this.lastProcessedBlock,
        lag: -1
      };
    }
  }
}
