/**
 * 区块链服务管理器
 * 统一管理BSC网络连接和合约交互
 */

import { BSCProvider, BSCConfig, ContractConfig } from './BSCProvider';
import { HAOX_TOKEN_ABI, MULTISIG_WALLET_ABI, CONTRACT_ADDRESSES, NETWORK_CONFIG } from './contracts/HAOXABI';
import { AuditLogger } from '@/services/wallet/AuditLogger';
import { ethers } from 'ethers';

export interface GasFeeEstimate {
  slow: {
    gasPrice: string;
    estimatedTime: number;
    cost: string;
  };
  standard: {
    gasPrice: string;
    estimatedTime: number;
    cost: string;
  };
  fast: {
    gasPrice: string;
    estimatedTime: number;
    cost: string;
  };
}

export class BlockchainService {
  private bscProvider: BSCProvider;
  private auditLogger: AuditLogger;
  private isInitialized = false;

  constructor(auditLogger: AuditLogger) {
    this.auditLogger = auditLogger;
    
    // 根据环境配置网络
    const isMainnet = process.env.NEXT_PUBLIC_NETWORK === 'mainnet';
    const networkConfig = isMainnet ? NETWORK_CONFIG.BSC_MAINNET : NETWORK_CONFIG.BSC_TESTNET;
    const contractAddresses = isMainnet ? CONTRACT_ADDRESSES.BSC_MAINNET : CONTRACT_ADDRESSES.BSC_TESTNET;

    const bscConfig: BSCConfig = {
      rpcUrl: process.env.NEXT_PUBLIC_BSC_RPC_URL || networkConfig.rpcUrl,
      chainId: networkConfig.chainId,
      networkName: networkConfig.name,
      blockExplorer: networkConfig.blockExplorer,
      nativeCurrency: networkConfig.nativeCurrency,
    };

    const contractConfig: ContractConfig = {
      haoxToken: {
        address: contractAddresses.HAOX_TOKEN,
        abi: HAOX_TOKEN_ABI,
      },
      multiSig: {
        address: contractAddresses.MULTISIG_WALLET,
        abi: MULTISIG_WALLET_ABI,
      },
    };

    this.bscProvider = new BSCProvider(bscConfig, contractConfig, auditLogger);
  }

  /**
   * 初始化区块链服务
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      await this.bscProvider.initialize();
      this.isInitialized = true;

      await this.auditLogger.log({
        action: 'blockchain_service_initialized',
        actor: 'system',
        actorType: 'system',
        resource: 'blockchain_service',
        resourceId: 'main',
        details: {
          network: this.bscProvider.getConfig().networkName,
          chainId: this.bscProvider.getConfig().chainId,
        },
      });
    } catch (error) {
      await this.auditLogger.log({
        action: 'blockchain_service_init_failed',
        actor: 'system',
        actorType: 'system',
        resource: 'blockchain_service',
        resourceId: 'main',
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
      throw error;
    }
  }

  /**
   * 获取钱包余额信息
   */
  async getWalletBalances(address: string): Promise<{
    bnbBalance: string;
    haoxBalance: string;
    usdValue: string;
  }> {
    await this.ensureInitialized();

    try {
      const [bnbBalance, haoxBalance] = await Promise.all([
        this.bscProvider.getBNBBalance(address),
        this.bscProvider.getHAOXBalance(address),
      ]);

      // 简单的USD估值计算（实际应该从价格预言机获取）
      const bnbPrice = 300; // 假设BNB价格为$300
      const haoxPrice = 0.1; // 假设HAOX价格为$0.1
      
      const usdValue = (
        parseFloat(bnbBalance) * bnbPrice + 
        parseFloat(haoxBalance) * haoxPrice
      ).toFixed(2);

      return {
        bnbBalance,
        haoxBalance,
        usdValue,
      };
    } catch (error) {
      await this.auditLogger.log({
        action: 'wallet_balance_query_failed',
        actor: 'system',
        actorType: 'system',
        resource: 'wallet_balance',
        resourceId: address,
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
      throw error;
    }
  }

  /**
   * 估算Gas费用
   */
  async estimateGasFees(transaction: {
    to: string;
    value?: string;
    data?: string;
  }): Promise<GasFeeEstimate> {
    await this.ensureInitialized();

    try {
      const currentGasPrice = await this.bscProvider.getGasPrice();
      const gasLimit = await this.bscProvider.estimateGas({
        to: transaction.to,
        value: transaction.value ? ethers.parseEther(transaction.value) : undefined,
        data: transaction.data,
      });

      // 计算不同速度的Gas费用
      const slowGasPrice = currentGasPrice;
      const standardGasPrice = currentGasPrice * BigInt(120) / BigInt(100); // +20%
      const fastGasPrice = currentGasPrice * BigInt(150) / BigInt(100); // +50%

      const slowCost = ethers.formatEther(slowGasPrice * gasLimit);
      const standardCost = ethers.formatEther(standardGasPrice * gasLimit);
      const fastCost = ethers.formatEther(fastGasPrice * gasLimit);

      return {
        slow: {
          gasPrice: slowGasPrice.toString(),
          estimatedTime: 60, // 1分钟
          cost: slowCost,
        },
        standard: {
          gasPrice: standardGasPrice.toString(),
          estimatedTime: 30, // 30秒
          cost: standardCost,
        },
        fast: {
          gasPrice: fastGasPrice.toString(),
          estimatedTime: 15, // 15秒
          cost: fastCost,
        },
      };
    } catch (error) {
      await this.auditLogger.log({
        action: 'gas_fee_estimation_failed',
        actor: 'system',
        actorType: 'system',
        resource: 'gas_estimation',
        resourceId: transaction.to,
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
          transaction,
        },
      });
      throw error;
    }
  }

  /**
   * 发送HAOX代币
   */
  async sendHAOXToken(
    fromPrivateKey: string,
    toAddress: string,
    amount: string,
    gasPrice?: string
  ): Promise<string> {
    await this.ensureInitialized();

    try {
      const contracts = this.bscProvider.getContracts();
      const amountWei = ethers.parseEther(amount);
      
      // 构建转账数据
      const iface = new ethers.Interface(contracts.haoxToken.abi);
      const data = iface.encodeFunctionData('transfer', [toAddress, amountWei]);

      const txHash = await this.bscProvider.sendTransaction(fromPrivateKey, {
        to: contracts.haoxToken.address,
        data,
        gasPrice: gasPrice ? BigInt(gasPrice) : undefined,
      });

      await this.auditLogger.log({
        action: 'haox_token_sent',
        actor: 'system',
        actorType: 'system',
        resource: 'haox_transfer',
        resourceId: txHash,
        details: {
          toAddress,
          amount,
          tokenContract: contracts.haoxToken.address,
        },
      });

      return txHash;
    } catch (error) {
      await this.auditLogger.log({
        action: 'haox_token_send_failed',
        actor: 'system',
        actorType: 'system',
        resource: 'haox_transfer',
        resourceId: 'unknown',
        details: {
          toAddress,
          amount,
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
      throw error;
    }
  }

  /**
   * 发送BNB
   */
  async sendBNB(
    fromPrivateKey: string,
    toAddress: string,
    amount: string,
    gasPrice?: string
  ): Promise<string> {
    await this.ensureInitialized();

    try {
      const amountWei = ethers.parseEther(amount);
      
      const txHash = await this.bscProvider.sendTransaction(fromPrivateKey, {
        to: toAddress,
        value: amountWei,
        gasPrice: gasPrice ? BigInt(gasPrice) : undefined,
      });

      await this.auditLogger.log({
        action: 'bnb_sent',
        actor: 'system',
        actorType: 'system',
        resource: 'bnb_transfer',
        resourceId: txHash,
        details: {
          toAddress,
          amount,
        },
      });

      return txHash;
    } catch (error) {
      await this.auditLogger.log({
        action: 'bnb_send_failed',
        actor: 'system',
        actorType: 'system',
        resource: 'bnb_transfer',
        resourceId: 'unknown',
        details: {
          toAddress,
          amount,
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
      throw error;
    }
  }

  /**
   * 等待交易确认
   */
  async waitForTransactionConfirmation(
    txHash: string,
    confirmations: number = 1
  ): Promise<ethers.TransactionReceipt> {
    await this.ensureInitialized();
    return await this.bscProvider.waitForTransaction(txHash, confirmations);
  }

  /**
   * 获取交易状态
   */
  async getTransactionStatus(txHash: string): Promise<{
    status: 'pending' | 'confirmed' | 'failed' | 'not_found';
    blockNumber?: number;
    gasUsed?: string;
    confirmations?: number;
  }> {
    await this.ensureInitialized();

    try {
      const receipt = await this.bscProvider.getTransactionReceipt(txHash);
      
      if (!receipt) {
        // 检查交易是否在内存池中
        const tx = await this.bscProvider.getTransaction(txHash);
        if (tx) {
          return { status: 'pending' };
        } else {
          return { status: 'not_found' };
        }
      }

      const currentBlock = await this.bscProvider.getProvider().getBlockNumber();
      const confirmations = currentBlock - receipt.blockNumber + 1;

      return {
        status: receipt.status === 1 ? 'confirmed' : 'failed',
        blockNumber: receipt.blockNumber,
        gasUsed: receipt.gasUsed.toString(),
        confirmations,
      };
    } catch (error) {
      await this.auditLogger.log({
        action: 'transaction_status_query_failed',
        actor: 'system',
        actorType: 'system',
        resource: 'transaction_status',
        resourceId: txHash,
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
      throw error;
    }
  }

  /**
   * 获取BSC提供者
   */
  getBSCProvider(): BSCProvider {
    return this.bscProvider;
  }

  /**
   * 确保服务已初始化
   */
  private async ensureInitialized(): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }
  }
}
