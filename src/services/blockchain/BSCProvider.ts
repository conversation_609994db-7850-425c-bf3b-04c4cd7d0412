/**
 * BSC区块链提供者服务
 * 处理BSC网络连接和合约交互
 */

import { ethers } from 'ethers';
import { AuditLogger } from '@/services/wallet/AuditLogger';
import { safeCreateContract } from '@/lib/contract-utils';

export interface BSCConfig {
  rpcUrl: string;
  chainId: number;
  networkName: string;
  blockExplorer: string;
  nativeCurrency: {
    name: string;
    symbol: string;
    decimals: number;
  };
}

export interface ContractConfig {
  haoxToken: {
    address: string;
    abi: any[];
  };
  multiSig: {
    address: string;
    abi: any[];
  };
}

export class BSCProvider {
  private provider: ethers.JsonRpcProvider;
  private config: BSCConfig;
  private contracts: ContractConfig;
  private auditLogger: AuditLogger;

  constructor(config: BSCConfig, contracts: ContractConfig, auditLogger: AuditLogger) {
    this.config = config;
    this.contracts = contracts;
    this.auditLogger = auditLogger;
    this.provider = new ethers.JsonRpcProvider(config.rpcUrl);
  }

  /**
   * 初始化BSC提供者
   */
  async initialize(): Promise<void> {
    try {
      // 验证网络连接
      const network = await this.provider.getNetwork();
      if (Number(network.chainId) !== this.config.chainId) {
        throw new Error(`Network mismatch: expected ${this.config.chainId}, got ${network.chainId}`);
      }

      // 验证合约地址
      await this.validateContracts();

      await this.auditLogger.log({
        action: 'bsc_provider_initialized',
        actor: 'system',
        actorType: 'system',
        resource: 'bsc_provider',
        resourceId: 'main',
        details: {
          chainId: this.config.chainId,
          networkName: this.config.networkName,
          rpcUrl: this.config.rpcUrl,
        },
      });
    } catch (error) {
      await this.auditLogger.log({
        action: 'bsc_provider_init_failed',
        actor: 'system',
        actorType: 'system',
        resource: 'bsc_provider',
        resourceId: 'main',
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
      throw error;
    }
  }

  /**
   * 获取BNB余额
   */
  async getBNBBalance(address: string): Promise<string> {
    try {
      const balance = await this.provider.getBalance(address);
      return ethers.formatEther(balance);
    } catch (error) {
      await this.auditLogger.log({
        action: 'bnb_balance_query_failed',
        actor: 'system',
        actorType: 'system',
        resource: 'balance_query',
        resourceId: address,
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
      throw error;
    }
  }

  /**
   * 获取HAOX代币余额
   */
  private isValidAddress(address: string): boolean {
    return address &&
           address !== '' &&
           address !== '0x' &&
           address.length === 42 &&
           address.startsWith('0x') &&
           ethers.isAddress(address);
  }

  async getHAOXBalance(address: string): Promise<string> {
    try {
      const contract = safeCreateContract(
        this.contracts.haoxToken.address,
        this.contracts.haoxToken.abi,
        this.provider
      );

      if (!contract) {
        throw new Error('Invalid HAOX token contract address');
      }
      
      const balance = await contract.balanceOf(address);
      return ethers.formatEther(balance);
    } catch (error) {
      await this.auditLogger.log({
        action: 'haox_balance_query_failed',
        actor: 'system',
        actorType: 'system',
        resource: 'balance_query',
        resourceId: address,
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
      throw error;
    }
  }

  /**
   * 获取当前Gas价格
   */
  async getGasPrice(): Promise<bigint> {
    try {
      const feeData = await this.provider.getFeeData();
      return feeData.gasPrice || BigInt(**********); // 5 Gwei fallback
    } catch (error) {
      await this.auditLogger.log({
        action: 'gas_price_query_failed',
        actor: 'system',
        actorType: 'system',
        resource: 'gas_price',
        resourceId: 'current',
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
      return BigInt(**********); // 5 Gwei fallback
    }
  }

  /**
   * 估算Gas费用
   */
  async estimateGas(transaction: {
    to: string;
    data?: string;
    value?: bigint;
  }): Promise<bigint> {
    try {
      return await this.provider.estimateGas(transaction);
    } catch (error) {
      await this.auditLogger.log({
        action: 'gas_estimation_failed',
        actor: 'system',
        actorType: 'system',
        resource: 'gas_estimation',
        resourceId: transaction.to,
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
          transaction,
        },
      });
      throw error;
    }
  }

  /**
   * 发送交易
   */
  async sendTransaction(
    privateKey: string,
    transaction: {
      to: string;
      value?: bigint;
      data?: string;
      gasLimit?: bigint;
      gasPrice?: bigint;
    }
  ): Promise<string> {
    try {
      const wallet = new ethers.Wallet(privateKey, this.provider);
      
      // 获取nonce
      const nonce = await this.provider.getTransactionCount(wallet.address);
      
      // 设置默认gas参数
      const gasPrice = transaction.gasPrice || await this.getGasPrice();
      const gasLimit = transaction.gasLimit || await this.estimateGas({
        to: transaction.to,
        data: transaction.data,
        value: transaction.value,
      });

      const tx = await wallet.sendTransaction({
        ...transaction,
        nonce,
        gasPrice,
        gasLimit,
      });

      await this.auditLogger.log({
        action: 'transaction_sent',
        actor: wallet.address,
        actorType: 'system',
        resource: 'transaction',
        resourceId: tx.hash,
        details: {
          to: transaction.to,
          value: transaction.value?.toString(),
          gasPrice: gasPrice.toString(),
          gasLimit: gasLimit.toString(),
          nonce,
        },
      });

      return tx.hash;
    } catch (error) {
      await this.auditLogger.log({
        action: 'transaction_send_failed',
        actor: 'system',
        actorType: 'system',
        resource: 'transaction',
        resourceId: 'unknown',
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
          transaction,
        },
      });
      throw error;
    }
  }

  /**
   * 等待交易确认
   */
  async waitForTransaction(txHash: string, confirmations: number = 1): Promise<ethers.TransactionReceipt> {
    try {
      const receipt = await this.provider.waitForTransaction(txHash, confirmations);
      if (!receipt) {
        throw new Error('Transaction receipt not found');
      }

      await this.auditLogger.log({
        action: 'transaction_confirmed',
        actor: 'system',
        actorType: 'system',
        resource: 'transaction',
        resourceId: txHash,
        details: {
          blockNumber: receipt.blockNumber,
          gasUsed: receipt.gasUsed.toString(),
          status: receipt.status,
          confirmations,
        },
      });

      return receipt;
    } catch (error) {
      await this.auditLogger.log({
        action: 'transaction_confirmation_failed',
        actor: 'system',
        actorType: 'system',
        resource: 'transaction',
        resourceId: txHash,
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
          confirmations,
        },
      });
      throw error;
    }
  }

  /**
   * 获取交易详情
   */
  async getTransaction(txHash: string): Promise<ethers.TransactionResponse | null> {
    try {
      return await this.provider.getTransaction(txHash);
    } catch (error) {
      await this.auditLogger.log({
        action: 'transaction_query_failed',
        actor: 'system',
        actorType: 'system',
        resource: 'transaction',
        resourceId: txHash,
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
      return null;
    }
  }

  /**
   * 获取交易收据
   */
  async getTransactionReceipt(txHash: string): Promise<ethers.TransactionReceipt | null> {
    try {
      return await this.provider.getTransactionReceipt(txHash);
    } catch (error) {
      await this.auditLogger.log({
        action: 'transaction_receipt_query_failed',
        actor: 'system',
        actorType: 'system',
        resource: 'transaction_receipt',
        resourceId: txHash,
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
      return null;
    }
  }

  /**
   * 验证合约地址
   */
  private async validateContracts(): Promise<void> {
    // 验证HAOX代币合约
    const haoxCode = await this.provider.getCode(this.contracts.haoxToken.address);
    if (haoxCode === '0x') {
      throw new Error(`HAOX token contract not found at ${this.contracts.haoxToken.address}`);
    }

    // 验证多重签名合约
    const multiSigCode = await this.provider.getCode(this.contracts.multiSig.address);
    if (multiSigCode === '0x') {
      throw new Error(`MultiSig contract not found at ${this.contracts.multiSig.address}`);
    }
  }

  /**
   * 获取提供者实例
   */
  getProvider(): ethers.JsonRpcProvider {
    return this.provider;
  }

  /**
   * 获取网络配置
   */
  getConfig(): BSCConfig {
    return this.config;
  }

  /**
   * 获取合约配置
   */
  getContracts(): ContractConfig {
    return this.contracts;
  }
}
