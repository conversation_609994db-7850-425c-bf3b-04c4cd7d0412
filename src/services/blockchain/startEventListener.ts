#!/usr/bin/env node

import { BlockchainEventListener } from './eventListener';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Configuration
const config = {
  rpcUrl: process.env.BSC_RPC_URL || 'https://bsc-dataseed1.binance.org/',
  startBlock: parseInt(process.env.START_BLOCK || '0'),
  batchSize: parseInt(process.env.BATCH_SIZE || '100'),
  pollInterval: parseInt(process.env.POLL_INTERVAL || '5000'), // 5 seconds
};

// Global event listener instance
let eventListener: BlockchainEventListener | null = null;

async function startEventListener(): Promise<void> {
  console.log('🔗 Initializing HAOX Blockchain Event Listener...');
  console.log('Configuration:', {
    rpcUrl: config.rpcUrl,
    startBlock: config.startBlock,
    batchSize: config.batchSize,
    pollInterval: config.pollInterval,
  });

  try {
    // Create and start event listener
    eventListener = new BlockchainEventListener(config);
    
    // Get last processed block from database
    const lastProcessedBlock = await eventListener.getLastProcessedBlock();
    console.log(`📊 Last processed block: ${lastProcessedBlock}`);
    
    // Start listening
    await eventListener.start();
    
    console.log('✅ Event listener started successfully');
    
    // Set up health check interval
    setInterval(async () => {
      if (eventListener) {
        const health = await eventListener.healthCheck();
        console.log(`💓 Health Check - Healthy: ${health.isHealthy}, Last Block: ${health.lastBlock}, Lag: ${health.lag} blocks`);
        
        if (!health.isHealthy) {
          console.warn('⚠️ Event listener is unhealthy!');
        }
      }
    }, 60000); // Every minute
    
  } catch (error) {
    console.error('❌ Failed to start event listener:', error);
    process.exit(1);
  }
}

async function stopEventListener(): Promise<void> {
  console.log('🛑 Stopping event listener...');
  
  if (eventListener) {
    await eventListener.stop();
    eventListener = null;
  }
  
  console.log('✅ Event listener stopped');
  process.exit(0);
}

// Handle graceful shutdown
process.on('SIGINT', stopEventListener);
process.on('SIGTERM', stopEventListener);
process.on('SIGQUIT', stopEventListener);

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('💥 Uncaught Exception:', error);
  stopEventListener();
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('💥 Unhandled Rejection at:', promise, 'reason:', reason);
  stopEventListener();
});

// Start the service
if (require.main === module) {
  startEventListener().catch((error) => {
    console.error('💥 Failed to start event listener service:', error);
    process.exit(1);
  });
}

export { startEventListener, stopEventListener };
