/**
 * Telegram认证服务
 * 处理Telegram OAuth验证和用户管理
 */

import crypto from 'crypto';
import { TelegramUser } from '@/services/wallet/types';

export interface TelegramAuthConfig {
  botToken: string;
  botUsername: string;
  authTimeout: number; // 认证超时时间（秒）
  tokenExpiry: number; // Token过期时间（秒）
}

export class TelegramAuthService {
  private config: TelegramAuthConfig;

  constructor() {
    this.config = {
      botToken: process.env.TELEGRAM_BOT_TOKEN || '',
      botUsername: process.env.NEXT_PUBLIC_TELEGRAM_BOT_USERNAME || '',
      authTimeout: 86400, // 24小时
      tokenExpiry: 7 * 24 * 3600, // 7天
    };

    if (!this.config.botToken) {
      throw new Error('TELEGRAM_BOT_TOKEN environment variable is required');
    }
  }

  /**
   * 验证Telegram认证数据
   */
  async validateTelegramAuth(authData: any): Promise<TelegramUser> {
    try {
      // 验证数据完整性
      this.validateAuthDataStructure(authData);
      
      // 验证哈希签名
      const isValidHash = this.verifyTelegramHash(authData);
      if (!isValidHash) {
        throw new Error('Invalid Telegram authentication hash');
      }

      // 验证认证时间
      const authDate = new Date(authData.auth_date * 1000);
      const now = new Date();
      const timeDiff = (now.getTime() - authDate.getTime()) / 1000;
      
      if (timeDiff > this.config.authTimeout) {
        throw new Error('Telegram authentication has expired');
      }

      // 构造用户对象
      const telegramUser: TelegramUser = {
        id: authData.id,
        username: authData.username,
        first_name: authData.first_name,
        last_name: authData.last_name,
        photo_url: authData.photo_url,
        auth_date: authData.auth_date,
        hash: authData.hash,
      };

      // 记录用户登录
      await this.recordUserLogin(telegramUser);

      return telegramUser;
    } catch (error) {
      console.error('Telegram auth validation failed:', error);
      throw error;
    }
  }

  /**
   * 生成认证Token
   */
  async generateAuthToken(user: TelegramUser): Promise<string> {
    const payload = {
      userId: user.id,
      username: user.username,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + this.config.tokenExpiry,
    };

    // 使用简单的JWT实现（生产环境建议使用专业的JWT库）
    const header = Buffer.from(JSON.stringify({ alg: 'HS256', typ: 'JWT' })).toString('base64url');
    const payloadStr = Buffer.from(JSON.stringify(payload)).toString('base64url');
    
    const signature = crypto
      .createHmac('sha256', this.config.botToken)
      .update(`${header}.${payloadStr}`)
      .digest('base64url');

    return `${header}.${payloadStr}.${signature}`;
  }

  /**
   * 验证Token
   */
  async validateToken(token: string): Promise<boolean> {
    try {
      const parts = token.split('.');
      if (parts.length !== 3) {
        return false;
      }

      const [header, payload, signature] = parts;
      
      // 验证签名
      const expectedSignature = crypto
        .createHmac('sha256', this.config.botToken)
        .update(`${header}.${payload}`)
        .digest('base64url');

      if (signature !== expectedSignature) {
        return false;
      }

      // 验证过期时间
      const payloadData = JSON.parse(Buffer.from(payload, 'base64url').toString());
      const now = Math.floor(Date.now() / 1000);
      
      if (payloadData.exp < now) {
        return false;
      }

      return true;
    } catch (error) {
      console.error('Token validation failed:', error);
      return false;
    }
  }

  /**
   * 获取用户信息
   */
  async getUserInfo(userId: number, token: string): Promise<TelegramUser> {
    // 验证token
    const isValidToken = await this.validateToken(token);
    if (!isValidToken) {
      throw new Error('Invalid or expired token');
    }

    try {
      // 从Telegram API获取用户信息
      const response = await fetch(`https://api.telegram.org/bot${this.config.botToken}/getChat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          chat_id: userId,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to fetch user info from Telegram');
      }

      const data = await response.json();
      
      if (!data.ok) {
        throw new Error(data.description || 'Telegram API error');
      }

      const chat = data.result;
      
      return {
        id: chat.id,
        username: chat.username,
        first_name: chat.first_name,
        last_name: chat.last_name,
        photo_url: undefined, // 需要单独获取头像
        auth_date: Math.floor(Date.now() / 1000),
        hash: '', // 重新生成的用户信息不包含原始hash
      };
    } catch (error) {
      console.error('Failed to get user info:', error);
      throw error;
    }
  }

  /**
   * 发送验证码到Telegram
   */
  async sendVerificationCode(userId: number, code: string): Promise<void> {
    try {
      const message = `🔐 您的SocioMint验证码是：\n\n**${code}**\n\n此验证码5分钟内有效，请勿泄露给他人。`;
      
      const response = await fetch(`https://api.telegram.org/bot${this.config.botToken}/sendMessage`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          chat_id: userId,
          text: message,
          parse_mode: 'Markdown',
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to send verification code');
      }

      const data = await response.json();
      
      if (!data.ok) {
        throw new Error(data.description || 'Failed to send message');
      }
    } catch (error) {
      console.error('Failed to send verification code:', error);
      throw error;
    }
  }

  /**
   * 生成验证码
   */
  generateVerificationCode(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  /**
   * 验证Telegram哈希
   */
  private verifyTelegramHash(authData: any): boolean {
    const { hash, ...dataToCheck } = authData;
    
    // 按字母顺序排序参数
    const dataCheckString = Object.keys(dataToCheck)
      .sort()
      .map(key => `${key}=${dataToCheck[key]}`)
      .join('\n');

    // 生成密钥
    const secretKey = crypto
      .createHmac('sha256', 'WebAppData')
      .update(this.config.botToken)
      .digest();

    // 计算哈希
    const calculatedHash = crypto
      .createHmac('sha256', secretKey)
      .update(dataCheckString)
      .digest('hex');

    return calculatedHash === hash;
  }

  /**
   * 验证认证数据结构
   */
  private validateAuthDataStructure(authData: any): void {
    const requiredFields = ['id', 'first_name', 'auth_date', 'hash'];
    
    for (const field of requiredFields) {
      if (!(field in authData)) {
        throw new Error(`Missing required field: ${field}`);
      }
    }

    // 验证数据类型
    if (typeof authData.id !== 'number') {
      throw new Error('Invalid user ID format');
    }

    if (typeof authData.auth_date !== 'number') {
      throw new Error('Invalid auth_date format');
    }

    if (typeof authData.first_name !== 'string' || authData.first_name.trim() === '') {
      throw new Error('Invalid first_name format');
    }
  }

  /**
   * 记录用户登录
   */
  private async recordUserLogin(user: TelegramUser): Promise<void> {
    try {
      // 这里可以记录到数据库或日志系统
      console.log(`User login recorded: ${user.id} (${user.first_name})`);
      
      // 可以发送到分析系统
      // await analytics.track('user_login', {
      //   userId: user.id,
      //   username: user.username,
      //   timestamp: new Date(),
      // });
    } catch (error) {
      console.error('Failed to record user login:', error);
      // 不抛出错误，因为这不应该影响登录流程
    }
  }
}
