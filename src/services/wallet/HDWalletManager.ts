/**
 * 分层确定性钱包管理器
 * 负责生成和管理用户钱包地址
 */

import { ethers } from 'ethers';
import { HDWalletConfig, WalletAddress, TelegramUser } from './types';
import { AuditLogger } from './AuditLogger';
import { kmsService } from '@/services/security/KMSService';

export class HDWalletManager {
  private auditLogger: AuditLogger;
  private config: HDWalletConfig;
  private masterWallet: ethers.HDNodeWallet | null = null;
  private kmsService = kmsService;

  constructor(
    config: HDWalletConfig,
    auditLogger: AuditLogger
  ) {
    this.config = config;
    this.auditLogger = auditLogger;
  }

  /**
   * 初始化HD钱包管理器
   */
  async initialize(): Promise<void> {
    try {
      // 生成一个固定的主种子用于开发环境
      // 在生产环境中，这应该从安全的密钥管理服务获取
      const masterSeed = ethers.randomBytes(32);

      // 创建HD钱包
      this.masterWallet = ethers.HDNodeWallet.fromSeed(masterSeed);

      await this.auditLogger.log({
        action: 'hd_wallet_initialized',
        actor: 'system',
        actorType: 'system',
        resource: 'hd_wallet',
        resourceId: 'master',
        details: {
          derivationBasePath: this.config.derivationBasePath,
          mock: true,
        },
      });
    } catch (error) {
      await this.auditLogger.log({
        action: 'hd_wallet_init_failed',
        actor: 'system',
        actorType: 'system',
        resource: 'hd_wallet',
        resourceId: 'master',
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
      throw error;
    }
  }

  /**
   * 为Telegram用户生成钱包地址
   */
  async generateWalletForUser(telegramUser: TelegramUser): Promise<WalletAddress> {
    if (!this.masterWallet) {
      throw new Error('HD Wallet not initialized');
    }

    try {
      // 基于Telegram用户ID生成确定性路径
      const derivationPath = this.getUserDerivationPath(telegramUser.id);
      
      // 生成子钱包
      const childWallet = this.masterWallet.derivePath(derivationPath);
      
      // 创建钱包地址记录
      const walletAddress: WalletAddress = {
        id: this.generateWalletId(),
        telegramUserId: telegramUser.id,
        address: childWallet.address,
        derivationPath,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // 记录审计日志
      await this.auditLogger.log({
        action: 'wallet_generated',
        actor: telegramUser.id.toString(),
        actorType: 'user',
        resource: 'wallet',
        resourceId: walletAddress.id,
        details: {
          address: walletAddress.address,
          derivationPath: walletAddress.derivationPath,
          telegramUsername: telegramUser.username,
        },
      });

      return walletAddress;
    } catch (error) {
      await this.auditLogger.log({
        action: 'wallet_generation_failed',
        actor: telegramUser.id.toString(),
        actorType: 'user',
        resource: 'wallet',
        resourceId: 'unknown',
        details: {
          telegramUserId: telegramUser.id,
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
      throw error;
    }
  }

  /**
   * 获取用户钱包的私钥（仅用于交易签名）
   */
  async getWalletPrivateKey(telegramUserId: number): Promise<string> {
    if (!this.masterWallet) {
      throw new Error('HD Wallet not initialized');
    }

    try {
      const derivationPath = this.getUserDerivationPath(telegramUserId);
      const childWallet = this.masterWallet.derivePath(derivationPath);
      
      // 记录私钥访问审计日志
      await this.auditLogger.log({
        action: 'private_key_accessed',
        actor: 'system',
        actorType: 'system',
        resource: 'private_key',
        resourceId: telegramUserId.toString(),
        details: {
          derivationPath,
          purpose: 'transaction_signing',
        },
      });

      return childWallet.privateKey;
    } catch (error) {
      await this.auditLogger.log({
        action: 'private_key_access_failed',
        actor: 'system',
        actorType: 'system',
        resource: 'private_key',
        resourceId: telegramUserId.toString(),
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
      throw error;
    }
  }

  /**
   * 验证钱包地址是否属于指定用户
   */
  async verifyWalletOwnership(
    telegramUserId: number,
    address: string
  ): Promise<boolean> {
    if (!this.masterWallet) {
      throw new Error('HD Wallet not initialized');
    }

    try {
      const derivationPath = this.getUserDerivationPath(telegramUserId);
      const childWallet = this.masterWallet.derivePath(derivationPath);
      
      const isValid = childWallet.address.toLowerCase() === address.toLowerCase();
      
      await this.auditLogger.log({
        action: 'wallet_ownership_verified',
        actor: telegramUserId.toString(),
        actorType: 'user',
        resource: 'wallet',
        resourceId: address,
        details: {
          isValid,
          derivationPath,
        },
      });

      return isValid;
    } catch (error) {
      await this.auditLogger.log({
        action: 'wallet_verification_failed',
        actor: telegramUserId.toString(),
        actorType: 'user',
        resource: 'wallet',
        resourceId: address,
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
      return false;
    }
  }

  /**
   * 生成平台热钱包地址
   */
  async generateHotWallet(): Promise<WalletAddress> {
    if (!this.masterWallet) {
      throw new Error('HD Wallet not initialized');
    }

    const derivationPath = `${this.config.derivationBasePath}/0`;
    const hotWallet = this.masterWallet.derivePath(derivationPath);
    
    const walletAddress: WalletAddress = {
      id: this.generateWalletId(),
      telegramUserId: 0, // 系统钱包
      address: hotWallet.address,
      derivationPath,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    await this.auditLogger.log({
      action: 'hot_wallet_generated',
      actor: 'system',
      actorType: 'system',
      resource: 'hot_wallet',
      resourceId: walletAddress.id,
      details: {
        address: walletAddress.address,
        derivationPath: walletAddress.derivationPath,
      },
    });

    return walletAddress;
  }

  /**
   * 生成平台冷钱包地址
   */
  async generateColdWallet(): Promise<WalletAddress> {
    if (!this.masterWallet) {
      throw new Error('HD Wallet not initialized');
    }

    const derivationPath = `${this.config.derivationBasePath}/1`;
    const coldWallet = this.masterWallet.derivePath(derivationPath);
    
    const walletAddress: WalletAddress = {
      id: this.generateWalletId(),
      telegramUserId: -1, // 冷钱包标识
      address: coldWallet.address,
      derivationPath,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    await this.auditLogger.log({
      action: 'cold_wallet_generated',
      actor: 'system',
      actorType: 'system',
      resource: 'cold_wallet',
      resourceId: walletAddress.id,
      details: {
        address: walletAddress.address,
        derivationPath: walletAddress.derivationPath,
      },
    });

    return walletAddress;
  }

  /**
   * 备份钱包数据
   */
  async backupWalletData(): Promise<void> {
    if (!this.masterWallet) {
      throw new Error('HD Wallet not initialized');
    }

    try {
      // 加密主种子
      const encryptedSeed = await this.kmsService.encryptData(
        this.masterWallet.mnemonic?.phrase || ''
      );

      // 备份到多个位置
      for (const location of this.config.backupLocations) {
        await this.backupToLocation(location, encryptedSeed);
      }

      await this.auditLogger.log({
        action: 'wallet_backup_completed',
        actor: 'system',
        actorType: 'system',
        resource: 'wallet_backup',
        resourceId: 'master',
        details: {
          backupLocations: this.config.backupLocations.length,
          timestamp: new Date().toISOString(),
        },
      });
    } catch (error) {
      await this.auditLogger.log({
        action: 'wallet_backup_failed',
        actor: 'system',
        actorType: 'system',
        resource: 'wallet_backup',
        resourceId: 'master',
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
      throw error;
    }
  }

  /**
   * 从备份恢复钱包
   */
  async restoreFromBackup(backupLocation: string): Promise<void> {
    try {
      const encryptedSeed = await this.restoreFromLocation(backupLocation);
      const decryptedSeed = await this.kmsService.decryptData(encryptedSeed);
      
      this.masterWallet = ethers.HDNodeWallet.fromMnemonic(
        ethers.Mnemonic.fromPhrase(decryptedSeed)
      );

      await this.auditLogger.log({
        action: 'wallet_restored_from_backup',
        actor: 'system',
        actorType: 'system',
        resource: 'wallet_restore',
        resourceId: 'master',
        details: {
          backupLocation,
          timestamp: new Date().toISOString(),
        },
      });
    } catch (error) {
      await this.auditLogger.log({
        action: 'wallet_restore_failed',
        actor: 'system',
        actorType: 'system',
        resource: 'wallet_restore',
        resourceId: 'master',
        details: {
          backupLocation,
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
      throw error;
    }
  }

  /**
   * 生成用户的派生路径
   */
  private getUserDerivationPath(telegramUserId: number): string {
    // 确保用户ID从2开始（0和1保留给平台钱包）
    const userIndex = telegramUserId + 2;
    return `${this.config.derivationBasePath}/${userIndex}`;
  }

  /**
   * 生成唯一的钱包ID
   */
  private generateWalletId(): string {
    return `wallet_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 备份到指定位置
   */
  private async backupToLocation(location: string, data: string): Promise<void> {
    // 实现具体的备份逻辑（AWS S3、本地文件系统等）
    // 这里是示例实现
    console.log(`Backing up to ${location}`);
  }

  /**
   * 从指定位置恢复
   */
  private async restoreFromLocation(location: string): Promise<string> {
    // 实现具体的恢复逻辑
    // 这里是示例实现
    console.log(`Restoring from ${location}`);
    return '';
  }
}
