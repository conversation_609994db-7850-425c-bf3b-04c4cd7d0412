/**
 * 多重签名管理器
 * 实现2/3多重签名机制
 */

import { ethers } from 'ethers';
import {
  MultiSigTransaction,
  MultiSigSignature,
  Transaction,
  WalletConfig
} from './types';
import { AuditLogger } from './AuditLogger';
import { BlockchainService } from '@/services/blockchain/BlockchainService';
import { kmsService } from '@/services/security/KMSService';

export interface MultiSigConfig {
  requiredSignatures: number;
  totalSigners: number;
  platformKey1Id: string;
  platformKey2Id: string;
  contractAddress: string;
}

export class MultiSigManager {
  private config: MultiSigConfig;
  private walletConfig: WalletConfig;
  private auditLogger: AuditLogger;
  private blockchainService: BlockchainService;
  private provider: ethers.JsonRpcProvider;
  private multiSigContract: ethers.Contract;
  private kmsService = kmsService;

  private isValidAddress(address: string): boolean {
    return address &&
           address !== '' &&
           address !== '0x' &&
           address.length === 42 &&
           address.startsWith('0x') &&
           ethers.isAddress(address);
  }

  constructor(
    config: MultiSigConfig,
    walletConfig: WalletConfig,
    auditLogger: AuditLogger,
    blockchainService: BlockchainService
  ) {
    this.config = config;
    this.walletConfig = walletConfig;
    this.auditLogger = auditLogger;
    this.blockchainService = blockchainService;
    this.provider = new ethers.JsonRpcProvider(walletConfig.rpcUrl);
    
    // 多重签名合约ABI（简化版）
    const multiSigABI = [
      "function submitTransaction(address to, uint256 value, bytes data) returns (uint256)",
      "function confirmTransaction(uint256 transactionId)",
      "function executeTransaction(uint256 transactionId)",
      "function getTransaction(uint256 transactionId) view returns (address, uint256, bytes, bool, uint256)",
      "function getConfirmationCount(uint256 transactionId) view returns (uint256)",
      "function isConfirmed(uint256 transactionId) view returns (bool)",
      "event Submission(uint256 indexed transactionId)",
      "event Confirmation(address indexed sender, uint256 indexed transactionId)",
      "event Execution(uint256 indexed transactionId)"
    ];

    // 验证合约地址 - 如果地址无效，使用默认地址
    if (!this.isValidAddress(config.contractAddress)) {
      console.warn('Invalid MultiSig contract address, using default:', config.contractAddress);
      config.contractAddress = '******************************************'; // 使用项目钱包地址作为默认值
    }

    this.multiSigContract = new ethers.Contract(
      config.contractAddress,
      multiSigABI,
      this.provider
    );
  }

  /**
   * 创建多重签名交易
   */
  async createMultiSigTransaction(
    transaction: Transaction,
    telegramUserId: number
  ): Promise<MultiSigTransaction> {
    try {
      const multiSigTx: MultiSigTransaction = {
        id: this.generateMultiSigId(),
        transactionId: transaction.id,
        requiredSignatures: this.getRequiredSignatures(transaction),
        signatures: [],
        status: 'pending',
        createdAt: new Date(),
      };

      await this.auditLogger.logTransaction(
        'transaction_created',
        transaction.id,
        {
          multiSigId: multiSigTx.id,
          requiredSignatures: multiSigTx.requiredSignatures,
          amount: transaction.amount,
          tokenType: transaction.tokenType,
          telegramUserId,
        }
      );

      return multiSigTx;
    } catch (error) {
      await this.auditLogger.logTransaction(
        'transaction_failed',
        transaction.id,
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          telegramUserId,
        }
      );
      throw error;
    }
  }

  /**
   * 使用平台密钥1签名（自动化交易）
   */
  async signWithPlatformKey1(
    multiSigTx: MultiSigTransaction,
    transaction: Transaction
  ): Promise<MultiSigSignature> {
    try {
      // 从KMS获取平台密钥1
      const privateKey = await this.kmsService.decryptData(this.config.platformKey1Id);
      const wallet = new ethers.Wallet(privateKey, this.provider);

      // 生成交易数据
      const txData = await this.buildTransactionData(transaction);
      
      // 签名交易
      const signature = await wallet.signMessage(txData);

      const multiSigSignature: MultiSigSignature = {
        id: this.generateSignatureId(),
        signerId: 'platform_key_1',
        signerType: 'platform_key_1',
        signature,
        signedAt: new Date(),
      };

      await this.auditLogger.log({
        action: 'multisig_signed_platform_key_1',
        actor: 'system',
        actorType: 'system',
        resource: 'multisig_transaction',
        resourceId: multiSigTx.id,
        details: {
          transactionId: transaction.id,
          signatureId: multiSigSignature.id,
        },
      });

      return multiSigSignature;
    } catch (error) {
      await this.auditLogger.log({
        action: 'multisig_signing_failed',
        actor: 'system',
        actorType: 'system',
        resource: 'multisig_transaction',
        resourceId: multiSigTx.id,
        details: {
          signerType: 'platform_key_1',
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
      throw error;
    }
  }

  /**
   * 使用平台密钥2签名（人工审核）
   */
  async signWithPlatformKey2(
    multiSigTx: MultiSigTransaction,
    transaction: Transaction,
    adminUserId: string
  ): Promise<MultiSigSignature> {
    try {
      // 验证管理员权限
      await this.verifyAdminPermission(adminUserId);

      // 从安全存储获取平台密钥2
      const privateKey = await this.kmsService.decryptData(this.config.platformKey2Id);
      const wallet = new ethers.Wallet(privateKey, this.provider);

      // 生成交易数据
      const txData = await this.buildTransactionData(transaction);
      
      // 签名交易
      const signature = await wallet.signMessage(txData);

      const multiSigSignature: MultiSigSignature = {
        id: this.generateSignatureId(),
        signerId: adminUserId,
        signerType: 'platform_key_2',
        signature,
        signedAt: new Date(),
      };

      await this.auditLogger.log({
        action: 'multisig_signed_platform_key_2',
        actor: adminUserId,
        actorType: 'admin',
        resource: 'multisig_transaction',
        resourceId: multiSigTx.id,
        details: {
          transactionId: transaction.id,
          signatureId: multiSigSignature.id,
          adminApproval: true,
        },
      });

      return multiSigSignature;
    } catch (error) {
      await this.auditLogger.log({
        action: 'multisig_signing_failed',
        actor: adminUserId,
        actorType: 'admin',
        resource: 'multisig_transaction',
        resourceId: multiSigTx.id,
        details: {
          signerType: 'platform_key_2',
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
      throw error;
    }
  }

  /**
   * 用户验证签名
   */
  async signWithUserVerification(
    multiSigTx: MultiSigTransaction,
    transaction: Transaction,
    telegramUserId: number,
    verificationCode: string
  ): Promise<MultiSigSignature> {
    try {
      // 验证Telegram验证码
      await this.verifyTelegramCode(telegramUserId, verificationCode);

      // 生成用户验证签名
      const verificationData = this.generateUserVerificationData(
        transaction,
        telegramUserId,
        verificationCode
      );
      
      const signature = ethers.keccak256(ethers.toUtf8Bytes(verificationData));

      const multiSigSignature: MultiSigSignature = {
        id: this.generateSignatureId(),
        signerId: telegramUserId.toString(),
        signerType: 'user_verification',
        signature,
        signedAt: new Date(),
      };

      await this.auditLogger.log({
        action: 'multisig_signed_user_verification',
        actor: telegramUserId.toString(),
        actorType: 'user',
        resource: 'multisig_transaction',
        resourceId: multiSigTx.id,
        details: {
          transactionId: transaction.id,
          signatureId: multiSigSignature.id,
          verificationMethod: 'telegram_code',
        },
      });

      return multiSigSignature;
    } catch (error) {
      await this.auditLogger.log({
        action: 'multisig_signing_failed',
        actor: telegramUserId.toString(),
        actorType: 'user',
        resource: 'multisig_transaction',
        resourceId: multiSigTx.id,
        details: {
          signerType: 'user_verification',
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
      throw error;
    }
  }

  /**
   * 执行多重签名交易
   */
  async executeMultiSigTransaction(
    multiSigTx: MultiSigTransaction,
    transaction: Transaction
  ): Promise<string> {
    try {
      // 验证签名数量
      if (multiSigTx.signatures.length < multiSigTx.requiredSignatures) {
        throw new Error('Insufficient signatures');
      }

      // 验证签名有效性
      await this.validateSignatures(multiSigTx, transaction);

      // 执行区块链交易
      const txHash = await this.executeOnChain(transaction);

      // 更新多重签名交易状态
      multiSigTx.status = 'executed';
      multiSigTx.executedAt = new Date();

      await this.auditLogger.logTransaction(
        'transaction_confirmed',
        transaction.id,
        {
          multiSigId: multiSigTx.id,
          txHash,
          signaturesUsed: multiSigTx.signatures.length,
          executedAt: multiSigTx.executedAt,
        }
      );

      return txHash;
    } catch (error) {
      multiSigTx.status = 'rejected';
      
      await this.auditLogger.logTransaction(
        'transaction_failed',
        transaction.id,
        {
          multiSigId: multiSigTx.id,
          error: error instanceof Error ? error.message : 'Unknown error',
        }
      );
      throw error;
    }
  }

  /**
   * 根据交易金额确定所需签名数量
   */
  private getRequiredSignatures(transaction: Transaction): number {
    const amount = parseFloat(transaction.amount);
    
    if (amount < 100) {
      return 1; // 小额交易：仅需平台密钥1
    } else if (amount < 1000) {
      return 2; // 中额交易：平台密钥1 + 用户验证
    } else {
      return 3; // 大额交易：平台密钥1 + 平台密钥2 + 用户验证
    }
  }

  /**
   * 构建交易数据
   */
  private async buildTransactionData(transaction: Transaction): Promise<string> {
    const data = {
      id: transaction.id,
      from: transaction.fromAddress,
      to: transaction.toAddress,
      amount: transaction.amount,
      tokenType: transaction.tokenType,
      timestamp: transaction.createdAt.getTime(),
    };
    
    return JSON.stringify(data);
  }

  /**
   * 验证管理员权限
   */
  private async verifyAdminPermission(adminUserId: string): Promise<void> {
    // 实现管理员权限验证逻辑
    // 这里需要检查用户是否有管理员权限
    console.log(`Verifying admin permission for user: ${adminUserId}`);
  }

  /**
   * 验证Telegram验证码
   */
  private async verifyTelegramCode(
    telegramUserId: number,
    verificationCode: string
  ): Promise<void> {
    // 实现Telegram验证码验证逻辑
    // 这里需要与Telegram Bot API集成
    console.log(`Verifying Telegram code for user: ${telegramUserId}`);
  }

  /**
   * 生成用户验证数据
   */
  private generateUserVerificationData(
    transaction: Transaction,
    telegramUserId: number,
    verificationCode: string
  ): string {
    return `${transaction.id}:${telegramUserId}:${verificationCode}:${Date.now()}`;
  }

  /**
   * 验证所有签名
   */
  private async validateSignatures(
    multiSigTx: MultiSigTransaction,
    transaction: Transaction
  ): Promise<void> {
    for (const signature of multiSigTx.signatures) {
      const isValid = await this.validateSingleSignature(signature, transaction);
      if (!isValid) {
        throw new Error(`Invalid signature: ${signature.id}`);
      }
    }
  }

  /**
   * 验证单个签名
   */
  private async validateSingleSignature(
    signature: MultiSigSignature,
    transaction: Transaction
  ): Promise<boolean> {
    try {
      const txData = await this.buildTransactionData(transaction);
      
      if (signature.signerType === 'user_verification') {
        // 用户验证签名的验证逻辑
        return true; // 简化实现
      } else {
        // 平台密钥签名的验证逻辑
        const recoveredAddress = ethers.verifyMessage(txData, signature.signature);
        // 这里需要验证恢复的地址是否匹配预期的签名者
        return true; // 简化实现
      }
    } catch (error) {
      return false;
    }
  }

  /**
   * 在区块链上执行交易
   */
  private async executeOnChain(transaction: Transaction): Promise<string> {
    // 实现具体的区块链交易执行逻辑
    // 这里需要根据交易类型调用相应的合约方法
    console.log(`Executing transaction on chain: ${transaction.id}`);
    return `0x${Math.random().toString(16).substr(2, 64)}`; // 模拟交易哈希
  }

  /**
   * 生成多重签名ID
   */
  private generateMultiSigId(): string {
    return `multisig_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 生成签名ID
   */
  private generateSignatureId(): string {
    return `sig_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
