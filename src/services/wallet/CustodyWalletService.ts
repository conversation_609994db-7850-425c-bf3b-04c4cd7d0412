/**
 * 托管钱包服务
 * 为Telegram用户自动生成和管理BSC钱包地址
 */

import { ethers } from 'ethers';
import { TelegramUser, WalletBalance, WalletTransaction, TransferRequest, WithdrawalRequest } from './types';

export interface CustodyWalletConfig {
  rpcUrl: string;
  chainId: number;
  masterSeed?: string;
}

export class CustodyWalletService {
  private provider: ethers.JsonRpcProvider;
  private config: CustodyWalletConfig;
  private walletCache: Map<number, ethers.Wallet> = new Map();

  constructor(config: CustodyWalletConfig) {
    this.config = config;

    // 在开发环境中使用模拟提供者，避免真实网络连接
    if (process.env.NODE_ENV === 'development') {
      console.log('🔧 开发环境：使用模拟区块链提供者');
      // 创建一个模拟的提供者，不会实际连接网络
      this.provider = {
        getBalance: async () => ethers.parseEther('1.0'),
        getTransactionHistory: async () => [],
        sendTransaction: async () => ({ hash: '0x' + Math.random().toString(16).substr(2, 64) }),
      } as any;
    } else {
      this.provider = new ethers.JsonRpcProvider(config.rpcUrl);
    }
  }

  /**
   * 为Telegram用户生成钱包地址
   */
  async generateWalletForUser(telegramUserId: number): Promise<string> {
    try {
      // 检查缓存
      if (this.walletCache.has(telegramUserId)) {
        return this.walletCache.get(telegramUserId)!.address;
      }

      // 在开发环境中生成简单的模拟地址
      if (process.env.NODE_ENV === 'development') {
        const mockAddress = `0x${telegramUserId.toString(16).padStart(40, '0')}`;
        console.log(`🔧 开发环境：为用户 ${telegramUserId} 生成模拟钱包地址: ${mockAddress}`);

        // 创建模拟钱包对象
        const mockWallet = {
          address: mockAddress,
          privateKey: '0x' + 'a'.repeat(64), // 模拟私钥
        } as any;

        this.walletCache.set(telegramUserId, mockWallet);
        return mockAddress;
      }

      // 生产环境的真实钱包生成
      const seed = this.generateSeedForUser(telegramUserId);
      const wallet = ethers.Wallet.fromPhrase(seed);

      // 连接到提供者
      const connectedWallet = wallet.connect(this.provider);

      // 缓存钱包
      this.walletCache.set(telegramUserId, connectedWallet);

      console.log(`Generated wallet for user ${telegramUserId}: ${wallet.address}`);

      return wallet.address;
    } catch (error) {
      console.error('Failed to generate wallet:', error);
      throw new Error('钱包生成失败');
    }
  }

  /**
   * 获取用户钱包余额
   */
  async getBalance(telegramUserId: number): Promise<WalletBalance> {
    try {
      const walletAddress = await this.generateWalletForUser(telegramUserId);

      // 在开发环境中返回模拟数据
      if (process.env.NODE_ENV === 'development') {
        console.log(`🔧 开发环境：返回模拟余额数据 for user ${telegramUserId}`);

        // 从localStorage获取待提现余额（模拟数据库）
        const pendingBalanceKey = `pending_balance_${telegramUserId}`;
        const storedPendingBalance = localStorage.getItem(pendingBalanceKey);
        let pendingHaoxBalance = '50'; // 默认50 HAOX
        let pendingBnbBalance = '0.01'; // 默认0.01 BNB

        if (storedPendingBalance) {
          try {
            const parsed = JSON.parse(storedPendingBalance);
            pendingHaoxBalance = parsed.haox || '50';
            pendingBnbBalance = parsed.bnb || '0.01';
          } catch (e) {
            console.warn('Failed to parse stored pending balance');
          }
        }

        return {
          address: walletAddress,
          bnbBalance: ethers.parseEther('0.5').toString(), // 模拟0.5 BNB
          haoxBalance: ethers.parseEther('1000').toString(), // 模拟1000 HAOX
          pendingHaoxBalance: ethers.parseEther(pendingHaoxBalance).toString(),
          pendingBnbBalance: ethers.parseEther(pendingBnbBalance).toString(),
          lastUpdated: new Date(),
        };
      }

      // 生产环境的真实余额查询
      const bnbBalance = await this.provider.getBalance(walletAddress);

      // 模拟HAOX余额（在真实环境中需要调用代币合约）
      const haoxBalance = ethers.parseEther('1000'); // 模拟1000 HAOX

      return {
        address: walletAddress,
        bnbBalance: bnbBalance.toString(),
        haoxBalance: haoxBalance.toString(),
        pendingHaoxBalance: '0', // 需要从数据库获取
        pendingBnbBalance: '0',  // 需要从数据库获取
        lastUpdated: new Date(),
      };
    } catch (error) {
      console.error('Failed to get balance:', error);
      throw new Error('获取余额失败');
    }
  }

  /**
   * 更新待提现余额（开发环境模拟）
   */
  async updatePendingBalance(
    telegramUserId: number,
    amount: string,
    tokenType: 'HAOX' | 'BNB'
  ): Promise<void> {
    if (process.env.NODE_ENV === 'development') {
      const pendingBalanceKey = `pending_balance_${telegramUserId}`;
      const storedPendingBalance = localStorage.getItem(pendingBalanceKey);
      let currentBalance = { haox: '50', bnb: '0.01' };

      if (storedPendingBalance) {
        try {
          currentBalance = JSON.parse(storedPendingBalance);
        } catch (e) {
          console.warn('Failed to parse stored pending balance');
        }
      }

      // 添加新的奖励到待提现余额
      if (tokenType === 'HAOX') {
        const currentHaox = parseFloat(currentBalance.haox || '0');
        const newAmount = parseFloat(amount);
        currentBalance.haox = (currentHaox + newAmount).toString();
      } else if (tokenType === 'BNB') {
        const currentBnb = parseFloat(currentBalance.bnb || '0');
        const newAmount = parseFloat(amount);
        currentBalance.bnb = (currentBnb + newAmount).toString();
      }

      localStorage.setItem(pendingBalanceKey, JSON.stringify(currentBalance));
      console.log(`💰 待提现余额已更新: +${amount} ${tokenType}`);
    }
  }

  /**
   * 获取交易历史
   */
  async getTransactionHistory(telegramUserId: number): Promise<WalletTransaction[]> {
    try {
      const walletAddress = await this.generateWalletForUser(telegramUserId);

      // 在开发环境中返回模拟数据
      if (process.env.NODE_ENV === 'development') {
        console.log(`🔧 开发环境：返回模拟交易历史 for user ${telegramUserId}`);
      }

      // 模拟交易历史（在真实环境中需要从区块链获取）
      const mockHistory: WalletTransaction[] = [
        {
          id: '1',
          type: 'deposit',
          tokenType: 'HAOX',
          amount: '500',
          fromAddress: '******************************************',
          toAddress: walletAddress,
          txHash: '0x1234567890abcdef',
          createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1天前
          status: 'confirmed',
          gasFee: '0.001',
        },
        {
          id: '2',
          type: 'transfer',
          tokenType: 'BNB',
          amount: '0.1',
          fromAddress: walletAddress,
          toAddress: '0x9876543210fedcba',
          txHash: '0xfedcba0987654321',
          createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2天前
          status: 'confirmed',
          gasFee: '0.0005',
        },
        {
          id: '3',
          type: 'reward_claim',
          tokenType: 'HAOX',
          amount: '100',
          fromAddress: '******************************************',
          toAddress: walletAddress,
          txHash: '0xabcdef1234567890',
          createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3天前
          status: 'confirmed',
          gasFee: '0.0002',
        },
      ];

      return mockHistory;
    } catch (error) {
      console.error('Failed to get transaction history:', error);
      throw new Error('获取交易历史失败');
    }
  }

  /**
   * 转账
   */
  async transfer(
    telegramUserId: number, 
    request: TransferRequest
  ): Promise<{ success: boolean; txHash?: string; error?: string }> {
    try {
      const wallet = this.walletCache.get(telegramUserId);
      if (!wallet) {
        throw new Error('钱包未找到');
      }

      // 在开发环境中跳过地址验证
      if (process.env.NODE_ENV !== 'development') {
        // 验证地址格式
        if (!ethers.isAddress(request.toAddress)) {
          throw new Error('无效的接收地址');
        }
      }

      // 模拟转账（在真实环境中需要实际发送交易）
      if (process.env.NODE_ENV === 'development') {
        console.log('🔧 开发环境：模拟转账操作:', {
          from: wallet.address,
          to: request.toAddress,
          amount: request.amount,
          tokenType: request.tokenType,
        });
      }

      // 模拟交易哈希
      const mockTxHash = '0x' + Math.random().toString(16).substr(2, 64);

      return {
        success: true,
        txHash: mockTxHash,
      };
    } catch (error) {
      console.error('Transfer failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '转账失败',
      };
    }
  }

  /**
   * 提现到外部地址
   */
  async withdraw(
    telegramUserId: number,
    request: WithdrawalRequest
  ): Promise<{ success: boolean; txHash?: string; error?: string }> {
    try {
      const wallet = this.walletCache.get(telegramUserId);
      if (!wallet) {
        throw new Error('钱包未找到');
      }

      // 在开发环境中跳过地址验证
      if (process.env.NODE_ENV !== 'development') {
        // 验证地址格式
        if (!ethers.isAddress(request.toAddress)) {
          throw new Error('无效的提现地址');
        }
      }

      // 模拟提现（在真实环境中需要实际发送交易）
      if (process.env.NODE_ENV === 'development') {
        console.log('🔧 开发环境：模拟提现操作:', {
          from: wallet.address,
          to: request.toAddress,
          amount: request.amount,
          tokenType: request.tokenType,
        });
      }

      // 模拟交易哈希
      const mockTxHash = '0x' + Math.random().toString(16).substr(2, 64);

      return {
        success: true,
        txHash: mockTxHash,
      };
    } catch (error) {
      console.error('Withdrawal failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '提现失败',
      };
    }
  }

  /**
   * 为用户生成确定性种子
   */
  private generateSeedForUser(telegramUserId: number): string {
    // 在真实环境中，这应该使用更安全的方法
    // 这里只是为了演示目的
    const baseSeed = this.config.masterSeed || 'sociomint-demo-seed';
    const userSeed = `${baseSeed}-user-${telegramUserId}`;
    
    // 生成助记词
    const entropy = ethers.keccak256(ethers.toUtf8Bytes(userSeed));
    const mnemonic = ethers.Mnemonic.fromEntropy(entropy.slice(0, 32));
    
    return mnemonic.phrase;
  }

  /**
   * 获取钱包私钥（仅用于内部操作）
   */
  private async getWalletPrivateKey(telegramUserId: number): Promise<string> {
    const wallet = this.walletCache.get(telegramUserId);
    if (!wallet) {
      throw new Error('钱包未找到');
    }
    return wallet.privateKey;
  }

  /**
   * 验证用户是否有足够余额
   */
  async hasEnoughBalance(
    telegramUserId: number,
    amount: string,
    tokenType: 'BNB' | 'HAOX'
  ): Promise<boolean> {
    try {
      const balance = await this.getBalance(telegramUserId);
      const requiredAmount = ethers.parseEther(amount);
      
      if (tokenType === 'BNB') {
        return BigInt(balance.bnb) >= requiredAmount;
      } else {
        return BigInt(balance.haox) >= requiredAmount;
      }
    } catch (error) {
      console.error('Failed to check balance:', error);
      return false;
    }
  }
}

// 创建默认实例
export const custodyWalletService = new CustodyWalletService({
  rpcUrl: process.env.NEXT_PUBLIC_BSC_RPC_URL || 'https://bsc-dataseed1.binance.org/',
  chainId: process.env.NEXT_PUBLIC_NETWORK === 'mainnet' ? 56 : 97,
  masterSeed: process.env.WALLET_MASTER_SEED,
});
