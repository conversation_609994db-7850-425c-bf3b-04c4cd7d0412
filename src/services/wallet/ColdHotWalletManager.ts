/**
 * 冷热钱包管理器
 * 管理资金在冷热钱包间的安全转移
 */

import { ethers } from 'ethers';
import { WalletAddress, Transaction } from './types';
import { BlockchainService } from '@/services/blockchain/BlockchainService';
import { AuditLogger } from './AuditLogger';
import { KMSService } from './KMSService';

export interface ColdHotWalletConfig {
  hotWalletThreshold: string; // 热钱包最大金额阈值
  coldWalletAddress: string; // 冷钱包地址
  transferThreshold: string; // 触发转移的阈值
  emergencyThreshold: string; // 紧急情况阈值
  autoTransferEnabled: boolean; // 是否启用自动转移
  transferCooldown: number; // 转移冷却时间（秒）
}

export interface WalletBalance {
  address: string;
  haoxBalance: string;
  bnbBalance: string;
  usdValue: string;
}

export class ColdHotWalletManager {
  private config: ColdHotWalletConfig;
  private blockchainService: BlockchainService;
  private auditLogger: AuditLogger;
  private kmsService: KMSService;
  private hotWallet: WalletAddress | null = null;
  private coldWallet: WalletAddress | null = null;
  private lastTransferTime: number = 0;

  constructor(
    config: ColdHotWalletConfig,
    blockchainService: BlockchainService,
    auditLogger: AuditLogger,
    kmsService: KMSService
  ) {
    this.config = config;
    this.blockchainService = blockchainService;
    this.auditLogger = auditLogger;
    this.kmsService = kmsService;
  }

  /**
   * 初始化冷热钱包管理器
   */
  async initialize(hotWallet: WalletAddress, coldWallet: WalletAddress): Promise<void> {
    try {
      this.hotWallet = hotWallet;
      this.coldWallet = coldWallet;

      // 验证钱包地址
      if (!ethers.isAddress(hotWallet.address) || !ethers.isAddress(coldWallet.address)) {
        throw new Error('Invalid wallet addresses');
      }

      await this.auditLogger.log({
        action: 'cold_hot_wallet_manager_initialized',
        actor: 'system',
        actorType: 'system',
        resource: 'cold_hot_manager',
        resourceId: 'main',
        details: {
          hotWalletAddress: hotWallet.address,
          coldWalletAddress: coldWallet.address,
          hotWalletThreshold: this.config.hotWalletThreshold,
          autoTransferEnabled: this.config.autoTransferEnabled,
        },
      });
    } catch (error) {
      await this.auditLogger.log({
        action: 'cold_hot_wallet_manager_init_failed',
        actor: 'system',
        actorType: 'system',
        resource: 'cold_hot_manager',
        resourceId: 'main',
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
      throw error;
    }
  }

  /**
   * 检查热钱包余额并执行自动转移
   */
  async checkAndTransferFunds(): Promise<void> {
    if (!this.config.autoTransferEnabled || !this.hotWallet || !this.coldWallet) {
      return;
    }

    try {
      // 检查冷却时间
      const now = Date.now() / 1000;
      if (now - this.lastTransferTime < this.config.transferCooldown) {
        return;
      }

      // 获取热钱包余额
      const hotWalletBalance = await this.getWalletBalance(this.hotWallet.address);
      const haoxBalance = parseFloat(hotWalletBalance.haoxBalance);
      const threshold = parseFloat(this.config.hotWalletThreshold);

      // 如果超过阈值，转移到冷钱包
      if (haoxBalance > threshold) {
        const transferAmount = haoxBalance - threshold * 0.8; // 保留20%缓冲
        await this.transferToColdWallet(transferAmount.toString());
      }
    } catch (error) {
      await this.auditLogger.log({
        action: 'auto_transfer_check_failed',
        actor: 'system',
        actorType: 'system',
        resource: 'auto_transfer',
        resourceId: 'check',
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
    }
  }

  /**
   * 手动转移资金到冷钱包
   */
  async transferToColdWallet(amount: string, adminUserId?: string): Promise<string> {
    if (!this.hotWallet || !this.coldWallet) {
      throw new Error('Wallets not initialized');
    }

    try {
      // 获取热钱包私钥
      const hotWalletPrivateKey = await this.kmsService.decryptData(
        `hot_wallet_${this.hotWallet.id}`
      );

      // 发送HAOX代币到冷钱包
      const txHash = await this.blockchainService.sendHAOXToken(
        hotWalletPrivateKey,
        this.coldWallet.address,
        amount
      );

      // 更新最后转移时间
      this.lastTransferTime = Date.now() / 1000;

      await this.auditLogger.log({
        action: 'funds_transferred_to_cold_wallet',
        actor: adminUserId || 'system',
        actorType: adminUserId ? 'admin' : 'system',
        resource: 'cold_transfer',
        resourceId: txHash,
        details: {
          amount,
          fromAddress: this.hotWallet.address,
          toAddress: this.coldWallet.address,
          txHash,
          transferType: adminUserId ? 'manual' : 'automatic',
        },
      });

      return txHash;
    } catch (error) {
      await this.auditLogger.log({
        action: 'cold_transfer_failed',
        actor: adminUserId || 'system',
        actorType: adminUserId ? 'admin' : 'system',
        resource: 'cold_transfer',
        resourceId: 'unknown',
        details: {
          amount,
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
      throw error;
    }
  }

  /**
   * 从冷钱包转移资金到热钱包
   */
  async transferFromColdWallet(amount: string, adminUserId: string): Promise<string> {
    if (!this.hotWallet || !this.coldWallet) {
      throw new Error('Wallets not initialized');
    }

    try {
      // 冷钱包转移需要管理员授权
      await this.verifyAdminPermission(adminUserId);

      // 获取冷钱包私钥（需要额外的安全验证）
      const coldWalletPrivateKey = await this.kmsService.decryptData(
        `cold_wallet_${this.coldWallet.id}`
      );

      // 发送HAOX代币到热钱包
      const txHash = await this.blockchainService.sendHAOXToken(
        coldWalletPrivateKey,
        this.hotWallet.address,
        amount
      );

      await this.auditLogger.log({
        action: 'funds_transferred_from_cold_wallet',
        actor: adminUserId,
        actorType: 'admin',
        resource: 'hot_transfer',
        resourceId: txHash,
        details: {
          amount,
          fromAddress: this.coldWallet.address,
          toAddress: this.hotWallet.address,
          txHash,
          adminUserId,
        },
      });

      return txHash;
    } catch (error) {
      await this.auditLogger.log({
        action: 'hot_transfer_failed',
        actor: adminUserId,
        actorType: 'admin',
        resource: 'hot_transfer',
        resourceId: 'unknown',
        details: {
          amount,
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
      throw error;
    }
  }

  /**
   * 获取钱包余额
   */
  async getWalletBalance(address: string): Promise<WalletBalance> {
    try {
      const balances = await this.blockchainService.getWalletBalances(address);
      return {
        address,
        haoxBalance: balances.haoxBalance,
        bnbBalance: balances.bnbBalance,
        usdValue: balances.usdValue,
      };
    } catch (error) {
      await this.auditLogger.log({
        action: 'wallet_balance_query_failed',
        actor: 'system',
        actorType: 'system',
        resource: 'balance_query',
        resourceId: address,
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
      throw error;
    }
  }

  /**
   * 获取冷热钱包状态
   */
  async getWalletStatus(): Promise<{
    hotWallet: WalletBalance;
    coldWallet: WalletBalance;
    totalBalance: {
      haoxBalance: string;
      bnbBalance: string;
      usdValue: string;
    };
    thresholdStatus: {
      isOverThreshold: boolean;
      threshold: string;
      recommendedAction: string;
    };
  }> {
    if (!this.hotWallet || !this.coldWallet) {
      throw new Error('Wallets not initialized');
    }

    try {
      const [hotBalance, coldBalance] = await Promise.all([
        this.getWalletBalance(this.hotWallet.address),
        this.getWalletBalance(this.coldWallet.address),
      ]);

      const totalHaoxBalance = (
        parseFloat(hotBalance.haoxBalance) + parseFloat(coldBalance.haoxBalance)
      ).toString();

      const totalBnbBalance = (
        parseFloat(hotBalance.bnbBalance) + parseFloat(coldBalance.bnbBalance)
      ).toString();

      const totalUsdValue = (
        parseFloat(hotBalance.usdValue) + parseFloat(coldBalance.usdValue)
      ).toFixed(2);

      const threshold = parseFloat(this.config.hotWalletThreshold);
      const hotHaoxBalance = parseFloat(hotBalance.haoxBalance);
      const isOverThreshold = hotHaoxBalance > threshold;

      let recommendedAction = 'No action needed';
      if (isOverThreshold) {
        recommendedAction = `Transfer ${(hotHaoxBalance - threshold * 0.8).toFixed(2)} HAOX to cold wallet`;
      } else if (hotHaoxBalance < threshold * 0.2) {
        recommendedAction = 'Consider transferring funds from cold wallet';
      }

      return {
        hotWallet: hotBalance,
        coldWallet: coldBalance,
        totalBalance: {
          haoxBalance: totalHaoxBalance,
          bnbBalance: totalBnbBalance,
          usdValue: totalUsdValue,
        },
        thresholdStatus: {
          isOverThreshold,
          threshold: this.config.hotWalletThreshold,
          recommendedAction,
        },
      };
    } catch (error) {
      await this.auditLogger.log({
        action: 'wallet_status_query_failed',
        actor: 'system',
        actorType: 'system',
        resource: 'wallet_status',
        resourceId: 'all',
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
      throw error;
    }
  }

  /**
   * 紧急冻结热钱包
   */
  async emergencyFreeze(adminUserId: string, reason: string): Promise<void> {
    try {
      await this.verifyAdminPermission(adminUserId);

      // 实现紧急冻结逻辑
      // 这里可以暂停所有热钱包交易
      
      await this.auditLogger.log({
        action: 'emergency_freeze_activated',
        actor: adminUserId,
        actorType: 'admin',
        resource: 'emergency_freeze',
        resourceId: this.hotWallet?.address || 'unknown',
        details: {
          reason,
          timestamp: new Date().toISOString(),
          adminUserId,
        },
      });
    } catch (error) {
      await this.auditLogger.log({
        action: 'emergency_freeze_failed',
        actor: adminUserId,
        actorType: 'admin',
        resource: 'emergency_freeze',
        resourceId: 'unknown',
        details: {
          reason,
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
      throw error;
    }
  }

  /**
   * 验证管理员权限
   */
  private async verifyAdminPermission(adminUserId: string): Promise<void> {
    // 实现管理员权限验证逻辑
    // 这里应该检查用户是否有管理员权限
    console.log(`Verifying admin permission for user: ${adminUserId}`);
  }
}
