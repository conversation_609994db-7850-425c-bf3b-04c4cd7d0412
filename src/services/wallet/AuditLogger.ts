/**
 * 审计日志服务
 * 记录所有敏感操作和安全事件
 */

import { AuditLog } from './types';

export interface AuditLoggerConfig {
  logLevel: 'debug' | 'info' | 'warn' | 'error';
  enableConsoleLogging: boolean;
  enableDatabaseLogging: boolean;
  enableCloudWatchLogging: boolean;
  retentionDays: number;
  encryptLogs: boolean;
}

export class AuditLogger {
  private config: AuditLoggerConfig;
  private logBuffer: AuditLog[] = [];
  private flushInterval: NodeJS.Timeout | null = null;

  constructor(config: AuditLoggerConfig) {
    this.config = config;
    this.startLogFlushing();
  }

  /**
   * 记录审计日志
   */
  async log(logData: Omit<AuditLog, 'id' | 'timestamp'>): Promise<void> {
    const auditLog: AuditLog = {
      id: this.generateLogId(),
      ...logData,
      timestamp: new Date(),
    };

    // 添加到缓冲区
    this.logBuffer.push(auditLog);

    // 控制台输出（开发环境）
    if (this.config.enableConsoleLogging) {
      this.logToConsole(auditLog);
    }

    // 如果是关键操作，立即刷新
    if (this.isCriticalAction(auditLog.action)) {
      await this.flushLogs();
    }
  }

  /**
   * 记录安全事件
   */
  async logSecurityEvent(
    type: 'login_attempt' | 'failed_transaction' | 'suspicious_activity' | 'unauthorized_access',
    details: Record<string, any>,
    severity: 'low' | 'medium' | 'high' | 'critical' = 'medium'
  ): Promise<void> {
    await this.log({
      action: `security_event_${type}`,
      actor: details.actor || 'unknown',
      actorType: details.actorType || 'unknown',
      resource: 'security',
      resourceId: details.resourceId || 'unknown',
      details: {
        ...details,
        severity,
        eventType: type,
      },
      ipAddress: details.ipAddress,
      userAgent: details.userAgent,
    });

    // 高危事件立即告警
    if (severity === 'high' || severity === 'critical') {
      await this.sendSecurityAlert(type, details, severity);
    }
  }

  /**
   * 记录交易事件
   */
  async logTransaction(
    action: 'transaction_created' | 'transaction_signed' | 'transaction_broadcast' | 'transaction_confirmed' | 'transaction_failed',
    transactionId: string,
    details: Record<string, any>
  ): Promise<void> {
    await this.log({
      action,
      actor: details.actor || 'system',
      actorType: details.actorType || 'system',
      resource: 'transaction',
      resourceId: transactionId,
      details: {
        ...details,
        transactionId,
      },
    });
  }

  /**
   * 记录钱包操作
   */
  async logWalletOperation(
    action: 'wallet_created' | 'wallet_accessed' | 'balance_checked' | 'private_key_accessed',
    walletId: string,
    telegramUserId: number,
    details: Record<string, any> = {}
  ): Promise<void> {
    await this.log({
      action,
      actor: telegramUserId.toString(),
      actorType: 'user',
      resource: 'wallet',
      resourceId: walletId,
      details: {
        ...details,
        telegramUserId,
        walletId,
      },
    });
  }

  /**
   * 查询审计日志
   */
  async queryLogs(filters: {
    action?: string;
    actor?: string;
    resource?: string;
    startDate?: Date;
    endDate?: Date;
    limit?: number;
  }): Promise<AuditLog[]> {
    // 实现日志查询逻辑
    // 这里需要根据实际的存储方式实现
    return [];
  }

  /**
   * 生成审计报告
   */
  async generateAuditReport(
    startDate: Date,
    endDate: Date,
    reportType: 'security' | 'transactions' | 'user_activity' | 'system_events'
  ): Promise<{
    reportId: string;
    period: { start: Date; end: Date };
    summary: Record<string, any>;
    details: AuditLog[];
  }> {
    const logs = await this.queryLogs({
      startDate,
      endDate,
    });

    const filteredLogs = this.filterLogsByReportType(logs, reportType);
    const summary = this.generateReportSummary(filteredLogs, reportType);

    return {
      reportId: this.generateReportId(),
      period: { start: startDate, end: endDate },
      summary,
      details: filteredLogs,
    };
  }

  /**
   * 刷新日志缓冲区
   */
  private async flushLogs(): Promise<void> {
    if (this.logBuffer.length === 0) return;

    const logsToFlush = [...this.logBuffer];
    this.logBuffer = [];

    try {
      // 批量写入数据库
      if (this.config.enableDatabaseLogging) {
        await this.writeToDatabase(logsToFlush);
      }

      // 写入CloudWatch
      if (this.config.enableCloudWatchLogging) {
        await this.writeToCloudWatch(logsToFlush);
      }
    } catch (error) {
      console.error('Failed to flush audit logs:', error);
      // 将失败的日志重新加入缓冲区
      this.logBuffer.unshift(...logsToFlush);
    }
  }

  /**
   * 启动日志刷新定时器
   */
  private startLogFlushing(): void {
    this.flushInterval = setInterval(async () => {
      await this.flushLogs();
    }, 5000); // 每5秒刷新一次
  }

  /**
   * 停止日志刷新
   */
  async stop(): Promise<void> {
    if (this.flushInterval) {
      clearInterval(this.flushInterval);
      this.flushInterval = null;
    }
    await this.flushLogs(); // 最后一次刷新
  }

  /**
   * 控制台输出日志
   */
  private logToConsole(auditLog: AuditLog): void {
    const logMessage = `[AUDIT] ${auditLog.timestamp.toISOString()} - ${auditLog.action} by ${auditLog.actor} on ${auditLog.resource}:${auditLog.resourceId}`;
    
    if (this.isCriticalAction(auditLog.action)) {
      console.error(logMessage, auditLog.details);
    } else if (auditLog.action.includes('failed') || auditLog.action.includes('error')) {
      console.warn(logMessage, auditLog.details);
    } else {
      console.log(logMessage);
    }
  }

  /**
   * 写入数据库
   */
  private async writeToDatabase(logs: AuditLog[]): Promise<void> {
    // 实现数据库写入逻辑
    // 可以使用PostgreSQL、MongoDB等
    console.log(`Writing ${logs.length} audit logs to database`);
  }

  /**
   * 写入CloudWatch
   */
  private async writeToCloudWatch(logs: AuditLog[]): Promise<void> {
    // 实现CloudWatch日志写入
    console.log(`Writing ${logs.length} audit logs to CloudWatch`);
  }

  /**
   * 发送安全告警
   */
  private async sendSecurityAlert(
    type: string,
    details: Record<string, any>,
    severity: string
  ): Promise<void> {
    // 实现安全告警逻辑
    // 可以发送邮件、Slack通知、短信等
    console.error(`SECURITY ALERT [${severity.toUpperCase()}]: ${type}`, details);
  }

  /**
   * 判断是否为关键操作
   */
  private isCriticalAction(action: string): boolean {
    const criticalActions = [
      'private_key_accessed',
      'master_seed_generated',
      'wallet_backup_failed',
      'security_event_unauthorized_access',
      'transaction_failed',
      'key_rotation_failed',
    ];
    return criticalActions.includes(action);
  }

  /**
   * 根据报告类型过滤日志
   */
  private filterLogsByReportType(logs: AuditLog[], reportType: string): AuditLog[] {
    switch (reportType) {
      case 'security':
        return logs.filter(log => 
          log.action.includes('security') || 
          log.action.includes('failed') ||
          log.resource === 'security'
        );
      case 'transactions':
        return logs.filter(log => log.resource === 'transaction');
      case 'user_activity':
        return logs.filter(log => log.actorType === 'user');
      case 'system_events':
        return logs.filter(log => log.actorType === 'system');
      default:
        return logs;
    }
  }

  /**
   * 生成报告摘要
   */
  private generateReportSummary(logs: AuditLog[], reportType: string): Record<string, any> {
    const summary: Record<string, any> = {
      totalEvents: logs.length,
      period: reportType,
      generatedAt: new Date().toISOString(),
    };

    // 按操作类型统计
    const actionCounts: Record<string, number> = {};
    logs.forEach(log => {
      actionCounts[log.action] = (actionCounts[log.action] || 0) + 1;
    });
    summary.actionBreakdown = actionCounts;

    // 按参与者统计
    const actorCounts: Record<string, number> = {};
    logs.forEach(log => {
      actorCounts[log.actor] = (actorCounts[log.actor] || 0) + 1;
    });
    summary.actorBreakdown = actorCounts;

    return summary;
  }

  /**
   * 生成日志ID
   */
  private generateLogId(): string {
    return `audit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 生成报告ID
   */
  private generateReportId(): string {
    return `report_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
