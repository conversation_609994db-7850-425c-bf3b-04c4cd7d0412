/**
 * 多重签名钱包服务
 * 模拟实现，用于开发环境
 */

import { MultiSigTransaction } from './types';

export interface MultiSigWalletTransaction {
  id: string;
  to: string;
  value: string;
  data: string;
  confirmations: number;
  requiredConfirmations: number;
  executed: boolean;
  timestamp: Date;
}

export class MultiSigWalletService {
  private transactions: Map<string, MultiSigWalletTransaction> = new Map();

  /**
   * 提交多重签名交易
   */
  async submitTransaction(
    to: string,
    value: string,
    data: string = '0x'
  ): Promise<string> {
    const txId = `multisig-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    const transaction: MultiSigWalletTransaction = {
      id: txId,
      to,
      value,
      data,
      confirmations: 1, // 提交者自动确认
      requiredConfirmations: 2, // 需要2个确认
      executed: false,
      timestamp: new Date(),
    };

    this.transactions.set(txId, transaction);
    
    console.log('MultiSig transaction submitted:', transaction);
    
    return txId;
  }

  /**
   * 确认交易
   */
  async confirmTransaction(txId: string): Promise<boolean> {
    const transaction = this.transactions.get(txId);
    if (!transaction) {
      throw new Error('交易不存在');
    }

    if (transaction.executed) {
      throw new Error('交易已执行');
    }

    transaction.confirmations += 1;

    // 如果达到所需确认数，执行交易
    if (transaction.confirmations >= transaction.requiredConfirmations) {
      transaction.executed = true;
      console.log('MultiSig transaction executed:', transaction);
    }

    this.transactions.set(txId, transaction);
    
    return transaction.executed;
  }

  /**
   * 获取交易信息
   */
  async getTransaction(txId: string): Promise<MultiSigWalletTransaction | null> {
    return this.transactions.get(txId) || null;
  }

  /**
   * 获取待确认的交易列表
   */
  async getPendingTransactions(): Promise<MultiSigWalletTransaction[]> {
    return Array.from(this.transactions.values()).filter(tx => !tx.executed);
  }
}

// 创建默认实例
export const multiSigWalletService = new MultiSigWalletService();
