/**
 * Gas费用估算服务
 * 用于估算BSC网络上的交易费用
 */

import { ethers } from 'ethers';

export interface GasEstimate {
  gasLimit: string;
  gasPrice: string;
  totalCost: string;
  totalCostUSD: string;
}

export interface TransactionParams {
  to: string;
  value?: string;
  data?: string;
  tokenType?: 'BNB' | 'HAOX';
}

export class GasEstimationService {
  private provider: ethers.JsonRpcProvider;
  private bnbPriceUSD: number = 300; // 模拟BNB价格

  constructor(rpcUrl: string) {
    this.provider = new ethers.JsonRpcProvider(rpcUrl);
  }

  /**
   * 估算交易Gas费用
   */
  async estimateGas(params: TransactionParams): Promise<GasEstimate> {
    try {
      // 模拟Gas估算（在真实环境中需要调用实际的估算方法）
      let gasLimit: bigint;
      
      if (params.tokenType === 'HAOX') {
        // ERC20代币转账需要更多Gas
        gasLimit = BigInt(65000);
      } else {
        // BNB转账
        gasLimit = BigInt(21000);
      }

      // 获取当前Gas价格
      const gasPrice = await this.getCurrentGasPrice();
      
      // 计算总费用
      const totalCost = gasLimit * gasPrice;
      const totalCostEther = ethers.formatEther(totalCost);
      const totalCostUSD = (parseFloat(totalCostEther) * this.bnbPriceUSD).toFixed(2);

      return {
        gasLimit: gasLimit.toString(),
        gasPrice: gasPrice.toString(),
        totalCost: totalCost.toString(),
        totalCostUSD,
      };
    } catch (error) {
      console.error('Gas estimation failed:', error);
      
      // 返回默认估算值
      return {
        gasLimit: '21000',
        gasPrice: '**********', // 5 Gwei
        totalCost: '10**********000', // 0.000105 BNB
        totalCostUSD: '0.03',
      };
    }
  }

  /**
   * 获取当前Gas价格
   */
  async getCurrentGasPrice(): Promise<bigint> {
    try {
      const feeData = await this.provider.getFeeData();
      return feeData.gasPrice || BigInt(**********); // 默认5 Gwei
    } catch (error) {
      console.error('Failed to get gas price:', error);
      return BigInt(**********); // 默认5 Gwei
    }
  }

  /**
   * 获取建议的Gas价格（快速、标准、慢速）
   */
  async getGasPriceSuggestions(): Promise<{
    slow: string;
    standard: string;
    fast: string;
  }> {
    try {
      const basePrice = await this.getCurrentGasPrice();
      
      return {
        slow: (basePrice * BigInt(80) / BigInt(100)).toString(), // 80%
        standard: basePrice.toString(), // 100%
        fast: (basePrice * BigInt(120) / BigInt(100)).toString(), // 120%
      };
    } catch (error) {
      console.error('Failed to get gas price suggestions:', error);
      
      return {
        slow: '**********', // 4 Gwei
        standard: '**********', // 5 Gwei
        fast: '**********', // 6 Gwei
      };
    }
  }

  /**
   * 估算代币转账Gas费用
   */
  async estimateTokenTransferGas(
    tokenAddress: string,
    to: string,
    amount: string
  ): Promise<GasEstimate> {
    try {
      // ERC20代币转账的标准Gas限制
      const gasLimit = BigInt(65000);
      const gasPrice = await this.getCurrentGasPrice();
      const totalCost = gasLimit * gasPrice;
      const totalCostEther = ethers.formatEther(totalCost);
      const totalCostUSD = (parseFloat(totalCostEther) * this.bnbPriceUSD).toFixed(2);

      return {
        gasLimit: gasLimit.toString(),
        gasPrice: gasPrice.toString(),
        totalCost: totalCost.toString(),
        totalCostUSD,
      };
    } catch (error) {
      console.error('Token transfer gas estimation failed:', error);
      
      return {
        gasLimit: '65000',
        gasPrice: '**********',
        totalCost: '32**********000',
        totalCostUSD: '0.10',
      };
    }
  }

  /**
   * 更新BNB价格（用于USD计算）
   */
  async updateBNBPrice(): Promise<void> {
    try {
      // 在真实环境中，这里应该调用价格API
      // 例如CoinGecko或Binance API
      console.log('BNB price update (mock)');
    } catch (error) {
      console.error('Failed to update BNB price:', error);
    }
  }

  /**
   * 格式化Gas费用显示
   */
  formatGasCost(gasEstimate: GasEstimate): {
    bnb: string;
    usd: string;
    gwei: string;
  } {
    const bnbAmount = ethers.formatEther(gasEstimate.totalCost);
    const gweiAmount = ethers.formatUnits(gasEstimate.gasPrice, 'gwei');

    return {
      bnb: parseFloat(bnbAmount).toFixed(6),
      usd: gasEstimate.totalCostUSD,
      gwei: parseFloat(gweiAmount).toFixed(1),
    };
  }
}

// 创建默认实例
export const gasEstimationService = new GasEstimationService(
  process.env.NEXT_PUBLIC_BSC_RPC_URL || 'https://bsc-dataseed1.binance.org/'
);
