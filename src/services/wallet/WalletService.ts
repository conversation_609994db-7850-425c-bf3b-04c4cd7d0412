/**
 * 钱包服务
 * 统一的钱包操作接口
 */

import { ethers } from 'ethers';
import {
  Telegram<PERSON>ser,
  WalletAddress,
  WalletBalance,
  WalletTransaction,
  CreateWalletRequest,
  CreateWalletResponse,
  GetBalanceRequest,
  GetBalanceResponse,
  TransferRequest,
  TransferResponse,
  WithdrawRequest,
  WithdrawResponse,
  WalletConfig,
  HDWalletConfig,
} from './types';
import { HDWalletManager } from './HDWalletManager';
import { AuditLogger, AuditLoggerConfig } from './AuditLogger';
import { MultiSigManager } from './MultiSigManager';
import { BlockchainService } from '@/services/blockchain/BlockchainService';
import { WalletDatabase } from '@/services/database/WalletDatabase';

export class WalletService {
  private hdWalletManager: HDWalletManager;
  private auditLogger: AuditLogger;
  private multiSigManager: MultiSigManager;
  private blockchainService: BlockchainService;
  private walletDatabase: WalletDatabase;
  private provider: ethers.JsonRpcProvider;
  private config: WalletConfig;
  private isInitialized = false;

  constructor() {
    // 配置初始化
    this.config = {
      network: (process.env.NEXT_PUBLIC_NETWORK as 'mainnet' | 'testnet') || 'testnet',
      rpcUrl: process.env.NEXT_PUBLIC_BSC_RPC_URL || 'https://bsc-dataseed1.binance.org/',
      chainId: process.env.NEXT_PUBLIC_NETWORK === 'mainnet' ? 56 : 97,
      haoxContractAddress: process.env.NEXT_PUBLIC_HAOX_CONTRACT_ADDRESS || '',
      multiSigContractAddress: process.env.NEXT_PUBLIC_MULTISIG_CONTRACT_ADDRESS || '',
      hotWalletThreshold: '10000', // 10,000 HAOX
      coldWalletAddress: process.env.COLD_WALLET_ADDRESS || '',
      gasLimitMultiplier: 1.2,
      maxGasPrice: '20000000000', // 20 Gwei
    };

    const hdWalletConfig: HDWalletConfig = {
      masterSeedPath: process.env.MASTER_SEED_PATH || '',
      derivationBasePath: "m/44'/60'/0'/0",
      encryptionKey: process.env.WALLET_ENCRYPTION_KEY || '',
      backupLocations: [
        process.env.BACKUP_LOCATION_1 || '',
        process.env.BACKUP_LOCATION_2 || '',
        process.env.BACKUP_LOCATION_3 || '',
      ].filter(Boolean),
    };

    const auditLoggerConfig: AuditLoggerConfig = {
      logLevel: 'info',
      enableConsoleLogging: process.env.NODE_ENV === 'development',
      enableDatabaseLogging: true,
      enableCloudWatchLogging: process.env.NODE_ENV === 'production',
      retentionDays: 365,
      encryptLogs: true,
    };

    // 初始化服务
    this.provider = new ethers.JsonRpcProvider(this.config.rpcUrl);
    this.auditLogger = new AuditLogger(auditLoggerConfig);
    this.hdWalletManager = new HDWalletManager(
      hdWalletConfig,
      this.auditLogger
    );
    this.blockchainService = new BlockchainService(this.auditLogger);
    this.multiSigManager = new MultiSigManager(
      {
        requiredSignatures: 2,
        totalSigners: 3,
        platformKey1Id: process.env.PLATFORM_KEY_1_ID || '',
        platformKey2Id: process.env.PLATFORM_KEY_2_ID || '',
        contractAddress: this.config.multiSigContractAddress,
      },
      this.config,
      this.auditLogger,
      this.blockchainService
    );
    this.walletDatabase = new WalletDatabase(
      {
        supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL || '',
        supabaseKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
      },
      this.auditLogger
    );
  }

  /**
   * 初始化钱包服务
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      await this.hdWalletManager.initialize();
      await this.blockchainService.initialize();
      await this.walletDatabase.initialize();

      this.isInitialized = true;
      
      await this.auditLogger.log({
        action: 'wallet_service_initialized',
        actor: 'system',
        actorType: 'system',
        resource: 'wallet_service',
        resourceId: 'main',
        details: {
          network: this.config.network,
          chainId: this.config.chainId,
        },
      });
    } catch (error) {
      await this.auditLogger.log({
        action: 'wallet_service_init_failed',
        actor: 'system',
        actorType: 'system',
        resource: 'wallet_service',
        resourceId: 'main',
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
      throw error;
    }
  }

  /**
   * 为用户创建钱包
   */
  async createWalletForUser(telegramUser: TelegramUser): Promise<WalletAddress> {
    await this.ensureInitialized();

    try {
      // 检查用户是否已有钱包
      const existingWallet = await this.walletDatabase.getWalletByTelegramUserId(telegramUser.id);
      if (existingWallet) {
        return existingWallet;
      }

      // 生成新钱包
      const walletAddress = await this.hdWalletManager.generateWalletForUser(telegramUser);

      // 保存到数据库
      await this.walletDatabase.saveWalletAddress(walletAddress);

      await this.auditLogger.logWalletOperation(
        'wallet_created',
        walletAddress.id,
        telegramUser.id,
        {
          address: walletAddress.address,
          derivationPath: walletAddress.derivationPath,
        }
      );

      return walletAddress;
    } catch (error) {
      await this.auditLogger.log({
        action: 'wallet_creation_failed',
        actor: telegramUser.id.toString(),
        actorType: 'user',
        resource: 'wallet',
        resourceId: 'unknown',
        details: {
          telegramUserId: telegramUser.id,
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
      throw error;
    }
  }

  /**
   * 获取用户钱包余额
   */
  async getWalletBalance(telegramUserId: number): Promise<WalletBalance> {
    await this.ensureInitialized();

    try {
      const wallet = await this.walletDatabase.getWalletByTelegramUserId(telegramUserId);
      if (!wallet) {
        throw new Error('Wallet not found for user');
      }

      // 使用区块链服务获取余额
      const balances = await this.blockchainService.getWalletBalances(wallet.address);

      const balance: WalletBalance = {
        address: wallet.address,
        haoxBalance: balances.haoxBalance,
        bnbBalance: balances.bnbBalance,
        lastUpdated: new Date(),
      };

      await this.auditLogger.logWalletOperation(
        'balance_checked',
        wallet.id,
        telegramUserId,
        {
          haoxBalance: balance.haoxBalance,
          bnbBalance: balance.bnbBalance,
        }
      );

      return balance;
    } catch (error) {
      await this.auditLogger.log({
        action: 'balance_check_failed',
        actor: telegramUserId.toString(),
        actorType: 'user',
        resource: 'wallet_balance',
        resourceId: telegramUserId.toString(),
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
      throw error;
    }
  }

  /**
   * 转账HAOX代币
   */
  async transferHAOX(
    fromTelegramUserId: number,
    toAddress: string,
    amount: string,
    verificationCode?: string
  ): Promise<WalletTransaction> {
    await this.ensureInitialized();

    try {
      // 验证地址格式
      if (!ethers.isAddress(toAddress)) {
        throw new Error('Invalid recipient address');
      }

      // 获取发送方钱包
      const fromWallet = await this.walletDatabase.getWalletByTelegramUserId(fromTelegramUserId);
      if (!fromWallet) {
        throw new Error('Sender wallet not found');
      }

      // 检查余额
      const balance = await this.getWalletBalance(fromTelegramUserId);
      if (parseFloat(balance.haoxBalance) < parseFloat(amount)) {
        throw new Error('Insufficient HAOX balance');
      }

      // 创建交易记录
      const transaction: WalletTransaction = {
        id: this.generateTransactionId(),
        fromAddress: fromWallet.address,
        toAddress,
        amount,
        tokenType: 'HAOX',
        status: 'pending',
        type: 'transfer',
        createdAt: new Date(),
        metadata: {
          telegramUserId: fromTelegramUserId,
          verificationCode,
        },
      };

      // 创建多重签名交易
      const multiSigTx = await this.multiSigManager.createMultiSigTransaction(
        transaction,
        fromTelegramUserId
      );

      // 根据金额确定签名策略
      const amountNum = parseFloat(amount);
      if (amountNum < 100) {
        // 小额交易：仅需平台密钥1
        const signature1 = await this.multiSigManager.signWithPlatformKey1(multiSigTx, transaction);
        multiSigTx.signatures.push(signature1);
      } else if (amountNum < 1000) {
        // 中额交易：平台密钥1 + 用户验证
        if (!verificationCode) {
          throw new Error('Verification code required for this amount');
        }
        const signature1 = await this.multiSigManager.signWithPlatformKey1(multiSigTx, transaction);
        const userSig = await this.multiSigManager.signWithUserVerification(
          multiSigTx,
          transaction,
          fromTelegramUserId,
          verificationCode
        );
        multiSigTx.signatures.push(signature1, userSig);
      } else {
        // 大额交易：需要人工审核
        transaction.status = 'pending';
        await this.saveTransactionToDatabase(transaction);
        return transaction;
      }

      // 执行交易
      const txHash = await this.multiSigManager.executeMultiSigTransaction(multiSigTx, transaction);
      transaction.txHash = txHash;
      transaction.status = 'confirmed';
      transaction.confirmedAt = new Date();

      // 保存交易记录
      await this.walletDatabase.saveTransaction(transaction);

      return transaction;
    } catch (error) {
      await this.auditLogger.logTransaction(
        'transaction_failed',
        'unknown',
        {
          fromTelegramUserId,
          toAddress,
          amount,
          error: error instanceof Error ? error.message : 'Unknown error',
        }
      );
      throw error;
    }
  }

  /**
   * 获取用户交易历史
   */
  async getUserTransactionHistory(
    telegramUserId: number,
    page: number = 1,
    limit: number = 50
  ): Promise<Transaction[]> {
    await this.ensureInitialized();

    try {
      const offset = (page - 1) * limit;
      return await this.walletDatabase.getUserTransactions(telegramUserId, limit, offset);
    } catch (error) {
      await this.auditLogger.log({
        action: 'transaction_history_query_failed',
        actor: telegramUserId.toString(),
        actorType: 'user',
        resource: 'transaction_history',
        resourceId: telegramUserId.toString(),
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
      throw error;
    }
  }

  /**
   * 获取用户待领取奖励
   */
  async getUserPendingRewards(telegramUserId: number): Promise<PendingReward[]> {
    await this.ensureInitialized();

    try {
      return await this.walletDatabase.getUserPendingRewards(telegramUserId);
    } catch (error) {
      await this.auditLogger.log({
        action: 'pending_rewards_query_failed',
        actor: telegramUserId.toString(),
        actorType: 'user',
        resource: 'pending_rewards',
        resourceId: telegramUserId.toString(),
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
      throw error;
    }
  }

  /**
   * 确保服务已初始化
   */
  private async ensureInitialized(): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }
  }

  /**
   * 生成交易ID
   */
  private generateTransactionId(): string {
    return `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// 创建默认实例
export const walletService = new WalletService();
