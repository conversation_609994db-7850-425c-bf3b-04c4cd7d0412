/**
 * 钱包工具函数
 */

import { ethers } from 'ethers';

/**
 * 验证BSC地址格式
 */
export function validateBSCAddress(address: string): boolean {
  try {
    return ethers.isAddress(address);
  } catch {
    return false;
  }
}

/**
 * 格式化代币金额显示
 */
export function formatTokenAmount(
  amount: string | number,
  decimals: number = 18,
  displayDecimals: number = 6
): string {
  try {
    const value = typeof amount === 'string' ? amount : amount.toString();
    const formatted = ethers.formatUnits(value, decimals);
    const num = parseFloat(formatted);
    
    if (num === 0) return '0';
    if (num < 0.000001) return '< 0.000001';
    
    return num.toFixed(displayDecimals).replace(/\.?0+$/, '');
  } catch {
    return '0';
  }
}

/**
 * 解析代币金额为最小单位
 */
export function parseTokenAmount(amount: string, decimals: number = 18): string {
  try {
    return ethers.parseUnits(amount, decimals).toString();
  } catch {
    return '0';
  }
}

/**
 * 格式化地址显示
 */
export function formatAddress(address: string, startLength: number = 6, endLength: number = 4): string {
  if (!address || address.length < startLength + endLength) {
    return address;
  }
  
  return `${address.slice(0, startLength)}...${address.slice(-endLength)}`;
}

/**
 * 生成随机助记词
 */
export function generateMnemonic(): string {
  return ethers.Wallet.createRandom().mnemonic?.phrase || '';
}

/**
 * 验证助记词
 */
export function validateMnemonic(mnemonic: string): boolean {
  try {
    ethers.Wallet.fromPhrase(mnemonic);
    return true;
  } catch {
    return false;
  }
}

/**
 * 从助记词创建钱包
 */
export function createWalletFromMnemonic(mnemonic: string, index: number = 0): ethers.Wallet {
  const hdNode = ethers.HDNodeWallet.fromPhrase(mnemonic);
  return hdNode.deriveChild(index);
}

/**
 * 计算交易哈希
 */
export function calculateTransactionHash(transaction: any): string {
  try {
    return ethers.keccak256(ethers.toUtf8Bytes(JSON.stringify(transaction)));
  } catch {
    return '';
  }
}

/**
 * 验证私钥格式
 */
export function validatePrivateKey(privateKey: string): boolean {
  try {
    new ethers.Wallet(privateKey);
    return true;
  } catch {
    return false;
  }
}

/**
 * 格式化Gas价格
 */
export function formatGasPrice(gasPrice: string | number, unit: 'wei' | 'gwei' = 'gwei'): string {
  try {
    const value = typeof gasPrice === 'string' ? gasPrice : gasPrice.toString();
    
    if (unit === 'gwei') {
      const gwei = ethers.formatUnits(value, 'gwei');
      return parseFloat(gwei).toFixed(2);
    }
    
    return value;
  } catch {
    return '0';
  }
}

/**
 * 计算交易费用
 */
export function calculateTransactionFee(gasLimit: string, gasPrice: string): string {
  try {
    const fee = BigInt(gasLimit) * BigInt(gasPrice);
    return ethers.formatEther(fee.toString());
  } catch {
    return '0';
  }
}

/**
 * 检查是否为合约地址
 */
export async function isContractAddress(address: string, provider: ethers.Provider): Promise<boolean> {
  try {
    const code = await provider.getCode(address);
    return code !== '0x';
  } catch {
    return false;
  }
}

/**
 * 生成随机交易ID
 */
export function generateTransactionId(): string {
  return `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * 验证交易签名
 */
export function verifyTransactionSignature(
  transaction: any,
  signature: string,
  expectedSigner: string
): boolean {
  try {
    const messageHash = calculateTransactionHash(transaction);
    const recoveredAddress = ethers.verifyMessage(messageHash, signature);
    return recoveredAddress.toLowerCase() === expectedSigner.toLowerCase();
  } catch {
    return false;
  }
}

/**
 * 格式化时间戳
 */
export function formatTimestamp(timestamp: number): string {
  try {
    return new Date(timestamp * 1000).toLocaleString();
  } catch {
    return 'Invalid Date';
  }
}

/**
 * 计算百分比变化
 */
export function calculatePercentageChange(oldValue: number, newValue: number): number {
  if (oldValue === 0) return 0;
  return ((newValue - oldValue) / oldValue) * 100;
}

/**
 * 安全的数字转换
 */
export function safeParseFloat(value: string | number, defaultValue: number = 0): number {
  try {
    const num = typeof value === 'string' ? parseFloat(value) : value;
    return isNaN(num) ? defaultValue : num;
  } catch {
    return defaultValue;
  }
}

/**
 * 延迟函数
 */
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 重试函数
 */
export async function retry<T>(
  fn: () => Promise<T>,
  maxAttempts: number = 3,
  delayMs: number = 1000
): Promise<T> {
  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      
      if (attempt === maxAttempts) {
        throw lastError;
      }
      
      await delay(delayMs * attempt);
    }
  }
  
  throw lastError!;
}
