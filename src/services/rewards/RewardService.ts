/**
 * 奖励管理服务
 * 处理用户奖励的生成、分发和领取
 */

import { PendingReward, Transaction } from '@/services/wallet/types';
import { WalletDatabase } from '@/services/database/WalletDatabase';
import { WalletService } from '@/services/wallet/WalletService';
import { AuditLogger } from '@/services/wallet/AuditLogger';

export interface RewardConfig {
  dailySignInReward: string;
  referralReward: string;
  socialTaskReward: string;
  tradingReward: string;
  stakingReward: string;
  maxDailyRewards: string;
}

export interface RewardSource {
  type: 'social_task' | 'referral' | 'trading' | 'staking' | 'daily_signin' | 'special_event';
  description: string;
  amount: string;
  metadata?: Record<string, any>;
}

export class RewardService {
  private walletDatabase: WalletDatabase;
  private walletService: WalletService;
  private auditLogger: AuditLogger;
  private config: RewardConfig;

  constructor(
    walletDatabase: WalletDatabase,
    walletService: WalletService,
    auditLogger: AuditLogger
  ) {
    this.walletDatabase = walletDatabase;
    this.walletService = walletService;
    this.auditLogger = auditLogger;
    
    // 默认奖励配置
    this.config = {
      dailySignInReward: '10',
      referralReward: '50',
      socialTaskReward: '25',
      tradingReward: '5',
      stakingReward: '100',
      maxDailyRewards: '500',
    };
  }

  /**
   * 创建待领取奖励
   */
  async createPendingReward(
    telegramUserId: number,
    rewardSource: RewardSource
  ): Promise<PendingReward> {
    try {
      // 验证奖励金额
      await this.validateRewardAmount(telegramUserId, rewardSource.amount, rewardSource.type);

      const reward: PendingReward = {
        id: this.generateRewardId(),
        telegramUserId,
        amount: rewardSource.amount,
        source: rewardSource.type,
        description: rewardSource.description,
        createdAt: new Date(),
      };

      // 保存到数据库
      await this.walletDatabase.savePendingReward(reward);

      await this.auditLogger.log({
        action: 'reward_created',
        actor: telegramUserId.toString(),
        actorType: 'user',
        resource: 'reward',
        resourceId: reward.id,
        details: {
          amount: reward.amount,
          source: reward.source,
          description: reward.description,
          metadata: rewardSource.metadata,
        },
      });

      return reward;
    } catch (error) {
      await this.auditLogger.log({
        action: 'reward_creation_failed',
        actor: telegramUserId.toString(),
        actorType: 'user',
        resource: 'reward',
        resourceId: 'unknown',
        details: {
          rewardSource,
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
      throw error;
    }
  }

  /**
   * 领取单个奖励
   */
  async claimReward(
    telegramUserId: number,
    rewardId: string
  ): Promise<Transaction> {
    try {
      // 获取待领取奖励
      const rewards = await this.walletDatabase.getUserPendingRewards(telegramUserId);
      const reward = rewards.find(r => r.id === rewardId);

      if (!reward) {
        throw new Error('Reward not found or already claimed');
      }

      // 获取用户钱包
      const wallet = await this.walletDatabase.getWalletByTelegramUserId(telegramUserId);
      if (!wallet) {
        throw new Error('User wallet not found');
      }

      // 创建奖励领取交易
      const transaction: Transaction = {
        id: this.generateTransactionId(),
        fromAddress: '******************************************', // 系统地址
        toAddress: wallet.address,
        amount: reward.amount,
        tokenType: 'HAOX',
        status: 'confirmed', // 奖励直接确认
        type: 'reward_claim',
        createdAt: new Date(),
        confirmedAt: new Date(),
        metadata: {
          rewardId: reward.id,
          rewardSource: reward.source,
          rewardDescription: reward.description,
        },
      };

      // 保存交易记录
      await this.walletDatabase.saveTransaction(transaction);

      // 标记奖励为已领取
      await this.walletDatabase.claimReward(rewardId);

      await this.auditLogger.log({
        action: 'reward_claimed',
        actor: telegramUserId.toString(),
        actorType: 'user',
        resource: 'reward_claim',
        resourceId: rewardId,
        details: {
          amount: reward.amount,
          source: reward.source,
          transactionId: transaction.id,
          walletAddress: wallet.address,
        },
      });

      return transaction;
    } catch (error) {
      await this.auditLogger.log({
        action: 'reward_claim_failed',
        actor: telegramUserId.toString(),
        actorType: 'user',
        resource: 'reward_claim',
        resourceId: rewardId,
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
      throw error;
    }
  }

  /**
   * 批量领取奖励
   */
  async claimMultipleRewards(
    telegramUserId: number,
    rewardIds: string[]
  ): Promise<Transaction[]> {
    try {
      const transactions: Transaction[] = [];

      for (const rewardId of rewardIds) {
        const transaction = await this.claimReward(telegramUserId, rewardId);
        transactions.push(transaction);
      }

      await this.auditLogger.log({
        action: 'multiple_rewards_claimed',
        actor: telegramUserId.toString(),
        actorType: 'user',
        resource: 'batch_reward_claim',
        resourceId: `batch_${Date.now()}`,
        details: {
          rewardIds,
          transactionCount: transactions.length,
          totalAmount: transactions.reduce((sum, tx) => sum + parseFloat(tx.amount), 0).toString(),
        },
      });

      return transactions;
    } catch (error) {
      await this.auditLogger.log({
        action: 'batch_reward_claim_failed',
        actor: telegramUserId.toString(),
        actorType: 'user',
        resource: 'batch_reward_claim',
        resourceId: 'unknown',
        details: {
          rewardIds,
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
      throw error;
    }
  }

  /**
   * 领取所有待领取奖励
   */
  async claimAllRewards(telegramUserId: number): Promise<Transaction[]> {
    try {
      const pendingRewards = await this.walletDatabase.getUserPendingRewards(telegramUserId);
      const rewardIds = pendingRewards.map(reward => reward.id);

      if (rewardIds.length === 0) {
        return [];
      }

      return await this.claimMultipleRewards(telegramUserId, rewardIds);
    } catch (error) {
      await this.auditLogger.log({
        action: 'claim_all_rewards_failed',
        actor: telegramUserId.toString(),
        actorType: 'user',
        resource: 'claim_all_rewards',
        resourceId: 'all',
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
      throw error;
    }
  }

  /**
   * 每日签到奖励
   */
  async processDailySignIn(telegramUserId: number): Promise<PendingReward | null> {
    try {
      // 检查今天是否已经签到
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      const existingRewards = await this.walletDatabase.getUserPendingRewards(telegramUserId);
      const todaySignIn = existingRewards.find(reward => 
        reward.source === 'daily_signin' && 
        reward.createdAt >= today
      );

      if (todaySignIn) {
        return null; // 今天已经签到过了
      }

      // 创建每日签到奖励
      return await this.createPendingReward(telegramUserId, {
        type: 'daily_signin',
        description: '每日签到奖励',
        amount: this.config.dailySignInReward,
        metadata: {
          signInDate: today.toISOString(),
        },
      });
    } catch (error) {
      await this.auditLogger.log({
        action: 'daily_signin_failed',
        actor: telegramUserId.toString(),
        actorType: 'user',
        resource: 'daily_signin',
        resourceId: telegramUserId.toString(),
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
      throw error;
    }
  }

  /**
   * 推荐奖励
   */
  async processReferralReward(
    referrerId: number,
    referredUserId: number
  ): Promise<PendingReward> {
    try {
      return await this.createPendingReward(referrerId, {
        type: 'referral',
        description: `邀请用户 ${referredUserId} 的奖励`,
        amount: this.config.referralReward,
        metadata: {
          referredUserId,
          referralDate: new Date().toISOString(),
        },
      });
    } catch (error) {
      await this.auditLogger.log({
        action: 'referral_reward_failed',
        actor: referrerId.toString(),
        actorType: 'user',
        resource: 'referral_reward',
        resourceId: `${referrerId}_${referredUserId}`,
        details: {
          referrerId,
          referredUserId,
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
      throw error;
    }
  }

  /**
   * 验证奖励金额
   */
  private async validateRewardAmount(
    telegramUserId: number,
    amount: string,
    rewardType: string
  ): Promise<void> {
    const rewardAmount = parseFloat(amount);
    
    if (isNaN(rewardAmount) || rewardAmount <= 0) {
      throw new Error('Invalid reward amount');
    }

    // 检查每日奖励限额
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const todayRewards = await this.walletDatabase.getUserPendingRewards(telegramUserId);
    const todayTotal = todayRewards
      .filter(reward => reward.createdAt >= today)
      .reduce((sum, reward) => sum + parseFloat(reward.amount), 0);

    const maxDaily = parseFloat(this.config.maxDailyRewards);
    if (todayTotal + rewardAmount > maxDaily) {
      throw new Error(`Daily reward limit exceeded. Max: ${maxDaily}, Current: ${todayTotal}`);
    }
  }

  /**
   * 生成奖励ID
   */
  private generateRewardId(): string {
    return `reward_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 生成交易ID
   */
  private generateTransactionId(): string {
    return `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// 导出单例实例
export const rewardService = new RewardService();
