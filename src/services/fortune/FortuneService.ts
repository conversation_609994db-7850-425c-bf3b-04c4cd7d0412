/**
 * 福气系统服务
 * 处理所有福气相关的业务逻辑，包括奖励发放、余额管理、等级计算等
 */

import { supabase } from '@/lib/supabase';

// 福气奖励配置
export const FORTUNE_REWARDS = {
  // 邀请奖励
  INVITE_BASE: 1000,        // 每邀请1人：1000福气
  INVITE_MILESTONE_5: 10000, // 邀请满5人：10000福气
  INVITE_MILESTONE_10: 50000, // 邀请满10人：50000福气
  
  // 日常奖励
  DAILY_CHECKIN: 10,        // 每日签到：10福气
  CONSECUTIVE_BONUS: 5,     // 连续签到奖励：额外5福气
  
  // 新用户奖励
  REGISTRATION: 50,         // 注册奖励：50福气
  FIRST_DEPOSIT: 100,       // 首次充值：100福气
  
  // 其他活动奖励
  SHARE_REWARD: 20,         // 分享赌约：20福气
  COMMENT_REWARD: 5,        // 参与讨论：5福气
} as const;

// 福气等级配置
export const FORTUNE_LEVELS = {
  1: { name: "初来乍到", min: 0, max: 999.******** },
  2: { name: "小有福气", min: 1000, max: 9999.******** },
  3: { name: "福气满满", min: 10000, max: 99999.******** },
  4: { name: "福星高照", min: 100000, max: 999999.******** },
  5: { name: "福气无边", min: 1000000, max: null }
} as const;

export interface FortuneAccount {
  id: string; // 使用users表的id字段
  available_fortune: number;
  locked_fortune: number;
  total_fortune_earned: number;
  total_fortune_spent: number;
  fortune_level: number;
  fortune_level_name: string;
  consecutive_checkin_days: number;
  last_checkin_date: string | null;
  total_checkin_days: number;
  created_at: string;
  updated_at: string;
}

export interface FortuneTransaction {
  id: string;
  user_id: string;
  transaction_type: string;
  amount: number;
  balance_before: number;
  balance_after: number;
  reference_id?: string;
  reference_type?: string;
  description: string;
  created_at: string;
}

export class FortuneService {
  /**
   * 获取用户福气账户信息
   */
  static async getUserFortune(userId: string): Promise<FortuneAccount | null> {
    const { data, error } = await supabase
      .from('users')
      .select(`
        id,
        available_fortune,
        locked_fortune,
        total_fortune_earned,
        total_fortune_spent,
        fortune_level,
        fortune_level_name,
        consecutive_checkin_days,
        last_checkin_date,
        total_checkin_days,
        created_at,
        updated_at
      `)
      .eq('id', userId)
      .single();

    if (error && error.code !== 'PGRST116') {
      throw new Error(`Failed to fetch user fortune: ${error.message}`);
    }

    return data;
  }

  /**
   * 初始化用户福气账户（更新现有用户记录）
   */
  static async initializeUserFortune(userId: string): Promise<FortuneAccount> {
    const { data, error } = await supabase
      .from('users')
      .update({
        available_fortune: 0,
        locked_fortune: 0,
        total_fortune_earned: 0,
        total_fortune_spent: 0,
        fortune_level: 1,
        fortune_level_name: '初来乍到',
        consecutive_checkin_days: 0,
        total_checkin_days: 0,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId)
      .select(`
        id,
        available_fortune,
        locked_fortune,
        total_fortune_earned,
        total_fortune_spent,
        fortune_level,
        fortune_level_name,
        consecutive_checkin_days,
        last_checkin_date,
        total_checkin_days,
        created_at,
        updated_at
      `)
      .single();

    if (error) {
      throw new Error(`Failed to initialize user fortune: ${error.message}`);
    }

    return data;
  }

  /**
   * 添加用户福气
   */
  static async addFortune(
    userId: string,
    amount: number,
    transactionType: string,
    referenceId?: string,
    description?: string
  ): Promise<string> {
    if (amount <= 0) {
      throw new Error('Amount must be positive');
    }

    // 获取或初始化用户福气账户
    let userFortune = await this.getUserFortune(userId);
    if (!userFortune || userFortune.available_fortune === undefined) {
      userFortune = await this.initializeUserFortune(userId);
    }

    const balanceBefore = userFortune.available_fortune;
    const balanceAfter = balanceBefore + amount;

    // 生成交易ID
    const transactionId = crypto.randomUUID();

    // 更新用户福气余额
    const { error: updateError } = await supabase
      .from('users')
      .update({
        available_fortune: balanceAfter,
        total_fortune_earned: userFortune.total_fortune_earned + amount,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId);

    if (updateError) {
      throw new Error(`Failed to update fortune balance: ${updateError.message}`);
    }

    // 记录交易
    const { error: transactionError } = await supabase
      .from('fortune_transactions')
      .insert({
        id: transactionId,
        user_id: userId,
        transaction_type: transactionType,
        amount: amount,
        balance_before: balanceBefore,
        balance_after: balanceAfter,
        reference_id: referenceId,
        description: description || `${transactionType} +${amount}福气`,
        created_at: new Date().toISOString()
      });

    if (transactionError) {
      throw new Error(`Failed to record transaction: ${transactionError.message}`);
    }

    // 更新用户福气等级
    await this.updateFortuneLevel(userId);

    return transactionId;
  }

  /**
   * 扣除用户福气
   */
  static async deductFortune(
    userId: string,
    amount: number,
    transactionType: string,
    referenceId?: string,
    description?: string
  ): Promise<string> {
    if (amount <= 0) {
      throw new Error('Amount must be positive');
    }

    const userFortune = await this.getUserFortune(userId);
    if (!userFortune) {
      throw new Error('User fortune account not found');
    }

    if (userFortune.available_fortune < amount) {
      throw new Error('Insufficient fortune balance');
    }

    const balanceBefore = userFortune.available_fortune;
    const balanceAfter = balanceBefore - amount;

    // 生成交易ID
    const transactionId = crypto.randomUUID();

    // 更新用户福气余额
    const { error: updateError } = await supabase
      .from('users')
      .update({
        available_fortune: balanceAfter,
        total_fortune_spent: userFortune.total_fortune_spent + amount,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId);

    if (updateError) {
      throw new Error(`Failed to update fortune balance: ${updateError.message}`);
    }

    // 记录交易（负数表示支出）
    const { error: transactionError } = await supabase
      .from('fortune_transactions')
      .insert({
        id: transactionId,
        user_id: userId,
        transaction_type: transactionType,
        amount: -amount, // 负数表示支出
        balance_before: balanceBefore,
        balance_after: balanceAfter,
        reference_id: referenceId,
        description: description || `${transactionType} -${amount}福气`,
        created_at: new Date().toISOString()
      });

    if (transactionError) {
      throw new Error(`Failed to record transaction: ${transactionError.message}`);
    }

    return transactionId;
  }

  /**
   * 更新用户福气等级
   */
  static async updateFortuneLevel(userId: string): Promise<void> {
    const userFortune = await this.getUserFortune(userId);
    if (!userFortune) return;

    const totalFortune = userFortune.available_fortune + userFortune.locked_fortune;
    
    let newLevel = 1;
    let newLevelName = '初来乍到';

    for (const [level, config] of Object.entries(FORTUNE_LEVELS)) {
      const levelNum = parseInt(level);
      if (totalFortune >= config.min && (config.max === null || totalFortune <= config.max)) {
        newLevel = levelNum;
        newLevelName = config.name;
      }
    }

    if (newLevel !== userFortune.fortune_level) {
      const { error } = await supabase
        .from('users')
        .update({
          fortune_level: newLevel,
          fortune_level_name: newLevelName,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      if (error) {
        console.error('Failed to update fortune level:', error);
      }
    }
  }

  /**
   * 处理邀请奖励
   */
  static async processInviteReward(inviterId: string, inviteeId: string): Promise<{
    baseReward: number;
    milestoneReward: number;
    totalInvitations: number;
    nextMilestone?: { target: number; reward: number };
  }> {
    // 基础邀请奖励
    const baseReward = FORTUNE_REWARDS.INVITE_BASE;
    await this.addFortune(
      inviterId,
      baseReward,
      'invite_reward',
      inviteeId,
      `邀请好友奖励 +${baseReward}福气`
    );

    // 更新现有的邀请统计表
    const { data: inviteStats, error } = await supabase
      .from('invitation_stats')
      .select('successful_invitations')
      .eq('user_id', inviterId)
      .single();

    let newInviteCount = 1;

    if (inviteStats) {
      // 更新现有记录
      newInviteCount = inviteStats.successful_invitations + 1;
      await supabase
        .from('invitation_stats')
        .update({
          successful_invitations: newInviteCount,
          fortune_reward_processed: true,
          fortune_reward_amount: baseReward,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', inviterId);
    } else {
      // 创建新记录
      await supabase
        .from('invitation_stats')
        .insert({
          user_id: inviterId,
          successful_invitations: 1,
          total_rewards: baseReward,
          fortune_reward_processed: true,
          fortune_reward_amount: baseReward
        });
    }

    // 检查里程碑奖励
    let milestoneReward = 0;
    if (newInviteCount === 5) {
      milestoneReward = FORTUNE_REWARDS.INVITE_MILESTONE_5;
      await this.addFortune(
        inviterId,
        milestoneReward,
        'invite_milestone_5',
        undefined,
        `邀请5人里程碑奖励 +${milestoneReward}福气`
      );
    } else if (newInviteCount === 10) {
      milestoneReward = FORTUNE_REWARDS.INVITE_MILESTONE_10;
      await this.addFortune(
        inviterId,
        milestoneReward,
        'invite_milestone_10',
        undefined,
        `邀请10人里程碑奖励 +${milestoneReward}福气`
      );
    }

    // 确定下一个里程碑
    let nextMilestone;
    if (newInviteCount < 5) {
      nextMilestone = { target: 5, reward: FORTUNE_REWARDS.INVITE_MILESTONE_5 };
    } else if (newInviteCount < 10) {
      nextMilestone = { target: 10, reward: FORTUNE_REWARDS.INVITE_MILESTONE_10 };
    }

    return {
      baseReward,
      milestoneReward,
      totalInvitations: newInviteCount,
      nextMilestone
    };
  }

  /**
   * 获取用户福气交易历史
   */
  static async getFortuneTransactions(
    userId: string,
    limit: number = 20,
    offset: number = 0,
    type?: string
  ): Promise<FortuneTransaction[]> {
    let query = supabase
      .from('fortune_transactions')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (type) {
      query = query.eq('transaction_type', type);
    }

    const { data, error } = await query;

    if (error) {
      throw new Error(`Failed to fetch transactions: ${error.message}`);
    }

    return data || [];
  }
}
