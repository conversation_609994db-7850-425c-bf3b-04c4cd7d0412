/**
 * 福气奖励管理服务
 * 处理所有基于福气系统的奖励逻辑，替代原有的HAOX奖励系统
 */

import { FortuneService, FORTUNE_REWARDS } from './FortuneService';
import { supabase } from '@/lib/supabase';

export interface FortuneRewardConfig {
  dailySignInReward: number;
  referralReward: number;
  socialTaskReward: number;
  shareReward: number;
  commentReward: number;
  maxDailyRewards: number;
}

export interface FortuneRewardSource {
  type: 'daily_checkin' | 'invite_reward' | 'share_reward' | 'comment_reward' | 'registration_bonus' | 'first_deposit_bonus' | 'special_event';
  description: string;
  amount: number;
  metadata?: Record<string, any>;
}

export interface DailyCheckInResult {
  baseReward: number;
  bonusReward: number;
  totalReward: number;
  consecutiveDays: number;
  nextCheckIn: string;
}

export class FortuneRewardService {
  private config: FortuneRewardConfig;

  constructor() {
    // 福气奖励配置
    this.config = {
      dailySignInReward: FORTUNE_REWARDS.DAILY_CHECKIN,
      referralReward: FORTUNE_REWARDS.INVITE_BASE,
      socialTaskReward: FORTUNE_REWARDS.SHARE_REWARD,
      shareReward: FORTUNE_REWARDS.SHARE_REWARD,
      commentReward: FORTUNE_REWARDS.COMMENT_REWARD,
      maxDailyRewards: 1000, // 每日最大福气奖励
    };
  }

  /**
   * 处理每日签到奖励
   */
  async processDailyCheckIn(userId: string): Promise<DailyCheckInResult> {
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD格式

    // 检查今日是否已签到
    const { data: todayCheckIn } = await supabase
      .from('daily_checkins')
      .select('id')
      .eq('user_id', userId)
      .eq('checkin_date', today)
      .single();

    if (todayCheckIn) {
      throw new Error('今日已签到，请明日再来！');
    }

    // 获取用户福气账户
    let userFortune = await FortuneService.getUserFortune(userId);
    if (!userFortune) {
      userFortune = await FortuneService.createUserFortune(userId);
    }

    // 计算连续签到天数
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayStr = yesterday.toISOString().split('T')[0];

    let consecutiveDays = 1;
    if (userFortune.last_checkin_date === yesterdayStr) {
      consecutiveDays = userFortune.consecutive_checkin_days + 1;
    }

    // 计算奖励
    const baseReward = this.config.dailySignInReward;
    const bonusReward = consecutiveDays >= 7 ? FORTUNE_REWARDS.CONSECUTIVE_BONUS : 0;
    const totalReward = baseReward + bonusReward;

    // 发放福气奖励
    const transactionId = await FortuneService.addFortune(
      userId,
      totalReward,
      'daily_checkin',
      undefined,
      `每日签到奖励 +${totalReward}福气${bonusReward > 0 ? ' (含连续签到奖励)' : ''}`
    );

    // 记录签到
    const { error: checkInError } = await supabase
      .from('daily_checkins')
      .insert({
        id: crypto.randomUUID(),
        user_id: userId,
        checkin_date: today,
        consecutive_days: consecutiveDays,
        base_reward: baseReward,
        bonus_reward: bonusReward,
        total_reward: totalReward,
        fortune_transaction_id: transactionId,
        created_at: new Date().toISOString()
      });

    if (checkInError) {
      throw new Error(`Failed to record check-in: ${checkInError.message}`);
    }

    // 更新用户签到统计
    await supabase
      .from('user_fortune')
      .update({
        consecutive_checkin_days: consecutiveDays,
        last_checkin_date: today,
        total_checkin_days: userFortune.total_checkin_days + 1,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId);

    // 计算下次签到时间
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);

    return {
      baseReward,
      bonusReward,
      totalReward,
      consecutiveDays,
      nextCheckIn: tomorrow.toISOString()
    };
  }

  /**
   * 处理分享奖励
   */
  async processShareReward(
    userId: string,
    contentType: string,
    contentId: string,
    platform: string
  ): Promise<number> {
    // 检查是否已分享过此内容到此平台
    const { data: existingShare } = await supabase
      .from('share_rewards')
      .select('id')
      .eq('user_id', userId)
      .eq('shared_content_id', contentId)
      .eq('platform', platform)
      .single();

    if (existingShare) {
      throw new Error('已分享过此内容到该平台');
    }

    const shareReward = this.config.shareReward;

    // 发放福气奖励
    const transactionId = await FortuneService.addFortune(
      userId,
      shareReward,
      'share_reward',
      contentId,
      `分享${contentType}奖励 +${shareReward}福气`
    );

    // 记录分享
    await supabase
      .from('share_rewards')
      .insert({
        id: crypto.randomUUID(),
        user_id: userId,
        shared_content_type: contentType,
        shared_content_id: contentId,
        platform: platform,
        reward_amount: shareReward,
        fortune_transaction_id: transactionId,
        created_at: new Date().toISOString()
      });

    return shareReward;
  }

  /**
   * 处理评论奖励
   */
  async processCommentReward(
    userId: string,
    contentId: string,
    commentId: string,
    commentText: string
  ): Promise<number> {
    // 验证评论质量（简单的长度检查）
    if (!this.isValidComment(commentText)) {
      throw new Error('评论内容不符合奖励标准');
    }

    // 检查是否已对此内容获得过评论奖励（防止刷奖励）
    const { data: existingReward } = await supabase
      .from('fortune_transactions')
      .select('id')
      .eq('user_id', userId)
      .eq('transaction_type', 'comment_reward')
      .eq('reference_id', contentId)
      .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()) // 24小时内
      .single();

    if (existingReward) {
      throw new Error('24小时内已对此内容获得过评论奖励');
    }

    const commentReward = this.config.commentReward;

    // 发放福气奖励
    await FortuneService.addFortune(
      userId,
      commentReward,
      'comment_reward',
      commentId,
      `参与讨论奖励 +${commentReward}福气`
    );

    return commentReward;
  }

  /**
   * 处理新用户注册奖励
   */
  async processRegistrationBonus(userId: string): Promise<number> {
    // 检查是否已发放过注册奖励
    const { data: existingBonus } = await supabase
      .from('fortune_transactions')
      .select('id')
      .eq('user_id', userId)
      .eq('transaction_type', 'registration_bonus')
      .single();

    if (existingBonus) {
      throw new Error('Registration bonus already processed');
    }

    const registrationReward = FORTUNE_REWARDS.REGISTRATION;

    // 发放福气奖励
    await FortuneService.addFortune(
      userId,
      registrationReward,
      'registration_bonus',
      undefined,
      `新用户注册奖励 +${registrationReward}福气`
    );

    return registrationReward;
  }

  /**
   * 处理首次充值奖励
   */
  async processFirstDepositBonus(userId: string, depositAmount: number): Promise<number> {
    // 检查是否已发放过首充奖励
    const { data: existingBonus } = await supabase
      .from('fortune_transactions')
      .select('id')
      .eq('user_id', userId)
      .eq('transaction_type', 'first_deposit_bonus')
      .single();

    if (existingBonus) {
      throw new Error('First deposit bonus already processed');
    }

    const firstDepositReward = FORTUNE_REWARDS.FIRST_DEPOSIT;

    // 发放福气奖励
    await FortuneService.addFortune(
      userId,
      firstDepositReward,
      'first_deposit_bonus',
      undefined,
      `首次充值奖励 +${firstDepositReward}福气`
    );

    return firstDepositReward;
  }

  /**
   * 获取用户今日已获得的奖励总额
   */
  async getTodayRewards(userId: string): Promise<number> {
    const today = new Date().toISOString().split('T')[0];
    
    const { data: todayTransactions } = await supabase
      .from('fortune_transactions')
      .select('amount')
      .eq('user_id', userId)
      .gte('created_at', `${today}T00:00:00.000Z`)
      .lt('created_at', `${today}T23:59:59.999Z`)
      .gt('amount', 0); // 只计算收入

    return todayTransactions?.reduce((sum, tx) => sum + tx.amount, 0) || 0;
  }

  /**
   * 验证评论质量
   */
  private isValidComment(commentText: string): boolean {
    // 简单的评论质量检查
    if (!commentText || commentText.trim().length < 10) {
      return false;
    }

    // 检查是否包含垃圾内容
    const spamKeywords = ['刷', '水', '顶', '沙发', '板凳'];
    const lowerText = commentText.toLowerCase();
    
    for (const keyword of spamKeywords) {
      if (lowerText.includes(keyword)) {
        return false;
      }
    }

    return true;
  }

  /**
   * 获取奖励配置
   */
  getConfig(): FortuneRewardConfig {
    return { ...this.config };
  }

  /**
   * 更新奖励配置
   */
  updateConfig(newConfig: Partial<FortuneRewardConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }
}
