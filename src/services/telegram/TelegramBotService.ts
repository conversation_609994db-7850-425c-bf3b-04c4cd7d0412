/**
 * Telegram Bot API 服务
 * 处理Telegram Bot相关的API调用和用户验证
 */

import crypto from 'crypto';

export interface TelegramUser {
  id: number;
  first_name: string;
  last_name?: string;
  username?: string;
  photo_url?: string;
  auth_date: number;
  hash: string;
}

export interface TelegramAuthData {
  id: string;
  first_name: string;
  last_name?: string;
  username?: string;
  photo_url?: string;
  auth_date: string;
  hash: string;
}

export interface TelegramBotConfig {
  botToken: string;
  botUsername: string;
  webhookUrl?: string;
}

export class TelegramBotService {
  private botToken: string;
  private botUsername: string;
  private apiUrl: string;

  constructor(config: TelegramBotConfig) {
    this.botToken = config.botToken;
    this.botUsername = config.botUsername;
    this.apiUrl = `https://api.telegram.org/bot${this.botToken}`;
  }

  /**
   * 验证Telegram登录数据的真实性
   */
  verifyTelegramAuth(authData: TelegramAuthData): boolean {
    try {
      const { hash, ...data } = authData;
      
      // 创建数据字符串
      const dataCheckString = Object.keys(data)
        .sort()
        .map(key => `${key}=${data[key as keyof typeof data]}`)
        .join('\n');

      // 创建密钥
      const secretKey = crypto
        .createHmac('sha256', 'WebAppData')
        .update(this.botToken)
        .digest();

      // 计算哈希
      const calculatedHash = crypto
        .createHmac('sha256', secretKey)
        .update(dataCheckString)
        .digest('hex');

      // 验证哈希
      return calculatedHash === hash;
    } catch (error) {
      console.error('Telegram auth verification failed:', error);
      return false;
    }
  }

  /**
   * 获取Bot信息
   */
  async getBotInfo(): Promise<any> {
    try {
      const response = await fetch(`${this.apiUrl}/getMe`);
      const data = await response.json();
      
      if (!data.ok) {
        throw new Error(`Telegram API error: ${data.description}`);
      }
      
      return data.result;
    } catch (error) {
      console.error('Failed to get bot info:', error);
      throw error;
    }
  }

  /**
   * 检查用户是否是频道成员
   */
  async checkChannelMembership(userId: number, channelId: string): Promise<boolean> {
    try {
      const response = await fetch(
        `${this.apiUrl}/getChatMember?chat_id=${channelId}&user_id=${userId}`
      );
      const data = await response.json();
      
      if (!data.ok) {
        return false;
      }
      
      const status = data.result.status;
      return ['creator', 'administrator', 'member'].includes(status);
    } catch (error) {
      console.error('Failed to check channel membership:', error);
      return false;
    }
  }

  /**
   * 发送消息给用户
   */
  async sendMessage(chatId: number, text: string, options?: any): Promise<any> {
    try {
      const payload = {
        chat_id: chatId,
        text,
        parse_mode: 'HTML',
        ...options,
      };

      const response = await fetch(`${this.apiUrl}/sendMessage`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const data = await response.json();
      
      if (!data.ok) {
        throw new Error(`Failed to send message: ${data.description}`);
      }
      
      return data.result;
    } catch (error) {
      console.error('Failed to send message:', error);
      throw error;
    }
  }

  /**
   * 设置Webhook
   */
  async setWebhook(webhookUrl: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.apiUrl}/setWebhook`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          url: webhookUrl,
          allowed_updates: ['message', 'callback_query'],
        }),
      });

      const data = await response.json();
      return data.ok;
    } catch (error) {
      console.error('Failed to set webhook:', error);
      return false;
    }
  }

  /**
   * 删除Webhook
   */
  async deleteWebhook(): Promise<boolean> {
    try {
      const response = await fetch(`${this.apiUrl}/deleteWebhook`, {
        method: 'POST',
      });

      const data = await response.json();
      return data.ok;
    } catch (error) {
      console.error('Failed to delete webhook:', error);
      return false;
    }
  }

  /**
   * 获取频道信息
   */
  async getChat(chatId: string): Promise<any> {
    try {
      const response = await fetch(`${this.apiUrl}/getChat?chat_id=${chatId}`);
      const data = await response.json();
      
      if (!data.ok) {
        throw new Error(`Failed to get chat info: ${data.description}`);
      }
      
      return data.result;
    } catch (error) {
      console.error('Failed to get chat info:', error);
      throw error;
    }
  }

  /**
   * 创建邀请链接
   */
  async createChatInviteLink(chatId: string, options?: {
    name?: string;
    expire_date?: number;
    member_limit?: number;
    creates_join_request?: boolean;
  }): Promise<string> {
    try {
      const response = await fetch(`${this.apiUrl}/createChatInviteLink`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          chat_id: chatId,
          ...options,
        }),
      });

      const data = await response.json();
      
      if (!data.ok) {
        throw new Error(`Failed to create invite link: ${data.description}`);
      }
      
      return data.result.invite_link;
    } catch (error) {
      console.error('Failed to create invite link:', error);
      throw error;
    }
  }

  /**
   * 生成Telegram登录URL
   */
  generateLoginUrl(redirectUrl: string): string {
    const params = new URLSearchParams({
      bot_id: this.botToken.split(':')[0],
      origin: window.location.origin,
      return_to: redirectUrl,
      request_access: 'write',
    });

    return `https://oauth.telegram.org/auth?${params.toString()}`;
  }

  /**
   * 处理Telegram登录回调
   */
  async handleLoginCallback(authData: TelegramAuthData): Promise<TelegramUser | null> {
    try {
      // 验证数据真实性
      if (!this.verifyTelegramAuth(authData)) {
        throw new Error('Invalid Telegram auth data');
      }

      // 检查数据是否过期（5分钟内有效）
      const authDate = parseInt(authData.auth_date);
      const now = Math.floor(Date.now() / 1000);
      if (now - authDate > 300) {
        throw new Error('Auth data expired');
      }

      // 转换为标准用户格式
      const user: TelegramUser = {
        id: parseInt(authData.id),
        first_name: authData.first_name,
        last_name: authData.last_name,
        username: authData.username,
        photo_url: authData.photo_url,
        auth_date: authDate,
        hash: authData.hash,
      };

      return user;
    } catch (error) {
      console.error('Failed to handle login callback:', error);
      return null;
    }
  }
}

/**
 * 创建Telegram Bot服务实例
 */
export function createTelegramBotService(): TelegramBotService {
  const config: TelegramBotConfig = {
    botToken: process.env.NEXT_PUBLIC_TELEGRAM_BOT_TOKEN || '',
    botUsername: process.env.NEXT_PUBLIC_TELEGRAM_BOT_USERNAME || '',
    webhookUrl: process.env.TELEGRAM_WEBHOOK_URL,
  };

  if (!config.botToken || !config.botUsername) {
    throw new Error('Telegram Bot configuration is missing');
  }

  return new TelegramBotService(config);
}

/**
 * 默认Telegram Bot服务实例
 */
export const telegramBotService = createTelegramBotService();
