/**
 * 用户等级和权限管理服务
 * 实现渐进式功能开放和用户成长体系
 */

export interface UserLevel {
  level: number;
  name: string;
  description: string;
  icon: string;
  color: string;
  requirements: {
    minBalance?: string;
    minTransactions?: number;
    minDaysActive?: number;
    completedTasks?: string[];
    referralCount?: number;
    kycVerified?: boolean;
  };
  benefits: {
    dailyRewardMultiplier: number;
    withdrawalLimitMultiplier: number;
    gasSubsidyPercentage: number;
    prioritySupport: boolean;
    exclusiveFeatures: string[];
  };
  nextLevelRequirements?: string[];
}

export interface UserPermission {
  feature: string;
  enabled: boolean;
  reason?: string;
  unlockRequirements?: string[];
}

export interface UserProgress {
  currentLevel: number;
  nextLevel: number;
  progressToNext: number; // 0-100
  totalExperience: number;
  levelUpRewards: {
    haoxAmount: string;
    specialBadge?: string;
    unlockFeatures: string[];
  };
}

export class UserLevelService {
  private levels: UserLevel[] = [
    {
      level: 1,
      name: '新手探索者',
      description: '刚开始您的SocioMint之旅',
      icon: 'user',
      color: '#6B7280',
      requirements: {},
      benefits: {
        dailyRewardMultiplier: 1.0,
        withdrawalLimitMultiplier: 1.0,
        gasSubsidyPercentage: 50,
        prioritySupport: false,
        exclusiveFeatures: [],
      },
      nextLevelRequirements: [
        '完成身份验证',
        '进行首次转账',
        '完成3个社交任务',
      ],
    },
    {
      level: 2,
      name: '活跃用户',
      description: '已熟悉基本功能的用户',
      icon: 'star',
      color: '#10B981',
      requirements: {
        minTransactions: 1,
        completedTasks: ['telegram_join', 'daily_signin'],
        minDaysActive: 3,
      },
      benefits: {
        dailyRewardMultiplier: 1.2,
        withdrawalLimitMultiplier: 1.5,
        gasSubsidyPercentage: 60,
        prioritySupport: false,
        exclusiveFeatures: ['advanced_transfer'],
      },
      nextLevelRequirements: [
        '累计转账金额达到100 HAOX',
        '邀请1位好友',
        '连续签到7天',
      ],
    },
    {
      level: 3,
      name: '资深玩家',
      description: '经验丰富的社区成员',
      icon: 'shield',
      color: '#3B82F6',
      requirements: {
        minBalance: '100',
        minTransactions: 10,
        completedTasks: ['telegram_join', 'twitter_follow', 'daily_signin'],
        minDaysActive: 7,
        referralCount: 1,
      },
      benefits: {
        dailyRewardMultiplier: 1.5,
        withdrawalLimitMultiplier: 2.0,
        gasSubsidyPercentage: 75,
        prioritySupport: true,
        exclusiveFeatures: ['advanced_transfer', 'batch_transfer', 'custom_gas'],
      },
      nextLevelRequirements: [
        '累计转账金额达到1000 HAOX',
        '邀请5位好友',
        '完成KYC验证',
      ],
    },
    {
      level: 4,
      name: '社区领袖',
      description: '社区的重要贡献者',
      icon: 'crown',
      color: '#8B5CF6',
      requirements: {
        minBalance: '1000',
        minTransactions: 50,
        minDaysActive: 30,
        referralCount: 5,
        kycVerified: true,
      },
      benefits: {
        dailyRewardMultiplier: 2.0,
        withdrawalLimitMultiplier: 3.0,
        gasSubsidyPercentage: 90,
        prioritySupport: true,
        exclusiveFeatures: [
          'advanced_transfer',
          'batch_transfer',
          'custom_gas',
          'early_features',
          'community_governance',
        ],
      },
      nextLevelRequirements: [
        '累计转账金额达到10000 HAOX',
        '邀请20位好友',
        '参与社区治理投票',
      ],
    },
    {
      level: 5,
      name: '钻石会员',
      description: '平台的顶级用户',
      icon: 'diamond',
      color: '#F59E0B',
      requirements: {
        minBalance: '10000',
        minTransactions: 200,
        minDaysActive: 90,
        referralCount: 20,
        kycVerified: true,
      },
      benefits: {
        dailyRewardMultiplier: 3.0,
        withdrawalLimitMultiplier: 5.0,
        gasSubsidyPercentage: 100,
        prioritySupport: true,
        exclusiveFeatures: [
          'advanced_transfer',
          'batch_transfer',
          'custom_gas',
          'early_features',
          'community_governance',
          'vip_support',
          'exclusive_events',
        ],
      },
    },
  ];

  /**
   * 获取用户当前等级
   */
  async getUserLevel(userId: number): Promise<UserLevel> {
    try {
      // 获取用户数据
      const userData = await this.getUserData(userId);
      
      // 计算用户等级
      let currentLevel = 1;
      
      for (const level of this.levels) {
        if (this.checkLevelRequirements(userData, level.requirements)) {
          currentLevel = level.level;
        } else {
          break;
        }
      }

      return this.levels.find(l => l.level === currentLevel) || this.levels[0];
    } catch (error) {
      console.error('Failed to get user level:', error);
      return this.levels[0];
    }
  }

  /**
   * 获取用户进度
   */
  async getUserProgress(userId: number): Promise<UserProgress> {
    try {
      const currentLevel = await this.getUserLevel(userId);
      const nextLevel = this.levels.find(l => l.level === currentLevel.level + 1);
      
      if (!nextLevel) {
        return {
          currentLevel: currentLevel.level,
          nextLevel: currentLevel.level,
          progressToNext: 100,
          totalExperience: 1000,
          levelUpRewards: {
            haoxAmount: '0',
            unlockFeatures: [],
          },
        };
      }

      // 计算升级进度
      const userData = await this.getUserData(userId);
      const progress = this.calculateLevelProgress(userData, nextLevel.requirements);

      return {
        currentLevel: currentLevel.level,
        nextLevel: nextLevel.level,
        progressToNext: progress,
        totalExperience: userData.totalExperience || 0,
        levelUpRewards: {
          haoxAmount: (nextLevel.level * 100).toString(),
          specialBadge: `level_${nextLevel.level}_badge`,
          unlockFeatures: nextLevel.benefits.exclusiveFeatures,
        },
      };
    } catch (error) {
      console.error('Failed to get user progress:', error);
      return {
        currentLevel: 1,
        nextLevel: 2,
        progressToNext: 0,
        totalExperience: 0,
        levelUpRewards: {
          haoxAmount: '0',
          unlockFeatures: [],
        },
      };
    }
  }

  /**
   * 检查用户权限
   */
  async getUserPermissions(userId: number): Promise<Record<string, UserPermission>> {
    try {
      const userLevel = await this.getUserLevel(userId);
      const userData = await this.getUserData(userId);

      const permissions: Record<string, UserPermission> = {
        // 基础功能
        basic_transfer: {
          feature: 'basic_transfer',
          enabled: true,
        },
        receive_tokens: {
          feature: 'receive_tokens',
          enabled: true,
        },
        view_balance: {
          feature: 'view_balance',
          enabled: true,
        },
        daily_signin: {
          feature: 'daily_signin',
          enabled: true,
        },

        // 高级功能
        advanced_transfer: {
          feature: 'advanced_transfer',
          enabled: userLevel.benefits.exclusiveFeatures.includes('advanced_transfer'),
          reason: userLevel.benefits.exclusiveFeatures.includes('advanced_transfer') 
            ? undefined 
            : `需要达到${this.getFeatureUnlockLevel('advanced_transfer')}级`,
        },
        batch_transfer: {
          feature: 'batch_transfer',
          enabled: userLevel.benefits.exclusiveFeatures.includes('batch_transfer'),
          reason: userLevel.benefits.exclusiveFeatures.includes('batch_transfer') 
            ? undefined 
            : `需要达到${this.getFeatureUnlockLevel('batch_transfer')}级`,
        },
        custom_gas: {
          feature: 'custom_gas',
          enabled: userLevel.benefits.exclusiveFeatures.includes('custom_gas'),
          reason: userLevel.benefits.exclusiveFeatures.includes('custom_gas') 
            ? undefined 
            : `需要达到${this.getFeatureUnlockLevel('custom_gas')}级`,
        },
        withdrawal: {
          feature: 'withdrawal',
          enabled: userData.kycVerified || userLevel.level >= 2,
          reason: userData.kycVerified || userLevel.level >= 2 
            ? undefined 
            : '需要完成身份验证或达到2级',
        },
        large_withdrawal: {
          feature: 'large_withdrawal',
          enabled: userData.kycVerified && userLevel.level >= 3,
          reason: userData.kycVerified && userLevel.level >= 3 
            ? undefined 
            : '需要完成KYC验证并达到3级',
        },
      };

      return permissions;
    } catch (error) {
      console.error('Failed to get user permissions:', error);
      return {};
    }
  }

  /**
   * 获取所有等级信息
   */
  getAllLevels(): UserLevel[] {
    return this.levels;
  }

  /**
   * 检查等级要求
   */
  private checkLevelRequirements(userData: any, requirements: UserLevel['requirements']): boolean {
    if (requirements.minBalance && parseFloat(userData.balance || '0') < parseFloat(requirements.minBalance)) {
      return false;
    }

    if (requirements.minTransactions && (userData.transactionCount || 0) < requirements.minTransactions) {
      return false;
    }

    if (requirements.minDaysActive && (userData.daysActive || 0) < requirements.minDaysActive) {
      return false;
    }

    if (requirements.referralCount && (userData.referralCount || 0) < requirements.referralCount) {
      return false;
    }

    if (requirements.kycVerified && !userData.kycVerified) {
      return false;
    }

    if (requirements.completedTasks) {
      const userTasks = userData.completedTasks || [];
      for (const task of requirements.completedTasks) {
        if (!userTasks.includes(task)) {
          return false;
        }
      }
    }

    return true;
  }

  /**
   * 计算升级进度
   */
  private calculateLevelProgress(userData: any, requirements: UserLevel['requirements']): number {
    const progressItems: number[] = [];

    if (requirements.minBalance) {
      const current = parseFloat(userData.balance || '0');
      const required = parseFloat(requirements.minBalance);
      progressItems.push(Math.min(100, (current / required) * 100));
    }

    if (requirements.minTransactions) {
      const current = userData.transactionCount || 0;
      const required = requirements.minTransactions;
      progressItems.push(Math.min(100, (current / required) * 100));
    }

    if (requirements.minDaysActive) {
      const current = userData.daysActive || 0;
      const required = requirements.minDaysActive;
      progressItems.push(Math.min(100, (current / required) * 100));
    }

    if (requirements.referralCount) {
      const current = userData.referralCount || 0;
      const required = requirements.referralCount;
      progressItems.push(Math.min(100, (current / required) * 100));
    }

    if (requirements.completedTasks) {
      const userTasks = userData.completedTasks || [];
      const completedCount = requirements.completedTasks.filter(task => 
        userTasks.includes(task)
      ).length;
      progressItems.push((completedCount / requirements.completedTasks.length) * 100);
    }

    return progressItems.length > 0 
      ? progressItems.reduce((sum, progress) => sum + progress, 0) / progressItems.length
      : 0;
  }

  /**
   * 获取功能解锁等级
   */
  private getFeatureUnlockLevel(feature: string): number {
    for (const level of this.levels) {
      if (level.benefits.exclusiveFeatures.includes(feature)) {
        return level.level;
      }
    }
    return 5; // 默认最高等级
  }

  /**
   * 获取用户数据（模拟）
   */
  private async getUserData(userId: number): Promise<any> {
    // 这里应该从数据库获取真实用户数据
    return {
      balance: '250.50',
      transactionCount: 15,
      daysActive: 12,
      referralCount: 2,
      kycVerified: false,
      completedTasks: ['telegram_join', 'daily_signin', 'twitter_follow'],
      totalExperience: 450,
    };
  }
}

export const userLevelService = new UserLevelService();
