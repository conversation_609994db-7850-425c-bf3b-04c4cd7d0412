/**
 * 用户资料服务
 * 管理用户资料信息，包括钱包地址绑定
 */

import { UserProfile } from '@/types/user';

export class UserProfileService {
  private static instance: UserProfileService;

  public static getInstance(): UserProfileService {
    if (!UserProfileService.instance) {
      UserProfileService.instance = new UserProfileService();
    }
    return UserProfileService.instance;
  }

  /**
   * 获取用户资料
   */
  async getUserProfile(telegramUserId: number): Promise<UserProfile | null> {
    try {
      // 在开发环境中从localStorage获取用户资料
      if (process.env.NODE_ENV === 'development') {
        const profileKey = `user_profile_${telegramUserId}`;
        const storedProfile = localStorage.getItem(profileKey);
        
        if (storedProfile) {
          const profile = JSON.parse(storedProfile);
          return {
            ...profile,
            createdAt: new Date(profile.createdAt),
            updatedAt: new Date(profile.updatedAt),
          };
        }
        
        // 如果没有存储的资料，创建默认资料
        const defaultProfile: UserProfile = {
          id: telegramUserId.toString(),
          address: '', // 将在钱包生成后设置
          nickname: `User${telegramUserId}`,
          createdAt: new Date(),
          updatedAt: new Date(),
        };
        
        this.saveUserProfile(telegramUserId, defaultProfile);
        return defaultProfile;
      }
      
      // 生产环境中从API获取
      const response = await fetch(`/api/user/profile/${telegramUserId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch user profile');
      }
      
      const data = await response.json();
      return data.profile;
    } catch (error) {
      console.error('Error fetching user profile:', error);
      return null;
    }
  }

  /**
   * 保存用户资料
   */
  async saveUserProfile(telegramUserId: number, profile: UserProfile): Promise<boolean> {
    try {
      // 在开发环境中保存到localStorage
      if (process.env.NODE_ENV === 'development') {
        const profileKey = `user_profile_${telegramUserId}`;
        const profileToSave = {
          ...profile,
          updatedAt: new Date(),
        };
        
        localStorage.setItem(profileKey, JSON.stringify(profileToSave));
        console.log(`💾 用户资料已保存: ${telegramUserId}`, profileToSave);
        return true;
      }
      
      // 生产环境中保存到API
      const response = await fetch(`/api/user/profile/${telegramUserId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(profile),
      });
      
      return response.ok;
    } catch (error) {
      console.error('Error saving user profile:', error);
      return false;
    }
  }

  /**
   * 更新用户钱包地址
   */
  async updateWalletAddress(telegramUserId: number, walletAddress: string): Promise<boolean> {
    try {
      const profile = await this.getUserProfile(telegramUserId);
      if (!profile) {
        console.error('User profile not found');
        return false;
      }
      
      const updatedProfile: UserProfile = {
        ...profile,
        address: walletAddress,
        updatedAt: new Date(),
      };
      
      return await this.saveUserProfile(telegramUserId, updatedProfile);
    } catch (error) {
      console.error('Error updating wallet address:', error);
      return false;
    }
  }

  /**
   * 更新用户资料字段
   */
  async updateProfile(
    telegramUserId: number, 
    updates: Partial<Omit<UserProfile, 'id' | 'createdAt'>>
  ): Promise<boolean> {
    try {
      const profile = await this.getUserProfile(telegramUserId);
      if (!profile) {
        console.error('User profile not found');
        return false;
      }
      
      const updatedProfile: UserProfile = {
        ...profile,
        ...updates,
        updatedAt: new Date(),
      };
      
      return await this.saveUserProfile(telegramUserId, updatedProfile);
    } catch (error) {
      console.error('Error updating profile:', error);
      return false;
    }
  }

  /**
   * 删除用户资料
   */
  async deleteUserProfile(telegramUserId: number): Promise<boolean> {
    try {
      // 在开发环境中从localStorage删除
      if (process.env.NODE_ENV === 'development') {
        const profileKey = `user_profile_${telegramUserId}`;
        localStorage.removeItem(profileKey);
        console.log(`🗑️ 用户资料已删除: ${telegramUserId}`);
        return true;
      }
      
      // 生产环境中从API删除
      const response = await fetch(`/api/user/profile/${telegramUserId}`, {
        method: 'DELETE',
      });
      
      return response.ok;
    } catch (error) {
      console.error('Error deleting user profile:', error);
      return false;
    }
  }
}

// 导出单例实例
export const userProfileService = UserProfileService.getInstance();
