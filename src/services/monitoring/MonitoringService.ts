/**
 * 系统监控服务
 * 实时监控系统状态，异常自动告警
 */

import { supabase } from '@/lib/supabase';

// 监控配置
export const MONITORING_CONFIG = {
  // 业务指标阈值
  BUSINESS_METRICS: {
    MIN_DAILY_ACTIVE_USERS: 10,
    MAX_BET_FAILURE_RATE: 0.05,      // 5%
    MIN_AVERAGE_BET_AMOUNT: 50,
    MAX_SETTLEMENT_DELAY_HOURS: 48,
    MIN_JUDGMENT_PARTICIPATION_RATE: 0.3
  },
  
  // 技术指标阈值
  TECHNICAL_METRICS: {
    MAX_API_RESPONSE_TIME: 2000,     // 2秒
    MAX_DATABASE_QUERY_TIME: 1000,   // 1秒
    MAX_ERROR_RATE: 0.01,            // 1%
    MIN_SYSTEM_AVAILABILITY: 0.99,   // 99%
    MAX_MEMORY_USAGE: 0.85           // 85%
  },
  
  // 安全指标阈值
  SECURITY_METRICS: {
    MAX_FAILED_LOGINS_PER_HOUR: 100,
    MAX_SUSPICIOUS_TRANSACTIONS_PER_DAY: 10,
    MAX_COORDINATED_VOTING_INCIDENTS: 5,
    MIN_BALANCE_CONSISTENCY_RATE: 0.999
  },
  
  // 告警配置
  ALERT_CONFIG: {
    CHECK_INTERVAL_MINUTES: 5,
    ESCALATION_DELAY_MINUTES: 15,
    MAX_ALERTS_PER_HOUR: 20,
    ALERT_COOLDOWN_MINUTES: 30
  }
};

// 监控指标接口
export interface MetricData {
  name: string;
  value: number;
  threshold: number;
  status: 'normal' | 'warning' | 'critical';
  timestamp: string;
  metadata?: any;
}

// 告警接口
export interface Alert {
  id: string;
  type: 'business' | 'technical' | 'security';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  metrics: MetricData[];
  status: 'active' | 'acknowledged' | 'resolved';
  createdAt: string;
  acknowledgedAt?: string;
  resolvedAt?: string;
  escalated: boolean;
}

// 系统健康状态接口
export interface SystemHealth {
  overall: 'healthy' | 'degraded' | 'critical';
  businessMetrics: MetricData[];
  technicalMetrics: MetricData[];
  securityMetrics: MetricData[];
  activeAlerts: Alert[];
  lastUpdated: string;
}

export class MonitoringService {
  /**
   * 收集业务指标
   */
  static async collectBusinessMetrics(): Promise<MetricData[]> {
    const metrics: MetricData[] = [];
    const now = new Date().toISOString();

    // 1. 日活跃用户数
    const today = new Date().toISOString().split('T')[0];
    const { data: activeUsers } = await supabase
      .from('bet_participants')
      .select('user_id')
      .gte('created_at', `${today}T00:00:00.000Z`)
      .lt('created_at', `${today}T23:59:59.999Z`);

    const dailyActiveUsers = new Set(activeUsers?.map(u => u.user_id) || []).size;
    metrics.push({
      name: 'daily_active_users',
      value: dailyActiveUsers,
      threshold: MONITORING_CONFIG.BUSINESS_METRICS.MIN_DAILY_ACTIVE_USERS,
      status: dailyActiveUsers >= MONITORING_CONFIG.BUSINESS_METRICS.MIN_DAILY_ACTIVE_USERS ? 'normal' : 'warning',
      timestamp: now
    });

    // 2. 平均投注金额
    const { data: recentBets } = await supabase
      .from('bet_participants')
      .select('bet_amount')
      .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString());

    const avgBetAmount = recentBets && recentBets.length > 0
      ? recentBets.reduce((sum, bet) => sum + bet.bet_amount, 0) / recentBets.length
      : 0;

    metrics.push({
      name: 'average_bet_amount',
      value: avgBetAmount,
      threshold: MONITORING_CONFIG.BUSINESS_METRICS.MIN_AVERAGE_BET_AMOUNT,
      status: avgBetAmount >= MONITORING_CONFIG.BUSINESS_METRICS.MIN_AVERAGE_BET_AMOUNT ? 'normal' : 'warning',
      timestamp: now
    });

    // 3. 活跃赌约数
    const { data: activeBets } = await supabase
      .from('social_bets')
      .select('id')
      .in('status', ['open', 'betting_closed', 'judging', 'confirming']);

    const activeBetCount = activeBets?.length || 0;
    metrics.push({
      name: 'active_bets_count',
      value: activeBetCount,
      threshold: 5, // 至少5个活跃赌约
      status: activeBetCount >= 5 ? 'normal' : 'warning',
      timestamp: now
    });

    // 4. 结算延迟
    const { data: delayedSettlements } = await supabase
      .from('social_bets')
      .select('id, result_deadline')
      .eq('status', 'settled')
      .lt('result_deadline', new Date(Date.now() - 48 * 60 * 60 * 1000).toISOString());

    const delayedCount = delayedSettlements?.length || 0;
    metrics.push({
      name: 'delayed_settlements',
      value: delayedCount,
      threshold: 5,
      status: delayedCount <= 5 ? 'normal' : 'warning',
      timestamp: now
    });

    return metrics;
  }

  /**
   * 收集技术指标
   */
  static async collectTechnicalMetrics(): Promise<MetricData[]> {
    const metrics: MetricData[] = [];
    const now = new Date().toISOString();

    // 1. 数据库响应时间
    const dbStartTime = Date.now();
    await supabase.from('users').select('id').limit(1);
    const dbResponseTime = Date.now() - dbStartTime;

    metrics.push({
      name: 'database_response_time',
      value: dbResponseTime,
      threshold: MONITORING_CONFIG.TECHNICAL_METRICS.MAX_DATABASE_QUERY_TIME,
      status: dbResponseTime <= MONITORING_CONFIG.TECHNICAL_METRICS.MAX_DATABASE_QUERY_TIME ? 'normal' : 'warning',
      timestamp: now
    });

    // 2. 系统可用性（简化计算）
    const availability = 0.995; // 模拟数据
    metrics.push({
      name: 'system_availability',
      value: availability,
      threshold: MONITORING_CONFIG.TECHNICAL_METRICS.MIN_SYSTEM_AVAILABILITY,
      status: availability >= MONITORING_CONFIG.TECHNICAL_METRICS.MIN_SYSTEM_AVAILABILITY ? 'normal' : 'critical',
      timestamp: now
    });

    // 3. 内存使用率（模拟）
    const memoryUsage = Math.random() * 0.8; // 模拟内存使用率
    metrics.push({
      name: 'memory_usage',
      value: memoryUsage,
      threshold: MONITORING_CONFIG.TECHNICAL_METRICS.MAX_MEMORY_USAGE,
      status: memoryUsage <= MONITORING_CONFIG.TECHNICAL_METRICS.MAX_MEMORY_USAGE ? 'normal' : 'warning',
      timestamp: now
    });

    // 4. 错误率（从日志中计算，这里模拟）
    const errorRate = Math.random() * 0.02; // 模拟错误率
    metrics.push({
      name: 'error_rate',
      value: errorRate,
      threshold: MONITORING_CONFIG.TECHNICAL_METRICS.MAX_ERROR_RATE,
      status: errorRate <= MONITORING_CONFIG.TECHNICAL_METRICS.MAX_ERROR_RATE ? 'normal' : 'warning',
      timestamp: now
    });

    return metrics;
  }

  /**
   * 收集安全指标
   */
  static async collectSecurityMetrics(): Promise<MetricData[]> {
    const metrics: MetricData[] = [];
    const now = new Date().toISOString();
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000).toISOString();

    // 1. 失败登录次数（模拟）
    const failedLogins = Math.floor(Math.random() * 50);
    metrics.push({
      name: 'failed_logins_per_hour',
      value: failedLogins,
      threshold: MONITORING_CONFIG.SECURITY_METRICS.MAX_FAILED_LOGINS_PER_HOUR,
      status: failedLogins <= MONITORING_CONFIG.SECURITY_METRICS.MAX_FAILED_LOGINS_PER_HOUR ? 'normal' : 'warning',
      timestamp: now
    });

    // 2. 可疑交易数量
    const { data: largeBets } = await supabase
      .from('bet_participants')
      .select('id')
      .gte('created_at', oneHourAgo)
      .gt('bet_amount', 5000);

    const suspiciousTransactions = largeBets?.length || 0;
    metrics.push({
      name: 'suspicious_transactions',
      value: suspiciousTransactions,
      threshold: 10,
      status: suspiciousTransactions <= 10 ? 'normal' : 'warning',
      timestamp: now
    });

    // 3. 余额一致性率
    const balanceConsistencyRate = 0.9995; // 模拟数据
    metrics.push({
      name: 'balance_consistency_rate',
      value: balanceConsistencyRate,
      threshold: MONITORING_CONFIG.SECURITY_METRICS.MIN_BALANCE_CONSISTENCY_RATE,
      status: balanceConsistencyRate >= MONITORING_CONFIG.SECURITY_METRICS.MIN_BALANCE_CONSISTENCY_RATE ? 'normal' : 'critical',
      timestamp: now
    });

    return metrics;
  }

  /**
   * 生成告警
   */
  static async generateAlerts(metrics: MetricData[]): Promise<Alert[]> {
    const alerts: Alert[] = [];

    // 检查关键指标
    const criticalMetrics = metrics.filter(m => m.status === 'critical');
    const warningMetrics = metrics.filter(m => m.status === 'warning');

    // 生成关键告警
    if (criticalMetrics.length > 0) {
      alerts.push({
        id: this.generateAlertId(),
        type: 'technical',
        severity: 'critical',
        title: '系统关键指标异常',
        description: `检测到 ${criticalMetrics.length} 个关键指标异常`,
        metrics: criticalMetrics,
        status: 'active',
        createdAt: new Date().toISOString(),
        escalated: false
      });
    }

    // 生成警告告警
    if (warningMetrics.length >= 3) {
      alerts.push({
        id: this.generateAlertId(),
        type: 'business',
        severity: 'medium',
        title: '多个指标出现警告',
        description: `检测到 ${warningMetrics.length} 个指标出现警告状态`,
        metrics: warningMetrics,
        status: 'active',
        createdAt: new Date().toISOString(),
        escalated: false
      });
    }

    // 特定业务逻辑告警
    const dailyActiveUsers = metrics.find(m => m.name === 'daily_active_users');
    if (dailyActiveUsers && dailyActiveUsers.value === 0) {
      alerts.push({
        id: this.generateAlertId(),
        type: 'business',
        severity: 'high',
        title: '无活跃用户',
        description: '今日无任何用户参与投注',
        metrics: [dailyActiveUsers],
        status: 'active',
        createdAt: new Date().toISOString(),
        escalated: false
      });
    }

    return alerts;
  }

  /**
   * 获取系统健康状态
   */
  static async getSystemHealth(): Promise<SystemHealth> {
    const businessMetrics = await this.collectBusinessMetrics();
    const technicalMetrics = await this.collectTechnicalMetrics();
    const securityMetrics = await this.collectSecurityMetrics();

    const allMetrics = [...businessMetrics, ...technicalMetrics, ...securityMetrics];
    const activeAlerts = await this.generateAlerts(allMetrics);

    // 计算整体健康状态
    const criticalCount = allMetrics.filter(m => m.status === 'critical').length;
    const warningCount = allMetrics.filter(m => m.status === 'warning').length;

    let overall: 'healthy' | 'degraded' | 'critical';
    if (criticalCount > 0) {
      overall = 'critical';
    } else if (warningCount > 3) {
      overall = 'degraded';
    } else {
      overall = 'healthy';
    }

    return {
      overall,
      businessMetrics,
      technicalMetrics,
      securityMetrics,
      activeAlerts,
      lastUpdated: new Date().toISOString()
    };
  }

  /**
   * 发送告警通知
   */
  static async sendAlert(alert: Alert): Promise<void> {
    // 这里应该集成实际的通知服务（Slack、邮件、短信等）
    console.log(`🚨 ALERT [${alert.severity.toUpperCase()}]: ${alert.title}`);
    console.log(`Description: ${alert.description}`);
    console.log(`Metrics affected: ${alert.metrics.map(m => m.name).join(', ')}`);
    console.log(`Created at: ${alert.createdAt}`);

    // 模拟发送到Slack
    if (alert.severity === 'critical') {
      console.log('📱 Sending critical alert to Slack channel #alerts');
      console.log('📧 Sending critical alert email to on-call team');
    }

    // 模拟发送邮件
    if (alert.severity === 'high' || alert.severity === 'critical') {
      console.log('📧 Sending alert email to monitoring team');
    }
  }

  /**
   * 运行监控检查
   */
  static async runMonitoringCheck(): Promise<SystemHealth> {
    try {
      const systemHealth = await this.getSystemHealth();

      // 发送新告警
      for (const alert of systemHealth.activeAlerts) {
        await this.sendAlert(alert);
      }

      // 记录监控数据
      console.log(`📊 System Health Check - Overall: ${systemHealth.overall}`);
      console.log(`📈 Business Metrics: ${systemHealth.businessMetrics.length}`);
      console.log(`⚙️ Technical Metrics: ${systemHealth.technicalMetrics.length}`);
      console.log(`🔒 Security Metrics: ${systemHealth.securityMetrics.length}`);
      console.log(`🚨 Active Alerts: ${systemHealth.activeAlerts.length}`);

      return systemHealth;
    } catch (error) {
      console.error('❌ Monitoring check failed:', error);
      
      // 发送监控系统故障告警
      await this.sendAlert({
        id: this.generateAlertId(),
        type: 'technical',
        severity: 'critical',
        title: '监控系统故障',
        description: `监控检查失败: ${error instanceof Error ? error.message : 'Unknown error'}`,
        metrics: [],
        status: 'active',
        createdAt: new Date().toISOString(),
        escalated: false
      });

      throw error;
    }
  }

  /**
   * 生成告警ID
   */
  private static generateAlertId(): string {
    return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 启动监控循环
   */
  static startMonitoring(): void {
    console.log('🔍 Starting system monitoring...');
    
    // 立即执行一次检查
    this.runMonitoringCheck();

    // 设置定期检查
    setInterval(async () => {
      try {
        await this.runMonitoringCheck();
      } catch (error) {
        console.error('Monitoring check error:', error);
      }
    }, MONITORING_CONFIG.ALERT_CONFIG.CHECK_INTERVAL_MINUTES * 60 * 1000);
  }

  /**
   * 获取监控仪表板数据
   */
  static async getDashboardData(): Promise<any> {
    const systemHealth = await this.getSystemHealth();
    
    return {
      systemHealth,
      summary: {
        totalMetrics: systemHealth.businessMetrics.length + 
                     systemHealth.technicalMetrics.length + 
                     systemHealth.securityMetrics.length,
        normalMetrics: [...systemHealth.businessMetrics, ...systemHealth.technicalMetrics, ...systemHealth.securityMetrics]
          .filter(m => m.status === 'normal').length,
        warningMetrics: [...systemHealth.businessMetrics, ...systemHealth.technicalMetrics, ...systemHealth.securityMetrics]
          .filter(m => m.status === 'warning').length,
        criticalMetrics: [...systemHealth.businessMetrics, ...systemHealth.technicalMetrics, ...systemHealth.securityMetrics]
          .filter(m => m.status === 'critical').length,
        activeAlerts: systemHealth.activeAlerts.length,
        uptime: '99.95%', // 模拟数据
        lastIncident: '2024-01-15T10:30:00Z' // 模拟数据
      }
    };
  }
}
