/**
 * Gas费管理服务
 * 处理BSC网络Gas费估算、优化和代付机制
 */

import { ethers } from 'ethers';

export interface GasFeeEstimate {
  slow: {
    gasPrice: string;
    cost: string;
    estimatedTime: string;
  };
  standard: {
    gasPrice: string;
    cost: string;
    estimatedTime: string;
  };
  fast: {
    gasPrice: string;
    cost: string;
    estimatedTime: string;
  };
}

export interface TransactionRequest {
  to: string;
  value?: string;
  data?: string;
  gasLimit?: string;
}

export interface GasOptimizationResult {
  originalGas: string;
  optimizedGas: string;
  savings: string;
  savingsPercentage: number;
}

export class GasFeeService {
  private provider: ethers.JsonRpcProvider;
  private readonly BSC_CHAIN_ID = 97; // BSC Testnet
  private readonly MIN_GAS_PRICE = '**********'; // 3 Gwei
  private readonly MAX_GAS_PRICE = '20000000000'; // 20 Gwei

  constructor() {
    const rpcUrl = process.env.NEXT_PUBLIC_RPC_URL || 'https://data-seed-prebsc-1-s1.binance.org:8545/';
    this.provider = new ethers.JsonRpcProvider(rpcUrl);
  }

  /**
   * 获取当前网络Gas费估算
   */
  async getGasFeeEstimate(): Promise<GasFeeEstimate> {
    try {
      // 获取当前Gas价格
      const currentGasPrice = await this.provider.getFeeData();
      const baseGasPrice = currentGasPrice.gasPrice || BigInt(this.MIN_GAS_PRICE);

      // 计算不同速度的Gas价格
      const slowGasPrice = baseGasPrice * BigInt(80) / BigInt(100); // 80% of current
      const standardGasPrice = baseGasPrice;
      const fastGasPrice = baseGasPrice * BigInt(120) / BigInt(100); // 120% of current

      // 确保Gas价格在合理范围内
      const clampGasPrice = (price: bigint) => {
        const min = BigInt(this.MIN_GAS_PRICE);
        const max = BigInt(this.MAX_GAS_PRICE);
        return price < min ? min : price > max ? max : price;
      };

      const slowPrice = clampGasPrice(slowGasPrice);
      const standardPrice = clampGasPrice(standardGasPrice);
      const fastPrice = clampGasPrice(fastGasPrice);

      // 估算标准转账的Gas消耗 (21000 gas)
      const standardGasLimit = BigInt(21000);

      return {
        slow: {
          gasPrice: ethers.formatUnits(slowPrice, 'gwei'),
          cost: ethers.formatEther(slowPrice * standardGasLimit),
          estimatedTime: '3-5分钟',
        },
        standard: {
          gasPrice: ethers.formatUnits(standardPrice, 'gwei'),
          cost: ethers.formatEther(standardPrice * standardGasLimit),
          estimatedTime: '1-2分钟',
        },
        fast: {
          gasPrice: ethers.formatUnits(fastPrice, 'gwei'),
          cost: ethers.formatEther(fastPrice * standardGasLimit),
          estimatedTime: '30秒-1分钟',
        },
      };
    } catch (error) {
      console.error('Failed to get gas fee estimate:', error);
      
      // 返回默认值
      return {
        slow: {
          gasPrice: '3',
          cost: '0.000063',
          estimatedTime: '3-5分钟',
        },
        standard: {
          gasPrice: '5',
          cost: '0.000105',
          estimatedTime: '1-2分钟',
        },
        fast: {
          gasPrice: '8',
          cost: '0.000168',
          estimatedTime: '30秒-1分钟',
        },
      };
    }
  }

  /**
   * 估算特定交易的Gas费用
   */
  async estimateTransactionGas(request: TransactionRequest): Promise<{
    gasLimit: string;
    gasFees: GasFeeEstimate;
    totalCosts: {
      slow: string;
      standard: string;
      fast: string;
    };
  }> {
    try {
      // 估算Gas限制
      let gasLimit: bigint;
      
      if (request.gasLimit) {
        gasLimit = BigInt(request.gasLimit);
      } else {
        try {
          const estimatedGas = await this.provider.estimateGas({
            to: request.to,
            value: request.value ? BigInt(request.value) : undefined,
            data: request.data,
          });
          // 添加20%的缓冲
          gasLimit = estimatedGas * BigInt(120) / BigInt(100);
        } catch (error) {
          console.warn('Gas estimation failed, using default:', error);
          // 根据交易类型使用默认值
          gasLimit = request.data ? BigInt(100000) : BigInt(21000);
        }
      }

      // 获取Gas费估算
      const gasFees = await this.getGasFeeEstimate();

      // 计算总成本
      const calculateCost = (gasPriceGwei: string) => {
        const gasPrice = ethers.parseUnits(gasPriceGwei, 'gwei');
        return ethers.formatEther(gasPrice * gasLimit);
      };

      return {
        gasLimit: gasLimit.toString(),
        gasFees,
        totalCosts: {
          slow: calculateCost(gasFees.slow.gasPrice),
          standard: calculateCost(gasFees.standard.gasPrice),
          fast: calculateCost(gasFees.fast.gasPrice),
        },
      };
    } catch (error) {
      console.error('Failed to estimate transaction gas:', error);
      throw error;
    }
  }

  /**
   * 优化Gas费用
   */
  async optimizeGasFee(request: TransactionRequest): Promise<GasOptimizationResult> {
    try {
      // 获取原始Gas估算
      const originalEstimate = await this.estimateTransactionGas(request);
      
      // 应用优化策略
      let optimizedGasLimit = BigInt(originalEstimate.gasLimit);
      
      // 策略1: 如果是简单转账，使用精确的Gas限制
      if (!request.data || request.data === '0x') {
        optimizedGasLimit = BigInt(21000);
      }
      
      // 策略2: 对于合约调用，减少缓冲区
      else {
        optimizedGasLimit = optimizedGasLimit * BigInt(110) / BigInt(120); // 从20%缓冲减少到10%
      }

      // 计算节省
      const originalGas = BigInt(originalEstimate.gasLimit);
      const savings = originalGas - optimizedGasLimit;
      const savingsPercentage = Number(savings * BigInt(100) / originalGas);

      return {
        originalGas: originalGas.toString(),
        optimizedGas: optimizedGasLimit.toString(),
        savings: savings.toString(),
        savingsPercentage,
      };
    } catch (error) {
      console.error('Failed to optimize gas fee:', error);
      throw error;
    }
  }

  /**
   * 检查是否符合平台代付条件
   */
  async checkSponsorshipEligibility(
    userAddress: string,
    transactionValue: string,
    gasEstimate: string
  ): Promise<{
    eligible: boolean;
    reason?: string;
    sponsoredAmount?: string;
  }> {
    try {
      const valueInEther = parseFloat(ethers.formatEther(transactionValue));
      const gasInEther = parseFloat(gasEstimate);

      // 代付条件
      const MAX_SPONSORED_GAS = 0.001; // 最大代付0.001 BNB
      const MIN_TRANSACTION_VALUE = 0.01; // 最小交易金额0.01 BNB
      const MAX_DAILY_SPONSORED = 0.01; // 每日最大代付0.01 BNB

      // 检查交易金额
      if (valueInEther < MIN_TRANSACTION_VALUE) {
        return {
          eligible: false,
          reason: `交易金额需大于 ${MIN_TRANSACTION_VALUE} BNB`,
        };
      }

      // 检查Gas费用
      if (gasInEther > MAX_SPONSORED_GAS) {
        return {
          eligible: false,
          reason: `Gas费用超过代付限额 ${MAX_SPONSORED_GAS} BNB`,
        };
      }

      // 检查用户每日代付额度（这里应该查询数据库）
      const dailySponsored = await this.getUserDailySponsored(userAddress);
      if (dailySponsored + gasInEther > MAX_DAILY_SPONSORED) {
        return {
          eligible: false,
          reason: `超过每日代付限额 ${MAX_DAILY_SPONSORED} BNB`,
        };
      }

      return {
        eligible: true,
        sponsoredAmount: gasEstimate,
      };
    } catch (error) {
      console.error('Failed to check sponsorship eligibility:', error);
      return {
        eligible: false,
        reason: '检查代付资格失败',
      };
    }
  }

  /**
   * 获取用户每日已代付金额
   */
  private async getUserDailySponsored(userAddress: string): Promise<number> {
    try {
      // 这里应该查询数据库获取用户今日已代付的Gas费用
      // 暂时返回模拟数据
      return 0.002; // 已代付0.002 BNB
    } catch (error) {
      console.error('Failed to get user daily sponsored amount:', error);
      return 0;
    }
  }

  /**
   * 记录Gas费用代付
   */
  async recordGasSponsorship(
    userAddress: string,
    transactionHash: string,
    sponsoredAmount: string
  ): Promise<void> {
    try {
      // 这里应该记录到数据库
      console.log('Recording gas sponsorship:', {
        userAddress,
        transactionHash,
        sponsoredAmount,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Failed to record gas sponsorship:', error);
    }
  }

  /**
   * 获取网络拥堵状态
   */
  async getNetworkCongestion(): Promise<{
    level: 'low' | 'medium' | 'high';
    description: string;
    recommendedGasPrice: string;
  }> {
    try {
      const gasFees = await this.getGasFeeEstimate();
      const standardGasPrice = parseFloat(gasFees.standard.gasPrice);

      let level: 'low' | 'medium' | 'high';
      let description: string;

      if (standardGasPrice < 5) {
        level = 'low';
        description = '网络畅通，Gas费用较低';
      } else if (standardGasPrice < 10) {
        level = 'medium';
        description = '网络正常，Gas费用适中';
      } else {
        level = 'high';
        description = '网络拥堵，Gas费用较高';
      }

      return {
        level,
        description,
        recommendedGasPrice: gasFees.standard.gasPrice,
      };
    } catch (error) {
      console.error('Failed to get network congestion:', error);
      return {
        level: 'medium',
        description: '无法获取网络状态',
        recommendedGasPrice: '5',
      };
    }
  }
}

export const gasFeeService = new GasFeeService();
