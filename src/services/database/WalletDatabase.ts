/**
 * 钱包数据库服务
 * 管理用户钱包数据的存储和查询
 */

import { createClient } from '@supabase/supabase-js';
import { WalletAddress, WalletTransaction, PendingReward, WalletSecurity } from '@/services/wallet/types';
import { AuditLogger } from '@/services/wallet/AuditLogger';

export interface DatabaseConfig {
  supabaseUrl: string;
  supabaseKey: string;
}

export class WalletDatabase {
  private supabase: any;
  private auditLogger: AuditLogger;

  constructor(config: DatabaseConfig, auditLogger: AuditLogger) {
    this.supabase = createClient(config.supabaseUrl, config.supabaseKey);
    this.auditLogger = auditLogger;
  }

  /**
   * 初始化数据库连接
   */
  async initialize(): Promise<void> {
    try {
      // 测试数据库连接
      const { data, error } = await this.supabase.from('user_wallets').select('count').limit(1);
      
      if (error && !error.message.includes('relation "user_wallets" does not exist')) {
        throw error;
      }

      await this.auditLogger.log({
        action: 'wallet_database_initialized',
        actor: 'system',
        actorType: 'system',
        resource: 'database',
        resourceId: 'wallet_db',
        details: {
          supabaseUrl: this.supabase.supabaseUrl,
        },
      });
    } catch (error) {
      await this.auditLogger.log({
        action: 'wallet_database_init_failed',
        actor: 'system',
        actorType: 'system',
        resource: 'database',
        resourceId: 'wallet_db',
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
      throw error;
    }
  }

  /**
   * 保存用户钱包地址
   */
  async saveWalletAddress(walletAddress: WalletAddress): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('user_wallets')
        .insert({
          id: walletAddress.id,
          telegram_user_id: walletAddress.telegramUserId,
          address: walletAddress.address,
          derivation_path: walletAddress.derivationPath,
          created_at: walletAddress.createdAt.toISOString(),
          updated_at: walletAddress.updatedAt.toISOString(),
        });

      if (error) throw error;

      await this.auditLogger.log({
        action: 'wallet_address_saved',
        actor: walletAddress.telegramUserId.toString(),
        actorType: 'user',
        resource: 'wallet_address',
        resourceId: walletAddress.id,
        details: {
          address: walletAddress.address,
          derivationPath: walletAddress.derivationPath,
        },
      });
    } catch (error) {
      await this.auditLogger.log({
        action: 'wallet_address_save_failed',
        actor: walletAddress.telegramUserId.toString(),
        actorType: 'user',
        resource: 'wallet_address',
        resourceId: walletAddress.id,
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
      throw error;
    }
  }

  /**
   * 根据Telegram用户ID获取钱包地址
   */
  async getWalletByTelegramUserId(telegramUserId: number): Promise<WalletAddress | null> {
    try {
      const { data, error } = await this.supabase
        .from('user_wallets')
        .select('*')
        .eq('telegram_user_id', telegramUserId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // 未找到记录
        }
        throw error;
      }

      return {
        id: data.id,
        telegramUserId: data.telegram_user_id,
        address: data.address,
        derivationPath: data.derivation_path,
        createdAt: new Date(data.created_at),
        updatedAt: new Date(data.updated_at),
      };
    } catch (error) {
      await this.auditLogger.log({
        action: 'wallet_query_failed',
        actor: telegramUserId.toString(),
        actorType: 'user',
        resource: 'wallet_query',
        resourceId: telegramUserId.toString(),
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
      throw error;
    }
  }

  /**
   * 保存交易记录
   */
  async saveTransaction(transaction: WalletTransaction): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('transactions')
        .insert({
          id: transaction.id,
          from_address: transaction.fromAddress,
          to_address: transaction.toAddress,
          amount: transaction.amount,
          token_type: transaction.tokenType,
          tx_hash: transaction.txHash,
          status: transaction.status,
          type: transaction.type,
          gas_used: transaction.gasUsed,
          gas_fee: transaction.gasFee,
          created_at: transaction.createdAt.toISOString(),
          confirmed_at: transaction.confirmedAt?.toISOString(),
          metadata: transaction.metadata,
        });

      if (error) throw error;

      await this.auditLogger.log({
        action: 'transaction_saved',
        actor: 'system',
        actorType: 'system',
        resource: 'transaction',
        resourceId: transaction.id,
        details: {
          fromAddress: transaction.fromAddress,
          toAddress: transaction.toAddress,
          amount: transaction.amount,
          type: transaction.type,
          status: transaction.status,
        },
      });
    } catch (error) {
      await this.auditLogger.log({
        action: 'transaction_save_failed',
        actor: 'system',
        actorType: 'system',
        resource: 'transaction',
        resourceId: transaction.id,
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
      throw error;
    }
  }

  /**
   * 更新交易状态
   */
  async updateTransactionStatus(
    transactionId: string,
    status: WalletTransaction['status'],
    txHash?: string,
    gasUsed?: string,
    gasFee?: string
  ): Promise<void> {
    try {
      const updateData: any = {
        status,
        updated_at: new Date().toISOString(),
      };

      if (txHash) updateData.tx_hash = txHash;
      if (gasUsed) updateData.gas_used = gasUsed;
      if (gasFee) updateData.gas_fee = gasFee;
      if (status === 'confirmed') updateData.confirmed_at = new Date().toISOString();

      const { error } = await this.supabase
        .from('transactions')
        .update(updateData)
        .eq('id', transactionId);

      if (error) throw error;

      await this.auditLogger.log({
        action: 'transaction_status_updated',
        actor: 'system',
        actorType: 'system',
        resource: 'transaction',
        resourceId: transactionId,
        details: {
          status,
          txHash,
          gasUsed,
          gasFee,
        },
      });
    } catch (error) {
      await this.auditLogger.log({
        action: 'transaction_update_failed',
        actor: 'system',
        actorType: 'system',
        resource: 'transaction',
        resourceId: transactionId,
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
      throw error;
    }
  }

  /**
   * 获取用户交易历史
   */
  async getUserTransactions(
    telegramUserId: number,
    limit: number = 50,
    offset: number = 0
  ): Promise<Transaction[]> {
    try {
      // 首先获取用户钱包地址
      const wallet = await this.getWalletByTelegramUserId(telegramUserId);
      if (!wallet) {
        return [];
      }

      const { data, error } = await this.supabase
        .from('transactions')
        .select('*')
        .or(`from_address.eq.${wallet.address},to_address.eq.${wallet.address}`)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) throw error;

      return data.map((row: any) => ({
        id: row.id,
        fromAddress: row.from_address,
        toAddress: row.to_address,
        amount: row.amount,
        tokenType: row.token_type,
        txHash: row.tx_hash,
        status: row.status,
        type: row.type,
        gasUsed: row.gas_used,
        gasFee: row.gas_fee,
        createdAt: new Date(row.created_at),
        confirmedAt: row.confirmed_at ? new Date(row.confirmed_at) : undefined,
        metadata: row.metadata,
      }));
    } catch (error) {
      await this.auditLogger.log({
        action: 'user_transactions_query_failed',
        actor: telegramUserId.toString(),
        actorType: 'user',
        resource: 'transaction_history',
        resourceId: telegramUserId.toString(),
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
      throw error;
    }
  }

  /**
   * 保存待领取奖励
   */
  async savePendingReward(reward: PendingReward): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('pending_rewards')
        .insert({
          id: reward.id,
          telegram_user_id: reward.telegramUserId,
          amount: reward.amount,
          source: reward.source,
          description: reward.description,
          created_at: reward.createdAt.toISOString(),
          claimed_at: reward.claimedAt?.toISOString(),
        });

      if (error) throw error;

      await this.auditLogger.log({
        action: 'pending_reward_saved',
        actor: reward.telegramUserId.toString(),
        actorType: 'user',
        resource: 'pending_reward',
        resourceId: reward.id,
        details: {
          amount: reward.amount,
          source: reward.source,
          description: reward.description,
        },
      });
    } catch (error) {
      await this.auditLogger.log({
        action: 'pending_reward_save_failed',
        actor: reward.telegramUserId.toString(),
        actorType: 'user',
        resource: 'pending_reward',
        resourceId: reward.id,
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
      throw error;
    }
  }

  /**
   * 获取用户待领取奖励
   */
  async getUserPendingRewards(telegramUserId: number): Promise<PendingReward[]> {
    try {
      const { data, error } = await this.supabase
        .from('pending_rewards')
        .select('*')
        .eq('telegram_user_id', telegramUserId)
        .is('claimed_at', null)
        .order('created_at', { ascending: false });

      if (error) throw error;

      return data.map((row: any) => ({
        id: row.id,
        telegramUserId: row.telegram_user_id,
        amount: row.amount,
        source: row.source,
        description: row.description,
        createdAt: new Date(row.created_at),
        claimedAt: row.claimed_at ? new Date(row.claimed_at) : undefined,
      }));
    } catch (error) {
      await this.auditLogger.log({
        action: 'pending_rewards_query_failed',
        actor: telegramUserId.toString(),
        actorType: 'user',
        resource: 'pending_rewards',
        resourceId: telegramUserId.toString(),
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
      throw error;
    }
  }

  /**
   * 标记奖励为已领取
   */
  async claimReward(rewardId: string): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('pending_rewards')
        .update({
          claimed_at: new Date().toISOString(),
        })
        .eq('id', rewardId);

      if (error) throw error;

      await this.auditLogger.log({
        action: 'reward_claimed',
        actor: 'system',
        actorType: 'system',
        resource: 'reward_claim',
        resourceId: rewardId,
        details: {
          claimedAt: new Date().toISOString(),
        },
      });
    } catch (error) {
      await this.auditLogger.log({
        action: 'reward_claim_failed',
        actor: 'system',
        actorType: 'system',
        resource: 'reward_claim',
        resourceId: rewardId,
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });
      throw error;
    }
  }

  /**
   * 创建数据库表（如果不存在）
   */
  async createTablesIfNotExists(): Promise<void> {
    // 这个方法在实际部署中应该通过数据库迁移脚本来执行
    // 这里提供SQL语句作为参考
    const createTableSQL = `
      -- 用户钱包表
      CREATE TABLE IF NOT EXISTS user_wallets (
        id VARCHAR(255) PRIMARY KEY,
        telegram_user_id BIGINT UNIQUE NOT NULL,
        address VARCHAR(42) UNIQUE NOT NULL,
        derivation_path VARCHAR(255) NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );

      -- 交易记录表
      CREATE TABLE IF NOT EXISTS transactions (
        id VARCHAR(255) PRIMARY KEY,
        from_address VARCHAR(42) NOT NULL,
        to_address VARCHAR(42) NOT NULL,
        amount VARCHAR(255) NOT NULL,
        token_type VARCHAR(10) NOT NULL,
        tx_hash VARCHAR(66),
        status VARCHAR(20) NOT NULL,
        type VARCHAR(20) NOT NULL,
        gas_used VARCHAR(255),
        gas_fee VARCHAR(255),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        confirmed_at TIMESTAMP WITH TIME ZONE,
        metadata JSONB
      );

      -- 待领取奖励表
      CREATE TABLE IF NOT EXISTS pending_rewards (
        id VARCHAR(255) PRIMARY KEY,
        telegram_user_id BIGINT NOT NULL,
        amount VARCHAR(255) NOT NULL,
        source VARCHAR(50) NOT NULL,
        description TEXT NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        claimed_at TIMESTAMP WITH TIME ZONE
      );

      -- 创建索引
      CREATE INDEX IF NOT EXISTS idx_user_wallets_telegram_user_id ON user_wallets(telegram_user_id);
      CREATE INDEX IF NOT EXISTS idx_transactions_from_address ON transactions(from_address);
      CREATE INDEX IF NOT EXISTS idx_transactions_to_address ON transactions(to_address);
      CREATE INDEX IF NOT EXISTS idx_transactions_created_at ON transactions(created_at);
      CREATE INDEX IF NOT EXISTS idx_pending_rewards_telegram_user_id ON pending_rewards(telegram_user_id);
      CREATE INDEX IF NOT EXISTS idx_pending_rewards_claimed_at ON pending_rewards(claimed_at);
    `;

    console.log('Database tables creation SQL:', createTableSQL);
  }
}
