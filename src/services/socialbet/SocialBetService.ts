/**
 * Social Bet核心服务
 * 处理赌约创建、参与、状态管理等核心业务逻辑
 */

import { supabase } from '@/lib/supabase';
import { FortuneService } from '@/services/fortune/FortuneService';

// 赌约选项接口
export interface BetOption {
  id: string;
  text: string;
  participants: number;
  totalAmount: number;
}

// 赌约创建参数
export interface CreateBetParams {
  title: string;
  description: string;
  category: string;
  betType: '1v1' | '1vN';
  templateType?: string;
  options: BetOption[];
  minBetAmount: number;
  maxBetAmount?: number;
  targetUserId?: string; // 1v1模式的目标用户
  creatorOption?: string; // 1v1模式创建者选择的选项
  bettingDeadline: string;
  resultDeadline: string;
  requiresJudgment?: boolean;
  tags?: string[];
  referralRewardRate?: number;
}

// 赌约详情接口
export interface SocialBet {
  id: string;
  title: string;
  description: string;
  category: string;
  betType: '1v1' | '1vN';
  templateType?: string;
  creatorId: string;
  options: BetOption[];
  minBetAmount: number;
  maxBetAmount?: number;
  totalPool: number;
  targetUserId?: string;
  creatorOption?: string;
  targetOption?: string;
  bettingDeadline: string;
  resultDeadline: string;
  status: 'open' | 'betting_closed' | 'judging' | 'confirming' | 'settled' | 'cancelled' | 'expired';
  requiresJudgment: boolean;
  judgmentStartTime?: string;
  currentJudgmentRound: number;
  winningOption?: string;
  resultConfirmedByCreator: boolean;
  resultConfirmedByParticipants: boolean;
  resultConfirmationDeadline?: string;
  platformFeeRate: number;
  referralRewardRate: number;
  tags?: string[];
  isFeatured: boolean;
  viewCount: number;
  participantCount: number;
  createdAt: string;
  updatedAt: string;
}

// 投注参与接口
export interface BetParticipation {
  id: string;
  betId: string;
  userId: string;
  selectedOption: string;
  betAmount: number;
  status: 'active' | 'withdrawn' | 'settled' | 'refunded';
  payoutAmount: number;
  isWinner?: boolean;
  referrerId?: string;
  referralReward: number;
  createdAt: string;
  updatedAt: string;
}

export class SocialBetService {
  /**
   * 创建新赌约
   */
  static async createBet(creatorId: string, params: CreateBetParams): Promise<SocialBet> {
    // 验证用户福气余额（如果是1v1模式且创建者需要投注）
    if (params.betType === '1v1' && params.creatorOption) {
      const userFortune = await FortuneService.getUserFortune(creatorId);
      if (!userFortune || userFortune.available_fortune < params.minBetAmount) {
        throw new Error('福气余额不足');
      }
    }

    // 验证参数
    if (params.options.length < 2) {
      throw new Error('至少需要2个选项');
    }

    if (new Date(params.bettingDeadline) <= new Date()) {
      throw new Error('投注截止时间必须在未来');
    }

    if (new Date(params.resultDeadline) <= new Date(params.bettingDeadline)) {
      throw new Error('结果截止时间必须在投注截止时间之后');
    }

    // 创建赌约
    const { data: bet, error } = await supabase
      .from('social_bets')
      .insert({
        title: params.title,
        description: params.description,
        category: params.category,
        bet_type: params.betType,
        template_type: params.templateType,
        creator_id: creatorId,
        options: params.options,
        min_bet_amount: params.minBetAmount,
        max_bet_amount: params.maxBetAmount,
        target_user_id: params.targetUserId,
        creator_option: params.creatorOption,
        betting_deadline: params.bettingDeadline,
        result_deadline: params.resultDeadline,
        requires_judgment: params.requiresJudgment ?? true,
        referral_reward_rate: params.referralRewardRate ?? 0.1,
        tags: params.tags,
        status: 'open'
      })
      .select()
      .single();

    if (error) {
      throw new Error(`创建赌约失败: ${error.message}`);
    }

    // 如果是1v1模式且创建者选择了选项，自动参与投注
    if (params.betType === '1v1' && params.creatorOption) {
      await this.participateInBet(bet.id, creatorId, {
        selectedOption: params.creatorOption,
        betAmount: params.minBetAmount
      });
    }

    return this.formatBetData(bet);
  }

  /**
   * 参与赌约投注
   */
  static async participateInBet(
    betId: string,
    userId: string,
    params: {
      selectedOption: string;
      betAmount: number;
      referrerId?: string;
    }
  ): Promise<BetParticipation> {
    // 获取赌约信息
    const bet = await this.getBetById(betId);
    if (!bet) {
      throw new Error('赌约不存在');
    }

    // 验证赌约状态
    if (bet.status !== 'open') {
      throw new Error('赌约已关闭投注');
    }

    // 验证投注截止时间
    if (new Date() > new Date(bet.bettingDeadline)) {
      throw new Error('投注时间已截止');
    }

    // 验证选项
    const validOptions = bet.options.map(opt => opt.id);
    if (!validOptions.includes(params.selectedOption)) {
      throw new Error('无效的选项');
    }

    // 验证投注金额
    if (params.betAmount < bet.minBetAmount) {
      throw new Error(`投注金额不能少于${bet.minBetAmount}福气`);
    }

    if (bet.maxBetAmount && params.betAmount > bet.maxBetAmount) {
      throw new Error(`投注金额不能超过${bet.maxBetAmount}福气`);
    }

    // 验证用户福气余额
    const userFortune = await FortuneService.getUserFortune(userId);
    if (!userFortune || userFortune.available_fortune < params.betAmount) {
      throw new Error('福气余额不足');
    }

    // 检查是否已经参与过
    const { data: existingParticipation } = await supabase
      .from('bet_participants')
      .select('id')
      .eq('bet_id', betId)
      .eq('user_id', userId)
      .single();

    if (existingParticipation) {
      throw new Error('您已经参与过此赌约');
    }

    // 1v1模式特殊处理
    if (bet.betType === '1v1') {
      // 检查是否已有参与者
      const { data: participants } = await supabase
        .from('bet_participants')
        .select('id')
        .eq('bet_id', betId);

      if (participants && participants.length >= 2) {
        throw new Error('1v1赌约已满员');
      }

      // 如果有目标用户，验证是否为目标用户
      if (bet.targetUserId && bet.targetUserId !== userId) {
        throw new Error('此赌约仅限指定用户参与');
      }
    }

    // 锁定用户福气
    await FortuneService.deductFortune(
      userId,
      params.betAmount,
      'bet_participation',
      betId,
      `参与赌约: ${bet.title}`
    );

    // 创建参与记录
    const { data: participation, error } = await supabase
      .from('bet_participants')
      .insert({
        bet_id: betId,
        user_id: userId,
        selected_option: params.selectedOption,
        bet_amount: params.betAmount,
        referrer_id: params.referrerId,
        status: 'active'
      })
      .select()
      .single();

    if (error) {
      // 如果创建参与记录失败，退还福气
      await FortuneService.addFortune(
        userId,
        params.betAmount,
        'bet_refund',
        betId,
        `赌约参与失败退款: ${bet.title}`
      );
      throw new Error(`参与赌约失败: ${error.message}`);
    }

    // 更新赌约统计
    await this.updateBetStats(betId);

    return participation;
  }

  /**
   * 获取赌约详情
   */
  static async getBetById(betId: string): Promise<SocialBet | null> {
    const { data, error } = await supabase
      .from('social_bets')
      .select('*')
      .eq('id', betId)
      .single();

    if (error && error.code !== 'PGRST116') {
      throw new Error(`获取赌约失败: ${error.message}`);
    }

    return data ? this.formatBetData(data) : null;
  }

  /**
   * 获取赌约列表
   */
  static async getBetList(params: {
    category?: string;
    status?: string;
    creatorId?: string;
    limit?: number;
    offset?: number;
    orderBy?: 'created_at' | 'betting_deadline' | 'total_pool';
    orderDirection?: 'asc' | 'desc';
  } = {}): Promise<{ bets: SocialBet[]; total: number }> {
    let query = supabase
      .from('social_bets')
      .select('*', { count: 'exact' });

    // 应用筛选条件
    if (params.category) {
      query = query.eq('category', params.category);
    }

    if (params.status) {
      query = query.eq('status', params.status);
    }

    if (params.creatorId) {
      query = query.eq('creator_id', params.creatorId);
    }

    // 排序
    const orderBy = params.orderBy || 'created_at';
    const orderDirection = params.orderDirection || 'desc';
    query = query.order(orderBy, { ascending: orderDirection === 'asc' });

    // 分页
    const limit = params.limit || 20;
    const offset = params.offset || 0;
    query = query.range(offset, offset + limit - 1);

    const { data, error, count } = await query;

    if (error) {
      throw new Error(`获取赌约列表失败: ${error.message}`);
    }

    return {
      bets: (data || []).map(bet => this.formatBetData(bet)),
      total: count || 0
    };
  }

  /**
   * 更新赌约状态
   */
  static async updateBetStatus(betId: string, status: SocialBet['status']): Promise<void> {
    const { error } = await supabase
      .from('social_bets')
      .update({ 
        status,
        updated_at: new Date().toISOString()
      })
      .eq('id', betId);

    if (error) {
      throw new Error(`更新赌约状态失败: ${error.message}`);
    }
  }

  /**
   * 更新赌约统计信息
   */
  static async updateBetStats(betId: string): Promise<void> {
    // 获取参与统计
    const { data: participants } = await supabase
      .from('bet_participants')
      .select('bet_amount, selected_option')
      .eq('bet_id', betId)
      .eq('status', 'active');

    if (!participants) return;

    const participantCount = participants.length;
    const totalPool = participants.reduce((sum, p) => sum + p.bet_amount, 0);

    // 统计各选项的参与情况
    const optionStats = participants.reduce((stats, p) => {
      if (!stats[p.selected_option]) {
        stats[p.selected_option] = { participants: 0, totalAmount: 0 };
      }
      stats[p.selected_option].participants++;
      stats[p.selected_option].totalAmount += p.bet_amount;
      return stats;
    }, {} as Record<string, { participants: number; totalAmount: number }>);

    // 获取当前赌约选项
    const { data: bet } = await supabase
      .from('social_bets')
      .select('options')
      .eq('id', betId)
      .single();

    if (bet && bet.options) {
      // 更新选项统计
      const updatedOptions = bet.options.map((option: BetOption) => ({
        ...option,
        participants: optionStats[option.id]?.participants || 0,
        totalAmount: optionStats[option.id]?.totalAmount || 0
      }));

      // 更新赌约
      const { error } = await supabase
        .from('social_bets')
        .update({
          participant_count: participantCount,
          total_pool: totalPool,
          options: updatedOptions,
          updated_at: new Date().toISOString()
        })
        .eq('id', betId);

      if (error) {
        console.error('更新赌约统计失败:', error);
      }
    }
  }

  /**
   * 格式化赌约数据
   */
  private static formatBetData(data: any): SocialBet {
    return {
      id: data.id,
      title: data.title,
      description: data.description,
      category: data.category,
      betType: data.bet_type,
      templateType: data.template_type,
      creatorId: data.creator_id,
      options: data.options || [],
      minBetAmount: data.min_bet_amount,
      maxBetAmount: data.max_bet_amount,
      totalPool: data.total_pool,
      targetUserId: data.target_user_id,
      creatorOption: data.creator_option,
      targetOption: data.target_option,
      bettingDeadline: data.betting_deadline,
      resultDeadline: data.result_deadline,
      status: data.status,
      requiresJudgment: data.requires_judgment,
      judgmentStartTime: data.judgment_start_time,
      currentJudgmentRound: data.current_judgment_round,
      winningOption: data.winning_option,
      resultConfirmedByCreator: data.result_confirmed_by_creator,
      resultConfirmedByParticipants: data.result_confirmed_by_participants,
      resultConfirmationDeadline: data.result_confirmation_deadline,
      platformFeeRate: data.platform_fee_rate,
      referralRewardRate: data.referral_reward_rate,
      tags: data.tags,
      isFeatured: data.is_featured,
      viewCount: data.view_count,
      participantCount: data.participant_count,
      createdAt: data.created_at,
      updatedAt: data.updated_at
    };
  }
}
