/**
 * Social Bet确认机制服务
 * 处理双方确认结果，替代申诉机制
 */

import { supabase } from '@/lib/supabase';
import { SocialBetService } from './SocialBetService';

// 确认配置
export const CONFIRMATION_CONFIG = {
  CONFIRMATION_PERIOD_HOURS: 24, // 24小时确认期
  AUTO_CONFIRM_DELAY_HOURS: 48,  // 48小时后自动确认
  DISPUTE_EXTENSION_HOURS: 24    // 争议时延长24小时
};

// 确认状态接口
export interface ConfirmationStatus {
  betId: string;
  creatorConfirmed: boolean;
  participantsConfirmed: boolean;
  confirmationDeadline: string;
  canConfirm: boolean;
  disputeRaised: boolean;
  autoConfirmAt?: string;
  status: 'pending' | 'confirmed' | 'disputed' | 'auto_confirmed';
}

// 确认结果接口
export interface ConfirmationResult {
  betId: string;
  userId: string;
  userType: 'creator' | 'participant';
  confirmed: boolean;
  disputeReason?: string;
  createdAt: string;
}

export class ConfirmationService {
  /**
   * 获取确认状态
   */
  static async getConfirmationStatus(betId: string): Promise<ConfirmationStatus | null> {
    // 获取赌约信息
    const { data: bet } = await supabase
      .from('social_bets')
      .select('*')
      .eq('id', betId)
      .single();

    if (!bet || bet.status !== 'confirming') {
      return null;
    }

    // 检查是否已过确认期
    const now = new Date();
    const confirmationDeadline = new Date(bet.result_confirmation_deadline);
    const autoConfirmAt = new Date(confirmationDeadline.getTime() + CONFIRMATION_CONFIG.AUTO_CONFIRM_DELAY_HOURS * 60 * 60 * 1000);

    // 如果已过自动确认时间，触发自动确认
    if (now > autoConfirmAt) {
      await this.autoConfirmResult(betId);
      return {
        betId,
        creatorConfirmed: true,
        participantsConfirmed: true,
        confirmationDeadline: bet.result_confirmation_deadline,
        canConfirm: false,
        disputeRaised: false,
        autoConfirmAt: autoConfirmAt.toISOString(),
        status: 'auto_confirmed'
      };
    }

    return {
      betId,
      creatorConfirmed: bet.result_confirmed_by_creator,
      participantsConfirmed: bet.result_confirmed_by_participants,
      confirmationDeadline: bet.result_confirmation_deadline,
      canConfirm: now <= confirmationDeadline,
      disputeRaised: false, // 从确认记录中获取
      autoConfirmAt: autoConfirmAt.toISOString(),
      status: this.getConfirmationStatusType(bet)
    };
  }

  /**
   * 确认结果
   */
  static async confirmResult(
    betId: string,
    userId: string,
    confirmed: boolean,
    disputeReason?: string
  ): Promise<ConfirmationResult> {
    // 获取赌约信息
    const bet = await SocialBetService.getBetById(betId);
    if (!bet) {
      throw new Error('赌约不存在');
    }

    if (bet.status !== 'confirming') {
      throw new Error('赌约不在确认状态');
    }

    // 检查确认期限
    const now = new Date();
    const deadline = new Date(bet.resultConfirmationDeadline!);
    if (now > deadline) {
      throw new Error('确认期限已过');
    }

    // 确定用户类型
    const userType = bet.creatorId === userId ? 'creator' : 'participant';

    // 验证用户权限
    if (userType === 'creator') {
      if (bet.creatorId !== userId) {
        throw new Error('只有创建者可以确认');
      }
    } else {
      // 检查是否为参与者
      const { data: participation } = await supabase
        .from('bet_participants')
        .select('id')
        .eq('bet_id', betId)
        .eq('user_id', userId)
        .single();

      if (!participation) {
        throw new Error('只有参与者可以确认');
      }
    }

    // 检查是否已确认过
    if (userType === 'creator' && bet.resultConfirmedByCreator) {
      throw new Error('创建者已确认过结果');
    }

    // 对于参与者，需要检查是否所有参与者都已确认
    if (userType === 'participant' && bet.resultConfirmedByParticipants) {
      throw new Error('参与者已确认过结果');
    }

    // 如果是争议，记录争议原因
    if (!confirmed && !disputeReason) {
      throw new Error('提出争议时必须提供理由');
    }

    // 更新确认状态
    const updateData: any = {
      updated_at: new Date().toISOString()
    };

    if (userType === 'creator') {
      updateData.result_confirmed_by_creator = confirmed;
    } else {
      // 对于1vN模式，需要检查所有参与者的确认状态
      if (bet.betType === '1vN') {
        updateData.result_confirmed_by_participants = confirmed;
      } else {
        // 1v1模式，直接设置
        updateData.result_confirmed_by_participants = confirmed;
      }
    }

    // 如果有争议，延长确认期限
    if (!confirmed) {
      const extendedDeadline = new Date(deadline.getTime() + CONFIRMATION_CONFIG.DISPUTE_EXTENSION_HOURS * 60 * 60 * 1000);
      updateData.result_confirmation_deadline = extendedDeadline.toISOString();
    }

    const { error } = await supabase
      .from('social_bets')
      .update(updateData)
      .eq('id', betId);

    if (error) {
      throw new Error(`确认失败: ${error.message}`);
    }

    // 记录确认历史
    const confirmationResult: ConfirmationResult = {
      betId,
      userId,
      userType,
      confirmed,
      disputeReason,
      createdAt: new Date().toISOString()
    };

    // 检查是否双方都已确认
    const updatedBet = await SocialBetService.getBetById(betId);
    if (updatedBet && updatedBet.resultConfirmedByCreator && updatedBet.resultConfirmedByParticipants) {
      // 双方确认，触发结算
      await this.triggerSettlement(betId);
    }

    return confirmationResult;
  }

  /**
   * 自动确认结果
   */
  static async autoConfirmResult(betId: string): Promise<void> {
    const { error } = await supabase
      .from('social_bets')
      .update({
        result_confirmed_by_creator: true,
        result_confirmed_by_participants: true,
        status: 'settled',
        updated_at: new Date().toISOString()
      })
      .eq('id', betId);

    if (error) {
      throw new Error(`自动确认失败: ${error.message}`);
    }

    // 触发结算
    await this.triggerSettlement(betId);
  }

  /**
   * 触发结算
   */
  static async triggerSettlement(betId: string): Promise<void> {
    // 更新赌约状态为已结算
    await supabase
      .from('social_bets')
      .update({
        status: 'settled',
        updated_at: new Date().toISOString()
      })
      .eq('id', betId);

    // 这里会调用结算服务进行福气分配
    // 由于结算服务还未创建，先记录日志
    console.log(`Bet ${betId} confirmed and ready for settlement`);
  }

  /**
   * 获取用户确认权限
   */
  static async getUserConfirmationPermission(
    betId: string,
    userId: string
  ): Promise<{
    canConfirm: boolean;
    userType?: 'creator' | 'participant';
    alreadyConfirmed: boolean;
    reason?: string;
  }> {
    // 获取赌约信息
    const bet = await SocialBetService.getBetById(betId);
    if (!bet) {
      return { canConfirm: false, alreadyConfirmed: false, reason: '赌约不存在' };
    }

    if (bet.status !== 'confirming') {
      return { canConfirm: false, alreadyConfirmed: false, reason: '赌约不在确认状态' };
    }

    // 检查确认期限
    const now = new Date();
    const deadline = new Date(bet.resultConfirmationDeadline!);
    if (now > deadline) {
      return { canConfirm: false, alreadyConfirmed: false, reason: '确认期限已过' };
    }

    // 确定用户类型
    let userType: 'creator' | 'participant' | undefined;
    let alreadyConfirmed = false;

    if (bet.creatorId === userId) {
      userType = 'creator';
      alreadyConfirmed = bet.resultConfirmedByCreator;
    } else {
      // 检查是否为参与者
      const { data: participation } = await supabase
        .from('bet_participants')
        .select('id')
        .eq('bet_id', betId)
        .eq('user_id', userId)
        .single();

      if (participation) {
        userType = 'participant';
        alreadyConfirmed = bet.resultConfirmedByParticipants;
      }
    }

    if (!userType) {
      return { canConfirm: false, alreadyConfirmed: false, reason: '无权限确认此赌约' };
    }

    if (alreadyConfirmed) {
      return { canConfirm: false, userType, alreadyConfirmed: true, reason: '已确认过结果' };
    }

    return { canConfirm: true, userType, alreadyConfirmed: false };
  }

  /**
   * 获取确认历史
   */
  static async getConfirmationHistory(betId: string): Promise<ConfirmationResult[]> {
    // 这里应该从确认历史表中获取，但由于表结构简化，我们从赌约状态推断
    const bet = await SocialBetService.getBetById(betId);
    if (!bet) return [];

    const history: ConfirmationResult[] = [];

    if (bet.resultConfirmedByCreator) {
      history.push({
        betId,
        userId: bet.creatorId,
        userType: 'creator',
        confirmed: true,
        createdAt: bet.updatedAt // 简化处理
      });
    }

    if (bet.resultConfirmedByParticipants) {
      // 获取参与者信息
      const { data: participants } = await supabase
        .from('bet_participants')
        .select('user_id')
        .eq('bet_id', betId)
        .limit(1);

      if (participants && participants.length > 0) {
        history.push({
          betId,
          userId: participants[0].user_id,
          userType: 'participant',
          confirmed: true,
          createdAt: bet.updatedAt // 简化处理
        });
      }
    }

    return history;
  }

  /**
   * 获取确认状态类型
   */
  private static getConfirmationStatusType(bet: any): 'pending' | 'confirmed' | 'disputed' | 'auto_confirmed' {
    if (bet.result_confirmed_by_creator && bet.result_confirmed_by_participants) {
      return 'confirmed';
    }

    const now = new Date();
    const deadline = new Date(bet.result_confirmation_deadline);
    
    if (now > deadline) {
      return 'auto_confirmed';
    }

    return 'pending';
  }

  /**
   * 批量检查过期确认
   */
  static async processExpiredConfirmations(): Promise<void> {
    const now = new Date();
    
    // 查找过期的确认
    const { data: expiredBets } = await supabase
      .from('social_bets')
      .select('id, result_confirmation_deadline')
      .eq('status', 'confirming')
      .lt('result_confirmation_deadline', now.toISOString());

    if (!expiredBets || expiredBets.length === 0) return;

    // 处理每个过期的赌约
    for (const bet of expiredBets) {
      try {
        const autoConfirmDeadline = new Date(
          new Date(bet.result_confirmation_deadline).getTime() + 
          CONFIRMATION_CONFIG.AUTO_CONFIRM_DELAY_HOURS * 60 * 60 * 1000
        );

        if (now > autoConfirmDeadline) {
          await this.autoConfirmResult(bet.id);
          console.log(`Auto-confirmed bet ${bet.id}`);
        }
      } catch (error) {
        console.error(`Failed to auto-confirm bet ${bet.id}:`, error);
      }
    }
  }
}
