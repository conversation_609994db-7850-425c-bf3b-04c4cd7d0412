/**
 * 认证等级服务
 * 基于HAOX持币的认证等级和权益管理
 */

import { supabase } from '@/lib/supabase';

// 认证等级配置
export const CERTIFICATION_CONFIG = {
  LEVELS: {
    0: { name: '普通用户', minHaox: 0, maxHaox: 999, color: '#9CA3AF' },
    1: { name: '铜牌认证', minHaox: 1000, maxHaox: 4999, color: '#CD7F32' },
    2: { name: '银牌认证', minHaox: 5000, maxHaox: 19999, color: '#C0C0C0' },
    3: { name: '金牌认证', minHaox: 20000, maxHaox: 49999, color: '#FFD700' },
    4: { name: '白金认证', minHaox: 50000, maxHaox: 99999, color: '#E5E4E2' },
    5: { name: '钻石认证', minHaox: 100000, maxHaox: Infinity, color: '#B9F2FF' }
  },
  
  // 等级权益
  BENEFITS: {
    0: {
      feeDiscount: 0,
      dailyJudgmentLimit: 3,
      specialFeatures: [],
      description: '基础功能访问'
    },
    1: {
      feeDiscount: 0.05, // 5%折扣
      dailyJudgmentLimit: 5,
      specialFeatures: ['priority_support'],
      description: '5%手续费折扣，优先客服'
    },
    2: {
      feeDiscount: 0.10, // 10%折扣
      dailyJudgmentLimit: 8,
      specialFeatures: ['priority_support', 'advanced_analytics'],
      description: '10%手续费折扣，高级数据分析'
    },
    3: {
      feeDiscount: 0.15, // 15%折扣
      dailyJudgmentLimit: 12,
      specialFeatures: ['priority_support', 'advanced_analytics', 'exclusive_bets'],
      description: '15%手续费折扣，专属赌约'
    },
    4: {
      feeDiscount: 0.20, // 20%折扣
      dailyJudgmentLimit: 20,
      specialFeatures: ['priority_support', 'advanced_analytics', 'exclusive_bets', 'vip_events'],
      description: '20%手续费折扣，VIP活动'
    },
    5: {
      feeDiscount: 0.25, // 25%折扣
      dailyJudgmentLimit: 30,
      specialFeatures: ['priority_support', 'advanced_analytics', 'exclusive_bets', 'vip_events', 'custom_features'],
      description: '25%手续费折扣，定制功能'
    }
  },
  
  // 检查频率（小时）
  CHECK_INTERVAL_HOURS: 1,
  
  // 缓存时间（分钟）
  CACHE_DURATION_MINUTES: 30
};

// 认证等级接口
export interface CertificationLevel {
  level: number;
  name: string;
  minHaox: number;
  maxHaox: number;
  color: string;
  benefits: {
    feeDiscount: number;
    dailyJudgmentLimit: number;
    specialFeatures: string[];
    description: string;
  };
}

// 用户认证状态接口
export interface UserCertification {
  userId: string;
  currentLevel: number;
  haoxBalance: number;
  lastCheckTime: string;
  nextCheckTime: string;
  levelInfo: CertificationLevel;
  canUpgrade: boolean;
  nextLevelInfo?: CertificationLevel;
}

export class CertificationService {
  /**
   * 获取用户认证等级
   */
  static async getUserCertification(userId: string): Promise<UserCertification> {
    // 获取用户信誉信息
    let { data: reputation } = await supabase
      .from('user_reputation')
      .select('*')
      .eq('user_id', userId)
      .single();

    // 如果不存在，创建默认记录
    if (!reputation) {
      const { data: newReputation, error } = await supabase
        .from('user_reputation')
        .insert({
          user_id: userId,
          certification_level: 0,
          haox_balance: 0,
          last_balance_check: new Date().toISOString()
        })
        .select()
        .single();

      if (error) {
        throw new Error(`创建用户信誉记录失败: ${error.message}`);
      }
      reputation = newReputation;
    }

    // 检查是否需要更新余额
    const shouldUpdate = this.shouldUpdateBalance(reputation.last_balance_check);
    if (shouldUpdate) {
      reputation = await this.updateUserBalance(userId);
    }

    const currentLevel = reputation.certification_level;
    const levelInfo = this.getLevelInfo(currentLevel);
    const nextLevelInfo = currentLevel < 5 ? this.getLevelInfo(currentLevel + 1) : undefined;
    const canUpgrade = nextLevelInfo ? reputation.haox_balance >= nextLevelInfo.minHaox : false;

    return {
      userId,
      currentLevel,
      haoxBalance: reputation.haox_balance,
      lastCheckTime: reputation.last_balance_check,
      nextCheckTime: this.getNextCheckTime(reputation.last_balance_check),
      levelInfo,
      canUpgrade,
      nextLevelInfo
    };
  }

  /**
   * 更新用户HAOX余额和认证等级
   */
  static async updateUserBalance(userId: string): Promise<any> {
    // 这里应该调用区块链API获取真实HAOX余额
    // 目前使用模拟数据
    const mockHaoxBalance = await this.getMockHaoxBalance(userId);
    
    // 计算新的认证等级
    const newLevel = this.calculateLevel(mockHaoxBalance);
    
    // 更新数据库
    const { data: updatedReputation, error } = await supabase
      .from('user_reputation')
      .update({
        haox_balance: mockHaoxBalance,
        certification_level: newLevel,
        last_balance_check: new Date().toISOString(),
        fee_discount_rate: CERTIFICATION_CONFIG.BENEFITS[newLevel as keyof typeof CERTIFICATION_CONFIG.BENEFITS].feeDiscount,
        daily_judgment_limit: CERTIFICATION_CONFIG.BENEFITS[newLevel as keyof typeof CERTIFICATION_CONFIG.BENEFITS].dailyJudgmentLimit,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId)
      .select()
      .single();

    if (error) {
      throw new Error(`更新用户余额失败: ${error.message}`);
    }

    return updatedReputation;
  }

  /**
   * 获取等级信息
   */
  static getLevelInfo(level: number): CertificationLevel {
    const config = CERTIFICATION_CONFIG.LEVELS[level as keyof typeof CERTIFICATION_CONFIG.LEVELS];
    const benefits = CERTIFICATION_CONFIG.BENEFITS[level as keyof typeof CERTIFICATION_CONFIG.BENEFITS];
    
    return {
      level,
      name: config.name,
      minHaox: config.minHaox,
      maxHaox: config.maxHaox,
      color: config.color,
      benefits
    };
  }

  /**
   * 计算认证等级
   */
  static calculateLevel(haoxBalance: number): number {
    for (let level = 5; level >= 0; level--) {
      const config = CERTIFICATION_CONFIG.LEVELS[level as keyof typeof CERTIFICATION_CONFIG.LEVELS];
      if (haoxBalance >= config.minHaox) {
        return level;
      }
    }
    return 0;
  }

  /**
   * 检查是否需要更新余额
   */
  private static shouldUpdateBalance(lastCheckTime: string): boolean {
    const lastCheck = new Date(lastCheckTime);
    const now = new Date();
    const hoursSinceLastCheck = (now.getTime() - lastCheck.getTime()) / (1000 * 60 * 60);
    
    return hoursSinceLastCheck >= CERTIFICATION_CONFIG.CHECK_INTERVAL_HOURS;
  }

  /**
   * 获取下次检查时间
   */
  private static getNextCheckTime(lastCheckTime: string): string {
    const lastCheck = new Date(lastCheckTime);
    const nextCheck = new Date(lastCheck.getTime() + CERTIFICATION_CONFIG.CHECK_INTERVAL_HOURS * 60 * 60 * 1000);
    return nextCheck.toISOString();
  }

  /**
   * 模拟获取HAOX余额（实际应该调用区块链API）
   */
  private static async getMockHaoxBalance(userId: string): Promise<number> {
    // 这里应该调用真实的区块链API
    // 目前返回模拟数据
    const mockBalances: Record<string, number> = {
      'user1': 15000,
      'user2': 75000,
      'user3': 150000,
      'default': Math.floor(Math.random() * 50000)
    };
    
    return mockBalances[userId] || mockBalances['default'];
  }

  /**
   * 获取所有等级信息
   */
  static getAllLevels(): CertificationLevel[] {
    return Object.keys(CERTIFICATION_CONFIG.LEVELS).map(level => 
      this.getLevelInfo(parseInt(level))
    );
  }

  /**
   * 批量更新用户认证等级
   */
  static async batchUpdateCertifications(): Promise<void> {
    // 获取需要更新的用户
    const cutoffTime = new Date(Date.now() - CERTIFICATION_CONFIG.CHECK_INTERVAL_HOURS * 60 * 60 * 1000);
    
    const { data: usersToUpdate } = await supabase
      .from('user_reputation')
      .select('user_id')
      .lt('last_balance_check', cutoffTime.toISOString())
      .limit(100); // 批量处理100个用户

    if (!usersToUpdate || usersToUpdate.length === 0) {
      return;
    }

    // 逐个更新用户
    for (const user of usersToUpdate) {
      try {
        await this.updateUserBalance(user.user_id);
        console.log(`Updated certification for user ${user.user_id}`);
      } catch (error) {
        console.error(`Failed to update certification for user ${user.user_id}:`, error);
      }
    }
  }

  /**
   * 获取认证统计
   */
  static async getCertificationStats(): Promise<any> {
    const { data: stats } = await supabase
      .from('user_reputation')
      .select('certification_level')
      .not('certification_level', 'is', null);

    if (!stats) return null;

    const levelCounts = stats.reduce((counts, user) => {
      counts[user.certification_level] = (counts[user.certification_level] || 0) + 1;
      return counts;
    }, {} as Record<number, number>);

    const totalUsers = stats.length;
    
    return {
      totalUsers,
      levelDistribution: Object.keys(CERTIFICATION_CONFIG.LEVELS).map(level => {
        const levelNum = parseInt(level);
        const count = levelCounts[levelNum] || 0;
        const percentage = totalUsers > 0 ? (count / totalUsers) * 100 : 0;
        
        return {
          level: levelNum,
          name: CERTIFICATION_CONFIG.LEVELS[levelNum as keyof typeof CERTIFICATION_CONFIG.LEVELS].name,
          count,
          percentage: Math.round(percentage * 100) / 100
        };
      })
    };
  }

  /**
   * 检查用户是否有特定权限
   */
  static async hasPermission(userId: string, feature: string): Promise<boolean> {
    const certification = await this.getUserCertification(userId);
    return certification.levelInfo.benefits.specialFeatures.includes(feature);
  }

  /**
   * 获取用户手续费折扣率
   */
  static async getFeeDiscount(userId: string): Promise<number> {
    const certification = await this.getUserCertification(userId);
    return certification.levelInfo.benefits.feeDiscount;
  }

  /**
   * 获取用户每日裁定限额
   */
  static async getDailyJudgmentLimit(userId: string): Promise<number> {
    const certification = await this.getUserCertification(userId);
    return certification.levelInfo.benefits.dailyJudgmentLimit;
  }
}
