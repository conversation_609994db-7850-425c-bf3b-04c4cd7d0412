/**
 * Social Bet结算服务
 * 处理赌约结束后的福气分配和转账
 */

import { supabase } from '@/lib/supabase';
import { FortuneService } from '@/services/fortune/FortuneService';
import { SocialBetService } from './SocialBetService';

// 结算配置
export const SETTLEMENT_CONFIG = {
  PLATFORM_FEE_RATE: 0.05,      // 平台手续费率 5%
  JUDGMENT_REWARD_RATE: 0.02,   // 裁判奖励率 2%
  REFERRAL_REWARD_RATE: 0.10,   // 转发奖励率 10%
  MIN_SETTLEMENT_AMOUNT: 1      // 最小结算金额
};

// 结算结果接口
export interface SettlementResult {
  betId: string;
  totalPool: number;
  platformFee: number;
  judgmentRewards: number;
  referralRewards: number;
  winnerPayouts: number;
  winners: Array<{
    userId: string;
    betAmount: number;
    payoutAmount: number;
    netProfit: number;
  }>;
  referralPayouts: Array<{
    referrerId: string;
    amount: number;
    fromUserId: string;
  }>;
  judgmentPayouts: Array<{
    judgeId: string;
    amount: number;
    accuracy: number;
  }>;
  settlementDate: string;
}

export class SettlementService {
  /**
   * 结算赌约
   */
  static async settleBet(betId: string): Promise<SettlementResult> {
    // 获取赌约信息
    const bet = await SocialBetService.getBetById(betId);
    if (!bet) {
      throw new Error('赌约不存在');
    }

    if (bet.status !== 'settled') {
      throw new Error('赌约状态不正确，无法结算');
    }

    if (!bet.winningOption) {
      throw new Error('未确定获胜选项');
    }

    // 检查是否已经结算过
    const { data: existingSettlement } = await supabase
      .from('fortune_transactions')
      .select('id')
      .eq('reference_id', betId)
      .eq('transaction_type', 'bet_settlement')
      .limit(1);

    if (existingSettlement && existingSettlement.length > 0) {
      throw new Error('赌约已经结算过');
    }

    // 获取所有参与者
    const { data: participants } = await supabase
      .from('bet_participants')
      .select('*')
      .eq('bet_id', betId)
      .eq('status', 'active');

    if (!participants || participants.length === 0) {
      throw new Error('没有有效的参与者');
    }

    // 计算获胜者和失败者
    const winners = participants.filter(p => p.selected_option === bet.winningOption);
    const losers = participants.filter(p => p.selected_option !== bet.winningOption);

    const totalPool = bet.totalPool;
    const winnersTotalBet = winners.reduce((sum, w) => sum + w.bet_amount, 0);
    const losersTotalBet = losers.reduce((sum, l) => sum + l.bet_amount, 0);

    // 计算各项费用
    const platformFee = totalPool * bet.platformFeeRate;
    const judgmentRewards = totalPool * SETTLEMENT_CONFIG.JUDGMENT_REWARD_RATE;
    const referralRewards = totalPool * bet.referralRewardRate;

    // 可分配给获胜者的金额
    const availableForWinners = totalPool - platformFee - judgmentRewards - referralRewards;

    // 计算获胜者分配
    const winnerPayouts: SettlementResult['winners'] = [];
    let totalWinnerPayouts = 0;

    if (winners.length > 0 && winnersTotalBet > 0) {
      for (const winner of winners) {
        // 按投注比例分配奖池
        const payoutRatio = winner.bet_amount / winnersTotalBet;
        const payoutAmount = Math.floor(availableForWinners * payoutRatio);
        const netProfit = payoutAmount - winner.bet_amount;

        winnerPayouts.push({
          userId: winner.user_id,
          betAmount: winner.bet_amount,
          payoutAmount,
          netProfit
        });

        totalWinnerPayouts += payoutAmount;
      }
    }

    // 处理转发奖励
    const referralPayouts = await this.calculateReferralRewards(participants, referralRewards);

    // 处理裁判奖励
    const judgmentPayouts = await this.calculateJudgmentRewards(betId, judgmentRewards);

    // 执行福气转账
    await this.executeSettlementTransactions(betId, {
      winners: winnerPayouts,
      referrals: referralPayouts,
      judgments: judgmentPayouts,
      platformFee
    });

    // 更新参与者状态
    await this.updateParticipantStatus(betId, winners, winnerPayouts);

    // 记录结算结果
    const settlementResult: SettlementResult = {
      betId,
      totalPool,
      platformFee,
      judgmentRewards,
      referralRewards,
      winnerPayouts: totalWinnerPayouts,
      winners: winnerPayouts,
      referralPayouts,
      judgmentPayouts,
      settlementDate: new Date().toISOString()
    };

    // 更新赌约最终状态
    await supabase
      .from('social_bets')
      .update({
        status: 'settled',
        updated_at: new Date().toISOString()
      })
      .eq('id', betId);

    return settlementResult;
  }

  /**
   * 计算转发奖励
   */
  private static async calculateReferralRewards(
    participants: any[],
    totalReferralRewards: number
  ): Promise<SettlementResult['referralPayouts']> {
    const referralPayouts: SettlementResult['referralPayouts'] = [];
    
    // 统计每个转发人的贡献
    const referrerContributions = participants.reduce((contributions, p) => {
      if (p.referrer_id) {
        if (!contributions[p.referrer_id]) {
          contributions[p.referrer_id] = {
            totalAmount: 0,
            participants: []
          };
        }
        contributions[p.referrer_id].totalAmount += p.bet_amount;
        contributions[p.referrer_id].participants.push(p.user_id);
      }
      return contributions;
    }, {} as Record<string, { totalAmount: number; participants: string[] }>);

    const totalReferredAmount = Object.values(referrerContributions)
      .reduce((sum, contrib) => sum + contrib.totalAmount, 0);

    if (totalReferredAmount > 0) {
      for (const [referrerId, contribution] of Object.entries(referrerContributions)) {
        const rewardRatio = contribution.totalAmount / totalReferredAmount;
        const rewardAmount = Math.floor(totalReferralRewards * rewardRatio);

        if (rewardAmount >= SETTLEMENT_CONFIG.MIN_SETTLEMENT_AMOUNT) {
          referralPayouts.push({
            referrerId,
            amount: rewardAmount,
            fromUserId: contribution.participants[0] // 简化处理
          });
        }
      }
    }

    return referralPayouts;
  }

  /**
   * 计算裁判奖励
   */
  private static async calculateJudgmentRewards(
    betId: string,
    totalJudgmentRewards: number
  ): Promise<SettlementResult['judgmentPayouts']> {
    const { data: judgments } = await supabase
      .from('bet_judgments')
      .select('*')
      .eq('bet_id', betId);

    if (!judgments || judgments.length === 0) {
      return [];
    }

    const judgmentPayouts: SettlementResult['judgmentPayouts'] = [];
    const correctJudgments = judgments.filter(j => j.is_correct_judgment === true);

    if (correctJudgments.length > 0) {
      const rewardPerJudge = Math.floor(totalJudgmentRewards / correctJudgments.length);

      for (const judgment of correctJudgments) {
        if (rewardPerJudge >= SETTLEMENT_CONFIG.MIN_SETTLEMENT_AMOUNT) {
          judgmentPayouts.push({
            judgeId: judgment.judge_id,
            amount: rewardPerJudge,
            accuracy: 1.0 // 正确裁定
          });
        }
      }
    }

    return judgmentPayouts;
  }

  /**
   * 执行结算交易
   */
  private static async executeSettlementTransactions(
    betId: string,
    payouts: {
      winners: SettlementResult['winners'];
      referrals: SettlementResult['referralPayouts'];
      judgments: SettlementResult['judgmentPayouts'];
      platformFee: number;
    }
  ): Promise<void> {
    // 发放获胜者奖励
    for (const winner of payouts.winners) {
      if (winner.payoutAmount >= SETTLEMENT_CONFIG.MIN_SETTLEMENT_AMOUNT) {
        await FortuneService.addFortune(
          winner.userId,
          winner.payoutAmount,
          'bet_settlement',
          betId,
          `赌约获胜奖励 +${winner.payoutAmount}福气 (净收益: ${winner.netProfit}福气)`
        );
      }
    }

    // 发放转发奖励
    for (const referral of payouts.referrals) {
      await FortuneService.addFortune(
        referral.referrerId,
        referral.amount,
        'referral_reward',
        betId,
        `转发奖励 +${referral.amount}福气`
      );
    }

    // 发放裁判奖励
    for (const judgment of payouts.judgments) {
      await FortuneService.addFortune(
        judgment.judgeId,
        judgment.amount,
        'judgment_settlement_reward',
        betId,
        `裁定结算奖励 +${judgment.amount}福气`
      );
    }

    // 记录平台手续费（可选，用于统计）
    if (payouts.platformFee > 0) {
      // 这里可以记录平台收入，暂时跳过
      console.log(`Platform fee collected: ${payouts.platformFee} fortune from bet ${betId}`);
    }
  }

  /**
   * 更新参与者状态
   */
  private static async updateParticipantStatus(
    betId: string,
    winners: any[],
    winnerPayouts: SettlementResult['winners']
  ): Promise<void> {
    // 更新所有参与者状态为已结算
    await supabase
      .from('bet_participants')
      .update({
        status: 'settled',
        updated_at: new Date().toISOString()
      })
      .eq('bet_id', betId);

    // 更新获胜者的具体信息
    for (const payout of winnerPayouts) {
      await supabase
        .from('bet_participants')
        .update({
          payout_amount: payout.payoutAmount,
          is_winner: true,
          updated_at: new Date().toISOString()
        })
        .eq('bet_id', betId)
        .eq('user_id', payout.userId);
    }

    // 更新失败者状态
    const winnerIds = winnerPayouts.map(w => w.userId);
    await supabase
      .from('bet_participants')
      .update({
        is_winner: false,
        payout_amount: 0,
        updated_at: new Date().toISOString()
      })
      .eq('bet_id', betId)
      .not('user_id', 'in', `(${winnerIds.map(id => `'${id}'`).join(',')})`);
  }

  /**
   * 获取结算统计
   */
  static async getSettlementStats(betId: string): Promise<any> {
    const { data: transactions } = await supabase
      .from('fortune_transactions')
      .select('*')
      .eq('reference_id', betId)
      .in('transaction_type', ['bet_settlement', 'referral_reward', 'judgment_settlement_reward']);

    if (!transactions) return null;

    const stats = {
      totalPayouts: 0,
      winnerPayouts: 0,
      referralRewards: 0,
      judgmentRewards: 0,
      transactionCount: transactions.length
    };

    transactions.forEach(tx => {
      stats.totalPayouts += tx.amount;
      
      switch (tx.transaction_type) {
        case 'bet_settlement':
          stats.winnerPayouts += tx.amount;
          break;
        case 'referral_reward':
          stats.referralRewards += tx.amount;
          break;
        case 'judgment_settlement_reward':
          stats.judgmentRewards += tx.amount;
          break;
      }
    });

    return stats;
  }

  /**
   * 批量结算到期赌约
   */
  static async processExpiredBets(): Promise<void> {
    // 查找需要结算的赌约
    const { data: expiredBets } = await supabase
      .from('social_bets')
      .select('id')
      .eq('status', 'settled')
      .is('winning_option', null);

    if (!expiredBets || expiredBets.length === 0) return;

    for (const bet of expiredBets) {
      try {
        await this.settleBet(bet.id);
        console.log(`Settled bet ${bet.id}`);
      } catch (error) {
        console.error(`Failed to settle bet ${bet.id}:`, error);
      }
    }
  }
}
