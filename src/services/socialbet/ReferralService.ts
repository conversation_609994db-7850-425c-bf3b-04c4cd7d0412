/**
 * 转发奖励服务
 * 赌约转发激励机制，病毒传播追踪和奖励分配
 */

import { supabase } from '@/lib/supabase';
import { FortuneService } from '@/services/fortune/FortuneService';

// 转发奖励配置
export const REFERRAL_CONFIG = {
  // 奖励比例配置
  REWARD_RATES: {
    '1v1': {
      min: 0.05,    // 5%
      max: 0.15,    // 15%
      default: 0.10 // 10%
    },
    '1vN': {
      min: 0.10,    // 10%
      max: 0.50,    // 50%
      default: 0.25 // 25%
    }
  },
  
  // 病毒传播阈值
  VIRAL_THRESHOLDS: {
    LEVEL_1: 5,   // 5个转发
    LEVEL_2: 15,  // 15个转发
    LEVEL_3: 50,  // 50个转发
    LEVEL_4: 100  // 100个转发
  },
  
  // 病毒传播奖励倍数
  VIRAL_MULTIPLIERS: {
    LEVEL_1: 1.2, // 1.2倍
    LEVEL_2: 1.5, // 1.5倍
    LEVEL_3: 2.0, // 2倍
    LEVEL_4: 3.0  // 3倍
  },
  
  // 转发链深度限制
  MAX_REFERRAL_DEPTH: 3,
  
  // 最小奖励金额
  MIN_REWARD_AMOUNT: 1
};

// 转发记录接口
export interface ReferralRecord {
  id: string;
  betId: string;
  referrerId: string;
  refereeId: string;
  referralCode: string;
  depth: number;
  rewardAmount: number;
  status: 'pending' | 'paid' | 'cancelled';
  createdAt: string;
}

// 转发统计接口
export interface ReferralStats {
  betId: string;
  totalReferrals: number;
  totalRewardPool: number;
  viralLevel: number;
  viralMultiplier: number;
  topReferrers: Array<{
    userId: string;
    referralCount: number;
    totalReward: number;
  }>;
  referralChain: ReferralRecord[];
}

// 用户转发统计接口
export interface UserReferralStats {
  userId: string;
  totalReferrals: number;
  totalRewards: number;
  successfulReferrals: number;
  averageReward: number;
  bestPerformingBet: {
    betId: string;
    referralCount: number;
    totalReward: number;
  } | null;
  recentActivity: ReferralRecord[];
}

export class ReferralService {
  /**
   * 生成转发链接
   */
  static async generateReferralLink(
    betId: string,
    referrerId: string,
    platform: 'twitter' | 'discord' | 'telegram' | 'custom' = 'custom'
  ): Promise<string> {
    // 生成唯一的转发码
    const referralCode = this.generateReferralCode(betId, referrerId);
    
    // 获取赌约信息
    const { data: bet } = await supabase
      .from('social_bets')
      .select('title, description')
      .eq('id', betId)
      .single();

    if (!bet) {
      throw new Error('赌约不存在');
    }

    // 构建转发链接
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://sociomint.com';
    const referralUrl = `${baseUrl}/bet/${betId}?ref=${referralCode}`;

    // 根据平台生成不同的分享文案
    const shareText = this.generateShareText(bet.title, bet.description, platform);
    
    return platform === 'custom' 
      ? referralUrl 
      : this.formatPlatformLink(referralUrl, shareText, platform);
  }

  /**
   * 处理转发参与
   */
  static async processReferralParticipation(
    betId: string,
    participantId: string,
    referralCode?: string
  ): Promise<ReferralRecord | null> {
    if (!referralCode) return null;

    // 解析转发码
    const referrerId = this.parseReferralCode(referralCode);
    if (!referrerId || referrerId === participantId) return null;

    // 检查是否已经记录过此转发
    const { data: existingReferral } = await supabase
      .from('bet_participants')
      .select('referrer_id')
      .eq('bet_id', betId)
      .eq('user_id', participantId)
      .single();

    if (existingReferral?.referrer_id) return null;

    // 计算转发深度
    const depth = await this.calculateReferralDepth(betId, referrerId);
    if (depth > REFERRAL_CONFIG.MAX_REFERRAL_DEPTH) return null;

    // 创建转发记录
    const referralRecord: Omit<ReferralRecord, 'id'> = {
      betId,
      referrerId,
      refereeId: participantId,
      referralCode,
      depth,
      rewardAmount: 0, // 将在结算时计算
      status: 'pending',
      createdAt: new Date().toISOString()
    };

    // 这里应该保存到专门的转发记录表
    // 目前简化处理，更新bet_participants表的referrer_id字段
    await supabase
      .from('bet_participants')
      .update({ referrer_id: referrerId })
      .eq('bet_id', betId)
      .eq('user_id', participantId);

    return { id: 'temp-id', ...referralRecord };
  }

  /**
   * 计算转发奖励
   */
  static async calculateReferralRewards(
    betId: string,
    totalPool: number
  ): Promise<Array<{ referrerId: string; amount: number }>> {
    // 获取赌约信息
    const { data: bet } = await supabase
      .from('social_bets')
      .select('bet_type, referral_reward_rate')
      .eq('id', betId)
      .single();

    if (!bet) return [];

    // 获取所有转发参与者
    const { data: referrals } = await supabase
      .from('bet_participants')
      .select('referrer_id, bet_amount')
      .eq('bet_id', betId)
      .not('referrer_id', 'is', null);

    if (!referrals || referrals.length === 0) return [];

    // 计算总转发奖励池
    const totalReferralPool = totalPool * bet.referral_reward_rate;

    // 统计每个转发人的贡献
    const referrerContributions = referrals.reduce((contributions, referral) => {
      const referrerId = referral.referrer_id!;
      if (!contributions[referrerId]) {
        contributions[referrerId] = {
          totalAmount: 0,
          referralCount: 0
        };
      }
      contributions[referrerId].totalAmount += referral.bet_amount;
      contributions[referrerId].referralCount++;
      return contributions;
    }, {} as Record<string, { totalAmount: number; referralCount: number }>);

    // 计算病毒传播倍数
    const totalReferralCount = referrals.length;
    const viralMultiplier = this.calculateViralMultiplier(totalReferralCount);

    // 按贡献比例分配奖励
    const totalContribution = Object.values(referrerContributions)
      .reduce((sum, contrib) => sum + contrib.totalAmount, 0);

    const rewards: Array<{ referrerId: string; amount: number }> = [];

    for (const [referrerId, contribution] of Object.entries(referrerContributions)) {
      if (totalContribution > 0) {
        const baseReward = (contribution.totalAmount / totalContribution) * totalReferralPool;
        const finalReward = Math.floor(baseReward * viralMultiplier);
        
        if (finalReward >= REFERRAL_CONFIG.MIN_REWARD_AMOUNT) {
          rewards.push({
            referrerId,
            amount: finalReward
          });
        }
      }
    }

    return rewards;
  }

  /**
   * 发放转发奖励
   */
  static async distributeReferralRewards(
    betId: string,
    rewards: Array<{ referrerId: string; amount: number }>
  ): Promise<void> {
    for (const reward of rewards) {
      try {
        await FortuneService.addFortune(
          reward.referrerId,
          reward.amount,
          'referral_reward',
          betId,
          `转发奖励 +${reward.amount}福气`
        );

        // 更新转发记录状态
        await supabase
          .from('bet_participants')
          .update({ referral_reward: reward.amount })
          .eq('bet_id', betId)
          .eq('referrer_id', reward.referrerId);

        console.log(`Distributed referral reward: ${reward.amount} to ${reward.referrerId}`);
      } catch (error) {
        console.error(`Failed to distribute referral reward to ${reward.referrerId}:`, error);
      }
    }
  }

  /**
   * 获取转发统计
   */
  static async getReferralStats(betId: string): Promise<ReferralStats> {
    // 获取转发参与数据
    const { data: referrals } = await supabase
      .from('bet_participants')
      .select(`
        referrer_id,
        bet_amount,
        referral_reward,
        users!referrer_id (
          username
        )
      `)
      .eq('bet_id', betId)
      .not('referrer_id', 'is', null);

    const totalReferrals = referrals?.length || 0;
    const totalRewardPool = referrals?.reduce((sum, r) => sum + (r.referral_reward || 0), 0) || 0;
    const viralLevel = this.getViralLevel(totalReferrals);
    const viralMultiplier = this.calculateViralMultiplier(totalReferrals);

    // 统计顶级转发者
    const referrerStats = referrals?.reduce((stats, referral) => {
      const referrerId = referral.referrer_id!;
      if (!stats[referrerId]) {
        stats[referrerId] = {
          userId: referrerId,
          referralCount: 0,
          totalReward: 0
        };
      }
      stats[referrerId].referralCount++;
      stats[referrerId].totalReward += referral.referral_reward || 0;
      return stats;
    }, {} as Record<string, any>) || {};

    const topReferrers = Object.values(referrerStats)
      .sort((a: any, b: any) => b.referralCount - a.referralCount)
      .slice(0, 10);

    return {
      betId,
      totalReferrals,
      totalRewardPool,
      viralLevel,
      viralMultiplier,
      topReferrers,
      referralChain: [] // 简化处理
    };
  }

  /**
   * 获取用户转发统计
   */
  static async getUserReferralStats(userId: string): Promise<UserReferralStats> {
    // 获取用户转发数据
    const { data: referrals } = await supabase
      .from('bet_participants')
      .select(`
        bet_id,
        bet_amount,
        referral_reward,
        created_at,
        social_bets (
          title
        )
      `)
      .eq('referrer_id', userId);

    const totalReferrals = referrals?.length || 0;
    const totalRewards = referrals?.reduce((sum, r) => sum + (r.referral_reward || 0), 0) || 0;
    const successfulReferrals = referrals?.filter(r => r.referral_reward > 0).length || 0;
    const averageReward = totalReferrals > 0 ? totalRewards / totalReferrals : 0;

    // 找出表现最好的赌约
    const betPerformance = referrals?.reduce((performance, referral) => {
      const betId = referral.bet_id;
      if (!performance[betId]) {
        performance[betId] = {
          betId,
          referralCount: 0,
          totalReward: 0
        };
      }
      performance[betId].referralCount++;
      performance[betId].totalReward += referral.referral_reward || 0;
      return performance;
    }, {} as Record<string, any>) || {};

    const bestPerformingBet = Object.values(betPerformance)
      .sort((a: any, b: any) => b.totalReward - a.totalReward)[0] || null;

    return {
      userId,
      totalReferrals,
      totalRewards,
      successfulReferrals,
      averageReward,
      bestPerformingBet,
      recentActivity: [] // 简化处理
    };
  }

  /**
   * 生成转发码
   */
  private static generateReferralCode(betId: string, referrerId: string): string {
    const timestamp = Date.now().toString(36);
    const userHash = referrerId.slice(-6);
    const betHash = betId.slice(-4);
    return `${userHash}${betHash}${timestamp}`;
  }

  /**
   * 解析转发码
   */
  private static parseReferralCode(referralCode: string): string | null {
    // 简化处理，实际应该有更复杂的解析逻辑
    if (referralCode.length < 10) return null;
    return referralCode.slice(0, 6); // 返回用户ID的一部分
  }

  /**
   * 计算转发深度
   */
  private static async calculateReferralDepth(betId: string, referrerId: string): Promise<number> {
    // 简化处理，返回固定深度
    return 1;
  }

  /**
   * 计算病毒传播倍数
   */
  private static calculateViralMultiplier(referralCount: number): number {
    if (referralCount >= REFERRAL_CONFIG.VIRAL_THRESHOLDS.LEVEL_4) {
      return REFERRAL_CONFIG.VIRAL_MULTIPLIERS.LEVEL_4;
    } else if (referralCount >= REFERRAL_CONFIG.VIRAL_THRESHOLDS.LEVEL_3) {
      return REFERRAL_CONFIG.VIRAL_MULTIPLIERS.LEVEL_3;
    } else if (referralCount >= REFERRAL_CONFIG.VIRAL_THRESHOLDS.LEVEL_2) {
      return REFERRAL_CONFIG.VIRAL_MULTIPLIERS.LEVEL_2;
    } else if (referralCount >= REFERRAL_CONFIG.VIRAL_THRESHOLDS.LEVEL_1) {
      return REFERRAL_CONFIG.VIRAL_MULTIPLIERS.LEVEL_1;
    }
    return 1.0;
  }

  /**
   * 获取病毒传播等级
   */
  private static getViralLevel(referralCount: number): number {
    if (referralCount >= REFERRAL_CONFIG.VIRAL_THRESHOLDS.LEVEL_4) return 4;
    if (referralCount >= REFERRAL_CONFIG.VIRAL_THRESHOLDS.LEVEL_3) return 3;
    if (referralCount >= REFERRAL_CONFIG.VIRAL_THRESHOLDS.LEVEL_2) return 2;
    if (referralCount >= REFERRAL_CONFIG.VIRAL_THRESHOLDS.LEVEL_1) return 1;
    return 0;
  }

  /**
   * 生成分享文案
   */
  private static generateShareText(title: string, description: string, platform: string): string {
    const baseText = `🎲 ${title}\n\n${description}\n\n快来参与这个有趣的赌约吧！`;
    
    switch (platform) {
      case 'twitter':
        return `${baseText}\n\n#SocioMint #SocialBet`;
      case 'discord':
        return `**${title}**\n\n${description}\n\n快来参与这个有趣的赌约吧！ 🎲`;
      case 'telegram':
        return `🎲 *${title}*\n\n${description}\n\n快来参与这个有趣的赌约吧！`;
      default:
        return baseText;
    }
  }

  /**
   * 格式化平台链接
   */
  private static formatPlatformLink(url: string, text: string, platform: string): string {
    const encodedText = encodeURIComponent(text);
    const encodedUrl = encodeURIComponent(url);
    
    switch (platform) {
      case 'twitter':
        return `https://twitter.com/intent/tweet?text=${encodedText}&url=${encodedUrl}`;
      case 'telegram':
        return `https://t.me/share/url?url=${encodedUrl}&text=${encodedText}`;
      default:
        return url;
    }
  }
}
