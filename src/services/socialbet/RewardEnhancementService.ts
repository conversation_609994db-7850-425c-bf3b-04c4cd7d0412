/**
 * Social Bet奖励增强服务
 * 将Social Bet与福气奖励系统深度集成，提供多层次奖励机制
 */

import { supabase } from '@/lib/supabase';
import { FortuneService } from '@/services/fortune/FortuneService';

// 奖励配置
export const REWARD_CONFIG = {
  // 投注行为奖励
  PARTICIPATION_REWARDS: {
    FIRST_BET_BONUS: 50,           // 首次投注奖励
    DAILY_PARTICIPATION_BONUS: 10, // 每日参与奖励
    STREAK_MULTIPLIER: 1.2,        // 连续参与倍数
    MIN_BET_FOR_REWARD: 10         // 最小投注金额获得奖励
  },
  
  // 裁定参与奖励
  JUDGMENT_REWARDS: {
    BASE_PARTICIPATION: 5,         // 基础参与奖励
    ACCURACY_BONUS: 15,           // 准确性奖励
    CONSENSUS_BONUS: 10,          // 共识奖励
    STREAK_BONUS: 5,              // 连胜奖励
    EXPERT_BONUS: 20              // 专家级裁判奖励
  },
  
  // 获胜奖励优化
  WINNING_REWARDS: {
    BASE_WIN_BONUS: 20,           // 基础获胜奖励
    UNDERDOG_MULTIPLIER: 2.0,     // 冷门获胜倍数
    PERFECT_PREDICTION_BONUS: 100, // 完美预测奖励
    CONSECUTIVE_WIN_BONUS: 30     // 连胜奖励
  },
  
  // 社交分享奖励
  SOCIAL_REWARDS: {
    SHARE_BONUS: 5,               // 分享奖励
    VIRAL_THRESHOLD: 10,          // 病毒传播阈值
    VIRAL_BONUS: 50,              // 病毒传播奖励
    REFERRAL_BONUS: 25            // 推荐奖励
  }
};

// 用户活动统计接口
export interface UserActivity {
  userId: string;
  totalBets: number;
  totalWins: number;
  totalJudgments: number;
  correctJudgments: number;
  currentWinStreak: number;
  currentJudgmentStreak: number;
  lastActivityDate: string;
  totalRewardsEarned: number;
}

export class RewardEnhancementService {
  /**
   * 处理投注行为奖励
   */
  static async processParticipationReward(
    userId: string,
    betId: string,
    betAmount: number
  ): Promise<number> {
    let totalReward = 0;

    // 检查是否符合最小投注要求
    if (betAmount < REWARD_CONFIG.PARTICIPATION_REWARDS.MIN_BET_FOR_REWARD) {
      return 0;
    }

    // 获取用户活动统计
    const activity = await this.getUserActivity(userId);

    // 首次投注奖励
    if (activity.totalBets === 0) {
      const firstBetBonus = REWARD_CONFIG.PARTICIPATION_REWARDS.FIRST_BET_BONUS;
      await FortuneService.addFortune(
        userId,
        firstBetBonus,
        'first_bet_bonus',
        betId,
        `首次投注奖励 +${firstBetBonus}福气`
      );
      totalReward += firstBetBonus;
    }

    // 每日参与奖励
    const today = new Date().toISOString().split('T')[0];
    const lastActivityDate = activity.lastActivityDate?.split('T')[0];
    
    if (lastActivityDate !== today) {
      const dailyBonus = REWARD_CONFIG.PARTICIPATION_REWARDS.DAILY_PARTICIPATION_BONUS;
      
      // 连续参与倍数
      const isConsecutive = this.isConsecutiveDay(lastActivityDate, today);
      const finalBonus = isConsecutive 
        ? Math.floor(dailyBonus * REWARD_CONFIG.PARTICIPATION_REWARDS.STREAK_MULTIPLIER)
        : dailyBonus;

      await FortuneService.addFortune(
        userId,
        finalBonus,
        'daily_participation_bonus',
        betId,
        `每日参与奖励 +${finalBonus}福气${isConsecutive ? ' (连续参与加成)' : ''}`
      );
      totalReward += finalBonus;
    }

    return totalReward;
  }

  /**
   * 处理裁定参与奖励
   */
  static async processJudgmentReward(
    judgeId: string,
    betId: string,
    isCorrect: boolean,
    isConsensus: boolean,
    judgeLevel: number
  ): Promise<number> {
    let totalReward = REWARD_CONFIG.JUDGMENT_REWARDS.BASE_PARTICIPATION;

    // 准确性奖励
    if (isCorrect) {
      totalReward += REWARD_CONFIG.JUDGMENT_REWARDS.ACCURACY_BONUS;
    }

    // 共识奖励
    if (isConsensus) {
      totalReward += REWARD_CONFIG.JUDGMENT_REWARDS.CONSENSUS_BONUS;
    }

    // 专家级裁判奖励
    if (judgeLevel >= 3) {
      totalReward += REWARD_CONFIG.JUDGMENT_REWARDS.EXPERT_BONUS;
    }

    // 连胜奖励
    const activity = await this.getUserActivity(judgeId);
    if (activity.currentJudgmentStreak >= 5) {
      const streakBonus = REWARD_CONFIG.JUDGMENT_REWARDS.STREAK_BONUS * 
        Math.floor(activity.currentJudgmentStreak / 5);
      totalReward += streakBonus;
    }

    // 发放奖励
    await FortuneService.addFortune(
      judgeId,
      totalReward,
      'enhanced_judgment_reward',
      betId,
      `增强裁定奖励 +${totalReward}福气`
    );

    return totalReward;
  }

  /**
   * 处理获胜奖励优化
   */
  static async processWinningReward(
    userId: string,
    betId: string,
    betAmount: number,
    payoutAmount: number,
    isUnderdog: boolean = false
  ): Promise<number> {
    let totalReward = REWARD_CONFIG.WINNING_REWARDS.BASE_WIN_BONUS;

    // 冷门获胜倍数
    if (isUnderdog) {
      totalReward = Math.floor(totalReward * REWARD_CONFIG.WINNING_REWARDS.UNDERDOG_MULTIPLIER);
    }

    // 完美预测奖励（高赔率获胜）
    const roi = (payoutAmount - betAmount) / betAmount;
    if (roi >= 3.0) { // 3倍以上回报
      totalReward += REWARD_CONFIG.WINNING_REWARDS.PERFECT_PREDICTION_BONUS;
    }

    // 连胜奖励
    const activity = await this.getUserActivity(userId);
    if (activity.currentWinStreak >= 3) {
      const streakBonus = REWARD_CONFIG.WINNING_REWARDS.CONSECUTIVE_WIN_BONUS * 
        Math.floor(activity.currentWinStreak / 3);
      totalReward += streakBonus;
    }

    // 发放奖励
    await FortuneService.addFortune(
      userId,
      totalReward,
      'enhanced_winning_reward',
      betId,
      `增强获胜奖励 +${totalReward}福气`
    );

    return totalReward;
  }

  /**
   * 处理社交分享奖励
   */
  static async processSocialReward(
    userId: string,
    betId: string,
    shareType: 'share' | 'viral' | 'referral',
    metadata?: any
  ): Promise<number> {
    let reward = 0;

    switch (shareType) {
      case 'share':
        reward = REWARD_CONFIG.SOCIAL_REWARDS.SHARE_BONUS;
        await FortuneService.addFortune(
          userId,
          reward,
          'social_share_reward',
          betId,
          `分享赌约奖励 +${reward}福气`
        );
        break;

      case 'viral':
        // 检查是否达到病毒传播阈值
        const shareCount = metadata?.shareCount || 0;
        if (shareCount >= REWARD_CONFIG.SOCIAL_REWARDS.VIRAL_THRESHOLD) {
          reward = REWARD_CONFIG.SOCIAL_REWARDS.VIRAL_BONUS;
          await FortuneService.addFortune(
            userId,
            reward,
            'viral_spread_reward',
            betId,
            `病毒传播奖励 +${reward}福气`
          );
        }
        break;

      case 'referral':
        reward = REWARD_CONFIG.SOCIAL_REWARDS.REFERRAL_BONUS;
        await FortuneService.addFortune(
          userId,
          reward,
          'referral_reward',
          betId,
          `推荐用户奖励 +${reward}福气`
        );
        break;
    }

    return reward;
  }

  /**
   * 获取用户活动统计
   */
  static async getUserActivity(userId: string): Promise<UserActivity> {
    // 获取投注统计
    const { data: betStats } = await supabase
      .from('bet_participants')
      .select('bet_amount, is_winner, created_at')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    // 获取裁定统计
    const { data: judgmentStats } = await supabase
      .from('bet_judgments')
      .select('is_correct_judgment, created_at')
      .eq('judge_id', userId)
      .order('created_at', { ascending: false });

    // 获取奖励统计
    const { data: rewardStats } = await supabase
      .from('fortune_transactions')
      .select('amount')
      .eq('user_id', userId)
      .in('transaction_type', [
        'first_bet_bonus',
        'daily_participation_bonus',
        'enhanced_judgment_reward',
        'enhanced_winning_reward',
        'social_share_reward',
        'viral_spread_reward',
        'referral_reward'
      ]);

    // 计算统计数据
    const totalBets = betStats?.length || 0;
    const totalWins = betStats?.filter(b => b.is_winner === true).length || 0;
    const totalJudgments = judgmentStats?.length || 0;
    const correctJudgments = judgmentStats?.filter(j => j.is_correct_judgment === true).length || 0;
    const totalRewardsEarned = rewardStats?.reduce((sum, r) => sum + r.amount, 0) || 0;

    // 计算连胜
    const currentWinStreak = this.calculateWinStreak(betStats || []);
    const currentJudgmentStreak = this.calculateJudgmentStreak(judgmentStats || []);

    // 最后活动时间
    const lastActivityDate = betStats?.[0]?.created_at || judgmentStats?.[0]?.created_at || '';

    return {
      userId,
      totalBets,
      totalWins,
      totalJudgments,
      correctJudgments,
      currentWinStreak,
      currentJudgmentStreak,
      lastActivityDate,
      totalRewardsEarned
    };
  }

  /**
   * 计算获胜连胜
   */
  private static calculateWinStreak(betHistory: any[]): number {
    let streak = 0;
    for (const bet of betHistory) {
      if (bet.is_winner === true) {
        streak++;
      } else if (bet.is_winner === false) {
        break;
      }
      // is_winner === null 的情况跳过（未结算）
    }
    return streak;
  }

  /**
   * 计算裁定连胜
   */
  private static calculateJudgmentStreak(judgmentHistory: any[]): number {
    let streak = 0;
    for (const judgment of judgmentHistory) {
      if (judgment.is_correct_judgment === true) {
        streak++;
      } else if (judgment.is_correct_judgment === false) {
        break;
      }
      // is_correct_judgment === null 的情况跳过（未确定）
    }
    return streak;
  }

  /**
   * 检查是否为连续日期
   */
  private static isConsecutiveDay(lastDate: string, currentDate: string): boolean {
    if (!lastDate) return false;
    
    const last = new Date(lastDate);
    const current = new Date(currentDate);
    const diffTime = current.getTime() - last.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays === 1;
  }

  /**
   * 获取用户奖励统计
   */
  static async getUserRewardStats(userId: string): Promise<any> {
    const activity = await this.getUserActivity(userId);
    
    // 计算各类奖励占比
    const { data: rewardBreakdown } = await supabase
      .from('fortune_transactions')
      .select('transaction_type, amount')
      .eq('user_id', userId)
      .in('transaction_type', [
        'first_bet_bonus',
        'daily_participation_bonus',
        'enhanced_judgment_reward',
        'enhanced_winning_reward',
        'social_share_reward',
        'viral_spread_reward',
        'referral_reward'
      ]);

    const breakdown = rewardBreakdown?.reduce((acc, tx) => {
      acc[tx.transaction_type] = (acc[tx.transaction_type] || 0) + tx.amount;
      return acc;
    }, {} as Record<string, number>) || {};

    return {
      activity,
      rewardBreakdown: breakdown,
      totalRewards: activity.totalRewardsEarned,
      averageRewardPerBet: activity.totalBets > 0 ? activity.totalRewardsEarned / activity.totalBets : 0,
      winRate: activity.totalBets > 0 ? (activity.totalWins / activity.totalBets) * 100 : 0,
      judgmentAccuracy: activity.totalJudgments > 0 ? (activity.correctJudgments / activity.totalJudgments) * 100 : 0
    };
  }
}
