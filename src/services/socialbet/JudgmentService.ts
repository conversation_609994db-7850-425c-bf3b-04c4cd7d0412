/**
 * Social Bet裁定服务
 * 处理三轮DAO裁定机制，认证等级验证，投票统计
 */

import { supabase } from '@/lib/supabase';
import { FortuneService } from '@/services/fortune/FortuneService';

// 裁定配置
export const JUDGMENT_CONFIG = {
  ROUNDS: 3,
  ROUND_DURATION_HOURS: 24, // 每轮24小时
  MIN_JUDGES_PER_ROUND: 3,
  CONSENSUS_THRESHOLD: 0.6, // 60%共识阈值
  CERTIFICATION_REQUIREMENTS: {
    1: 0, // 第一轮：无认证要求
    2: 1, // 第二轮：至少1级认证
    3: 2  // 第三轮：至少2级认证
  },
  DAILY_JUDGMENT_LIMITS: {
    0: 3,  // 0级认证：每日3次
    1: 5,  // 1级认证：每日5次
    2: 8,  // 2级认证：每日8次
    3: 12, // 3级认证：每日12次
    4: 20, // 4级认证：每日20次
    5: 30  // 5级认证：每日30次
  },
  JUDGMENT_REWARDS: {
    BASE_REWARD: 5,      // 基础奖励5福气
    ACCURACY_BONUS: 10,  // 准确奖励10福气
    CONSENSUS_BONUS: 5   // 共识奖励5福气
  }
};

// 裁定投票接口
export interface JudgmentVote {
  betId: string;
  judgeId: string;
  round: number;
  selectedOption: string;
  confidenceLevel: number; // 1-5
  reasoning?: string;
}

// 裁定结果接口
export interface JudgmentResult {
  betId: string;
  round: number;
  winningOption?: string;
  consensus: boolean;
  voteCount: number;
  optionVotes: Record<string, number>;
  nextRound?: number;
  isComplete: boolean;
}

export class JudgmentService {
  /**
   * 检查用户裁定资格
   */
  static async checkJudgmentEligibility(
    userId: string,
    betId: string,
    round: number
  ): Promise<{ eligible: boolean; reason?: string }> {
    // 获取用户信誉信息
    const { data: reputation } = await supabase
      .from('user_reputation')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (!reputation) {
      return { eligible: false, reason: '用户信誉信息不存在' };
    }

    // 检查认证等级要求
    const requiredLevel = JUDGMENT_CONFIG.CERTIFICATION_REQUIREMENTS[round as keyof typeof JUDGMENT_CONFIG.CERTIFICATION_REQUIREMENTS];
    if (reputation.certification_level < requiredLevel) {
      return { 
        eligible: false, 
        reason: `第${round}轮裁定需要${requiredLevel}级以上认证` 
      };
    }

    // 检查每日裁定次数限制
    const today = new Date().toISOString().split('T')[0];
    if (reputation.last_judgment_date === today) {
      const dailyLimit = JUDGMENT_CONFIG.DAILY_JUDGMENT_LIMITS[reputation.certification_level as keyof typeof JUDGMENT_CONFIG.DAILY_JUDGMENT_LIMITS];
      if (reputation.daily_judgment_count >= dailyLimit) {
        return { 
          eligible: false, 
          reason: `今日裁定次数已达上限(${dailyLimit}次)` 
        };
      }
    }

    // 检查利益冲突
    const { data: participation } = await supabase
      .from('bet_participants')
      .select('id')
      .eq('bet_id', betId)
      .eq('user_id', userId)
      .single();

    if (participation) {
      return { eligible: false, reason: '不能裁定自己参与的赌约' };
    }

    // 检查是否已在此轮投票
    const { data: existingVote } = await supabase
      .from('bet_judgments')
      .select('id')
      .eq('bet_id', betId)
      .eq('judge_id', userId)
      .eq('judgment_round', round)
      .single();

    if (existingVote) {
      return { eligible: false, reason: '已在此轮投票' };
    }

    return { eligible: true };
  }

  /**
   * 提交裁定投票
   */
  static async submitJudgment(vote: JudgmentVote): Promise<string> {
    // 检查裁定资格
    const eligibility = await this.checkJudgmentEligibility(
      vote.judgeId,
      vote.betId,
      vote.round
    );

    if (!eligibility.eligible) {
      throw new Error(eligibility.reason || '不符合裁定资格');
    }

    // 获取赌约信息
    const { data: bet } = await supabase
      .from('social_bets')
      .select('*')
      .eq('id', vote.betId)
      .single();

    if (!bet) {
      throw new Error('赌约不存在');
    }

    // 验证赌约状态
    if (bet.status !== 'judging') {
      throw new Error('赌约不在裁定状态');
    }

    // 验证当前轮次
    if (bet.current_judgment_round !== vote.round) {
      throw new Error(`当前为第${bet.current_judgment_round}轮裁定`);
    }

    // 验证选项
    const validOptions = bet.options.map((opt: any) => opt.id);
    if (!validOptions.includes(vote.selectedOption)) {
      throw new Error('无效的选项');
    }

    // 验证信心等级
    if (vote.confidenceLevel < 1 || vote.confidenceLevel > 5) {
      throw new Error('信心等级必须在1-5之间');
    }

    // 获取裁判信誉信息
    const { data: reputation } = await supabase
      .from('user_reputation')
      .select('*')
      .eq('user_id', vote.judgeId)
      .single();

    if (!reputation) {
      throw new Error('裁判信誉信息不存在');
    }

    // 创建裁定记录
    const { data: judgment, error } = await supabase
      .from('bet_judgments')
      .insert({
        bet_id: vote.betId,
        judge_id: vote.judgeId,
        judgment_round: vote.round,
        selected_option: vote.selectedOption,
        confidence_level: vote.confidenceLevel,
        reasoning: vote.reasoning,
        judge_certification_level: reputation.certification_level,
        judge_reputation_score: reputation.reputation_score,
        reward_amount: JUDGMENT_CONFIG.JUDGMENT_REWARDS.BASE_REWARD
      })
      .select()
      .single();

    if (error) {
      throw new Error(`提交裁定失败: ${error.message}`);
    }

    // 更新裁判每日统计
    const today = new Date().toISOString().split('T')[0];
    const newDailyCount = reputation.last_judgment_date === today 
      ? reputation.daily_judgment_count + 1 
      : 1;

    await supabase
      .from('user_reputation')
      .update({
        daily_judgment_count: newDailyCount,
        last_judgment_date: today,
        total_judgments: reputation.total_judgments + 1,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', vote.judgeId);

    // 发放基础裁定奖励
    await FortuneService.addFortune(
      vote.judgeId,
      JUDGMENT_CONFIG.JUDGMENT_REWARDS.BASE_REWARD,
      'judgment_reward',
      vote.betId,
      `参与第${vote.round}轮裁定奖励 +${JUDGMENT_CONFIG.JUDGMENT_REWARDS.BASE_REWARD}福气`
    );

    // 检查是否需要进入下一轮或结束裁定
    await this.checkRoundCompletion(vote.betId, vote.round);

    return judgment.id;
  }

  /**
   * 检查轮次完成情况
   */
  static async checkRoundCompletion(betId: string, round: number): Promise<void> {
    // 获取当前轮次的投票
    const { data: votes } = await supabase
      .from('bet_judgments')
      .select('selected_option, confidence_level')
      .eq('bet_id', betId)
      .eq('judgment_round', round);

    if (!votes || votes.length < JUDGMENT_CONFIG.MIN_JUDGES_PER_ROUND) {
      return; // 投票数不足，继续等待
    }

    // 计算投票结果
    const result = this.calculateJudgmentResult(betId, round, votes);

    if (result.consensus || round >= JUDGMENT_CONFIG.ROUNDS) {
      // 达成共识或已是最后一轮，结束裁定
      await this.completeJudgment(betId, result);
    } else {
      // 进入下一轮
      await this.startNextRound(betId, round + 1);
    }
  }

  /**
   * 计算裁定结果
   */
  static calculateJudgmentResult(
    betId: string,
    round: number,
    votes: Array<{ selected_option: string; confidence_level: number }>
  ): JudgmentResult {
    const voteCount = votes.length;
    const optionVotes: Record<string, number> = {};

    // 统计投票（考虑信心等级权重）
    votes.forEach(vote => {
      const weight = vote.confidence_level; // 信心等级作为权重
      optionVotes[vote.selected_option] = (optionVotes[vote.selected_option] || 0) + weight;
    });

    // 找出得票最多的选项
    let winningOption: string | undefined;
    let maxVotes = 0;
    let totalWeightedVotes = 0;

    Object.entries(optionVotes).forEach(([option, votes]) => {
      totalWeightedVotes += votes;
      if (votes > maxVotes) {
        maxVotes = votes;
        winningOption = option;
      }
    });

    // 检查是否达成共识
    const consensus = winningOption && totalWeightedVotes > 0
      ? (maxVotes / totalWeightedVotes) >= JUDGMENT_CONFIG.CONSENSUS_THRESHOLD
      : false;

    return {
      betId,
      round,
      winningOption,
      consensus,
      voteCount,
      optionVotes,
      nextRound: consensus ? undefined : round + 1,
      isComplete: consensus || round >= JUDGMENT_CONFIG.ROUNDS
    };
  }

  /**
   * 完成裁定
   */
  static async completeJudgment(betId: string, result: JudgmentResult): Promise<void> {
    // 更新赌约状态
    await supabase
      .from('social_bets')
      .update({
        status: 'confirming',
        winning_option: result.winningOption,
        result_confirmation_deadline: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24小时确认期
        updated_at: new Date().toISOString()
      })
      .eq('id', betId);

    // 发放裁判奖励
    await this.distributeJudgmentRewards(betId, result.winningOption);
  }

  /**
   * 开始下一轮裁定
   */
  static async startNextRound(betId: string, nextRound: number): Promise<void> {
    await supabase
      .from('social_bets')
      .update({
        current_judgment_round: nextRound,
        updated_at: new Date().toISOString()
      })
      .eq('id', betId);
  }

  /**
   * 分配裁判奖励
   */
  static async distributeJudgmentRewards(betId: string, winningOption?: string): Promise<void> {
    if (!winningOption) return;

    // 获取所有裁定记录
    const { data: judgments } = await supabase
      .from('bet_judgments')
      .select('*')
      .eq('bet_id', betId);

    if (!judgments) return;

    // 计算准确裁定的裁判
    const correctJudges = judgments.filter(j => j.selected_option === winningOption);
    const totalJudges = judgments.length;

    // 更新裁定准确性
    for (const judgment of judgments) {
      const isCorrect = judgment.selected_option === winningOption;
      
      // 更新裁定记录
      await supabase
        .from('bet_judgments')
        .update({ is_correct_judgment: isCorrect })
        .eq('id', judgment.id);

      // 发放准确性奖励
      if (isCorrect) {
        const accuracyReward = JUDGMENT_CONFIG.JUDGMENT_REWARDS.ACCURACY_BONUS;
        
        await FortuneService.addFortune(
          judgment.judge_id,
          accuracyReward,
          'judgment_accuracy_reward',
          betId,
          `裁定准确奖励 +${accuracyReward}福气`
        );

        // 更新裁判信誉
        await this.updateJudgeReputation(judgment.judge_id, true);
      } else {
        // 错误裁定扣分
        await this.updateJudgeReputation(judgment.judge_id, false);
      }
    }

    // 发放共识奖励（如果达成高度共识）
    const consensusRate = correctJudges.length / totalJudges;
    if (consensusRate >= 0.8) { // 80%以上共识
      for (const judge of correctJudges) {
        await FortuneService.addFortune(
          judge.judge_id,
          JUDGMENT_CONFIG.JUDGMENT_REWARDS.CONSENSUS_BONUS,
          'judgment_consensus_reward',
          betId,
          `高度共识奖励 +${JUDGMENT_CONFIG.JUDGMENT_REWARDS.CONSENSUS_BONUS}福气`
        );
      }
    }
  }

  /**
   * 更新裁判信誉
   */
  static async updateJudgeReputation(judgeId: string, isCorrect: boolean): Promise<void> {
    const { data: reputation } = await supabase
      .from('user_reputation')
      .select('*')
      .eq('user_id', judgeId)
      .single();

    if (!reputation) return;

    const newCorrectJudgments = isCorrect 
      ? reputation.correct_judgments + 1 
      : reputation.correct_judgments;

    const newReputationScore = isCorrect 
      ? reputation.reputation_score + 1 
      : Math.max(0, reputation.reputation_score - 5); // 错误裁定扣5分

    const newAccuracyRate = reputation.total_judgments > 0 
      ? newCorrectJudgments / reputation.total_judgments 
      : 0;

    // 更新连胜记录
    let newCurrentStreak = reputation.current_streak;
    let newBestStreak = reputation.best_streak;

    if (isCorrect) {
      newCurrentStreak++;
      newBestStreak = Math.max(newBestStreak, newCurrentStreak);
    } else {
      newCurrentStreak = 0;
    }

    await supabase
      .from('user_reputation')
      .update({
        correct_judgments: newCorrectJudgments,
        reputation_score: newReputationScore,
        accuracy_rate: newAccuracyRate,
        current_streak: newCurrentStreak,
        best_streak: newBestStreak,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', judgeId);
  }

  /**
   * 获取裁定统计
   */
  static async getJudgmentStats(betId: string): Promise<any> {
    const { data: judgments } = await supabase
      .from('bet_judgments')
      .select('*')
      .eq('bet_id', betId)
      .order('judgment_round')
      .order('created_at');

    if (!judgments) return null;

    // 按轮次分组统计
    const roundStats = judgments.reduce((stats, j) => {
      const round = j.judgment_round;
      if (!stats[round]) {
        stats[round] = {
          round,
          votes: {},
          totalVotes: 0,
          judges: []
        };
      }

      stats[round].votes[j.selected_option] = (stats[round].votes[j.selected_option] || 0) + 1;
      stats[round].totalVotes++;
      stats[round].judges.push({
        judgeId: j.judge_id,
        selectedOption: j.selected_option,
        confidenceLevel: j.confidence_level,
        reasoning: j.reasoning,
        certificationLevel: j.judge_certification_level,
        reputationScore: j.judge_reputation_score
      });

      return stats;
    }, {} as Record<number, any>);

    return {
      totalJudgments: judgments.length,
      roundStats: Object.values(roundStats)
    };
  }
}
