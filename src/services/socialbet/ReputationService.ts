/**
 * 信誉积分服务
 * 裁判信誉积分计算和等级管理
 */

import { supabase } from '@/lib/supabase';
import { FortuneService } from '@/services/fortune/FortuneService';

// 信誉积分配置
export const REPUTATION_CONFIG = {
  // 积分规则
  SCORING: {
    CORRECT_JUDGMENT: 1,      // 正确裁定 +1分
    INCORRECT_JUDGMENT: -5,   // 错误裁定 -5分
    CONSENSUS_BONUS: 2,       // 共识奖励 +2分
    STREAK_BONUS: 1,          // 连胜奖励 +1分/每5连胜
    PARTICIPATION_BONUS: 0.5  // 参与奖励 +0.5分
  },
  
  // 信誉等级
  REPUTATION_LEVELS: {
    0: { name: '新手裁判', minScore: 0, maxScore: 49, color: '#9CA3AF', feeDiscount: 0 },
    1: { name: '见习裁判', minScore: 50, maxScore: 149, color: '#10B981', feeDiscount: 0.02 },
    2: { name: '资深裁判', minScore: 150, maxScore: 299, color: '#3B82F6', feeDiscount: 0.05 },
    3: { name: '专家裁判', minScore: 300, maxScore: 499, color: '#8B5CF6', feeDiscount: 0.08 },
    4: { name: '大师裁判', minScore: 500, maxScore: 999, color: '#F59E0B', feeDiscount: 0.12 },
    5: { name: '传奇裁判', minScore: 1000, maxScore: Infinity, color: '#EF4444', feeDiscount: 0.15 }
  },
  
  // 特殊权限
  SPECIAL_PERMISSIONS: {
    2: ['advanced_judgment_tools'],
    3: ['expert_judgment_priority', 'judgment_mentoring'],
    4: ['master_judgment_review', 'special_case_handling'],
    5: ['legendary_judgment_authority', 'system_governance']
  },
  
  // 衰减机制
  DECAY: {
    ENABLED: true,
    RATE: 0.01,              // 每月衰减1%
    MIN_SCORE: 0,            // 最低积分
    INACTIVE_THRESHOLD: 30   // 30天无活动开始衰减
  }
};

// 信誉等级接口
export interface ReputationLevel {
  level: number;
  name: string;
  minScore: number;
  maxScore: number;
  color: string;
  feeDiscount: number;
  permissions: string[];
}

// 用户信誉状态接口
export interface UserReputation {
  userId: string;
  currentScore: number;
  currentLevel: number;
  levelInfo: ReputationLevel;
  totalJudgments: number;
  correctJudgments: number;
  accuracyRate: number;
  currentStreak: number;
  bestStreak: number;
  lastJudgmentDate: string | null;
  nextLevelInfo?: ReputationLevel;
  scoreToNextLevel?: number;
  recentActivity: ReputationActivity[];
}

// 信誉活动记录接口
export interface ReputationActivity {
  id: string;
  type: 'correct_judgment' | 'incorrect_judgment' | 'consensus_bonus' | 'streak_bonus' | 'decay';
  scoreChange: number;
  description: string;
  betId?: string;
  createdAt: string;
}

export class ReputationService {
  /**
   * 获取用户信誉状态
   */
  static async getUserReputation(userId: string): Promise<UserReputation> {
    // 获取用户信誉记录
    const { data: reputation } = await supabase
      .from('user_reputation')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (!reputation) {
      throw new Error('用户信誉记录不存在');
    }

    const currentLevel = this.calculateReputationLevel(reputation.reputation_score);
    const levelInfo = this.getLevelInfo(currentLevel);
    const nextLevelInfo = currentLevel < 5 ? this.getLevelInfo(currentLevel + 1) : undefined;
    const scoreToNextLevel = nextLevelInfo ? nextLevelInfo.minScore - reputation.reputation_score : undefined;

    // 获取最近活动记录
    const recentActivity = await this.getRecentActivity(userId);

    return {
      userId,
      currentScore: reputation.reputation_score,
      currentLevel,
      levelInfo,
      totalJudgments: reputation.total_judgments,
      correctJudgments: reputation.correct_judgments,
      accuracyRate: reputation.accuracy_rate,
      currentStreak: reputation.current_streak,
      bestStreak: reputation.best_streak,
      lastJudgmentDate: reputation.last_judgment_date,
      nextLevelInfo,
      scoreToNextLevel,
      recentActivity
    };
  }

  /**
   * 更新裁定结果的信誉积分
   */
  static async updateJudgmentReputation(
    judgeId: string,
    betId: string,
    isCorrect: boolean,
    isConsensus: boolean = false
  ): Promise<number> {
    const { data: reputation } = await supabase
      .from('user_reputation')
      .select('*')
      .eq('user_id', judgeId)
      .single();

    if (!reputation) {
      throw new Error('用户信誉记录不存在');
    }

    let scoreChange = 0;
    const activities: ReputationActivity[] = [];

    // 基础积分
    if (isCorrect) {
      scoreChange += REPUTATION_CONFIG.SCORING.CORRECT_JUDGMENT;
      activities.push({
        id: '',
        type: 'correct_judgment',
        scoreChange: REPUTATION_CONFIG.SCORING.CORRECT_JUDGMENT,
        description: '正确裁定',
        betId,
        createdAt: new Date().toISOString()
      });
    } else {
      scoreChange += REPUTATION_CONFIG.SCORING.INCORRECT_JUDGMENT;
      activities.push({
        id: '',
        type: 'incorrect_judgment',
        scoreChange: REPUTATION_CONFIG.SCORING.INCORRECT_JUDGMENT,
        description: '错误裁定',
        betId,
        createdAt: new Date().toISOString()
      });
    }

    // 共识奖励
    if (isCorrect && isConsensus) {
      scoreChange += REPUTATION_CONFIG.SCORING.CONSENSUS_BONUS;
      activities.push({
        id: '',
        type: 'consensus_bonus',
        scoreChange: REPUTATION_CONFIG.SCORING.CONSENSUS_BONUS,
        description: '共识奖励',
        betId,
        createdAt: new Date().toISOString()
      });
    }

    // 计算新的连胜和积分
    let newCurrentStreak = reputation.current_streak;
    let newBestStreak = reputation.best_streak;
    
    if (isCorrect) {
      newCurrentStreak++;
      newBestStreak = Math.max(newBestStreak, newCurrentStreak);
      
      // 连胜奖励（每5连胜）
      if (newCurrentStreak % 5 === 0) {
        const streakBonus = REPUTATION_CONFIG.SCORING.STREAK_BONUS;
        scoreChange += streakBonus;
        activities.push({
          id: '',
          type: 'streak_bonus',
          scoreChange: streakBonus,
          description: `${newCurrentStreak}连胜奖励`,
          betId,
          createdAt: new Date().toISOString()
        });
      }
    } else {
      newCurrentStreak = 0;
    }

    // 计算新的统计数据
    const newTotalJudgments = reputation.total_judgments + 1;
    const newCorrectJudgments = isCorrect ? reputation.correct_judgments + 1 : reputation.correct_judgments;
    const newAccuracyRate = newCorrectJudgments / newTotalJudgments;
    const newReputationScore = Math.max(0, reputation.reputation_score + scoreChange);

    // 更新数据库
    const { error } = await supabase
      .from('user_reputation')
      .update({
        reputation_score: newReputationScore,
        total_judgments: newTotalJudgments,
        correct_judgments: newCorrectJudgments,
        accuracy_rate: newAccuracyRate,
        current_streak: newCurrentStreak,
        best_streak: newBestStreak,
        last_judgment_date: new Date().toISOString().split('T')[0],
        updated_at: new Date().toISOString()
      })
      .eq('user_id', judgeId);

    if (error) {
      throw new Error(`更新信誉积分失败: ${error.message}`);
    }

    // 记录积分变化历史
    await this.recordReputationActivity(judgeId, activities);

    // 检查等级变化
    const oldLevel = this.calculateReputationLevel(reputation.reputation_score);
    const newLevel = this.calculateReputationLevel(newReputationScore);
    
    if (newLevel > oldLevel) {
      await this.handleLevelUp(judgeId, oldLevel, newLevel);
    }

    return scoreChange;
  }

  /**
   * 计算信誉等级
   */
  static calculateReputationLevel(score: number): number {
    for (let level = 5; level >= 0; level--) {
      const config = REPUTATION_CONFIG.REPUTATION_LEVELS[level as keyof typeof REPUTATION_CONFIG.REPUTATION_LEVELS];
      if (score >= config.minScore) {
        return level;
      }
    }
    return 0;
  }

  /**
   * 获取等级信息
   */
  static getLevelInfo(level: number): ReputationLevel {
    const config = REPUTATION_CONFIG.REPUTATION_LEVELS[level as keyof typeof REPUTATION_CONFIG.REPUTATION_LEVELS];
    const permissions = REPUTATION_CONFIG.SPECIAL_PERMISSIONS[level as keyof typeof REPUTATION_CONFIG.SPECIAL_PERMISSIONS] || [];
    
    return {
      level,
      name: config.name,
      minScore: config.minScore,
      maxScore: config.maxScore,
      color: config.color,
      feeDiscount: config.feeDiscount,
      permissions
    };
  }

  /**
   * 处理等级提升
   */
  private static async handleLevelUp(userId: string, oldLevel: number, newLevel: number): Promise<void> {
    const newLevelInfo = this.getLevelInfo(newLevel);
    
    // 发放等级提升奖励
    const levelUpReward = (newLevel - oldLevel) * 100; // 每级100福气
    await FortuneService.addFortune(
      userId,
      levelUpReward,
      'reputation_level_up',
      '',
      `信誉等级提升至${newLevelInfo.name} +${levelUpReward}福气`
    );

    console.log(`User ${userId} leveled up from ${oldLevel} to ${newLevel}`);
  }

  /**
   * 记录信誉活动
   */
  private static async recordReputationActivity(
    userId: string,
    activities: ReputationActivity[]
  ): Promise<void> {
    // 这里应该记录到专门的信誉活动表
    // 目前简化处理，记录到fortune_transactions表的描述中
    for (const activity of activities) {
      console.log(`Reputation activity for ${userId}: ${activity.description} (${activity.scoreChange})`);
    }
  }

  /**
   * 获取最近活动记录
   */
  private static async getRecentActivity(userId: string): Promise<ReputationActivity[]> {
    // 这里应该从专门的信誉活动表获取
    // 目前返回模拟数据
    return [
      {
        id: '1',
        type: 'correct_judgment',
        scoreChange: 1,
        description: '正确裁定',
        betId: 'bet-123',
        createdAt: new Date().toISOString()
      }
    ];
  }

  /**
   * 执行信誉积分衰减
   */
  static async processReputationDecay(): Promise<void> {
    if (!REPUTATION_CONFIG.DECAY.ENABLED) return;

    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - REPUTATION_CONFIG.DECAY.INACTIVE_THRESHOLD);

    // 获取需要衰减的用户
    const { data: inactiveUsers } = await supabase
      .from('user_reputation')
      .select('user_id, reputation_score')
      .lt('last_judgment_date', cutoffDate.toISOString().split('T')[0])
      .gt('reputation_score', REPUTATION_CONFIG.DECAY.MIN_SCORE);

    if (!inactiveUsers || inactiveUsers.length === 0) return;

    // 批量处理衰减
    for (const user of inactiveUsers) {
      const decayAmount = Math.floor(user.reputation_score * REPUTATION_CONFIG.DECAY.RATE);
      const newScore = Math.max(REPUTATION_CONFIG.DECAY.MIN_SCORE, user.reputation_score - decayAmount);

      if (newScore !== user.reputation_score) {
        await supabase
          .from('user_reputation')
          .update({
            reputation_score: newScore,
            updated_at: new Date().toISOString()
          })
          .eq('user_id', user.user_id);

        console.log(`Applied decay to user ${user.user_id}: ${user.reputation_score} -> ${newScore}`);
      }
    }
  }

  /**
   * 获取信誉排行榜
   */
  static async getReputationLeaderboard(limit: number = 50): Promise<any[]> {
    const { data: leaderboard } = await supabase
      .from('user_reputation')
      .select(`
        user_id,
        reputation_score,
        total_judgments,
        correct_judgments,
        accuracy_rate,
        current_streak,
        users (
          username,
          email
        )
      `)
      .order('reputation_score', { ascending: false })
      .limit(limit);

    return leaderboard?.map((entry, index) => ({
      rank: index + 1,
      userId: entry.user_id,
      username: entry.users?.username || 'Unknown',
      reputationScore: entry.reputation_score,
      level: this.calculateReputationLevel(entry.reputation_score),
      levelInfo: this.getLevelInfo(this.calculateReputationLevel(entry.reputation_score)),
      totalJudgments: entry.total_judgments,
      accuracyRate: entry.accuracy_rate,
      currentStreak: entry.current_streak
    })) || [];
  }

  /**
   * 获取信誉统计
   */
  static async getReputationStats(): Promise<any> {
    const { data: stats } = await supabase
      .from('user_reputation')
      .select('reputation_score, total_judgments, accuracy_rate');

    if (!stats) return null;

    const levelDistribution = stats.reduce((dist, user) => {
      const level = this.calculateReputationLevel(user.reputation_score);
      dist[level] = (dist[level] || 0) + 1;
      return dist;
    }, {} as Record<number, number>);

    const totalUsers = stats.length;
    const averageScore = stats.reduce((sum, user) => sum + user.reputation_score, 0) / totalUsers;
    const averageAccuracy = stats.reduce((sum, user) => sum + user.accuracy_rate, 0) / totalUsers;

    return {
      totalUsers,
      averageScore: Math.round(averageScore * 100) / 100,
      averageAccuracy: Math.round(averageAccuracy * 10000) / 100, // 转换为百分比
      levelDistribution: Object.keys(REPUTATION_CONFIG.REPUTATION_LEVELS).map(level => {
        const levelNum = parseInt(level);
        const count = levelDistribution[levelNum] || 0;
        const percentage = totalUsers > 0 ? (count / totalUsers) * 100 : 0;
        
        return {
          level: levelNum,
          name: REPUTATION_CONFIG.REPUTATION_LEVELS[levelNum as keyof typeof REPUTATION_CONFIG.REPUTATION_LEVELS].name,
          count,
          percentage: Math.round(percentage * 100) / 100
        };
      })
    };
  }
}
