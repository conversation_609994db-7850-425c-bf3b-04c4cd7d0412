/**
 * Zustand 全局状态管理
 * 统一管理应用状态
 */

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

// 用户状态接口
export interface StoreUser {
  id: string;
  walletAddress: string;
  username?: string;
  email?: string;
  avatar?: string;
  isVerified: boolean;
  isMerchant: boolean;
  level: number;
  totalEarned: number;
  tasksCompleted: number;
  createdAt: string;
}

// 钱包状态接口
export interface WalletState {
  isConnected: boolean;
  address?: string;
  balance: {
    bnb: string;
    haox: string;
  };
  isLoading: boolean;
  error?: string;
}

// 任务状态接口
export interface Task {
  id: string;
  title: string;
  description: string;
  taskType: string;
  rewardAmount: number;
  maxCompletions: number;
  currentCompletions: number;
  endTime?: string;
  status: 'active' | 'completed' | 'expired';
  userHasCompleted: boolean;
  createdAt: string;
}

// 通知状态接口
export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  autoClose?: boolean;
}

// 应用设置接口
export interface AppSettings {
  theme: 'light' | 'dark' | 'system';
  language: 'zh' | 'en';
  notifications: {
    email: boolean;
    push: boolean;
    telegram: boolean;
  };
  privacy: {
    showBalance: boolean;
    showActivity: boolean;
  };
}

// 主状态接口
export interface AppState {
  // 用户状态
  user: StoreUser | null;
  isAuthenticated: boolean;
  
  // 钱包状态
  wallet: WalletState;
  
  // 任务状态
  tasks: Task[];
  taskStats: {
    total: number;
    completed: number;
    pending: number;
    totalRewards: number;
  };
  
  // 通知状态
  notifications: Notification[];
  unreadCount: number;
  
  // UI 状态
  ui: {
    sidebarOpen: boolean;
    loading: boolean;
    error?: string;
    modal: {
      isOpen: boolean;
      type?: string;
      data?: any;
    };
  };
  
  // 应用设置
  settings: AppSettings;
  
  // 缓存数据
  cache: {
    priceData?: any;
    lastUpdated?: string;
  };
}

// 状态操作接口
export interface AppActions {
  // 用户操作
  setUser: (user: StoreUser | null) => void;
  updateUser: (updates: Partial<StoreUser>) => void;
  logout: () => void;
  
  // 钱包操作
  setWalletConnected: (address: string) => void;
  setWalletDisconnected: () => void;
  updateBalance: (balance: { bnb: string; haox: string }) => void;
  setWalletLoading: (loading: boolean) => void;
  setWalletError: (error?: string) => void;
  
  // 任务操作
  setTasks: (tasks: Task[]) => void;
  addTask: (task: Task) => void;
  updateTask: (id: string, updates: Partial<Task>) => void;
  removeTask: (id: string) => void;
  updateTaskStats: (stats: Partial<AppState['taskStats']>) => void;
  
  // 通知操作
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp'>) => void;
  removeNotification: (id: string) => void;
  markNotificationRead: (id: string) => void;
  markAllNotificationsRead: () => void;
  clearNotifications: () => void;
  
  // UI 操作
  setSidebarOpen: (open: boolean) => void;
  setLoading: (loading: boolean) => void;
  setError: (error?: string) => void;
  openModal: (type: string, data?: any) => void;
  closeModal: () => void;
  
  // 设置操作
  updateSettings: (updates: Partial<AppSettings>) => void;
  
  // 缓存操作
  updateCache: (key: string, data: any) => void;
  clearCache: () => void;
  
  // 重置操作
  reset: () => void;
}

// 初始状态
const initialState: AppState = {
  user: null,
  isAuthenticated: false,
  
  wallet: {
    isConnected: false,
    balance: {
      bnb: '0',
      haox: '0',
    },
    isLoading: false,
  },
  
  tasks: [],
  taskStats: {
    total: 0,
    completed: 0,
    pending: 0,
    totalRewards: 0,
  },
  
  notifications: [],
  unreadCount: 0,
  
  ui: {
    sidebarOpen: false,
    loading: false,
    modal: {
      isOpen: false,
    },
  },
  
  settings: {
    theme: 'system',
    language: 'zh',
    notifications: {
      email: true,
      push: true,
      telegram: true,
    },
    privacy: {
      showBalance: true,
      showActivity: true,
    },
  },
  
  cache: {},
};

// 创建状态存储
export const useAppStore = create<AppState & AppActions>()(
  devtools(
    persist(
      immer((set, get) => ({
        ...initialState,
        
        // 用户操作
        setUser: (user) =>
          set((state) => {
            state.user = user;
            state.isAuthenticated = !!user;
          }),
        
        updateUser: (updates) =>
          set((state) => {
            if (state.user) {
              Object.assign(state.user, updates);
            }
          }),
        
        logout: () =>
          set((state) => {
            state.user = null;
            state.isAuthenticated = false;
            state.wallet.isConnected = false;
            state.wallet.address = undefined;
            state.tasks = [];
            state.notifications = [];
          }),
        
        // 钱包操作
        setWalletConnected: (address) =>
          set((state) => {
            state.wallet.isConnected = true;
            state.wallet.address = address;
            state.wallet.error = undefined;
          }),
        
        setWalletDisconnected: () =>
          set((state) => {
            state.wallet.isConnected = false;
            state.wallet.address = undefined;
            state.wallet.balance = { bnb: '0', haox: '0' };
          }),
        
        updateBalance: (balance) =>
          set((state) => {
            state.wallet.balance = balance;
          }),
        
        setWalletLoading: (loading) =>
          set((state) => {
            state.wallet.isLoading = loading;
          }),
        
        setWalletError: (error) =>
          set((state) => {
            state.wallet.error = error;
          }),
        
        // 任务操作
        setTasks: (tasks) =>
          set((state) => {
            state.tasks = tasks;
          }),
        
        addTask: (task) =>
          set((state) => {
            state.tasks.push(task);
          }),
        
        updateTask: (id, updates) =>
          set((state) => {
            const index = state.tasks.findIndex((task) => task.id === id);
            if (index !== -1) {
              Object.assign(state.tasks[index], updates);
            }
          }),
        
        removeTask: (id) =>
          set((state) => {
            state.tasks = state.tasks.filter((task) => task.id !== id);
          }),
        
        updateTaskStats: (stats) =>
          set((state) => {
            Object.assign(state.taskStats, stats);
          }),
        
        // 通知操作
        addNotification: (notification) =>
          set((state) => {
            const newNotification: Notification = {
              ...notification,
              id: Date.now().toString(),
              timestamp: new Date().toISOString(),
              read: false,
            };
            state.notifications.unshift(newNotification);
            state.unreadCount += 1;
          }),
        
        removeNotification: (id) =>
          set((state) => {
            const notification = state.notifications.find((n) => n.id === id);
            if (notification && !notification.read) {
              state.unreadCount -= 1;
            }
            state.notifications = state.notifications.filter((n) => n.id !== id);
          }),
        
        markNotificationRead: (id) =>
          set((state) => {
            const notification = state.notifications.find((n) => n.id === id);
            if (notification && !notification.read) {
              notification.read = true;
              state.unreadCount -= 1;
            }
          }),
        
        markAllNotificationsRead: () =>
          set((state) => {
            state.notifications.forEach((n) => {
              n.read = true;
            });
            state.unreadCount = 0;
          }),
        
        clearNotifications: () =>
          set((state) => {
            state.notifications = [];
            state.unreadCount = 0;
          }),
        
        // UI 操作
        setSidebarOpen: (open) =>
          set((state) => {
            state.ui.sidebarOpen = open;
          }),
        
        setLoading: (loading) =>
          set((state) => {
            state.ui.loading = loading;
          }),
        
        setError: (error) =>
          set((state) => {
            state.ui.error = error;
          }),
        
        openModal: (type, data) =>
          set((state) => {
            state.ui.modal = {
              isOpen: true,
              type,
              data,
            };
          }),
        
        closeModal: () =>
          set((state) => {
            state.ui.modal = {
              isOpen: false,
            };
          }),
        
        // 设置操作
        updateSettings: (updates) =>
          set((state) => {
            Object.assign(state.settings, updates);
          }),
        
        // 缓存操作
        updateCache: (key, data) =>
          set((state) => {
            state.cache[key] = data;
            state.cache.lastUpdated = new Date().toISOString();
          }),
        
        clearCache: () =>
          set((state) => {
            state.cache = {};
          }),
        
        // 重置操作
        reset: () =>
          set(() => ({
            ...initialState,
          })),
      })),
      {
        name: 'sociomint-store',
        partialize: (state) => ({
          user: state.user,
          isAuthenticated: state.isAuthenticated,
          settings: state.settings,
          // 不持久化敏感数据如钱包余额
        }),
      }
    ),
    {
      name: 'SocioMint Store',
    }
  )
);

// 选择器 hooks
export const useUser = () => useAppStore((state) => state.user);
export const useIsAuthenticated = () => useAppStore((state) => state.isAuthenticated);
export const useWallet = () => useAppStore((state) => state.wallet);
export const useTasks = () => useAppStore((state) => state.tasks);
export const useTaskStats = () => useAppStore((state) => state.taskStats);
export const useNotifications = () => useAppStore((state) => state.notifications);
export const useUnreadCount = () => useAppStore((state) => state.unreadCount);
export const useUI = () => useAppStore((state) => state.ui);
export const useSettings = () => useAppStore((state) => state.settings);
