// 白皮书内容结构类型定义

export interface WhitepaperSection {
  id: string;
  title: string;
  level: number; // 1-4 对应 H1-H4
  content: string;
  subsections?: WhitepaperSection[];
  icon?: string;
  color?: string;
  type?: 'executive' | 'technical' | 'tokenomics' | 'roadmap' | 'team' | 'risk' | 'appendix' | 'default';
}

export interface WhitepaperTOC {
  id: string;
  title: string;
  level: number;
  anchor: string;
  children?: WhitepaperTOC[];
  type?: string;
  icon?: string;
}

export interface WhitepaperStructure {
  title: string;
  subtitle: string;
  sections: WhitepaperSection[];
  toc: WhitepaperTOC[];
  metadata: {
    version: string;
    date: string;
    language: 'zh' | 'en';
    totalSections: number;
    estimatedReadTime: number;
  };
}

// 章节类型配置
export const SECTION_TYPES = {
  executive: {
    color: 'system-blue',
    icon: '🎯',
    bgColor: 'bg-system-blue/10',
    borderColor: 'border-system-blue/20'
  },
  technical: {
    color: 'system-purple',
    icon: '⚙️',
    bgColor: 'bg-system-purple/10',
    borderColor: 'border-system-purple/20'
  },
  tokenomics: {
    color: 'system-green',
    icon: '💰',
    bgColor: 'bg-system-green/10',
    borderColor: 'border-system-green/20'
  },
  roadmap: {
    color: 'system-orange',
    icon: '🚀',
    bgColor: 'bg-system-orange/10',
    borderColor: 'border-system-orange/20'
  },
  team: {
    color: 'system-indigo',
    icon: '👥',
    bgColor: 'bg-system-indigo/10',
    borderColor: 'border-system-indigo/20'
  },
  risk: {
    color: 'system-red',
    icon: '⚠️',
    bgColor: 'bg-system-red/10',
    borderColor: 'border-system-red/20'
  },
  appendix: {
    color: 'system-gray',
    icon: '📋',
    bgColor: 'bg-system-gray/10',
    borderColor: 'border-system-gray/20'
  },
  default: {
    color: 'label',
    icon: '📄',
    bgColor: 'bg-system-gray-6/50',
    borderColor: 'border-system-gray-4/30'
  }
} as const;

// 预定义的章节结构 (总计30分钟阅读时间)
export const WHITEPAPER_STRUCTURE: Record<string, { type: keyof typeof SECTION_TYPES; estimatedReadTime: number }> = {
  '0': { type: 'executive', estimatedReadTime: 3 },
  '1': { type: 'executive', estimatedReadTime: 2 },
  '2': { type: 'default', estimatedReadTime: 3 },
  '3': { type: 'technical', estimatedReadTime: 5 },
  '4': { type: 'tokenomics', estimatedReadTime: 5 },
  '5': { type: 'technical', estimatedReadTime: 4 },
  '6': { type: 'roadmap', estimatedReadTime: 2 },
  '7': { type: 'team', estimatedReadTime: 2 },
  '8': { type: 'default', estimatedReadTime: 2 },
  '9': { type: 'risk', estimatedReadTime: 1 },
  '10': { type: 'default', estimatedReadTime: 1 },
  '11': { type: 'executive', estimatedReadTime: 0 },
  'appendix': { type: 'appendix', estimatedReadTime: 3 }
};

// 语言配置
export const LANGUAGE_CONFIG = {
  zh: {
    toc: '目录',
    readingTime: '预计阅读时间',
    minutes: '分钟',
    backToTop: '返回顶部',
    downloadPdf: '下载PDF',
    tableOfContents: '目录导航',
    progress: '阅读进度',
    section: '章节',
    subsection: '小节',
    executiveSummary: '执行摘要',
    technicalDetails: '技术细节',
    tokenomics: '代币经济学',
    roadmap: '发展路线图',
    teamInfo: '团队信息',
    riskDisclosure: '风险披露',
    appendix: '附录'
  },
  en: {
    toc: 'Table of Contents',
    readingTime: 'Estimated Reading Time',
    minutes: 'minutes',
    backToTop: 'Back to Top',
    downloadPdf: 'Download PDF',
    tableOfContents: 'Table of Contents',
    progress: 'Reading Progress',
    section: 'Section',
    subsection: 'Subsection',
    executiveSummary: 'Executive Summary',
    technicalDetails: 'Technical Details',
    tokenomics: 'Tokenomics',
    roadmap: 'Roadmap',
    teamInfo: 'Team Information',
    riskDisclosure: 'Risk Disclosure',
    appendix: 'Appendix'
  }
} as const;
