/**
 * 用户相关类型定义
 */

export interface UserProfile {
  id: string;
  address: string;
  nickname?: string;
  avatar?: string;
  email?: string;
  bio?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserAssets {
  haoxBalance: {
    available: string; // 可用余额
    locked: string;    // 锁定余额
    pending: string;   // 待领取奖励
  };
  usdValue: string;    // USD估值
  cnyValue: string;    // CNY估值
}

export interface SocialAccounts {
  twitter?: {
    id: string;
    username: string;
    verified: boolean;
    connectedAt: Date;
  };
  telegram?: {
    id: string;
    username: string;
    verified: boolean;
    connectedAt: Date;
  };
  discord?: {
    id: string;
    username: string;
    verified: boolean;
    connectedAt: Date;
  };
}

export interface InvitationData {
  inviteCode: string;
  invitedCount: number;
  totalRewards: string;
  pendingRewards: string;
  invitees: Array<{
    address: string;
    nickname?: string;
    joinedAt: Date;
    reward: string;
    status: 'pending' | 'completed';
  }>;
}

export interface Transaction {
  id: string;
  type: 'deposit' | 'withdraw' | 'transfer' | 'reward' | 'exchange';
  amount: string;
  token: string;
  from?: string;
  to?: string;
  txHash?: string;
  status: 'pending' | 'confirmed' | 'failed';
  timestamp: Date;
  gasUsed?: string;
  gasFee?: string;
}

export interface Task {
  id: string;
  title: string;
  description: string;
  type: 'social' | 'trading' | 'referral' | 'daily';
  reward: string;
  status: 'available' | 'in_progress' | 'completed' | 'claimed';
  progress?: {
    current: number;
    target: number;
  };
  deadline?: Date;
  requirements?: string[];
}

export interface UserStats {
  totalInvited: number;
  totalRewards: string;
  completedTasks: number;
  tradingVolume: string;
  joinedAt: Date;
  lastActiveAt: Date;
}

export interface UserSettings {
  language: 'zh' | 'en';
  theme: 'light' | 'dark' | 'auto';
  currency: 'USD' | 'CNY';
  notifications: {
    email: boolean;
    push: boolean;
    trading: boolean;
    rewards: boolean;
    social: boolean;
  };
  security: {
    twoFactorEnabled: boolean;
    transactionPassword: boolean;
    loginNotifications: boolean;
  };
}

export interface AuthState {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: UserProfile | null;
  assets: UserAssets | null;
  socialAccounts: SocialAccounts | null;
  error: string | null;
}

// API 响应类型
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// 用户操作类型
export type UserAction = 
  | { type: 'AUTH_START' }
  | { type: 'AUTH_SUCCESS'; payload: { user: UserProfile; assets: UserAssets } }
  | { type: 'AUTH_FAILURE'; payload: string }
  | { type: 'LOGOUT' }
  | { type: 'UPDATE_PROFILE'; payload: Partial<UserProfile> }
  | { type: 'UPDATE_ASSETS'; payload: UserAssets }
  | { type: 'UPDATE_SOCIAL_ACCOUNTS'; payload: SocialAccounts }
  | { type: 'CLEAR_ERROR' };
