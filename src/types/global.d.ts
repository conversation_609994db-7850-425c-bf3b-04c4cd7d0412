/**
 * 全局类型定义
 * 定义环境变量、全局接口和类型扩展
 */

declare global {
  namespace NodeJS {
    interface ProcessEnv {
      // 基础配置
      NODE_ENV: 'development' | 'production' | 'test';
      NEXT_PUBLIC_APP_URL: string;
      
      // Supabase 配置
      NEXT_PUBLIC_SUPABASE_URL: string;
      NEXT_PUBLIC_SUPABASE_ANON_KEY: string;
      SUPABASE_SERVICE_ROLE_KEY: string;
      
      // BSC 区块链配置
      NEXT_PUBLIC_CHAIN_ID: string;
      NEXT_PUBLIC_RPC_URL: string;
      NEXT_PUBLIC_HAOX_TOKEN_ADDRESS: string;
      NEXT_PUBLIC_HAOX_PRESALE_ADDRESS: string;
      NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID?: string;
      NEXT_PUBLIC_BLOCK_EXPLORER?: string;
      
      // Telegram 配置
      TELEGRAM_BOT_TOKEN: string;
      TELEGRAM_BOT_USERNAME: string;
      NEXT_PUBLIC_TELEGRAM_BOT_USERNAME: string;
      TELEGRAM_WEBHOOK_SECRET?: string;
      
      // 安全配置
      JWT_SECRET: string;
      NEXTAUTH_SECRET: string;
      
      // 可选配置
      SENTRY_DSN?: string;
      NEXT_PUBLIC_GA_ID?: string;
      LOG_ENDPOINT?: string;
    }
  }

  // 扩展 Window 接口
  interface Window {
    // Telegram Web App
    Telegram?: {
      WebApp?: {
        ready(): void;
        close(): void;
        expand(): void;
        MainButton: {
          text: string;
          color: string;
          textColor: string;
          isVisible: boolean;
          isActive: boolean;
          setText(text: string): void;
          onClick(callback: () => void): void;
          show(): void;
          hide(): void;
        };
        initData: string;
        initDataUnsafe: {
          user?: {
            id: number;
            first_name: string;
            last_name?: string;
            username?: string;
            language_code?: string;
            photo_url?: string;
          };
          auth_date: number;
          hash: string;
        };
      };
    };

    // Web3 钱包 - 使用更安全的类型定义
    ethereum?: {
      isMetaMask?: boolean;
      request: (args: { method: string; params?: any[] }) => Promise<any>;
      on: (event: string, callback: (...args: any[]) => void) => void;
      removeListener: (event: string, callback: (...args: any[]) => void) => void;
      [key: string]: any; // 允许动态属性
    };

    // Solana 钱包 - 添加类型定义
    solana?: {
      isPhantom?: boolean;
      connect: () => Promise<{ publicKey: any }>;
      disconnect: () => Promise<void>;
      signTransaction: (transaction: any) => Promise<any>;
      [key: string]: any; // 允许动态属性
    };

    // 分析工具
    gtag?: (...args: any[]) => void;
    dataLayer?: any[];
  }
}

// 区块链相关类型
export interface ChainConfig {
  chainId: number;
  name: string;
  rpcUrl: string;
  blockExplorer: string;
  nativeCurrency: {
    name: string;
    symbol: string;
    decimals: number;
  };
}

export interface TokenConfig {
  address: string;
  symbol: string;
  decimals: number;
  name: string;
}

// API 响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
  requestId?: string;
}

export interface PaginatedResponse<T = any> extends ApiResponse<{
  items: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}> {}

// 用户相关类型
export interface User {
  id: string;
  telegramId: number;
  username?: string;
  firstName: string;
  lastName?: string;
  email?: string;
  photoUrl?: string;
  walletAddress?: string;
  isMerchant: boolean;
  merchantStatus: 'none' | 'pending' | 'approved' | 'rejected';
  createdAt: string;
  updatedAt: string;
}

// 钱包相关类型
export interface WalletBalance {
  haoxBalance: {
    available: string;
    locked: string;
    total: string;
  };
  bnbBalance: {
    available: string;
    locked: string;
    total: string;
  };
  usdValue: {
    haox: number;
    bnb: number;
    total: number;
  };
  lastUpdated: string;
}

export interface Transaction {
  id: string;
  userId: string;
  type: 'deposit' | 'withdraw' | 'transfer' | 'trade';
  amount: string;
  currency: string;
  status: 'pending' | 'completed' | 'failed';
  hash?: string;
  createdAt: string;
  completedAt?: string;
}

// 任务相关类型
export interface SocialTask {
  id: string;
  title: string;
  description: string;
  type: 'follow' | 'like' | 'share' | 'comment' | 'join';
  platform: 'telegram' | 'twitter' | 'discord';
  reward: string;
  requirements: Record<string, any>;
  isActive: boolean;
  createdAt: string;
}

export interface TaskCompletion {
  id: string;
  userId: string;
  taskId: string;
  status: 'pending' | 'completed' | 'failed';
  proof?: string;
  rewardClaimed: boolean;
  completedAt?: string;
}

// 邀请相关类型
export interface InvitationStats {
  totalInvitations: number;
  successfulInvitations: number;
  pendingInvitations: number;
  totalRewards: string;
  pendingRewards: string;
  claimedRewards: string;
  invitationLevel: number;
  levelProgress: number;
}

export interface InvitationDetail {
  id: string;
  inviterId: string;
  inviteeId?: string;
  invitationCode: string;
  status: 'pending' | 'accepted' | 'expired' | 'cancelled';
  rewardAmount: string;
  rewardStatus: 'pending' | 'processing' | 'completed' | 'failed';
  invitedAt: string;
  acceptedAt?: string;
}

// 活动相关类型
export interface UserActivity {
  id: string;
  userId: string;
  activityType: string;
  activityCategory: 'auth' | 'wallet' | 'task' | 'social' | 'trading';
  activityDescription?: string;
  targetType?: string;
  targetId?: string;
  status: 'completed' | 'failed' | 'pending' | 'cancelled';
  rewardAmount?: string;
  rewardCurrency?: string;
  metadata?: Record<string, any>;
  createdAt: string;
}

// 预售相关类型
export interface PresaleInfo {
  isActive: boolean;
  currentPhase: number;
  totalPhases: number;
  currentPrice: string;
  nextPrice?: string;
  tokensRemaining: string;
  totalRaised: string;
  targetAmount: string;
  startTime: string;
  endTime: string;
}

// 价格数据类型
export interface PriceData {
  symbol: string;
  price: string;
  change24h: string;
  volume24h: string;
  high24h: string;
  low24h: string;
  lastUpdated: string;
}

// 错误类型
export interface AppError extends Error {
  code?: string;
  statusCode?: number;
  details?: any;
}

// 表单类型
export interface FormState<T = any> {
  data: T;
  errors: Record<string, string>;
  isSubmitting: boolean;
  isValid: boolean;
}

// 导出空对象以确保这是一个模块
export {};
