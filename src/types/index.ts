// User Types
export interface User {
  id: string;
  email: string;
  username: string;
  avatar_url?: string;
  wallet_address?: string;

  created_at: string;
  updated_at: string;
}

// Social Platform Types
export interface SocialAccount {
  id: string;
  user_id: string;
  platform: 'twitter' | 'discord' | 'telegram';
  platform_user_id: string;
  platform_username: string;
  is_verified: boolean;
  connected_at: string;
}

// Token Types
export interface HAOXToken {
  id: string;
  name: string;
  symbol: string;
  total_supply: string;
  current_price: number;
  price_change_24h: number;
  market_cap: number;
  volume_24h: number;
  contract_address: string;
}



// Social Task Types
export interface SocialTask {
  id: string;
  title: string;
  description: string;
  platform: 'twitter' | 'discord' | 'telegram';
  task_type: 'follow' | 'like' | 'retweet' | 'join' | 'share';
  reward_amount: number;
  max_completions: number;
  current_completions: number;
  is_active: boolean;
  expires_at?: string;
  created_at: string;
}

export interface UserTaskCompletion {
  id: string;
  user_id: string;
  task_id: string;
  completed_at: string;
  reward_claimed: boolean;
  verification_data?: any;
}

// Wallet Types
export interface WalletConnection {
  address: string;
  connector: string;
  isConnected: boolean;
  isConnecting: boolean;
  isReconnecting: boolean;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Component Props Types
export interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  children?: React.ReactNode;
  onClick?: () => void;
  className?: string;
  icon?: React.ReactNode;
  fullWidth?: boolean;
  'aria-label'?: string;
  type?: 'button' | 'submit' | 'reset';
  id?: string;
  href?: string;
  target?: '_blank' | '_self' | '_parent' | '_top';
}

export interface InputProps {
  label?: string;
  placeholder?: string;
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url';
  value: string;
  onChange: (value: string) => void;
  error?: string;
  disabled?: boolean;
  required?: boolean;
  className?: string;
  icon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  helperText?: string;
  maxLength?: number;
  minLength?: number;
  id?: string;
  name?: string;
  autoComplete?: string;
}

export interface CardProps {
  title?: string;
  children: React.ReactNode;
  className?: string;
  padding?: 'sm' | 'md' | 'lg';
}

// Navigation Types
export interface NavItem {
  label: string;
  href: string;
  icon?: React.ComponentType<any>;
  badge?: string | number;
  children?: NavItem[];
}

// Form Types
export interface LoginForm {
  email: string;
  password: string;
}

export interface RegisterForm {
  email: string;
  password: string;
  confirmPassword: string;
  username: string;
}


