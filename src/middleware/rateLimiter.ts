/**
 * API频率限制中间件
 * 防止恶意请求和API滥用
 */

import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

// 频率限制配置
export const RATE_LIMIT_CONFIG = {
  // 通用API限制
  GENERAL: {
    windowMs: 15 * 60 * 1000,    // 15分钟窗口
    maxRequests: 100,            // 最大请求数
    message: '请求过于频繁，请稍后再试'
  },
  
  // 敏感操作限制
  SENSITIVE: {
    windowMs: 60 * 60 * 1000,    // 1小时窗口
    maxRequests: 10,             // 最大请求数
    message: '敏感操作过于频繁，请稍后再试'
  },
  
  // 投注操作限制
  BETTING: {
    windowMs: 60 * 1000,         // 1分钟窗口
    maxRequests: 5,              // 最大请求数
    message: '投注操作过于频繁，请稍后再试'
  },
  
  // 裁定操作限制
  JUDGMENT: {
    windowMs: 5 * 60 * 1000,     // 5分钟窗口
    maxRequests: 3,              // 最大请求数
    message: '裁定操作过于频繁，请稍后再试'
  }
};

// 请求记录接口
interface RequestRecord {
  ip: string;
  userId?: string;
  endpoint: string;
  timestamp: number;
  count: number;
}

// 内存存储（生产环境应使用Redis）
const requestStore = new Map<string, RequestRecord[]>();

/**
 * 获取客户端IP地址
 */
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const remoteAddr = request.headers.get('x-vercel-forwarded-for');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  return realIP || remoteAddr || 'unknown';
}

/**
 * 获取用户ID（如果已认证）
 */
async function getUserId(request: NextRequest): Promise<string | null> {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }
    
    const token = authHeader.substring(7);
    const { data: { user }, error } = await supabase.auth.getUser(token);
    
    return error ? null : user?.id || null;
  } catch {
    return null;
  }
}

/**
 * 生成限制键
 */
function generateLimitKey(ip: string, userId: string | null, endpoint: string): string {
  const identifier = userId || ip;
  return `${identifier}:${endpoint}`;
}

/**
 * 清理过期记录
 */
function cleanupExpiredRecords(windowMs: number): void {
  const now = Date.now();
  const cutoff = now - windowMs;
  
  for (const [key, records] of requestStore.entries()) {
    const validRecords = records.filter(record => record.timestamp > cutoff);
    
    if (validRecords.length === 0) {
      requestStore.delete(key);
    } else {
      requestStore.set(key, validRecords);
    }
  }
}

/**
 * 检查频率限制
 */
function checkRateLimit(
  key: string,
  config: typeof RATE_LIMIT_CONFIG.GENERAL,
  ip: string,
  userId: string | null,
  endpoint: string
): { allowed: boolean; remaining: number; resetTime: number } {
  const now = Date.now();
  const windowStart = now - config.windowMs;
  
  // 清理过期记录
  cleanupExpiredRecords(config.windowMs);
  
  // 获取当前记录
  const records = requestStore.get(key) || [];
  const validRecords = records.filter(record => record.timestamp > windowStart);
  
  // 计算当前请求数
  const currentCount = validRecords.reduce((sum, record) => sum + record.count, 0);
  
  // 检查是否超限
  const allowed = currentCount < config.maxRequests;
  const remaining = Math.max(0, config.maxRequests - currentCount - 1);
  const resetTime = now + config.windowMs;
  
  if (allowed) {
    // 添加新记录
    const newRecord: RequestRecord = {
      ip,
      userId,
      endpoint,
      timestamp: now,
      count: 1
    };
    
    validRecords.push(newRecord);
    requestStore.set(key, validRecords);
  }
  
  return { allowed, remaining, resetTime };
}

/**
 * 获取端点配置
 */
function getEndpointConfig(pathname: string): typeof RATE_LIMIT_CONFIG.GENERAL {
  // 敏感操作
  if (pathname.includes('/settlement') || 
      pathname.includes('/confirm') ||
      pathname.includes('/certification/update')) {
    return RATE_LIMIT_CONFIG.SENSITIVE;
  }
  
  // 投注操作
  if (pathname.includes('/participate') || 
      pathname.includes('/create')) {
    return RATE_LIMIT_CONFIG.BETTING;
  }
  
  // 裁定操作
  if (pathname.includes('/judgment') && 
      !pathname.includes('/history')) {
    return RATE_LIMIT_CONFIG.JUDGMENT;
  }
  
  // 默认限制
  return RATE_LIMIT_CONFIG.GENERAL;
}

/**
 * 频率限制中间件
 */
export async function rateLimitMiddleware(
  request: NextRequest,
  endpoint?: string
): Promise<NextResponse | null> {
  try {
    const ip = getClientIP(request);
    const userId = await getUserId(request);
    const pathname = endpoint || request.nextUrl.pathname;
    
    // 获取配置
    const config = getEndpointConfig(pathname);
    
    // 生成限制键
    const key = generateLimitKey(ip, userId, pathname);
    
    // 检查频率限制
    const { allowed, remaining, resetTime } = checkRateLimit(
      key, config, ip, userId, pathname
    );
    
    // 创建响应头
    const headers = new Headers();
    headers.set('X-RateLimit-Limit', config.maxRequests.toString());
    headers.set('X-RateLimit-Remaining', remaining.toString());
    headers.set('X-RateLimit-Reset', Math.ceil(resetTime / 1000).toString());
    headers.set('X-RateLimit-Window', (config.windowMs / 1000).toString());
    
    if (!allowed) {
      // 记录限制事件
      console.warn(`Rate limit exceeded for ${userId || ip} on ${pathname}`);
      
      // 返回429错误
      return new NextResponse(
        JSON.stringify({
          error: config.message,
          code: 'RATE_LIMIT_EXCEEDED',
          retryAfter: Math.ceil(config.windowMs / 1000)
        }),
        {
          status: 429,
          headers: {
            'Content-Type': 'application/json',
            ...Object.fromEntries(headers.entries())
          }
        }
      );
    }
    
    // 允许请求继续，但添加限制头
    return null; // 表示继续处理请求
  } catch (error) {
    console.error('Rate limiter error:', error);
    // 出错时允许请求继续，避免影响正常功能
    return null;
  }
}

/**
 * 创建特定端点的限制器
 */
export function createRateLimiter(config: typeof RATE_LIMIT_CONFIG.GENERAL) {
  return async (request: NextRequest): Promise<NextResponse | null> => {
    const ip = getClientIP(request);
    const userId = await getUserId(request);
    const pathname = request.nextUrl.pathname;
    const key = generateLimitKey(ip, userId, pathname);
    
    const { allowed, remaining, resetTime } = checkRateLimit(
      key, config, ip, userId, pathname
    );
    
    if (!allowed) {
      return new NextResponse(
        JSON.stringify({
          error: config.message,
          code: 'RATE_LIMIT_EXCEEDED'
        }),
        {
          status: 429,
          headers: {
            'Content-Type': 'application/json',
            'X-RateLimit-Limit': config.maxRequests.toString(),
            'X-RateLimit-Remaining': '0',
            'X-RateLimit-Reset': Math.ceil(resetTime / 1000).toString()
          }
        }
      );
    }
    
    return null;
  };
}

/**
 * 获取用户当前限制状态
 */
export async function getUserRateLimitStatus(
  request: NextRequest,
  endpoint: string
): Promise<{
  limit: number;
  remaining: number;
  resetTime: number;
  blocked: boolean;
}> {
  const ip = getClientIP(request);
  const userId = await getUserId(request);
  const config = getEndpointConfig(endpoint);
  const key = generateLimitKey(ip, userId, endpoint);
  
  const now = Date.now();
  const windowStart = now - config.windowMs;
  const records = requestStore.get(key) || [];
  const validRecords = records.filter(record => record.timestamp > windowStart);
  const currentCount = validRecords.reduce((sum, record) => sum + record.count, 0);
  
  return {
    limit: config.maxRequests,
    remaining: Math.max(0, config.maxRequests - currentCount),
    resetTime: now + config.windowMs,
    blocked: currentCount >= config.maxRequests
  };
}

/**
 * 重置用户限制（管理员功能）
 */
export function resetUserRateLimit(
  ip: string,
  userId: string | null,
  endpoint?: string
): boolean {
  try {
    if (endpoint) {
      const key = generateLimitKey(ip, userId, endpoint);
      requestStore.delete(key);
    } else {
      // 清除该用户/IP的所有限制
      const identifier = userId || ip;
      for (const key of requestStore.keys()) {
        if (key.startsWith(identifier + ':')) {
          requestStore.delete(key);
        }
      }
    }
    return true;
  } catch {
    return false;
  }
}

/**
 * 获取系统限制统计
 */
export function getRateLimitStats(): {
  totalKeys: number;
  totalRequests: number;
  blockedIPs: string[];
  topEndpoints: Array<{ endpoint: string; requests: number }>;
} {
  const stats = {
    totalKeys: requestStore.size,
    totalRequests: 0,
    blockedIPs: [] as string[],
    topEndpoints: new Map<string, number>()
  };
  
  const now = Date.now();
  
  for (const [key, records] of requestStore.entries()) {
    const recentRecords = records.filter(r => r.timestamp > now - 60 * 60 * 1000); // 1小时内
    const requestCount = recentRecords.reduce((sum, r) => sum + r.count, 0);
    
    stats.totalRequests += requestCount;
    
    // 统计端点使用情况
    for (const record of recentRecords) {
      const current = stats.topEndpoints.get(record.endpoint) || 0;
      stats.topEndpoints.set(record.endpoint, current + record.count);
    }
    
    // 检查是否被阻止
    if (requestCount >= RATE_LIMIT_CONFIG.GENERAL.maxRequests) {
      const [identifier] = key.split(':');
      if (!stats.blockedIPs.includes(identifier)) {
        stats.blockedIPs.push(identifier);
      }
    }
  }
  
  return {
    ...stats,
    topEndpoints: Array.from(stats.topEndpoints.entries())
      .map(([endpoint, requests]) => ({ endpoint, requests }))
      .sort((a, b) => b.requests - a.requests)
      .slice(0, 10)
  };
}
