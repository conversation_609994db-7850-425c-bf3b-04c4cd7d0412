{"extends": ["next/core-web-vitals"], "rules": {"@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-require-imports": "off", "react-hooks/exhaustive-deps": "off", "react-hooks/rules-of-hooks": "off", "@next/next/no-img-element": "off", "jsx-a11y/alt-text": "off", "react/no-unescaped-entities": "off", "import/no-anonymous-default-export": "off"}}