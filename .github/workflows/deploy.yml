name: Deploy to Cloudflare Pages

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '18'

jobs:
  # 代码质量检查
  lint-and-test:
    name: Lint and Test
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run ESLint
      run: npm run lint
      continue-on-error: true
      
    - name: Run TypeScript check
      run: npm run type-check
      continue-on-error: true
      
    - name: Run tests
      run: npm run test
      continue-on-error: true

  # 构建和部署
  deploy:
    name: Build and Deploy
    runs-on: ubuntu-latest
    needs: lint-and-test
    if: github.event_name == 'push'

    strategy:
      matrix:
        environment:
          - name: staging
            branch: develop
            url: https://sociomint-staging.pages.dev
            project: sociomint-staging
          - name: production
            branch: main
            url: https://sociomint.com
            project: sociomint

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Create environment file
      run: |
        echo "NODE_ENV=${{ matrix.environment.name == 'production' && 'production' || 'staging' }}" >> .env.local
        echo "NEXT_PUBLIC_APP_ENV=${{ matrix.environment.name }}" >> .env.local
        echo "NEXT_PUBLIC_APP_URL=${{ matrix.environment.url }}" >> .env.local
        echo "NEXT_PUBLIC_SUPABASE_URL=${{ secrets.NEXT_PUBLIC_SUPABASE_URL }}" >> .env.local
        echo "NEXT_PUBLIC_SUPABASE_ANON_KEY=${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY }}" >> .env.local
        echo "SUPABASE_SERVICE_ROLE_KEY=${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}" >> .env.local
        echo "NEXT_PUBLIC_HAOX_CONTRACT_ADDRESS_BSC=${{ secrets.NEXT_PUBLIC_HAOX_CONTRACT_ADDRESS_BSC }}" >> .env.local
        echo "NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID=${{ secrets.NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID }}" >> .env.local
        echo "NEXT_PUBLIC_ALCHEMY_API_KEY=${{ secrets.NEXT_PUBLIC_ALCHEMY_API_KEY }}" >> .env.local
        echo "TELEGRAM_BOT_TOKEN=${{ secrets.TELEGRAM_BOT_TOKEN }}" >> .env.local

    - name: Build application
      run: |
        if [ "${{ matrix.environment.name }}" = "production" ]; then
          npm run build:production
        else
          npm run build
        fi

    - name: Deploy to Cloudflare Pages
      if: github.ref == format('refs/heads/{0}', matrix.environment.branch)
      uses: cloudflare/pages-action@v1
      with:
        apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
        accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
        projectName: ${{ matrix.environment.project }}
        directory: .next
        gitHubToken: ${{ secrets.GITHUB_TOKEN }}
        wranglerVersion: '3'

    - name: Verify deployment
      if: github.ref == format('refs/heads/{0}', matrix.environment.branch)
      run: |
        echo "Waiting for deployment to be ready..."
        sleep 60

        # 检查网站可访问性
        if curl -f -s "${{ matrix.environment.url }}" > /dev/null; then
          echo "✅ Deployment successful: ${{ matrix.environment.url }}"
        else
          echo "❌ Deployment verification failed"
          exit 1
        fi

  # 部署预览环境
  deploy-preview:
    name: Deploy Preview
    runs-on: ubuntu-latest
    needs: lint-and-test
    if: github.event_name == 'pull_request'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Create environment file
      run: |
        echo "NODE_ENV=staging" >> .env.local
        echo "NEXT_PUBLIC_APP_ENV=preview" >> .env.local
        echo "NEXT_PUBLIC_APP_URL=https://sociomint-preview.pages.dev" >> .env.local
        echo "NEXT_PUBLIC_SUPABASE_URL=${{ secrets.NEXT_PUBLIC_SUPABASE_URL }}" >> .env.local
        echo "NEXT_PUBLIC_SUPABASE_ANON_KEY=${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY }}" >> .env.local
        echo "NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID=${{ secrets.NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID }}" >> .env.local
        echo "NEXT_PUBLIC_ALCHEMY_API_KEY=${{ secrets.NEXT_PUBLIC_ALCHEMY_API_KEY }}" >> .env.local

    - name: Build application
      run: npm run build

    - name: Deploy to Cloudflare Pages (Preview)
      uses: cloudflare/pages-action@v1
      with:
        apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
        accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
        projectName: sociomint
        directory: .next
        gitHubToken: ${{ secrets.GITHUB_TOKEN }}
        wranglerVersion: '3'

    - name: Comment PR with preview URL
      uses: actions/github-script@v7
      with:
        script: |
          const { data: comments } = await github.rest.issues.listComments({
            owner: context.repo.owner,
            repo: context.repo.repo,
            issue_number: context.issue.number,
          });

          const botComment = comments.find(comment =>
            comment.user.type === 'Bot' && comment.body.includes('🚀 Preview Deployment')
          );

          const body = `🚀 Preview Deployment Ready!

          **Environment**: preview
          **URL**: https://sociomint-preview.pages.dev
          **Commit**: ${context.sha.substring(0, 7)}

          📊 Performance metrics and detailed logs are available in the Actions tab.`;

          if (botComment) {
            await github.rest.issues.updateComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              comment_id: botComment.id,
              body: body
            });
          } else {
            await github.rest.issues.createComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
              body: body
            });
          }

  # 数据库迁移（仅生产环境）
  database-migration:
    name: Database Migration
    runs-on: ubuntu-latest
    needs: deploy
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}

    - name: Install dependencies
      run: npm ci

    - name: Run database migrations
      run: node scripts/fix-database-direct.js
      env:
        NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.NEXT_PUBLIC_SUPABASE_URL }}
        SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}

  # 性能监控（仅生产环境）
  performance-monitoring:
    name: Performance Monitoring
    runs-on: ubuntu-latest
    needs: deploy
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run performance baseline
      run: npm run perf:baseline || true

    - name: Run Lighthouse CI
      run: |
        npm install -g @lhci/cli@0.12.x
        lhci autorun || true
      env:
        LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}

    - name: Upload performance report
      uses: actions/upload-artifact@v4
      with:
        name: performance-report
        path: reports/
        retention-days: 30

    - name: Notify deployment success
      run: |
        echo "🎉 Deployment and monitoring complete!"
        echo "Site URL: https://sociomint.com"
