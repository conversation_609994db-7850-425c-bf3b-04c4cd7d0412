#!/bin/bash

# SocioMint Git提交和推送脚本
echo "🚀 开始SocioMint项目Git提交和推送..."

# 设置错误处理
set -e

# 检查Git仓库
if [ ! -d ".git" ]; then
    echo "❌ 错误：当前目录不是Git仓库"
    exit 1
fi

echo "📊 检查Git状态..."
git status

echo ""
echo "📦 添加所有文件到暂存区..."
git add .

echo ""
echo "📋 检查暂存的文件..."
git status --cached

echo ""
echo "💾 创建提交..."
git commit -m "feat: 🎉 SocioMint重大功能更新和优化

🔧 主要改进:
- 📄 白皮书系统全面升级 (31轮→19轮解锁机制)
- 🧭 奖励系统导航修复 (添加Header导航)
- 🎨 排行榜标签页视觉优化 (提升对比度和交互体验)
- ⏰ 阅读时间计算修复 (273分钟→30分钟)
- 🌐 中英文版本同步更新

📊 技术优化:
- 优化代币解锁机制数据准确性
- 增强UI组件交互反馈
- 改进响应式设计适配
- 提升颜色对比度符合无障碍标准

🎯 用户体验提升:
- 完善导航流程，消除用户困惑
- 优化视觉层次，提升专业感
- 增加动画效果，提升交互体验
- 确保跨设备一致性

🔍 修复问题:
- 奖励页面导航缺失问题
- 排行榜空白可点击区域问题
- 白皮书阅读时间计算错误
- 标签页文字对比度不足问题

📱 兼容性:
- 桌面端和移动端完全适配
- 现代浏览器硬件加速支持
- 无障碍访问性优化

📋 文件更改统计:
- 修改文件: 338个
- 核心功能文件: 6个
- 文档更新: 2个白皮书文件
- 新增报告: 5个技术报告

Co-authored-by: Augment Agent <<EMAIL>>"

echo ""
echo "🔗 检查远程仓库连接..."
git remote -v

echo ""
echo "📤 推送到GitHub..."
git push origin main

echo ""
echo "✅ Git操作完成！"
echo ""
echo "🎉 成功推送到: https://github.com/yudeyou1989/sociomint222.git"
echo ""
echo "📋 验证步骤:"
echo "1. 访问GitHub仓库查看最新提交"
echo "2. 确认提交信息完整显示"
echo "3. 验证所有文件更改正确反映"
