# Supabase Configuration
# Get these from: https://supabase.com/dashboard/project/[project-id]/settings/api
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Web3 Configuration
# WalletConnect: https://cloud.walletconnect.com
NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID=a1b2c3d4e5f6g7h8i9j0
# Alchemy: https://dashboard.alchemy.com
NEXT_PUBLIC_ALCHEMY_API_KEY=your_alchemy_api_key

# Social Platform API Keys
# Twitter: https://developer.twitter.com/en/portal/dashboard
TWITTER_API_KEY=your_twitter_api_key
TWITTER_API_SECRET=your_twitter_api_secret
TWITTER_BEARER_TOKEN=your_twitter_bearer_token
# Discord: https://discord.com/developers/applications
DISCORD_BOT_TOKEN=your_discord_bot_token
DISCORD_CLIENT_ID=your_discord_client_id
# Telegram: @BotFather on Telegram
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_BOT_USERNAME=your_bot_username
TELEGRAM_WEBHOOK_SECRET=your_webhook_secret_token

# Payment Configuration
ALIPAY_APP_ID=your_alipay_app_id
ALIPAY_PRIVATE_KEY=your_alipay_private_key
WECHAT_PAY_MERCHANT_ID=your_wechat_pay_merchant_id
WECHAT_PAY_API_KEY=your_wechat_pay_api_key

# HAOX Token Configuration
NEXT_PUBLIC_HAOX_CONTRACT_ADDRESS_MAINNET=0x...
NEXT_PUBLIC_HAOX_CONTRACT_ADDRESS_SEPOLIA=0x...
NEXT_PUBLIC_HAOX_CONTRACT_ADDRESS_BSC=0x...
NEXT_PUBLIC_HAOX_CONTRACT_ADDRESS_POLYGON=0x...
NEXT_PUBLIC_HAOX_TOTAL_SUPPLY=5000000000

# Application Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=http://localhost:3000

# Analytics and Monitoring (Optional)
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX
NEXT_PUBLIC_VERCEL_ANALYTICS_ID=your_vercel_analytics_id
SENTRY_DSN=your_sentry_dsn

# Development/Production Environment
NODE_ENV=development
