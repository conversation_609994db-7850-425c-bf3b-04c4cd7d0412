const { ethers } = require("hardhat");

/**
 * 精简版合约功能测试脚本
 * 验证所有核心功能是否正常工作
 */
async function main() {
    console.log("🧪 开始精简版合约功能测试...");
    
    const [deployer, user1, user2] = await ethers.getSigners();
    console.log("👤 测试账户:");
    console.log("   部署者:", deployer.address);
    console.log("   用户1:", user1.address);
    console.log("   用户2:", user2.address);
    
    const testResults = {
        vestingTests: { passed: 0, total: 0, details: [] },
        aggregatorTests: { passed: 0, total: 0, details: [] },
        integrationTests: { passed: 0, total: 0, details: [] }
    };
    
    try {
        // 测试1: 部署精简版合约
        console.log("\n📦 测试1: 部署精简版合约");
        const contracts = await deployMinimalContracts();
        
        if (contracts.vesting && contracts.aggregator) {
            testResults.vestingTests.passed++;
            testResults.aggregatorTests.passed++;
            console.log("✅ 精简版合约部署成功");
        } else {
            console.log("❌ 精简版合约部署失败");
            return;
        }
        testResults.vestingTests.total++;
        testResults.aggregatorTests.total++;
        
        // 测试2: Vesting合约核心功能
        console.log("\n🔒 测试2: Vesting合约核心功能");
        await testVestingCoreFunctions(contracts.vesting, testResults.vestingTests);
        
        // 测试3: 价格聚合器核心功能
        console.log("\n📊 测试3: 价格聚合器核心功能");
        await testAggregatorCoreFunctions(contracts.aggregator, testResults.aggregatorTests);
        
        // 测试4: 安全功能测试
        console.log("\n🛡️ 测试4: 安全功能测试");
        await testSecurityFeatures(contracts, testResults.integrationTests);
        
        // 测试5: Gas效率测试
        console.log("\n⚡ 测试5: Gas效率测试");
        await testGasEfficiency(contracts, testResults.integrationTests);
        
        // 生成测试报告
        generateTestReport(testResults);
        
    } catch (error) {
        console.error("❌ 测试执行失败:", error);
        throw error;
    }
}

/**
 * 部署精简版合约
 */
async function deployMinimalContracts() {
    console.log("📦 部署精简版合约...");
    
    // 模拟合约地址
    const mockToken = "******************************************";
    const mockOracle = "******************************************";
    const [deployer] = await ethers.getSigners();
    
    try {
        // 部署精简版Vesting合约
        const HAOXVestingV2Minimal = await ethers.getContractFactory("HAOXVestingV2Minimal");
        const vestingContract = await HAOXVestingV2Minimal.deploy(
            mockToken,
            mockOracle,
            deployer.address,
            deployer.address
        );
        await vestingContract.waitForDeployment();
        
        // 部署精简版价格聚合器
        const HAOXPriceAggregatorMinimal = await ethers.getContractFactory("HAOXPriceAggregatorMinimal");
        const aggregatorContract = await HAOXPriceAggregatorMinimal.deploy();
        await aggregatorContract.waitForDeployment();
        
        console.log("✅ 精简版合约部署完成");
        console.log("   Vesting:", await vestingContract.getAddress());
        console.log("   Aggregator:", await aggregatorContract.getAddress());
        
        return {
            vesting: vestingContract,
            aggregator: aggregatorContract
        };
        
    } catch (error) {
        console.error("❌ 合约部署失败:", error);
        return null;
    }
}

/**
 * 测试Vesting合约核心功能
 */
async function testVestingCoreFunctions(vestingContract, results) {
    const tests = [
        {
            name: "轮次信息查询",
            test: async () => {
                const roundInfo = await vestingContract.getRoundInfo(1);
                return roundInfo[0] > 0; // 检查触发价格是否大于0
            }
        },
        {
            name: "解锁进度查询",
            test: async () => {
                const progress = await vestingContract.getUnlockProgress();
                return progress[1] > 0; // 检查下一轮次是否大于0
            }
        },
        {
            name: "价格历史查询",
            test: async () => {
                const history = await vestingContract.getPriceHistory(1);
                return Array.isArray(history); // 检查返回是否为数组
            }
        },
        {
            name: "紧急签名者管理",
            test: async () => {
                const [deployer] = await ethers.getSigners();
                const isSigner = await vestingContract.emergencySigners(deployer.address);
                return isSigner === true; // 检查部署者是否为紧急签名者
            }
        },
        {
            name: "暂停功能",
            test: async () => {
                await vestingContract.pause();
                const isPaused = await vestingContract.paused();
                await vestingContract.unpause();
                return isPaused === true;
            }
        }
    ];
    
    for (const test of tests) {
        results.total++;
        try {
            const passed = await test.test();
            if (passed) {
                results.passed++;
                results.details.push(`✅ ${test.name}: 通过`);
                console.log(`   ✅ ${test.name}: 通过`);
            } else {
                results.details.push(`❌ ${test.name}: 失败`);
                console.log(`   ❌ ${test.name}: 失败`);
            }
        } catch (error) {
            results.details.push(`❌ ${test.name}: 错误 - ${error.message}`);
            console.log(`   ❌ ${test.name}: 错误 - ${error.message}`);
        }
    }
}

/**
 * 测试价格聚合器核心功能
 */
async function testAggregatorCoreFunctions(aggregatorContract, results) {
    const tests = [
        {
            name: "聚合器状态查询",
            test: async () => {
                const status = await aggregatorContract.getAggregatorStatus();
                return status.length === 5; // 检查返回值数量
            }
        },
        {
            name: "价格源添加",
            test: async () => {
                const mockOracle = "0x1234567890123456789012345678901234567890";
                await aggregatorContract.addPriceSource(mockOracle, 50);
                const sourceCount = await aggregatorContract.sourceCount();
                return sourceCount > 0;
            }
        },
        {
            name: "价格源查询",
            test: async () => {
                const sourceInfo = await aggregatorContract.getPriceSource(0);
                return sourceInfo[0] !== "******************************************";
            }
        },
        {
            name: "紧急模式",
            test: async () => {
                await aggregatorContract.activateEmergencyMode(ethers.parseUnits("0.01", 8));
                const status = await aggregatorContract.getAggregatorStatus();
                await aggregatorContract.deactivateEmergencyMode();
                return status[2] === true; // 检查紧急模式是否激活
            }
        },
        {
            name: "暂停功能",
            test: async () => {
                await aggregatorContract.pause();
                const isPaused = await aggregatorContract.paused();
                await aggregatorContract.unpause();
                return isPaused === true;
            }
        }
    ];
    
    for (const test of tests) {
        results.total++;
        try {
            const passed = await test.test();
            if (passed) {
                results.passed++;
                results.details.push(`✅ ${test.name}: 通过`);
                console.log(`   ✅ ${test.name}: 通过`);
            } else {
                results.details.push(`❌ ${test.name}: 失败`);
                console.log(`   ❌ ${test.name}: 失败`);
            }
        } catch (error) {
            results.details.push(`❌ ${test.name}: 错误 - ${error.message}`);
            console.log(`   ❌ ${test.name}: 错误 - ${error.message}`);
        }
    }
}

/**
 * 测试安全功能
 */
async function testSecurityFeatures(contracts, results) {
    const tests = [
        {
            name: "权限控制",
            test: async () => {
                const [, user1] = await ethers.getSigners();
                try {
                    await contracts.vesting.connect(user1).pause();
                    return false; // 如果没有抛出错误，说明权限控制失败
                } catch (error) {
                    return error.message.includes("Ownable"); // 检查是否是权限错误
                }
            }
        },
        {
            name: "时间锁验证",
            test: async () => {
                const emergencyDelay = await contracts.vesting.EMERGENCY_DELAY();
                return emergencyDelay == 7 * 24 * 60 * 60; // 7天
            }
        },
        {
            name: "金额限制验证",
            test: async () => {
                const maxAmount = await contracts.vesting.MAX_EMERGENCY_AMOUNT();
                return maxAmount == ethers.parseEther("1000000"); // 100万代币
            }
        },
        {
            name: "价格偏差检测",
            test: async () => {
                const maxDeviation = await contracts.aggregator.MAX_PRICE_DEVIATION();
                return maxDeviation == 500; // 5%
            }
        }
    ];
    
    for (const test of tests) {
        results.total++;
        try {
            const passed = await test.test();
            if (passed) {
                results.passed++;
                results.details.push(`✅ ${test.name}: 通过`);
                console.log(`   ✅ ${test.name}: 通过`);
            } else {
                results.details.push(`❌ ${test.name}: 失败`);
                console.log(`   ❌ ${test.name}: 失败`);
            }
        } catch (error) {
            results.details.push(`❌ ${test.name}: 错误 - ${error.message}`);
            console.log(`   ❌ ${test.name}: 错误 - ${error.message}`);
        }
    }
}

/**
 * 测试Gas效率
 */
async function testGasEfficiency(contracts, results) {
    console.log("   测量Gas使用情况...");
    
    const tests = [
        {
            name: "Vesting查询Gas效率",
            test: async () => {
                const gasEstimate = await contracts.vesting.getUnlockProgress.estimateGas();
                console.log(`     getUnlockProgress Gas: ${gasEstimate.toString()}`);
                return gasEstimate < 100000; // 期望小于10万Gas
            }
        },
        {
            name: "聚合器查询Gas效率",
            test: async () => {
                const gasEstimate = await contracts.aggregator.getAggregatorStatus.estimateGas();
                console.log(`     getAggregatorStatus Gas: ${gasEstimate.toString()}`);
                return gasEstimate < 50000; // 期望小于5万Gas
            }
        }
    ];
    
    for (const test of tests) {
        results.total++;
        try {
            const passed = await test.test();
            if (passed) {
                results.passed++;
                results.details.push(`✅ ${test.name}: 通过`);
                console.log(`   ✅ ${test.name}: 通过`);
            } else {
                results.details.push(`❌ ${test.name}: 失败`);
                console.log(`   ❌ ${test.name}: 失败`);
            }
        } catch (error) {
            results.details.push(`❌ ${test.name}: 错误 - ${error.message}`);
            console.log(`   ❌ ${test.name}: 错误 - ${error.message}`);
        }
    }
}

/**
 * 生成测试报告
 */
function generateTestReport(testResults) {
    console.log("\n" + "=".repeat(60));
    console.log("📋 精简版合约测试报告");
    console.log("=".repeat(60));
    
    const totalTests = testResults.vestingTests.total + testResults.aggregatorTests.total + testResults.integrationTests.total;
    const totalPassed = testResults.vestingTests.passed + testResults.aggregatorTests.passed + testResults.integrationTests.passed;
    
    console.log(`\n📊 测试总结:`);
    console.log(`   总测试数: ${totalTests}`);
    console.log(`   通过测试: ${totalPassed}`);
    console.log(`   失败测试: ${totalTests - totalPassed}`);
    console.log(`   通过率: ${((totalPassed / totalTests) * 100).toFixed(1)}%`);
    
    console.log(`\n🔒 Vesting合约测试: ${testResults.vestingTests.passed}/${testResults.vestingTests.total}`);
    testResults.vestingTests.details.forEach(detail => console.log(`   ${detail}`));
    
    console.log(`\n📊 聚合器合约测试: ${testResults.aggregatorTests.passed}/${testResults.aggregatorTests.total}`);
    testResults.aggregatorTests.details.forEach(detail => console.log(`   ${detail}`));
    
    console.log(`\n🛡️ 集成和安全测试: ${testResults.integrationTests.passed}/${testResults.integrationTests.total}`);
    testResults.integrationTests.details.forEach(detail => console.log(`   ${detail}`));
    
    // 保存测试报告
    const fs = require('fs');
    const path = require('path');
    
    const reportsDir = path.join(__dirname, '../test-reports');
    if (!fs.existsSync(reportsDir)) {
        fs.mkdirSync(reportsDir, { recursive: true });
    }
    
    const reportData = {
        timestamp: new Date().toISOString(),
        totalTests,
        totalPassed,
        passRate: ((totalPassed / totalTests) * 100).toFixed(1),
        testResults,
        summary: {
            allPassed: totalPassed === totalTests,
            readyForDeployment: totalPassed >= totalTests * 0.9 // 90%通过率
        }
    };
    
    const reportFile = path.join(reportsDir, `minimal-contracts-test-${Date.now()}.json`);
    fs.writeFileSync(reportFile, JSON.stringify(reportData, null, 2));
    
    console.log(`\n📄 详细测试报告已保存: ${reportFile}`);
    
    if (totalPassed === totalTests) {
        console.log("\n🎉 所有测试通过！精简版合约可以安全部署！");
    } else if (totalPassed >= totalTests * 0.9) {
        console.log("\n✅ 大部分测试通过，可以考虑部署，但建议修复失败的测试。");
    } else {
        console.log("\n⚠️  测试通过率较低，建议修复问题后再进行部署。");
    }
    
    console.log("=".repeat(60));
}

// 如果直接运行此脚本
if (require.main === module) {
    main()
        .then(() => {
            console.log("\n🎉 精简版合约测试完成!");
            process.exit(0);
        })
        .catch((error) => {
            console.error("❌ 测试失败:", error);
            process.exit(1);
        });
}

module.exports = { main };
