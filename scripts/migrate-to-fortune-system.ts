/**
 * 数据迁移脚本：从HAOX奖励系统迁移到福气系统
 * 
 * 此脚本将：
 * 1. 为所有现有用户创建福气账户
 * 2. 将现有的HAOX奖励记录转换为福气记录
 * 3. 迁移邀请奖励数据
 * 4. 保持数据一致性
 */

import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';

// 加载环境变量
config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

if (!supabaseUrl || !supabaseServiceKey) {
  throw new Error('Missing Supabase configuration');
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

interface MigrationStats {
  usersProcessed: number;
  fortuneAccountsCreated: number;
  transactionsMigrated: number;
  invitationsMigrated: number;
  errors: string[];
}

/**
 * 主迁移函数
 */
async function migrateToFortuneSystem(): Promise<MigrationStats> {
  const stats: MigrationStats = {
    usersProcessed: 0,
    fortuneAccountsCreated: 0,
    transactionsMigrated: 0,
    invitationsMigrated: 0,
    errors: []
  };

  console.log('🚀 开始迁移到福气系统...');

  try {
    // 1. 获取所有现有用户
    console.log('📋 获取现有用户列表...');
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, email, telegram_username, created_at');

    if (usersError) {
      throw new Error(`获取用户列表失败: ${usersError.message}`);
    }

    console.log(`📊 找到 ${users?.length || 0} 个用户需要迁移`);

    // 2. 为每个用户创建福气账户
    if (users) {
      for (const user of users) {
        try {
          await migrateUserToFortune(user, stats);
          stats.usersProcessed++;
          
          if (stats.usersProcessed % 10 === 0) {
            console.log(`✅ 已处理 ${stats.usersProcessed}/${users.length} 个用户`);
          }
        } catch (error) {
          const errorMsg = `用户 ${user.id} 迁移失败: ${error instanceof Error ? error.message : 'Unknown error'}`;
          stats.errors.push(errorMsg);
          console.error(`❌ ${errorMsg}`);
        }
      }
    }

    // 3. 迁移邀请关系数据
    console.log('🔗 迁移邀请关系数据...');
    await migrateInvitationData(stats);

    // 4. 验证迁移结果
    console.log('🔍 验证迁移结果...');
    await validateMigration(stats);

    console.log('🎉 迁移完成！');
    printMigrationSummary(stats);

  } catch (error) {
    console.error('💥 迁移过程中发生错误:', error);
    stats.errors.push(error instanceof Error ? error.message : 'Unknown error');
  }

  return stats;
}

/**
 * 迁移单个用户到福气系统
 */
async function migrateUserToFortune(user: any, stats: MigrationStats): Promise<void> {
  // 检查是否已经有福气账户
  const { data: existingFortune } = await supabase
    .from('user_fortune')
    .select('user_id')
    .eq('user_id', user.id)
    .single();

  if (existingFortune) {
    console.log(`⏭️  用户 ${user.id} 已有福气账户，跳过`);
    return;
  }

  // 获取用户的HAOX奖励历史（如果存在）
  const { data: haoxRewards } = await supabase
    .from('user_rewards') // 假设原有的奖励表名
    .select('*')
    .eq('user_id', user.id);

  // 计算总奖励金额
  let totalEarned = 0;
  if (haoxRewards) {
    totalEarned = haoxRewards.reduce((sum, reward) => {
      return sum + parseFloat(reward.amount || '0');
    }, 0);
  }

  // 创建福气账户
  const { error: fortuneError } = await supabase
    .from('user_fortune')
    .insert({
      user_id: user.id,
      available_fortune: totalEarned, // 将HAOX奖励转换为福气
      locked_fortune: 0,
      total_earned: totalEarned,
      total_spent: 0,
      total_deposited: 0,
      total_withdrawn: 0,
      fortune_level: calculateFortuneLevel(totalEarned),
      fortune_level_name: getFortuneeLevelName(calculateFortuneLevel(totalEarned)),
      consecutive_checkin_days: 0,
      total_checkin_days: 0,
      total_invitations: 0,
      successful_invitations: 0,
      created_at: user.created_at,
      updated_at: new Date().toISOString()
    });

  if (fortuneError) {
    throw new Error(`创建福气账户失败: ${fortuneError.message}`);
  }

  stats.fortuneAccountsCreated++;

  // 迁移奖励记录为福气交易记录
  if (haoxRewards && haoxRewards.length > 0) {
    await migrateRewardTransactions(user.id, haoxRewards, stats);
  }

  // 发放新用户注册奖励（如果是新用户）
  const registrationDate = new Date(user.created_at);
  const now = new Date();
  const daysSinceRegistration = (now.getTime() - registrationDate.getTime()) / (1000 * 60 * 60 * 24);

  if (daysSinceRegistration <= 30) { // 30天内的新用户
    await addRegistrationBonus(user.id, stats);
  }
}

/**
 * 迁移奖励记录为福气交易记录
 */
async function migrateRewardTransactions(userId: string, haoxRewards: any[], stats: MigrationStats): Promise<void> {
  const fortuneTransactions = haoxRewards.map(reward => ({
    id: crypto.randomUUID(),
    user_id: userId,
    transaction_type: mapRewardTypeToFortuneType(reward.type),
    amount: parseFloat(reward.amount || '0'),
    balance_before: 0, // 历史数据无法准确计算
    balance_after: 0,  // 历史数据无法准确计算
    reference_id: reward.reference_id,
    reference_type: reward.reference_type,
    description: `迁移自HAOX奖励: ${reward.description || reward.type}`,
    created_at: reward.created_at || new Date().toISOString()
  }));

  if (fortuneTransactions.length > 0) {
    const { error } = await supabase
      .from('fortune_transactions')
      .insert(fortuneTransactions);

    if (error) {
      throw new Error(`迁移奖励记录失败: ${error.message}`);
    }

    stats.transactionsMigrated += fortuneTransactions.length;
  }
}

/**
 * 迁移邀请关系数据
 */
async function migrateInvitationData(stats: MigrationStats): Promise<void> {
  // 获取现有的邀请数据（如果存在）
  const { data: existingInvitations } = await supabase
    .from('invitation_records') // 假设原有的邀请表名
    .select('*');

  if (existingInvitations && existingInvitations.length > 0) {
    const userInvitations = existingInvitations.map(inv => ({
      id: crypto.randomUUID(),
      inviter_id: inv.inviter_id,
      invitee_id: inv.invitee_id,
      invite_code: inv.invite_code,
      status: inv.status || 'completed',
      reward_processed: true,
      processed_at: inv.created_at,
      created_at: inv.created_at,
      updated_at: new Date().toISOString()
    }));

    const { error } = await supabase
      .from('user_invitations')
      .insert(userInvitations);

    if (error) {
      throw new Error(`迁移邀请数据失败: ${error.message}`);
    }

    stats.invitationsMigrated = userInvitations.length;
  }
}

/**
 * 添加注册奖励
 */
async function addRegistrationBonus(userId: string, stats: MigrationStats): Promise<void> {
  const registrationBonus = 50; // 注册奖励50福气

  // 更新福气余额
  const { error: updateError } = await supabase
    .from('user_fortune')
    .update({
      available_fortune: supabase.rpc('increment_fortune', { amount: registrationBonus }),
      total_earned: supabase.rpc('increment_fortune', { amount: registrationBonus }),
      updated_at: new Date().toISOString()
    })
    .eq('user_id', userId);

  if (updateError) {
    console.warn(`添加注册奖励失败: ${updateError.message}`);
    return;
  }

  // 记录交易
  const { error: transactionError } = await supabase
    .from('fortune_transactions')
    .insert({
      id: crypto.randomUUID(),
      user_id: userId,
      transaction_type: 'registration_bonus',
      amount: registrationBonus,
      balance_before: 0,
      balance_after: registrationBonus,
      description: '新用户注册奖励 +50福气',
      created_at: new Date().toISOString()
    });

  if (transactionError) {
    console.warn(`记录注册奖励交易失败: ${transactionError.message}`);
  }
}

/**
 * 验证迁移结果
 */
async function validateMigration(stats: MigrationStats): Promise<void> {
  // 检查福气账户数量
  const { count: fortuneCount } = await supabase
    .from('user_fortune')
    .select('*', { count: 'exact', head: true });

  // 检查福气交易数量
  const { count: transactionCount } = await supabase
    .from('fortune_transactions')
    .select('*', { count: 'exact', head: true });

  console.log(`✅ 验证结果:`);
  console.log(`   - 福气账户数量: ${fortuneCount}`);
  console.log(`   - 福气交易数量: ${transactionCount}`);
}

/**
 * 打印迁移摘要
 */
function printMigrationSummary(stats: MigrationStats): void {
  console.log('\n📊 迁移摘要:');
  console.log(`   👥 处理用户数: ${stats.usersProcessed}`);
  console.log(`   🍀 创建福气账户: ${stats.fortuneAccountsCreated}`);
  console.log(`   📝 迁移交易记录: ${stats.transactionsMigrated}`);
  console.log(`   🔗 迁移邀请记录: ${stats.invitationsMigrated}`);
  console.log(`   ❌ 错误数量: ${stats.errors.length}`);

  if (stats.errors.length > 0) {
    console.log('\n❌ 错误详情:');
    stats.errors.forEach((error, index) => {
      console.log(`   ${index + 1}. ${error}`);
    });
  }
}

/**
 * 辅助函数
 */
function mapRewardTypeToFortuneType(rewardType: string): string {
  const typeMap: Record<string, string> = {
    'daily_signin': 'daily_checkin',
    'invitation': 'invite_reward',
    'social_task': 'share_reward',
    'referral': 'invite_reward',
    'bonus': 'admin_adjust'
  };

  return typeMap[rewardType] || 'admin_adjust';
}

function calculateFortuneLevel(totalFortune: number): number {
  if (totalFortune >= 1000000) return 5;
  if (totalFortune >= 100000) return 4;
  if (totalFortune >= 10000) return 3;
  if (totalFortune >= 1000) return 2;
  return 1;
}

function getFortuneeLevelName(level: number): string {
  const levelNames = {
    1: '初来乍到',
    2: '小有福气',
    3: '福气满满',
    4: '福星高照',
    5: '福气无边'
  };
  return levelNames[level as keyof typeof levelNames] || '初来乍到';
}

/**
 * 执行迁移
 */
if (require.main === module) {
  migrateToFortuneSystem()
    .then((stats) => {
      console.log('\n🎉 迁移完成！');
      process.exit(stats.errors.length > 0 ? 1 : 0);
    })
    .catch((error) => {
      console.error('💥 迁移失败:', error);
      process.exit(1);
    });
}

export { migrateToFortuneSystem };
