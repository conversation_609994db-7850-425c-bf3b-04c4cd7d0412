/**
 * 修复验证脚本
 * 验证代码审查中发现的问题是否已修复
 */

interface ValidationResult {
  category: string;
  checks: Array<{
    name: string;
    status: 'PASS' | 'FAIL' | 'WARNING';
    message: string;
    details?: any;
  }>;
}

/**
 * 主验证函数
 */
async function validateFixes(): Promise<void> {
  console.log('🔍 开始验证Social Bet系统修复...\n');

  const results: ValidationResult[] = [
    await validateDatabaseConstraints(),
    await validateAPIRateLimiting(),
    await validateConcurrencyControl(),
    await validateSecurityMechanisms(),
    await validatePerformanceOptimizations(),
    await validateMonitoringSystem()
  ];

  printValidationResults(results);
}

/**
 * 验证数据库约束
 */
async function validateDatabaseConstraints(): Promise<ValidationResult> {
  const checks = [];

  // 检查修复脚本是否存在
  const fs = require('fs');
  const fixScriptExists = fs.existsSync('scripts/fix-critical-issues.sql');
  
  checks.push({
    name: '数据库修复脚本',
    status: fixScriptExists ? 'PASS' : 'FAIL' as const,
    message: fixScriptExists ? '修复脚本已创建' : '修复脚本缺失'
  });

  if (fixScriptExists) {
    const scriptContent = fs.readFileSync('scripts/fix-critical-issues.sql', 'utf8');
    
    // 检查外键约束
    const foreignKeyCount = (scriptContent.match(/FOREIGN KEY/g) || []).length;
    checks.push({
      name: '外键约束',
      status: foreignKeyCount >= 6 ? 'PASS' : 'WARNING' as const,
      message: `已定义${foreignKeyCount}个外键约束`,
      details: { expectedMinimum: 6, actual: foreignKeyCount }
    });

    // 检查CHECK约束
    const checkConstraintCount = (scriptContent.match(/ADD CONSTRAINT.*CHECK/g) || []).length;
    checks.push({
      name: 'CHECK约束',
      status: checkConstraintCount >= 10 ? 'PASS' : 'WARNING' as const,
      message: `已定义${checkConstraintCount}个CHECK约束`,
      details: { expectedMinimum: 10, actual: checkConstraintCount }
    });

    // 检查并发控制函数
    const hasConcurrencyControl = scriptContent.includes('update_bet_with_lock');
    checks.push({
      name: '并发控制函数',
      status: hasConcurrencyControl ? 'PASS' : 'FAIL' as const,
      message: hasConcurrencyControl ? '并发控制函数已定义' : '并发控制函数缺失'
    });

    // 检查数据一致性检查函数
    const hasConsistencyCheck = scriptContent.includes('check_bet_data_consistency');
    checks.push({
      name: '数据一致性检查',
      status: hasConsistencyCheck ? 'PASS' : 'FAIL' as const,
      message: hasConsistencyCheck ? '一致性检查函数已定义' : '一致性检查函数缺失'
    });
  }

  return {
    category: '数据库约束修复',
    checks
  };
}

/**
 * 验证API频率限制
 */
async function validateAPIRateLimiting(): Promise<ValidationResult> {
  const checks = [];
  const fs = require('fs');

  // 检查频率限制中间件
  const rateLimiterExists = fs.existsSync('src/middleware/rateLimiter.ts');
  checks.push({
    name: '频率限制中间件',
    status: rateLimiterExists ? 'PASS' : 'FAIL' as const,
    message: rateLimiterExists ? '频率限制中间件已创建' : '频率限制中间件缺失'
  });

  if (rateLimiterExists) {
    const middlewareContent = fs.readFileSync('src/middleware/rateLimiter.ts', 'utf8');
    
    // 检查配置完整性
    const hasGeneralConfig = middlewareContent.includes('GENERAL:');
    const hasSensitiveConfig = middlewareContent.includes('SENSITIVE:');
    const hasBettingConfig = middlewareContent.includes('BETTING:');
    
    checks.push({
      name: '限制配置完整性',
      status: (hasGeneralConfig && hasSensitiveConfig && hasBettingConfig) ? 'PASS' : 'WARNING' as const,
      message: '频率限制配置已定义',
      details: {
        general: hasGeneralConfig,
        sensitive: hasSensitiveConfig,
        betting: hasBettingConfig
      }
    });

    // 检查核心功能
    const hasRateLimitCheck = middlewareContent.includes('checkRateLimit');
    const hasCleanup = middlewareContent.includes('cleanupExpiredRecords');
    
    checks.push({
      name: '核心功能实现',
      status: (hasRateLimitCheck && hasCleanup) ? 'PASS' : 'FAIL' as const,
      message: '频率限制核心功能已实现'
    });

    // 检查统计功能
    const hasStats = middlewareContent.includes('getRateLimitStats');
    checks.push({
      name: '统计监控功能',
      status: hasStats ? 'PASS' : 'WARNING' as const,
      message: hasStats ? '统计功能已实现' : '建议添加统计功能'
    });
  }

  return {
    category: 'API频率限制',
    checks
  };
}

/**
 * 验证并发控制
 */
async function validateConcurrencyControl(): Promise<ValidationResult> {
  const checks = [];
  const fs = require('fs');

  // 检查服务层并发处理
  const serviceFiles = [
    'src/services/socialbet/SocialBetService.ts',
    'src/services/socialbet/SettlementService.ts',
    'src/services/socialbet/JudgmentService.ts'
  ];

  let concurrencyImplemented = 0;
  serviceFiles.forEach(file => {
    if (fs.existsSync(file)) {
      const content = fs.readFileSync(file, 'utf8');
      if (content.includes('transaction') || content.includes('lock') || content.includes('atomic')) {
        concurrencyImplemented++;
      }
    }
  });

  checks.push({
    name: '服务层并发控制',
    status: concurrencyImplemented > 0 ? 'PASS' : 'WARNING' as const,
    message: `${concurrencyImplemented}/${serviceFiles.length}个服务实现了并发控制`,
    details: { implemented: concurrencyImplemented, total: serviceFiles.length }
  });

  // 检查数据库层并发控制
  const fixScriptExists = fs.existsSync('scripts/fix-critical-issues.sql');
  if (fixScriptExists) {
    const scriptContent = fs.readFileSync('scripts/fix-critical-issues.sql', 'utf8');
    const hasLockFunction = scriptContent.includes('FOR UPDATE');
    
    checks.push({
      name: '数据库层并发控制',
      status: hasLockFunction ? 'PASS' : 'FAIL' as const,
      message: hasLockFunction ? '数据库锁机制已实现' : '数据库锁机制缺失'
    });
  }

  return {
    category: '并发控制机制',
    checks
  };
}

/**
 * 验证安全机制
 */
async function validateSecurityMechanisms(): Promise<ValidationResult> {
  const checks = [];
  const fs = require('fs');

  // 检查安全服务
  const securityServiceExists = fs.existsSync('src/services/security/SecurityService.ts');
  checks.push({
    name: '安全服务',
    status: securityServiceExists ? 'PASS' : 'FAIL' as const,
    message: securityServiceExists ? '安全服务已实现' : '安全服务缺失'
  });

  if (securityServiceExists) {
    const securityContent = fs.readFileSync('src/services/security/SecurityService.ts', 'utf8');
    
    // 检查防作弊功能
    const antiCheatFeatures = [
      'detectCoordinatedVoting',
      'detectRapidBetting',
      'detectBalanceInconsistency',
      'calculateUserRiskScore'
    ];

    let implementedFeatures = 0;
    antiCheatFeatures.forEach(feature => {
      if (securityContent.includes(feature)) {
        implementedFeatures++;
      }
    });

    checks.push({
      name: '防作弊功能',
      status: implementedFeatures === antiCheatFeatures.length ? 'PASS' : 'WARNING' as const,
      message: `${implementedFeatures}/${antiCheatFeatures.length}个防作弊功能已实现`,
      details: { implemented: implementedFeatures, total: antiCheatFeatures.length }
    });

    // 检查紧急暂停机制
    const hasEmergencyPause = securityContent.includes('emergencyPause');
    checks.push({
      name: '紧急暂停机制',
      status: hasEmergencyPause ? 'PASS' : 'WARNING' as const,
      message: hasEmergencyPause ? '紧急暂停机制已实现' : '建议实现紧急暂停机制'
    });
  }

  return {
    category: '安全防护机制',
    checks
  };
}

/**
 * 验证性能优化
 */
async function validatePerformanceOptimizations(): Promise<ValidationResult> {
  const checks = [];
  const fs = require('fs');

  // 检查性能服务
  const performanceServiceExists = fs.existsSync('src/services/performance/PerformanceService.ts');
  checks.push({
    name: '性能测试服务',
    status: performanceServiceExists ? 'PASS' : 'FAIL' as const,
    message: performanceServiceExists ? '性能测试服务已实现' : '性能测试服务缺失'
  });

  // 检查压力测试脚本
  const stressTestExists = fs.existsSync('scripts/stress-test.ts');
  checks.push({
    name: '压力测试脚本',
    status: stressTestExists ? 'PASS' : 'FAIL' as const,
    message: stressTestExists ? '压力测试脚本已创建' : '压力测试脚本缺失'
  });

  // 检查数据库索引优化
  const migrationExists = fs.existsSync('database/migrations/005_create_social_bet_system.sql');
  if (migrationExists) {
    const migrationContent = fs.readFileSync('database/migrations/005_create_social_bet_system.sql', 'utf8');
    const indexCount = (migrationContent.match(/CREATE INDEX/g) || []).length;
    
    checks.push({
      name: '数据库索引优化',
      status: indexCount >= 10 ? 'PASS' : 'WARNING' as const,
      message: `已创建${indexCount}个数据库索引`,
      details: { expectedMinimum: 10, actual: indexCount }
    });
  }

  return {
    category: '性能优化',
    checks
  };
}

/**
 * 验证监控系统
 */
async function validateMonitoringSystem(): Promise<ValidationResult> {
  const checks = [];
  const fs = require('fs');

  // 检查监控服务
  const monitoringServiceExists = fs.existsSync('src/services/monitoring/MonitoringService.ts');
  checks.push({
    name: '监控服务',
    status: monitoringServiceExists ? 'PASS' : 'FAIL' as const,
    message: monitoringServiceExists ? '监控服务已实现' : '监控服务缺失'
  });

  if (monitoringServiceExists) {
    const monitoringContent = fs.readFileSync('src/services/monitoring/MonitoringService.ts', 'utf8');
    
    // 检查监控功能
    const monitoringFeatures = [
      'collectBusinessMetrics',
      'collectTechnicalMetrics',
      'collectSecurityMetrics',
      'generateAlerts'
    ];

    let implementedFeatures = 0;
    monitoringFeatures.forEach(feature => {
      if (monitoringContent.includes(feature)) {
        implementedFeatures++;
      }
    });

    checks.push({
      name: '监控功能完整性',
      status: implementedFeatures === monitoringFeatures.length ? 'PASS' : 'WARNING' as const,
      message: `${implementedFeatures}/${monitoringFeatures.length}个监控功能已实现`
    });

    // 检查告警机制
    const hasAlertSystem = monitoringContent.includes('sendAlert');
    checks.push({
      name: '告警机制',
      status: hasAlertSystem ? 'PASS' : 'WARNING' as const,
      message: hasAlertSystem ? '告警机制已实现' : '建议完善告警机制'
    });
  }

  // 检查综合测试
  const comprehensiveTestExists = fs.existsSync('scripts/comprehensive-system-test.ts');
  checks.push({
    name: '综合测试脚本',
    status: comprehensiveTestExists ? 'PASS' : 'FAIL' as const,
    message: comprehensiveTestExists ? '综合测试脚本已创建' : '综合测试脚本缺失'
  });

  return {
    category: '监控告警系统',
    checks
  };
}

/**
 * 打印验证结果
 */
function printValidationResults(results: ValidationResult[]): void {
  console.log('📊 Social Bet系统修复验证报告\n');

  let totalChecks = 0;
  let passedChecks = 0;
  let failedChecks = 0;
  let warningChecks = 0;

  results.forEach(result => {
    const categoryIcon = result.checks.every(c => c.status === 'PASS') ? '✅' : 
                        result.checks.some(c => c.status === 'FAIL') ? '❌' : '⚠️';
    
    console.log(`${categoryIcon} ${result.category}:`);
    
    result.checks.forEach(check => {
      const statusIcon = check.status === 'PASS' ? '  ✅' : 
                        check.status === 'FAIL' ? '  ❌' : '  ⚠️';
      
      console.log(`${statusIcon} ${check.name}: ${check.message}`);
      
      if (check.details) {
        console.log(`     详情: ${JSON.stringify(check.details)}`);
      }
      
      totalChecks++;
      if (check.status === 'PASS') passedChecks++;
      else if (check.status === 'FAIL') failedChecks++;
      else warningChecks++;
    });
    
    console.log('');
  });

  // 总结
  console.log('🎯 验证总结:');
  console.log(`✅ 通过: ${passedChecks}/${totalChecks}`);
  console.log(`❌ 失败: ${failedChecks}/${totalChecks}`);
  console.log(`⚠️ 警告: ${warningChecks}/${totalChecks}`);
  console.log(`📈 通过率: ${((passedChecks / totalChecks) * 100).toFixed(1)}%`);

  if (failedChecks === 0) {
    console.log('\n🎉 所有关键修复已完成！系统可以部署到生产环境。');
  } else {
    console.log(`\n⚠️ 还有${failedChecks}个关键问题需要修复。`);
  }

  // 建议
  console.log('\n💡 后续建议:');
  if (failedChecks > 0) {
    console.log('1. 优先修复失败的检查项');
  }
  if (warningChecks > 0) {
    console.log('2. 考虑改进警告项以提升系统稳定性');
  }
  console.log('3. 在生产环境部署前运行完整的压力测试');
  console.log('4. 设置监控告警，密切关注系统运行状态');
  console.log('5. 准备灾难恢复计划和数据备份策略');
}

/**
 * 执行验证
 */
if (require.main === module) {
  validateFixes()
    .then(() => {
      console.log('\n🏁 修复验证完成！');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 验证失败:', error);
      process.exit(1);
    });
}

export { validateFixes };
