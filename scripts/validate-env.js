#!/usr/bin/env node

/**
 * 环境变量验证脚本
 * 验证环境变量的完整性和有效性
 */

import { readFile } from 'fs/promises';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

// 颜色定义
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 必需的环境变量定义
const REQUIRED_VARS = {
  // 基础配置
  NODE_ENV: {
    required: true,
    values: ['development', 'staging', 'production'],
    description: '应用运行环境'
  },
  NEXT_PUBLIC_APP_URL: {
    required: true,
    pattern: /^https?:\/\/.+/,
    description: '应用访问URL'
  },
  
  // Supabase 配置
  NEXT_PUBLIC_SUPABASE_URL: {
    required: true,
    pattern: /^https:\/\/.+\.supabase\.co$/,
    description: 'Supabase 项目URL'
  },
  NEXT_PUBLIC_SUPABASE_ANON_KEY: {
    required: true,
    minLength: 100,
    description: 'Supabase 匿名密钥'
  },
  SUPABASE_SERVICE_ROLE_KEY: {
    required: true,
    minLength: 100,
    description: 'Supabase 服务角色密钥'
  },
  
  // 认证配置
  JWT_SECRET: {
    required: true,
    minLength: 32,
    description: 'JWT 签名密钥'
  },
  NEXTAUTH_SECRET: {
    required: true,
    minLength: 32,
    description: 'NextAuth 密钥'
  },
  
  // Telegram 配置
  TELEGRAM_BOT_TOKEN: {
    required: true,
    pattern: /^\d+:[A-Za-z0-9_-]+$/,
    description: 'Telegram Bot Token'
  },
  TELEGRAM_BOT_USERNAME: {
    required: true,
    pattern: /^[a-zA-Z0-9_]+$/,
    description: 'Telegram Bot 用户名'
  },
  NEXT_PUBLIC_TELEGRAM_BOT_USERNAME: {
    required: true,
    pattern: /^[a-zA-Z0-9_]+$/,
    description: 'Telegram Bot 用户名（公开）'
  },
  
  // 区块链配置
  NEXT_PUBLIC_CHAIN_ID: {
    required: true,
    values: ['97', '56', '1337'],
    description: '区块链网络ID'
  },
  NEXT_PUBLIC_RPC_URL: {
    required: true,
    pattern: /^https?:\/\/.+/,
    description: '区块链RPC节点URL'
  },
  NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID: {
    required: false,
    pattern: /^[a-f0-9]{32}$/,
    description: 'WalletConnect 项目ID'
  },
  
  // 智能合约地址
  NEXT_PUBLIC_HAOX_TOKEN_ADDRESS: {
    required: true,
    pattern: /^0x[a-fA-F0-9]{40}$/,
    description: 'HAOX 代币合约地址'
  },
  NEXT_PUBLIC_HAOX_PRESALE_ADDRESS: {
    required: true,
    pattern: /^0x[a-fA-F0-9]{40}$/,
    description: 'HAOX 预售合约地址'
  },
  
  // 可选配置
  NEXT_PUBLIC_ALCHEMY_API_KEY: {
    required: false,
    minLength: 32,
    description: 'Alchemy API 密钥'
  },
  SENTRY_DSN: {
    required: false,
    pattern: /^https:\/\/[a-f0-9]+@[a-f0-9]+\.ingest\.sentry\.io\/\d+$/,
    description: 'Sentry DSN'
  },
  NEXT_PUBLIC_GA_ID: {
    required: false,
    pattern: /^G-[A-Z0-9]+$/,
    description: 'Google Analytics ID'
  }
};

// 环境特定的变量要求
const ENV_SPECIFIC_VARS = {
  production: {
    NEXT_PUBLIC_APP_URL: 'https://sociomint.app',
    NODE_ENV: 'production'
  },
  staging: {
    NEXT_PUBLIC_APP_URL: 'https://staging.sociomint.app',
    NODE_ENV: 'staging'
  },
  development: {
    NODE_ENV: 'development'
  }
};

/**
 * 加载环境变量文件
 */
async function loadEnvFile(envFile) {
  try {
    const content = await readFile(envFile, 'utf-8');
    const vars = {};
    
    content.split('\n').forEach((line, index) => {
      const trimmed = line.trim();
      
      // 跳过注释和空行
      if (!trimmed || trimmed.startsWith('#')) {
        return;
      }
      
      const equalIndex = trimmed.indexOf('=');
      if (equalIndex === -1) {
        log('yellow', `警告: 第 ${index + 1} 行格式不正确: ${trimmed}`);
        return;
      }
      
      const key = trimmed.substring(0, equalIndex).trim();
      const value = trimmed.substring(equalIndex + 1).trim();
      
      // 移除引号
      const cleanValue = value.replace(/^["']|["']$/g, '');
      vars[key] = cleanValue;
    });
    
    return vars;
  } catch (error) {
    throw new Error(`无法读取环境文件 ${envFile}: ${error.message}`);
  }
}

/**
 * 验证单个环境变量
 */
function validateVar(key, value, config) {
  const errors = [];
  
  // 检查是否为必需变量
  if (config.required && (!value || value.trim() === '')) {
    errors.push(`缺少必需的环境变量: ${key}`);
    return errors;
  }
  
  // 如果变量不存在且不是必需的，跳过验证
  if (!value) {
    return errors;
  }
  
  // 检查值是否在允许的列表中
  if (config.values && !config.values.includes(value)) {
    errors.push(`${key} 的值必须是以下之一: ${config.values.join(', ')}`);
  }
  
  // 检查正则表达式模式
  if (config.pattern && !config.pattern.test(value)) {
    errors.push(`${key} 的格式不正确`);
  }
  
  // 检查最小长度
  if (config.minLength && value.length < config.minLength) {
    errors.push(`${key} 的长度至少需要 ${config.minLength} 个字符`);
  }
  
  // 检查占位符值
  if (value.includes('your_') || value.includes('YOUR_') || 
      value.includes('_HERE') || value === 'change_me') {
    errors.push(`${key} 包含占位符值，需要设置实际值`);
  }
  
  return errors;
}

/**
 * 验证环境特定的要求
 */
function validateEnvSpecific(vars, env) {
  const errors = [];
  const requirements = ENV_SPECIFIC_VARS[env];
  
  if (!requirements) {
    return errors;
  }
  
  Object.entries(requirements).forEach(([key, expectedValue]) => {
    if (vars[key] !== expectedValue) {
      errors.push(`${env} 环境中 ${key} 应该是 "${expectedValue}"，当前是 "${vars[key]}"`);
    }
  });
  
  return errors;
}

/**
 * 检查变量一致性
 */
function validateConsistency(vars) {
  const errors = [];
  
  // 检查 Telegram Bot 用户名一致性
  if (vars.TELEGRAM_BOT_USERNAME && vars.NEXT_PUBLIC_TELEGRAM_BOT_USERNAME) {
    if (vars.TELEGRAM_BOT_USERNAME !== vars.NEXT_PUBLIC_TELEGRAM_BOT_USERNAME) {
      errors.push('TELEGRAM_BOT_USERNAME 和 NEXT_PUBLIC_TELEGRAM_BOT_USERNAME 应该相同');
    }
  }
  
  // 检查链ID和RPC URL的一致性
  if (vars.NEXT_PUBLIC_CHAIN_ID && vars.NEXT_PUBLIC_RPC_URL) {
    const chainId = vars.NEXT_PUBLIC_CHAIN_ID;
    const rpcUrl = vars.NEXT_PUBLIC_RPC_URL;
    
    if (chainId === '97' && !rpcUrl.includes('testnet')) {
      errors.push('链ID 97 (BSC测试网) 应该使用测试网RPC URL');
    }
    
    if (chainId === '56' && rpcUrl.includes('testnet')) {
      errors.push('链ID 56 (BSC主网) 不应该使用测试网RPC URL');
    }
  }
  
  return errors;
}

/**
 * 生成验证报告
 */
function generateReport(vars, errors, warnings) {
  log('cyan', '\n📊 环境变量验证报告');
  log('cyan', '='.repeat(50));
  
  // 统计信息
  const totalVars = Object.keys(REQUIRED_VARS).length;
  const configuredVars = Object.keys(vars).filter(key => REQUIRED_VARS[key]).length;
  const requiredVars = Object.values(REQUIRED_VARS).filter(config => config.required).length;
  const configuredRequired = Object.keys(vars).filter(key => 
    REQUIRED_VARS[key] && REQUIRED_VARS[key].required && vars[key]
  ).length;
  
  log('blue', `总变量数: ${totalVars}`);
  log('blue', `已配置变量: ${configuredVars}`);
  log('blue', `必需变量: ${requiredVars}`);
  log('blue', `已配置必需变量: ${configuredRequired}`);
  
  // 错误和警告
  if (errors.length > 0) {
    log('red', `\n❌ 发现 ${errors.length} 个错误:`);
    errors.forEach(error => log('red', `  - ${error}`));
  }
  
  if (warnings.length > 0) {
    log('yellow', `\n⚠️  发现 ${warnings.length} 个警告:`);
    warnings.forEach(warning => log('yellow', `  - ${warning}`));
  }
  
  if (errors.length === 0 && warnings.length === 0) {
    log('green', '\n✅ 所有环境变量验证通过！');
  }
  
  // 缺失的可选变量
  const missingOptional = Object.keys(REQUIRED_VARS).filter(key => 
    !REQUIRED_VARS[key].required && !vars[key]
  );
  
  if (missingOptional.length > 0) {
    log('cyan', `\n💡 可选的未配置变量:`);
    missingOptional.forEach(key => {
      log('cyan', `  - ${key}: ${REQUIRED_VARS[key].description}`);
    });
  }
}

/**
 * 主验证函数
 */
async function validateEnvironment(envFile) {
  log('magenta', `🔍 验证环境文件: ${envFile}`);
  
  try {
    // 加载环境变量
    const vars = await loadEnvFile(envFile);
    const errors = [];
    const warnings = [];
    
    // 验证每个变量
    Object.entries(REQUIRED_VARS).forEach(([key, config]) => {
      const value = vars[key];
      const varErrors = validateVar(key, value, config);
      errors.push(...varErrors);
    });
    
    // 验证环境特定要求
    const env = vars.NODE_ENV || 'development';
    const envErrors = validateEnvSpecific(vars, env);
    errors.push(...envErrors);
    
    // 验证一致性
    const consistencyErrors = validateConsistency(vars);
    errors.push(...consistencyErrors);
    
    // 生成报告
    generateReport(vars, errors, warnings);
    
    return {
      success: errors.length === 0,
      errors,
      warnings,
      vars
    };
    
  } catch (error) {
    log('red', `❌ 验证失败: ${error.message}`);
    return {
      success: false,
      errors: [error.message],
      warnings: [],
      vars: {}
    };
  }
}

/**
 * 主函数
 */
async function main() {
  const envFile = process.argv[2] || '.env.local';
  const envPath = join(projectRoot, envFile);
  
  console.log('🔧 SocioMint 环境变量验证工具');
  console.log('='.repeat(40));
  
  const result = await validateEnvironment(envPath);
  
  if (result.success) {
    log('green', '\n🎉 环境变量验证成功！');
    process.exit(0);
  } else {
    log('red', '\n💥 环境变量验证失败！');
    log('red', '请修复上述错误后重新运行验证。');
    process.exit(1);
  }
}

// 运行主函数
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    log('red', `💥 脚本执行失败: ${error.message}`);
    process.exit(1);
  });
}
