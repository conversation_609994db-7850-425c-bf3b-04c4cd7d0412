#!/usr/bin/env node

/**
 * 安全环境变量设置脚本
 * 
 * 使用方法：
 * 1. node scripts/setup-secure-env.js
 * 2. 按提示输入钱包信息
 * 3. 脚本会自动创建.env.local文件
 * 4. 私钥信息不会显示在终端
 */

import fs from 'fs';
import path from 'path';
import { createInterface } from 'readline';

const rl = createInterface({
  input: process.stdin,
  output: process.stdout
});

// 隐藏输入的函数（用于私钥输入）
function hiddenInput(prompt) {
  return new Promise((resolve) => {
    process.stdout.write(prompt);
    process.stdin.setRawMode(true);
    process.stdin.resume();
    process.stdin.setEncoding('utf8');
    
    let input = '';
    process.stdin.on('data', (char) => {
      char = char.toString();
      
      if (char === '\n' || char === '\r' || char === '\u0004') {
        process.stdin.setRawMode(false);
        process.stdin.pause();
        process.stdout.write('\n');
        resolve(input);
      } else if (char === '\u0003') {
        process.exit();
      } else if (char === '\u007f') { // backspace
        if (input.length > 0) {
          input = input.slice(0, -1);
          process.stdout.write('\b \b');
        }
      } else {
        input += char;
        process.stdout.write('*');
      }
    });
  });
}

// 普通输入函数
function normalInput(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, (answer) => {
      resolve(answer);
    });
  });
}

// 验证以太坊地址格式
function isValidAddress(address) {
  return /^0x[a-fA-F0-9]{40}$/.test(address);
}

// 验证私钥格式
function isValidPrivateKey(privateKey) {
  return /^[a-fA-F0-9]{64}$/.test(privateKey);
}

async function main() {
  console.log('🔐 SocioMint 安全环境变量设置');
  console.log('=====================================');
  console.log('⚠️  重要提醒：');
  console.log('- 私钥信息将被隐藏显示');
  console.log('- 信息仅保存在本地.env.local文件');
  console.log('- 请确保您的电脑安全');
  console.log('=====================================\n');

  const config = {};

  try {
    // 收集钱包地址信息
    console.log('📍 1. 钱包地址信息：');
    
    config.DEPLOYER_WALLET_ADDRESS = await normalInput('部署者钱包地址 (0x...): ');
    if (!isValidAddress(config.DEPLOYER_WALLET_ADDRESS)) {
      throw new Error('无效的部署者钱包地址格式');
    }

    config.PROJECT_WALLET_ADDRESS = await normalInput('项目钱包地址 (0x...): ');
    if (!isValidAddress(config.PROJECT_WALLET_ADDRESS)) {
      throw new Error('无效的项目钱包地址格式');
    }

    config.COMMUNITY_WALLET_ADDRESS = await normalInput('社区钱包地址 (0x...): ');
    if (!isValidAddress(config.COMMUNITY_WALLET_ADDRESS)) {
      throw new Error('无效的社区钱包地址格式');
    }

    config.TEST_WALLET_ADDRESS = await normalInput('测试钱包地址 (0x...): ');
    if (!isValidAddress(config.TEST_WALLET_ADDRESS)) {
      throw new Error('无效的测试钱包地址格式');
    }

    // 收集私钥信息（隐藏输入）
    console.log('\n🔑 2. 私钥信息（输入时会隐藏）：');
    
    config.DEPLOYER_PRIVATE_KEY = await hiddenInput('部署者钱包私钥: ');
    if (!isValidPrivateKey(config.DEPLOYER_PRIVATE_KEY)) {
      throw new Error('无效的部署者私钥格式（应为64位十六进制）');
    }

    config.PROJECT_WALLET_PRIVATE_KEY = await hiddenInput('项目钱包私钥: ');
    if (!isValidPrivateKey(config.PROJECT_WALLET_PRIVATE_KEY)) {
      throw new Error('无效的项目私钥格式（应为64位十六进制）');
    }

    config.COMMUNITY_WALLET_PRIVATE_KEY = await hiddenInput('社区钱包私钥: ');
    if (!isValidPrivateKey(config.COMMUNITY_WALLET_PRIVATE_KEY)) {
      throw new Error('无效的社区私钥格式（应为64位十六进制）');
    }

    config.TEST_WALLET_PRIVATE_KEY = await hiddenInput('测试钱包私钥: ');
    if (!isValidPrivateKey(config.TEST_WALLET_PRIVATE_KEY)) {
      throw new Error('无效的测试私钥格式（应为64位十六进制）');
    }

    // 读取模板文件
    const templatePath = path.join(process.cwd(), '.env.template');
    let envContent = fs.readFileSync(templatePath, 'utf8');

    // 替换占位符
    envContent = envContent.replace('请填入您的部署者钱包私钥_64位十六进制字符串', config.DEPLOYER_PRIVATE_KEY);
    envContent = envContent.replace('请填入您的部署者钱包地址_0x开头', config.DEPLOYER_WALLET_ADDRESS);
    envContent = envContent.replace('请填入您的项目钱包地址_0x开头', config.PROJECT_WALLET_ADDRESS);
    envContent = envContent.replace('请填入您的项目钱包私钥_64位十六进制字符串', config.PROJECT_WALLET_PRIVATE_KEY);
    envContent = envContent.replace('请填入您的社区钱包地址_0x开头', config.COMMUNITY_WALLET_ADDRESS);
    envContent = envContent.replace('请填入您的社区钱包私钥_64位十六进制字符串', config.COMMUNITY_WALLET_PRIVATE_KEY);
    envContent = envContent.replace('请填入您的测试钱包地址_0x开头', config.TEST_WALLET_ADDRESS);
    envContent = envContent.replace('请填入您的测试钱包私钥_64位十六进制字符串', config.TEST_WALLET_PRIVATE_KEY);
    envContent = envContent.replace('请填入开发用私钥_与DEPLOYER_PRIVATE_KEY相同', config.DEPLOYER_PRIVATE_KEY);
    envContent = envContent.replace('请填入钱包种子_用于开发环境', 'development_master_seed_change_in_production');

    // 保存.env.local文件
    const envPath = path.join(process.cwd(), '.env.local');
    fs.writeFileSync(envPath, envContent);

    console.log('\n✅ 配置完成！');
    console.log('=====================================');
    console.log('📄 .env.local 文件已创建');
    console.log('🔒 私钥信息已安全保存');
    console.log('⚠️  请勿将.env.local文件分享给他人');
    console.log('=====================================');

    // 验证配置
    console.log('\n🔍 配置验证：');
    console.log(`部署者地址: ${config.DEPLOYER_WALLET_ADDRESS}`);
    console.log(`项目地址: ${config.PROJECT_WALLET_ADDRESS}`);
    console.log(`社区地址: ${config.COMMUNITY_WALLET_ADDRESS}`);
    console.log(`测试地址: ${config.TEST_WALLET_ADDRESS}`);
    console.log('私钥: ****已隐藏****');

  } catch (error) {
    console.error('\n❌ 配置失败:', error.message);
    process.exit(1);
  } finally {
    rl.close();
  }
}

main();
