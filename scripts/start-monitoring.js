#!/usr/bin/env node

import PriceMonitoringService from '../services/PriceMonitoringService.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * HAOX价格监控服务启动脚本
 * 支持命令行参数和配置文件
 */

// 解析命令行参数
const args = process.argv.slice(2);
const options = {};

for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    switch (arg) {
        case '--help':
        case '-h':
            showHelp();
            process.exit(0);
            break;
        case '--config':
        case '-c':
            options.configFile = args[++i];
            break;
        case '--interval':
        case '-i':
            options.interval = parseInt(args[++i]) * 1000;
            break;
        case '--verbose':
        case '-v':
            options.verbose = true;
            break;
        case '--dry-run':
        case '-d':
            options.dryRun = true;
            break;
        case '--log-file':
        case '-l':
            options.logFile = args[++i];
            break;
        default:
            if (arg.startsWith('--')) {
                console.error(`未知参数: ${arg}`);
                process.exit(1);
            }
    }
}

// 显示帮助信息
function showHelp() {
    console.log(`
HAOX价格监控服务

用法: node start-monitoring.js [选项]

选项:
  -h, --help              显示帮助信息
  -c, --config <file>     指定配置文件路径
  -i, --interval <sec>    设置检查间隔（秒）
  -v, --verbose           详细输出模式
  -d, --dry-run           试运行模式（不执行实际交易）
  -l, --log-file <file>   指定日志文件路径

示例:
  node start-monitoring.js                    # 使用默认配置启动
  node start-monitoring.js -i 60 -v          # 60秒间隔，详细模式
  node start-monitoring.js -c config.json    # 使用自定义配置文件
  node start-monitoring.js -d                # 试运行模式
`);
}

// 加载配置文件
function loadConfig(configFile) {
    try {
        const configPath = path.resolve(configFile);
        if (!fs.existsSync(configPath)) {
            throw new Error(`配置文件不存在: ${configPath}`);
        }
        
        const configData = fs.readFileSync(configPath, 'utf8');
        return JSON.parse(configData);
    } catch (error) {
        console.error('❌ 配置文件加载失败:', error.message);
        process.exit(1);
    }
}

// 设置日志记录
function setupLogging(logFile) {
    if (!logFile) return;
    
    const logPath = path.resolve(logFile);
    const logDir = path.dirname(logPath);
    
    // 确保日志目录存在
    if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true });
    }
    
    // 重定向console.log到文件
    const originalLog = console.log;
    const originalError = console.error;
    
    const logStream = fs.createWriteStream(logPath, { flags: 'a' });
    
    console.log = (...args) => {
        const timestamp = new Date().toISOString();
        const message = `[${timestamp}] ${args.join(' ')}\n`;
        logStream.write(message);
        originalLog(...args);
    };
    
    console.error = (...args) => {
        const timestamp = new Date().toISOString();
        const message = `[${timestamp}] ERROR: ${args.join(' ')}\n`;
        logStream.write(message);
        originalError(...args);
    };
    
    console.log('📝 日志记录已启用:', logPath);
}

// 验证环境变量
function validateEnvironment() {
    const required = [
        'NEXT_PUBLIC_BSC_RPC_URL',
        'NEXT_PUBLIC_HAOX_VESTING_ADDRESS_V2_FIXED',
        'NEXT_PUBLIC_HAOX_PRICE_ORACLE_ADDRESS',
        'DEPLOYER_PRIVATE_KEY'
    ];
    
    const missing = required.filter(key => !process.env[key]);
    
    if (missing.length > 0) {
        console.error('❌ 缺少必要的环境变量:');
        missing.forEach(key => console.error(`   - ${key}`));
        console.error('\n请检查 .env.local 文件');
        process.exit(1);
    }
    
    console.log('✅ 环境变量验证通过');
}

// 显示启动信息
function showStartupInfo(config) {
    console.log('🚀 HAOX价格监控服务');
    console.log('==========================================');
    console.log('配置信息:');
    console.log(`  检查间隔: ${config.checkInterval / 1000}秒`);
    console.log(`  重试次数: ${config.retryAttempts}`);
    console.log(`  Gas限制: ${config.gasLimit.toLocaleString()}`);
    console.log(`  详细模式: ${options.verbose ? '开启' : '关闭'}`);
    console.log(`  试运行: ${options.dryRun ? '是' : '否'}`);
    console.log('合约信息:');
    console.log(`  Vesting: ${process.env.NEXT_PUBLIC_HAOX_VESTING_ADDRESS_V2_FIXED}`);
    console.log(`  Oracle: ${process.env.NEXT_PUBLIC_HAOX_PRICE_ORACLE_ADDRESS}`);
    console.log(`  RPC: ${process.env.NEXT_PUBLIC_BSC_RPC_URL}`);
    console.log('==========================================\n');
}

// 主函数
async function main() {
    try {
        console.log('🔧 初始化HAOX价格监控服务...\n');
        
        // 验证环境
        validateEnvironment();
        
        // 加载配置
        let config = {};
        if (options.configFile) {
            config = loadConfig(options.configFile);
            console.log('✅ 配置文件加载成功:', options.configFile);
        }
        
        // 应用命令行选项
        if (options.interval) {
            config.checkInterval = options.interval;
        }
        
        // 设置日志
        if (options.logFile) {
            setupLogging(options.logFile);
        }
        
        // 创建监控服务实例
        const monitor = new PriceMonitoringService();
        
        // 应用配置
        if (Object.keys(config).length > 0) {
            Object.assign(monitor.config, config);
        }
        
        // 显示启动信息
        showStartupInfo(monitor.config);
        
        // 试运行模式
        if (options.dryRun) {
            console.log('🧪 试运行模式 - 不会执行实际交易');
            
            // 验证合约连接
            await monitor.validateContracts();
            
            console.log('✅ 试运行完成，所有连接正常');
            process.exit(0);
        }
        
        // 设置优雅关闭
        const gracefulShutdown = (signal) => {
            console.log(`\n🛑 收到${signal}信号，正在关闭监控服务...`);
            monitor.stopMonitoring();
            
            setTimeout(() => {
                console.log('✅ 监控服务已安全关闭');
                process.exit(0);
            }, 1000);
        };
        
        process.on('SIGINT', () => gracefulShutdown('SIGINT'));
        process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
        
        // 启动监控服务
        await monitor.startMonitoring();
        
        // 定期输出状态（如果启用详细模式）
        if (options.verbose) {
            setInterval(() => {
                const status = monitor.getStatus();
                console.log('📊 服务状态:', {
                    运行中: status.isMonitoring,
                    错误次数: status.errorCount,
                    上次检查: new Date(status.lastSuccessfulCheck).toLocaleTimeString('zh-CN')
                });
            }, 300000); // 每5分钟输出一次状态
        }
        
        console.log('🎉 监控服务启动成功！');
        console.log('按 Ctrl+C 停止服务\n');
        
    } catch (error) {
        console.error('❌ 监控服务启动失败:', error.message);
        
        if (options.verbose) {
            console.error('详细错误信息:', error);
        }
        
        process.exit(1);
    }
}

// 未捕获异常处理
process.on('uncaughtException', (error) => {
    console.error('❌ 未捕获异常:', error);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ 未处理的Promise拒绝:', reason);
    process.exit(1);
});

// 启动服务
main();
