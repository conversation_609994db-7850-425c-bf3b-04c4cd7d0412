#!/usr/bin/env node

/**
 * 修复 window 对象相关错误的脚本
 * 主要解决：
 * 1. Cannot assign to read only property 'solana'
 * 2. Cannot redefine property: ethereum
 * 3. React 组件导入问题
 */

import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const projectRoot = process.cwd();

// 日志工具
function log(level, message) {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    reset: '\x1b[0m'
  };
  
  const color = colors[level] || colors.reset;
  console.log(`${color}${message}${colors.reset}`);
}

/**
 * 创建安全的 window 对象扩展
 */
async function createWindowExtensions() {
  log('blue', '🔧 创建安全的 window 对象扩展...');
  
  const windowExtensionContent = `/**
 * 安全的 window 对象扩展
 * 解决 Cannot assign to read only property 错误
 */

// 安全地扩展 window 对象
export function safeExtendWindow() {
  if (typeof window === 'undefined') {
    return; // 服务器端渲染时跳过
  }

  try {
    // 安全地设置 ethereum 属性
    if (!window.ethereum) {
      Object.defineProperty(window, 'ethereum', {
        value: undefined,
        writable: true,
        configurable: true
      });
    }

    // 安全地设置 solana 属性
    if (!window.solana) {
      Object.defineProperty(window, 'solana', {
        value: undefined,
        writable: true,
        configurable: true
      });
    }

    // 检测钱包
    detectWallets();
  } catch (error) {
    console.warn('Window 对象扩展失败:', error);
  }
}

/**
 * 检测可用的钱包
 */
function detectWallets() {
  // 检测 MetaMask
  if (window.ethereum?.isMetaMask) {
    console.log('✅ 检测到 MetaMask 钱包');
  }

  // 检测 Phantom (Solana)
  if (window.solana?.isPhantom) {
    console.log('✅ 检测到 Phantom 钱包');
  }

  // 检测其他钱包
  if (window.ethereum && !window.ethereum.isMetaMask) {
    console.log('✅ 检测到其他以太坊钱包');
  }
}

/**
 * 安全地获取钱包实例
 */
export function getEthereumWallet() {
  if (typeof window === 'undefined' || !window.ethereum) {
    return null;
  }
  return window.ethereum;
}

export function getSolanaWallet() {
  if (typeof window === 'undefined' || !window.solana) {
    return null;
  }
  return window.solana;
}

// 在客户端自动初始化
if (typeof window !== 'undefined') {
  // 延迟执行，确保所有脚本加载完成
  setTimeout(safeExtendWindow, 100);
}
`;

  const windowExtensionPath = path.join(projectRoot, 'src/lib/window-extensions.ts');
  await fs.writeFile(windowExtensionPath, windowExtensionContent, 'utf-8');
  log('green', '✅ 创建 window 扩展文件: src/lib/window-extensions.ts');
}

/**
 * 修复组件导入问题
 */
async function fixComponentImports() {
  log('blue', '🔧 检查组件导入问题...');
  
  const filesToCheck = [
    'src/app/page.tsx',
    'src/app/layout.tsx',
    'src/components/ui/index.ts'
  ];

  for (const filePath of filesToCheck) {
    const fullPath = path.join(projectRoot, filePath);
    
    try {
      const content = await fs.readFile(fullPath, 'utf-8');
      
      // 检查是否有问题的导入
      const problematicPatterns = [
        /export\s+\{\s*default\s+as\s+\w+,.*\}\s+from/g,
        /import\s+\{[^}]*default[^}]*\}/g,
      ];

      let hasIssues = false;
      for (const pattern of problematicPatterns) {
        if (pattern.test(content)) {
          hasIssues = true;
          log('yellow', `⚠️ ${filePath} 可能存在导入问题`);
          break;
        }
      }

      if (!hasIssues) {
        log('green', `✅ ${filePath} 导入正常`);
      }
    } catch (error) {
      log('yellow', `⚠️ 无法检查 ${filePath}: ${error.message}`);
    }
  }
}

/**
 * 创建客户端组件包装器
 */
async function createClientWrapper() {
  log('blue', '🔧 创建客户端组件包装器...');
  
  const clientWrapperContent = `'use client';

/**
 * 客户端组件包装器
 * 确保组件只在客户端渲染，避免 SSR 相关问题
 */

import React, { useEffect, useState } from 'react';

interface ClientOnlyProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export function ClientOnly({ children, fallback = null }: ClientOnlyProps) {
  const [hasMounted, setHasMounted] = useState(false);

  useEffect(() => {
    setHasMounted(true);
  }, []);

  if (!hasMounted) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

export default ClientOnly;
`;

  const clientWrapperPath = path.join(projectRoot, 'src/components/ClientOnly.tsx');
  await fs.writeFile(clientWrapperPath, clientWrapperContent, 'utf-8');
  log('green', '✅ 创建客户端包装器: src/components/ClientOnly.tsx');
}

/**
 * 更新 layout.tsx 以包含 window 扩展
 */
async function updateLayout() {
  log('blue', '🔧 更新 layout.tsx...');
  
  const layoutPath = path.join(projectRoot, 'src/app/layout.tsx');
  
  try {
    const content = await fs.readFile(layoutPath, 'utf-8');
    
    // 检查是否已经包含 window 扩展
    if (content.includes('window-extensions')) {
      log('green', '✅ layout.tsx 已包含 window 扩展');
      return;
    }

    // 添加导入
    const importLine = "import { safeExtendWindow } from '@/lib/window-extensions';";
    const useEffectCode = `
  useEffect(() => {
    // 安全地扩展 window 对象
    safeExtendWindow();
  }, []);`;

    let updatedContent = content;

    // 添加导入
    if (!content.includes(importLine)) {
      const importIndex = content.indexOf('import "./globals.css";');
      if (importIndex !== -1) {
        const insertIndex = content.indexOf('\n', importIndex) + 1;
        updatedContent = content.slice(0, insertIndex) + importLine + '\n' + content.slice(insertIndex);
      }
    }

    // 添加 useEffect
    if (!content.includes('useEffect')) {
      // 添加 React import
      if (!updatedContent.includes('import React')) {
        updatedContent = updatedContent.replace(
          'import type { Metadata, Viewport } from "next";',
          'import React, { useEffect } from "react";\nimport type { Metadata, Viewport } from "next";'
        );
      }

      // 在 RootLayout 函数中添加 useEffect
      const functionStart = updatedContent.indexOf('export default function RootLayout(');
      if (functionStart !== -1) {
        const bodyStart = updatedContent.indexOf('{', functionStart) + 1;
        const insertIndex = updatedContent.indexOf('\n', bodyStart) + 1;
        updatedContent = updatedContent.slice(0, insertIndex) + useEffectCode + '\n' + updatedContent.slice(insertIndex);
      }
    }

    if (updatedContent !== content) {
      await fs.writeFile(layoutPath, updatedContent, 'utf-8');
      log('green', '✅ 更新 layout.tsx 完成');
    }
  } catch (error) {
    log('red', `❌ 更新 layout.tsx 失败: ${error.message}`);
  }
}

/**
 * 主函数
 */
async function main() {
  log('blue', '🚀 开始修复 window 对象相关错误...');
  
  try {
    await createWindowExtensions();
    await createClientWrapper();
    await fixComponentImports();
    await updateLayout();
    
    log('green', '✅ 修复完成！');
    log('blue', '📝 建议重启开发服务器以应用更改');
    
  } catch (error) {
    log('red', `❌ 修复过程中出现错误: ${error.message}`);
    process.exit(1);
  }
}

// 运行脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export {
  createWindowExtensions,
  createClientWrapper,
  fixComponentImports,
  updateLayout
};
