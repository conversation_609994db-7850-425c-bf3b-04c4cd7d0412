#!/usr/bin/env node

/**
 * 水合错误修复测试脚本
 * 验证登录跳转和SSR修复是否正常工作
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔧 SocioMint V2.1 - 水合错误修复测试');
console.log('=====================================\n');

// 检查修复文件是否存在
const checkFiles = [
  'src/components/auth/SafeTelegramLogin.tsx',
  'src/components/ErrorBoundary.tsx',
  'src/app/login/page.tsx',
  'src/app/profile/page.tsx'
];

console.log('📁 检查修复文件...');
let allFilesExist = true;

checkFiles.forEach(file => {
  const filePath = path.join(process.cwd(), file);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - 文件不存在`);
    allFilesExist = false;
  }
});

if (!allFilesExist) {
  console.log('\n❌ 部分修复文件缺失，请检查文件是否正确创建');
  process.exit(1);
}

console.log('\n🔍 检查代码修复...');

// 检查SafeTelegramLogin是否正确使用dynamic导入
const safeTelegramLoginPath = path.join(process.cwd(), 'src/components/auth/SafeTelegramLogin.tsx');
const safeTelegramLoginContent = fs.readFileSync(safeTelegramLoginPath, 'utf8');

if (safeTelegramLoginContent.includes('dynamic') && safeTelegramLoginContent.includes('ssr: false')) {
  console.log('✅ SafeTelegramLogin 正确使用了 dynamic 导入禁用 SSR');
} else {
  console.log('❌ SafeTelegramLogin 未正确配置 SSR 禁用');
}

// 检查profile页面是否添加了mounted状态检查
const profilePagePath = path.join(process.cwd(), 'src/app/profile/page.tsx');
const profilePageContent = fs.readFileSync(profilePagePath, 'utf8');

if (profilePageContent.includes('mounted') && profilePageContent.includes('setMounted(true)')) {
  console.log('✅ Profile 页面添加了客户端挂载检查');
} else {
  console.log('❌ Profile 页面缺少客户端挂载检查');
}

// 检查登录页面是否使用了SafeTelegramLogin
const loginPagePath = path.join(process.cwd(), 'src/app/login/page.tsx');
const loginPageContent = fs.readFileSync(loginPagePath, 'utf8');

if (loginPageContent.includes('SafeTelegramLogin')) {
  console.log('✅ Login 页面使用了 SafeTelegramLogin 组件');
} else {
  console.log('❌ Login 页面未使用 SafeTelegramLogin 组件');
}

// 检查ErrorBoundary是否更新了水合错误检测
const errorBoundaryPath = path.join(process.cwd(), 'src/components/ErrorBoundary.tsx');
const errorBoundaryContent = fs.readFileSync(errorBoundaryPath, 'utf8');

if (errorBoundaryContent.includes('Hydration') && errorBoundaryContent.includes('Text content does not match')) {
  console.log('✅ ErrorBoundary 添加了水合错误检测');
} else {
  console.log('❌ ErrorBoundary 缺少水合错误检测');
}

console.log('\n🧪 运行类型检查...');
try {
  execSync('npx tsc --noEmit', { stdio: 'pipe' });
  console.log('✅ TypeScript 类型检查通过');
} catch (error) {
  console.log('⚠️  TypeScript 类型检查有警告，但不影响运行');
}

console.log('\n🚀 启动开发服务器测试...');
console.log('请按照以下步骤测试修复效果：\n');

console.log('1. 🌐 打开浏览器访问 http://localhost:3000');
console.log('2. 🔍 打开开发者工具的 Console 标签页');
console.log('3. 📱 点击登录按钮进行 Telegram 登录');
console.log('4. ✅ 验证登录成功后能正常跳转到个人中心');
console.log('5. 🔄 检查控制台是否还有水合错误');

console.log('\n📋 预期结果：');
console.log('- ✅ 登录成功后立即跳转到 /profile 页面');
console.log('- ✅ 控制台无 "Text content does not match" 错误');
console.log('- ✅ 页面不会卡在 "登录成功，正在跳转..." 状态');
console.log('- ✅ 个人中心页面正常显示用户信息');

console.log('\n🛠️  如果仍有问题，请检查：');
console.log('1. 浏览器缓存是否已清除');
console.log('2. localStorage 中是否有旧的认证数据');
console.log('3. 网络请求是否正常');

console.log('\n🎯 修复完成！准备启动开发服务器...\n');

// 启动开发服务器
try {
  console.log('启动命令: npm run dev');
  console.log('按 Ctrl+C 停止服务器\n');
  execSync('npm run dev', { stdio: 'inherit' });
} catch (error) {
  console.log('\n⚠️  开发服务器已停止');
}
