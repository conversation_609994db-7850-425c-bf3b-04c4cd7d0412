#!/bin/bash

# HAOX安全密钥设置脚本
# 用于初始化加密密钥存储系统

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装，请先安装 Node.js"
        exit 1
    fi
    
    # 检查npm
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装，请先安装 npm"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 创建密钥目录
create_key_directory() {
    log_info "创建密钥目录..."
    
    KEY_DIR=".keys"
    
    if [ -d "$KEY_DIR" ]; then
        log_warning "密钥目录已存在: $KEY_DIR"
        read -p "是否要删除现有目录并重新创建? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            rm -rf "$KEY_DIR"
            log_info "已删除现有密钥目录"
        else
            log_info "保留现有密钥目录"
        fi
    fi
    
    if [ ! -d "$KEY_DIR" ]; then
        mkdir -p "$KEY_DIR"
        chmod 700 "$KEY_DIR"
        log_success "密钥目录创建成功: $KEY_DIR"
    fi
}

# 检查环境变量
check_environment() {
    log_info "检查环境变量..."
    
    # 检查必要的环境变量
    if [ -z "$DEPLOYER_PRIVATE_KEY" ]; then
        log_error "DEPLOYER_PRIVATE_KEY 环境变量未设置"
        log_info "请在 .env.local 文件中设置 DEPLOYER_PRIVATE_KEY"
        exit 1
    fi
    
    # 验证私钥格式
    if [[ ! $DEPLOYER_PRIVATE_KEY =~ ^0x[a-fA-F0-9]{64}$ ]]; then
        log_error "DEPLOYER_PRIVATE_KEY 格式无效，应为64位十六进制字符串（以0x开头）"
        exit 1
    fi
    
    log_success "环境变量检查通过"
}

# 获取主密码
get_master_password() {
    log_info "设置主密码..."
    
    # 检查是否已设置环境变量中的密码
    if [ -n "$MASTER_PASSWORD" ]; then
        log_info "使用环境变量中的主密码"
        MASTER_PASS="$MASTER_PASSWORD"
        return
    fi
    
    # 交互式输入密码
    while true; do
        echo -n "请输入主密码（至少12个字符）: "
        read -s MASTER_PASS
        echo
        
        if [ ${#MASTER_PASS} -lt 12 ]; then
            log_error "密码长度至少需要12个字符"
            continue
        fi
        
        echo -n "请再次输入主密码确认: "
        read -s MASTER_PASS_CONFIRM
        echo
        
        if [ "$MASTER_PASS" != "$MASTER_PASS_CONFIRM" ]; then
            log_error "两次输入的密码不一致"
            continue
        fi
        
        break
    done
    
    log_success "主密码设置完成"
}

# 生成加密密钥
generate_encrypted_keys() {
    log_info "生成加密密钥..."
    
    # 创建临时的Node.js脚本
    cat > temp_key_setup.js << 'EOF'
import SecureKeyManager from './utils/SecureKeyManager.js';

async function setupKeys() {
    try {
        const keyManager = new SecureKeyManager();
        
        const password = process.argv[2];
        const privateKey = process.argv[3];
        const keyId = process.argv[4] || 'monitoring-service';
        
        if (!password || !privateKey) {
            throw new Error('Password and private key are required');
        }
        
        console.log('🔧 生成主密钥...');
        keyManager.generateMasterKey(password);
        
        console.log('🔐 加密私钥...');
        keyManager.encryptPrivateKey(privateKey, password, keyId);
        
        console.log('✅ 密钥设置完成');
        
        // 验证密钥完整性
        console.log('🔍 验证密钥完整性...');
        const isValid = keyManager.validateKeyIntegrity(password);
        
        if (isValid) {
            console.log('✅ 密钥完整性验证通过');
        } else {
            throw new Error('密钥完整性验证失败');
        }
        
        // 显示统计信息
        const stats = keyManager.getKeyStatistics();
        console.log('📊 密钥统计信息:');
        console.log(`   当前版本: v${stats.currentVersion}`);
        console.log(`   加密密钥数量: ${stats.totalEncryptedKeys}`);
        console.log(`   密钥ID: ${stats.encryptedKeys.join(', ')}`);
        
    } catch (error) {
        console.error('❌ 密钥设置失败:', error.message);
        process.exit(1);
    }
}

setupKeys();
EOF
    
    # 执行密钥生成
    node temp_key_setup.js "$MASTER_PASS" "$DEPLOYER_PRIVATE_KEY" "monitoring-service"
    
    # 清理临时文件
    rm temp_key_setup.js
    
    log_success "加密密钥生成完成"
}

# 更新环境变量文件
update_env_file() {
    log_info "更新环境变量文件..."
    
    ENV_FILE=".env.local"
    
    if [ ! -f "$ENV_FILE" ]; then
        log_warning "环境变量文件不存在: $ENV_FILE"
        return
    fi
    
    # 备份原文件
    cp "$ENV_FILE" "${ENV_FILE}.backup.$(date +%Y%m%d_%H%M%S)"
    log_info "已备份环境变量文件"
    
    # 删除明文私钥
    if grep -q "DEPLOYER_PRIVATE_KEY=" "$ENV_FILE"; then
        sed -i.bak '/^DEPLOYER_PRIVATE_KEY=/d' "$ENV_FILE"
        log_success "已从环境变量文件中删除明文私钥"
    fi
    
    # 添加主密码（如果不存在）
    if ! grep -q "MASTER_PASSWORD=" "$ENV_FILE"; then
        echo "" >> "$ENV_FILE"
        echo "# 密钥管理" >> "$ENV_FILE"
        echo "MASTER_PASSWORD=$MASTER_PASS" >> "$ENV_FILE"
        log_success "已添加主密码到环境变量文件"
    fi
    
    # 添加密钥管理配置
    if ! grep -q "SECURE_KEY_ENABLED=" "$ENV_FILE"; then
        echo "SECURE_KEY_ENABLED=true" >> "$ENV_FILE"
        echo "KEY_ROTATION_DAYS=90" >> "$ENV_FILE"
        log_success "已添加密钥管理配置"
    fi
}

# 设置文件权限
set_permissions() {
    log_info "设置文件权限..."
    
    # 设置密钥目录权限
    chmod 700 .keys
    
    # 设置密钥文件权限
    if [ -d ".keys" ]; then
        find .keys -type f -exec chmod 600 {} \;
        log_success "密钥文件权限设置完成"
    fi
    
    # 设置环境变量文件权限
    if [ -f ".env.local" ]; then
        chmod 600 .env.local
        log_success "环境变量文件权限设置完成"
    fi
}

# 创建备份
create_backup() {
    log_info "创建密钥备份..."
    
    BACKUP_DIR="backups/keys_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # 复制密钥文件
    if [ -d ".keys" ]; then
        cp -r .keys/* "$BACKUP_DIR/"
        chmod 700 "$BACKUP_DIR"
        find "$BACKUP_DIR" -type f -exec chmod 600 {} \;
        log_success "密钥备份创建完成: $BACKUP_DIR"
    fi
}

# 验证设置
verify_setup() {
    log_info "验证设置..."
    
    # 创建验证脚本
    cat > temp_verify.js << 'EOF'
import SecureKeyManager from './utils/SecureKeyManager.js';

async function verify() {
    try {
        const keyManager = new SecureKeyManager();
        
        // 检查密钥文件
        if (!keyManager.hasEncryptedKey('monitoring-service')) {
            throw new Error('加密密钥文件不存在');
        }
        
        // 尝试获取密码
        const password = await keyManager.getPasswordFromSources();
        
        // 验证解密
        const privateKey = keyManager.decryptPrivateKey(password, 'monitoring-service');
        
        if (!privateKey.startsWith('0x') || privateKey.length !== 66) {
            throw new Error('解密后的私钥格式无效');
        }
        
        console.log('✅ 设置验证通过');
        console.log('🔐 加密密钥可以正常解密');
        console.log('📊 系统已准备就绪');
        
    } catch (error) {
        console.error('❌ 验证失败:', error.message);
        process.exit(1);
    }
}

verify();
EOF
    
    # 执行验证
    node temp_verify.js
    
    # 清理临时文件
    rm temp_verify.js
    
    log_success "设置验证完成"
}

# 显示使用说明
show_usage_instructions() {
    log_info "使用说明"
    echo
    echo "🔐 安全密钥设置已完成！"
    echo
    echo "📁 密钥文件位置: .keys/"
    echo "🔑 主密码已设置在环境变量 MASTER_PASSWORD 中"
    echo
    echo "🚀 启动监控服务:"
    echo "   node services/PriceMonitoringServiceSecure.js"
    echo
    echo "🔄 手动密钥轮换:"
    echo "   node scripts/rotate-keys.js"
    echo
    echo "📊 查看密钥状态:"
    echo "   node scripts/key-status.js"
    echo
    echo "⚠️  安全提醒:"
    echo "   1. 定期备份 .keys/ 目录"
    echo "   2. 保护好主密码"
    echo "   3. 定期轮换密钥（建议90天）"
    echo "   4. 监控密钥访问日志"
    echo
}

# 主函数
main() {
    echo "🔐 HAOX安全密钥设置脚本"
    echo "=========================="
    echo
    
    # 检查是否以root用户运行
    if [ "$EUID" -eq 0 ]; then
        log_warning "不建议以root用户运行此脚本"
        read -p "是否继续? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    # 执行设置步骤
    check_dependencies
    create_key_directory
    check_environment
    get_master_password
    generate_encrypted_keys
    update_env_file
    set_permissions
    create_backup
    verify_setup
    show_usage_instructions
    
    log_success "安全密钥设置完成！"
}

# 错误处理
trap 'log_error "脚本执行过程中发生错误，请检查上面的错误信息"; exit 1' ERR

# 执行主函数
main "$@"
