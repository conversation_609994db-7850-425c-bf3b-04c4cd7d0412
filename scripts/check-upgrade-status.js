#!/usr/bin/env node

/**
 * SocioMint升级状态检查脚本
 * 检查当前部署版本并提供升级建议
 */

const fs = require('fs');
const path = require('path');

// 颜色定义
const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    reset: '\x1b[0m'
};

// 日志函数
const log = {
    info: (msg) => console.log(`${colors.blue}[INFO]${colors.reset} ${msg}`),
    success: (msg) => console.log(`${colors.green}[SUCCESS]${colors.reset} ${msg}`),
    warning: (msg) => console.log(`${colors.yellow}[WARNING]${colors.reset} ${msg}`),
    error: (msg) => console.log(`${colors.red}[ERROR]${colors.reset} ${msg}`),
    title: (msg) => console.log(`${colors.cyan}${msg}${colors.reset}`),
    highlight: (msg) => console.log(`${colors.magenta}${msg}${colors.reset}`)
};

// 检查结果
const checkResults = {
    currentVersion: 'Unknown',
    hasV2Contracts: false,
    hasV21SecurityContracts: false,
    hasSecurityFiles: false,
    securityLevel: 'Unknown',
    upgradeRecommendation: 'Unknown'
};

/**
 * 检查环境变量文件
 */
function checkEnvironmentFile() {
    log.info('检查环境变量配置...');
    
    const envFile = '.env.local';
    if (!fs.existsSync(envFile)) {
        log.warning('未找到 .env.local 文件');
        return false;
    }
    
    const envContent = fs.readFileSync(envFile, 'utf8');
    
    // 检查V2合约地址
    const v2Contracts = [
        'NEXT_PUBLIC_HAOX_TOKEN_ADDRESS',
        'NEXT_PUBLIC_HAOX_PRESALE_ADDRESS',
        'NEXT_PUBLIC_HAOX_VESTING_ADDRESS'
    ];
    
    const hasV2 = v2Contracts.every(contract => envContent.includes(contract));
    checkResults.hasV2Contracts = hasV2;
    
    // 检查V2.1安全合约地址
    const v21SecurityContracts = [
        'NEXT_PUBLIC_HAOX_VESTING_ADDRESS_V2_FIXED_SECURE',
        'NEXT_PUBLIC_HAOX_PRICE_AGGREGATOR_ADDRESS',
        'SECURE_KEY_ENABLED'
    ];
    
    const hasV21 = v21SecurityContracts.some(contract => envContent.includes(contract));
    checkResults.hasV21SecurityContracts = hasV21;
    
    // 确定当前版本
    if (hasV21) {
        checkResults.currentVersion = 'V2.1 Security';
        checkResults.securityLevel = 'Low Risk';
    } else if (hasV2) {
        checkResults.currentVersion = 'V2';
        checkResults.securityLevel = 'Medium Risk';
    } else {
        checkResults.currentVersion = 'V1 or Unknown';
        checkResults.securityLevel = 'High Risk';
    }
    
    log.success('环境变量检查完成');
    return true;
}

/**
 * 检查安全文件
 */
function checkSecurityFiles() {
    log.info('检查安全修复文件...');
    
    const securityFiles = [
        'utils/SecureKeyManager.js',
        'services/PriceMonitoringServiceSecure.js',
        'hooks/useSecureWallet.js',
        'components/AdminPanelSecure.jsx',
        'contracts/contracts/HAOXVestingV2FixedSecure.sol',
        'contracts/contracts/HAOXPriceAggregatorV2.sol',
        'scripts/setup-secure-keys.sh'
    ];
    
    let foundFiles = 0;
    securityFiles.forEach(file => {
        if (fs.existsSync(file)) {
            foundFiles++;
            log.success(`✅ ${file}`);
        } else {
            log.warning(`❌ ${file}`);
        }
    });
    
    checkResults.hasSecurityFiles = foundFiles >= securityFiles.length * 0.8; // 80%的文件存在
    
    if (checkResults.hasSecurityFiles) {
        log.success('安全文件检查通过');
    } else {
        log.warning('部分安全文件缺失');
    }
    
    return checkResults.hasSecurityFiles;
}

/**
 * 检查合约文件
 */
function checkContractFiles() {
    log.info('检查智能合约文件...');
    
    const contractsDir = 'contracts/contracts';
    if (!fs.existsSync(contractsDir)) {
        log.error('合约目录不存在');
        return false;
    }
    
    const contractFiles = fs.readdirSync(contractsDir);
    
    // 检查V2合约
    const v2Contracts = contractFiles.filter(file => file.includes('V2') && !file.includes('V2.1'));
    log.info(`发现 ${v2Contracts.length} 个V2合约文件`);
    
    // 检查V2.1安全合约
    const v21Contracts = contractFiles.filter(file => 
        file.includes('V2FixedSecure') || 
        file.includes('V2Optimized') || 
        file.includes('AggregatorV2')
    );
    log.info(`发现 ${v21Contracts.length} 个V2.1安全合约文件`);
    
    if (v21Contracts.length > 0) {
        checkResults.hasV21SecurityContracts = true;
    }
    
    return true;
}

/**
 * 生成升级建议
 */
function generateUpgradeRecommendation() {
    log.info('生成升级建议...');
    
    if (checkResults.currentVersion === 'V2.1 Security') {
        checkResults.upgradeRecommendation = 'CURRENT';
    } else if (checkResults.currentVersion === 'V2') {
        checkResults.upgradeRecommendation = 'RECOMMENDED';
    } else {
        checkResults.upgradeRecommendation = 'REQUIRED';
    }
}

/**
 * 显示检查结果
 */
function displayResults() {
    console.log('\n' + '='.repeat(60));
    log.title('🔍 SocioMint 升级状态检查报告');
    console.log('='.repeat(60));
    
    console.log(`\n📊 当前状态:`);
    console.log(`   版本: ${checkResults.currentVersion}`);
    console.log(`   安全等级: ${checkResults.securityLevel}`);
    console.log(`   V2合约: ${checkResults.hasV2Contracts ? '✅ 已部署' : '❌ 未部署'}`);
    console.log(`   V2.1安全合约: ${checkResults.hasV21SecurityContracts ? '✅ 已部署' : '❌ 未部署'}`);
    console.log(`   安全文件: ${checkResults.hasSecurityFiles ? '✅ 完整' : '❌ 缺失'}`);
    
    console.log(`\n🎯 升级建议:`);
    
    switch (checkResults.upgradeRecommendation) {
        case 'CURRENT':
            log.success('🎉 您已使用最新的V2.1安全版本！');
            console.log('   ✅ 系统安全等级: 低风险');
            console.log('   ✅ 已修复5个中危漏洞');
            console.log('   ✅ 满足主网部署要求');
            break;
            
        case 'RECOMMENDED':
            log.warning('⚠️  强烈建议升级到V2.1安全版本');
            console.log('   🔄 当前版本: V2 (中等风险)');
            console.log('   🎯 目标版本: V2.1 (低风险)');
            console.log('   📋 升级文档: docs/STAGING-DEPLOYMENT-V2.1-SECURE.md');
            console.log('\n   升级收益:');
            console.log('   ✅ 修复5个中危安全漏洞');
            console.log('   ✅ 企业级密钥管理系统');
            console.log('   ✅ 多预言机价格聚合');
            console.log('   ✅ 带时间锁的紧急提取');
            console.log('   ✅ 前端安全连接模式');
            console.log('   ✅ 存储和Gas优化');
            break;
            
        case 'REQUIRED':
            log.error('🚨 必须升级！当前版本存在安全风险');
            console.log('   ❌ 当前版本: V1或未知 (高风险)');
            console.log('   🎯 目标版本: V2.1安全版本 (低风险)');
            console.log('   📋 部署文档: docs/STAGING-DEPLOYMENT-V2.1-SECURE.md');
            break;
    }
    
    console.log(`\n🚀 快速升级命令:`);
    if (checkResults.upgradeRecommendation !== 'CURRENT') {
        console.log('   # 1. 备份当前配置');
        console.log('   cp .env.local .env.backup');
        console.log('');
        console.log('   # 2. 设置安全密钥管理');
        console.log('   chmod +x scripts/setup-secure-keys.sh');
        console.log('   ./scripts/setup-secure-keys.sh');
        console.log('');
        console.log('   # 3. 部署安全合约');
        console.log('   cd contracts');
        console.log('   npx hardhat run scripts/deploy-secure-vesting.js --network bscTestnet');
        console.log('');
        console.log('   # 4. 验证升级');
        console.log('   npx hardhat run scripts/test-security-fixes.js --network bscTestnet');
    } else {
        console.log('   # 系统已是最新版本，无需升级');
        console.log('   # 可以进行主网部署准备');
        console.log('   npx hardhat run scripts/test-security-fixes.js --network bscTestnet');
    }
    
    console.log(`\n📞 技术支持:`);
    console.log('   📧 技术团队: <EMAIL>');
    console.log('   🛡️ 安全团队: <EMAIL>');
    console.log('   📚 文档: docs/STAGING-DEPLOYMENT-V2.1-SECURE.md');
    
    console.log('\n' + '='.repeat(60));
}

/**
 * 主函数
 */
async function main() {
    try {
        log.title('🔍 SocioMint 升级状态检查工具');
        console.log('检查当前部署版本并提供升级建议...\n');
        
        // 执行检查
        checkEnvironmentFile();
        checkSecurityFiles();
        checkContractFiles();
        generateUpgradeRecommendation();
        
        // 显示结果
        displayResults();
        
        // 退出码
        if (checkResults.upgradeRecommendation === 'REQUIRED') {
            process.exit(2); // 需要升级
        } else if (checkResults.upgradeRecommendation === 'RECOMMENDED') {
            process.exit(1); // 建议升级
        } else {
            process.exit(0); // 当前版本
        }
        
    } catch (error) {
        log.error(`检查过程中发生错误: ${error.message}`);
        process.exit(3);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = { main, checkResults };
