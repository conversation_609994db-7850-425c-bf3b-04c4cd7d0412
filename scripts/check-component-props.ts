#!/usr/bin/env ts-node

/**
 * 组件属性一致性检查工具
 * 检查TypeScript接口定义与组件实现是否匹配
 */

import * as fs from 'fs';
import * as path from 'path';

interface ComponentCheck {
  componentName: string;
  filePath: string;
  interfaceName: string;
  declaredProps: string[];
  implementedProps: string[];
  missingProps: string[];
  unusedProps: string[];
}

class ComponentPropsChecker {
  private componentsDir = 'src/components/ui';
  private typesFile = 'src/types/index.ts';
  
  async checkAllComponents(): Promise<ComponentCheck[]> {
    const results: ComponentCheck[] = [];
    
    // 读取类型定义
    const typeDefinitions = this.parseTypeDefinitions();
    
    // 检查每个组件
    const componentFiles = fs.readdirSync(this.componentsDir)
      .filter(file => file.endsWith('.tsx') && !file.includes('index'));
    
    for (const file of componentFiles) {
      const componentName = path.basename(file, '.tsx');
      const interfaceName = `${componentName}Props`;
      
      if (typeDefinitions[interfaceName]) {
        const check = await this.checkComponent(
          componentName,
          path.join(this.componentsDir, file),
          interfaceName,
          typeDefinitions[interfaceName]
        );
        results.push(check);
      }
    }
    
    return results;
  }
  
  private parseTypeDefinitions(): Record<string, string[]> {
    const content = fs.readFileSync(this.typesFile, 'utf-8');
    const interfaces: Record<string, string[]> = {};
    
    // 简单的正则解析（实际项目中应使用AST）
    const interfaceRegex = /export interface (\w+Props)\s*{([^}]+)}/g;
    let match;
    
    while ((match = interfaceRegex.exec(content)) !== null) {
      const interfaceName = match[1];
      const propsContent = match[2];
      
      // 提取属性名
      const propRegex = /(\w+)\??:/g;
      const props: string[] = [];
      let propMatch;
      
      while ((propMatch = propRegex.exec(propsContent)) !== null) {
        props.push(propMatch[1]);
      }
      
      interfaces[interfaceName] = props;
    }
    
    return interfaces;
  }
  
  private async checkComponent(
    componentName: string,
    filePath: string,
    interfaceName: string,
    declaredProps: string[]
  ): Promise<ComponentCheck> {
    const content = fs.readFileSync(filePath, 'utf-8');
    
    // 提取组件实现中使用的属性
    const implementedProps = this.extractImplementedProps(content);
    
    // 计算差异
    const missingProps = declaredProps.filter(prop => 
      !implementedProps.includes(prop) && prop !== 'children'
    );
    
    const unusedProps = implementedProps.filter(prop => 
      !declaredProps.includes(prop) && !['children', 'className'].includes(prop)
    );
    
    return {
      componentName,
      filePath,
      interfaceName,
      declaredProps,
      implementedProps,
      missingProps,
      unusedProps
    };
  }
  
  private extractImplementedProps(content: string): string[] {
    const props: string[] = [];
    
    // 提取解构赋值中的属性
    const destructuringRegex = /{\s*([^}]+)\s*}/g;
    const match = destructuringRegex.exec(content);
    
    if (match) {
      const propsString = match[1];
      const propNames = propsString
        .split(',')
        .map(prop => prop.trim().split(/[=\s]/)[0])
        .filter(prop => prop && !prop.includes('...'));
      
      props.push(...propNames);
    }
    
    // 提取props.xxx的使用
    const propsUsageRegex = /props\.(\w+)/g;
    let usageMatch;
    
    while ((usageMatch = propsUsageRegex.exec(content)) !== null) {
      if (!props.includes(usageMatch[1])) {
        props.push(usageMatch[1]);
      }
    }
    
    return props;
  }
  
  generateReport(checks: ComponentCheck[]): string {
    let report = '# 组件属性一致性检查报告\n\n';
    
    const problemComponents = checks.filter(check => 
      check.missingProps.length > 0 || check.unusedProps.length > 0
    );
    
    if (problemComponents.length === 0) {
      report += '✅ 所有组件的属性定义与实现完全一致！\n';
      return report;
    }
    
    report += `🚨 发现 ${problemComponents.length} 个组件存在属性不一致问题：\n\n`;
    
    for (const check of problemComponents) {
      report += `## ${check.componentName}\n`;
      report += `- 接口：${check.interfaceName}\n`;
      report += `- 文件：${check.filePath}\n\n`;
      
      if (check.missingProps.length > 0) {
        report += `❌ **未实现的属性** (${check.missingProps.length})：\n`;
        check.missingProps.forEach(prop => {
          report += `  - \`${prop}\`\n`;
        });
        report += '\n';
      }
      
      if (check.unusedProps.length > 0) {
        report += `⚠️ **未声明的属性** (${check.unusedProps.length})：\n`;
        check.unusedProps.forEach(prop => {
          report += `  - \`${prop}\`\n`;
        });
        report += '\n';
      }
      
      report += '---\n\n';
    }
    
    return report;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const checker = new ComponentPropsChecker();
  checker.checkAllComponents().then(results => {
    const report = checker.generateReport(results);
    console.log(report);
    
    // 保存报告到文件
    fs.writeFileSync('component-props-report.md', report);
    console.log('\n📄 报告已保存到 component-props-report.md');
  });
}

export default ComponentPropsChecker;
