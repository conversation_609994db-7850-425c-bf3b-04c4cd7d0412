#!/usr/bin/env node

/**
 * Staging环境测试脚本
 * 自动化测试staging环境的功能和性能
 */

import { readFile } from 'fs/promises';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

// 颜色定义
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 测试配置
const STAGING_CONFIG = {
  baseUrl: 'https://staging.sociomint.app',
  apiUrl: 'https://staging.sociomint.app/api',
  timeout: 30000,
  retries: 3,
};

// 测试用例定义
const TEST_CASES = [
  {
    name: '健康检查',
    category: 'system',
    priority: 'critical',
    test: testHealthCheck,
  },
  {
    name: '页面可访问性',
    category: 'frontend',
    priority: 'critical',
    test: testPageAccessibility,
  },
  {
    name: 'API端点测试',
    category: 'api',
    priority: 'critical',
    test: testApiEndpoints,
  },
  {
    name: '认证流程测试',
    category: 'auth',
    priority: 'high',
    test: testAuthFlow,
  },
  {
    name: '钱包功能测试',
    category: 'wallet',
    priority: 'high',
    test: testWalletFunctionality,
  },
  {
    name: '性能测试',
    category: 'performance',
    priority: 'medium',
    test: testPerformance,
  },
  {
    name: '安全测试',
    category: 'security',
    priority: 'high',
    test: testSecurity,
  },
  {
    name: '数据库连接测试',
    category: 'database',
    priority: 'critical',
    test: testDatabaseConnection,
  },
];

/**
 * HTTP请求工具函数
 */
async function makeRequest(url, options = {}) {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), STAGING_CONFIG.timeout);

  try {
    const response = await fetch(url, {
      ...options,
      signal: controller.signal,
      headers: {
        'User-Agent': 'SocioMint-Staging-Test/1.0',
        'Accept': 'application/json',
        ...options.headers,
      },
    });

    clearTimeout(timeoutId);
    return response;
  } catch (error) {
    clearTimeout(timeoutId);
    throw error;
  }
}

/**
 * 测试1: 健康检查
 */
async function testHealthCheck() {
  const results = [];

  try {
    const response = await makeRequest(`${STAGING_CONFIG.apiUrl}/health`);
    
    if (response.ok) {
      const data = await response.json();
      results.push({
        test: '健康检查API',
        status: 'pass',
        message: `响应时间: ${response.headers.get('x-response-time') || 'N/A'}`,
        data: data,
      });
    } else {
      results.push({
        test: '健康检查API',
        status: 'fail',
        message: `HTTP ${response.status}: ${response.statusText}`,
      });
    }
  } catch (error) {
    results.push({
      test: '健康检查API',
      status: 'fail',
      message: error.message,
    });
  }

  return results;
}

/**
 * 测试2: 页面可访问性
 */
async function testPageAccessibility() {
  const results = [];
  const pages = [
    { path: '/', name: '首页' },
    { path: '/login', name: '登录页' },
    { path: '/dashboard', name: '仪表板' },
    { path: '/wallet', name: '钱包页' },
    { path: '/tasks', name: '任务页' },
    { path: '/presale', name: '预售页' },
  ];

  for (const page of pages) {
    try {
      const response = await makeRequest(`${STAGING_CONFIG.baseUrl}${page.path}`);
      
      if (response.ok) {
        const html = await response.text();
        const hasTitle = html.includes('<title>');
        const hasMetaViewport = html.includes('name="viewport"');
        
        results.push({
          test: `${page.name}可访问性`,
          status: hasTitle && hasMetaViewport ? 'pass' : 'warn',
          message: `状态码: ${response.status}, 标题: ${hasTitle ? '✓' : '✗'}, 视口: ${hasMetaViewport ? '✓' : '✗'}`,
        });
      } else {
        results.push({
          test: `${page.name}可访问性`,
          status: 'fail',
          message: `HTTP ${response.status}: ${response.statusText}`,
        });
      }
    } catch (error) {
      results.push({
        test: `${page.name}可访问性`,
        status: 'fail',
        message: error.message,
      });
    }
  }

  return results;
}

/**
 * 测试3: API端点测试
 */
async function testApiEndpoints() {
  const results = [];
  const endpoints = [
    { path: '/api/health', method: 'GET', name: '健康检查' },
    { path: '/api/auth/verify', method: 'GET', name: '认证验证' },
    { path: '/api/price-data', method: 'GET', name: '价格数据' },
  ];

  for (const endpoint of endpoints) {
    try {
      const response = await makeRequest(`${STAGING_CONFIG.baseUrl}${endpoint.path}`, {
        method: endpoint.method,
      });

      const isJson = response.headers.get('content-type')?.includes('application/json');
      
      results.push({
        test: `API ${endpoint.name}`,
        status: response.ok ? 'pass' : 'fail',
        message: `${endpoint.method} ${response.status}, JSON: ${isJson ? '✓' : '✗'}`,
      });
    } catch (error) {
      results.push({
        test: `API ${endpoint.name}`,
        status: 'fail',
        message: error.message,
      });
    }
  }

  return results;
}

/**
 * 测试4: 认证流程测试
 */
async function testAuthFlow() {
  const results = [];

  try {
    // 测试认证验证端点
    const verifyResponse = await makeRequest(`${STAGING_CONFIG.apiUrl}/auth/verify`);
    
    results.push({
      test: '认证验证端点',
      status: verifyResponse.status === 401 ? 'pass' : 'warn',
      message: `未认证状态返回: ${verifyResponse.status}`,
    });

    // 测试Telegram认证端点结构
    const telegramResponse = await makeRequest(`${STAGING_CONFIG.apiUrl}/auth/telegram`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({}),
    });

    results.push({
      test: 'Telegram认证端点',
      status: telegramResponse.status === 400 ? 'pass' : 'warn',
      message: `空请求返回: ${telegramResponse.status}`,
    });

  } catch (error) {
    results.push({
      test: '认证流程',
      status: 'fail',
      message: error.message,
    });
  }

  return results;
}

/**
 * 测试5: 钱包功能测试
 */
async function testWalletFunctionality() {
  const results = [];

  try {
    // 测试钱包余额端点（需要认证）
    const balanceResponse = await makeRequest(`${STAGING_CONFIG.apiUrl}/wallet/balance`);
    
    results.push({
      test: '钱包余额端点',
      status: balanceResponse.status === 401 ? 'pass' : 'warn',
      message: `未认证访问返回: ${balanceResponse.status}`,
    });

    // 测试交易记录端点
    const transactionsResponse = await makeRequest(`${STAGING_CONFIG.apiUrl}/wallet/transactions`);
    
    results.push({
      test: '交易记录端点',
      status: transactionsResponse.status === 401 ? 'pass' : 'warn',
      message: `未认证访问返回: ${transactionsResponse.status}`,
    });

  } catch (error) {
    results.push({
      test: '钱包功能',
      status: 'fail',
      message: error.message,
    });
  }

  return results;
}

/**
 * 测试6: 性能测试
 */
async function testPerformance() {
  const results = [];

  try {
    const startTime = Date.now();
    const response = await makeRequest(STAGING_CONFIG.baseUrl);
    const endTime = Date.now();
    const responseTime = endTime - startTime;

    results.push({
      test: '首页响应时间',
      status: responseTime < 3000 ? 'pass' : responseTime < 5000 ? 'warn' : 'fail',
      message: `响应时间: ${responseTime}ms`,
    });

    // 测试API响应时间
    const apiStartTime = Date.now();
    const apiResponse = await makeRequest(`${STAGING_CONFIG.apiUrl}/health`);
    const apiEndTime = Date.now();
    const apiResponseTime = apiEndTime - apiStartTime;

    results.push({
      test: 'API响应时间',
      status: apiResponseTime < 1000 ? 'pass' : apiResponseTime < 2000 ? 'warn' : 'fail',
      message: `API响应时间: ${apiResponseTime}ms`,
    });

  } catch (error) {
    results.push({
      test: '性能测试',
      status: 'fail',
      message: error.message,
    });
  }

  return results;
}

/**
 * 测试7: 安全测试
 */
async function testSecurity() {
  const results = [];

  try {
    // 测试安全头部
    const response = await makeRequest(STAGING_CONFIG.baseUrl);
    const headers = response.headers;

    const securityHeaders = [
      { name: 'X-Frame-Options', expected: true },
      { name: 'X-Content-Type-Options', expected: true },
      { name: 'Referrer-Policy', expected: true },
    ];

    securityHeaders.forEach(({ name, expected }) => {
      const hasHeader = headers.has(name.toLowerCase());
      results.push({
        test: `安全头部 ${name}`,
        status: hasHeader === expected ? 'pass' : 'warn',
        message: hasHeader ? `存在: ${headers.get(name.toLowerCase())}` : '缺失',
      });
    });

    // 测试HTTPS重定向
    try {
      const httpResponse = await makeRequest(STAGING_CONFIG.baseUrl.replace('https:', 'http:'));
      results.push({
        test: 'HTTPS重定向',
        status: httpResponse.status >= 300 && httpResponse.status < 400 ? 'pass' : 'warn',
        message: `HTTP访问返回: ${httpResponse.status}`,
      });
    } catch (error) {
      results.push({
        test: 'HTTPS重定向',
        status: 'warn',
        message: 'HTTP访问测试失败',
      });
    }

  } catch (error) {
    results.push({
      test: '安全测试',
      status: 'fail',
      message: error.message,
    });
  }

  return results;
}

/**
 * 测试8: 数据库连接测试
 */
async function testDatabaseConnection() {
  const results = [];

  try {
    // 通过健康检查API测试数据库连接
    const response = await makeRequest(`${STAGING_CONFIG.apiUrl}/health`);
    
    if (response.ok) {
      const data = await response.json();
      const dbStatus = data.data?.checks?.database?.status;
      
      results.push({
        test: '数据库连接',
        status: dbStatus === 'healthy' ? 'pass' : 'fail',
        message: `数据库状态: ${dbStatus || '未知'}`,
      });
    } else {
      results.push({
        test: '数据库连接',
        status: 'fail',
        message: '无法获取数据库状态',
      });
    }

  } catch (error) {
    results.push({
      test: '数据库连接',
      status: 'fail',
      message: error.message,
    });
  }

  return results;
}

/**
 * 运行单个测试用例
 */
async function runTestCase(testCase) {
  log('blue', `\n🧪 运行测试: ${testCase.name}`);
  
  try {
    const results = await testCase.test();
    return {
      name: testCase.name,
      category: testCase.category,
      priority: testCase.priority,
      status: 'completed',
      results,
    };
  } catch (error) {
    return {
      name: testCase.name,
      category: testCase.category,
      priority: testCase.priority,
      status: 'error',
      error: error.message,
      results: [],
    };
  }
}

/**
 * 生成测试报告
 */
function generateReport(testResults) {
  log('cyan', '\n📊 Staging环境测试报告');
  log('cyan', '='.repeat(60));
  
  let totalTests = 0;
  let passedTests = 0;
  let failedTests = 0;
  let warnTests = 0;

  testResults.forEach(testCase => {
    log('magenta', `\n📋 ${testCase.name} (${testCase.category})`);
    
    if (testCase.status === 'error') {
      log('red', `❌ 测试执行失败: ${testCase.error}`);
      failedTests++;
      totalTests++;
      return;
    }

    testCase.results.forEach(result => {
      totalTests++;
      
      switch (result.status) {
        case 'pass':
          log('green', `✅ ${result.test}: ${result.message}`);
          passedTests++;
          break;
        case 'fail':
          log('red', `❌ ${result.test}: ${result.message}`);
          failedTests++;
          break;
        case 'warn':
          log('yellow', `⚠️ ${result.test}: ${result.message}`);
          warnTests++;
          break;
      }
    });
  });

  // 总结
  log('cyan', '\n📈 测试总结');
  log('cyan', '='.repeat(30));
  log('blue', `总测试数: ${totalTests}`);
  log('green', `通过: ${passedTests}`);
  log('yellow', `警告: ${warnTests}`);
  log('red', `失败: ${failedTests}`);
  
  const successRate = totalTests > 0 ? ((passedTests / totalTests) * 100).toFixed(1) : 0;
  log('blue', `成功率: ${successRate}%`);

  // 建议
  log('cyan', '\n💡 建议');
  if (failedTests > 0) {
    log('red', '- 修复失败的测试项目');
  }
  if (warnTests > 0) {
    log('yellow', '- 检查警告项目，考虑优化');
  }
  if (passedTests === totalTests) {
    log('green', '- 所有测试通过，staging环境状态良好！');
  }

  return {
    total: totalTests,
    passed: passedTests,
    failed: failedTests,
    warnings: warnTests,
    successRate: parseFloat(successRate),
  };
}

/**
 * 主函数
 */
async function main() {
  console.log('🧪 SocioMint Staging环境测试工具');
  console.log('='.repeat(50));
  
  log('blue', `测试目标: ${STAGING_CONFIG.baseUrl}`);
  log('blue', `超时设置: ${STAGING_CONFIG.timeout}ms`);
  
  const testResults = [];
  
  // 按优先级排序测试用例
  const sortedTestCases = TEST_CASES.sort((a, b) => {
    const priorityOrder = { critical: 0, high: 1, medium: 2, low: 3 };
    return priorityOrder[a.priority] - priorityOrder[b.priority];
  });

  // 运行所有测试
  for (const testCase of sortedTestCases) {
    const result = await runTestCase(testCase);
    testResults.push(result);
    
    // 如果是关键测试失败，考虑是否继续
    if (testCase.priority === 'critical' && result.status === 'error') {
      log('red', '⚠️ 关键测试失败，但继续执行其他测试...');
    }
  }

  // 生成报告
  const summary = generateReport(testResults);
  
  // 退出码
  const exitCode = summary.failed > 0 ? 1 : 0;
  
  log('cyan', `\n🏁 测试完成，退出码: ${exitCode}`);
  process.exit(exitCode);
}

// 运行主函数
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    log('red', `💥 测试执行失败: ${error.message}`);
    process.exit(1);
  });
}
