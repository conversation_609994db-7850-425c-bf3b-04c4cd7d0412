#!/usr/bin/env node

/**
 * 页面可访问性测试脚本
 * 验证所有路由和页面是否正常工作
 */

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { readdir, stat } from 'fs/promises';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

// 测试配置
const TEST_CONFIG = {
  baseUrl: 'http://localhost:3000',
  timeout: 30000,
  retries: 3,
};

// 需要测试的页面路由
const ROUTES_TO_TEST = [
  '/',
  '/login',
  '/dashboard',
  '/presale',
  '/merchants',
  '/tasks',
  '/profile',
  '/wallet',
  '/governance',
  '/api/health',
  '/api/price-data',
];

// 颜色定义
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 启动开发服务器
function startDevServer() {
  return new Promise((resolve, reject) => {
    log('blue', '🚀 启动开发服务器...');
    
    const server = spawn('npm', ['run', 'dev'], {
      cwd: projectRoot,
      stdio: 'pipe',
      env: {
        ...process.env,
        NODE_ENV: 'development',
        PORT: '3000',
      }
    });

    let serverReady = false;
    
    server.stdout.on('data', (data) => {
      const output = data.toString();
      if (output.includes('Ready') || output.includes('started server')) {
        if (!serverReady) {
          serverReady = true;
          log('green', '✅ 开发服务器已启动');
          resolve(server);
        }
      }
    });

    server.stderr.on('data', (data) => {
      const output = data.toString();
      if (output.includes('Error') || output.includes('Failed')) {
        log('red', `❌ 服务器启动错误: ${output}`);
        reject(new Error(output));
      }
    });

    server.on('error', (error) => {
      log('red', `❌ 服务器进程错误: ${error.message}`);
      reject(error);
    });

    // 超时检查
    setTimeout(() => {
      if (!serverReady) {
        log('yellow', '⏰ 服务器启动超时，但继续测试...');
        resolve(server);
      }
    }, 30000);
  });
}

// 测试单个路由
async function testRoute(route) {
  const url = `${TEST_CONFIG.baseUrl}${route}`;
  
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'User-Agent': 'SocioMint-Test-Bot/1.0',
      },
      signal: AbortSignal.timeout(TEST_CONFIG.timeout),
    });

    const result = {
      route,
      url,
      status: response.status,
      ok: response.ok,
      contentType: response.headers.get('content-type'),
      size: response.headers.get('content-length'),
    };

    if (response.ok) {
      log('green', `✅ ${route} - ${response.status}`);
    } else {
      log('red', `❌ ${route} - ${response.status}`);
    }

    return result;
  } catch (error) {
    log('red', `❌ ${route} - 错误: ${error.message}`);
    return {
      route,
      url,
      error: error.message,
      ok: false,
    };
  }
}

// 发现页面路由
async function discoverRoutes() {
  const appDir = join(projectRoot, 'src', 'app');
  const routes = new Set(ROUTES_TO_TEST);

  try {
    const discoverInDir = async (dir, basePath = '') => {
      const items = await readdir(dir);
      
      for (const item of items) {
        const itemPath = join(dir, item);
        const itemStat = await stat(itemPath);
        
        if (itemStat.isDirectory()) {
          // 跳过特殊目录
          if (item.startsWith('(') || item.startsWith('_') || item === 'api') {
            continue;
          }
          
          const routePath = `${basePath}/${item}`;
          
          // 检查是否有page.tsx或page.js
          try {
            const pageFiles = await readdir(itemPath);
            if (pageFiles.some(file => file.startsWith('page.'))) {
              routes.add(routePath);
            }
          } catch (e) {
            // 忽略错误
          }
          
          // 递归搜索子目录
          await discoverInDir(itemPath, routePath);
        }
      }
    };

    await discoverInDir(appDir);
    log('cyan', `🔍 发现 ${routes.size} 个路由`);
    
  } catch (error) {
    log('yellow', `⚠️ 路由发现失败: ${error.message}`);
  }

  return Array.from(routes);
}

// 生成测试报告
function generateReport(results) {
  const total = results.length;
  const passed = results.filter(r => r.ok).length;
  const failed = total - passed;
  
  log('cyan', '\n📊 测试报告');
  log('cyan', '='.repeat(50));
  log('green', `✅ 通过: ${passed}`);
  log('red', `❌ 失败: ${failed}`);
  log('blue', `📊 总计: ${total}`);
  log('cyan', `📈 成功率: ${((passed / total) * 100).toFixed(1)}%`);
  
  if (failed > 0) {
    log('red', '\n❌ 失败的路由:');
    results
      .filter(r => !r.ok)
      .forEach(r => {
        log('red', `  - ${r.route}: ${r.error || r.status}`);
      });
  }
  
  return { total, passed, failed, successRate: (passed / total) * 100 };
}

// 主测试函数
async function runTests() {
  log('magenta', '🧪 SocioMint 页面可访问性测试');
  log('magenta', '='.repeat(50));
  
  let server = null;
  
  try {
    // 启动开发服务器
    server = await startDevServer();
    
    // 等待服务器完全启动
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // 发现路由
    const routes = await discoverRoutes();
    
    log('blue', `\n🔍 开始测试 ${routes.length} 个路由...`);
    
    // 测试所有路由
    const results = [];
    for (const route of routes) {
      const result = await testRoute(route);
      results.push(result);
      
      // 短暂延迟避免过载
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    // 生成报告
    const report = generateReport(results);
    
    // 保存详细结果
    const fs = await import('fs/promises');
    const reportPath = join(projectRoot, 'test-results', 'page-accessibility.json');
    
    try {
      await fs.mkdir(join(projectRoot, 'test-results'), { recursive: true });
      await fs.writeFile(reportPath, JSON.stringify({
        timestamp: new Date().toISOString(),
        config: TEST_CONFIG,
        summary: report,
        results,
      }, null, 2));
      
      log('blue', `\n📄 详细报告已保存到: ${reportPath}`);
    } catch (e) {
      log('yellow', `⚠️ 无法保存报告: ${e.message}`);
    }
    
    // 返回结果
    return report.successRate >= 80; // 80%成功率为通过
    
  } catch (error) {
    log('red', `❌ 测试过程出错: ${error.message}`);
    return false;
  } finally {
    // 清理：关闭服务器
    if (server) {
      log('yellow', '🛑 关闭开发服务器...');
      server.kill('SIGTERM');
      setTimeout(() => {
        server.kill('SIGKILL');
      }, 5000);
    }
  }
}

// 运行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  runTests()
    .then(success => {
      if (success) {
        log('green', '\n🎉 所有测试通过！');
        process.exit(0);
      } else {
        log('red', '\n💥 测试失败！');
        process.exit(1);
      }
    })
    .catch(error => {
      log('red', `\n💥 测试异常: ${error.message}`);
      process.exit(1);
    });
}
