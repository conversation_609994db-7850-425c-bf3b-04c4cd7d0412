/**
 * 福气系统数据库迁移执行脚本
 * 执行004_add_fortune_system.sql迁移文件
 */

import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { join } from 'path';
import { config } from 'dotenv';

// 加载环境变量
config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

if (!supabaseUrl || !supabaseServiceKey) {
  throw new Error('Missing Supabase configuration. Please check your environment variables.');
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

interface MigrationResult {
  success: boolean;
  message: string;
  error?: string;
  details?: any;
}

/**
 * 执行福气系统迁移
 */
async function runFortuneMigration(): Promise<MigrationResult> {
  console.log('🚀 开始执行福气系统数据库迁移...\n');

  try {
    // 1. 读取迁移文件
    const migrationPath = join(process.cwd(), 'database/migrations/004_add_fortune_system.sql');
    console.log('📄 读取迁移文件:', migrationPath);
    
    const migrationSQL = readFileSync(migrationPath, 'utf8');
    console.log('✅ 迁移文件读取成功\n');

    // 2. 检查数据库连接
    console.log('🔍 检查数据库连接...');
    const { data: connectionTest, error: connectionError } = await supabase
      .from('users')
      .select('count')
      .limit(1);

    if (connectionError) {
      throw new Error(`数据库连接失败: ${connectionError.message}`);
    }
    console.log('✅ 数据库连接正常\n');

    // 3. 检查是否已经迁移过
    console.log('🔍 检查迁移状态...');
    const { data: existingTables, error: checkError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .in('table_name', ['fortune_transactions', 'daily_checkins', 'share_rewards', 'fortune_levels']);

    if (existingTables && existingTables.length > 0) {
      console.log('⚠️  检测到福气系统表已存在，跳过迁移');
      return {
        success: true,
        message: '福气系统已经迁移过，无需重复执行',
        details: { existingTables: existingTables.map(t => t.table_name) }
      };
    }

    // 4. 执行迁移
    console.log('⚡ 开始执行迁移SQL...');
    
    // 将SQL分割成多个语句执行（避免复杂的DO块问题）
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    let executedStatements = 0;
    const errors: string[] = [];

    for (const statement of statements) {
      try {
        if (statement.includes('DO $$') || statement.includes('BEGIN') || statement.includes('END $$')) {
          // 跳过复杂的DO块，使用简化的SQL
          console.log('⏭️  跳过复杂语句块');
          continue;
        }

        const { error } = await supabase.rpc('exec_sql', { sql: statement });
        
        if (error) {
          console.warn(`⚠️  语句执行警告: ${error.message}`);
          errors.push(`Statement: ${statement.substring(0, 50)}... Error: ${error.message}`);
        } else {
          executedStatements++;
        }
      } catch (err) {
        console.warn(`⚠️  语句执行异常: ${err instanceof Error ? err.message : 'Unknown error'}`);
        errors.push(`Statement: ${statement.substring(0, 50)}... Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
      }
    }

    console.log(`✅ 迁移执行完成，成功执行 ${executedStatements} 个语句\n`);

    // 5. 验证迁移结果
    console.log('🔍 验证迁移结果...');
    const { data: newTables, error: verifyError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .in('table_name', ['fortune_transactions', 'daily_checkins', 'share_rewards', 'fortune_levels']);

    if (verifyError) {
      throw new Error(`迁移验证失败: ${verifyError.message}`);
    }

    const createdTables = newTables?.map(t => t.table_name) || [];
    console.log('✅ 创建的表:', createdTables);

    // 6. 检查users表是否添加了福气字段
    const { data: userColumns, error: columnError } = await supabase
      .from('information_schema.columns')
      .select('column_name')
      .eq('table_name', 'users')
      .eq('table_schema', 'public')
      .in('column_name', ['available_fortune', 'fortune_level', 'fortune_level_name']);

    const addedColumns = userColumns?.map(c => c.column_name) || [];
    console.log('✅ 添加的用户表字段:', addedColumns);

    // 7. 插入福气等级配置
    console.log('📝 插入福气等级配置...');
    const { error: levelError } = await supabase
      .from('fortune_levels')
      .upsert([
        { level: 1, level_name: '初来乍到', min_fortune: 0, max_fortune: 999.99999999, benefits: ["基础功能使用权"] },
        { level: 2, level_name: '小有福气', min_fortune: 1000, max_fortune: 9999.99999999, benefits: ["基础功能使用权", "优先客服支持"] },
        { level: 3, level_name: '福气满满', min_fortune: 10000, max_fortune: 99999.99999999, benefits: ["基础功能使用权", "优先客服支持", "可发起1vN赌约"] },
        { level: 4, level_name: '福星高照', min_fortune: 100000, max_fortune: 999999.99999999, benefits: ["基础功能使用权", "优先客服支持", "可发起1vN赌约", "专属客服通道"] },
        { level: 5, level_name: '福气无边', min_fortune: 1000000, max_fortune: null, benefits: ["基础功能使用权", "优先客服支持", "可发起1vN赌约", "专属客服通道", "内测新功能权限"] }
      ]);

    if (levelError) {
      console.warn('⚠️  福气等级配置插入失败:', levelError.message);
    } else {
      console.log('✅ 福气等级配置插入成功');
    }

    console.log('\n🎉 福气系统迁移完成！');

    return {
      success: true,
      message: '福气系统迁移成功完成',
      details: {
        executedStatements,
        createdTables,
        addedColumns,
        errors: errors.length > 0 ? errors : undefined
      }
    };

  } catch (error) {
    console.error('💥 迁移失败:', error);
    
    return {
      success: false,
      message: '福气系统迁移失败',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * 简化的迁移执行（手动创建表）
 */
async function runSimplifiedMigration(): Promise<MigrationResult> {
  console.log('🔧 执行简化迁移...\n');

  try {
    // 1. 添加用户表字段
    console.log('📝 添加用户表字段...');
    const userTableUpdates = [
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS available_fortune DECIMAL(20,8) DEFAULT 0',
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS locked_fortune DECIMAL(20,8) DEFAULT 0',
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS total_fortune_earned DECIMAL(20,8) DEFAULT 0',
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS total_fortune_spent DECIMAL(20,8) DEFAULT 0',
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS fortune_level INTEGER DEFAULT 1',
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS fortune_level_name VARCHAR(20) DEFAULT \'初来乍到\'',
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS consecutive_checkin_days INTEGER DEFAULT 0',
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS last_checkin_date DATE',
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS total_checkin_days INTEGER DEFAULT 0'
    ];

    for (const sql of userTableUpdates) {
      try {
        await supabase.rpc('exec_sql', { sql });
        console.log('✅', sql.substring(0, 50) + '...');
      } catch (err) {
        console.warn('⚠️ ', sql.substring(0, 50) + '...', err);
      }
    }

    return {
      success: true,
      message: '简化迁移完成'
    };

  } catch (error) {
    return {
      success: false,
      message: '简化迁移失败',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * 执行迁移
 */
if (require.main === module) {
  const args = process.argv.slice(2);
  const useSimplified = args.includes('--simplified');

  const migrationFunction = useSimplified ? runSimplifiedMigration : runFortuneMigration;

  migrationFunction()
    .then((result) => {
      if (result.success) {
        console.log('\n✨ 迁移成功完成！');
        if (result.details) {
          console.log('详细信息:', JSON.stringify(result.details, null, 2));
        }
        process.exit(0);
      } else {
        console.log('\n❌ 迁移失败:', result.message);
        if (result.error) {
          console.log('错误详情:', result.error);
        }
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error('💥 迁移执行异常:', error);
      process.exit(1);
    });
}

export { runFortuneMigration, runSimplifiedMigration };
