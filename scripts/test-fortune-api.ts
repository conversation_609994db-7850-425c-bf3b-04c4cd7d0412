/**
 * 福气系统API测试脚本
 * 测试所有福气系统的API端点功能
 */

interface TestResult {
  endpoint: string;
  method: string;
  passed: boolean;
  status?: number;
  error?: string;
  responseTime?: number;
}

interface TestSuite {
  name: string;
  results: TestResult[];
  passed: number;
  failed: number;
}

/**
 * 主测试函数
 */
async function testFortuneAPIs(): Promise<void> {
  console.log('🧪 开始福气系统API测试...\n');

  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
  
  const testSuites: TestSuite[] = [
    await testAccountAPIs(baseUrl),
    await testRewardAPIs(baseUrl),
    await testAdminAPIs(baseUrl)
  ];

  // 打印测试结果
  printTestResults(testSuites);
}

/**
 * 测试账户相关API
 */
async function testAccountAPIs(baseUrl: string): Promise<TestSuite> {
  const suite: TestSuite = {
    name: '福气账户API',
    results: [],
    passed: 0,
    failed: 0
  };

  // 测试获取账户信息（未认证）
  await testEndpoint(
    suite,
    `${baseUrl}/api/fortune/account`,
    'GET',
    undefined,
    401 // 期望401未授权
  );

  // 测试初始化账户（未认证）
  await testEndpoint(
    suite,
    `${baseUrl}/api/fortune/account/initialize`,
    'POST',
    undefined,
    401 // 期望401未授权
  );

  // 统计结果
  suite.passed = suite.results.filter(r => r.passed).length;
  suite.failed = suite.results.filter(r => !r.passed).length;

  return suite;
}

/**
 * 测试奖励相关API
 */
async function testRewardAPIs(baseUrl: string): Promise<TestSuite> {
  const suite: TestSuite = {
    name: '福气奖励API',
    results: [],
    passed: 0,
    failed: 0
  };

  // 测试每日签到API
  await testEndpoint(
    suite,
    `${baseUrl}/api/fortune/daily-checkin`,
    'GET',
    undefined,
    401
  );

  await testEndpoint(
    suite,
    `${baseUrl}/api/fortune/daily-checkin`,
    'POST',
    undefined,
    401
  );

  // 测试邀请奖励API
  await testEndpoint(
    suite,
    `${baseUrl}/api/fortune/invite-reward`,
    'GET',
    undefined,
    401
  );

  await testEndpoint(
    suite,
    `${baseUrl}/api/fortune/invite-reward`,
    'POST',
    { inviteeId: 'test-id' },
    401
  );

  // 测试分享奖励API
  await testEndpoint(
    suite,
    `${baseUrl}/api/fortune/share-reward`,
    'GET',
    undefined,
    401
  );

  await testEndpoint(
    suite,
    `${baseUrl}/api/fortune/share-reward`,
    'POST',
    { 
      contentType: 'bet',
      contentId: 'test-id',
      platform: 'telegram'
    },
    401
  );

  // 统计结果
  suite.passed = suite.results.filter(r => r.passed).length;
  suite.failed = suite.results.filter(r => !r.passed).length;

  return suite;
}

/**
 * 测试管理员API
 */
async function testAdminAPIs(baseUrl: string): Promise<TestSuite> {
  const suite: TestSuite = {
    name: '福气管理API',
    results: [],
    passed: 0,
    failed: 0
  };

  // 测试管理员统计API
  await testEndpoint(
    suite,
    `${baseUrl}/api/fortune/admin`,
    'GET',
    undefined,
    401
  );

  // 测试管理员调整API
  await testEndpoint(
    suite,
    `${baseUrl}/api/fortune/admin`,
    'POST',
    {
      targetUserId: 'test-id',
      amount: 100,
      reason: 'test adjustment'
    },
    401
  );

  // 统计结果
  suite.passed = suite.results.filter(r => r.passed).length;
  suite.failed = suite.results.filter(r => !r.passed).length;

  return suite;
}

/**
 * 测试单个API端点
 */
async function testEndpoint(
  suite: TestSuite,
  url: string,
  method: string,
  body?: any,
  expectedStatus?: number
): Promise<void> {
  const startTime = Date.now();
  
  try {
    const options: RequestInit = {
      method,
      headers: {
        'Content-Type': 'application/json',
      },
    };

    if (body && (method === 'POST' || method === 'PUT')) {
      options.body = JSON.stringify(body);
    }

    const response = await fetch(url, options);
    const responseTime = Date.now() - startTime;
    
    const passed = expectedStatus ? response.status === expectedStatus : response.ok;
    
    suite.results.push({
      endpoint: url.replace(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000', ''),
      method,
      passed,
      status: response.status,
      responseTime
    });

  } catch (error) {
    const responseTime = Date.now() - startTime;
    
    suite.results.push({
      endpoint: url.replace(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000', ''),
      method,
      passed: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      responseTime
    });
  }
}

/**
 * 打印测试结果
 */
function printTestResults(testSuites: TestSuite[]): void {
  console.log('\n📊 API测试结果摘要:\n');

  let totalPassed = 0;
  let totalFailed = 0;

  testSuites.forEach(suite => {
    const status = suite.failed === 0 ? '✅' : '❌';
    console.log(`${status} ${suite.name}: ${suite.passed}/${suite.passed + suite.failed} 通过`);

    suite.results.forEach(result => {
      const resultStatus = result.passed ? '  ✅' : '  ❌';
      const timing = result.responseTime ? ` (${result.responseTime}ms)` : '';
      console.log(`${resultStatus} ${result.method} ${result.endpoint}${timing}`);
      
      if (!result.passed) {
        if (result.error) {
          console.log(`     错误: ${result.error}`);
        } else if (result.status) {
          console.log(`     状态码: ${result.status}`);
        }
      }
    });

    totalPassed += suite.passed;
    totalFailed += suite.failed;
    console.log('');
  });

  console.log(`🎯 总计: ${totalPassed}/${totalPassed + totalFailed} API测试通过`);
  
  if (totalFailed === 0) {
    console.log('🎉 所有API端点响应正常！');
  } else {
    console.log(`⚠️  有 ${totalFailed} 个API测试失败，请检查相关端点。`);
  }
}

/**
 * 测试数据库连接和基本查询
 */
async function testDatabaseConnection(): Promise<void> {
  console.log('🔍 测试数据库连接...\n');

  try {
    // 这里需要使用Supabase客户端测试连接
    // 由于这是一个独立脚本，我们通过API来测试
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
    
    // 测试一个简单的健康检查端点
    const response = await fetch(`${baseUrl}/api/health`);
    
    if (response.ok) {
      console.log('✅ 应用服务器连接正常');
    } else {
      console.log('❌ 应用服务器连接失败');
    }

  } catch (error) {
    console.log('❌ 数据库连接测试失败:', error);
  }
}

/**
 * 执行完整的福气系统测试
 */
async function runCompleteTest(): Promise<void> {
  console.log('🚀 开始福气系统完整测试...\n');

  // 1. 测试数据库连接
  await testDatabaseConnection();

  // 2. 测试API端点
  await testFortuneAPIs();

  console.log('\n🏁 福气系统测试完成！');
}

/**
 * 执行测试
 */
if (require.main === module) {
  runCompleteTest()
    .then(() => {
      console.log('\n✨ 测试执行完成！');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 测试执行失败:', error);
      process.exit(1);
    });
}

export { testFortuneAPIs, runCompleteTest };
