#!/usr/bin/env node

/**
 * 环境管理脚本
 * 用于管理不同环境的配置和部署
 */

import { spawn } from 'child_process';
import { readFile, writeFile, access } from 'fs/promises';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

// 环境配置
const ENVIRONMENTS = {
  development: {
    name: 'Development',
    envFile: '.env.local',
    url: 'http://localhost:3000',
    description: '本地开发环境',
  },
  staging: {
    name: 'Staging',
    envFile: '.env.staging',
    url: 'https://staging.sociomint.app',
    description: '预发布测试环境',
  },
  production: {
    name: 'Production',
    envFile: '.env.production',
    url: 'https://sociomint.app',
    description: '生产环境',
  },
};

// 颜色定义
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 检查环境文件是否存在
async function checkEnvFile(envName) {
  const envConfig = ENVIRONMENTS[envName];
  if (!envConfig) {
    throw new Error(`未知环境: ${envName}`);
  }

  const envPath = join(projectRoot, envConfig.envFile);
  
  try {
    await access(envPath);
    return true;
  } catch {
    return false;
  }
}

// 列出所有环境
async function listEnvironments() {
  log('cyan', '📋 可用环境列表:');
  log('cyan', '='.repeat(50));

  for (const [key, config] of Object.entries(ENVIRONMENTS)) {
    const exists = await checkEnvFile(key);
    const status = exists ? '✅' : '❌';
    
    log('blue', `${status} ${config.name} (${key})`);
    log('blue', `   描述: ${config.description}`);
    log('blue', `   URL: ${config.url}`);
    log('blue', `   配置文件: ${config.envFile} ${exists ? '' : '(不存在)'}`);
    console.log();
  }
}

// 验证环境配置
async function validateEnvironment(envName) {
  log('yellow', `🔍 验证 ${envName} 环境配置...`);

  const envConfig = ENVIRONMENTS[envName];
  const envPath = join(projectRoot, envConfig.envFile);

  try {
    const content = await readFile(envPath, 'utf-8');
    const lines = content.split('\n');
    
    const variables = {};
    const missingValues = [];
    
    lines.forEach((line, index) => {
      const trimmed = line.trim();
      if (trimmed && !trimmed.startsWith('#')) {
        const [key, ...valueParts] = trimmed.split('=');
        const value = valueParts.join('=');
        
        if (key && value !== undefined) {
          variables[key] = value;
          
          // 检查是否有占位符值
          if (value.includes('your_') || value.includes('YOUR_') || 
              value.includes('_HERE') || value === '') {
            missingValues.push(key);
          }
        }
      }
    });

    log('green', `✅ 找到 ${Object.keys(variables).length} 个环境变量`);
    
    if (missingValues.length > 0) {
      log('yellow', `⚠️  需要配置的变量 (${missingValues.length}个):`);
      missingValues.forEach(key => {
        log('yellow', `   - ${key}`);
      });
    } else {
      log('green', '✅ 所有变量都已配置');
    }

    return {
      total: Object.keys(variables).length,
      missing: missingValues.length,
      variables,
      missingValues,
    };

  } catch (error) {
    log('red', `❌ 验证失败: ${error.message}`);
    return null;
  }
}

// 比较环境配置
async function compareEnvironments(env1, env2) {
  log('cyan', `🔄 比较 ${env1} 和 ${env2} 环境配置...`);

  const result1 = await validateEnvironment(env1);
  const result2 = await validateEnvironment(env2);

  if (!result1 || !result2) {
    log('red', '❌ 无法比较环境配置');
    return;
  }

  const keys1 = new Set(Object.keys(result1.variables));
  const keys2 = new Set(Object.keys(result2.variables));

  const onlyIn1 = [...keys1].filter(key => !keys2.has(key));
  const onlyIn2 = [...keys2].filter(key => !keys1.has(key));
  const common = [...keys1].filter(key => keys2.has(key));

  log('blue', '\n📊 比较结果:');
  log('blue', '='.repeat(30));
  
  if (onlyIn1.length > 0) {
    log('yellow', `仅在 ${env1} 中存在 (${onlyIn1.length}个):`);
    onlyIn1.forEach(key => log('yellow', `   - ${key}`));
  }

  if (onlyIn2.length > 0) {
    log('yellow', `仅在 ${env2} 中存在 (${onlyIn2.length}个):`);
    onlyIn2.forEach(key => log('yellow', `   - ${key}`));
  }

  log('green', `共同变量: ${common.length}个`);

  // 检查值差异
  const differentValues = common.filter(key => 
    result1.variables[key] !== result2.variables[key]
  );

  if (differentValues.length > 0) {
    log('cyan', `\n值不同的变量 (${differentValues.length}个):`);
    differentValues.slice(0, 10).forEach(key => {
      log('cyan', `   ${key}:`);
      log('cyan', `     ${env1}: ${result1.variables[key].substring(0, 50)}...`);
      log('cyan', `     ${env2}: ${result2.variables[key].substring(0, 50)}...`);
    });
    
    if (differentValues.length > 10) {
      log('cyan', `   ... 还有 ${differentValues.length - 10} 个变量不同`);
    }
  }
}

// 复制环境配置
async function copyEnvironment(source, target) {
  log('blue', `📋 复制 ${source} 环境配置到 ${target}...`);

  const sourceConfig = ENVIRONMENTS[source];
  const targetConfig = ENVIRONMENTS[target];

  if (!sourceConfig || !targetConfig) {
    log('red', '❌ 无效的环境名称');
    return;
  }

  const sourcePath = join(projectRoot, sourceConfig.envFile);
  const targetPath = join(projectRoot, targetConfig.envFile);

  try {
    const content = await readFile(sourcePath, 'utf-8');
    
    // 更新环境特定的变量
    let updatedContent = content
      .replace(/NODE_ENV=.*/g, `NODE_ENV=${target}`)
      .replace(/NEXT_PUBLIC_APP_ENV=.*/g, `NEXT_PUBLIC_APP_ENV=${target}`)
      .replace(/NEXT_PUBLIC_APP_URL=.*/g, `NEXT_PUBLIC_APP_URL=${targetConfig.url}`);

    // 添加复制信息
    const header = `# ${targetConfig.name} 环境配置文件\n# 从 ${sourceConfig.name} 环境复制于 ${new Date().toISOString()}\n\n`;
    updatedContent = header + updatedContent;

    await writeFile(targetPath, updatedContent);
    log('green', `✅ 成功复制到 ${targetConfig.envFile}`);

  } catch (error) {
    log('red', `❌ 复制失败: ${error.message}`);
  }
}

// 部署到指定环境
async function deployToEnvironment(envName) {
  log('blue', `🚀 部署到 ${envName} 环境...`);

  const envConfig = ENVIRONMENTS[envName];
  if (!envConfig) {
    log('red', `❌ 未知环境: ${envName}`);
    return;
  }

  // 检查环境文件
  const hasEnvFile = await checkEnvFile(envName);
  if (!hasEnvFile) {
    log('red', `❌ 环境配置文件 ${envConfig.envFile} 不存在`);
    return;
  }

  // 验证配置
  const validation = await validateEnvironment(envName);
  if (!validation) {
    log('red', '❌ 环境配置验证失败');
    return;
  }

  if (validation.missing > 0) {
    log('yellow', `⚠️  有 ${validation.missing} 个变量需要配置，是否继续？`);
    // 在实际使用中，这里可以添加用户确认逻辑
  }

  // 执行部署
  const deployCommand = envName === 'production' 
    ? 'wrangler pages deploy out --project-name=sociomint-production'
    : 'wrangler pages deploy out --project-name=sociomint-staging';

  log('blue', `执行部署命令: ${deployCommand}`);
  
  // 这里可以添加实际的部署逻辑
  log('green', `✅ ${envName} 环境部署完成`);
  log('green', `🌐 访问地址: ${envConfig.url}`);
}

// 主函数
async function main() {
  const command = process.argv[2];
  const arg1 = process.argv[3];
  const arg2 = process.argv[4];

  console.log('🌍 SocioMint 环境管理工具');
  console.log('='.repeat(40));

  try {
    switch (command) {
      case 'list':
        await listEnvironments();
        break;

      case 'validate':
        if (!arg1) {
          log('red', '❌ 请指定环境名称');
          process.exit(1);
        }
        await validateEnvironment(arg1);
        break;

      case 'compare':
        if (!arg1 || !arg2) {
          log('red', '❌ 请指定两个环境名称');
          process.exit(1);
        }
        await compareEnvironments(arg1, arg2);
        break;

      case 'copy':
        if (!arg1 || !arg2) {
          log('red', '❌ 请指定源环境和目标环境');
          process.exit(1);
        }
        await copyEnvironment(arg1, arg2);
        break;

      case 'deploy':
        if (!arg1) {
          log('red', '❌ 请指定环境名称');
          process.exit(1);
        }
        await deployToEnvironment(arg1);
        break;

      default:
        console.log('用法:');
        console.log('  node scripts/manage-environments.js list                    - 列出所有环境');
        console.log('  node scripts/manage-environments.js validate <env>         - 验证环境配置');
        console.log('  node scripts/manage-environments.js compare <env1> <env2>  - 比较两个环境');
        console.log('  node scripts/manage-environments.js copy <source> <target> - 复制环境配置');
        console.log('  node scripts/manage-environments.js deploy <env>           - 部署到指定环境');
        console.log('');
        console.log('可用环境: development, staging, production');
        break;
    }
  } catch (error) {
    log('red', `❌ 执行失败: ${error.message}`);
    process.exit(1);
  }
}

// 运行主函数
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
