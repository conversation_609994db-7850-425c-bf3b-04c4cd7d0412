#!/usr/bin/env node

/**
 * BSC配置验证脚本
 * 验证项目是否正确配置为BSC链
 */

import { readFile } from 'fs/promises';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

// 颜色定义
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
};

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// BSC网络配置
const BSC_NETWORKS = {
  mainnet: {
    chainId: '56',
    name: 'BSC Mainnet',
    rpcUrls: [
      'https://bsc-dataseed1.binance.org/',
      'https://bsc-dataseed2.binance.org/',
      'https://bsc-dataseed3.binance.org/',
      'https://bsc-dataseed4.binance.org/'
    ],
    blockExplorer: 'https://bscscan.com',
    nativeCurrency: 'BNB'
  },
  testnet: {
    chainId: '97',
    name: 'BSC Testnet',
    rpcUrls: [
      'https://data-seed-prebsc-1-s1.binance.org:8545/',
      'https://data-seed-prebsc-2-s1.binance.org:8545/'
    ],
    blockExplorer: 'https://testnet.bscscan.com',
    nativeCurrency: 'tBNB'
  }
};

// 必需的环境变量
const REQUIRED_ENV_VARS = [
  'NEXT_PUBLIC_CHAIN_ID',
  'NEXT_PUBLIC_RPC_URL',
  'NEXT_PUBLIC_HAOX_TOKEN_ADDRESS',
  'NEXT_PUBLIC_HAOX_PRESALE_ADDRESS'
];

// 可选的环境变量
const OPTIONAL_ENV_VARS = [
  'NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID',
  'NEXT_PUBLIC_ALCHEMY_API_KEY',
  'NEXT_PUBLIC_BLOCK_EXPLORER'
];

/**
 * 加载环境变量文件
 */
async function loadEnvFile(envFile) {
  try {
    const content = await readFile(envFile, 'utf-8');
    const vars = {};
    
    content.split('\n').forEach(line => {
      const trimmed = line.trim();
      if (!trimmed || trimmed.startsWith('#')) return;
      
      const equalIndex = trimmed.indexOf('=');
      if (equalIndex === -1) return;
      
      const key = trimmed.substring(0, equalIndex).trim();
      const value = trimmed.substring(equalIndex + 1).trim().replace(/^["']|["']$/g, '');
      vars[key] = value;
    });
    
    return vars;
  } catch (error) {
    throw new Error(`无法读取环境文件 ${envFile}: ${error.message}`);
  }
}

/**
 * 验证链ID
 */
function validateChainId(chainId) {
  const validChainIds = ['56', '97'];
  
  if (!chainId) {
    return { valid: false, error: '链ID未设置' };
  }
  
  if (!validChainIds.includes(chainId)) {
    return { 
      valid: false, 
      error: `无效的链ID: ${chainId}，应该是 56 (BSC主网) 或 97 (BSC测试网)` 
    };
  }
  
  const network = chainId === '56' ? 'mainnet' : 'testnet';
  return { 
    valid: true, 
    network,
    info: BSC_NETWORKS[network]
  };
}

/**
 * 验证RPC URL
 */
function validateRpcUrl(rpcUrl, expectedNetwork) {
  if (!rpcUrl) {
    return { valid: false, error: 'RPC URL未设置' };
  }
  
  const validRpcUrls = BSC_NETWORKS[expectedNetwork].rpcUrls;
  const isValidRpc = validRpcUrls.some(validUrl => 
    rpcUrl.includes(validUrl.replace('https://', '').replace('/', ''))
  );
  
  if (!isValidRpc) {
    return { 
      valid: false, 
      error: `RPC URL不匹配${expectedNetwork}网络: ${rpcUrl}` 
    };
  }
  
  return { valid: true };
}

/**
 * 验证合约地址
 */
function validateContractAddress(address, name) {
  if (!address) {
    return { valid: false, error: `${name}地址未设置` };
  }
  
  if (!address.startsWith('0x') || address.length !== 42) {
    return { 
      valid: false, 
      error: `${name}地址格式无效: ${address}` 
    };
  }
  
  // 检查是否为零地址
  if (address === '0x0000000000000000000000000000000000000000') {
    return { 
      valid: false, 
      error: `${name}地址不能为零地址` 
    };
  }
  
  return { valid: true };
}

/**
 * 验证区块浏览器URL
 */
function validateBlockExplorer(explorerUrl, expectedNetwork) {
  if (!explorerUrl) {
    return { valid: true, warning: '区块浏览器URL未设置（可选）' };
  }
  
  const expectedExplorer = BSC_NETWORKS[expectedNetwork].blockExplorer;
  if (!explorerUrl.includes(expectedExplorer.replace('https://', ''))) {
    return { 
      valid: false, 
      error: `区块浏览器URL不匹配${expectedNetwork}网络: ${explorerUrl}` 
    };
  }
  
  return { valid: true };
}

/**
 * 验证环境变量
 */
function validateEnvironmentVariables(vars) {
  const results = {
    errors: [],
    warnings: [],
    info: []
  };
  
  // 验证必需变量
  REQUIRED_ENV_VARS.forEach(varName => {
    if (!vars[varName]) {
      results.errors.push(`缺少必需环境变量: ${varName}`);
    }
  });
  
  if (results.errors.length > 0) {
    return results;
  }
  
  // 验证链ID
  const chainIdResult = validateChainId(vars.NEXT_PUBLIC_CHAIN_ID);
  if (!chainIdResult.valid) {
    results.errors.push(chainIdResult.error);
    return results;
  }
  
  const network = chainIdResult.network;
  results.info.push(`检测到网络: ${chainIdResult.info.name} (Chain ID: ${vars.NEXT_PUBLIC_CHAIN_ID})`);
  
  // 验证RPC URL
  const rpcResult = validateRpcUrl(vars.NEXT_PUBLIC_RPC_URL, network);
  if (!rpcResult.valid) {
    results.errors.push(rpcResult.error);
  } else {
    results.info.push(`RPC URL验证通过: ${vars.NEXT_PUBLIC_RPC_URL}`);
  }
  
  // 验证合约地址
  const contractChecks = [
    { var: 'NEXT_PUBLIC_HAOX_TOKEN_ADDRESS', name: 'HAOX代币合约' },
    { var: 'NEXT_PUBLIC_HAOX_PRESALE_ADDRESS', name: 'HAOX预售合约' }
  ];
  
  contractChecks.forEach(({ var: varName, name }) => {
    if (vars[varName]) {
      const contractResult = validateContractAddress(vars[varName], name);
      if (!contractResult.valid) {
        results.errors.push(contractResult.error);
      } else {
        results.info.push(`${name}地址验证通过: ${vars[varName]}`);
      }
    }
  });
  
  // 验证区块浏览器
  if (vars.NEXT_PUBLIC_BLOCK_EXPLORER) {
    const explorerResult = validateBlockExplorer(vars.NEXT_PUBLIC_BLOCK_EXPLORER, network);
    if (!explorerResult.valid) {
      results.errors.push(explorerResult.error);
    } else if (explorerResult.warning) {
      results.warnings.push(explorerResult.warning);
    } else {
      results.info.push(`区块浏览器URL验证通过: ${vars.NEXT_PUBLIC_BLOCK_EXPLORER}`);
    }
  }
  
  // 检查可选变量
  OPTIONAL_ENV_VARS.forEach(varName => {
    if (!vars[varName]) {
      results.warnings.push(`可选环境变量未设置: ${varName}`);
    } else {
      results.info.push(`可选变量已设置: ${varName}`);
    }
  });
  
  return results;
}

/**
 * 验证Wagmi配置文件
 */
async function validateWagmiConfig() {
  const wagmiPath = join(projectRoot, 'src/lib/wagmi.ts');
  
  try {
    const content = await readFile(wagmiPath, 'utf-8');
    
    const checks = {
      hasBscImport: content.includes('bsc, bscTestnet'),
      hasCorrectChains: content.includes('chains = [bsc, bscTestnet]'),
      noMainnetImport: !content.includes('mainnet'),
      noEthereumImport: !content.includes('ethereum')
    };
    
    const results = {
      errors: [],
      warnings: [],
      info: []
    };
    
    if (!checks.hasBscImport) {
      results.errors.push('Wagmi配置缺少BSC链导入');
    } else {
      results.info.push('Wagmi配置正确导入BSC链');
    }
    
    if (!checks.hasCorrectChains) {
      results.warnings.push('Wagmi配置可能包含非BSC链');
    } else {
      results.info.push('Wagmi配置只使用BSC链');
    }
    
    if (!checks.noMainnetImport) {
      results.warnings.push('Wagmi配置包含以太坊主网导入');
    }
    
    if (!checks.noEthereumImport) {
      results.warnings.push('Wagmi配置包含以太坊相关导入');
    }
    
    return results;
    
  } catch (error) {
    return {
      errors: [`无法读取Wagmi配置: ${error.message}`],
      warnings: [],
      info: []
    };
  }
}

/**
 * 生成验证报告
 */
function generateReport(envResults, wagmiResults, envFile) {
  log('cyan', '\n📊 BSC配置验证报告');
  log('cyan', '='.repeat(50));
  
  log('blue', `环境文件: ${envFile}`);
  log('blue', `验证时间: ${new Date().toLocaleString()}`);
  
  // 错误报告
  const allErrors = [...envResults.errors, ...wagmiResults.errors];
  if (allErrors.length > 0) {
    log('red', `\n❌ 发现 ${allErrors.length} 个错误:`);
    allErrors.forEach(error => log('red', `  - ${error}`));
  }
  
  // 警告报告
  const allWarnings = [...envResults.warnings, ...wagmiResults.warnings];
  if (allWarnings.length > 0) {
    log('yellow', `\n⚠️ 发现 ${allWarnings.length} 个警告:`);
    allWarnings.forEach(warning => log('yellow', `  - ${warning}`));
  }
  
  // 信息报告
  const allInfo = [...envResults.info, ...wagmiResults.info];
  if (allInfo.length > 0) {
    log('green', `\n✅ 验证通过的配置:`);
    allInfo.forEach(info => log('green', `  - ${info}`));
  }
  
  // 总结
  log('cyan', '\n📋 验证总结');
  log('cyan', '='.repeat(30));
  
  if (allErrors.length === 0) {
    log('green', '🎉 BSC配置验证通过！项目已正确配置为BSC链。');
    
    if (allWarnings.length > 0) {
      log('yellow', '💡 建议处理上述警告以获得更好的配置。');
    }
    
    return true;
  } else {
    log('red', '💥 BSC配置验证失败！请修复上述错误。');
    return false;
  }
}

/**
 * 主函数
 */
async function main() {
  const envFile = process.argv[2] || '.env.local';
  const envPath = join(projectRoot, envFile);
  
  console.log('🔍 SocioMint BSC配置验证工具');
  console.log('='.repeat(40));
  
  try {
    // 加载环境变量
    log('blue', `📁 加载环境文件: ${envFile}`);
    const vars = await loadEnvFile(envPath);
    
    // 验证环境变量
    log('blue', '🔧 验证环境变量配置...');
    const envResults = validateEnvironmentVariables(vars);
    
    // 验证Wagmi配置
    log('blue', '🔧 验证Wagmi配置...');
    const wagmiResults = await validateWagmiConfig();
    
    // 生成报告
    const isValid = generateReport(envResults, wagmiResults, envFile);
    
    // 退出码
    process.exit(isValid ? 0 : 1);
    
  } catch (error) {
    log('red', `💥 验证失败: ${error.message}`);
    process.exit(1);
  }
}

// 运行主函数
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    log('red', `💥 脚本执行失败: ${error.message}`);
    process.exit(1);
  });
}
