/**
 * 福气系统功能测试脚本
 * 
 * 此脚本将测试：
 * 1. 福气账户创建和管理
 * 2. 每日签到功能
 * 3. 邀请奖励系统
 * 4. 分享奖励功能
 * 5. 福气等级计算
 * 6. API端点功能
 */

import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';

// 加载环境变量
config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

if (!supabaseUrl || !supabaseServiceKey) {
  throw new Error('Missing Supabase configuration');
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

interface TestResult {
  testName: string;
  passed: boolean;
  error?: string;
  details?: any;
}

interface TestSuite {
  suiteName: string;
  results: TestResult[];
  passed: number;
  failed: number;
}

/**
 * 主测试函数
 */
async function runFortuneSystemTests(): Promise<void> {
  console.log('🧪 开始福气系统功能测试...\n');

  const testSuites: TestSuite[] = [
    await testFortuneAccountManagement(),
    await testDailyCheckIn(),
    await testInviteRewards(),
    await testShareRewards(),
    await testFortuneLevels(),
    await testAPIEndpoints()
  ];

  // 打印测试结果
  printTestResults(testSuites);
}

/**
 * 测试福气账户管理
 */
async function testFortuneAccountManagement(): Promise<TestSuite> {
  const suite: TestSuite = {
    suiteName: '福气账户管理',
    results: [],
    passed: 0,
    failed: 0
  };

  // 创建测试用户
  const testUserId = crypto.randomUUID();

  // 测试1: 创建福气账户
  try {
    const { error } = await supabase
      .from('user_fortune')
      .insert({
        user_id: testUserId,
        available_fortune: 0,
        locked_fortune: 0,
        total_earned: 0,
        total_spent: 0,
        fortune_level: 1,
        fortune_level_name: '初来乍到'
      });

    suite.results.push({
      testName: '创建福气账户',
      passed: !error,
      error: error?.message
    });
  } catch (err) {
    suite.results.push({
      testName: '创建福气账户',
      passed: false,
      error: err instanceof Error ? err.message : 'Unknown error'
    });
  }

  // 测试2: 查询福气账户
  try {
    const { data, error } = await supabase
      .from('user_fortune')
      .select('*')
      .eq('user_id', testUserId)
      .single();

    suite.results.push({
      testName: '查询福气账户',
      passed: !error && data !== null,
      error: error?.message,
      details: data
    });
  } catch (err) {
    suite.results.push({
      testName: '查询福气账户',
      passed: false,
      error: err instanceof Error ? err.message : 'Unknown error'
    });
  }

  // 测试3: 更新福气余额
  try {
    const { error } = await supabase
      .from('user_fortune')
      .update({
        available_fortune: 100,
        total_earned: 100,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', testUserId);

    suite.results.push({
      testName: '更新福气余额',
      passed: !error,
      error: error?.message
    });
  } catch (err) {
    suite.results.push({
      testName: '更新福气余额',
      passed: false,
      error: err instanceof Error ? err.message : 'Unknown error'
    });
  }

  // 清理测试数据
  await supabase.from('user_fortune').delete().eq('user_id', testUserId);

  // 统计结果
  suite.passed = suite.results.filter(r => r.passed).length;
  suite.failed = suite.results.filter(r => !r.passed).length;

  return suite;
}

/**
 * 测试每日签到功能
 */
async function testDailyCheckIn(): Promise<TestSuite> {
  const suite: TestSuite = {
    suiteName: '每日签到功能',
    results: [],
    passed: 0,
    failed: 0
  };

  const testUserId = crypto.randomUUID();
  const today = new Date().toISOString().split('T')[0];

  // 先创建测试用户的福气账户
  await supabase.from('user_fortune').insert({
    user_id: testUserId,
    available_fortune: 0,
    locked_fortune: 0,
    total_earned: 0,
    total_spent: 0,
    fortune_level: 1,
    fortune_level_name: '初来乍到'
  });

  // 测试1: 创建签到记录
  try {
    const { error } = await supabase
      .from('daily_checkins')
      .insert({
        id: crypto.randomUUID(),
        user_id: testUserId,
        checkin_date: today,
        consecutive_days: 1,
        base_reward: 10,
        bonus_reward: 0,
        total_reward: 10
      });

    suite.results.push({
      testName: '创建签到记录',
      passed: !error,
      error: error?.message
    });
  } catch (err) {
    suite.results.push({
      testName: '创建签到记录',
      passed: false,
      error: err instanceof Error ? err.message : 'Unknown error'
    });
  }

  // 测试2: 查询签到记录
  try {
    const { data, error } = await supabase
      .from('daily_checkins')
      .select('*')
      .eq('user_id', testUserId)
      .eq('checkin_date', today)
      .single();

    suite.results.push({
      testName: '查询签到记录',
      passed: !error && data !== null,
      error: error?.message,
      details: data
    });
  } catch (err) {
    suite.results.push({
      testName: '查询签到记录',
      passed: false,
      error: err instanceof Error ? err.message : 'Unknown error'
    });
  }

  // 测试3: 检查重复签到限制
  try {
    const { error } = await supabase
      .from('daily_checkins')
      .insert({
        id: crypto.randomUUID(),
        user_id: testUserId,
        checkin_date: today,
        consecutive_days: 1,
        base_reward: 10,
        bonus_reward: 0,
        total_reward: 10
      });

    // 应该失败（违反唯一约束）
    suite.results.push({
      testName: '重复签到限制',
      passed: error !== null, // 期望有错误
      error: error ? '正确阻止重复签到' : '未能阻止重复签到'
    });
  } catch (err) {
    suite.results.push({
      testName: '重复签到限制',
      passed: true, // 抛出异常说明约束生效
      error: '正确阻止重复签到'
    });
  }

  // 清理测试数据
  await supabase.from('daily_checkins').delete().eq('user_id', testUserId);
  await supabase.from('user_fortune').delete().eq('user_id', testUserId);

  // 统计结果
  suite.passed = suite.results.filter(r => r.passed).length;
  suite.failed = suite.results.filter(r => !r.passed).length;

  return suite;
}

/**
 * 测试邀请奖励系统
 */
async function testInviteRewards(): Promise<TestSuite> {
  const suite: TestSuite = {
    suiteName: '邀请奖励系统',
    results: [],
    passed: 0,
    failed: 0
  };

  const inviterId = crypto.randomUUID();
  const inviteeId = crypto.randomUUID();

  // 创建测试用户的福气账户
  await supabase.from('user_fortune').insert([
    {
      user_id: inviterId,
      available_fortune: 0,
      locked_fortune: 0,
      total_earned: 0,
      total_spent: 0,
      fortune_level: 1,
      fortune_level_name: '初来乍到'
    },
    {
      user_id: inviteeId,
      available_fortune: 0,
      locked_fortune: 0,
      total_earned: 0,
      total_spent: 0,
      fortune_level: 1,
      fortune_level_name: '初来乍到'
    }
  ]);

  // 测试1: 创建邀请关系
  try {
    const { error } = await supabase
      .from('user_invitations')
      .insert({
        id: crypto.randomUUID(),
        inviter_id: inviterId,
        invitee_id: inviteeId,
        invite_code: 'TEST123',
        status: 'completed',
        reward_processed: true,
        processed_at: new Date().toISOString()
      });

    suite.results.push({
      testName: '创建邀请关系',
      passed: !error,
      error: error?.message
    });
  } catch (err) {
    suite.results.push({
      testName: '创建邀请关系',
      passed: false,
      error: err instanceof Error ? err.message : 'Unknown error'
    });
  }

  // 测试2: 记录邀请奖励交易
  try {
    const { error } = await supabase
      .from('fortune_transactions')
      .insert({
        id: crypto.randomUUID(),
        user_id: inviterId,
        transaction_type: 'invite_reward',
        amount: 1000,
        balance_before: 0,
        balance_after: 1000,
        reference_id: inviteeId,
        description: '邀请好友奖励 +1000福气'
      });

    suite.results.push({
      testName: '记录邀请奖励交易',
      passed: !error,
      error: error?.message
    });
  } catch (err) {
    suite.results.push({
      testName: '记录邀请奖励交易',
      passed: false,
      error: err instanceof Error ? err.message : 'Unknown error'
    });
  }

  // 清理测试数据
  await supabase.from('fortune_transactions').delete().eq('user_id', inviterId);
  await supabase.from('user_invitations').delete().eq('inviter_id', inviterId);
  await supabase.from('user_fortune').delete().in('user_id', [inviterId, inviteeId]);

  // 统计结果
  suite.passed = suite.results.filter(r => r.passed).length;
  suite.failed = suite.results.filter(r => !r.passed).length;

  return suite;
}

/**
 * 测试分享奖励功能
 */
async function testShareRewards(): Promise<TestSuite> {
  const suite: TestSuite = {
    suiteName: '分享奖励功能',
    results: [],
    passed: 0,
    failed: 0
  };

  const testUserId = crypto.randomUUID();
  const contentId = crypto.randomUUID();

  // 创建测试用户的福气账户
  await supabase.from('user_fortune').insert({
    user_id: testUserId,
    available_fortune: 0,
    locked_fortune: 0,
    total_earned: 0,
    total_spent: 0,
    fortune_level: 1,
    fortune_level_name: '初来乍到'
  });

  // 测试1: 创建分享奖励记录
  try {
    const { error } = await supabase
      .from('share_rewards')
      .insert({
        id: crypto.randomUUID(),
        user_id: testUserId,
        shared_content_type: 'bet',
        shared_content_id: contentId,
        platform: 'telegram',
        reward_amount: 20
      });

    suite.results.push({
      testName: '创建分享奖励记录',
      passed: !error,
      error: error?.message
    });
  } catch (err) {
    suite.results.push({
      testName: '创建分享奖励记录',
      passed: false,
      error: err instanceof Error ? err.message : 'Unknown error'
    });
  }

  // 测试2: 检查重复分享限制
  try {
    const { error } = await supabase
      .from('share_rewards')
      .insert({
        id: crypto.randomUUID(),
        user_id: testUserId,
        shared_content_type: 'bet',
        shared_content_id: contentId,
        platform: 'telegram',
        reward_amount: 20
      });

    // 应该失败（违反唯一约束）
    suite.results.push({
      testName: '重复分享限制',
      passed: error !== null,
      error: error ? '正确阻止重复分享' : '未能阻止重复分享'
    });
  } catch (err) {
    suite.results.push({
      testName: '重复分享限制',
      passed: true,
      error: '正确阻止重复分享'
    });
  }

  // 清理测试数据
  await supabase.from('share_rewards').delete().eq('user_id', testUserId);
  await supabase.from('user_fortune').delete().eq('user_id', testUserId);

  // 统计结果
  suite.passed = suite.results.filter(r => r.passed).length;
  suite.failed = suite.results.filter(r => !r.passed).length;

  return suite;
}

/**
 * 测试福气等级计算
 */
async function testFortuneLevels(): Promise<TestSuite> {
  const suite: TestSuite = {
    suiteName: '福气等级计算',
    results: [],
    passed: 0,
    failed: 0
  };

  // 测试1: 查询福气等级配置
  try {
    const { data, error } = await supabase
      .from('fortune_levels')
      .select('*')
      .order('level');

    const expectedLevels = [1, 2, 3, 4, 5];
    const actualLevels = data?.map(level => level.level) || [];

    suite.results.push({
      testName: '查询福气等级配置',
      passed: !error && JSON.stringify(expectedLevels) === JSON.stringify(actualLevels),
      error: error?.message,
      details: { expected: expectedLevels, actual: actualLevels }
    });
  } catch (err) {
    suite.results.push({
      testName: '查询福气等级配置',
      passed: false,
      error: err instanceof Error ? err.message : 'Unknown error'
    });
  }

  // 测试2: 等级计算逻辑
  const testCases = [
    { fortune: 500, expectedLevel: 1, expectedName: '初来乍到' },
    { fortune: 5000, expectedLevel: 2, expectedName: '小有福气' },
    { fortune: 50000, expectedLevel: 3, expectedName: '福气满满' },
    { fortune: 500000, expectedLevel: 4, expectedName: '福星高照' },
    { fortune: 5000000, expectedLevel: 5, expectedName: '福气无边' }
  ];

  for (const testCase of testCases) {
    const calculatedLevel = calculateFortuneLevel(testCase.fortune);
    const calculatedName = getFortuneLevelName(calculatedLevel);

    suite.results.push({
      testName: `等级计算 - ${testCase.fortune}福气`,
      passed: calculatedLevel === testCase.expectedLevel && calculatedName === testCase.expectedName,
      details: {
        fortune: testCase.fortune,
        expected: { level: testCase.expectedLevel, name: testCase.expectedName },
        actual: { level: calculatedLevel, name: calculatedName }
      }
    });
  }

  // 统计结果
  suite.passed = suite.results.filter(r => r.passed).length;
  suite.failed = suite.results.filter(r => !r.passed).length;

  return suite;
}

/**
 * 测试API端点
 */
async function testAPIEndpoints(): Promise<TestSuite> {
  const suite: TestSuite = {
    suiteName: 'API端点测试',
    results: [],
    passed: 0,
    failed: 0
  };

  // 注意：这里只能测试API的可访问性，不能测试需要认证的功能
  const apiEndpoints = [
    '/api/fortune/daily-checkin',
    '/api/fortune/invite-reward',
    '/api/fortune/share-reward'
  ];

  for (const endpoint of apiEndpoints) {
    try {
      const response = await fetch(`http://localhost:3000${endpoint}`, {
        method: 'GET'
      });

      // 期望401未授权错误（说明端点存在但需要认证）
      suite.results.push({
        testName: `API端点可访问性 - ${endpoint}`,
        passed: response.status === 401,
        details: { status: response.status, endpoint }
      });
    } catch (err) {
      suite.results.push({
        testName: `API端点可访问性 - ${endpoint}`,
        passed: false,
        error: err instanceof Error ? err.message : 'Unknown error'
      });
    }
  }

  // 统计结果
  suite.passed = suite.results.filter(r => r.passed).length;
  suite.failed = suite.results.filter(r => !r.passed).length;

  return suite;
}

/**
 * 打印测试结果
 */
function printTestResults(testSuites: TestSuite[]): void {
  console.log('\n📊 测试结果摘要:\n');

  let totalPassed = 0;
  let totalFailed = 0;

  testSuites.forEach(suite => {
    const status = suite.failed === 0 ? '✅' : '❌';
    console.log(`${status} ${suite.suiteName}: ${suite.passed}/${suite.passed + suite.failed} 通过`);

    suite.results.forEach(result => {
      const resultStatus = result.passed ? '  ✅' : '  ❌';
      console.log(`${resultStatus} ${result.testName}`);
      if (!result.passed && result.error) {
        console.log(`     错误: ${result.error}`);
      }
    });

    totalPassed += suite.passed;
    totalFailed += suite.failed;
    console.log('');
  });

  console.log(`🎯 总计: ${totalPassed}/${totalPassed + totalFailed} 测试通过`);
  
  if (totalFailed === 0) {
    console.log('🎉 所有测试通过！福气系统运行正常。');
  } else {
    console.log(`⚠️  有 ${totalFailed} 个测试失败，请检查相关功能。`);
  }
}

/**
 * 辅助函数
 */
function calculateFortuneLevel(totalFortune: number): number {
  if (totalFortune >= 1000000) return 5;
  if (totalFortune >= 100000) return 4;
  if (totalFortune >= 10000) return 3;
  if (totalFortune >= 1000) return 2;
  return 1;
}

function getFortuneLevelName(level: number): string {
  const levelNames = {
    1: '初来乍到',
    2: '小有福气',
    3: '福气满满',
    4: '福星高照',
    5: '福气无边'
  };
  return levelNames[level as keyof typeof levelNames] || '初来乍到';
}

/**
 * 执行测试
 */
if (require.main === module) {
  runFortuneSystemTests()
    .then(() => {
      console.log('\n🏁 测试完成！');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 测试执行失败:', error);
      process.exit(1);
    });
}

export { runFortuneSystemTests };
