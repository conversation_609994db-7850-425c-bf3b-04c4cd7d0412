import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const projectRoot = path.dirname(__dirname);

// 日志函数
function log(color, message) {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color]}${message}${colors.reset}`);
}

/**
 * 查找可能导致ENS错误的代码模式
 */
async function findENSErrorSources() {
  log('blue', '🔍 查找ENS错误源...');
  
  const problematicPatterns = [
    // 空字符串作为合约地址
    /new\s+ethers\.Contract\s*\(\s*['"]\s*['"]/g,
    // 空的环境变量
    /process\.env\.[A-Z_]+\s*\|\|\s*['"]\s*['"]/g,
    // 可能的空地址传递
    /Contract\s*\(\s*['"]\s*['"]/g,
  ];
  
  const filesToCheck = [
    'src/hooks/useHAOXContract.ts',
    'src/config/contracts.ts',
    'src/lib/wagmi.ts',
    'src/components/**/*.tsx',
    'src/app/**/*.tsx',
  ];
  
  const issues = [];
  
  for (const pattern of filesToCheck) {
    try {
      const files = await findFiles(pattern);
      for (const file of files) {
        const content = await fs.readFile(file, 'utf-8');
        
        for (const regex of problematicPatterns) {
          const matches = content.match(regex);
          if (matches) {
            issues.push({
              file,
              pattern: regex.toString(),
              matches
            });
          }
        }
      }
    } catch (error) {
      // 忽略文件不存在的错误
    }
  }
  
  return issues;
}

/**
 * 查找文件（简化版glob）
 */
async function findFiles(pattern) {
  if (pattern.includes('**')) {
    // 递归查找
    return await findFilesRecursive(path.dirname(pattern), path.basename(pattern));
  } else {
    // 直接文件
    const filePath = path.join(projectRoot, pattern);
    try {
      await fs.access(filePath);
      return [filePath];
    } catch {
      return [];
    }
  }
}

/**
 * 递归查找文件
 */
async function findFilesRecursive(dir, pattern) {
  const files = [];
  const fullDir = path.join(projectRoot, dir);
  
  try {
    const entries = await fs.readdir(fullDir, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = path.join(fullDir, entry.name);
      
      if (entry.isDirectory() && !entry.name.startsWith('.') && entry.name !== 'node_modules') {
        const subFiles = await findFilesRecursive(path.join(dir, entry.name), pattern);
        files.push(...subFiles);
      } else if (entry.isFile() && entry.name.match(pattern.replace('*', '.*'))) {
        files.push(fullPath);
      }
    }
  } catch (error) {
    // 忽略无法访问的目录
  }
  
  return files;
}

/**
 * 修复合约地址验证
 */
async function fixContractAddressValidation() {
  log('blue', '🔧 修复合约地址验证...');
  
  const fixes = [];
  
  // 修复useHAOXContract.ts中的地址验证
  const hookPath = path.join(projectRoot, 'src/hooks/useHAOXContract.ts');
  try {
    let content = await fs.readFile(hookPath, 'utf-8');
    let modified = false;
    
    // 确保所有合约实例化都有地址验证
    const contractCreationPattern = /new ethers\.Contract\([^,]+,/g;
    const matches = content.match(contractCreationPattern);
    
    if (matches) {
      // 添加更严格的地址验证
      const strictValidation = `
  // Validate contract address with strict checking
  const isValidAddress = (address: string): boolean => {
    return address && 
           address !== '' && 
           address !== '0x' && 
           address.length === 42 && 
           address.startsWith('0x') && 
           ethers.isAddress(address);
  };`;
      
      if (!content.includes('address !== \'0x\'')) {
        content = content.replace(
          /const isValidAddress = \(address: string\): boolean => \{[^}]+\};/,
          strictValidation.trim()
        );
        modified = true;
        fixes.push('增强地址验证逻辑');
      }
    }
    
    if (modified) {
      await fs.writeFile(hookPath, content);
    }
    
  } catch (error) {
    log('yellow', `⚠️ 无法修复 ${hookPath}: ${error.message}`);
  }
  
  return fixes;
}

/**
 * 修复环境变量默认值
 */
async function fixEnvironmentDefaults() {
  log('blue', '🔧 修复环境变量默认值...');
  
  const fixes = [];
  
  // 修复合约配置文件
  const contractsPath = path.join(projectRoot, 'src/config/contracts.ts');
  try {
    let content = await fs.readFile(contractsPath, 'utf-8');
    let modified = false;
    
    // 确保所有环境变量都有有效的默认值
    const envVarPattern = /process\.env\.NEXT_PUBLIC_HAOX_[A-Z_]+\s*\|\|\s*['"]/g;
    const matches = content.match(envVarPattern);
    
    if (matches) {
      // 替换可能的空默认值
      content = content.replace(
        /\|\|\s*['"]\s*['"]/g,
        "|| '0xe8f5D853F689e7798f7d8F4E0ACF3601191e6670'" // 使用有效的默认地址
      );
      modified = true;
      fixes.push('修复环境变量默认值');
    }
    
    if (modified) {
      await fs.writeFile(contractsPath, content);
    }
    
  } catch (error) {
    log('yellow', `⚠️ 无法修复 ${contractsPath}: ${error.message}`);
  }
  
  return fixes;
}

/**
 * 添加ENS错误防护
 */
async function addENSErrorGuards() {
  log('blue', '🛡️ 添加ENS错误防护...');
  
  const fixes = [];
  
  // 创建地址验证工具函数
  const utilsPath = path.join(projectRoot, 'src/lib/address-utils.ts');
  const utilsContent = `/**
 * 地址验证工具函数
 * 防止ENS错误
 */

import { ethers } from 'ethers';

/**
 * 严格验证以太坊地址
 */
export function isValidEthereumAddress(address: string): boolean {
  if (!address || typeof address !== 'string') {
    return false;
  }
  
  // 检查基本格式
  if (address === '' || address === '0x' || address.length !== 42) {
    return false;
  }
  
  // 检查是否以0x开头
  if (!address.startsWith('0x')) {
    return false;
  }
  
  // 使用ethers验证
  try {
    return ethers.isAddress(address);
  } catch {
    return false;
  }
}

/**
 * 安全创建合约实例
 */
export function safeCreateContract(
  address: string, 
  abi: any[], 
  provider: any
): any | null {
  if (!isValidEthereumAddress(address)) {
    console.warn('Invalid contract address:', address);
    return null;
  }
  
  try {
    return new ethers.Contract(address, abi, provider);
  } catch (error) {
    console.error('Failed to create contract:', error);
    return null;
  }
}

/**
 * 获取有效的合约地址
 */
export function getValidContractAddress(
  envAddress: string | undefined, 
  fallbackAddress: string
): string {
  if (isValidEthereumAddress(envAddress || '')) {
    return envAddress!;
  }
  
  if (isValidEthereumAddress(fallbackAddress)) {
    return fallbackAddress;
  }
  
  throw new Error('No valid contract address available');
}
`;
  
  try {
    await fs.writeFile(utilsPath, utilsContent);
    fixes.push('创建地址验证工具函数');
  } catch (error) {
    log('yellow', `⚠️ 无法创建工具函数: ${error.message}`);
  }
  
  return fixes;
}

/**
 * 验证修复结果
 */
async function verifyFixes() {
  log('blue', '✅ 验证修复结果...');
  
  const issues = await findENSErrorSources();
  
  if (issues.length === 0) {
    log('green', '✅ 未发现ENS错误源');
    return true;
  } else {
    log('yellow', `⚠️ 仍有 ${issues.length} 个潜在问题:`);
    issues.forEach(issue => {
      log('yellow', `   ${issue.file}: ${issue.matches.length} 个匹配`);
    });
    return false;
  }
}

/**
 * 主函数
 */
async function main() {
  log('cyan', '🧹 开始修复ENS错误...\n');
  
  try {
    // 1. 查找问题源
    const issues = await findENSErrorSources();
    if (issues.length > 0) {
      log('yellow', `发现 ${issues.length} 个潜在ENS错误源`);
    }
    
    // 2. 修复合约地址验证
    const validationFixes = await fixContractAddressValidation();
    
    // 3. 修复环境变量默认值
    const envFixes = await fixEnvironmentDefaults();
    
    // 4. 添加ENS错误防护
    const guardFixes = await addENSErrorGuards();
    
    // 5. 验证修复结果
    const isFixed = await verifyFixes();
    
    // 总结
    const allFixes = [...validationFixes, ...envFixes, ...guardFixes];
    
    log('cyan', '\n📋 修复总结');
    log('cyan', '='.repeat(50));
    
    if (allFixes.length > 0) {
      log('green', '✅ 完成的修复:');
      allFixes.forEach(fix => log('green', `   • ${fix}`));
    }
    
    if (isFixed) {
      log('green', '\n🎉 ENS错误修复完成！');
    } else {
      log('yellow', '\n⚠️ 可能仍有ENS相关问题，建议手动检查');
    }
    
    log('cyan', '\n📝 建议的后续步骤:');
    log('cyan', '1. 运行 npm run build 检查是否还有ENS错误');
    log('cyan', '2. 检查浏览器控制台是否有合约相关错误');
    log('cyan', '3. 确保所有环境变量都有有效值');
    
  } catch (error) {
    log('red', `❌ 修复失败: ${error.message}`);
    process.exit(1);
  }
}

// 运行主函数
main();
