#!/usr/bin/env node

/**
 * 构建测试脚本
 * 验证项目是否可以成功构建
 */

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

console.log('🔨 开始构建测试...');
console.log('项目根目录:', projectRoot);

// 设置环境变量
process.env.NODE_ENV = 'production';
process.env.NEXT_TELEMETRY_DISABLED = '1';

// 运行构建
const buildProcess = spawn('npm', ['run', 'build'], {
  cwd: projectRoot,
  stdio: 'inherit',
  env: {
    ...process.env,
    NODE_ENV: 'production',
    NEXT_TELEMETRY_DISABLED: '1',
  }
});

buildProcess.on('close', (code) => {
  if (code === 0) {
    console.log('✅ 构建成功！');
    process.exit(0);
  } else {
    console.log('❌ 构建失败，退出码:', code);
    process.exit(1);
  }
});

buildProcess.on('error', (error) => {
  console.error('❌ 构建过程出错:', error);
  process.exit(1);
});

// 设置超时
setTimeout(() => {
  console.log('⏰ 构建超时，终止进程...');
  buildProcess.kill('SIGTERM');
  setTimeout(() => {
    buildProcess.kill('SIGKILL');
  }, 5000);
}, 300000); // 5分钟超时
