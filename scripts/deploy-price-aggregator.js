const { ethers } = require("hardhat");

/**
 * 部署和配置HAOX价格聚合器
 * 支持多个价格源的聚合和故障转移
 */
async function main() {
    console.log("🚀 开始部署HAOXPriceAggregatorV2合约...");
    
    // 获取部署者账户
    const [deployer] = await ethers.getSigners();
    console.log("📝 部署者地址:", deployer.address);
    
    // 检查余额
    const balance = await deployer.provider.getBalance(deployer.address);
    console.log("💰 部署者余额:", ethers.formatEther(balance), "BNB");
    
    try {
        // 获取合约工厂
        console.log("📦 获取合约工厂...");
        const HAOXPriceAggregatorV2 = await ethers.getContractFactory("HAOXPriceAggregatorV2");
        
        // 估算部署Gas
        console.log("⛽ 估算部署Gas...");
        const deploymentData = HAOXPriceAggregatorV2.interface.encodeDeploy([]);
        
        const gasEstimate = await deployer.estimateGas({
            data: deploymentData
        });
        
        console.log("⛽ 预估Gas使用:", gasEstimate.toString());
        
        // 获取当前Gas价格
        const feeData = await deployer.provider.getFeeData();
        const gasPrice = feeData.gasPrice;
        console.log("💰 当前Gas价格:", ethers.formatUnits(gasPrice, "gwei"), "Gwei");
        
        // 计算部署成本
        const deploymentCost = gasEstimate * gasPrice;
        console.log("💸 预估部署成本:", ethers.formatEther(deploymentCost), "BNB");
        
        // 部署合约
        console.log("🚀 开始部署合约...");
        const aggregatorContract = await HAOXPriceAggregatorV2.deploy({
            gasLimit: gasEstimate * 120n / 100n, // 增加20%缓冲
            gasPrice: gasPrice
        });
        
        console.log("⏳ 等待合约部署确认...");
        await aggregatorContract.waitForDeployment();
        
        const contractAddress = await aggregatorContract.getAddress();
        console.log("✅ 合约部署成功!");
        console.log("📍 合约地址:", contractAddress);
        
        // 获取部署交易信息
        const deployTx = aggregatorContract.deploymentTransaction();
        console.log("📤 部署交易哈希:", deployTx.hash);
        
        // 等待更多确认
        console.log("⏳ 等待交易确认...");
        const receipt = await deployTx.wait(2); // 等待2个确认
        
        console.log("✅ 交易已确认!");
        console.log("⛽ 实际Gas使用:", receipt.gasUsed.toString());
        console.log("💸 实际部署成本:", ethers.formatEther(receipt.gasUsed * receipt.gasPrice), "BNB");
        
        // 验证合约部署
        console.log("🔍 验证合约部署...");
        
        const maxDeviation = await aggregatorContract.MAX_PRICE_DEVIATION();
        const stalenessThreshold = await aggregatorContract.PRICE_STALENESS_THRESHOLD();
        const minSources = await aggregatorContract.MIN_SOURCES_REQUIRED();
        const maxSources = await aggregatorContract.MAX_SOURCES();
        
        console.log("✅ 合约验证通过:");
        console.log("   最大价格偏差:", maxDeviation.toString(), "基点");
        console.log("   价格过期阈值:", stalenessThreshold.toString(), "秒");
        console.log("   最小源数量:", minSources.toString());
        console.log("   最大源数量:", maxSources.toString());
        
        // 配置价格源
        console.log("🔧 配置价格源...");
        await configurePriceSources(aggregatorContract);
        
        // 生成部署报告
        const deploymentReport = {
            contractName: "HAOXPriceAggregatorV2",
            contractAddress: contractAddress,
            deploymentTxHash: deployTx.hash,
            deployer: deployer.address,
            deploymentTime: new Date().toISOString(),
            gasUsed: receipt.gasUsed.toString(),
            gasPrice: receipt.gasPrice.toString(),
            deploymentCost: ethers.formatEther(receipt.gasUsed * receipt.gasPrice),
            blockNumber: receipt.blockNumber,
            configuration: {
                maxDeviation: maxDeviation.toString(),
                stalenessThreshold: stalenessThreshold.toString(),
                minSources: minSources.toString(),
                maxSources: maxSources.toString()
            }
        };
        
        // 保存部署报告
        const fs = require('fs');
        const path = require('path');
        
        const reportsDir = path.join(__dirname, '../deployment-reports');
        if (!fs.existsSync(reportsDir)) {
            fs.mkdirSync(reportsDir, { recursive: true });
        }
        
        const reportFile = path.join(reportsDir, `HAOXPriceAggregatorV2-${Date.now()}.json`);
        fs.writeFileSync(reportFile, JSON.stringify(deploymentReport, null, 2));
        
        console.log("📄 部署报告已保存:", reportFile);
        
        // 更新环境变量建议
        console.log("\n🔧 环境变量更新建议:");
        console.log(`NEXT_PUBLIC_HAOX_PRICE_AGGREGATOR_ADDRESS=${contractAddress}`);
        
        return {
            contractAddress,
            deploymentTxHash: deployTx.hash,
            deploymentReport
        };
        
    } catch (error) {
        console.error("❌ 部署失败:", error);
        throw error;
    }
}

/**
 * 配置价格源
 */
async function configurePriceSources(aggregatorContract) {
    console.log("📊 开始配置价格源...");
    
    // 价格源配置
    const priceSources = [
        {
            oracle: process.env.NEXT_PUBLIC_HAOX_PRICE_ORACLE_ADDRESS,
            name: "HAOX Primary Oracle",
            weight: 40,
            maxFailures: 3
        },
        {
            oracle: "******************************************", // Chainlink BNB/USD
            name: "Chainlink BNB/USD",
            weight: 30,
            maxFailures: 5
        },
        {
            oracle: "******************************************", // Chainlink BTC/USD (作为参考)
            name: "Chainlink BTC/USD Reference",
            weight: 20,
            maxFailures: 5
        }
        // 注意：实际部署时需要替换为真实的预言机地址
    ];
    
    // 添加价格源
    for (let i = 0; i < priceSources.length; i++) {
        const source = priceSources[i];
        
        if (!source.oracle || source.oracle === "******************************************") {
            console.log(`⚠️  跳过无效的价格源: ${source.name}`);
            continue;
        }
        
        try {
            console.log(`📊 添加价格源: ${source.name}`);
            
            const tx = await aggregatorContract.addPriceSource(
                source.oracle,
                source.name,
                source.weight,
                source.maxFailures
            );
            
            console.log(`📤 交易已发送: ${tx.hash}`);
            await tx.wait();
            
            console.log(`✅ 价格源添加成功: ${source.name}`);
            
        } catch (error) {
            console.error(`❌ 添加价格源失败 ${source.name}:`, error.message);
        }
    }
    
    // 验证配置
    console.log("🔍 验证价格源配置...");
    
    try {
        const sourceCount = await aggregatorContract.sourceCount();
        const activeCount = await aggregatorContract.getActiveSourceCount();
        
        console.log("✅ 价格源配置验证:");
        console.log("   总源数量:", sourceCount.toString());
        console.log("   活跃源数量:", activeCount.toString());
        
        if (activeCount >= 2) {
            console.log("✅ 价格源配置满足最小要求");
        } else {
            console.warn("⚠️  活跃价格源数量不足，可能影响聚合功能");
        }
        
        // 获取所有源信息
        if (sourceCount > 0) {
            const sourcesInfo = await aggregatorContract.getAllSources();
            console.log("📊 已配置的价格源:");
            
            for (let i = 0; i < sourceCount; i++) {
                console.log(`   源${i}: ${sourcesInfo[1][i]} (权重: ${sourcesInfo[2][i]}, 活跃: ${sourcesInfo[3][i]})`);
            }
        }
        
    } catch (error) {
        console.error("❌ 验证价格源配置失败:", error);
    }
}

/**
 * 测试价格聚合功能
 */
async function testPriceAggregation(aggregatorContract) {
    console.log("🧪 测试价格聚合功能...");
    
    try {
        // 尝试更新聚合价格
        console.log("📊 更新聚合价格...");
        const updateTx = await aggregatorContract.updateAggregatedPrice();
        console.log("📤 更新交易已发送:", updateTx.hash);
        
        await updateTx.wait();
        console.log("✅ 价格更新成功");
        
        // 获取最新价格
        const latestPrice = await aggregatorContract.getLatestPrice();
        const lastUpdateTime = await aggregatorContract.getLastUpdateTime();
        
        console.log("📊 聚合结果:");
        console.log("   最新价格:", ethers.formatUnits(latestPrice, 8), "USD");
        console.log("   更新时间:", new Date(Number(lastUpdateTime) * 1000).toLocaleString());
        
        // 获取聚合器状态
        const status = await aggregatorContract.getAggregatorStatus();
        console.log("📊 聚合器状态:");
        console.log("   总源数量:", status[0].toString());
        console.log("   活跃源数量:", status[1].toString());
        console.log("   紧急模式:", status[2]);
        console.log("   置信度:", status[5].toString(), "%");
        
    } catch (error) {
        console.error("❌ 价格聚合测试失败:", error.message);
        
        if (error.message.includes("Insufficient valid sources")) {
            console.log("💡 提示: 需要至少2个有效的价格源才能进行聚合");
        }
    }
}

// 错误处理
process.on('unhandledRejection', (error) => {
    console.error('❌ 未处理的错误:', error);
    process.exit(1);
});

// 如果直接运行此脚本
if (require.main === module) {
    main()
        .then(async (result) => {
            console.log("\n🎉 部署完成!");
            console.log("合约地址:", result.contractAddress);
            
            // 可选：运行测试
            if (process.env.RUN_TESTS === 'true') {
                const aggregatorContract = await ethers.getContractAt(
                    "HAOXPriceAggregatorV2",
                    result.contractAddress
                );
                await testPriceAggregation(aggregatorContract);
            }
            
            process.exit(0);
        })
        .catch((error) => {
            console.error("❌ 部署失败:", error);
            process.exit(1);
        });
}

module.exports = { main, configurePriceSources, testPriceAggregation };
