#!/bin/bash

echo "🛡️  SocioMint V2.1 安全审计开始..."
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 创建审计报告目录
mkdir -p audit-reports
REPORT_DIR="audit-reports/$(date +%Y%m%d_%H%M%S)"
mkdir -p "$REPORT_DIR"

echo -e "${BLUE}📁 审计报告将保存到: $REPORT_DIR${NC}"

# 1. Solhint 静态分析
echo -e "\n${YELLOW}🔍 步骤1: Solhint 静态安全分析${NC}"
echo "分析核心合约..."

CONTRACTS=(
    "contracts/HAOXVestingV2Minimal.sol"
    "contracts/HAOXVestingV2Ultra.sol" 
    "contracts/HAOXPriceAggregatorMinimal.sol"
    "contracts/HAOXTokenV2.sol"
    "contracts/HAOXPresaleV2.sol"
    "contracts/HAOXInvitationV2.sol"
    "contracts/HAOXPriceOracleV2.sol"
)

for contract in "${CONTRACTS[@]}"; do
    echo "  📄 分析: $contract"
    npx solhint "$contract" --max-warnings 0 > "$REPORT_DIR/solhint_$(basename $contract .sol).txt" 2>&1
    if [ $? -eq 0 ]; then
        echo -e "    ${GREEN}✅ 通过${NC}"
    else
        echo -e "    ${RED}⚠️  发现问题${NC}"
    fi
done

# 2. 合约编译检查
echo -e "\n${YELLOW}🔍 步骤2: 合约编译和大小检查${NC}"
cd contracts
npx hardhat compile > "$REPORT_DIR/compile_report.txt" 2>&1
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ 编译成功${NC}"
else
    echo -e "${RED}❌ 编译失败${NC}"
fi

# 3. Gas 使用分析
echo -e "\n${YELLOW}🔍 步骤3: Gas 使用分析${NC}"
REPORT_GAS=true npx hardhat compile > "$REPORT_DIR/gas_report.txt" 2>&1
echo -e "${GREEN}✅ Gas分析完成${NC}"

# 4. 测试覆盖率检查
echo -e "\n${YELLOW}🔍 步骤4: 测试覆盖率分析${NC}"
npx hardhat coverage > "$REPORT_DIR/coverage_report.txt" 2>&1
echo -e "${GREEN}✅ 覆盖率分析完成${NC}"

cd ..

# 5. 生成综合报告
echo -e "\n${YELLOW}📊 生成综合安全报告${NC}"

cat > "$REPORT_DIR/security_audit_summary.md" << EOF
# SocioMint V2.1 安全审计报告

**审计日期**: $(date)
**审计工具**: Solhint, Hardhat, Solidity Coverage
**审计范围**: 7个核心智能合约

## 📋 审计概要

### 审计的合约
1. HAOXVestingV2Minimal.sol - 精简版解锁合约
2. HAOXVestingV2Ultra.sol - 超精简版解锁合约  
3. HAOXPriceAggregatorMinimal.sol - 价格聚合器
4. HAOXTokenV2.sol - ERC20代币合约
5. HAOXPresaleV2.sol - 预售合约
6. HAOXInvitationV2.sol - 邀请奖励合约
7. HAOXPriceOracleV2.sol - 价格预言机

## 🔍 审计结果

### Solhint 静态分析
- 详细结果请查看各合约的 solhint_*.txt 文件

### 编译检查
- 详细结果请查看 compile_report.txt

### Gas 使用分析  
- 详细结果请查看 gas_report.txt

### 测试覆盖率
- 详细结果请查看 coverage_report.txt

## ⚠️ 发现的问题

### 🔴 高优先级问题
- 待人工审查确认

### 🟡 中优先级问题  
- 待人工审查确认

### 🟢 低优先级问题
- 待人工审查确认

## 📝 建议

1. **增加测试覆盖率**: 当前测试覆盖率较低，建议补充单元测试
2. **人工代码审查**: 建议进行人工代码审查，特别关注业务逻辑
3. **第三方审计**: 考虑使用其他专业审计工具进行补充审计

## 🎯 下一步行动

1. 审查本报告中发现的所有问题
2. 修复高优先级和中优先级问题
3. 补充测试用例提高覆盖率
4. 考虑进行专业第三方审计

---
*本报告由自动化工具生成，建议结合人工审查使用*
EOF

echo -e "\n${GREEN}🎉 安全审计完成！${NC}"
echo -e "${BLUE}📊 审计报告位置: $REPORT_DIR${NC}"
echo -e "${BLUE}📄 查看综合报告: $REPORT_DIR/security_audit_summary.md${NC}"

# 显示报告文件列表
echo -e "\n${YELLOW}📁 生成的报告文件:${NC}"
ls -la "$REPORT_DIR"

echo -e "\n${YELLOW}💡 建议:${NC}"
echo "1. 仔细审查所有报告文件"
echo "2. 重点关注 Solhint 发现的安全问题"
echo "3. 补充测试用例提高覆盖率"
echo "4. 考虑使用专业审计服务"
