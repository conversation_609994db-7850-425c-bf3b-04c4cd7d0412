#!/usr/bin/env node

/**
 * 配置错误修复脚本
 * 自动检测和修复项目中的配置错误
 */

import { readFile, writeFile } from 'fs/promises';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

// 颜色定义
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

/**
 * 修复环境变量不一致问题
 */
async function fixEnvInconsistencies() {
  log('blue', '🔧 修复环境变量不一致问题...');
  
  const envFiles = ['.env.example', '.env.production', '.env.staging'];
  const fixes = [];
  
  for (const envFile of envFiles) {
    const envPath = join(projectRoot, envFile);
    
    try {
      let content = await readFile(envPath, 'utf-8');
      let modified = false;
      
      // 修复1: 统一合约地址变量名
      const contractAddressReplacements = [
        {
          old: 'NEXT_PUBLIC_HAOX_CONTRACT_ADDRESS_BSC',
          new: 'NEXT_PUBLIC_HAOX_TOKEN_ADDRESS'
        },
        {
          old: 'NEXT_PUBLIC_HAOX_CONTRACT_ADDRESS_BSC_TESTNET',
          new: 'NEXT_PUBLIC_HAOX_TOKEN_ADDRESS'
        },
        {
          old: 'NEXT_PUBLIC_HAOX_CONTRACT_ADDRESS_MAINNET',
          new: 'NEXT_PUBLIC_HAOX_TOKEN_ADDRESS'
        },
        {
          old: 'NEXT_PUBLIC_HAOX_CONTRACT_ADDRESS_SEPOLIA',
          new: 'NEXT_PUBLIC_HAOX_TOKEN_ADDRESS'
        },
        {
          old: 'NEXT_PUBLIC_HAOX_CONTRACT_ADDRESS_POLYGON',
          new: 'NEXT_PUBLIC_HAOX_TOKEN_ADDRESS'
        }
      ];
      
      contractAddressReplacements.forEach(({ old, new: newVar }) => {
        if (content.includes(old)) {
          content = content.replace(new RegExp(old, 'g'), newVar);
          modified = true;
          fixes.push(`${envFile}: 统一合约地址变量名 ${old} -> ${newVar}`);
        }
      });
      
      // 修复2: 移除非BSC相关的配置
      const nonBscVars = [
        'NEXT_PUBLIC_HAOX_CONTRACT_ADDRESS_POLYGON',
        'NEXT_PUBLIC_HAOX_CONTRACT_ADDRESS_SEPOLIA',
        'NEXT_PUBLIC_HAOX_CONTRACT_ADDRESS_MAINNET',
        'CHAINLINK_ETH_USD_FEED'
      ];
      
      nonBscVars.forEach(varName => {
        const regex = new RegExp(`^${varName}=.*$`, 'gm');
        if (regex.test(content)) {
          content = content.replace(regex, '');
          modified = true;
          fixes.push(`${envFile}: 移除非BSC变量 ${varName}`);
        }
      });
      
      // 修复3: 确保必需的BSC变量存在
      const requiredBscVars = [
        'NEXT_PUBLIC_CHAIN_ID=97',
        'NEXT_PUBLIC_RPC_URL=https://data-seed-prebsc-1-s1.binance.org:8545/',
        'NEXT_PUBLIC_BLOCK_EXPLORER=https://testnet.bscscan.com'
      ];
      
      requiredBscVars.forEach(varLine => {
        const [varName] = varLine.split('=');
        if (!content.includes(varName)) {
          content += `\n# BSC Configuration\n${varLine}\n`;
          modified = true;
          fixes.push(`${envFile}: 添加必需的BSC变量 ${varName}`);
        }
      });
      
      // 修复4: 清理重复的空行
      content = content.replace(/\n\s*\n\s*\n/g, '\n\n');
      
      if (modified) {
        await writeFile(envPath, content);
        log('green', `✅ 修复了 ${envFile}`);
      }
      
    } catch (error) {
      log('yellow', `⚠️ 无法处理 ${envFile}: ${error.message}`);
    }
  }
  
  return fixes;
}

/**
 * 修复Wagmi配置中的环境变量引用
 */
async function fixWagmiConfig() {
  log('blue', '🔧 修复Wagmi配置...');
  
  const wagmiPath = join(projectRoot, 'src/lib/wagmi.ts');
  const fixes = [];
  
  try {
    let content = await readFile(wagmiPath, 'utf-8');
    let modified = false;
    
    // 修复1: 统一合约地址环境变量引用
    const oldReferences = [
      'NEXT_PUBLIC_HAOX_CONTRACT_ADDRESS_BSC',
      'NEXT_PUBLIC_HAOX_CONTRACT_ADDRESS_BSC_TESTNET'
    ];
    
    oldReferences.forEach(oldRef => {
      if (content.includes(oldRef)) {
        content = content.replace(
          new RegExp(oldRef, 'g'),
          'NEXT_PUBLIC_HAOX_TOKEN_ADDRESS'
        );
        modified = true;
        fixes.push(`统一环境变量引用: ${oldRef} -> NEXT_PUBLIC_HAOX_TOKEN_ADDRESS`);
      }
    });
    
    // 修复2: 确保正确的默认合约地址
    const defaultAddress = '0xe8f5D853F689e7798f7d8F4E0ACF3601191e6670';
    if (!content.includes(defaultAddress)) {
      content = content.replace(
        /process\.env\.NEXT_PUBLIC_HAOX_TOKEN_ADDRESS \|\| '[^']*'/g,
        `process.env.NEXT_PUBLIC_HAOX_TOKEN_ADDRESS || '${defaultAddress}'`
      );
      modified = true;
      fixes.push('更新默认合约地址');
    }
    
    if (modified) {
      await writeFile(wagmiPath, content);
      log('green', '✅ 修复了Wagmi配置');
    }
    
  } catch (error) {
    log('red', `❌ 修复Wagmi配置失败: ${error.message}`);
  }
  
  return fixes;
}

/**
 * 修复合约配置文件
 */
async function fixContractConfig() {
  log('blue', '🔧 修复合约配置...');
  
  const contractPath = join(projectRoot, 'src/config/contracts.ts');
  const fixes = [];
  
  try {
    let content = await readFile(contractPath, 'utf-8');
    let modified = false;
    
    // 修复1: 确保环境变量引用一致
    const envVarReplacements = [
      {
        old: 'process.env.NEXT_PUBLIC_HAOX_CONTRACT_ADDRESS',
        new: 'process.env.NEXT_PUBLIC_HAOX_TOKEN_ADDRESS'
      }
    ];
    
    envVarReplacements.forEach(({ old, new: newRef }) => {
      if (content.includes(old)) {
        content = content.replace(new RegExp(old.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), newRef);
        modified = true;
        fixes.push(`统一环境变量引用: ${old} -> ${newRef}`);
      }
    });
    
    // 修复2: 确保合约地址格式正确
    const addressPattern = /HAOX_TOKEN: '[^']*'/g;
    const correctAddress = "HAOX_TOKEN: '0xe8f5D853F689e7798f7d8F4E0ACF3601191e6670'";
    
    if (addressPattern.test(content)) {
      content = content.replace(addressPattern, correctAddress);
      modified = true;
      fixes.push('统一HAOX代币合约地址');
    }
    
    if (modified) {
      await writeFile(contractPath, content);
      log('green', '✅ 修复了合约配置');
    }
    
  } catch (error) {
    log('red', `❌ 修复合约配置失败: ${error.message}`);
  }
  
  return fixes;
}

/**
 * 修复环境验证配置
 */
async function fixEnvValidationConfig() {
  log('blue', '🔧 修复环境验证配置...');
  
  const envValidationPath = join(projectRoot, 'src/lib/env-validation.ts');
  const fixes = [];
  
  try {
    let content = await readFile(envValidationPath, 'utf-8');
    let modified = false;
    
    // 修复1: 更新环境变量名称
    const varNameUpdates = [
      {
        old: 'NEXT_PUBLIC_ALCHEMY_API_KEY',
        new: 'NEXT_PUBLIC_HAOX_TOKEN_ADDRESS'
      }
    ];
    
    varNameUpdates.forEach(({ old, new: newName }) => {
      if (content.includes(`'${old}'`)) {
        content = content.replace(`'${old}'`, `'${newName}'`);
        modified = true;
        fixes.push(`更新环境变量验证: ${old} -> ${newName}`);
      }
    });
    
    // 修复2: 添加BSC相关的验证配置
    const bscValidationConfig = `
  // BSC 配置
  {
    name: 'NEXT_PUBLIC_CHAIN_ID',
    required: true,
    validator: (value) => ['56', '97'].includes(value),
    description: 'BSC 链 ID (56=主网, 97=测试网)',
  },
  {
    name: 'NEXT_PUBLIC_HAOX_TOKEN_ADDRESS',
    required: true,
    validator: (value) => value.startsWith('0x') && value.length === 42,
    description: 'HAOX 代币合约地址',
  },
  {
    name: 'NEXT_PUBLIC_HAOX_PRESALE_ADDRESS',
    required: true,
    validator: (value) => value.startsWith('0x') && value.length === 42,
    description: 'HAOX 预售合约地址',
  },`;
    
    if (!content.includes('NEXT_PUBLIC_HAOX_TOKEN_ADDRESS')) {
      // 在ENV_CONFIGS数组中添加BSC配置
      content = content.replace(
        /\/\/ 应用配置/,
        `${bscValidationConfig}\n\n  // 应用配置`
      );
      modified = true;
      fixes.push('添加BSC相关的环境变量验证配置');
    }
    
    if (modified) {
      await writeFile(envValidationPath, content);
      log('green', '✅ 修复了环境验证配置');
    }
    
  } catch (error) {
    log('red', `❌ 修复环境验证配置失败: ${error.message}`);
  }
  
  return fixes;
}

/**
 * 修复TypeScript类型定义
 */
async function fixTypeDefinitions() {
  log('blue', '🔧 检查TypeScript类型定义...');
  
  const fixes = [];
  
  // 检查是否需要创建全局类型定义
  const globalTypesPath = join(projectRoot, 'src/types/global.d.ts');
  
  try {
    await readFile(globalTypesPath, 'utf-8');
    log('green', '✅ 全局类型定义文件已存在');
  } catch (error) {
    // 创建全局类型定义文件
    const globalTypesContent = `/**
 * 全局类型定义
 */

declare global {
  namespace NodeJS {
    interface ProcessEnv {
      // 基础配置
      NODE_ENV: 'development' | 'production' | 'test';
      NEXT_PUBLIC_APP_URL: string;
      
      // Supabase 配置
      NEXT_PUBLIC_SUPABASE_URL: string;
      NEXT_PUBLIC_SUPABASE_ANON_KEY: string;
      SUPABASE_SERVICE_ROLE_KEY: string;
      
      // BSC 配置
      NEXT_PUBLIC_CHAIN_ID: string;
      NEXT_PUBLIC_RPC_URL: string;
      NEXT_PUBLIC_HAOX_TOKEN_ADDRESS: string;
      NEXT_PUBLIC_HAOX_PRESALE_ADDRESS: string;
      NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID?: string;
      
      // Telegram 配置
      TELEGRAM_BOT_TOKEN: string;
      TELEGRAM_BOT_USERNAME: string;
      NEXT_PUBLIC_TELEGRAM_BOT_USERNAME: string;
      
      // 安全配置
      JWT_SECRET: string;
      NEXTAUTH_SECRET: string;
      
      // 可选配置
      SENTRY_DSN?: string;
      NEXT_PUBLIC_GA_ID?: string;
    }
  }
}

export {};
`;
    
    await writeFile(globalTypesPath, globalTypesContent);
    fixes.push('创建全局TypeScript类型定义文件');
    log('green', '✅ 创建了全局类型定义文件');
  }
  
  return fixes;
}

/**
 * 验证修复结果
 */
async function validateFixes() {
  log('blue', '🔍 验证修复结果...');
  
  const validationResults = [];
  
  // 验证环境变量一致性
  const envFiles = ['.env.example', '.env.production', '.env.staging'];
  
  for (const envFile of envFiles) {
    const envPath = join(projectRoot, envFile);
    
    try {
      const content = await readFile(envPath, 'utf-8');
      
      const checks = {
        hasUnifiedTokenAddress: content.includes('NEXT_PUBLIC_HAOX_TOKEN_ADDRESS'),
        hasChainId: content.includes('NEXT_PUBLIC_CHAIN_ID'),
        hasRpcUrl: content.includes('NEXT_PUBLIC_RPC_URL'),
        noOldContractVars: !content.includes('NEXT_PUBLIC_HAOX_CONTRACT_ADDRESS_BSC')
      };
      
      const passed = Object.values(checks).every(check => check);
      validationResults.push({
        file: envFile,
        passed,
        checks
      });
      
      if (passed) {
        log('green', `✅ ${envFile} 验证通过`);
      } else {
        log('yellow', `⚠️ ${envFile} 仍有问题`);
      }
      
    } catch (error) {
      log('yellow', `⚠️ 无法验证 ${envFile}: ${error.message}`);
    }
  }
  
  return validationResults;
}

/**
 * 生成修复报告
 */
function generateFixReport(allFixes, validationResults) {
  log('cyan', '\n📊 配置修复报告');
  log('cyan', '='.repeat(50));
  
  if (allFixes.length === 0) {
    log('green', '🎉 没有发现需要修复的配置错误！');
    return;
  }
  
  log('blue', `📝 总共修复了 ${allFixes.length} 个配置问题:`);
  allFixes.forEach((fix, index) => {
    log('green', `  ${index + 1}. ${fix}`);
  });
  
  // 验证结果
  log('cyan', '\n🔍 验证结果:');
  const allPassed = validationResults.every(result => result.passed);
  
  if (allPassed) {
    log('green', '✅ 所有配置文件验证通过');
  } else {
    log('yellow', '⚠️ 部分配置文件仍需要手动检查');
    validationResults.forEach(result => {
      if (!result.passed) {
        log('yellow', `  - ${result.file}: 需要进一步检查`);
      }
    });
  }
  
  // 建议
  log('cyan', '\n💡 建议:');
  log('blue', '  1. 运行 node scripts/validate-env.js 验证环境变量');
  log('blue', '  2. 运行 node scripts/validate-bsc-config.js 验证BSC配置');
  log('blue', '  3. 运行 npm run type-check 检查TypeScript类型');
  log('blue', '  4. 运行 npm run build 测试构建');
}

/**
 * 主函数
 */
async function main() {
  console.log('🔧 SocioMint 配置错误修复工具');
  console.log('='.repeat(40));
  
  try {
    const allFixes = [];
    
    // 执行各项修复
    log('magenta', '开始修复配置错误...\n');
    
    const envFixes = await fixEnvInconsistencies();
    allFixes.push(...envFixes);
    
    const wagmiFixes = await fixWagmiConfig();
    allFixes.push(...wagmiFixes);
    
    const contractFixes = await fixContractConfig();
    allFixes.push(...contractFixes);
    
    const envValidationFixes = await fixEnvValidationConfig();
    allFixes.push(...envValidationFixes);
    
    const typeFixes = await fixTypeDefinitions();
    allFixes.push(...typeFixes);
    
    // 验证修复结果
    const validationResults = await validateFixes();
    
    // 生成报告
    generateFixReport(allFixes, validationResults);
    
    log('cyan', '\n🎉 配置修复完成！');
    
  } catch (error) {
    log('red', `💥 配置修复失败: ${error.message}`);
    process.exit(1);
  }
}

// 运行主函数
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error('💥 脚本执行失败:', error.message);
    process.exit(1);
  });
}
