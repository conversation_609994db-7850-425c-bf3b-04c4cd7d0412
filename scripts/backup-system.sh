#!/bin/bash

# SocioMint 系统备份脚本
# 执行完整的系统备份，包括数据库、配置和代码

set -e

# 配置变量
BACKUP_ROOT="/backup"
DATE=$(date +%Y%m%d_%H%M%S)
LOG_FILE="/var/log/sociomint/backup_${DATE}.log"
RETENTION_DAYS=90

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

log_success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS:${NC} $1" | tee -a "$LOG_FILE"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1" | tee -a "$LOG_FILE"
}

# 检查依赖
check_dependencies() {
    log "检查备份依赖..."
    
    local deps=("pg_dump" "aws" "gpg" "tar" "gzip")
    local missing=()
    
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            missing+=("$dep")
        fi
    done
    
    if [ ${#missing[@]} -ne 0 ]; then
        log_error "缺少依赖: ${missing[*]}"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 创建备份目录
create_backup_dirs() {
    log "创建备份目录..."
    
    local dirs=(
        "$BACKUP_ROOT/database"
        "$BACKUP_ROOT/config"
        "$BACKUP_ROOT/assets"
        "$BACKUP_ROOT/logs"
        "$BACKUP_ROOT/temp"
    )
    
    for dir in "${dirs[@]}"; do
        mkdir -p "$dir"
    done
    
    log_success "备份目录创建完成"
}

# 备份数据库
backup_database() {
    log "开始数据库备份..."
    
    local backup_file="$BACKUP_ROOT/database/sociomint_db_${DATE}.sql"
    
    if [ -z "$DATABASE_URL" ]; then
        log_error "DATABASE_URL 环境变量未设置"
        return 1
    fi
    
    # 执行数据库备份
    if pg_dump "$DATABASE_URL" > "$backup_file"; then
        log_success "数据库备份完成: $backup_file"
        
        # 压缩备份
        gzip "$backup_file"
        log_success "数据库备份压缩完成"
        
        # 加密备份
        if [ -n "$BACKUP_ENCRYPTION_KEY" ]; then
            gpg --batch --yes --passphrase "$BACKUP_ENCRYPTION_KEY" \
                --cipher-algo AES256 --symmetric "${backup_file}.gz"
            rm "${backup_file}.gz"
            log_success "数据库备份加密完成"
        fi
        
        return 0
    else
        log_error "数据库备份失败"
        return 1
    fi
}

# 备份配置文件
backup_config() {
    log "开始配置备份..."
    
    local config_backup="$BACKUP_ROOT/config/config_${DATE}.tar.gz"
    local temp_dir="$BACKUP_ROOT/temp/config_${DATE}"
    
    mkdir -p "$temp_dir"
    
    # 备份环境变量
    if [ -f ".env.production" ]; then
        cp ".env.production" "$temp_dir/"
    fi
    
    if [ -f ".env.staging" ]; then
        cp ".env.staging" "$temp_dir/"
    fi
    
    # 备份 wrangler 配置
    if [ -f "wrangler.toml" ]; then
        cp "wrangler.toml" "$temp_dir/"
    fi
    
    # 备份 package.json 和 lock 文件
    if [ -f "package.json" ]; then
        cp "package.json" "$temp_dir/"
    fi
    
    if [ -f "package-lock.json" ]; then
        cp "package-lock.json" "$temp_dir/"
    fi
    
    # 备份 Next.js 配置
    if [ -f "next.config.mjs" ]; then
        cp "next.config.mjs" "$temp_dir/"
    fi
    
    # 备份 TypeScript 配置
    if [ -f "tsconfig.json" ]; then
        cp "tsconfig.json" "$temp_dir/"
    fi
    
    # 备份 Tailwind 配置
    if [ -f "tailwind.config.ts" ]; then
        cp "tailwind.config.ts" "$temp_dir/"
    fi
    
    # 创建配置备份压缩包
    tar -czf "$config_backup" -C "$BACKUP_ROOT/temp" "config_${DATE}"
    
    # 加密配置备份
    if [ -n "$BACKUP_ENCRYPTION_KEY" ]; then
        gpg --batch --yes --passphrase "$BACKUP_ENCRYPTION_KEY" \
            --cipher-algo AES256 --symmetric "$config_backup"
        rm "$config_backup"
        log_success "配置备份加密完成"
    fi
    
    # 清理临时文件
    rm -rf "$temp_dir"
    
    log_success "配置备份完成"
}

# 备份应用日志
backup_logs() {
    log "开始日志备份..."
    
    local log_backup="$BACKUP_ROOT/logs/logs_${DATE}.tar.gz"
    local log_dirs=(
        "/var/log/sociomint"
        "/var/log/nginx"
        "/var/log/cloudflare"
    )
    
    local existing_dirs=()
    for dir in "${log_dirs[@]}"; do
        if [ -d "$dir" ]; then
            existing_dirs+=("$dir")
        fi
    done
    
    if [ ${#existing_dirs[@]} -gt 0 ]; then
        tar -czf "$log_backup" "${existing_dirs[@]}" 2>/dev/null || true
        log_success "日志备份完成"
    else
        log_warning "未找到日志目录"
    fi
}

# 上传到云存储
upload_to_cloud() {
    log "开始上传备份到云存储..."
    
    if [ -z "$AWS_S3_BACKUP_BUCKET" ]; then
        log_warning "AWS_S3_BACKUP_BUCKET 未设置，跳过云存储上传"
        return 0
    fi
    
    # 上传数据库备份
    local db_files=("$BACKUP_ROOT"/database/*"${DATE}"*)
    for file in "${db_files[@]}"; do
        if [ -f "$file" ]; then
            aws s3 cp "$file" "s3://$AWS_S3_BACKUP_BUCKET/database/" || log_error "数据库备份上传失败: $file"
        fi
    done
    
    # 上传配置备份
    local config_files=("$BACKUP_ROOT"/config/*"${DATE}"*)
    for file in "${config_files[@]}"; do
        if [ -f "$file" ]; then
            aws s3 cp "$file" "s3://$AWS_S3_BACKUP_BUCKET/config/" || log_error "配置备份上传失败: $file"
        fi
    done
    
    # 上传日志备份
    local log_files=("$BACKUP_ROOT"/logs/*"${DATE}"*)
    for file in "${log_files[@]}"; do
        if [ -f "$file" ]; then
            aws s3 cp "$file" "s3://$AWS_S3_BACKUP_BUCKET/logs/" || log_error "日志备份上传失败: $file"
        fi
    done
    
    log_success "云存储上传完成"
}

# 清理旧备份
cleanup_old_backups() {
    log "清理旧备份文件..."
    
    # 清理本地备份
    find "$BACKUP_ROOT" -type f -mtime +$RETENTION_DAYS -delete 2>/dev/null || true
    
    # 清理云存储备份（如果配置了）
    if [ -n "$AWS_S3_BACKUP_BUCKET" ]; then
        local cutoff_date=$(date -d "$RETENTION_DAYS days ago" +%Y%m%d)
        
        # 列出并删除旧文件
        aws s3 ls "s3://$AWS_S3_BACKUP_BUCKET/" --recursive | \
        awk -v cutoff="$cutoff_date" '$1 < cutoff {print $4}' | \
        while read -r file; do
            aws s3 rm "s3://$AWS_S3_BACKUP_BUCKET/$file" || true
        done
    fi
    
    log_success "旧备份清理完成"
}

# 验证备份
verify_backup() {
    log "验证备份完整性..."
    
    local errors=0
    
    # 检查数据库备份
    local db_backup=$(find "$BACKUP_ROOT/database" -name "*${DATE}*" -type f | head -1)
    if [ -n "$db_backup" ] && [ -f "$db_backup" ]; then
        log_success "数据库备份文件存在: $db_backup"
    else
        log_error "数据库备份文件不存在"
        ((errors++))
    fi
    
    # 检查配置备份
    local config_backup=$(find "$BACKUP_ROOT/config" -name "*${DATE}*" -type f | head -1)
    if [ -n "$config_backup" ] && [ -f "$config_backup" ]; then
        log_success "配置备份文件存在: $config_backup"
    else
        log_error "配置备份文件不存在"
        ((errors++))
    fi
    
    if [ $errors -eq 0 ]; then
        log_success "备份验证通过"
        return 0
    else
        log_error "备份验证失败，发现 $errors 个错误"
        return 1
    fi
}

# 发送通知
send_notification() {
    local status=$1
    local message=$2
    
    if [ "$status" = "success" ]; then
        log_success "$message"
    else
        log_error "$message"
    fi
    
    # 发送邮件通知（如果配置了）
    if [ -n "$NOTIFICATION_EMAIL" ]; then
        echo "$message" | mail -s "SocioMint Backup $status" "$NOTIFICATION_EMAIL" || true
    fi
    
    # 发送 Slack 通知（如果配置了）
    if [ -n "$SLACK_WEBHOOK_URL" ]; then
        local color="good"
        if [ "$status" != "success" ]; then
            color="danger"
        fi
        
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"attachments\":[{\"color\":\"$color\",\"text\":\"$message\"}]}" \
            "$SLACK_WEBHOOK_URL" || true
    fi
}

# 主函数
main() {
    log "开始 SocioMint 系统备份 - $DATE"
    
    # 创建日志目录
    mkdir -p "$(dirname "$LOG_FILE")"
    
    local start_time=$(date +%s)
    local success=true
    
    # 执行备份步骤
    check_dependencies || success=false
    create_backup_dirs || success=false
    backup_database || success=false
    backup_config || success=false
    backup_logs || success=false
    upload_to_cloud || success=false
    verify_backup || success=false
    cleanup_old_backups || success=false
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    if [ "$success" = true ]; then
        send_notification "success" "备份成功完成，耗时 ${duration} 秒"
        log_success "系统备份完成，耗时 ${duration} 秒"
        exit 0
    else
        send_notification "failed" "备份过程中出现错误，请检查日志: $LOG_FILE"
        log_error "系统备份失败，请检查错误日志"
        exit 1
    fi
}

# 运行主函数
main "$@"
