#!/usr/bin/env node

/**
 * 生成生产环境安全密钥
 * 用于生成JWT_SECRET、NEXTAUTH_SECRET等安全密钥
 */

import crypto from 'crypto';

/**
 * 生成随机密钥
 */
function generateSecureKey(length = 64) {
  return crypto.randomBytes(length).toString('hex');
}

/**
 * 生成Base64编码的密钥
 */
function generateBase64Key(length = 32) {
  return crypto.randomBytes(length).toString('base64');
}

/**
 * 生成UUID
 */
function generateUUID() {
  return crypto.randomUUID();
}

/**
 * 生成强密码
 */
function generateStrongPassword(length = 32) {
  const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
  let password = '';
  for (let i = 0; i < length; i++) {
    password += charset.charAt(Math.floor(Math.random() * charset.length));
  }
  return password;
}

console.log('🔐 SocioMint 生产环境密钥生成器');
console.log('=====================================\n');

console.log('📋 请将以下密钥添加到生产环境配置中：\n');

console.log('# 认证密钥');
console.log(`JWT_SECRET=${generateSecureKey(64)}`);
console.log(`NEXTAUTH_SECRET=${generateSecureKey(64)}`);
console.log('');

console.log('# 加密密钥');
console.log(`ENCRYPTION_KEY=${generateSecureKey(32)}`);
console.log(`KMS_MASTER_KEY=${generateSecureKey(32)}`);
console.log('');

console.log('# 数据库密钥');
console.log(`DATABASE_ENCRYPTION_KEY=${generateBase64Key(32)}`);
console.log(`BACKUP_ENCRYPTION_KEY=${generateBase64Key(32)}`);
console.log('');

console.log('# API 密钥');
console.log(`API_SECRET_KEY=${generateSecureKey(32)}`);
console.log(`WEBHOOK_SECRET=${generateSecureKey(16)}`);
console.log('');

console.log('# 会话密钥');
console.log(`SESSION_SECRET=${generateSecureKey(32)}`);
console.log(`COOKIE_SECRET=${generateSecureKey(16)}`);
console.log('');

console.log('# 其他安全密钥');
console.log(`CSRF_SECRET=${generateSecureKey(16)}`);
console.log(`RATE_LIMIT_SECRET=${generateSecureKey(16)}`);
console.log('');

console.log('🔒 密钥安全提示：');
console.log('- 请将这些密钥安全存储，不要提交到版本控制');
console.log('- 定期轮换密钥以提高安全性');
console.log('- 使用环境变量或密钥管理服务存储');
console.log('- 确保生产环境和开发环境使用不同的密钥');
console.log('');

console.log('📝 建议的密钥管理方案：');
console.log('1. 使用 Cloudflare Workers 环境变量');
console.log('2. 使用 AWS Secrets Manager 或类似服务');
console.log('3. 使用 HashiCorp Vault');
console.log('4. 使用 GitHub Secrets (仅用于 CI/CD)');
console.log('');

// 生成示例配置文件
const exampleConfig = `
# 生产环境安全配置示例
# 生成时间: ${new Date().toISOString()}

# 认证密钥
JWT_SECRET=${generateSecureKey(64)}
NEXTAUTH_SECRET=${generateSecureKey(64)}

# 加密密钥
ENCRYPTION_KEY=${generateSecureKey(32)}
KMS_MASTER_KEY=${generateSecureKey(32)}

# 数据库密钥
DATABASE_ENCRYPTION_KEY=${generateBase64Key(32)}
BACKUP_ENCRYPTION_KEY=${generateBase64Key(32)}

# API 密钥
API_SECRET_KEY=${generateSecureKey(32)}
WEBHOOK_SECRET=${generateSecureKey(16)}

# 会话密钥
SESSION_SECRET=${generateSecureKey(32)}
COOKIE_SECRET=${generateSecureKey(16)}

# 其他安全密钥
CSRF_SECRET=${generateSecureKey(16)}
RATE_LIMIT_SECRET=${generateSecureKey(16)}
`;

// 可选：将配置写入文件
if (process.argv.includes('--save')) {
  const fs = await import('fs');
  const path = await import('path');
  
  const filename = `production-keys-${Date.now()}.env`;
  const filepath = path.join(__dirname, '..', 'temp', filename);
  
  // 确保temp目录存在
  const tempDir = path.dirname(filepath);
  if (!fs.existsSync(tempDir)) {
    fs.mkdirSync(tempDir, { recursive: true });
  }
  
  fs.writeFileSync(filepath, exampleConfig);
  console.log(`💾 配置已保存到: ${filepath}`);
  console.log('⚠️  请在使用后删除此文件！');
}

console.log('\n✅ 密钥生成完成！');
