/**
 * Social Bet系统边界条件测试
 * 测试极端场景和边界情况
 */

interface BoundaryTestResult {
  testName: string;
  scenario: string;
  passed: boolean;
  expectedBehavior: string;
  actualBehavior: string;
  error?: string;
}

interface BoundaryTestSuite {
  category: string;
  tests: BoundaryTestResult[];
  passed: number;
  failed: number;
}

/**
 * 主边界测试函数
 */
async function runBoundaryTests(): Promise<void> {
  console.log('🧪 开始Social Bet系统边界条件测试...\n');

  const testSuites: BoundaryTestSuite[] = [
    await testZeroAndNegativeValues(),
    await testExtremelyLargeValues(),
    await testNetworkAndTimeoutScenarios(),
    await testConcurrencyEdgeCases(),
    await testDataCorruptionScenarios(),
    await testSecurityBoundaries()
  ];

  printBoundaryTestResults(testSuites);
}

/**
 * 测试零值和负值场景
 */
async function testZeroAndNegativeValues(): Promise<BoundaryTestSuite> {
  const suite: BoundaryTestSuite = {
    category: '零值和负值测试',
    tests: [],
    passed: 0,
    failed: 0
  };

  // 测试零投注金额
  await runBoundaryTest(suite, {
    testName: 'zero_bet_amount',
    scenario: '用户尝试投注0福气',
    expectedBehavior: 'API返回400错误，拒绝零投注',
    testFunction: async () => {
      const response = await fetch('/api/social-bet/participate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          betId: 'test-bet-id',
          selectedOption: 'A',
          betAmount: 0
        })
      });
      return { status: response.status, ok: response.ok };
    }
  });

  // 测试负投注金额
  await runBoundaryTest(suite, {
    testName: 'negative_bet_amount',
    scenario: '用户尝试投注-100福气',
    expectedBehavior: 'API返回400错误，拒绝负数投注',
    testFunction: async () => {
      const response = await fetch('/api/social-bet/participate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          betId: 'test-bet-id',
          selectedOption: 'A',
          betAmount: -100
        })
      });
      return { status: response.status, ok: response.ok };
    }
  });

  // 测试零参与者赌约
  await runBoundaryTest(suite, {
    testName: 'zero_participants',
    scenario: '赌约到期时无人参与',
    expectedBehavior: '赌约自动取消，无需结算',
    testFunction: async () => {
      // 模拟检查零参与者赌约的处理
      return { handled: true, status: 'cancelled' };
    }
  });

  // 测试零信心等级
  await runBoundaryTest(suite, {
    testName: 'zero_confidence_level',
    scenario: '裁判提交0级信心投票',
    expectedBehavior: 'API返回400错误，要求1-5级信心',
    testFunction: async () => {
      const response = await fetch('/api/social-bet/judgment', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          betId: 'test-bet-id',
          selectedOption: 'A',
          confidenceLevel: 0,
          reasoning: '测试零信心等级'
        })
      });
      return { status: response.status, ok: response.ok };
    }
  });

  return suite;
}

/**
 * 测试极大值场景
 */
async function testExtremelyLargeValues(): Promise<BoundaryTestSuite> {
  const suite: BoundaryTestSuite = {
    category: '极大值测试',
    tests: [],
    passed: 0,
    failed: 0
  };

  // 测试超大投注金额
  await runBoundaryTest(suite, {
    testName: 'extremely_large_bet',
    scenario: '用户尝试投注1000万福气',
    expectedBehavior: 'API检查用户余额，可能拒绝超额投注',
    testFunction: async () => {
      const response = await fetch('/api/social-bet/participate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          betId: 'test-bet-id',
          selectedOption: 'A',
          betAmount: 10000000
        })
      });
      return { status: response.status, ok: response.ok };
    }
  });

  // 测试超长标题
  await runBoundaryTest(suite, {
    testName: 'extremely_long_title',
    scenario: '创建超长标题的赌约（1000字符）',
    expectedBehavior: 'API返回400错误或截断标题',
    testFunction: async () => {
      const longTitle = 'A'.repeat(1000);
      const response = await fetch('/api/social-bet/create', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          title: longTitle,
          description: '测试超长标题',
          category: 'sports',
          betType: '1vN',
          options: [{ id: 'A', text: '选项A' }, { id: 'B', text: '选项B' }],
          minBetAmount: 10,
          bettingDeadline: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
          resultDeadline: new Date(Date.now() + 48 * 60 * 60 * 1000).toISOString()
        })
      });
      return { status: response.status, ok: response.ok };
    }
  });

  // 测试大量选项
  await runBoundaryTest(suite, {
    testName: 'too_many_options',
    scenario: '创建包含100个选项的赌约',
    expectedBehavior: 'API限制选项数量，返回400错误',
    testFunction: async () => {
      const manyOptions = Array.from({ length: 100 }, (_, i) => ({
        id: `option_${i}`,
        text: `选项${i}`
      }));
      
      const response = await fetch('/api/social-bet/create', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          title: '多选项测试',
          description: '测试大量选项',
          category: 'sports',
          betType: '1vN',
          options: manyOptions,
          minBetAmount: 10,
          bettingDeadline: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
          resultDeadline: new Date(Date.now() + 48 * 60 * 60 * 1000).toISOString()
        })
      });
      return { status: response.status, ok: response.ok };
    }
  });

  return suite;
}

/**
 * 测试网络和超时场景
 */
async function testNetworkAndTimeoutScenarios(): Promise<BoundaryTestSuite> {
  const suite: BoundaryTestSuite = {
    category: '网络和超时测试',
    tests: [],
    passed: 0,
    failed: 0
  };

  // 测试请求超时
  await runBoundaryTest(suite, {
    testName: 'request_timeout',
    scenario: '模拟网络超时情况',
    expectedBehavior: '请求在合理时间内超时，返回错误信息',
    testFunction: async () => {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 100); // 100ms超时
      
      try {
        const response = await fetch('/api/social-bet/bets', {
          signal: controller.signal
        });
        clearTimeout(timeoutId);
        return { status: response.status, timedOut: false };
      } catch (error) {
        clearTimeout(timeoutId);
        return { status: 0, timedOut: true, error: error.name };
      }
    }
  });

  // 测试无效JSON
  await runBoundaryTest(suite, {
    testName: 'invalid_json',
    scenario: '发送格式错误的JSON数据',
    expectedBehavior: 'API返回400错误，提示JSON格式错误',
    testFunction: async () => {
      const response = await fetch('/api/social-bet/create', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: '{ invalid json }'
      });
      return { status: response.status, ok: response.ok };
    }
  });

  // 测试空请求体
  await runBoundaryTest(suite, {
    testName: 'empty_request_body',
    scenario: '发送空的请求体',
    expectedBehavior: 'API返回400错误，要求必需参数',
    testFunction: async () => {
      const response = await fetch('/api/social-bet/create', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: ''
      });
      return { status: response.status, ok: response.ok };
    }
  });

  return suite;
}

/**
 * 测试并发边界情况
 */
async function testConcurrencyEdgeCases(): Promise<BoundaryTestSuite> {
  const suite: BoundaryTestSuite = {
    category: '并发边界测试',
    tests: [],
    passed: 0,
    failed: 0
  };

  // 测试同时参与同一赌约
  await runBoundaryTest(suite, {
    testName: 'concurrent_participation',
    scenario: '同一用户同时多次参与同一赌约',
    expectedBehavior: '只允许一次参与，其他请求被拒绝',
    testFunction: async () => {
      const requests = Array.from({ length: 5 }, () =>
        fetch('/api/social-bet/participate', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            betId: 'test-bet-id',
            selectedOption: 'A',
            betAmount: 100
          })
        })
      );
      
      const responses = await Promise.all(requests);
      const successCount = responses.filter(r => r.ok).length;
      return { successCount, totalRequests: 5 };
    }
  });

  // 测试频率限制边界
  await runBoundaryTest(suite, {
    testName: 'rate_limit_boundary',
    scenario: '快速连续发送请求触发频率限制',
    expectedBehavior: '超过限制后返回429错误',
    testFunction: async () => {
      const requests = Array.from({ length: 10 }, () =>
        fetch('/api/social-bet/bets')
      );
      
      const responses = await Promise.all(requests);
      const rateLimitedCount = responses.filter(r => r.status === 429).length;
      return { rateLimitedCount, totalRequests: 10 };
    }
  });

  return suite;
}

/**
 * 测试数据损坏场景
 */
async function testDataCorruptionScenarios(): Promise<BoundaryTestSuite> {
  const suite: BoundaryTestSuite = {
    category: '数据损坏测试',
    tests: [],
    passed: 0,
    failed: 0
  };

  // 测试不存在的赌约ID
  await runBoundaryTest(suite, {
    testName: 'nonexistent_bet_id',
    scenario: '尝试参与不存在的赌约',
    expectedBehavior: 'API返回404错误',
    testFunction: async () => {
      const response = await fetch('/api/social-bet/participate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          betId: 'nonexistent-bet-id-12345',
          selectedOption: 'A',
          betAmount: 100
        })
      });
      return { status: response.status, ok: response.ok };
    }
  });

  // 测试无效的选项ID
  await runBoundaryTest(suite, {
    testName: 'invalid_option_id',
    scenario: '选择不存在的选项',
    expectedBehavior: 'API返回400错误，提示无效选项',
    testFunction: async () => {
      const response = await fetch('/api/social-bet/participate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          betId: 'test-bet-id',
          selectedOption: 'INVALID_OPTION',
          betAmount: 100
        })
      });
      return { status: response.status, ok: response.ok };
    }
  });

  return suite;
}

/**
 * 测试安全边界
 */
async function testSecurityBoundaries(): Promise<BoundaryTestSuite> {
  const suite: BoundaryTestSuite = {
    category: '安全边界测试',
    tests: [],
    passed: 0,
    failed: 0
  };

  // 测试SQL注入尝试
  await runBoundaryTest(suite, {
    testName: 'sql_injection_attempt',
    scenario: '在参数中尝试SQL注入',
    expectedBehavior: 'API安全处理，不执行恶意SQL',
    testFunction: async () => {
      const response = await fetch('/api/social-bet/bets/\'; DROP TABLE social_bets; --', {
        method: 'GET'
      });
      return { status: response.status, ok: response.ok };
    }
  });

  // 测试XSS尝试
  await runBoundaryTest(suite, {
    testName: 'xss_attempt',
    scenario: '在标题中尝试XSS攻击',
    expectedBehavior: 'API过滤或转义恶意脚本',
    testFunction: async () => {
      const response = await fetch('/api/social-bet/create', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          title: '<script>alert("XSS")</script>',
          description: '测试XSS防护',
          category: 'sports',
          betType: '1vN',
          options: [{ id: 'A', text: '选项A' }],
          minBetAmount: 10,
          bettingDeadline: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
          resultDeadline: new Date(Date.now() + 48 * 60 * 60 * 1000).toISOString()
        })
      });
      return { status: response.status, ok: response.ok };
    }
  });

  return suite;
}

/**
 * 运行单个边界测试
 */
async function runBoundaryTest(
  suite: BoundaryTestSuite,
  test: {
    testName: string;
    scenario: string;
    expectedBehavior: string;
    testFunction: () => Promise<any>;
  }
): Promise<void> {
  try {
    const result = await test.testFunction();
    
    // 根据测试结果判断是否通过
    let passed = false;
    let actualBehavior = '';
    
    if (result.status !== undefined) {
      // HTTP状态码测试
      passed = result.status >= 400 && result.status < 500; // 期望客户端错误
      actualBehavior = `HTTP ${result.status}`;
    } else if (result.handled !== undefined) {
      // 业务逻辑测试
      passed = result.handled === true;
      actualBehavior = `处理状态: ${result.status || 'unknown'}`;
    } else if (result.successCount !== undefined) {
      // 并发测试
      passed = result.successCount <= 1; // 期望只有一个成功
      actualBehavior = `成功请求: ${result.successCount}/${result.totalRequests}`;
    } else if (result.rateLimitedCount !== undefined) {
      // 频率限制测试
      passed = result.rateLimitedCount > 0; // 期望有限制
      actualBehavior = `被限制请求: ${result.rateLimitedCount}/${result.totalRequests}`;
    } else {
      passed = true;
      actualBehavior = JSON.stringify(result);
    }
    
    suite.tests.push({
      testName: test.testName,
      scenario: test.scenario,
      passed,
      expectedBehavior: test.expectedBehavior,
      actualBehavior
    });
    
    if (passed) {
      suite.passed++;
    } else {
      suite.failed++;
    }
    
  } catch (error) {
    suite.tests.push({
      testName: test.testName,
      scenario: test.scenario,
      passed: false,
      expectedBehavior: test.expectedBehavior,
      actualBehavior: '测试执行失败',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    
    suite.failed++;
  }
}

/**
 * 打印边界测试结果
 */
function printBoundaryTestResults(testSuites: BoundaryTestSuite[]): void {
  console.log('📊 Social Bet系统边界条件测试结果:\n');

  let totalPassed = 0;
  let totalFailed = 0;

  testSuites.forEach(suite => {
    const status = suite.failed === 0 ? '✅' : '❌';
    console.log(`${status} ${suite.category}: ${suite.passed}/${suite.passed + suite.failed} 通过`);

    suite.tests.forEach(test => {
      const testStatus = test.passed ? '  ✅' : '  ❌';
      console.log(`${testStatus} ${test.testName}`);
      console.log(`     场景: ${test.scenario}`);
      console.log(`     期望: ${test.expectedBehavior}`);
      console.log(`     实际: ${test.actualBehavior}`);
      
      if (test.error) {
        console.log(`     错误: ${test.error}`);
      }
      console.log('');
    });

    totalPassed += suite.passed;
    totalFailed += suite.failed;
  });

  console.log(`🎯 边界测试总计: ${totalPassed}/${totalPassed + totalFailed} 通过`);
  console.log(`📈 通过率: ${((totalPassed / (totalPassed + totalFailed)) * 100).toFixed(1)}%`);

  if (totalFailed === 0) {
    console.log('\n🎉 所有边界条件测试通过！系统边界处理健壮。');
  } else {
    console.log(`\n⚠️ 有 ${totalFailed} 个边界测试失败，需要加强边界处理。`);
  }
}

/**
 * 执行边界测试
 */
if (require.main === module) {
  runBoundaryTests()
    .then(() => {
      console.log('\n🏁 边界条件测试完成！');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 边界测试失败:', error);
      process.exit(1);
    });
}

export { runBoundaryTests };
