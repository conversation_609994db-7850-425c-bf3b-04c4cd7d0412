const { ethers } = require("hardhat");

/**
 * 部署安全版本的HAOX Vesting合约
 * 包含带时间锁的紧急提取机制
 */
async function main() {
    console.log("🚀 开始部署HAOXVestingV2FixedSecure合约...");
    
    // 获取部署者账户
    const [deployer] = await ethers.getSigners();
    console.log("📝 部署者地址:", deployer.address);
    
    // 检查余额
    const balance = await deployer.provider.getBalance(deployer.address);
    console.log("💰 部署者余额:", ethers.formatEther(balance), "BNB");
    
    if (balance < ethers.parseEther("0.1")) {
        console.warn("⚠️  余额可能不足以完成部署");
    }
    
    // 合约地址配置
    const config = {
        haoxToken: process.env.NEXT_PUBLIC_HAOX_TOKEN_ADDRESS,
        priceOracle: process.env.NEXT_PUBLIC_HAOX_PRICE_ORACLE_ADDRESS,
        projectWallet: process.env.PROJECT_WALLET_ADDRESS || deployer.address,
        communityWallet: process.env.COMMUNITY_WALLET_ADDRESS || deployer.address
    };
    
    console.log("🔧 合约配置:");
    console.log("   HAOX Token:", config.haoxToken);
    console.log("   Price Oracle:", config.priceOracle);
    console.log("   Project Wallet:", config.projectWallet);
    console.log("   Community Wallet:", config.communityWallet);
    
    // 验证配置
    if (!config.haoxToken || !config.priceOracle) {
        throw new Error("❌ 缺少必要的合约地址配置");
    }
    
    try {
        // 获取合约工厂
        console.log("📦 获取合约工厂...");
        const HAOXVestingV2FixedSecure = await ethers.getContractFactory("HAOXVestingV2FixedSecure");
        
        // 估算部署Gas
        console.log("⛽ 估算部署Gas...");
        const deploymentData = HAOXVestingV2FixedSecure.interface.encodeDeploy([
            config.haoxToken,
            config.priceOracle,
            config.projectWallet,
            config.communityWallet
        ]);
        
        const gasEstimate = await deployer.estimateGas({
            data: deploymentData
        });
        
        console.log("⛽ 预估Gas使用:", gasEstimate.toString());
        
        // 获取当前Gas价格
        const feeData = await deployer.provider.getFeeData();
        const gasPrice = feeData.gasPrice;
        console.log("💰 当前Gas价格:", ethers.formatUnits(gasPrice, "gwei"), "Gwei");
        
        // 计算部署成本
        const deploymentCost = gasEstimate * gasPrice;
        console.log("💸 预估部署成本:", ethers.formatEther(deploymentCost), "BNB");
        
        // 部署合约
        console.log("🚀 开始部署合约...");
        const vestingContract = await HAOXVestingV2FixedSecure.deploy(
            config.haoxToken,
            config.priceOracle,
            config.projectWallet,
            config.communityWallet,
            {
                gasLimit: gasEstimate * 120n / 100n, // 增加20%缓冲
                gasPrice: gasPrice
            }
        );
        
        console.log("⏳ 等待合约部署确认...");
        await vestingContract.waitForDeployment();
        
        const contractAddress = await vestingContract.getAddress();
        console.log("✅ 合约部署成功!");
        console.log("📍 合约地址:", contractAddress);
        
        // 获取部署交易信息
        const deployTx = vestingContract.deploymentTransaction();
        console.log("📤 部署交易哈希:", deployTx.hash);
        
        // 等待更多确认
        console.log("⏳ 等待交易确认...");
        const receipt = await deployTx.wait(2); // 等待2个确认
        
        console.log("✅ 交易已确认!");
        console.log("⛽ 实际Gas使用:", receipt.gasUsed.toString());
        console.log("💸 实际部署成本:", ethers.formatEther(receipt.gasUsed * receipt.gasPrice), "BNB");
        
        // 验证合约部署
        console.log("🔍 验证合约部署...");
        
        // 检查基本功能
        const totalRounds = await vestingContract.TOTAL_ROUNDS();
        const emergencyDelay = await vestingContract.EMERGENCY_DELAY();
        const maxEmergencyAmount = await vestingContract.MAX_EMERGENCY_AMOUNT();
        
        console.log("✅ 合约验证通过:");
        console.log("   总轮次:", totalRounds.toString());
        console.log("   紧急延迟:", emergencyDelay.toString(), "秒 (", emergencyDelay / 86400n, "天)");
        console.log("   最大紧急提取:", ethers.formatEther(maxEmergencyAmount), "代币");
        
        // 检查安全功能
        const owner = await vestingContract.owner();
        const requiredSignatures = await vestingContract.requiredSignatures();
        const isPaused = await vestingContract.paused();
        
        console.log("🔒 安全功能验证:");
        console.log("   合约所有者:", owner);
        console.log("   所需签名数:", requiredSignatures.toString());
        console.log("   暂停状态:", isPaused);
        
        // 检查紧急签名者
        const isEmergencySigner = await vestingContract.emergencySigners(deployer.address);
        console.log("   部署者是紧急签名者:", isEmergencySigner);
        
        // 生成部署报告
        const deploymentReport = {
            contractName: "HAOXVestingV2FixedSecure",
            contractAddress: contractAddress,
            deploymentTxHash: deployTx.hash,
            deployer: deployer.address,
            deploymentTime: new Date().toISOString(),
            gasUsed: receipt.gasUsed.toString(),
            gasPrice: receipt.gasPrice.toString(),
            deploymentCost: ethers.formatEther(receipt.gasUsed * receipt.gasPrice),
            blockNumber: receipt.blockNumber,
            configuration: config,
            verification: {
                totalRounds: totalRounds.toString(),
                emergencyDelay: emergencyDelay.toString(),
                maxEmergencyAmount: ethers.formatEther(maxEmergencyAmount),
                owner: owner,
                requiredSignatures: requiredSignatures.toString(),
                isPaused: isPaused
            }
        };
        
        // 保存部署报告
        const fs = require('fs');
        const path = require('path');
        
        const reportsDir = path.join(__dirname, '../deployment-reports');
        if (!fs.existsSync(reportsDir)) {
            fs.mkdirSync(reportsDir, { recursive: true });
        }
        
        const reportFile = path.join(reportsDir, `HAOXVestingV2FixedSecure-${Date.now()}.json`);
        fs.writeFileSync(reportFile, JSON.stringify(deploymentReport, null, 2));
        
        console.log("📄 部署报告已保存:", reportFile);
        
        // 更新环境变量建议
        console.log("\n🔧 环境变量更新建议:");
        console.log(`NEXT_PUBLIC_HAOX_VESTING_ADDRESS_V2_FIXED_SECURE=${contractAddress}`);
        
        // 后续步骤提示
        console.log("\n📋 后续步骤:");
        console.log("1. 更新 .env.local 文件中的合约地址");
        console.log("2. 验证合约在区块链浏览器上的代码");
        console.log("3. 配置额外的紧急签名者");
        console.log("4. 测试紧急提取功能");
        console.log("5. 更新前端应用使用新合约");
        
        // 安全检查清单
        console.log("\n🔒 安全检查清单:");
        console.log("□ 验证所有者地址正确");
        console.log("□ 确认紧急签名者配置");
        console.log("□ 测试时间锁机制");
        console.log("□ 验证多重签名功能");
        console.log("□ 检查权限分离");
        
        return {
            contractAddress,
            deploymentTxHash: deployTx.hash,
            deploymentReport
        };
        
    } catch (error) {
        console.error("❌ 部署失败:", error);
        
        // 错误分析
        if (error.code === 'INSUFFICIENT_FUNDS') {
            console.error("💸 余额不足，请充值后重试");
        } else if (error.code === 'REPLACEMENT_UNDERPRICED') {
            console.error("⛽ Gas价格过低，请提高Gas价格");
        } else if (error.message.includes('revert')) {
            console.error("🔄 交易被回滚，请检查合约参数");
        }
        
        throw error;
    }
}

// 错误处理
process.on('unhandledRejection', (error) => {
    console.error('❌ 未处理的错误:', error);
    process.exit(1);
});

// 如果直接运行此脚本
if (require.main === module) {
    main()
        .then((result) => {
            console.log("\n🎉 部署完成!");
            console.log("合约地址:", result.contractAddress);
            process.exit(0);
        })
        .catch((error) => {
            console.error("❌ 部署失败:", error);
            process.exit(1);
        });
}

module.exports = { main };
