-- Social Bet系统关键问题修复脚本
-- 修复代码审查中发现的高优先级问题

-- 1. 添加外键约束，确保数据完整性
ALTER TABLE public.bet_participants 
ADD CONSTRAINT fk_bet_participants_bet_id 
FOREIGN KEY (bet_id) REFERENCES public.social_bets(id) ON DELETE CASCADE;

ALTER TABLE public.bet_participants 
ADD CONSTRAINT fk_bet_participants_user_id 
FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;

ALTER TABLE public.bet_participants 
ADD CONSTRAINT fk_bet_participants_referrer_id 
FOREIGN KEY (referrer_id) REFERENCES public.users(id) ON DELETE SET NULL;

ALTER TABLE public.bet_judgments 
ADD CONSTRAINT fk_bet_judgments_bet_id 
FOREIGN KEY (bet_id) REFERENCES public.social_bets(id) ON DELETE CASCADE;

ALTER TABLE public.bet_judgments 
ADD CONSTRAINT fk_bet_judgments_judge_id 
FOREI<PERSON><PERSON> KEY (judge_id) REFERENCES public.users(id) ON DELETE CASCADE;

ALTER TABLE public.user_reputation 
ADD CONSTRAINT fk_user_reputation_user_id 
FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;

ALTER TABLE public.bet_appeals 
ADD CONSTRAINT fk_bet_appeals_bet_id 
FOREIGN KEY (bet_id) REFERENCES public.social_bets(id) ON DELETE CASCADE;

ALTER TABLE public.bet_appeals 
ADD CONSTRAINT fk_bet_appeals_appellant_id 
FOREIGN KEY (appellant_id) REFERENCES public.users(id) ON DELETE CASCADE;

-- 2. 添加CHECK约束，确保数据有效性
ALTER TABLE public.social_bets 
ADD CONSTRAINT chk_social_bets_status 
CHECK (status IN ('open', 'betting_closed', 'judging', 'confirming', 'settled', 'cancelled', 'expired'));

ALTER TABLE public.social_bets 
ADD CONSTRAINT chk_social_bets_bet_type 
CHECK (bet_type IN ('1v1', '1vN'));

ALTER TABLE public.social_bets 
ADD CONSTRAINT chk_social_bets_amounts 
CHECK (min_bet_amount > 0 AND max_bet_amount >= min_bet_amount AND total_pool >= 0);

ALTER TABLE public.social_bets 
ADD CONSTRAINT chk_social_bets_deadlines 
CHECK (betting_deadline < result_deadline);

ALTER TABLE public.social_bets 
ADD CONSTRAINT chk_social_bets_rates 
CHECK (platform_fee_rate >= 0 AND platform_fee_rate <= 1 AND referral_reward_rate >= 0 AND referral_reward_rate <= 1);

ALTER TABLE public.bet_participants 
ADD CONSTRAINT chk_bet_participants_status 
CHECK (status IN ('active', 'cancelled', 'settled'));

ALTER TABLE public.bet_participants 
ADD CONSTRAINT chk_bet_participants_amounts 
CHECK (bet_amount > 0 AND payout_amount >= 0);

ALTER TABLE public.bet_judgments 
ADD CONSTRAINT chk_bet_judgments_round 
CHECK (judgment_round BETWEEN 1 AND 3);

ALTER TABLE public.bet_judgments 
ADD CONSTRAINT chk_bet_judgments_confidence 
CHECK (confidence_level BETWEEN 1 AND 5);

ALTER TABLE public.bet_judgments 
ADD CONSTRAINT chk_bet_judgments_amounts 
CHECK (reward_amount >= 0);

ALTER TABLE public.user_reputation 
ADD CONSTRAINT chk_user_reputation_level 
CHECK (certification_level BETWEEN 0 AND 5);

ALTER TABLE public.user_reputation 
ADD CONSTRAINT chk_user_reputation_scores 
CHECK (reputation_score >= 0 AND accuracy_rate >= 0 AND accuracy_rate <= 1);

ALTER TABLE public.user_reputation 
ADD CONSTRAINT chk_user_reputation_counts 
CHECK (total_judgments >= 0 AND correct_judgments >= 0 AND correct_judgments <= total_judgments);

ALTER TABLE public.user_reputation 
ADD CONSTRAINT chk_user_reputation_streaks 
CHECK (current_streak >= 0 AND best_streak >= 0 AND best_streak >= current_streak);

ALTER TABLE public.user_reputation 
ADD CONSTRAINT chk_user_reputation_daily 
CHECK (daily_judgment_count >= 0 AND daily_judgment_limit >= 0);

-- 3. 添加唯一约束，防止重复数据
ALTER TABLE public.bet_participants 
ADD CONSTRAINT uk_bet_participants_bet_user 
UNIQUE (bet_id, user_id);

ALTER TABLE public.bet_judgments 
ADD CONSTRAINT uk_bet_judgments_bet_judge_round 
UNIQUE (bet_id, judge_id, judgment_round);

-- 4. 添加部分索引，提升查询性能
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_social_bets_active 
ON public.social_bets(created_at DESC) 
WHERE status IN ('open', 'betting_closed', 'judging', 'confirming');

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_bet_participants_active 
ON public.bet_participants(created_at DESC) 
WHERE status = 'active';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_bet_judgments_pending 
ON public.bet_judgments(created_at DESC) 
WHERE is_correct_judgment IS NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_reputation_active 
ON public.user_reputation(last_judgment_date DESC) 
WHERE total_judgments > 0;

-- 5. 创建并发控制函数
CREATE OR REPLACE FUNCTION public.update_bet_with_lock(
    p_bet_id UUID,
    p_new_status TEXT,
    p_participant_count INTEGER DEFAULT NULL,
    p_total_pool DECIMAL DEFAULT NULL
) RETURNS BOOLEAN AS $$
DECLARE
    v_current_status TEXT;
BEGIN
    -- 使用FOR UPDATE锁定行
    SELECT status INTO v_current_status 
    FROM public.social_bets 
    WHERE id = p_bet_id 
    FOR UPDATE;
    
    -- 检查状态转换是否有效
    IF NOT public.is_valid_status_transition(v_current_status, p_new_status) THEN
        RAISE EXCEPTION 'Invalid status transition from % to %', v_current_status, p_new_status;
    END IF;
    
    -- 更新赌约
    UPDATE public.social_bets 
    SET 
        status = p_new_status,
        participant_count = COALESCE(p_participant_count, participant_count),
        total_pool = COALESCE(p_total_pool, total_pool),
        updated_at = NOW()
    WHERE id = p_bet_id;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- 6. 创建状态转换验证函数
CREATE OR REPLACE FUNCTION public.is_valid_status_transition(
    p_from_status TEXT,
    p_to_status TEXT
) RETURNS BOOLEAN AS $$
BEGIN
    -- 定义有效的状态转换
    RETURN CASE 
        WHEN p_from_status = 'open' AND p_to_status IN ('betting_closed', 'cancelled') THEN TRUE
        WHEN p_from_status = 'betting_closed' AND p_to_status IN ('judging', 'cancelled') THEN TRUE
        WHEN p_from_status = 'judging' AND p_to_status IN ('confirming', 'cancelled') THEN TRUE
        WHEN p_from_status = 'confirming' AND p_to_status IN ('settled', 'cancelled') THEN TRUE
        WHEN p_to_status = 'expired' THEN TRUE -- 任何状态都可以过期
        ELSE FALSE
    END;
END;
$$ LANGUAGE plpgsql;

-- 7. 创建数据一致性检查函数
CREATE OR REPLACE FUNCTION public.check_bet_data_consistency(p_bet_id UUID) 
RETURNS TABLE(
    check_name TEXT,
    is_consistent BOOLEAN,
    details TEXT
) AS $$
BEGIN
    -- 检查参与者数量一致性
    RETURN QUERY
    SELECT 
        'participant_count'::TEXT,
        (sb.participant_count = actual_count.count)::BOOLEAN,
        format('Expected: %s, Actual: %s', sb.participant_count, actual_count.count)::TEXT
    FROM public.social_bets sb
    CROSS JOIN (
        SELECT COUNT(*) as count 
        FROM public.bet_participants 
        WHERE bet_id = p_bet_id AND status = 'active'
    ) actual_count
    WHERE sb.id = p_bet_id;
    
    -- 检查奖池金额一致性
    RETURN QUERY
    SELECT 
        'total_pool'::TEXT,
        (sb.total_pool = actual_pool.amount)::BOOLEAN,
        format('Expected: %s, Actual: %s', sb.total_pool, actual_pool.amount)::TEXT
    FROM public.social_bets sb
    CROSS JOIN (
        SELECT COALESCE(SUM(bet_amount), 0) as amount 
        FROM public.bet_participants 
        WHERE bet_id = p_bet_id AND status = 'active'
    ) actual_pool
    WHERE sb.id = p_bet_id;
    
    -- 检查时间逻辑一致性
    RETURN QUERY
    SELECT 
        'deadline_logic'::TEXT,
        (sb.betting_deadline < sb.result_deadline)::BOOLEAN,
        format('Betting: %s, Result: %s', sb.betting_deadline, sb.result_deadline)::TEXT
    FROM public.social_bets sb
    WHERE sb.id = p_bet_id;
END;
$$ LANGUAGE plpgsql;

-- 8. 创建自动数据修复函数
CREATE OR REPLACE FUNCTION public.auto_fix_bet_inconsistencies(p_bet_id UUID) 
RETURNS TEXT AS $$
DECLARE
    v_actual_count INTEGER;
    v_actual_pool DECIMAL;
    v_fixes TEXT := '';
BEGIN
    -- 修复参与者数量
    SELECT COUNT(*) INTO v_actual_count 
    FROM public.bet_participants 
    WHERE bet_id = p_bet_id AND status = 'active';
    
    UPDATE public.social_bets 
    SET participant_count = v_actual_count 
    WHERE id = p_bet_id AND participant_count != v_actual_count;
    
    IF FOUND THEN
        v_fixes := v_fixes || 'Fixed participant_count; ';
    END IF;
    
    -- 修复奖池金额
    SELECT COALESCE(SUM(bet_amount), 0) INTO v_actual_pool 
    FROM public.bet_participants 
    WHERE bet_id = p_bet_id AND status = 'active';
    
    UPDATE public.social_bets 
    SET total_pool = v_actual_pool 
    WHERE id = p_bet_id AND total_pool != v_actual_pool;
    
    IF FOUND THEN
        v_fixes := v_fixes || 'Fixed total_pool; ';
    END IF;
    
    RETURN COALESCE(NULLIF(v_fixes, ''), 'No fixes needed');
END;
$$ LANGUAGE plpgsql;

-- 9. 创建定期清理函数
CREATE OR REPLACE FUNCTION public.cleanup_expired_bets() 
RETURNS INTEGER AS $$
DECLARE
    v_count INTEGER := 0;
BEGIN
    -- 标记过期的赌约
    UPDATE public.social_bets 
    SET status = 'expired', updated_at = NOW()
    WHERE status IN ('open', 'betting_closed') 
    AND result_deadline < NOW() - INTERVAL '7 days';
    
    GET DIAGNOSTICS v_count = ROW_COUNT;
    
    -- 清理过期的临时数据
    DELETE FROM public.bet_judgments 
    WHERE bet_id IN (
        SELECT id FROM public.social_bets 
        WHERE status = 'expired' 
        AND updated_at < NOW() - INTERVAL '30 days'
    );
    
    RETURN v_count;
END;
$$ LANGUAGE plpgsql;

-- 10. 创建性能监控视图
CREATE OR REPLACE VIEW public.v_bet_performance_metrics AS
SELECT 
    DATE_TRUNC('hour', created_at) as hour,
    COUNT(*) as total_bets,
    COUNT(*) FILTER (WHERE status = 'settled') as settled_bets,
    COUNT(*) FILTER (WHERE status = 'cancelled') as cancelled_bets,
    AVG(total_pool) as avg_pool_size,
    AVG(participant_count) as avg_participants,
    AVG(EXTRACT(EPOCH FROM (updated_at - created_at))/3600) as avg_duration_hours
FROM public.social_bets 
WHERE created_at >= NOW() - INTERVAL '7 days'
GROUP BY DATE_TRUNC('hour', created_at)
ORDER BY hour DESC;

-- 11. 创建安全监控视图
CREATE OR REPLACE VIEW public.v_security_alerts AS
SELECT 
    'rapid_betting' as alert_type,
    user_id,
    COUNT(*) as event_count,
    MAX(created_at) as last_event,
    'User made ' || COUNT(*) || ' bets in 1 hour' as description
FROM public.bet_participants 
WHERE created_at >= NOW() - INTERVAL '1 hour'
GROUP BY user_id
HAVING COUNT(*) > 20

UNION ALL

SELECT 
    'large_bet' as alert_type,
    user_id,
    COUNT(*) as event_count,
    MAX(created_at) as last_event,
    'User made bet > 5000 fortune' as description
FROM public.bet_participants 
WHERE bet_amount > 5000 
AND created_at >= NOW() - INTERVAL '1 hour'
GROUP BY user_id;

-- 提交所有更改
COMMIT;
