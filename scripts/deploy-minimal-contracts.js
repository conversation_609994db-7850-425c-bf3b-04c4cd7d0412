const { ethers } = require("hardhat");

/**
 * 部署精简版安全合约 - 成本优化版本
 * 仅部署核心必要的安全合约，大幅降低部署成本
 */
async function main() {
    console.log("🚀 开始部署精简版安全合约...");
    
    const [deployer] = await ethers.getSigners();
    console.log("📝 部署者地址:", deployer.address);
    
    // 检查余额
    const balance = await deployer.provider.getBalance(deployer.address);
    console.log("💰 部署者余额:", ethers.formatEther(balance), "BNB");
    
    if (balance < ethers.parseEther("0.08")) {
        console.warn("⚠️  余额可能不足以完成部署");
    }
    
    // 合约配置
    const config = {
        haoxToken: process.env.NEXT_PUBLIC_HAOX_TOKEN_ADDRESS,
        priceOracle: process.env.NEXT_PUBLIC_HAOX_PRICE_ORACLE_ADDRESS,
        projectWallet: process.env.PROJECT_WALLET_ADDRESS || deployer.address,
        communityWallet: process.env.COMMUNITY_WALLET_ADDRESS || deployer.address
    };
    
    console.log("🔧 合约配置:");
    console.log("   HAOX Token:", config.haoxToken);
    console.log("   Price Oracle:", config.priceOracle);
    console.log("   Project Wallet:", config.projectWallet);
    console.log("   Community Wallet:", config.communityWallet);
    
    const deploymentResults = {};
    let totalGasUsed = 0n;
    let totalCost = 0n;
    
    try {
        // 获取当前Gas价格
        const feeData = await deployer.provider.getFeeData();
        const gasPrice = feeData.gasPrice;
        console.log("💰 当前Gas价格:", ethers.formatUnits(gasPrice, "gwei"), "Gwei");
        
        // 1. 部署精简版Vesting合约
        console.log("\n📦 部署精简版Vesting合约...");
        const HAOXVestingV2Minimal = await ethers.getContractFactory("HAOXVestingV2Minimal");
        
        // 估算Gas
        const vestingGasEstimate = await deployer.estimateGas({
            data: HAOXVestingV2Minimal.interface.encodeDeploy([
                config.haoxToken,
                config.priceOracle,
                config.projectWallet,
                config.communityWallet
            ])
        });
        
        console.log("⛽ Vesting合约预估Gas:", vestingGasEstimate.toString());
        
        const vestingContract = await HAOXVestingV2Minimal.deploy(
            config.haoxToken,
            config.priceOracle,
            config.projectWallet,
            config.communityWallet,
            {
                gasLimit: vestingGasEstimate * 110n / 100n, // 增加10%缓冲
                gasPrice: gasPrice
            }
        );
        
        await vestingContract.waitForDeployment();
        const vestingAddress = await vestingContract.getAddress();
        const vestingTx = vestingContract.deploymentTransaction();
        const vestingReceipt = await vestingTx.wait();
        
        deploymentResults.vestingContract = {
            address: vestingAddress,
            txHash: vestingTx.hash,
            gasUsed: vestingReceipt.gasUsed,
            cost: vestingReceipt.gasUsed * vestingReceipt.gasPrice
        };
        
        totalGasUsed += vestingReceipt.gasUsed;
        totalCost += vestingReceipt.gasUsed * vestingReceipt.gasPrice;
        
        console.log("✅ Vesting合约部署成功:", vestingAddress);
        console.log("⛽ 实际Gas使用:", vestingReceipt.gasUsed.toString());
        console.log("💸 部署成本:", ethers.formatEther(vestingReceipt.gasUsed * vestingReceipt.gasPrice), "BNB");
        
        // 2. 部署精简版价格聚合器
        console.log("\n📦 部署精简版价格聚合器...");
        const HAOXPriceAggregatorMinimal = await ethers.getContractFactory("HAOXPriceAggregatorMinimal");
        
        const aggregatorGasEstimate = await deployer.estimateGas({
            data: HAOXPriceAggregatorMinimal.interface.encodeDeploy([])
        });
        
        console.log("⛽ 聚合器预估Gas:", aggregatorGasEstimate.toString());
        
        const aggregatorContract = await HAOXPriceAggregatorMinimal.deploy({
            gasLimit: aggregatorGasEstimate * 110n / 100n,
            gasPrice: gasPrice
        });
        
        await aggregatorContract.waitForDeployment();
        const aggregatorAddress = await aggregatorContract.getAddress();
        const aggregatorTx = aggregatorContract.deploymentTransaction();
        const aggregatorReceipt = await aggregatorTx.wait();
        
        deploymentResults.aggregatorContract = {
            address: aggregatorAddress,
            txHash: aggregatorTx.hash,
            gasUsed: aggregatorReceipt.gasUsed,
            cost: aggregatorReceipt.gasUsed * aggregatorReceipt.gasPrice
        };
        
        totalGasUsed += aggregatorReceipt.gasUsed;
        totalCost += aggregatorReceipt.gasUsed * aggregatorReceipt.gasPrice;
        
        console.log("✅ 聚合器部署成功:", aggregatorAddress);
        console.log("⛽ 实际Gas使用:", aggregatorReceipt.gasUsed.toString());
        console.log("💸 部署成本:", ethers.formatEther(aggregatorReceipt.gasUsed * aggregatorReceipt.gasPrice), "BNB");
        
        // 3. 配置价格聚合器
        console.log("\n🔧 配置价格聚合器...");
        
        // 添加主要价格源
        const priceSources = [
            {
                oracle: config.priceOracle,
                weight: 60
            },
            {
                oracle: "******************************************", // Chainlink BNB/USD
                weight: 40
            }
        ];
        
        for (let i = 0; i < priceSources.length; i++) {
            const source = priceSources[i];
            if (source.oracle && source.oracle !== "******************************************") {
                try {
                    const tx = await aggregatorContract.addPriceSource(source.oracle, source.weight);
                    await tx.wait();
                    console.log(`✅ 添加价格源 ${i + 1}: ${source.oracle} (权重: ${source.weight})`);
                } catch (error) {
                    console.warn(`⚠️  添加价格源 ${i + 1} 失败:`, error.message);
                }
            }
        }
        
        // 4. 验证部署
        console.log("\n🔍 验证合约部署...");
        
        // 验证Vesting合约
        const totalRounds = await vestingContract.TOTAL_ROUNDS();
        const emergencyDelay = await vestingContract.EMERGENCY_DELAY();
        console.log("✅ Vesting合约验证:");
        console.log("   总轮次:", totalRounds.toString());
        console.log("   紧急延迟:", emergencyDelay.toString(), "秒");
        
        // 验证聚合器
        const status = await aggregatorContract.getAggregatorStatus();
        console.log("✅ 聚合器验证:");
        console.log("   价格源数量:", status[0].toString());
        console.log("   活跃源数量:", status[1].toString());
        
        // 5. 生成部署报告
        const deploymentReport = {
            timestamp: new Date().toISOString(),
            deployer: deployer.address,
            network: "BSC Testnet",
            contracts: {
                vestingContract: {
                    name: "HAOXVestingV2Minimal",
                    address: vestingAddress,
                    txHash: vestingTx.hash,
                    gasUsed: vestingReceipt.gasUsed.toString(),
                    cost: ethers.formatEther(deploymentResults.vestingContract.cost)
                },
                aggregatorContract: {
                    name: "HAOXPriceAggregatorMinimal",
                    address: aggregatorAddress,
                    txHash: aggregatorTx.hash,
                    gasUsed: aggregatorReceipt.gasUsed.toString(),
                    cost: ethers.formatEther(deploymentResults.aggregatorContract.cost)
                }
            },
            summary: {
                totalGasUsed: totalGasUsed.toString(),
                totalCost: ethers.formatEther(totalCost),
                estimatedUSDCost: (parseFloat(ethers.formatEther(totalCost)) * 800).toFixed(2) // 假设BNB = $800
            }
        };
        
        // 保存部署报告
        const fs = require('fs');
        const path = require('path');
        
        const reportsDir = path.join(__dirname, '../deployment-reports');
        if (!fs.existsSync(reportsDir)) {
            fs.mkdirSync(reportsDir, { recursive: true });
        }
        
        const reportFile = path.join(reportsDir, `minimal-contracts-${Date.now()}.json`);
        fs.writeFileSync(reportFile, JSON.stringify(deploymentReport, null, 2));
        
        // 显示总结
        console.log("\n" + "=".repeat(60));
        console.log("🎉 精简版合约部署完成!");
        console.log("=".repeat(60));
        console.log("📍 合约地址:");
        console.log("   Vesting (精简版):", vestingAddress);
        console.log("   聚合器 (精简版):", aggregatorAddress);
        console.log("\n💰 成本总结:");
        console.log("   总Gas使用:", totalGasUsed.toString());
        console.log("   总成本:", ethers.formatEther(totalCost), "BNB");
        console.log("   预估USD成本: $", deploymentReport.summary.estimatedUSDCost);
        console.log("\n📄 部署报告:", reportFile);
        
        // 环境变量建议
        console.log("\n🔧 环境变量更新建议:");
        console.log(`NEXT_PUBLIC_HAOX_VESTING_ADDRESS_V2_MINIMAL=${vestingAddress}`);
        console.log(`NEXT_PUBLIC_HAOX_PRICE_AGGREGATOR_MINIMAL=${aggregatorAddress}`);
        
        // 成本对比
        const originalCost = 0.12845332; // 原始V2.1成本
        const newCost = parseFloat(ethers.formatEther(totalCost));
        const savings = originalCost - newCost;
        const savingsPercent = (savings / originalCost * 100).toFixed(1);
        
        console.log("\n📊 成本对比:");
        console.log("   原始V2.1成本:", originalCost.toFixed(8), "BNB");
        console.log("   精简版成本:", newCost.toFixed(8), "BNB");
        console.log("   节省成本:", savings.toFixed(8), "BNB");
        console.log("   节省比例:", savingsPercent + "%");
        
        return deploymentReport;
        
    } catch (error) {
        console.error("❌ 部署失败:", error);
        throw error;
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main()
        .then((result) => {
            console.log("\n🎉 部署完成!");
            process.exit(0);
        })
        .catch((error) => {
            console.error("❌ 部署失败:", error);
            process.exit(1);
        });
}

module.exports = { main };
