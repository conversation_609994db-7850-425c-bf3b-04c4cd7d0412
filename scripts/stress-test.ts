/**
 * Social Bet系统压力测试脚本
 * 验证系统在高并发情况下的性能表现
 */

interface TestConfig {
  baseUrl: string;
  concurrentUsers: number;
  testDuration: number; // 秒
  rampUpTime: number;   // 秒
}

interface TestResult {
  testName: string;
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  maxResponseTime: number;
  minResponseTime: number;
  requestsPerSecond: number;
  errorRate: number;
  errors: Array<{ error: string; count: number }>;
}

interface StressTestReport {
  config: TestConfig;
  results: TestResult[];
  overallStats: {
    totalRequests: number;
    totalSuccessful: number;
    totalFailed: number;
    overallErrorRate: number;
    averageRPS: number;
    peakRPS: number;
  };
  recommendations: string[];
}

/**
 * 主压力测试函数
 */
async function runStressTest(config: TestConfig): Promise<StressTestReport> {
  console.log('🚀 开始Social Bet系统压力测试...');
  console.log(`配置: ${config.concurrentUsers}并发用户, ${config.testDuration}秒测试时长`);

  const results: TestResult[] = [
    await testBetListAPI(config),
    await testBetCreationAPI(config),
    await testParticipationAPI(config),
    await testJudgmentAPI(config),
    await testCertificationAPI(config)
  ];

  const overallStats = calculateOverallStats(results);
  const recommendations = generateRecommendations(results, overallStats);

  return {
    config,
    results,
    overallStats,
    recommendations
  };
}

/**
 * 测试赌约列表API
 */
async function testBetListAPI(config: TestConfig): Promise<TestResult> {
  console.log('📋 测试赌约列表API...');
  
  const results = await runConcurrentTest({
    testName: 'bet_list_api',
    url: `${config.baseUrl}/api/social-bet/bets`,
    method: 'GET',
    concurrentUsers: config.concurrentUsers,
    testDuration: config.testDuration,
    rampUpTime: config.rampUpTime
  });

  return results;
}

/**
 * 测试赌约创建API
 */
async function testBetCreationAPI(config: TestConfig): Promise<TestResult> {
  console.log('🎲 测试赌约创建API...');
  
  const testData = {
    title: '压力测试赌约',
    description: '这是一个压力测试创建的赌约',
    category: 'sports',
    betType: '1vN',
    options: [
      { id: 'A', text: '选项A' },
      { id: 'B', text: '选项B' }
    ],
    minBetAmount: 10,
    bettingDeadline: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
    resultDeadline: new Date(Date.now() + 48 * 60 * 60 * 1000).toISOString()
  };

  const results = await runConcurrentTest({
    testName: 'bet_creation_api',
    url: `${config.baseUrl}/api/social-bet/create`,
    method: 'POST',
    body: testData,
    concurrentUsers: Math.min(config.concurrentUsers, 10), // 限制创建并发
    testDuration: config.testDuration,
    rampUpTime: config.rampUpTime
  });

  return results;
}

/**
 * 测试参与投注API
 */
async function testParticipationAPI(config: TestConfig): Promise<TestResult> {
  console.log('💰 测试参与投注API...');
  
  const testData = {
    betId: 'test-bet-id',
    selectedOption: 'A',
    betAmount: 100
  };

  const results = await runConcurrentTest({
    testName: 'participation_api',
    url: `${config.baseUrl}/api/social-bet/participate`,
    method: 'POST',
    body: testData,
    concurrentUsers: config.concurrentUsers,
    testDuration: config.testDuration,
    rampUpTime: config.rampUpTime,
    expectedStatus: 401 // 预期未认证错误
  });

  return results;
}

/**
 * 测试裁定API
 */
async function testJudgmentAPI(config: TestConfig): Promise<TestResult> {
  console.log('⚖️ 测试裁定API...');
  
  const testData = {
    betId: 'test-bet-id',
    selectedOption: 'A',
    confidenceLevel: 4,
    reasoning: '基于分析，选项A更可能获胜'
  };

  const results = await runConcurrentTest({
    testName: 'judgment_api',
    url: `${config.baseUrl}/api/social-bet/judgment`,
    method: 'POST',
    body: testData,
    concurrentUsers: config.concurrentUsers,
    testDuration: config.testDuration,
    rampUpTime: config.rampUpTime,
    expectedStatus: 401 // 预期未认证错误
  });

  return results;
}

/**
 * 测试认证API
 */
async function testCertificationAPI(config: TestConfig): Promise<TestResult> {
  console.log('🏆 测试认证API...');
  
  const results = await runConcurrentTest({
    testName: 'certification_api',
    url: `${config.baseUrl}/api/social-bet/certification/benefits/stats`,
    method: 'POST',
    concurrentUsers: config.concurrentUsers,
    testDuration: config.testDuration,
    rampUpTime: config.rampUpTime,
    expectedStatus: 200 // 这个API允许未认证访问
  });

  return results;
}

/**
 * 运行并发测试
 */
async function runConcurrentTest(params: {
  testName: string;
  url: string;
  method: string;
  body?: any;
  concurrentUsers: number;
  testDuration: number;
  rampUpTime: number;
  expectedStatus?: number;
}): Promise<TestResult> {
  
  const startTime = Date.now();
  const endTime = startTime + params.testDuration * 1000;
  const rampUpInterval = (params.rampUpTime * 1000) / params.concurrentUsers;
  
  const results: Array<{ success: boolean; responseTime: number; error?: string }> = [];
  const errors = new Map<string, number>();
  
  // 创建并发用户
  const userPromises: Promise<void>[] = [];
  
  for (let i = 0; i < params.concurrentUsers; i++) {
    const userPromise = new Promise<void>(async (resolve) => {
      // 渐进式启动用户
      await sleep(i * rampUpInterval);
      
      while (Date.now() < endTime) {
        const requestStart = Date.now();
        
        try {
          const response = await fetch(params.url, {
            method: params.method,
            headers: {
              'Content-Type': 'application/json',
            },
            body: params.body ? JSON.stringify(params.body) : undefined,
          });
          
          const responseTime = Date.now() - requestStart;
          const expectedStatus = params.expectedStatus || 200;
          const success = response.status === expectedStatus || 
                         (response.status >= 200 && response.status < 300);
          
          results.push({ success, responseTime });
          
          if (!success) {
            const errorKey = `HTTP_${response.status}`;
            errors.set(errorKey, (errors.get(errorKey) || 0) + 1);
          }
          
        } catch (error) {
          const responseTime = Date.now() - requestStart;
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          
          results.push({ success: false, responseTime, error: errorMessage });
          errors.set(errorMessage, (errors.get(errorMessage) || 0) + 1);
        }
        
        // 短暂休息避免过度压力
        await sleep(Math.random() * 100);
      }
      
      resolve();
    });
    
    userPromises.push(userPromise);
  }
  
  // 等待所有用户完成
  await Promise.all(userPromises);
  
  // 计算统计信息
  const totalRequests = results.length;
  const successfulRequests = results.filter(r => r.success).length;
  const failedRequests = totalRequests - successfulRequests;
  const responseTimes = results.map(r => r.responseTime);
  
  const averageResponseTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
  const maxResponseTime = Math.max(...responseTimes);
  const minResponseTime = Math.min(...responseTimes);
  const requestsPerSecond = totalRequests / params.testDuration;
  const errorRate = (failedRequests / totalRequests) * 100;
  
  return {
    testName: params.testName,
    totalRequests,
    successfulRequests,
    failedRequests,
    averageResponseTime: Math.round(averageResponseTime),
    maxResponseTime,
    minResponseTime,
    requestsPerSecond: Math.round(requestsPerSecond * 100) / 100,
    errorRate: Math.round(errorRate * 100) / 100,
    errors: Array.from(errors.entries()).map(([error, count]) => ({ error, count }))
  };
}

/**
 * 计算总体统计
 */
function calculateOverallStats(results: TestResult[]): StressTestReport['overallStats'] {
  const totalRequests = results.reduce((sum, r) => sum + r.totalRequests, 0);
  const totalSuccessful = results.reduce((sum, r) => sum + r.successfulRequests, 0);
  const totalFailed = results.reduce((sum, r) => sum + r.failedRequests, 0);
  const overallErrorRate = totalRequests > 0 ? (totalFailed / totalRequests) * 100 : 0;
  const averageRPS = results.reduce((sum, r) => sum + r.requestsPerSecond, 0) / results.length;
  const peakRPS = Math.max(...results.map(r => r.requestsPerSecond));
  
  return {
    totalRequests,
    totalSuccessful,
    totalFailed,
    overallErrorRate: Math.round(overallErrorRate * 100) / 100,
    averageRPS: Math.round(averageRPS * 100) / 100,
    peakRPS: Math.round(peakRPS * 100) / 100
  };
}

/**
 * 生成优化建议
 */
function generateRecommendations(
  results: TestResult[], 
  overallStats: StressTestReport['overallStats']
): string[] {
  const recommendations: string[] = [];
  
  // 错误率检查
  if (overallStats.overallErrorRate > 5) {
    recommendations.push(`总体错误率${overallStats.overallErrorRate}%过高，建议检查服务器配置和错误处理`);
  }
  
  // 响应时间检查
  const slowAPIs = results.filter(r => r.averageResponseTime > 2000);
  if (slowAPIs.length > 0) {
    recommendations.push(`以下API响应时间过慢: ${slowAPIs.map(r => r.testName).join(', ')}`);
  }
  
  // 吞吐量检查
  if (overallStats.peakRPS < 50) {
    recommendations.push('系统吞吐量较低，建议优化数据库查询和API性能');
  }
  
  // 特定API建议
  results.forEach(result => {
    if (result.errorRate > 10) {
      recommendations.push(`${result.testName} API错误率${result.errorRate}%过高，需要重点优化`);
    }
    
    if (result.maxResponseTime > 10000) {
      recommendations.push(`${result.testName} API最大响应时间${result.maxResponseTime}ms过长，可能存在性能瓶颈`);
    }
  });
  
  // 通用建议
  if (recommendations.length === 0) {
    recommendations.push('系统性能表现良好，可以考虑进一步提升并发能力');
  }
  
  return recommendations;
}

/**
 * 打印测试报告
 */
function printStressTestReport(report: StressTestReport): void {
  console.log('\n📊 Social Bet系统压力测试报告\n');
  
  console.log('🔧 测试配置:');
  console.log(`  并发用户: ${report.config.concurrentUsers}`);
  console.log(`  测试时长: ${report.config.testDuration}秒`);
  console.log(`  渐进启动: ${report.config.rampUpTime}秒\n`);
  
  console.log('📈 总体统计:');
  console.log(`  总请求数: ${report.overallStats.totalRequests}`);
  console.log(`  成功请求: ${report.overallStats.totalSuccessful}`);
  console.log(`  失败请求: ${report.overallStats.totalFailed}`);
  console.log(`  错误率: ${report.overallStats.overallErrorRate}%`);
  console.log(`  平均RPS: ${report.overallStats.averageRPS}`);
  console.log(`  峰值RPS: ${report.overallStats.peakRPS}\n`);
  
  console.log('🎯 各API性能:');
  report.results.forEach(result => {
    console.log(`  ${result.testName}:`);
    console.log(`    请求数: ${result.totalRequests}`);
    console.log(`    成功率: ${((result.successfulRequests / result.totalRequests) * 100).toFixed(1)}%`);
    console.log(`    平均响应时间: ${result.averageResponseTime}ms`);
    console.log(`    最大响应时间: ${result.maxResponseTime}ms`);
    console.log(`    RPS: ${result.requestsPerSecond}`);
    
    if (result.errors.length > 0) {
      console.log(`    主要错误: ${result.errors.slice(0, 3).map(e => `${e.error}(${e.count})`).join(', ')}`);
    }
    console.log('');
  });
  
  console.log('💡 优化建议:');
  report.recommendations.forEach((rec, index) => {
    console.log(`  ${index + 1}. ${rec}`);
  });
  
  console.log('\n🏁 压力测试完成！');
}

/**
 * 休眠函数
 */
function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 执行压力测试
 */
async function main(): Promise<void> {
  const config: TestConfig = {
    baseUrl: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
    concurrentUsers: 50,
    testDuration: 60,  // 60秒
    rampUpTime: 10     // 10秒渐进启动
  };
  
  try {
    const report = await runStressTest(config);
    printStressTestReport(report);
    
    // 保存报告到文件
    const fs = require('fs');
    const reportPath = `stress-test-report-${Date.now()}.json`;
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    console.log(`\n📄 详细报告已保存到: ${reportPath}`);
    
  } catch (error) {
    console.error('💥 压力测试失败:', error);
    process.exit(1);
  }
}

// 执行测试
if (require.main === module) {
  main();
}

export { runStressTest, StressTestReport };
