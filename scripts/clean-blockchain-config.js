#!/usr/bin/env node

/**
 * 区块链配置清理脚本
 * 移除ENS相关配置，优化BSC配置
 */

import { readFile, writeFile } from 'fs/promises';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

// 颜色定义
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

/**
 * 检查和清理Wagmi配置
 */
async function cleanWagmiConfig() {
  log('blue', '🔧 检查Wagmi配置...');
  
  const wagmiPath = join(projectRoot, 'src/lib/wagmi.ts');
  
  try {
    const content = await readFile(wagmiPath, 'utf-8');
    
    // 检查是否有ENS相关导入
    const ensImports = [
      'normalize',
      'ensResolver',
      'ensAvatar',
      'ensName',
      'mainnet',
      'ethereum'
    ];
    
    let hasEnsConfig = false;
    let cleanedContent = content;
    
    ensImports.forEach(ensImport => {
      if (content.includes(ensImport)) {
        hasEnsConfig = true;
        log('yellow', `⚠️ 发现ENS相关配置: ${ensImport}`);
      }
    });
    
    // 确保只使用BSC链
    if (!content.includes('bsc, bscTestnet')) {
      log('yellow', '⚠️ Wagmi配置可能包含非BSC链');
    }
    
    // 检查是否有不必要的链配置
    const unnecessaryChains = ['mainnet', 'goerli', 'sepolia', 'polygon', 'arbitrum'];
    unnecessaryChains.forEach(chain => {
      if (content.includes(chain)) {
        log('yellow', `⚠️ 发现不必要的链配置: ${chain}`);
        hasEnsConfig = true;
      }
    });
    
    if (!hasEnsConfig) {
      log('green', '✅ Wagmi配置已优化，只使用BSC链');
    } else {
      log('red', '❌ 需要清理Wagmi配置中的ENS/非BSC相关内容');
    }
    
    return { hasEnsConfig, path: wagmiPath };
    
  } catch (error) {
    log('red', `❌ 读取Wagmi配置失败: ${error.message}`);
    return { hasEnsConfig: false, path: wagmiPath };
  }
}

/**
 * 检查环境变量配置
 */
async function checkEnvConfig() {
  log('blue', '🔧 检查环境变量配置...');
  
  const envFiles = ['.env.example', '.env.production', '.env.staging'];
  const results = [];
  
  for (const envFile of envFiles) {
    const envPath = join(projectRoot, envFile);
    
    try {
      const content = await readFile(envPath, 'utf-8');
      
      // 检查ENS相关配置
      const ensVars = [
        'ENS_',
        'MAINNET_',
        'ETHEREUM_',
        'INFURA_',
        'ALCHEMY_MAINNET'
      ];
      
      let hasEnsVars = false;
      ensVars.forEach(ensVar => {
        if (content.includes(ensVar)) {
          hasEnsVars = true;
          log('yellow', `⚠️ ${envFile} 包含可能的ENS相关变量: ${ensVar}`);
        }
      });
      
      // 检查BSC配置完整性
      const requiredBscVars = [
        'NEXT_PUBLIC_CHAIN_ID',
        'NEXT_PUBLIC_RPC_URL',
        'NEXT_PUBLIC_HAOX_TOKEN_ADDRESS'
      ];
      
      let missingBscVars = [];
      requiredBscVars.forEach(bscVar => {
        if (!content.includes(bscVar)) {
          missingBscVars.push(bscVar);
        }
      });
      
      if (missingBscVars.length > 0) {
        log('yellow', `⚠️ ${envFile} 缺少BSC配置: ${missingBscVars.join(', ')}`);
      }
      
      results.push({
        file: envFile,
        hasEnsVars,
        missingBscVars,
        isClean: !hasEnsVars && missingBscVars.length === 0
      });
      
      if (!hasEnsVars && missingBscVars.length === 0) {
        log('green', `✅ ${envFile} 配置正确`);
      }
      
    } catch (error) {
      log('yellow', `⚠️ 无法读取 ${envFile}: ${error.message}`);
    }
  }
  
  return results;
}

/**
 * 检查合约配置
 */
async function checkContractConfig() {
  log('blue', '🔧 检查智能合约配置...');
  
  const contractPath = join(projectRoot, 'src/config/contracts.ts');
  
  try {
    const content = await readFile(contractPath, 'utf-8');
    
    // 检查是否有非BSC网络配置
    const nonBscNetworks = ['mainnet', 'goerli', 'sepolia', 'polygon', 'arbitrum'];
    let hasNonBscConfig = false;
    
    nonBscNetworks.forEach(network => {
      if (content.toLowerCase().includes(network)) {
        hasNonBscConfig = true;
        log('yellow', `⚠️ 合约配置包含非BSC网络: ${network}`);
      }
    });
    
    // 检查BSC配置完整性
    const requiredConfigs = [
      'BSC_TESTNET_CONFIG',
      'BSC_MAINNET_CONFIG',
      'HAOX_TOKEN',
      'HAOX_PRESALE'
    ];
    
    let missingConfigs = [];
    requiredConfigs.forEach(config => {
      if (!content.includes(config)) {
        missingConfigs.push(config);
      }
    });
    
    if (missingConfigs.length > 0) {
      log('yellow', `⚠️ 合约配置缺少: ${missingConfigs.join(', ')}`);
    }
    
    if (!hasNonBscConfig && missingConfigs.length === 0) {
      log('green', '✅ 智能合约配置正确，只包含BSC网络');
    }
    
    return {
      hasNonBscConfig,
      missingConfigs,
      isClean: !hasNonBscConfig && missingConfigs.length === 0
    };
    
  } catch (error) {
    log('red', `❌ 读取合约配置失败: ${error.message}`);
    return { hasNonBscConfig: false, missingConfigs: [], isClean: false };
  }
}

/**
 * 检查包依赖
 */
async function checkPackageDependencies() {
  log('blue', '🔧 检查包依赖...');
  
  const packagePath = join(projectRoot, 'package.json');
  
  try {
    const content = await readFile(packagePath, 'utf-8');
    const packageJson = JSON.parse(content);
    
    // 检查ENS相关依赖
    const ensPackages = [
      '@ensdomains/ensjs',
      '@ensdomains/resolver',
      'ethereum-ens',
      'ens-utils'
    ];
    
    let hasEnsPackages = false;
    const allDeps = {
      ...packageJson.dependencies,
      ...packageJson.devDependencies
    };
    
    ensPackages.forEach(pkg => {
      if (allDeps[pkg]) {
        hasEnsPackages = true;
        log('yellow', `⚠️ 发现ENS相关包: ${pkg}`);
      }
    });
    
    // 检查必需的BSC/Web3包
    const requiredPackages = [
      'wagmi',
      'viem',
      '@tanstack/react-query'
    ];
    
    let missingPackages = [];
    requiredPackages.forEach(pkg => {
      if (!allDeps[pkg]) {
        missingPackages.push(pkg);
      }
    });
    
    if (missingPackages.length > 0) {
      log('yellow', `⚠️ 缺少必需包: ${missingPackages.join(', ')}`);
    }
    
    if (!hasEnsPackages && missingPackages.length === 0) {
      log('green', '✅ 包依赖配置正确');
    }
    
    return {
      hasEnsPackages,
      missingPackages,
      isClean: !hasEnsPackages && missingPackages.length === 0
    };
    
  } catch (error) {
    log('red', `❌ 读取package.json失败: ${error.message}`);
    return { hasEnsPackages: false, missingPackages: [], isClean: false };
  }
}

/**
 * 生成配置优化建议
 */
function generateOptimizationSuggestions(results) {
  log('cyan', '\n📋 配置优化建议');
  log('cyan', '='.repeat(50));
  
  const suggestions = [];
  
  // Wagmi配置建议
  if (!results.wagmi.hasEnsConfig) {
    suggestions.push('✅ Wagmi配置已优化，只使用BSC链');
  } else {
    suggestions.push('🔧 建议清理Wagmi配置中的ENS/非BSC相关内容');
  }
  
  // 环境变量建议
  const cleanEnvFiles = results.env.filter(env => env.isClean);
  if (cleanEnvFiles.length === results.env.length) {
    suggestions.push('✅ 所有环境变量配置正确');
  } else {
    suggestions.push('🔧 建议清理环境变量中的ENS相关配置');
  }
  
  // 合约配置建议
  if (results.contracts.isClean) {
    suggestions.push('✅ 智能合约配置已优化');
  } else {
    suggestions.push('🔧 建议优化智能合约配置，移除非BSC网络');
  }
  
  // 包依赖建议
  if (results.packages.isClean) {
    suggestions.push('✅ 包依赖配置正确');
  } else {
    suggestions.push('🔧 建议移除ENS相关包依赖');
  }
  
  // 额外优化建议
  suggestions.push('💡 建议定期检查配置一致性');
  suggestions.push('💡 建议在部署前验证所有BSC配置');
  suggestions.push('💡 建议使用环境变量验证脚本');
  
  suggestions.forEach(suggestion => {
    if (suggestion.startsWith('✅')) {
      log('green', suggestion);
    } else if (suggestion.startsWith('🔧')) {
      log('yellow', suggestion);
    } else {
      log('blue', suggestion);
    }
  });
}

/**
 * 创建配置验证脚本
 */
async function createConfigValidationScript() {
  const scriptContent = `#!/usr/bin/env node

/**
 * BSC配置验证脚本
 * 验证项目是否正确配置为BSC链
 */

const requiredEnvVars = [
  'NEXT_PUBLIC_CHAIN_ID',
  'NEXT_PUBLIC_RPC_URL',
  'NEXT_PUBLIC_HAOX_TOKEN_ADDRESS',
  'NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID'
];

const bscChainIds = ['56', '97']; // BSC主网和测试网

function validateBscConfig() {
  console.log('🔍 验证BSC配置...');
  
  let isValid = true;
  
  // 检查必需环境变量
  requiredEnvVars.forEach(varName => {
    if (!process.env[varName]) {
      console.error(\`❌ 缺少环境变量: \${varName}\`);
      isValid = false;
    }
  });
  
  // 检查链ID是否为BSC
  const chainId = process.env.NEXT_PUBLIC_CHAIN_ID;
  if (chainId && !bscChainIds.includes(chainId)) {
    console.error(\`❌ 链ID不是BSC: \${chainId}\`);
    isValid = false;
  }
  
  // 检查RPC URL是否为BSC
  const rpcUrl = process.env.NEXT_PUBLIC_RPC_URL;
  if (rpcUrl && !rpcUrl.includes('bsc') && !rpcUrl.includes('binance')) {
    console.error(\`❌ RPC URL不是BSC: \${rpcUrl}\`);
    isValid = false;
  }
  
  if (isValid) {
    console.log('✅ BSC配置验证通过');
  } else {
    console.log('❌ BSC配置验证失败');
    process.exit(1);
  }
}

validateBscConfig();
`;
  
  const scriptPath = join(projectRoot, 'scripts/validate-bsc-config.js');
  await writeFile(scriptPath, scriptContent);
  log('green', `✅ 创建BSC配置验证脚本: ${scriptPath}`);
}

/**
 * 主函数
 */
async function main() {
  console.log('🧹 SocioMint 区块链配置清理工具');
  console.log('='.repeat(40));
  
  try {
    // 执行各项检查
    const wagmiResult = await cleanWagmiConfig();
    const envResults = await checkEnvConfig();
    const contractsResult = await checkContractConfig();
    const packagesResult = await checkPackageDependencies();
    
    // 汇总结果
    const results = {
      wagmi: wagmiResult,
      env: envResults,
      contracts: contractsResult,
      packages: packagesResult
    };
    
    // 生成优化建议
    generateOptimizationSuggestions(results);
    
    // 创建配置验证脚本
    await createConfigValidationScript();
    
    // 总结
    log('cyan', '\n📊 配置清理总结');
    log('cyan', '='.repeat(50));
    
    const allClean = !wagmiResult.hasEnsConfig && 
                    envResults.every(env => env.isClean) && 
                    contractsResult.isClean && 
                    packagesResult.isClean;
    
    if (allClean) {
      log('green', '🎉 所有配置都已优化，项目专注于BSC链！');
    } else {
      log('yellow', '⚠️ 发现一些可以优化的配置项，请参考上述建议');
    }
    
    log('blue', '\n💡 提示: 运行 node scripts/validate-bsc-config.js 验证BSC配置');
    
  } catch (error) {
    log('red', \`💥 配置清理失败: \${error.message}\`);
    process.exit(1);
  }
}

// 运行主函数
if (import.meta.url === \`file://\${process.argv[1]}\`) {
  main().catch(error => {
    console.error('💥 脚本执行失败:', error.message);
    process.exit(1);
  });
}
`;

const scriptPath = join(projectRoot, 'scripts/clean-blockchain-config.js');
await writeFile(scriptPath, scriptContent);
log('green', `✅ 创建区块链配置清理脚本: ${scriptPath}`);
