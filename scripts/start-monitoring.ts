/**
 * 启动实时监控系统
 * 集成所有监控组件，提供统一的监控服务
 */

import { MonitoringService } from '../src/services/monitoring/MonitoringService';
import { PerformanceService } from '../src/services/performance/PerformanceService';
import { SecurityService } from '../src/services/security/SecurityService';

// 监控配置
const MONITORING_CONFIG = {
  // 检查间隔（毫秒）
  INTERVALS: {
    HEALTH_CHECK: 5 * 60 * 1000,      // 5分钟
    PERFORMANCE_CHECK: 30 * 60 * 1000, // 30分钟
    SECURITY_SCAN: 15 * 60 * 1000,     // 15分钟
    DATA_CONSISTENCY: 10 * 60 * 1000   // 10分钟
  },
  
  // 告警阈值
  ALERT_THRESHOLDS: {
    ERROR_RATE: 0.05,           // 5%错误率
    RESPONSE_TIME: 2000,        // 2秒响应时间
    MEMORY_USAGE: 0.85,         // 85%内存使用
    ACTIVE_USERS: 10,           // 最少10个活跃用户
    SECURITY_EVENTS: 5          // 最多5个安全事件
  },
  
  // 通知配置
  NOTIFICATIONS: {
    SLACK_WEBHOOK: process.env.SLACK_WEBHOOK_URL,
    EMAIL_ENABLED: process.env.EMAIL_NOTIFICATIONS === 'true',
    SMS_ENABLED: process.env.SMS_NOTIFICATIONS === 'true'
  }
};

// 监控状态
interface MonitoringState {
  isRunning: boolean;
  startTime: Date;
  lastHealthCheck: Date | null;
  lastPerformanceCheck: Date | null;
  lastSecurityScan: Date | null;
  totalAlerts: number;
  criticalAlerts: number;
  systemStatus: 'healthy' | 'degraded' | 'critical';
}

let monitoringState: MonitoringState = {
  isRunning: false,
  startTime: new Date(),
  lastHealthCheck: null,
  lastPerformanceCheck: null,
  lastSecurityScan: null,
  totalAlerts: 0,
  criticalAlerts: 0,
  systemStatus: 'healthy'
};

/**
 * 启动监控系统
 */
async function startMonitoring(): Promise<void> {
  console.log('🔍 启动Social Bet实时监控系统...');
  
  monitoringState.isRunning = true;
  monitoringState.startTime = new Date();
  
  // 立即执行一次全面检查
  await performInitialHealthCheck();
  
  // 启动定期检查
  startPeriodicChecks();
  
  // 设置优雅关闭
  setupGracefulShutdown();
  
  console.log('✅ 监控系统已启动');
  console.log(`📊 监控仪表板: http://localhost:3000/admin/monitoring`);
  console.log('🔔 告警已配置，将通过配置的渠道发送');
  
  // 保持进程运行
  process.stdin.resume();
}

/**
 * 执行初始健康检查
 */
async function performInitialHealthCheck(): Promise<void> {
  console.log('🏥 执行初始系统健康检查...');
  
  try {
    // 系统健康检查
    const systemHealth = await MonitoringService.getSystemHealth();
    console.log(`📈 系统整体状态: ${systemHealth.overall}`);
    console.log(`📊 业务指标: ${systemHealth.businessMetrics.length}个`);
    console.log(`⚙️ 技术指标: ${systemHealth.technicalMetrics.length}个`);
    console.log(`🔒 安全指标: ${systemHealth.securityMetrics.length}个`);
    console.log(`🚨 活跃告警: ${systemHealth.activeAlerts.length}个`);
    
    // 更新监控状态
    monitoringState.systemStatus = systemHealth.overall;
    monitoringState.lastHealthCheck = new Date();
    
    // 发送初始状态通知
    if (systemHealth.overall !== 'healthy') {
      await sendAlert({
        type: 'system_status',
        severity: systemHealth.overall === 'critical' ? 'critical' : 'warning',
        title: '系统状态异常',
        message: `系统当前状态: ${systemHealth.overall}`,
        details: systemHealth.activeAlerts
      });
    }
    
  } catch (error) {
    console.error('❌ 初始健康检查失败:', error);
    await sendAlert({
      type: 'monitoring_error',
      severity: 'critical',
      title: '监控系统故障',
      message: '初始健康检查失败',
      details: { error: error instanceof Error ? error.message : 'Unknown error' }
    });
  }
}

/**
 * 启动定期检查
 */
function startPeriodicChecks(): void {
  console.log('⏰ 启动定期检查任务...');
  
  // 健康检查
  setInterval(async () => {
    if (!monitoringState.isRunning) return;
    
    try {
      await performHealthCheck();
    } catch (error) {
      console.error('健康检查失败:', error);
    }
  }, MONITORING_CONFIG.INTERVALS.HEALTH_CHECK);
  
  // 性能检查
  setInterval(async () => {
    if (!monitoringState.isRunning) return;
    
    try {
      await performPerformanceCheck();
    } catch (error) {
      console.error('性能检查失败:', error);
    }
  }, MONITORING_CONFIG.INTERVALS.PERFORMANCE_CHECK);
  
  // 安全扫描
  setInterval(async () => {
    if (!monitoringState.isRunning) return;
    
    try {
      await performSecurityScan();
    } catch (error) {
      console.error('安全扫描失败:', error);
    }
  }, MONITORING_CONFIG.INTERVALS.SECURITY_SCAN);
  
  // 数据一致性检查
  setInterval(async () => {
    if (!monitoringState.isRunning) return;
    
    try {
      await performDataConsistencyCheck();
    } catch (error) {
      console.error('数据一致性检查失败:', error);
    }
  }, MONITORING_CONFIG.INTERVALS.DATA_CONSISTENCY);
}

/**
 * 执行健康检查
 */
async function performHealthCheck(): Promise<void> {
  const systemHealth = await MonitoringService.runMonitoringCheck();
  monitoringState.lastHealthCheck = new Date();
  
  // 检查是否需要发送告警
  if (systemHealth.overall !== monitoringState.systemStatus) {
    monitoringState.systemStatus = systemHealth.overall;
    
    if (systemHealth.overall !== 'healthy') {
      await sendAlert({
        type: 'system_status_change',
        severity: systemHealth.overall === 'critical' ? 'critical' : 'warning',
        title: '系统状态变化',
        message: `系统状态从健康变为: ${systemHealth.overall}`,
        details: systemHealth.activeAlerts
      });
    }
  }
  
  console.log(`🏥 健康检查完成 - 状态: ${systemHealth.overall}`);
}

/**
 * 执行性能检查
 */
async function performPerformanceCheck(): Promise<void> {
  const performanceReport = await PerformanceService.generatePerformanceReport();
  monitoringState.lastPerformanceCheck = new Date();
  
  // 检查性能指标
  const criticalIssues = performanceReport.results.filter(r => !r.success);
  
  if (criticalIssues.length > 0) {
    await sendAlert({
      type: 'performance_degradation',
      severity: 'warning',
      title: '性能指标异常',
      message: `发现${criticalIssues.length}个性能问题`,
      details: criticalIssues
    });
  }
  
  console.log(`⚡ 性能检查完成 - 整体评级: ${performanceReport.overall}`);
}

/**
 * 执行安全扫描
 */
async function performSecurityScan(): Promise<void> {
  const securityEvents = await SecurityService.runSecurityScan();
  monitoringState.lastSecurityScan = new Date();
  
  // 检查安全事件
  const criticalEvents = securityEvents.filter(e => e.severity === 'critical');
  const highEvents = securityEvents.filter(e => e.severity === 'high');
  
  if (criticalEvents.length > 0) {
    await sendAlert({
      type: 'security_threat',
      severity: 'critical',
      title: '发现严重安全威胁',
      message: `检测到${criticalEvents.length}个严重安全事件`,
      details: criticalEvents
    });
  } else if (highEvents.length > MONITORING_CONFIG.ALERT_THRESHOLDS.SECURITY_EVENTS) {
    await sendAlert({
      type: 'security_warning',
      severity: 'warning',
      title: '安全事件增多',
      message: `检测到${highEvents.length}个高风险安全事件`,
      details: highEvents
    });
  }
  
  console.log(`🔒 安全扫描完成 - 发现${securityEvents.length}个事件`);
}

/**
 * 执行数据一致性检查
 */
async function performDataConsistencyCheck(): Promise<void> {
  // 这里应该调用数据库的一致性检查函数
  // 目前模拟检查过程
  console.log('🔍 执行数据一致性检查...');
  
  try {
    // 模拟检查结果
    const inconsistencies = Math.floor(Math.random() * 3); // 0-2个不一致
    
    if (inconsistencies > 0) {
      await sendAlert({
        type: 'data_inconsistency',
        severity: 'warning',
        title: '数据一致性问题',
        message: `发现${inconsistencies}个数据不一致问题`,
        details: { count: inconsistencies }
      });
    }
    
    console.log(`📊 数据一致性检查完成 - 发现${inconsistencies}个问题`);
  } catch (error) {
    console.error('数据一致性检查失败:', error);
  }
}

/**
 * 发送告警
 */
async function sendAlert(alert: {
  type: string;
  severity: 'low' | 'warning' | 'critical';
  title: string;
  message: string;
  details?: any;
}): Promise<void> {
  monitoringState.totalAlerts++;
  if (alert.severity === 'critical') {
    monitoringState.criticalAlerts++;
  }
  
  const alertMessage = {
    timestamp: new Date().toISOString(),
    type: alert.type,
    severity: alert.severity,
    title: alert.title,
    message: alert.message,
    details: alert.details,
    systemStatus: monitoringState.systemStatus
  };
  
  console.log(`🚨 ${alert.severity.toUpperCase()} 告警: ${alert.title}`);
  console.log(`   ${alert.message}`);
  
  // 发送到Slack
  if (MONITORING_CONFIG.NOTIFICATIONS.SLACK_WEBHOOK) {
    try {
      await sendSlackNotification(alertMessage);
    } catch (error) {
      console.error('Slack通知发送失败:', error);
    }
  }
  
  // 发送邮件（如果配置）
  if (MONITORING_CONFIG.NOTIFICATIONS.EMAIL_ENABLED) {
    try {
      await sendEmailNotification(alertMessage);
    } catch (error) {
      console.error('邮件通知发送失败:', error);
    }
  }
  
  // 记录到日志文件
  await logAlert(alertMessage);
}

/**
 * 发送Slack通知
 */
async function sendSlackNotification(alert: any): Promise<void> {
  if (!MONITORING_CONFIG.NOTIFICATIONS.SLACK_WEBHOOK) return;
  
  const slackMessage = {
    text: `🚨 Social Bet监控告警`,
    attachments: [
      {
        color: alert.severity === 'critical' ? 'danger' : 'warning',
        title: alert.title,
        text: alert.message,
        fields: [
          {
            title: '严重程度',
            value: alert.severity.toUpperCase(),
            short: true
          },
          {
            title: '系统状态',
            value: alert.systemStatus,
            short: true
          },
          {
            title: '时间',
            value: alert.timestamp,
            short: false
          }
        ]
      }
    ]
  };
  
  const response = await fetch(MONITORING_CONFIG.NOTIFICATIONS.SLACK_WEBHOOK, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(slackMessage)
  });
  
  if (!response.ok) {
    throw new Error(`Slack通知发送失败: ${response.status}`);
  }
}

/**
 * 发送邮件通知
 */
async function sendEmailNotification(alert: any): Promise<void> {
  // 这里应该集成实际的邮件服务
  console.log('📧 邮件通知已发送 (模拟)');
}

/**
 * 记录告警日志
 */
async function logAlert(alert: any): Promise<void> {
  const fs = require('fs');
  const path = require('path');
  
  const logDir = 'logs';
  const logFile = path.join(logDir, 'monitoring-alerts.log');
  
  // 确保日志目录存在
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }
  
  // 写入日志
  const logEntry = `${alert.timestamp} [${alert.severity.toUpperCase()}] ${alert.title}: ${alert.message}\n`;
  fs.appendFileSync(logFile, logEntry);
}

/**
 * 获取监控状态
 */
function getMonitoringStatus(): MonitoringState & { uptime: number } {
  const uptime = monitoringState.isRunning 
    ? Date.now() - monitoringState.startTime.getTime()
    : 0;
    
  return {
    ...monitoringState,
    uptime: Math.floor(uptime / 1000) // 秒
  };
}

/**
 * 设置优雅关闭
 */
function setupGracefulShutdown(): void {
  const shutdown = () => {
    console.log('\n🛑 正在关闭监控系统...');
    monitoringState.isRunning = false;
    
    // 发送关闭通知
    sendAlert({
      type: 'monitoring_shutdown',
      severity: 'warning',
      title: '监控系统关闭',
      message: '监控系统正在关闭',
      details: getMonitoringStatus()
    }).finally(() => {
      console.log('✅ 监控系统已关闭');
      process.exit(0);
    });
  };
  
  process.on('SIGINT', shutdown);
  process.on('SIGTERM', shutdown);
}

/**
 * 主函数
 */
async function main(): Promise<void> {
  try {
    await startMonitoring();
  } catch (error) {
    console.error('💥 监控系统启动失败:', error);
    process.exit(1);
  }
}

// 导出函数供其他模块使用
export { 
  startMonitoring, 
  getMonitoringStatus, 
  sendAlert,
  MONITORING_CONFIG 
};

// 如果直接运行此脚本
if (require.main === module) {
  main();
}
