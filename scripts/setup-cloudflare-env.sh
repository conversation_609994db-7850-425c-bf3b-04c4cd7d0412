#!/bin/bash

# Cloudflare 环境变量设置脚本
# 用于设置生产和staging环境的环境变量

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必需的工具
check_requirements() {
    log_info "检查必需的工具..."
    
    if ! command -v wrangler &> /dev/null; then
        log_error "wrangler CLI 未安装。请运行: npm install -g wrangler"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        log_warning "jq 未安装，某些功能可能不可用"
    fi
    
    log_success "工具检查完成"
}

# 设置环境变量
set_env_vars() {
    local env=$1
    log_info "设置 $env 环境变量..."
    
    # 基础配置
    wrangler pages secret put NODE_ENV --env=$env <<< "$env"
    wrangler pages secret put NEXT_PUBLIC_APP_ENV --env=$env <<< "$env"
    
    # 应用配置
    if [ "$env" = "production" ]; then
        wrangler pages secret put NEXT_PUBLIC_APP_URL --env=$env <<< "https://sociomint.app"
        wrangler pages secret put NEXT_PUBLIC_API_URL --env=$env <<< "https://sociomint.app/api"
    else
        wrangler pages secret put NEXT_PUBLIC_APP_URL --env=$env <<< "https://staging.sociomint.app"
        wrangler pages secret put NEXT_PUBLIC_API_URL --env=$env <<< "https://staging.sociomint.app/api"
    fi
    
    # 区块链配置
    wrangler pages secret put NEXT_PUBLIC_CHAIN_ID --env=$env <<< "97"
    wrangler pages secret put NEXT_PUBLIC_NETWORK --env=$env <<< "testnet"
    wrangler pages secret put NEXT_PUBLIC_BSC_RPC_URL --env=$env <<< "https://data-seed-prebsc-1-s1.binance.org:8545/"
    
    log_success "$env 环境基础变量设置完成"
}

# 设置敏感环境变量
set_sensitive_vars() {
    local env=$1
    log_info "设置 $env 环境敏感变量..."
    
    # 提示用户输入敏感信息
    echo "请输入以下敏感信息（输入为空将跳过）："
    
    # JWT Secret
    read -s -p "JWT_SECRET: " jwt_secret
    echo
    if [ ! -z "$jwt_secret" ]; then
        wrangler pages secret put JWT_SECRET --env=$env <<< "$jwt_secret"
    fi
    
    # NextAuth Secret
    read -s -p "NEXTAUTH_SECRET: " nextauth_secret
    echo
    if [ ! -z "$nextauth_secret" ]; then
        wrangler pages secret put NEXTAUTH_SECRET --env=$env <<< "$nextauth_secret"
    fi
    
    # Supabase配置
    read -p "NEXT_PUBLIC_SUPABASE_URL: " supabase_url
    if [ ! -z "$supabase_url" ]; then
        wrangler pages secret put NEXT_PUBLIC_SUPABASE_URL --env=$env <<< "$supabase_url"
    fi
    
    read -s -p "NEXT_PUBLIC_SUPABASE_ANON_KEY: " supabase_anon_key
    echo
    if [ ! -z "$supabase_anon_key" ]; then
        wrangler pages secret put NEXT_PUBLIC_SUPABASE_ANON_KEY --env=$env <<< "$supabase_anon_key"
    fi
    
    read -s -p "SUPABASE_SERVICE_ROLE_KEY: " supabase_service_key
    echo
    if [ ! -z "$supabase_service_key" ]; then
        wrangler pages secret put SUPABASE_SERVICE_ROLE_KEY --env=$env <<< "$supabase_service_key"
    fi
    
    # Telegram配置
    read -s -p "TELEGRAM_BOT_TOKEN: " telegram_token
    echo
    if [ ! -z "$telegram_token" ]; then
        wrangler pages secret put TELEGRAM_BOT_TOKEN --env=$env <<< "$telegram_token"
    fi
    
    read -p "TELEGRAM_BOT_USERNAME: " telegram_username
    if [ ! -z "$telegram_username" ]; then
        wrangler pages secret put TELEGRAM_BOT_USERNAME --env=$env <<< "$telegram_username"
        wrangler pages secret put NEXT_PUBLIC_TELEGRAM_BOT_USERNAME --env=$env <<< "$telegram_username"
    fi
    
    # WalletConnect配置
    read -p "NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID: " walletconnect_id
    if [ ! -z "$walletconnect_id" ]; then
        wrangler pages secret put NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID --env=$env <<< "$walletconnect_id"
    fi
    
    # Alchemy配置
    read -s -p "NEXT_PUBLIC_ALCHEMY_API_KEY: " alchemy_key
    echo
    if [ ! -z "$alchemy_key" ]; then
        wrangler pages secret put NEXT_PUBLIC_ALCHEMY_API_KEY --env=$env <<< "$alchemy_key"
    fi
    
    log_success "$env 环境敏感变量设置完成"
}

# 从文件设置环境变量
set_vars_from_file() {
    local env=$1
    local file=$2
    
    if [ ! -f "$file" ]; then
        log_error "环境变量文件 $file 不存在"
        return 1
    fi
    
    log_info "从文件 $file 设置 $env 环境变量..."
    
    # 读取文件并设置变量
    while IFS='=' read -r key value; do
        # 跳过注释和空行
        if [[ $key =~ ^#.*$ ]] || [[ -z $key ]]; then
            continue
        fi
        
        # 移除引号
        value=$(echo "$value" | sed 's/^["'\'']//' | sed 's/["'\'']$//')
        
        if [ ! -z "$value" ] && [ "$value" != "your_" ] && [[ ! $value =~ ^your_ ]]; then
            echo "设置 $key..."
            wrangler pages secret put "$key" --env=$env <<< "$value"
        fi
    done < "$file"
    
    log_success "从文件设置环境变量完成"
}

# 列出环境变量
list_env_vars() {
    local env=$1
    log_info "列出 $env 环境变量..."
    wrangler pages secret list --env=$env
}

# 删除环境变量
delete_env_var() {
    local env=$1
    local var_name=$2
    log_info "删除 $env 环境变量 $var_name..."
    wrangler pages secret delete "$var_name" --env=$env
}

# 主函数
main() {
    echo "🔧 Cloudflare 环境变量设置工具"
    echo "================================"
    
    check_requirements
    
    case "${1:-}" in
        "setup")
            env="${2:-production}"
            set_env_vars "$env"
            set_sensitive_vars "$env"
            ;;
        "setup-from-file")
            env="${2:-production}"
            file="${3:-.env.production}"
            set_vars_from_file "$env" "$file"
            ;;
        "list")
            env="${2:-production}"
            list_env_vars "$env"
            ;;
        "delete")
            env="${2:-production}"
            var_name="${3}"
            if [ -z "$var_name" ]; then
                log_error "请提供要删除的变量名"
                exit 1
            fi
            delete_env_var "$env" "$var_name"
            ;;
        *)
            echo "用法:"
            echo "  $0 setup [production|staging]           - 交互式设置环境变量"
            echo "  $0 setup-from-file [env] [file]         - 从文件设置环境变量"
            echo "  $0 list [production|staging]            - 列出环境变量"
            echo "  $0 delete [env] [var_name]              - 删除环境变量"
            echo ""
            echo "示例:"
            echo "  $0 setup production"
            echo "  $0 setup-from-file staging .env.staging"
            echo "  $0 list production"
            echo "  $0 delete staging JWT_SECRET"
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
