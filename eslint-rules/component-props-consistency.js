/**
 * ESLint规则：检查组件属性定义与实现的一致性
 * 防止TypeScript接口声明了属性但组件实现中未使用的问题
 */

module.exports = {
  meta: {
    type: 'problem',
    docs: {
      description: 'Ensure component props interface matches implementation',
      category: 'Possible Errors',
      recommended: true,
    },
    fixable: null,
    schema: [
      {
        type: 'object',
        properties: {
          ignoredProps: {
            type: 'array',
            items: { type: 'string' },
            default: ['children', 'className', 'key', 'ref']
          }
        },
        additionalProperties: false
      }
    ],
    messages: {
      missingPropImplementation: 'Property "{{prop}}" is declared in {{interface}} but not used in component implementation',
      undeclaredPropUsage: 'Property "{{prop}}" is used in component but not declared in {{interface}}',
    }
  },

  create(context) {
    const options = context.options[0] || {};
    const ignoredProps = options.ignoredProps || ['children', 'className', 'key', 'ref'];
    
    let componentName = null;
    let propsInterface = null;
    let declaredProps = new Set();
    let usedProps = new Set();

    return {
      // 检测组件函数声明
      'FunctionDeclaration[id.name=/^[A-Z]/]'(node) {
        componentName = node.id.name;
      },

      // 检测箭头函数组件
      'VariableDeclarator[id.name=/^[A-Z]/] > ArrowFunctionExpression'(node) {
        componentName = node.parent.id.name;
      },

      // 检测TypeScript接口声明
      'TSInterfaceDeclaration[id.name$="Props"]'(node) {
        if (node.id.name === `${componentName}Props`) {
          propsInterface = node.id.name;
          declaredProps.clear();
          
          // 收集接口中声明的属性
          node.body.body.forEach(member => {
            if (member.type === 'TSPropertySignature' && member.key) {
              const propName = member.key.name;
              if (!ignoredProps.includes(propName)) {
                declaredProps.add(propName);
              }
            }
          });
        }
      },

      // 检测组件参数解构
      'FunctionDeclaration[id.name=/^[A-Z]/] > :first-child[type="ObjectPattern"]'(node) {
        usedProps.clear();
        
        node.properties.forEach(prop => {
          if (prop.type === 'Property' && prop.key) {
            const propName = prop.key.name;
            if (!ignoredProps.includes(propName)) {
              usedProps.add(propName);
            }
          }
        });
      },

      // 检测props.xxx的使用
      'MemberExpression[object.name="props"]'(node) {
        if (node.property && node.property.name) {
          const propName = node.property.name;
          if (!ignoredProps.includes(propName)) {
            usedProps.add(propName);
          }
        }
      },

      // 在文件结束时进行验证
      'Program:exit'() {
        if (!componentName || !propsInterface) {
          return;
        }

        // 检查声明了但未使用的属性
        declaredProps.forEach(prop => {
          if (!usedProps.has(prop)) {
            context.report({
              node: context.getSourceCode().ast,
              messageId: 'missingPropImplementation',
              data: {
                prop,
                interface: propsInterface
              }
            });
          }
        });

        // 检查使用了但未声明的属性
        usedProps.forEach(prop => {
          if (!declaredProps.has(prop)) {
            context.report({
              node: context.getSourceCode().ast,
              messageId: 'undeclaredPropUsage',
              data: {
                prop,
                interface: propsInterface
              }
            });
          }
        });

        // 重置状态
        componentName = null;
        propsInterface = null;
        declaredProps.clear();
        usedProps.clear();
      }
    };
  }
};

/**
 * 使用示例：
 * 
 * // .eslintrc.js
 * module.exports = {
 *   rules: {
 *     'custom/component-props-consistency': ['error', {
 *       ignoredProps: ['children', 'className', 'key', 'ref']
 *     }]
 *   }
 * };
 */
