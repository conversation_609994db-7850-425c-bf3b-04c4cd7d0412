# Cloudflare Pages 配置文件
# SocioMint 项目部署配置

name = "sociomint"
compatibility_date = "2024-07-01"
compatibility_flags = ["nodejs_compat"]

# Pages 配置
[env.production]
name = "sociomint-production"
compatibility_date = "2024-01-01"

# 环境变量 (生产环境)
[env.production.vars]
NODE_ENV = "production"
NEXT_PUBLIC_APP_ENV = "production"
NEXT_PUBLIC_CHAIN_ID = "97"
NEXT_PUBLIC_NETWORK = "testnet"

# Functions 配置
[[env.production.rules]]
type = "ESModule"
globs = ["**/*.js", "**/*.mjs"]

# 开发环境配置
[env.development]
name = "sociomint-development"
compatibility_date = "2024-01-01"

[env.development.vars]
NODE_ENV = "development"
NEXT_PUBLIC_APP_ENV = "development"
NEXT_PUBLIC_CHAIN_ID = "97"
NEXT_PUBLIC_NETWORK = "testnet"

# 构建配置
[build]
command = "npm run build"
cwd = "."
watch_dir = "src"

# 部署配置
[triggers]
crons = []

# 限制和配额
[limits]
cpu_ms = 50000
memory_mb = 128

# Staging 环境配置
[env.staging]
name = "sociomint-staging"
compatibility_date = "2024-07-01"

[env.staging.vars]
NODE_ENV = "staging"
NEXT_PUBLIC_APP_ENV = "staging"
NEXT_PUBLIC_APP_URL = "https://staging.sociomint.app"
NEXT_PUBLIC_CHAIN_ID = "97"
NEXT_PUBLIC_NETWORK_NAME = "BSC Testnet"
NEXT_PUBLIC_RPC_URL = "https://data-seed-prebsc-1-s1.binance.org:8545/"
NEXT_PUBLIC_BLOCK_EXPLORER = "https://testnet.bscscan.com"
DEBUG_MODE = "true"
ENABLE_TEST_ENDPOINTS = "true"

# 安全头部配置
[[headers]]
for = "/*"
[headers.values]
X-Frame-Options = "DENY"
X-Content-Type-Options = "nosniff"
X-XSS-Protection = "1; mode=block"
Referrer-Policy = "strict-origin-when-cross-origin"
Permissions-Policy = "camera=(), microphone=(), geolocation=()"

[[headers]]
for = "/api/*"
[headers.values]
Cache-Control = "no-cache, no-store, must-revalidate"
Pragma = "no-cache"
Expires = "0"

[[headers]]
for = "/_next/static/*"
[headers.values]
Cache-Control = "public, max-age=31536000, immutable"

# 重定向规则
[[redirects]]
from = "/home"
to = "/"
status = 301

# 错误页面
[error_pages]
"404" = "/404"
"500" = "/500"
