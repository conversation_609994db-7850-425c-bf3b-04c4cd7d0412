/**
 * Next.js 性能优化配置
 * 专门用于生产环境的性能优化设置
 */

/** @type {import('next').NextConfig} */
const performanceConfig = {
  // 实验性功能
  experimental: {
    // 启用 App Router
    appDir: true,
    
    // 启用服务器组件
    serverComponentsExternalPackages: [
      '@supabase/supabase-js',
      'sharp',
      'onnxruntime-node'
    ],

    // 优化包导入
    optimizePackageImports: [
      'lucide-react',
      '@radix-ui/react-icons',
      'framer-motion',
      'date-fns',
      'lodash-es',
      'wagmi',
      'viem'
    ],
    
    // 启用 Turbopack (开发环境)
    turbo: {
      rules: {
        '*.svg': {
          loaders: ['@svgr/webpack'],
          as: '*.js',
        },
      },
    },
    
    // 启用并发特性
    concurrentFeatures: true,
    
    // 启用 React 18 特性
    reactRoot: true,
    
    // 启用增量静态再生成
    isrMemoryCacheSize: 0, // 禁用内存缓存，使用磁盘缓存
    
    // 启用边缘运行时
    runtime: 'edge',
    
    // 启用 SWC 压缩
    swcMinify: true,
    
    // 启用 SWC 文件系统缓存
    swcFileReading: true,
    
    // 启用字体优化
    fontLoaders: [
      { loader: '@next/font/google', options: { subsets: ['latin'] } },
    ],
  },

  // 编译器优化
  compiler: {
    // 移除 console.log
    removeConsole: process.env.NODE_ENV === 'production' ? {
      exclude: ['error', 'warn'],
    } : false,
    
    // React 编译器优化
    reactRemoveProperties: process.env.NODE_ENV === 'production',
    
    // 移除 data-testid
    removeTestIds: process.env.NODE_ENV === 'production',
    
    // Styled Components 支持
    styledComponents: true,
    
    // Emotion 支持
    emotion: true,
  },

  // 压缩配置
  compress: true,
  
  // 启用 SWC 压缩
  swcMinify: true,

  // 图片优化
  images: {
    // 图片格式优化
    formats: ['image/avif', 'image/webp'],
    
    // 图片尺寸
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    
    // 图片域名
    domains: [
      'images.unsplash.com',
      'via.placeholder.com',
      'supabase.co',
      'sociomint.app',
    ],
    
    // 图片加载器
    loader: 'default',
    
    // 图片质量
    quality: 85,
    
    // 启用图片优化
    unoptimized: false,
    
    // 危险的允许 SVG
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },

  // 输出配置
  output: 'standalone',
  
  // 静态导出（如果需要）
  // output: 'export',
  // trailingSlash: true,
  // skipTrailingSlashRedirect: true,

  // 构建优化
  generateBuildId: async () => {
    // 使用 git commit hash 作为 build ID
    return process.env.GIT_COMMIT_SHA || 'development';
  },

  // 页面扩展名
  pageExtensions: ['ts', 'tsx', 'js', 'jsx', 'md', 'mdx'],

  // 环境变量
  env: {
    BUILD_TIME: new Date().toISOString(),
    BUILD_ID: process.env.GIT_COMMIT_SHA || 'development',
  },

  // 重定向
  async redirects() {
    return [
      {
        source: '/home',
        destination: '/',
        permanent: true,
      },
      {
        source: '/dashboard/home',
        destination: '/dashboard',
        permanent: true,
      },
    ];
  },

  // 重写
  async rewrites() {
    return [
      // API 代理
      {
        source: '/api/proxy/:path*',
        destination: 'https://api.external-service.com/:path*',
      },
    ];
  },

  // 头部配置
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          // 安全头部
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()',
          },
        ],
      },
      {
        source: '/api/(.*)',
        headers: [
          // API 缓存控制
          {
            key: 'Cache-Control',
            value: 'no-cache, no-store, must-revalidate',
          },
        ],
      },
      {
        source: '/_next/static/(.*)',
        headers: [
          // 静态资源缓存
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
      {
        source: '/images/(.*)',
        headers: [
          // 图片缓存
          {
            key: 'Cache-Control',
            value: 'public, max-age=86400',
          },
        ],
      },
    ];
  },

  // Webpack 配置
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // 性能优化
    if (!dev && !isServer) {
      // 代码分割优化
      config.optimization.splitChunks = {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
            priority: 10,
          },
          common: {
            name: 'common',
            minChunks: 2,
            chunks: 'all',
            priority: 5,
            reuseExistingChunk: true,
          },
          react: {
            test: /[\\/]node_modules[\\/](react|react-dom)[\\/]/,
            name: 'react',
            chunks: 'all',
            priority: 20,
          },
          ui: {
            test: /[\\/]src[\\/]components[\\/]ui[\\/]/,
            name: 'ui',
            chunks: 'all',
            priority: 15,
          },
        },
      };

      // 压缩优化
      config.optimization.minimize = true;
      
      // Tree shaking
      config.optimization.usedExports = true;
      config.optimization.sideEffects = false;
    }

    // 别名配置
    config.resolve.alias = {
      ...config.resolve.alias,
      '@': path.resolve(__dirname, 'src'),
    };

    // 模块解析
    config.resolve.modules = ['node_modules', path.resolve(__dirname, 'src')];

    // 插件配置
    config.plugins.push(
      new webpack.DefinePlugin({
        __BUILD_TIME__: JSON.stringify(new Date().toISOString()),
        __BUILD_ID__: JSON.stringify(buildId),
      })
    );

    // Bundle 分析
    if (process.env.ANALYZE === 'true') {
      const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
      config.plugins.push(
        new BundleAnalyzerPlugin({
          analyzerMode: 'static',
          openAnalyzer: false,
          reportFilename: 'bundle-analyzer-report.html',
        })
      );
    }

    return config;
  },

  // TypeScript 配置
  typescript: {
    // 在构建时忽略 TypeScript 错误（不推荐）
    ignoreBuildErrors: false,
  },

  // ESLint 配置
  eslint: {
    // 在构建时忽略 ESLint 错误（不推荐）
    ignoreDuringBuilds: false,
    
    // 指定要检查的目录
    dirs: ['src', 'pages', 'components', 'lib', 'utils'],
  },

  // 性能预算
  onDemandEntries: {
    // 页面在内存中保留的时间
    maxInactiveAge: 25 * 1000,
    // 同时保留的页面数
    pagesBufferLength: 2,
  },

  // 开发服务器配置
  devIndicators: {
    buildActivity: true,
    buildActivityPosition: 'bottom-right',
  },

  // 国际化
  i18n: {
    locales: ['zh-CN', 'en-US'],
    defaultLocale: 'zh-CN',
    localeDetection: false,
  },

  // 功能标志
  productionBrowserSourceMaps: false,
  optimizeFonts: true,
  optimizeImages: true,
  
  // 静态优化
  staticPageGenerationTimeout: 60,
  
  // 服务器端渲染
  serverRuntimeConfig: {
    // 仅在服务器端可用
  },
  
  publicRuntimeConfig: {
    // 在服务器端和客户端都可用
    staticFolder: '/static',
  },
};

module.exports = performanceConfig;
