# 贡献指南

感谢您对 SocioMint 项目的关注！我们欢迎所有形式的贡献，包括但不限于代码、文档、设计、测试和反馈。

## 📋 目录

1. [行为准则](#行为准则)
2. [如何贡献](#如何贡献)
3. [开发流程](#开发流程)
4. [代码规范](#代码规范)
5. [提交规范](#提交规范)
6. [Pull Request 流程](#pull-request-流程)
7. [问题报告](#问题报告)
8. [功能建议](#功能建议)

## 🤝 行为准则

### 我们的承诺

为了营造一个开放和友好的环境，我们作为贡献者和维护者承诺，无论年龄、体型、残疾、种族、性别认同和表达、经验水平、国籍、个人形象、种族、宗教或性取向如何，参与我们项目和社区的每个人都能享受无骚扰的体验。

### 我们的标准

有助于创造积极环境的行为包括：

- ✅ 使用友好和包容的语言
- ✅ 尊重不同的观点和经验
- ✅ 优雅地接受建设性批评
- ✅ 关注对社区最有利的事情
- ✅ 对其他社区成员表示同情

不可接受的行为包括：

- ❌ 使用性化的语言或图像，以及不受欢迎的性关注或性骚扰
- ❌ 恶意评论、人身攻击或政治攻击
- ❌ 公开或私下骚扰
- ❌ 未经明确许可发布他人的私人信息
- ❌ 在专业环境中可能被认为不当的其他行为

## 🚀 如何贡献

### 贡献类型

我们欢迎以下类型的贡献：

1. **🐛 错误修复** - 修复已知问题
2. **✨ 新功能** - 添加新的功能特性
3. **📚 文档改进** - 改进文档质量
4. **🧪 测试增强** - 添加或改进测试
5. **🎨 UI/UX 改进** - 改进用户界面和体验
6. **⚡ 性能优化** - 提升应用性能
7. **🔒 安全增强** - 提高安全性
8. **🔧 工具改进** - 改进开发工具和流程

### 贡献前的准备

1. **了解项目** - 阅读 README 和相关文档
2. **搭建环境** - 按照开发指南搭建本地环境
3. **熟悉代码** - 浏览代码结构和架构
4. **查看 Issues** - 了解当前的问题和需求

## 💻 开发流程

### 1. Fork 和克隆

```bash
# Fork 项目到你的 GitHub 账户
# 然后克隆到本地
git clone https://github.com/your-username/sociomint.git
cd sociomint

# 添加上游仓库
git remote add upstream https://github.com/sociomint/sociomint.git
```

### 2. 创建分支

```bash
# 从 main 分支创建新的功能分支
git checkout -b feature/your-feature-name

# 或者修复分支
git checkout -b fix/issue-number
```

### 3. 开发环境设置

```bash
# 安装依赖
npm install

# 复制环境变量
cp .env.example .env.local

# 启动开发服务器
npm run dev
```

### 4. 开发和测试

```bash
# 运行测试
npm test

# 运行代码检查
npm run lint

# 运行类型检查
npm run type-check

# 构建项目
npm run build
```

### 5. 提交更改

```bash
# 添加更改
git add .

# 提交更改（遵循提交规范）
git commit -m "feat: add new feature"

# 推送到你的分支
git push origin feature/your-feature-name
```

## 📝 代码规范

### TypeScript 规范

```typescript
// ✅ 好的例子
interface User {
  id: number;
  username: string;
  email?: string;
  createdAt: Date;
}

function getUserById(id: number): Promise<User | null> {
  return userService.findById(id);
}

// ❌ 避免的例子
function getUser(id: any): any {
  return userService.findById(id);
}
```

### React 组件规范

```typescript
// ✅ 好的例子
interface ButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary';
  onClick?: () => void;
  disabled?: boolean;
}

export function Button({ 
  children, 
  variant = 'primary', 
  onClick, 
  disabled = false 
}: ButtonProps) {
  return (
    <button
      className={`btn btn-${variant}`}
      onClick={onClick}
      disabled={disabled}
    >
      {children}
    </button>
  );
}
```

### 样式规范

```typescript
// ✅ 使用 Tailwind CSS 类名
<div className="flex items-center justify-between p-4 bg-white rounded-lg shadow-md">
  <h2 className="text-lg font-semibold text-gray-900">Title</h2>
  <Button variant="primary">Action</Button>
</div>

// ❌ 避免内联样式
<div style={{ display: 'flex', padding: '16px' }}>
  <h2 style={{ fontSize: '18px', fontWeight: 'bold' }}>Title</h2>
</div>
```

### 命名规范

```typescript
// 文件命名: kebab-case
user-profile.tsx
api-error-handler.ts

// 组件命名: PascalCase
UserProfile
ApiErrorHandler

// 函数命名: camelCase
getUserProfile()
handleApiError()

// 常量命名: SCREAMING_SNAKE_CASE
const API_BASE_URL = 'https://api.example.com';
const MAX_RETRY_COUNT = 3;

// 类型命名: PascalCase
interface UserProfile {}
type ApiResponse<T> = {};
```

## 📋 提交规范

我们使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

### 提交类型

- `feat`: 新功能
- `fix`: 错误修复
- `docs`: 文档更新
- `style`: 代码格式调整（不影响功能）
- `refactor`: 代码重构
- `test`: 添加或修改测试
- `chore`: 构建过程或辅助工具的变动
- `perf`: 性能优化
- `ci`: CI/CD 相关更改

### 提交格式

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

### 示例

```bash
# 新功能
git commit -m "feat(auth): add Telegram authentication"

# 错误修复
git commit -m "fix(wallet): resolve balance display issue"

# 文档更新
git commit -m "docs: update API documentation"

# 重大变更
git commit -m "feat!: change API response format

BREAKING CHANGE: API responses now use 'data' field instead of 'result'"
```

## 🔄 Pull Request 流程

### 1. 创建 Pull Request

1. 推送你的分支到 GitHub
2. 在 GitHub 上创建 Pull Request
3. 填写 PR 模板中的所有必需信息
4. 添加相关的标签和里程碑

### 2. PR 模板

```markdown
## 变更描述
简要描述这个 PR 的变更内容

## 变更类型
- [ ] 错误修复
- [ ] 新功能
- [ ] 文档更新
- [ ] 性能优化
- [ ] 其他

## 测试
- [ ] 单元测试通过
- [ ] 集成测试通过
- [ ] 手动测试完成

## 检查清单
- [ ] 代码遵循项目规范
- [ ] 添加了必要的测试
- [ ] 更新了相关文档
- [ ] 没有引入新的警告

## 相关 Issue
Closes #123
```

### 3. 代码审查

- 所有 PR 都需要至少一个维护者的审查
- 自动化测试必须通过
- 代码覆盖率不能降低
- 遵循项目的代码规范

### 4. 合并要求

- ✅ 所有检查通过
- ✅ 至少一个批准的审查
- ✅ 没有合并冲突
- ✅ 分支是最新的

## 🐛 问题报告

### 报告错误

使用 [GitHub Issues](https://github.com/sociomint/sociomint/issues) 报告错误：

1. **搜索现有问题** - 确保问题尚未报告
2. **使用问题模板** - 填写所有必需信息
3. **提供详细信息** - 包括重现步骤和环境信息
4. **添加标签** - 选择适当的标签

### 错误报告模板

```markdown
## 错误描述
清晰简洁地描述错误

## 重现步骤
1. 访问 '...'
2. 点击 '....'
3. 滚动到 '....'
4. 看到错误

## 预期行为
描述你期望发生的行为

## 实际行为
描述实际发生的行为

## 环境信息
- OS: [e.g. macOS 12.0]
- Browser: [e.g. Chrome 95.0]
- Version: [e.g. 1.0.0]

## 附加信息
添加任何其他相关信息、截图或日志
```

## 💡 功能建议

### 建议新功能

使用 [GitHub Discussions](https://github.com/sociomint/sociomint/discussions) 建议新功能：

1. **描述问题** - 解释当前的限制或需求
2. **提出解决方案** - 描述你建议的解决方案
3. **考虑替代方案** - 列出其他可能的解决方案
4. **提供用例** - 给出具体的使用场景

### 功能建议模板

```markdown
## 问题描述
描述你遇到的问题或限制

## 建议的解决方案
描述你希望实现的功能

## 替代方案
描述你考虑过的其他解决方案

## 用例
提供具体的使用场景和示例

## 优先级
- [ ] 高 - 阻塞性问题
- [ ] 中 - 重要改进
- [ ] 低 - 好有更好
```

## 🏆 贡献者认可

我们重视每一个贡献，并会在以下方式认可贡献者：

- 📝 **贡献者列表** - 在 README 中列出所有贡献者
- 🎖️ **特殊徽章** - 为重要贡献者提供特殊徽章
- 📢 **社区宣传** - 在社交媒体上宣传优秀贡献
- 🎁 **奖励计划** - 为活跃贡献者提供奖励

## 📞 联系我们

如果你有任何问题或需要帮助：

- 💬 **Discord**: [SocioMint Community](https://discord.gg/sociomint)
- 📧 **邮箱**: <EMAIL>
- 🐛 **Issues**: [GitHub Issues](https://github.com/sociomint/sociomint/issues)
- 💡 **Discussions**: [GitHub Discussions](https://github.com/sociomint/sociomint/discussions)

## 📚 相关资源

- [开发者文档](./docs/DEVELOPER.md)
- [API 文档](./docs/API.md)
- [部署指南](./docs/DEPLOYMENT.md)
- [代码规范](https://github.com/sociomint/eslint-config)

---

再次感谢您的贡献！让我们一起构建更好的 SocioMint！ 🚀
