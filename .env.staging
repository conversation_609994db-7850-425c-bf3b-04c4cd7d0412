# SocioMint Staging 环境配置文件
# 用于测试和预发布环境

# 应用环境
NODE_ENV=staging
NEXT_PUBLIC_APP_ENV=staging
NEXT_PUBLIC_APP_VERSION=1.0.0-staging

# 应用 URL (Staging 环境)
NEXT_PUBLIC_APP_URL=https://staging.sociomint.app
NEXT_PUBLIC_API_URL=https://staging.sociomint.app/api

# 私钥（Staging环境使用测试密钥）
PRIVATE_KEY=YOUR_STAGING_PRIVATE_KEY_HERE

# Supabase 配置（Staging环境）
NEXT_PUBLIC_SUPABASE_URL=https://your-staging-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_staging_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_staging_service_role_key_here

# 智能合约地址 (BSC测试网)
NEXT_PUBLIC_HAOX_TOKEN_ADDRESS=******************************************
NEXT_PUBLIC_HAOX_PRESALE_ADDRESS=******************************************
NEXT_PUBLIC_HAOX_INVITATION_ADDRESS=******************************************
NEXT_PUBLIC_HAOX_PRICE_ORACLE_ADDRESS=******************************************
NEXT_PUBLIC_HAOX_VESTING_ADDRESS=******************************************

# WalletConnect 配置
NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID=your_staging_walletconnect_project_id

# 区块链配置
NEXT_PUBLIC_CHAIN_ID=97
NEXT_PUBLIC_NETWORK_NAME=BSC Testnet
NEXT_PUBLIC_RPC_URL=https://data-seed-prebsc-1-s1.binance.org:8545/
NEXT_PUBLIC_BLOCK_EXPLORER=https://testnet.bscscan.com

# Alchemy API Key（Staging环境）
NEXT_PUBLIC_ALCHEMY_API_KEY=your_staging_alchemy_api_key_here

# 社交平台 API 配置（Staging环境）
TWITTER_API_KEY=your_staging_twitter_api_key
TWITTER_API_SECRET=your_staging_twitter_api_secret
TWITTER_BEARER_TOKEN=your_staging_twitter_bearer_token

DISCORD_BOT_TOKEN=your_staging_discord_bot_token
DISCORD_CLIENT_ID=your_staging_discord_client_id
DISCORD_CLIENT_SECRET=your_staging_discord_client_secret

TELEGRAM_BOT_TOKEN=your_staging_telegram_bot_token
TELEGRAM_BOT_USERNAME=sociomint_staging_bot
NEXT_PUBLIC_TELEGRAM_BOT_USERNAME=sociomint_staging_bot
TELEGRAM_WEBHOOK_URL=https://staging.sociomint.app/api/telegram/webhook

# 支付配置（Staging环境使用沙盒）
ALIPAY_APP_ID=your_staging_alipay_app_id
ALIPAY_PRIVATE_KEY=your_staging_alipay_private_key
ALIPAY_PUBLIC_KEY=your_staging_alipay_public_key

WECHAT_PAY_APP_ID=your_staging_wechat_app_id
WECHAT_PAY_MCH_ID=your_staging_wechat_mch_id
WECHAT_PAY_API_KEY=your_staging_wechat_api_key

# 监控和分析（Staging环境）
SENTRY_DSN=your_staging_sentry_dsn
GOOGLE_ANALYTICS_ID=your_staging_ga_id
MIXPANEL_TOKEN=your_staging_mixpanel_token

# 邮件服务（Staging环境）
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_staging_email_password

# Redis 缓存（Staging环境）
REDIS_URL=redis://your-staging-redis-url:6379
REDIS_PASSWORD=your_staging_redis_password

# 安全配置（Staging环境使用测试密钥）
JWT_SECRET=staging_jwt_secret_key_here_should_be_different_from_production
NEXTAUTH_SECRET=staging_nextauth_secret_key_here
ENCRYPTION_KEY=staging_encryption_key_here
KMS_MASTER_KEY=staging_kms_master_key_here
CORS_ORIGIN=https://staging.sociomint.app,https://staging-admin.sociomint.app

# 文件存储（Staging环境）
AWS_ACCESS_KEY_ID=your_staging_aws_access_key
AWS_SECRET_ACCESS_KEY=your_staging_aws_secret_key
AWS_S3_BUCKET=sociomint-staging-assets
AWS_REGION=us-east-1

# 数据库备份（Staging环境）
DATABASE_BACKUP_URL=your_staging_backup_database_url

# 限流配置（Staging环境更宽松）
RATE_LIMIT_MAX_REQUESTS=200
RATE_LIMIT_WINDOW_MS=900000

# 日志配置
LOG_LEVEL=debug
LOG_FILE_PATH=/var/log/sociomint/staging-app.log

# 健康检查
HEALTH_CHECK_INTERVAL=30000
HEALTH_CHECK_TIMEOUT=5000

# 特性开关（Staging环境可以启用实验性功能）
FEATURE_SOCIAL_TASKS=true
FEATURE_MERCHANT_APPLICATIONS=true
FEATURE_P2P_TRADING=true
FEATURE_STAKING=true
FEATURE_NFT_MARKETPLACE=true
FEATURE_EXPERIMENTAL_FEATURES=true

# 调试配置
DEBUG_MODE=true
VERBOSE_LOGGING=true
ENABLE_PERFORMANCE_MONITORING=true

# 测试配置
ENABLE_TEST_ENDPOINTS=true
MOCK_EXTERNAL_SERVICES=false
BYPASS_RATE_LIMITING=false

# 开发工具
ENABLE_REACT_DEVTOOLS=true
ENABLE_REDUX_DEVTOOLS=true
