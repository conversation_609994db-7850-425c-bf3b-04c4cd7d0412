import React, { useState, useEffect } from 'react';
import { usePriceOracle } from '../hooks/usePriceOracle';
import { useVestingProgress } from '../hooks/useVestingProgress';
import UnlockRoadmap from './UnlockRoadmap';
import PriceChart from './PriceChart';

/**
 * HAOX价格显示主组件
 * 集成实时价格、解锁进度、路线图等功能
 */
const PriceDisplay = () => {
    const {
        currentPrice,
        bnbPrice,
        isLoading: priceLoading,
        error: priceError,
        lastUpdated,
        dataSource,
        priceChange,
        stats24h,
        priceHistory,
        formatPrice,
        formatBNBPrice,
        formatPercentage,
        refreshPrice
    } = usePriceOracle();

    const {
        unlockProgress,
        statistics,
        isLoading: vestingLoading,
        error: vestingError,
        priceProgress,
        formattedTimeRemaining,
        overallProgress,
        nextMilestone,
        formatTokens,
        refreshData
    } = useVestingProgress();

    const [activeTab, setActiveTab] = useState('overview');
    const [autoRefresh, setAutoRefresh] = useState(true);

    // 自动刷新控制
    useEffect(() => {
        if (!autoRefresh) return;
        
        const interval = setInterval(() => {
            refreshPrice();
            refreshData();
        }, 30000);
        
        return () => clearInterval(interval);
    }, [autoRefresh, refreshPrice, refreshData]);

    // 获取价格变化样式
    const getPriceChangeStyle = (change) => {
        if (change > 0) return 'text-green-500';
        if (change < 0) return 'text-red-500';
        return 'text-gray-500';
    };

    // 获取数据源状态
    const getDataSourceStatus = () => {
        switch (dataSource) {
            case 'chainlink': return { text: 'Chainlink', color: 'bg-blue-500' };
            case 'binance': return { text: 'Binance', color: 'bg-orange-500' };
            case 'weighted': return { text: 'Multi-Source', color: 'bg-green-500' };
            case 'conservative': return { text: 'Conservative', color: 'bg-yellow-500' };
            default: return { text: 'Unknown', color: 'bg-gray-500' };
        }
    };

    const dataSourceStatus = getDataSourceStatus();

    if (priceLoading || vestingLoading) {
        return (
            <div className="price-display loading">
                <div className="loading-spinner">
                    <div className="spinner"></div>
                    <p>加载价格数据中...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="price-display">
            {/* 头部控制栏 */}
            <div className="price-header">
                <div className="header-left">
                    <h1>HAOX 实时价格监控</h1>
                    <div className="last-updated">
                        最后更新: {new Date(lastUpdated).toLocaleTimeString('zh-CN')}
                    </div>
                </div>
                
                <div className="header-right">
                    <div className="data-source-indicator">
                        <span className={`status-dot ${dataSourceStatus.color}`}></span>
                        <span>{dataSourceStatus.text}</span>
                    </div>
                    
                    <button 
                        className={`auto-refresh-btn ${autoRefresh ? 'active' : ''}`}
                        onClick={() => setAutoRefresh(!autoRefresh)}
                    >
                        {autoRefresh ? '🔄 自动刷新' : '⏸️ 手动刷新'}
                    </button>
                    
                    <button 
                        className="refresh-btn"
                        onClick={() => {
                            refreshPrice();
                            refreshData();
                        }}
                    >
                        🔄 立即刷新
                    </button>
                </div>
            </div>

            {/* 错误提示 */}
            {(priceError || vestingError) && (
                <div className="error-banner">
                    <span>⚠️ {priceError || vestingError}</span>
                    <button onClick={() => window.location.reload()}>重新加载</button>
                </div>
            )}

            {/* 标签导航 */}
            <div className="tab-navigation">
                <button 
                    className={`tab ${activeTab === 'overview' ? 'active' : ''}`}
                    onClick={() => setActiveTab('overview')}
                >
                    📊 总览
                </button>
                <button 
                    className={`tab ${activeTab === 'unlock' ? 'active' : ''}`}
                    onClick={() => setActiveTab('unlock')}
                >
                    🔓 解锁进度
                </button>
                <button 
                    className={`tab ${activeTab === 'roadmap' ? 'active' : ''}`}
                    onClick={() => setActiveTab('roadmap')}
                >
                    🗺️ 路线图
                </button>
                <button 
                    className={`tab ${activeTab === 'chart' ? 'active' : ''}`}
                    onClick={() => setActiveTab('chart')}
                >
                    📈 价格图表
                </button>
            </div>

            {/* 总览标签 */}
            {activeTab === 'overview' && (
                <div className="overview-tab">
                    {/* 当前价格卡片 */}
                    <div className="price-card main-price">
                        <div className="price-header">
                            <h2>HAOX 当前价格</h2>
                            <div className="price-badges">
                                <span className="badge live">🔴 实时</span>
                                <span className="badge source">{dataSourceStatus.text}</span>
                            </div>
                        </div>
                        
                        <div className="price-main">
                            <div className="current-price">
                                <span className="price-value">{formatPrice(currentPrice)}</span>
                                <div className={`price-change ${getPriceChangeStyle(priceChange.change)}`}>
                                    <span className="change-value">{formatPrice(Math.abs(priceChange.change))}</span>
                                    <span className="change-percent">({formatPercentage(priceChange.percentage)})</span>
                                </div>
                            </div>
                            
                            <div className="price-sources">
                                <div className="source-item">
                                    <span className="label">BNB价格:</span>
                                    <span className="value">{formatBNBPrice(bnbPrice)}</span>
                                </div>
                                <div className="source-item">
                                    <span className="label">汇率:</span>
                                    <span className="value">1 BNB = 263,111 HAOX</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* 24小时统计 */}
                    {stats24h && (
                        <div className="stats-grid">
                            <div className="stat-card">
                                <div className="stat-label">24H 最高</div>
                                <div className="stat-value">{formatPrice(stats24h.high)}</div>
                            </div>
                            <div className="stat-card">
                                <div className="stat-label">24H 最低</div>
                                <div className="stat-value">{formatPrice(stats24h.low)}</div>
                            </div>
                            <div className="stat-card">
                                <div className="stat-label">24H 变化</div>
                                <div className={`stat-value ${getPriceChangeStyle(stats24h.change)}`}>
                                    {formatPercentage(stats24h.changePercent)}
                                </div>
                            </div>
                            <div className="stat-card">
                                <div className="stat-label">数据点</div>
                                <div className="stat-value">{stats24h.volume}</div>
                            </div>
                        </div>
                    )}

                    {/* 解锁统计概览 */}
                    {statistics && (
                        <div className="unlock-overview">
                            <h3>解锁统计概览</h3>
                            <div className="overview-grid">
                                <div className="overview-item">
                                    <div className="item-label">已解锁轮次</div>
                                    <div className="item-value">
                                        {statistics.totalUnlockedRounds} / 31
                                    </div>
                                    <div className="item-progress">
                                        <div 
                                            className="progress-bar"
                                            style={{ width: `${(statistics.totalUnlockedRounds / 31) * 100}%` }}
                                        />
                                    </div>
                                </div>
                                
                                <div className="overview-item">
                                    <div className="item-label">已解锁代币</div>
                                    <div className="item-value">
                                        {formatTokens(statistics.totalUnlockedTokens)}
                                    </div>
                                    <div className="item-subtitle">
                                        占总量 {((statistics.totalUnlockedTokens / 5000) * 100).toFixed(1)}%
                                    </div>
                                </div>
                                
                                <div className="overview-item">
                                    <div className="item-label">剩余代币</div>
                                    <div className="item-value">
                                        {formatTokens(statistics.remainingTokens)}
                                    </div>
                                    <div className="item-subtitle">
                                        等待解锁
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            )}

            {/* 解锁进度标签 */}
            {activeTab === 'unlock' && unlockProgress && (
                <div className="unlock-tab">
                    <div className="unlock-progress-section">
                        <h3>第 {unlockProgress.nextRoundNumber} 轮解锁进度</h3>
                        
                        <div className="progress-card">
                            <div className="progress-header">
                                <div className="round-info">
                                    <span className="round-number">第 {unlockProgress.nextRoundNumber} 轮</span>
                                    <span className="target-price">
                                        目标: {formatPrice(unlockProgress.nextRoundTriggerPrice)}
                                    </span>
                                </div>
                                
                                <div className="progress-status">
                                    {unlockProgress.priceConditionMet ? (
                                        <span className="status-badge met">✅ 价格条件达成</span>
                                    ) : (
                                        <span className="status-badge pending">⏳ 等待价格达成</span>
                                    )}
                                </div>
                            </div>
                            
                            <div className="progress-visual">
                                <div className="progress-bar-container">
                                    <div 
                                        className="progress-bar-fill"
                                        style={{ width: `${priceProgress}%` }}
                                    />
                                    <div className="progress-text">
                                        {priceProgress.toFixed(1)}%
                                    </div>
                                </div>
                                
                                <div className="progress-labels">
                                    <span>当前: {formatPrice(unlockProgress.currentPrice)}</span>
                                    <span>目标: {formatPrice(unlockProgress.nextRoundTriggerPrice)}</span>
                                </div>
                            </div>
                            
                            <div className="progress-details">
                                <div className="detail-row">
                                    <span className="label">价格差距:</span>
                                    <span className="value">
                                        {formatPrice(unlockProgress.nextRoundTriggerPrice - unlockProgress.currentPrice)}
                                        ({((unlockProgress.nextRoundTriggerPrice / unlockProgress.currentPrice - 1) * 100).toFixed(1)}%)
                                    </span>
                                </div>
                                
                                {unlockProgress.priceConditionMet && (
                                    <div className="maintenance-period">
                                        <div className="period-header">
                                            <span className="icon">⏰</span>
                                            <span className="title">7天价格维持期</span>
                                        </div>
                                        <div className="time-remaining">
                                            剩余时间: {formattedTimeRemaining}
                                        </div>
                                        <div className="maintenance-progress">
                                            <div 
                                                className="maintenance-bar"
                                                style={{ 
                                                    width: `${Math.max(0, (604800 - unlockProgress.timeRemaining) / 604800 * 100)}%` 
                                                }}
                                            />
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                        
                        {/* 解锁奖励分配 */}
                        <div className="reward-distribution">
                            <h4>解锁奖励分配</h4>
                            <div className="distribution-grid">
                                <div className="distribution-item">
                                    <div className="item-icon">🏢</div>
                                    <div className="item-info">
                                        <div className="item-label">项目钱包</div>
                                        <div className="item-value">60,000,000 HAOX</div>
                                        <div className="item-percent">40%</div>
                                    </div>
                                </div>
                                
                                <div className="distribution-item">
                                    <div className="item-icon">👥</div>
                                    <div className="item-info">
                                        <div className="item-label">社区钱包</div>
                                        <div className="item-value">90,000,000 HAOX</div>
                                        <div className="item-percent">60%</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        {/* 下一个里程碑 */}
                        {nextMilestone && (
                            <div className="next-milestone">
                                <h4>下一个里程碑</h4>
                                <div className="milestone-card">
                                    <div className="milestone-info">
                                        <span className="milestone-round">第 {nextMilestone.roundNumber} 轮</span>
                                        <span className="milestone-desc">{nextMilestone.description}</span>
                                    </div>
                                    <div className="milestone-details">
                                        <div className="detail">
                                            <span className="label">目标价格:</span>
                                            <span className="value">{formatPrice(nextMilestone.triggerPrice)}</span>
                                        </div>
                                        <div className="detail">
                                            <span className="label">解锁代币:</span>
                                            <span className="value">{nextMilestone.tokensToUnlock}亿 HAOX</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            )}

            {/* 路线图标签 */}
            {activeTab === 'roadmap' && (
                <div className="roadmap-tab">
                    <UnlockRoadmap 
                        currentRound={statistics?.totalUnlockedRounds || 1}
                        currentPrice={currentPrice}
                    />
                </div>
            )}

            {/* 价格图表标签 */}
            {activeTab === 'chart' && (
                <div className="chart-tab">
                    <PriceChart 
                        data={priceHistory}
                        currentPrice={currentPrice}
                        unlockProgress={unlockProgress}
                    />
                </div>
            )}
        </div>
    );
};

export default PriceDisplay;
