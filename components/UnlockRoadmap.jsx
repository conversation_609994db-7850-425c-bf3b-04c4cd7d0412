import React, { useState, useMemo } from 'react';

/**
 * HAOX解锁路线图组件
 * 显示所有31轮的解锁价格阶梯和状态
 */
const UnlockRoadmap = ({ currentRound = 1, currentPrice = 0 }) => {
    const [viewMode, setViewMode] = useState('timeline'); // timeline, grid, phases
    const [showDetails, setShowDetails] = useState(false);

    // 生成所有轮次的路线图数据
    const roadmapData = useMemo(() => {
        const rounds = [];
        let price = 0.003041; // 基准价格
        
        // 第1轮（已完成）
        rounds.push({
            round: 1,
            price: price,
            increase: 'Base',
            status: 'completed',
            tokens: '5亿 HAOX',
            description: '预售完成',
            phase: 'presale',
            phaseColor: 'bg-blue-500',
            allocation: { project: '2亿', community: '3亿' }
        });
        
        // 第2-11轮：每轮上涨100%
        for (let i = 2; i <= 11; i++) {
            price *= 2;
            rounds.push({
                round: i,
                price: price,
                increase: '+100%',
                status: getStatus(i, currentRound),
                tokens: '1.5亿 HAOX',
                description: '高增长期',
                phase: 'growth',
                phaseColor: 'bg-green-500',
                allocation: { project: '6000万', community: '9000万' }
            });
        }
        
        // 第12-21轮：每轮上涨50%
        for (let i = 12; i <= 21; i++) {
            price *= 1.5;
            rounds.push({
                round: i,
                price: price,
                increase: '+50%',
                status: getStatus(i, currentRound),
                tokens: '1.5亿 HAOX',
                description: '稳定增长期',
                phase: 'stable',
                phaseColor: 'bg-yellow-500',
                allocation: { project: '6000万', community: '9000万' }
            });
        }
        
        // 第22-31轮：每轮上涨20%
        for (let i = 22; i <= 31; i++) {
            price *= 1.2;
            rounds.push({
                round: i,
                price: price,
                increase: '+20%',
                status: getStatus(i, currentRound),
                tokens: '1.5亿 HAOX',
                description: '成熟期',
                phase: 'mature',
                phaseColor: 'bg-purple-500',
                allocation: { project: '6000万', community: '9000万' }
            });
        }
        
        return rounds;
    }, [currentRound]);

    // 获取轮次状态
    function getStatus(round, current) {
        if (round <= current) return 'completed';
        if (round === current + 1) return 'active';
        return 'pending';
    }

    // 获取状态图标
    const getStatusIcon = (status) => {
        switch (status) {
            case 'completed': return '✅';
            case 'active': return '🔄';
            case 'pending': return '⏳';
            default: return '⚪';
        }
    };

    // 获取状态样式
    const getStatusClass = (status) => {
        switch (status) {
            case 'completed': return 'completed';
            case 'active': return 'active';
            case 'pending': return 'pending';
            default: return 'default';
        }
    };

    // 计算阶段统计
    const phaseStats = useMemo(() => {
        const phases = {
            presale: { name: '预售阶段', rounds: [], color: 'bg-blue-500' },
            growth: { name: '高增长期', rounds: [], color: 'bg-green-500' },
            stable: { name: '稳定增长期', rounds: [], color: 'bg-yellow-500' },
            mature: { name: '成熟期', rounds: [], color: 'bg-purple-500' }
        };

        roadmapData.forEach(round => {
            phases[round.phase].rounds.push(round);
        });

        return Object.entries(phases).map(([key, phase]) => ({
            key,
            ...phase,
            completed: phase.rounds.filter(r => r.status === 'completed').length,
            total: phase.rounds.length,
            priceRange: phase.rounds.length > 0 ? {
                min: Math.min(...phase.rounds.map(r => r.price)),
                max: Math.max(...phase.rounds.map(r => r.price))
            } : null
        }));
    }, [roadmapData]);

    // 时间线视图
    const TimelineView = () => (
        <div className="roadmap-timeline">
            <div className="timeline-container">
                {roadmapData.map((round, index) => (
                    <div key={round.round} className={`timeline-item ${getStatusClass(round.status)}`}>
                        <div className="timeline-marker">
                            <div className={`marker-dot ${round.phaseColor}`}>
                                <span className="status-icon">{getStatusIcon(round.status)}</span>
                            </div>
                            <div className="round-number">R{round.round}</div>
                        </div>
                        
                        <div className="timeline-content">
                            <div className="round-header">
                                <div className="price-info">
                                    <span className="price">${round.price.toFixed(6)}</span>
                                    <span className={`increase ${round.increase === 'Base' ? 'base' : 'positive'}`}>
                                        {round.increase}
                                    </span>
                                </div>
                                <div className="phase-badge">
                                    <span className={`badge ${round.phaseColor}`}>
                                        {round.description}
                                    </span>
                                </div>
                            </div>
                            
                            <div className="round-details">
                                <div className="token-info">
                                    <span className="tokens">{round.tokens}</span>
                                </div>
                                
                                {showDetails && (
                                    <div className="allocation-details">
                                        <div className="allocation-item">
                                            <span className="label">项目:</span>
                                            <span className="value">{round.allocation.project}</span>
                                        </div>
                                        <div className="allocation-item">
                                            <span className="label">社区:</span>
                                            <span className="value">{round.allocation.community}</span>
                                        </div>
                                    </div>
                                )}
                            </div>
                            
                            {/* 当前价格指示器 */}
                            {currentPrice > 0 && currentPrice >= round.price * 0.8 && currentPrice <= round.price * 1.2 && (
                                <div className="current-price-indicator">
                                    <span className="indicator-line"></span>
                                    <span className="indicator-text">当前价格区间</span>
                                </div>
                            )}
                        </div>
                        
                        {index < roadmapData.length - 1 && (
                            <div className="timeline-connector"></div>
                        )}
                    </div>
                ))}
            </div>
        </div>
    );

    // 网格视图
    const GridView = () => (
        <div className="roadmap-grid">
            {roadmapData.map((round) => (
                <div key={round.round} className={`grid-item ${getStatusClass(round.status)}`}>
                    <div className="grid-header">
                        <span className="round-number">R{round.round}</span>
                        <span className="status-icon">{getStatusIcon(round.status)}</span>
                    </div>
                    
                    <div className="grid-content">
                        <div className="price">${round.price.toFixed(6)}</div>
                        <div className="increase">{round.increase}</div>
                        <div className="tokens">{round.tokens}</div>
                    </div>
                    
                    <div className={`phase-indicator ${round.phaseColor}`}></div>
                </div>
            ))}
        </div>
    );

    // 阶段视图
    const PhasesView = () => (
        <div className="roadmap-phases">
            {phaseStats.map((phase) => (
                <div key={phase.key} className="phase-section">
                    <div className="phase-header">
                        <div className="phase-title">
                            <div className={`phase-color ${phase.color}`}></div>
                            <h3>{phase.name}</h3>
                            <span className="phase-progress">
                                {phase.completed}/{phase.total} 完成
                            </span>
                        </div>
                        
                        {phase.priceRange && (
                            <div className="phase-price-range">
                                <span>价格范围: ${phase.priceRange.min.toFixed(6)} - ${phase.priceRange.max.toFixed(2)}</span>
                            </div>
                        )}
                    </div>
                    
                    <div className="phase-progress-bar">
                        <div 
                            className="progress-fill"
                            style={{ width: `${(phase.completed / phase.total) * 100}%` }}
                        ></div>
                    </div>
                    
                    <div className="phase-rounds">
                        {phase.rounds.map((round) => (
                            <div key={round.round} className={`phase-round ${getStatusClass(round.status)}`}>
                                <span className="round-num">R{round.round}</span>
                                <span className="round-price">${round.price.toFixed(6)}</span>
                                <span className="round-status">{getStatusIcon(round.status)}</span>
                            </div>
                        ))}
                    </div>
                </div>
            ))}
        </div>
    );

    return (
        <div className="unlock-roadmap">
            {/* 头部控制 */}
            <div className="roadmap-header">
                <div className="header-left">
                    <h3>HAOX解锁路线图</h3>
                    <div className="roadmap-stats">
                        <span>已完成: {currentRound}/31 轮</span>
                        <span>总进度: {((currentRound / 31) * 100).toFixed(1)}%</span>
                    </div>
                </div>
                
                <div className="header-controls">
                    <div className="view-mode-selector">
                        <button 
                            className={`mode-btn ${viewMode === 'timeline' ? 'active' : ''}`}
                            onClick={() => setViewMode('timeline')}
                        >
                            📅 时间线
                        </button>
                        <button 
                            className={`mode-btn ${viewMode === 'grid' ? 'active' : ''}`}
                            onClick={() => setViewMode('grid')}
                        >
                            🔲 网格
                        </button>
                        <button 
                            className={`mode-btn ${viewMode === 'phases' ? 'active' : ''}`}
                            onClick={() => setViewMode('phases')}
                        >
                            📊 阶段
                        </button>
                    </div>
                    
                    <button 
                        className={`details-toggle ${showDetails ? 'active' : ''}`}
                        onClick={() => setShowDetails(!showDetails)}
                    >
                        {showDetails ? '🔼 隐藏详情' : '🔽 显示详情'}
                    </button>
                </div>
            </div>

            {/* 阶段概览 */}
            <div className="phases-overview">
                {phaseStats.map((phase) => (
                    <div key={phase.key} className="phase-overview-item">
                        <div className={`phase-indicator ${phase.color}`}></div>
                        <div className="phase-info">
                            <div className="phase-name">{phase.name}</div>
                            <div className="phase-progress-text">
                                {phase.completed}/{phase.total}
                            </div>
                        </div>
                    </div>
                ))}
            </div>

            {/* 主要内容区域 */}
            <div className="roadmap-content">
                {viewMode === 'timeline' && <TimelineView />}
                {viewMode === 'grid' && <GridView />}
                {viewMode === 'phases' && <PhasesView />}
            </div>

            {/* 底部统计 */}
            <div className="roadmap-summary">
                <div className="summary-grid">
                    <div className="summary-item">
                        <div className="item-label">已完成轮次</div>
                        <div className="item-value">{currentRound}/31</div>
                    </div>
                    <div className="summary-item">
                        <div className="item-label">已解锁代币</div>
                        <div className="item-value">{(currentRound === 1 ? 5 : 5 + (currentRound - 1) * 1.5).toFixed(1)}亿</div>
                    </div>
                    <div className="summary-item">
                        <div className="item-label">剩余代币</div>
                        <div className="item-value">{(50 - (currentRound === 1 ? 5 : 5 + (currentRound - 1) * 1.5)).toFixed(1)}亿</div>
                    </div>
                    <div className="summary-item">
                        <div className="item-label">最终目标价格</div>
                        <div className="item-value">${roadmapData[roadmapData.length - 1].price.toFixed(2)}</div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default UnlockRoadmap;
