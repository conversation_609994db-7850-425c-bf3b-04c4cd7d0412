import React, { useState, useEffect, useCallback } from 'react';
import { useSecureWallet } from '../hooks/useSecureWallet';

/**
 * 安全版本的HAOX管理面板组件
 * 使用安全钱包连接，分离只读查询和签名操作
 */
const AdminPanelSecure = () => {
    const {
        account,
        isConnected,
        chainId,
        balance,
        isConnecting,
        error: walletError,
        connectForSigning,
        connectReadOnly,
        executeTransaction,
        queryContract,
        getTransactionStatus,
        disconnect,
        refreshAccount,
        formatAddress,
        formatBalance,
        isCorrectNetwork
    } = useSecureWallet();

    const [allRounds, setAllRounds] = useState([]);
    const [statistics, setStatistics] = useState(null);
    const [priceHistory, setPriceHistory] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState('');
    const [logs, setLogs] = useState([]);
    const [lastTxHash, setLastTxHash] = useState('');
    const [txStatus, setTxStatus] = useState(null);

    // 合约配置
    const VESTING_CONTRACT_ADDRESS = process.env.NEXT_PUBLIC_HAOX_VESTING_ADDRESS_V2_FIXED;
    const vestingABI = [
        'function checkPriceCondition()',
        'function getUnlockProgress() view returns (uint256, uint256, uint256, bool, uint256, uint256)',
        'function getRoundInfo(uint256) view returns (uint256, bool, bool, uint256, uint256, uint256, uint256)',
        'function getUnlockStatistics() view returns (uint256, uint256, uint256, uint256, uint256)',
        'function getPriceCheckHistory(uint256, uint256) view returns (tuple(uint256,uint256,uint256,bool)[])',
        'function currentRound() view returns (uint256)',
        'function TOTAL_ROUNDS() view returns (uint256)',
        'function owner() view returns (address)',
        'function pause()',
        'function unpause()',
        'function paused() view returns (bool)',
        'event PriceConditionMet(uint256 indexed roundNumber, uint256 price, uint256 timestamp)',
        'event RoundUnlocked(uint256 indexed roundNumber, uint256 triggerPrice, uint256 projectTokens, uint256 communityTokens, uint256 timestamp)',
        'event PriceConditionReset(uint256 indexed roundNumber, uint256 price, uint256 timestamp)'
    ];

    // 添加日志
    const addLog = useCallback((message, type = 'info') => {
        const timestamp = new Date().toLocaleTimeString('zh-CN');
        const logEntry = {
            id: Date.now(),
            timestamp,
            message,
            type
        };
        setLogs(prev => [...prev.slice(-49), logEntry]);
    }, []);

    // 初始化只读连接
    useEffect(() => {
        const initReadOnlyConnection = async () => {
            try {
                await connectReadOnly();
                addLog('✅ 只读连接初始化成功');
                await loadAllData();
            } catch (error) {
                addLog(`❌ 只读连接失败: ${error.message}`, 'error');
                setError('只读连接失败: ' + error.message);
            }
        };

        initReadOnlyConnection();
    }, [connectReadOnly]);

    // 加载所有数据
    const loadAllData = useCallback(async () => {
        setIsLoading(true);
        setError('');
        
        try {
            addLog('📊 开始加载数据...');
            
            // 并行加载数据
            const [roundsData, statsData] = await Promise.all([
                loadAllRounds(),
                loadStatistics()
            ]);

            setAllRounds(roundsData);
            setStatistics(statsData);
            
            // 加载最近的价格检查历史
            if (statsData) {
                await loadPriceHistory(statsData.totalUnlockedRounds + 1);
            }
            
            addLog('✅ 数据加载完成');

        } catch (error) {
            const errorMsg = `数据加载失败: ${error.message}`;
            setError(errorMsg);
            addLog(`❌ ${errorMsg}`, 'error');
        } finally {
            setIsLoading(false);
        }
    }, []);

    // 加载所有轮次信息
    const loadAllRounds = useCallback(async () => {
        try {
            const totalRounds = await queryContract(VESTING_CONTRACT_ADDRESS, vestingABI, 'TOTAL_ROUNDS');
            const rounds = [];

            for (let i = 1; i <= Number(totalRounds); i++) {
                const roundInfo = await queryContract(VESTING_CONTRACT_ADDRESS, vestingABI, 'getRoundInfo', [i]);
                rounds.push({
                    roundNumber: i,
                    triggerPrice: parseFloat(ethers.formatUnits(roundInfo[0], 8)),
                    priceConditionMet: roundInfo[1],
                    unlocked: roundInfo[2],
                    priceReachedTime: Number(roundInfo[3]),
                    unlockTime: Number(roundInfo[4]),
                    projectTokens: parseFloat(ethers.formatEther(roundInfo[5])),
                    communityTokens: parseFloat(ethers.formatEther(roundInfo[6]))
                });
            }

            return rounds;
        } catch (error) {
            console.error('加载轮次信息失败:', error);
            throw error;
        }
    }, [queryContract]);

    // 加载统计信息
    const loadStatistics = useCallback(async () => {
        try {
            const stats = await queryContract(VESTING_CONTRACT_ADDRESS, vestingABI, 'getUnlockStatistics');
            return {
                totalUnlockedRounds: Number(stats[0]),
                totalUnlockedTokens: parseFloat(ethers.formatEther(stats[1])),
                totalProjectTokens: parseFloat(ethers.formatEther(stats[2])),
                totalCommunityTokens: parseFloat(ethers.formatEther(stats[3])),
                remainingTokens: parseFloat(ethers.formatEther(stats[4]))
            };
        } catch (error) {
            console.error('加载统计信息失败:', error);
            throw error;
        }
    }, [queryContract]);

    // 加载价格检查历史
    const loadPriceHistory = useCallback(async (roundNumber) => {
        try {
            const history = await queryContract(VESTING_CONTRACT_ADDRESS, vestingABI, 'getPriceCheckHistory', [roundNumber, 50]);
            const formattedHistory = history.map(item => ({
                timestamp: Number(item[0]) * 1000,
                price: parseFloat(ethers.formatUnits(item[1], 8)),
                targetPrice: parseFloat(ethers.formatUnits(item[2], 8)),
                conditionMet: item[3]
            }));
            setPriceHistory(formattedHistory);
        } catch (error) {
            console.error('加载价格历史失败:', error);
            // 不抛出错误，因为这不是关键功能
        }
    }, [queryContract]);

    // 手动触发价格检查
    const triggerPriceCheck = useCallback(async () => {
        if (!isConnected) {
            addLog('⚠️  请先连接钱包', 'warning');
            return;
        }

        if (!isCorrectNetwork()) {
            addLog('⚠️  请切换到正确的网络', 'warning');
            return;
        }

        setIsLoading(true);
        try {
            addLog('🔍 手动触发价格检查...');
            
            const result = await executeTransaction(
                VESTING_CONTRACT_ADDRESS,
                vestingABI,
                'checkPriceCondition'
            );

            setLastTxHash(result.hash);
            addLog(`📤 交易已发送: ${result.hash}`);
            
            // 等待交易确认
            const receipt = await result.wait();
            addLog(`✅ 价格检查完成, Gas使用: ${receipt.gasUsed.toString()}`);
            
            // 刷新数据
            await loadAllData();
            
            // 更新交易状态
            setTxStatus({ status: 'success', receipt });

        } catch (error) {
            const errorMsg = `价格检查失败: ${error.message}`;
            setError(errorMsg);
            addLog(`❌ ${errorMsg}`, 'error');
            setTxStatus({ status: 'failed', error: error.message });
        } finally {
            setIsLoading(false);
        }
    }, [isConnected, isCorrectNetwork, executeTransaction, loadAllData]);

    // 暂停/恢复合约
    const togglePause = useCallback(async () => {
        if (!isConnected) {
            addLog('⚠️  请先连接钱包', 'warning');
            return;
        }

        try {
            // 检查当前暂停状态
            const isPaused = await queryContract(VESTING_CONTRACT_ADDRESS, vestingABI, 'paused');
            const action = isPaused ? 'unpause' : 'pause';
            const actionText = isPaused ? '恢复' : '暂停';
            
            addLog(`🔄 ${actionText}合约...`);
            
            const result = await executeTransaction(
                VESTING_CONTRACT_ADDRESS,
                vestingABI,
                action
            );

            addLog(`📤 ${actionText}交易已发送: ${result.hash}`);
            
            const receipt = await result.wait();
            addLog(`✅ 合约${actionText}成功`);
            
            // 刷新数据
            await loadAllData();

        } catch (error) {
            const errorMsg = `合约操作失败: ${error.message}`;
            setError(errorMsg);
            addLog(`❌ ${errorMsg}`, 'error');
        }
    }, [isConnected, queryContract, executeTransaction, loadAllData]);

    // 检查交易状态
    const checkTransactionStatus = useCallback(async () => {
        if (!lastTxHash) return;
        
        try {
            const status = await getTransactionStatus(lastTxHash);
            setTxStatus(status);
            
            if (status.status === 'success') {
                addLog(`✅ 交易确认成功: ${lastTxHash}`);
            } else if (status.status === 'failed') {
                addLog(`❌ 交易失败: ${lastTxHash}`, 'error');
            }
        } catch (error) {
            console.error('检查交易状态失败:', error);
        }
    }, [lastTxHash, getTransactionStatus]);

    // 定期检查交易状态
    useEffect(() => {
        if (lastTxHash && txStatus?.status === 'pending') {
            const interval = setInterval(checkTransactionStatus, 5000);
            return () => clearInterval(interval);
        }
    }, [lastTxHash, txStatus, checkTransactionStatus]);

    // 获取轮次状态样式
    const getRoundStatusClass = (round) => {
        if (round.unlocked) return 'status-completed';
        if (round.priceConditionMet) return 'status-waiting';
        return 'status-pending';
    };

    // 获取轮次状态文本
    const getRoundStatusText = (round) => {
        if (round.unlocked) return '已解锁';
        if (round.priceConditionMet) return '维持期';
        return '待解锁';
    };

    // 获取日志样式
    const getLogClass = (type) => {
        switch (type) {
            case 'error': return 'log-error';
            case 'warning': return 'log-warning';
            case 'success': return 'log-success';
            default: return 'log-info';
        }
    };

    return (
        <div className="admin-panel-secure">
            {/* 头部 */}
            <div className="panel-header">
                <div className="header-left">
                    <h1>🔧 HAOX 安全管理面板</h1>
                    <div className="version-info">
                        <span className="version-badge">V2.1 安全版</span>
                        <span className="security-badge">🛡️ 安全模式</span>
                    </div>
                </div>
                
                <div className="header-controls">
                    {!isConnected ? (
                        <button 
                            className="connect-btn"
                            onClick={connectForSigning}
                            disabled={isConnecting}
                        >
                            {isConnecting ? '🔄 连接中...' : '🔗 连接钱包'}
                        </button>
                    ) : (
                        <div className="wallet-info">
                            <div className="wallet-details">
                                <div className="wallet-address">
                                    👤 {formatAddress(account)}
                                </div>
                                <div className="wallet-balance">
                                    💰 {formatBalance(balance)} BNB
                                </div>
                                <div className="network-status">
                                    🔗 {isCorrectNetwork() ? '✅ 正确网络' : '❌ 错误网络'}
                                </div>
                            </div>
                            <div className="wallet-actions">
                                <button className="refresh-btn" onClick={refreshAccount}>
                                    🔄 刷新
                                </button>
                                <button className="refresh-btn" onClick={() => loadAllData()}>
                                    📊 刷新数据
                                </button>
                                <button className="disconnect-btn" onClick={disconnect}>
                                    🔌 断开
                                </button>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* 错误提示 */}
            {(error || walletError) && (
                <div className="error-banner">
                    <span>⚠️ {error || walletError}</span>
                    <button onClick={() => { setError(''); }}>✕</button>
                </div>
            )}

            {/* 网络警告 */}
            {isConnected && !isCorrectNetwork() && (
                <div className="warning-banner">
                    <span>⚠️ 请切换到正确的网络</span>
                    <button onClick={checkAndSwitchNetwork}>🔄 切换网络</button>
                </div>
            )}

            <div className="panel-content">
                {/* 统计概览 */}
                {statistics && (
                    <div className="stats-overview">
                        <div className="stat-card">
                            <div className="stat-label">已解锁轮次</div>
                            <div className="stat-value">{statistics.totalUnlockedRounds}/31</div>
                            <div className="stat-progress">
                                <div 
                                    className="progress-bar"
                                    style={{ width: `${(statistics.totalUnlockedRounds / 31) * 100}%` }}
                                />
                            </div>
                        </div>
                        
                        <div className="stat-card">
                            <div className="stat-label">已解锁代币</div>
                            <div className="stat-value">{statistics.totalUnlockedTokens.toLocaleString()}M</div>
                            <div className="stat-subtitle">HAOX</div>
                        </div>
                        
                        <div className="stat-card">
                            <div className="stat-label">剩余代币</div>
                            <div className="stat-value">{statistics.remainingTokens.toLocaleString()}M</div>
                            <div className="stat-subtitle">HAOX</div>
                        </div>
                        
                        <div className="stat-card">
                            <div className="stat-label">总体进度</div>
                            <div className="stat-value">{((statistics.totalUnlockedTokens / 5000) * 100).toFixed(1)}%</div>
                            <div className="stat-subtitle">完成度</div>
                        </div>
                    </div>
                )}

                {/* 操作控制 */}
                <div className="control-panel">
                    <h3>🎛️ 安全操作控制</h3>
                    <div className="control-buttons">
                        <button 
                            className="control-btn primary"
                            onClick={triggerPriceCheck}
                            disabled={isLoading || !isConnected || !isCorrectNetwork()}
                        >
                            {isLoading ? '⏳ 处理中...' : '🔍 手动价格检查'}
                        </button>
                        
                        <button 
                            className="control-btn secondary"
                            onClick={togglePause}
                            disabled={isLoading || !isConnected || !isCorrectNetwork()}
                        >
                            🛑 暂停/恢复合约
                        </button>
                        
                        <button 
                            className="control-btn info"
                            onClick={() => window.open(`https://testnet.bscscan.com/address/${VESTING_CONTRACT_ADDRESS}`, '_blank')}
                        >
                            🔗 查看合约
                        </button>
                        
                        {lastTxHash && (
                            <button 
                                className="control-btn info"
                                onClick={() => window.open(`https://testnet.bscscan.com/tx/${lastTxHash}`, '_blank')}
                            >
                                🔍 查看交易
                            </button>
                        )}
                    </div>
                    
                    {/* 交易状态 */}
                    {txStatus && (
                        <div className={`tx-status ${txStatus.status}`}>
                            <span className="status-icon">
                                {txStatus.status === 'success' ? '✅' : 
                                 txStatus.status === 'failed' ? '❌' : 
                                 txStatus.status === 'pending' ? '⏳' : '❓'}
                            </span>
                            <span className="status-text">
                                交易状态: {txStatus.status === 'success' ? '成功' : 
                                         txStatus.status === 'failed' ? '失败' : 
                                         txStatus.status === 'pending' ? '待确认' : '未知'}
                            </span>
                        </div>
                    )}
                </div>

                {/* 轮次状态表格 */}
                <div className="rounds-table">
                    <h3>📊 轮次状态</h3>
                    <div className="table-container">
                        <table>
                            <thead>
                                <tr>
                                    <th>轮次</th>
                                    <th>触发价格</th>
                                    <th>状态</th>
                                    <th>项目代币</th>
                                    <th>社区代币</th>
                                    <th>解锁时间</th>
                                </tr>
                            </thead>
                            <tbody>
                                {allRounds.slice(0, 10).map(round => (
                                    <tr key={round.roundNumber} className={getRoundStatusClass(round)}>
                                        <td>第{round.roundNumber}轮</td>
                                        <td>${round.triggerPrice.toFixed(6)}</td>
                                        <td>
                                            <span className="status-badge">
                                                {getRoundStatusText(round)}
                                            </span>
                                        </td>
                                        <td>{round.projectTokens.toLocaleString()}M</td>
                                        <td>{round.communityTokens.toLocaleString()}M</td>
                                        <td>
                                            {round.unlockTime > 0 
                                                ? new Date(round.unlockTime * 1000).toLocaleString('zh-CN')
                                                : '-'
                                            }
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>

                {/* 价格检查历史 */}
                {priceHistory.length > 0 && (
                    <div className="price-history">
                        <h3>📈 价格检查历史</h3>
                        <div className="history-container">
                            {priceHistory.slice(-10).map((check, index) => (
                                <div key={index} className={`history-item ${check.conditionMet ? 'met' : 'not-met'}`}>
                                    <div className="check-time">
                                        {new Date(check.timestamp).toLocaleString('zh-CN')}
                                    </div>
                                    <div className="check-prices">
                                        ${check.price.toFixed(8)} / ${check.targetPrice.toFixed(8)}
                                    </div>
                                    <div className="check-status">
                                        {check.conditionMet ? '✅' : '❌'}
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                )}

                {/* 操作日志 */}
                <div className="logs-panel">
                    <h3>📝 操作日志</h3>
                    <div className="logs-container">
                        {logs.map((log) => (
                            <div key={log.id} className={`log-entry ${getLogClass(log.type)}`}>
                                <span className="log-timestamp">[{log.timestamp}]</span>
                                <span className="log-message">{log.message}</span>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default AdminPanelSecure;
