import React, { useState, useEffect } from 'react';
import { ethers } from 'ethers';

/**
 * HAOX管理面板组件
 * 显示解锁状态、价格历史、手动触发功能
 */
const AdminPanel = () => {
    const [isConnected, setIsConnected] = useState(false);
    const [account, setAccount] = useState('');
    const [contract, setContract] = useState(null);
    const [allRounds, setAllRounds] = useState([]);
    const [statistics, setStatistics] = useState(null);
    const [priceHistory, setPriceHistory] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState('');
    const [logs, setLogs] = useState([]);

    // 合约配置
    const VESTING_CONTRACT_ADDRESS = process.env.NEXT_PUBLIC_HAOX_VESTING_ADDRESS_V2_FIXED;
    const vestingABI = [
        'function checkPriceCondition()',
        'function getUnlockProgress() view returns (uint256, uint256, uint256, bool, uint256, uint256)',
        'function getRoundInfo(uint256) view returns (uint256, bool, bool, uint256, uint256, uint256, uint256)',
        'function getUnlockStatistics() view returns (uint256, uint256, uint256, uint256, uint256)',
        'function getPriceCheckHistory(uint256, uint256) view returns (tuple(uint256,uint256,uint256,bool)[])',
        'function currentRound() view returns (uint256)',
        'function TOTAL_ROUNDS() view returns (uint256)',
        'function owner() view returns (address)',
        'function pause()',
        'function unpause()',
        'function updatePriceOracle(address)',
        'event PriceConditionMet(uint256 indexed roundNumber, uint256 price, uint256 timestamp)',
        'event RoundUnlocked(uint256 indexed roundNumber, uint256 triggerPrice, uint256 projectTokens, uint256 communityTokens, uint256 timestamp)',
        'event PriceConditionReset(uint256 indexed roundNumber, uint256 price, uint256 timestamp)'
    ];

    // 连接钱包
    const connectWallet = async () => {
        try {
            if (!window.ethereum) {
                throw new Error('请安装MetaMask钱包');
            }

            const accounts = await window.ethereum.request({
                method: 'eth_requestAccounts'
            });

            const provider = new ethers.BrowserProvider(window.ethereum);
            const signer = await provider.getSigner();
            const address = await signer.getAddress();

            const vestingContract = new ethers.Contract(
                VESTING_CONTRACT_ADDRESS,
                vestingABI,
                signer
            );

            setAccount(address);
            setContract(vestingContract);
            setIsConnected(true);
            
            addLog('✅ 钱包连接成功: ' + address);
            
            // 加载数据
            await loadAllData(vestingContract);

        } catch (error) {
            setError('钱包连接失败: ' + error.message);
            addLog('❌ 钱包连接失败: ' + error.message);
        }
    };

    // 加载所有数据
    const loadAllData = async (contractInstance = contract) => {
        if (!contractInstance) return;

        setIsLoading(true);
        try {
            // 并行加载数据
            const [roundsData, statsData] = await Promise.all([
                loadAllRounds(contractInstance),
                loadStatistics(contractInstance)
            ]);

            setAllRounds(roundsData);
            setStatistics(statsData);
            
            // 加载最近的价格检查历史
            await loadPriceHistory(contractInstance, statsData.totalUnlockedRounds + 1);
            
            addLog('📊 数据加载完成');

        } catch (error) {
            setError('数据加载失败: ' + error.message);
            addLog('❌ 数据加载失败: ' + error.message);
        } finally {
            setIsLoading(false);
        }
    };

    // 加载所有轮次信息
    const loadAllRounds = async (contractInstance) => {
        const totalRounds = await contractInstance.TOTAL_ROUNDS();
        const rounds = [];

        for (let i = 1; i <= Number(totalRounds); i++) {
            const roundInfo = await contractInstance.getRoundInfo(i);
            rounds.push({
                roundNumber: i,
                triggerPrice: parseFloat(ethers.formatUnits(roundInfo[0], 8)),
                priceConditionMet: roundInfo[1],
                unlocked: roundInfo[2],
                priceReachedTime: Number(roundInfo[3]),
                unlockTime: Number(roundInfo[4]),
                projectTokens: parseFloat(ethers.formatEther(roundInfo[5])),
                communityTokens: parseFloat(ethers.formatEther(roundInfo[6]))
            });
        }

        return rounds;
    };

    // 加载统计信息
    const loadStatistics = async (contractInstance) => {
        const stats = await contractInstance.getUnlockStatistics();
        return {
            totalUnlockedRounds: Number(stats[0]),
            totalUnlockedTokens: parseFloat(ethers.formatEther(stats[1])),
            totalProjectTokens: parseFloat(ethers.formatEther(stats[2])),
            totalCommunityTokens: parseFloat(ethers.formatEther(stats[3])),
            remainingTokens: parseFloat(ethers.formatEther(stats[4]))
        };
    };

    // 加载价格检查历史
    const loadPriceHistory = async (contractInstance, roundNumber) => {
        try {
            const history = await contractInstance.getPriceCheckHistory(roundNumber, 50);
            const formattedHistory = history.map(item => ({
                timestamp: Number(item[0]) * 1000,
                price: parseFloat(ethers.formatUnits(item[1], 8)),
                targetPrice: parseFloat(ethers.formatUnits(item[2], 8)),
                conditionMet: item[3]
            }));
            setPriceHistory(formattedHistory);
        } catch (error) {
            console.error('加载价格历史失败:', error);
        }
    };

    // 手动触发价格检查
    const triggerPriceCheck = async () => {
        if (!contract) return;

        setIsLoading(true);
        try {
            addLog('🔍 手动触发价格检查...');
            
            const tx = await contract.checkPriceCondition({
                gasLimit: 500000
            });
            
            addLog('📤 交易已发送: ' + tx.hash);
            
            const receipt = await tx.wait();
            addLog('✅ 价格检查完成, Gas使用: ' + receipt.gasUsed.toString());
            
            // 刷新数据
            await loadAllData();

        } catch (error) {
            setError('价格检查失败: ' + error.message);
            addLog('❌ 价格检查失败: ' + error.message);
        } finally {
            setIsLoading(false);
        }
    };

    // 添加日志
    const addLog = (message) => {
        const timestamp = new Date().toLocaleTimeString('zh-CN');
        setLogs(prev => [...prev.slice(-49), `[${timestamp}] ${message}`]);
    };

    // 获取轮次状态样式
    const getRoundStatusClass = (round) => {
        if (round.unlocked) return 'status-completed';
        if (round.priceConditionMet) return 'status-waiting';
        return 'status-pending';
    };

    // 获取轮次状态文本
    const getRoundStatusText = (round) => {
        if (round.unlocked) return '已解锁';
        if (round.priceConditionMet) return '维持期';
        return '待解锁';
    };

    useEffect(() => {
        // 检查是否已连接钱包
        if (window.ethereum && window.ethereum.selectedAddress) {
            connectWallet();
        }
    }, []);

    return (
        <div className="admin-panel">
            {/* 头部 */}
            <div className="panel-header">
                <h1>🔧 HAOX 管理面板</h1>
                <div className="header-controls">
                    {!isConnected ? (
                        <button className="connect-btn" onClick={connectWallet}>
                            🔗 连接钱包
                        </button>
                    ) : (
                        <div className="wallet-info">
                            <span className="wallet-address">
                                👤 {account.slice(0, 6)}...{account.slice(-4)}
                            </span>
                            <button className="refresh-btn" onClick={() => loadAllData()}>
                                🔄 刷新数据
                            </button>
                        </div>
                    )}
                </div>
            </div>

            {/* 错误提示 */}
            {error && (
                <div className="error-banner">
                    <span>⚠️ {error}</span>
                    <button onClick={() => setError('')}>✕</button>
                </div>
            )}

            {!isConnected ? (
                <div className="connect-prompt">
                    <div className="prompt-content">
                        <h2>请连接钱包</h2>
                        <p>需要连接MetaMask钱包来访问管理功能</p>
                        <button className="connect-btn-large" onClick={connectWallet}>
                            🔗 连接MetaMask
                        </button>
                    </div>
                </div>
            ) : (
                <div className="panel-content">
                    {/* 统计概览 */}
                    {statistics && (
                        <div className="stats-overview">
                            <div className="stat-card">
                                <div className="stat-label">已解锁轮次</div>
                                <div className="stat-value">{statistics.totalUnlockedRounds}/31</div>
                                <div className="stat-progress">
                                    <div 
                                        className="progress-bar"
                                        style={{ width: `${(statistics.totalUnlockedRounds / 31) * 100}%` }}
                                    />
                                </div>
                            </div>
                            
                            <div className="stat-card">
                                <div className="stat-label">已解锁代币</div>
                                <div className="stat-value">{statistics.totalUnlockedTokens.toLocaleString()}M</div>
                                <div className="stat-subtitle">HAOX</div>
                            </div>
                            
                            <div className="stat-card">
                                <div className="stat-label">剩余代币</div>
                                <div className="stat-value">{statistics.remainingTokens.toLocaleString()}M</div>
                                <div className="stat-subtitle">HAOX</div>
                            </div>
                            
                            <div className="stat-card">
                                <div className="stat-label">总体进度</div>
                                <div className="stat-value">{((statistics.totalUnlockedTokens / 5000) * 100).toFixed(1)}%</div>
                                <div className="stat-subtitle">完成度</div>
                            </div>
                        </div>
                    )}

                    {/* 操作控制 */}
                    <div className="control-panel">
                        <h3>🎛️ 操作控制</h3>
                        <div className="control-buttons">
                            <button 
                                className="control-btn primary"
                                onClick={triggerPriceCheck}
                                disabled={isLoading}
                            >
                                {isLoading ? '⏳ 处理中...' : '🔍 手动价格检查'}
                            </button>
                            
                            <button 
                                className="control-btn secondary"
                                onClick={() => loadAllData()}
                                disabled={isLoading}
                            >
                                🔄 刷新所有数据
                            </button>
                            
                            <button 
                                className="control-btn info"
                                onClick={() => window.open(`https://testnet.bscscan.com/address/${VESTING_CONTRACT_ADDRESS}`, '_blank')}
                            >
                                🔗 查看合约
                            </button>
                        </div>
                    </div>

                    {/* 轮次状态表格 */}
                    <div className="rounds-table">
                        <h3>📊 轮次状态</h3>
                        <div className="table-container">
                            <table>
                                <thead>
                                    <tr>
                                        <th>轮次</th>
                                        <th>触发价格</th>
                                        <th>状态</th>
                                        <th>项目代币</th>
                                        <th>社区代币</th>
                                        <th>解锁时间</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {allRounds.slice(0, 10).map(round => (
                                        <tr key={round.roundNumber} className={getRoundStatusClass(round)}>
                                            <td>第{round.roundNumber}轮</td>
                                            <td>${round.triggerPrice.toFixed(6)}</td>
                                            <td>
                                                <span className="status-badge">
                                                    {getRoundStatusText(round)}
                                                </span>
                                            </td>
                                            <td>{round.projectTokens.toLocaleString()}M</td>
                                            <td>{round.communityTokens.toLocaleString()}M</td>
                                            <td>
                                                {round.unlockTime > 0 
                                                    ? new Date(round.unlockTime * 1000).toLocaleString('zh-CN')
                                                    : '-'
                                                }
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    {/* 价格检查历史 */}
                    {priceHistory.length > 0 && (
                        <div className="price-history">
                            <h3>📈 价格检查历史</h3>
                            <div className="history-container">
                                {priceHistory.slice(-10).map((check, index) => (
                                    <div key={index} className={`history-item ${check.conditionMet ? 'met' : 'not-met'}`}>
                                        <div className="check-time">
                                            {new Date(check.timestamp).toLocaleString('zh-CN')}
                                        </div>
                                        <div className="check-prices">
                                            ${check.price.toFixed(8)} / ${check.targetPrice.toFixed(8)}
                                        </div>
                                        <div className="check-status">
                                            {check.conditionMet ? '✅' : '❌'}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}

                    {/* 操作日志 */}
                    <div className="logs-panel">
                        <h3>📝 操作日志</h3>
                        <div className="logs-container">
                            {logs.map((log, index) => (
                                <div key={index} className="log-entry">
                                    {log}
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default AdminPanel;
