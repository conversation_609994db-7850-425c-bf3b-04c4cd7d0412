import React, { useState, useMemo, useRef, useEffect } from 'react';

/**
 * HAOX价格图表组件
 * 显示24小时价格历史和解锁目标价格线
 */
const PriceChart = ({ data = [], currentPrice = 0, unlockProgress = null }) => {
    const [timeRange, setTimeRange] = useState('24h'); // 1h, 6h, 24h
    const [showTargetLine, setShowTargetLine] = useState(true);
    const canvasRef = useRef(null);
    const containerRef = useRef(null);

    // 过滤数据根据时间范围
    const filteredData = useMemo(() => {
        if (!data || data.length === 0) return [];
        
        const now = Date.now();
        let timeWindow;
        
        switch (timeRange) {
            case '1h': timeWindow = 60 * 60 * 1000; break;
            case '6h': timeWindow = 6 * 60 * 60 * 1000; break;
            case '24h': timeWindow = 24 * 60 * 60 * 1000; break;
            default: timeWindow = 24 * 60 * 60 * 1000;
        }
        
        return data.filter(item => now - item.timestamp <= timeWindow);
    }, [data, timeRange]);

    // 计算图表数据
    const chartData = useMemo(() => {
        if (filteredData.length === 0) return null;
        
        const prices = filteredData.map(item => item.price);
        const timestamps = filteredData.map(item => item.timestamp);
        
        const minPrice = Math.min(...prices);
        const maxPrice = Math.max(...prices);
        const priceRange = maxPrice - minPrice;
        
        // 添加一些padding
        const padding = priceRange * 0.1;
        const adjustedMin = Math.max(0, minPrice - padding);
        const adjustedMax = maxPrice + padding;
        
        return {
            prices,
            timestamps,
            minPrice: adjustedMin,
            maxPrice: adjustedMax,
            priceRange: adjustedMax - adjustedMin,
            minTime: Math.min(...timestamps),
            maxTime: Math.max(...timestamps),
            timeRange: Math.max(...timestamps) - Math.min(...timestamps)
        };
    }, [filteredData]);

    // 绘制图表
    const drawChart = () => {
        const canvas = canvasRef.current;
        const container = containerRef.current;
        
        if (!canvas || !container || !chartData) return;
        
        const ctx = canvas.getContext('2d');
        const rect = container.getBoundingClientRect();
        
        // 设置canvas尺寸
        canvas.width = rect.width * window.devicePixelRatio;
        canvas.height = 400 * window.devicePixelRatio;
        canvas.style.width = rect.width + 'px';
        canvas.style.height = '400px';
        
        ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
        
        const width = rect.width;
        const height = 400;
        const padding = { top: 20, right: 80, bottom: 40, left: 80 };
        const chartWidth = width - padding.left - padding.right;
        const chartHeight = height - padding.top - padding.bottom;
        
        // 清空画布
        ctx.clearRect(0, 0, width, height);
        
        // 绘制背景网格
        ctx.strokeStyle = '#f0f0f0';
        ctx.lineWidth = 1;
        
        // 水平网格线
        for (let i = 0; i <= 5; i++) {
            const y = padding.top + (chartHeight / 5) * i;
            ctx.beginPath();
            ctx.moveTo(padding.left, y);
            ctx.lineTo(padding.left + chartWidth, y);
            ctx.stroke();
        }
        
        // 垂直网格线
        for (let i = 0; i <= 6; i++) {
            const x = padding.left + (chartWidth / 6) * i;
            ctx.beginPath();
            ctx.moveTo(x, padding.top);
            ctx.lineTo(x, padding.top + chartHeight);
            ctx.stroke();
        }
        
        // 绘制价格线
        if (filteredData.length > 1) {
            ctx.strokeStyle = '#3b82f6';
            ctx.lineWidth = 2;
            ctx.beginPath();
            
            filteredData.forEach((point, index) => {
                const x = padding.left + ((point.timestamp - chartData.minTime) / chartData.timeRange) * chartWidth;
                const y = padding.top + chartHeight - ((point.price - chartData.minPrice) / chartData.priceRange) * chartHeight;
                
                if (index === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            });
            
            ctx.stroke();
            
            // 绘制数据点
            ctx.fillStyle = '#3b82f6';
            filteredData.forEach((point) => {
                const x = padding.left + ((point.timestamp - chartData.minTime) / chartData.timeRange) * chartWidth;
                const y = padding.top + chartHeight - ((point.price - chartData.minPrice) / chartData.priceRange) * chartHeight;
                
                ctx.beginPath();
                ctx.arc(x, y, 3, 0, 2 * Math.PI);
                ctx.fill();
            });
        }
        
        // 绘制目标价格线
        if (showTargetLine && unlockProgress && unlockProgress.nextRoundTriggerPrice) {
            const targetPrice = unlockProgress.nextRoundTriggerPrice;
            
            if (targetPrice >= chartData.minPrice && targetPrice <= chartData.maxPrice) {
                const y = padding.top + chartHeight - ((targetPrice - chartData.minPrice) / chartData.priceRange) * chartHeight;
                
                ctx.strokeStyle = '#ef4444';
                ctx.lineWidth = 2;
                ctx.setLineDash([5, 5]);
                
                ctx.beginPath();
                ctx.moveTo(padding.left, y);
                ctx.lineTo(padding.left + chartWidth, y);
                ctx.stroke();
                
                ctx.setLineDash([]);
                
                // 目标价格标签
                ctx.fillStyle = '#ef4444';
                ctx.font = '12px Arial';
                ctx.textAlign = 'left';
                ctx.fillText(`目标: $${targetPrice.toFixed(6)}`, padding.left + chartWidth + 5, y + 4);
            }
        }
        
        // 绘制当前价格线
        if (currentPrice > 0 && currentPrice >= chartData.minPrice && currentPrice <= chartData.maxPrice) {
            const y = padding.top + chartHeight - ((currentPrice - chartData.minPrice) / chartData.priceRange) * chartHeight;
            
            ctx.strokeStyle = '#10b981';
            ctx.lineWidth = 2;
            ctx.setLineDash([3, 3]);
            
            ctx.beginPath();
            ctx.moveTo(padding.left, y);
            ctx.lineTo(padding.left + chartWidth, y);
            ctx.stroke();
            
            ctx.setLineDash([]);
            
            // 当前价格标签
            ctx.fillStyle = '#10b981';
            ctx.font = '12px Arial';
            ctx.textAlign = 'left';
            ctx.fillText(`当前: $${currentPrice.toFixed(6)}`, padding.left + chartWidth + 5, y + 4);
        }
        
        // 绘制Y轴标签（价格）
        ctx.fillStyle = '#666';
        ctx.font = '11px Arial';
        ctx.textAlign = 'right';
        
        for (let i = 0; i <= 5; i++) {
            const price = chartData.minPrice + (chartData.priceRange / 5) * i;
            const y = padding.top + chartHeight - (chartHeight / 5) * i;
            ctx.fillText(`$${price.toFixed(6)}`, padding.left - 10, y + 4);
        }
        
        // 绘制X轴标签（时间）
        ctx.textAlign = 'center';
        
        for (let i = 0; i <= 6; i++) {
            const time = chartData.minTime + (chartData.timeRange / 6) * i;
            const x = padding.left + (chartWidth / 6) * i;
            const date = new Date(time);
            const timeStr = date.toLocaleTimeString('zh-CN', { 
                hour: '2-digit', 
                minute: '2-digit' 
            });
            ctx.fillText(timeStr, x, padding.top + chartHeight + 20);
        }
    };

    // 重绘图表
    useEffect(() => {
        drawChart();
    }, [chartData, currentPrice, unlockProgress, showTargetLine]);

    // 窗口大小变化时重绘
    useEffect(() => {
        const handleResize = () => {
            setTimeout(drawChart, 100);
        };
        
        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    // 计算统计数据
    const stats = useMemo(() => {
        if (!chartData || filteredData.length === 0) return null;
        
        const prices = filteredData.map(item => item.price);
        const first = prices[0];
        const last = prices[prices.length - 1];
        const change = last - first;
        const changePercent = (change / first) * 100;
        
        return {
            high: Math.max(...prices),
            low: Math.min(...prices),
            change,
            changePercent,
            dataPoints: prices.length
        };
    }, [chartData, filteredData]);

    if (!data || data.length === 0) {
        return (
            <div className="price-chart">
                <div className="chart-empty">
                    <div className="empty-icon">📈</div>
                    <div className="empty-text">暂无价格数据</div>
                    <div className="empty-subtitle">价格数据将在30秒后开始显示</div>
                </div>
            </div>
        );
    }

    return (
        <div className="price-chart">
            {/* 图表头部 */}
            <div className="chart-header">
                <div className="header-left">
                    <h3>HAOX 价格图表</h3>
                    {stats && (
                        <div className="chart-stats">
                            <span className="stat-item">
                                高: ${stats.high.toFixed(6)}
                            </span>
                            <span className="stat-item">
                                低: ${stats.low.toFixed(6)}
                            </span>
                            <span className={`stat-item ${stats.change >= 0 ? 'positive' : 'negative'}`}>
                                {stats.change >= 0 ? '+' : ''}{stats.changePercent.toFixed(2)}%
                            </span>
                            <span className="stat-item">
                                数据点: {stats.dataPoints}
                            </span>
                        </div>
                    )}
                </div>
                
                <div className="header-controls">
                    <div className="time-range-selector">
                        {['1h', '6h', '24h'].map(range => (
                            <button
                                key={range}
                                className={`range-btn ${timeRange === range ? 'active' : ''}`}
                                onClick={() => setTimeRange(range)}
                            >
                                {range}
                            </button>
                        ))}
                    </div>
                    
                    <button
                        className={`toggle-btn ${showTargetLine ? 'active' : ''}`}
                        onClick={() => setShowTargetLine(!showTargetLine)}
                    >
                        {showTargetLine ? '🎯 隐藏目标线' : '🎯 显示目标线'}
                    </button>
                </div>
            </div>

            {/* 图表容器 */}
            <div className="chart-container" ref={containerRef}>
                <canvas ref={canvasRef} className="price-canvas" />
                
                {/* 图例 */}
                <div className="chart-legend">
                    <div className="legend-item">
                        <div className="legend-color price-line"></div>
                        <span>HAOX价格</span>
                    </div>
                    <div className="legend-item">
                        <div className="legend-color current-line"></div>
                        <span>当前价格</span>
                    </div>
                    {showTargetLine && unlockProgress && (
                        <div className="legend-item">
                            <div className="legend-color target-line"></div>
                            <span>解锁目标</span>
                        </div>
                    )}
                </div>
            </div>

            {/* 图表信息 */}
            <div className="chart-info">
                <div className="info-item">
                    <span className="info-label">数据源:</span>
                    <span className="info-value">Chainlink + Binance</span>
                </div>
                <div className="info-item">
                    <span className="info-label">更新频率:</span>
                    <span className="info-value">30秒</span>
                </div>
                <div className="info-item">
                    <span className="info-label">时间范围:</span>
                    <span className="info-value">{timeRange}</span>
                </div>
                {unlockProgress && (
                    <div className="info-item">
                        <span className="info-label">下一轮目标:</span>
                        <span className="info-value">
                            第{unlockProgress.nextRoundNumber}轮 - ${unlockProgress.nextRoundTriggerPrice?.toFixed(6)}
                        </span>
                    </div>
                )}
            </div>
        </div>
    );
};

export default PriceChart;
