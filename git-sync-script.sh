#!/bin/bash

# SocioMint项目Git同步脚本
# 用于将最新的代码更改同步到GitHub仓库

echo "🚀 开始SocioMint项目Git同步..."

# 检查是否在Git仓库中
if [ ! -d ".git" ]; then
    echo "📁 初始化Git仓库..."
    git init
    echo "✅ Git仓库初始化完成"
fi

# 检查Git配置
echo "🔧 检查Git配置..."
if [ -z "$(git config user.name)" ]; then
    echo "⚠️  请设置Git用户名: git config user.name 'Your Name'"
fi

if [ -z "$(git config user.email)" ]; then
    echo "⚠️  请设置Git邮箱: git config user.email '<EMAIL>'"
fi

# 检查当前状态
echo "📊 检查项目状态..."
git status

# 添加所有文件到暂存区
echo "📦 添加文件到暂存区..."
git add .

# 创建提交
echo "💾 创建提交..."
git commit -m "feat: 🎉 SocioMint重大功能更新和优化

🔧 主要改进:
- 📄 白皮书系统全面升级 (31轮→19轮解锁机制)
- 🧭 奖励系统导航修复 (添加Header导航)
- 🎨 排行榜标签页视觉优化 (提升对比度和交互体验)
- ⏰ 阅读时间计算修复 (273分钟→30分钟)
- 🌐 中英文版本同步更新

📊 技术优化:
- 优化代币解锁机制数据准确性
- 增强UI组件交互反馈
- 改进响应式设计适配
- 提升颜色对比度符合无障碍标准

🎯 用户体验提升:
- 完善导航流程，消除用户困惑
- 优化视觉层次，提升专业感
- 增加动画效果，提升交互体验
- 确保跨设备一致性

🔍 修复问题:
- 奖励页面导航缺失问题
- 排行榜空白可点击区域问题
- 白皮书阅读时间计算错误
- 标签页文字对比度不足问题

📱 兼容性:
- 桌面端和移动端完全适配
- 现代浏览器硬件加速支持
- 无障碍访问性优化

Co-authored-by: Augment Agent <<EMAIL>>"

# 检查是否有远程仓库
echo "🔗 检查远程仓库..."
if git remote | grep -q "origin"; then
    echo "📤 推送到远程仓库..."
    git push origin main
    echo "✅ 代码已成功推送到GitHub!"
else
    echo "⚠️  未找到远程仓库配置"
    echo "请添加远程仓库: git remote add origin <your-github-repo-url>"
    echo "然后推送: git push -u origin main"
fi

echo "🎉 Git同步脚本执行完成!"
echo ""
echo "📋 下一步操作:"
echo "1. 如果是首次推送，请添加远程仓库:"
echo "   git remote add origin https://github.com/yourusername/SocioMint.git"
echo "2. 推送代码:"
echo "   git push -u origin main"
echo "3. 验证GitHub仓库是否更新成功"
