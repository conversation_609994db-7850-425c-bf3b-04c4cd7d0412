{"extends": "solhint:recommended", "rules": {"compiler-version": ["error", "^0.8.0"], "func-visibility": ["warn", {"ignoreConstructors": true}], "max-line-length": ["error", 120], "not-rely-on-time": "warn", "avoid-suicide": "error", "avoid-sha3": "warn", "no-unused-vars": "warn", "no-empty-blocks": "error", "check-send-result": "error", "avoid-call-value": "warn", "avoid-low-level-calls": "warn", "avoid-tx-origin": "error", "no-inline-assembly": "warn", "no-complex-fallback": "error", "payable-fallback": "error", "reason-string": ["warn", {"maxLength": 64}], "constructor-syntax": "error"}}