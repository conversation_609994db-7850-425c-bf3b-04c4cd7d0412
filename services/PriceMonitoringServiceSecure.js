import { ethers } from 'ethers';
import dotenv from 'dotenv';
import SecureKeyManager from '../utils/SecureKeyManager.js';

// 加载环境变量
dotenv.config({ path: '.env.local' });

/**
 * 安全版本的HAOX价格监控服务
 * 使用加密存储的私钥，支持多种密钥源
 */
class PriceMonitoringServiceSecure {
    constructor(options = {}) {
        this.isMonitoring = false;
        this.monitoringInterval = null;
        this.eventListeners = [];
        this.wallet = null;
        this.provider = null;
        
        // 初始化安全密钥管理器
        this.keyManager = new SecureKeyManager({
            keyPath: options.keyPath || '.keys',
            keyRotationDays: options.keyRotationDays || 90
        });
        
        // 监控配置
        this.config = {
            checkInterval: 30000, // 30秒检查一次
            retryAttempts: 3,
            retryDelay: 5000,
            gasLimit: 500000,
            gasPrice: ethers.parseUnits('10', 'gwei'),
            notificationCooldown: 300000, // 5分钟通知冷却期
            keyId: options.keyId || 'monitoring-service',
            ...options
        };
        
        // 状态跟踪
        this.lastNotification = {};
        this.errorCount = 0;
        this.lastSuccessfulCheck = Date.now();
        this.keyLastRotated = null;
        
        console.log('🔍 安全版价格监控服务初始化完成');
    }

    /**
     * 安全初始化 - 获取并解密私钥
     */
    async initializeSecurely() {
        try {
            console.log('🔐 正在安全初始化监控服务...');
            
            // 检查是否存在加密的私钥
            if (!this.keyManager.hasEncryptedKey(this.config.keyId)) {
                console.log('⚠️  未找到加密的私钥，需要首次设置');
                await this.setupInitialKey();
            }
            
            // 从多种源获取密码
            const password = await this.keyManager.getPasswordFromSources();
            
            // 解密私钥
            const privateKey = this.keyManager.decryptPrivateKey(password, this.config.keyId);
            
            // 初始化区块链连接
            this.provider = new ethers.JsonRpcProvider(process.env.NEXT_PUBLIC_BSC_RPC_URL);
            this.wallet = new ethers.Wallet(privateKey, this.provider);
            
            // 验证钱包地址
            const address = await this.wallet.getAddress();
            console.log('✅ 钱包地址验证:', address);
            
            // 检查余额
            const balance = await this.provider.getBalance(address);
            console.log('💰 钱包余额:', ethers.formatEther(balance), 'BNB');
            
            if (balance < ethers.parseEther('0.01')) {
                console.warn('⚠️  钱包余额较低，可能影响交易执行');
            }
            
            // 初始化合约实例
            this.vestingContract = new ethers.Contract(
                process.env.NEXT_PUBLIC_HAOX_VESTING_ADDRESS_V2_FIXED,
                this.getVestingABI(),
                this.wallet
            );
            
            this.priceOracleContract = new ethers.Contract(
                process.env.NEXT_PUBLIC_HAOX_PRICE_ORACLE_ADDRESS,
                this.getPriceOracleABI(),
                this.provider
            );
            
            // 清除内存中的敏感数据
            privateKey = null;
            password = null;
            
            console.log('✅ 安全初始化完成');
            return true;
        } catch (error) {
            console.error('❌ 安全初始化失败:', error.message);
            throw error;
        }
    }

    /**
     * 首次设置密钥
     */
    async setupInitialKey() {
        console.log('🔧 开始首次密钥设置...');
        
        // 获取明文私钥（仅用于迁移）
        const plainPrivateKey = process.env.DEPLOYER_PRIVATE_KEY;
        if (!plainPrivateKey) {
            throw new Error('DEPLOYER_PRIVATE_KEY not found in environment variables');
        }
        
        // 获取密码
        const password = await this.keyManager.getPasswordFromSources();
        
        // 生成主密钥
        this.keyManager.generateMasterKey(password);
        
        // 加密私钥
        this.keyManager.encryptPrivateKey(plainPrivateKey, password, this.config.keyId);
        
        console.log('✅ 首次密钥设置完成');
        console.log('⚠️  请从环境变量中删除明文私钥 DEPLOYER_PRIVATE_KEY');
    }

    /**
     * 启动监控
     */
    async startMonitoring() {
        if (this.isMonitoring) {
            console.log('⚠️  监控服务已在运行');
            return;
        }

        try {
            // 安全初始化
            await this.initializeSecurely();
            
            // 验证合约连接
            await this.validateContracts();
            
            this.isMonitoring = true;
            console.log('🚀 启动安全版价格监控服务...');
            
            // 设置事件监听器
            this.setupEventListeners();
            
            // 启动定期检查
            this.monitoringInterval = setInterval(async () => {
                await this.performPriceCheck();
            }, this.config.checkInterval);
            
            // 启动密钥轮换检查
            this.keyRotationInterval = setInterval(async () => {
                await this.checkKeyRotation();
            }, 24 * 60 * 60 * 1000); // 每天检查一次
            
            // 立即执行一次检查
            await this.performPriceCheck();
            
            console.log(`✅ 安全版价格监控服务已启动 (检查间隔: ${this.config.checkInterval / 1000}秒)`);
            
        } catch (error) {
            console.error('❌ 监控服务启动失败:', error);
            this.isMonitoring = false;
            throw error;
        }
    }

    /**
     * 检查密钥轮换
     */
    async checkKeyRotation() {
        try {
            const stats = this.keyManager.getKeyStatistics();
            const currentVersion = stats.keyVersions.find(v => v.version === stats.currentVersion);
            
            if (currentVersion && currentVersion.isExpired) {
                console.log('🔄 密钥已过期，开始自动轮换...');
                await this.rotateKeys();
            }
        } catch (error) {
            console.error('❌ 密钥轮换检查失败:', error);
        }
    }

    /**
     * 轮换密钥
     */
    async rotateKeys() {
        try {
            console.log('🔄 开始密钥轮换...');
            
            const oldPassword = await this.keyManager.getPasswordFromSources();
            
            // 生成新密码（在生产环境中应该从安全源获取）
            const newPassword = await this.generateNewPassword();
            
            // 执行密钥轮换
            const newVersion = this.keyManager.rotateKey(oldPassword, newPassword);
            
            // 重新初始化服务
            await this.initializeSecurely();
            
            this.keyLastRotated = new Date().toISOString();
            
            console.log(`✅ 密钥轮换完成，新版本: v${newVersion}`);
            
            // 发送通知
            await this.sendNotification({
                type: 'key_rotation',
                title: '🔄 密钥轮换完成',
                message: `监控服务密钥已成功轮换到版本 v${newVersion}`,
                data: { newVersion, timestamp: this.keyLastRotated }
            });
            
        } catch (error) {
            console.error('❌ 密钥轮换失败:', error);
            
            // 发送错误通知
            await this.sendNotification({
                type: 'key_rotation_error',
                title: '❌ 密钥轮换失败',
                message: `密钥轮换过程中发生错误: ${error.message}`,
                data: { error: error.message, timestamp: new Date().toISOString() }
            });
        }
    }

    /**
     * 生成新密码
     */
    async generateNewPassword() {
        // 在生产环境中，这应该从安全的密钥管理服务获取
        if (process.env.NODE_ENV === 'production') {
            // 从KMS或其他安全服务获取新密码
            return await this.keyManager.getPasswordFromSources();
        } else {
            // 开发环境生成随机密码
            const crypto = await import('crypto');
            return crypto.randomBytes(32).toString('hex');
        }
    }

    /**
     * 验证合约连接
     */
    async validateContracts() {
        try {
            // 检查Vesting合约
            const currentRound = await this.vestingContract.currentRound();
            console.log('✅ Vesting合约连接正常，当前轮次:', currentRound.toString());
            
            // 检查价格预言机
            const latestPrice = await this.priceOracleContract.getLatestPrice();
            console.log('✅ 价格预言机连接正常，当前价格: $', (Number(latestPrice) / 100000000).toFixed(8));
            
        } catch (error) {
            console.error('❌ 合约验证失败:', error);
            throw new Error(`合约连接失败: ${error.message}`);
        }
    }

    /**
     * 获取Vesting合约ABI
     */
    getVestingABI() {
        return [
            'function checkPriceCondition()',
            'function getUnlockProgress() view returns (uint256, uint256, uint256, bool, uint256, uint256)',
            'function currentRound() view returns (uint256)',
            'event PriceConditionMet(uint256 indexed roundNumber, uint256 price, uint256 timestamp)',
            'event RoundUnlocked(uint256 indexed roundNumber, uint256 triggerPrice, uint256 projectTokens, uint256 communityTokens, uint256 timestamp)',
            'event PriceConditionReset(uint256 indexed roundNumber, uint256 price, uint256 timestamp)',
            'event PriceChecked(uint256 indexed roundNumber, uint256 currentPrice, uint256 targetPrice, bool conditionMet, uint256 timestamp)'
        ];
    }

    /**
     * 获取价格预言机ABI
     */
    getPriceOracleABI() {
        return [
            'function getLatestPrice() view returns (uint256)',
            'function getLastUpdateTime() view returns (uint256)'
        ];
    }

    /**
     * 执行价格检查
     */
    async performPriceCheck() {
        try {
            console.log('🔍 执行价格检查...');
            
            // 获取当前状态
            const progress = await this.vestingContract.getUnlockProgress();
            const currentPrice = Number(progress[0]) / 100000000;
            const nextRound = Number(progress[1]);
            const targetPrice = Number(progress[2]) / 100000000;
            const conditionMet = progress[3];
            const timeRemaining = Number(progress[4]);
            
            console.log(`📊 当前状态: 第${nextRound}轮, 价格 $${currentPrice.toFixed(8)}, 目标 $${targetPrice.toFixed(8)}`);
            
            if (nextRound > 31) {
                console.log('🎉 所有轮次已完成，停止监控');
                this.stopMonitoring();
                return;
            }
            
            // 调用合约检查价格条件
            const tx = await this.vestingContract.checkPriceCondition({
                gasLimit: this.config.gasLimit,
                gasPrice: this.config.gasPrice
            });
            
            console.log('📤 价格检查交易已发送:', tx.hash);
            
            // 等待交易确认
            const receipt = await tx.wait();
            console.log('✅ 价格检查完成, Gas使用:', receipt.gasUsed.toString());
            
            // 重置错误计数
            this.errorCount = 0;
            this.lastSuccessfulCheck = Date.now();
            
        } catch (error) {
            this.errorCount++;
            console.error(`❌ 价格检查失败 (${this.errorCount}/${this.config.retryAttempts}):`, error.message);
            
            // 如果连续失败次数过多，发送警报
            if (this.errorCount >= this.config.retryAttempts) {
                await this.sendErrorNotification({
                    type: 'price_check_failed',
                    error: error.message,
                    errorCount: this.errorCount,
                    lastSuccess: this.lastSuccessfulCheck
                });
                
                // 重置错误计数，避免重复发送警报
                this.errorCount = 0;
            }
        }
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        console.log('📡 设置事件监听器...');
        
        // 监听价格条件达成事件
        this.vestingContract.on('PriceConditionMet', async (roundNumber, price, timestamp, event) => {
            await this.handlePriceConditionMet({
                roundNumber: Number(roundNumber),
                price: Number(price),
                timestamp: Number(timestamp),
                txHash: event.transactionHash
            });
        });

        // 监听轮次解锁事件
        this.vestingContract.on('RoundUnlocked', async (roundNumber, triggerPrice, projectTokens, communityTokens, timestamp, event) => {
            await this.handleRoundUnlocked({
                roundNumber: Number(roundNumber),
                triggerPrice: Number(triggerPrice),
                projectTokens: Number(projectTokens),
                communityTokens: Number(communityTokens),
                timestamp: Number(timestamp),
                txHash: event.transactionHash
            });
        });

        console.log('✅ 事件监听器设置完成');
    }

    /**
     * 处理价格条件达成事件
     */
    async handlePriceConditionMet(eventData) {
        const { roundNumber, price, timestamp, txHash } = eventData;
        const priceUSD = price / 100000000;
        
        console.log(`🎯 第${roundNumber}轮价格条件达成: $${priceUSD.toFixed(8)}`);
        
        await this.sendNotification({
            type: 'price_condition_met',
            title: '🎯 价格条件达成',
            message: `第${roundNumber}轮解锁价格条件已达成！\n价格: $${priceUSD.toFixed(8)}\n现在需要维持7天才能解锁。`,
            data: {
                roundNumber,
                price: priceUSD,
                timestamp: new Date(timestamp * 1000).toISOString(),
                txHash
            }
        });
    }

    /**
     * 处理轮次解锁事件
     */
    async handleRoundUnlocked(eventData) {
        const { roundNumber, triggerPrice, projectTokens, communityTokens, timestamp, txHash } = eventData;
        const priceUSD = triggerPrice / 100000000;
        const projectHAOX = ethers.formatEther(projectTokens);
        const communityHAOX = ethers.formatEther(communityTokens);
        
        console.log(`🎉 第${roundNumber}轮解锁完成! 价格: $${priceUSD.toFixed(8)}`);
        
        await this.sendNotification({
            type: 'round_unlocked',
            title: '🎉 轮次解锁完成',
            message: `第${roundNumber}轮解锁成功！\n` +
                    `触发价格: $${priceUSD.toFixed(8)}\n` +
                    `项目代币: ${parseFloat(projectHAOX).toLocaleString()} HAOX\n` +
                    `社区代币: ${parseFloat(communityHAOX).toLocaleString()} HAOX`,
            data: {
                roundNumber,
                triggerPrice: priceUSD,
                projectTokens: projectHAOX,
                communityTokens: communityHAOX,
                timestamp: new Date(timestamp * 1000).toISOString(),
                txHash
            }
        });
    }

    /**
     * 发送通知
     */
    async sendNotification(notification) {
        const { type, title, message, data } = notification;
        const now = Date.now();
        
        // 检查通知冷却期
        if (this.lastNotification[type] && 
            now - this.lastNotification[type] < this.config.notificationCooldown) {
            return;
        }
        
        this.lastNotification[type] = now;
        
        try {
            // 控制台输出
            console.log(`📢 ${title}`);
            console.log(message);
            
            // 这里可以集成其他通知方式
            // TODO: 实现Telegram、Discord、邮件通知
            
        } catch (error) {
            console.error('❌ 通知发送失败:', error);
        }
    }

    /**
     * 发送错误通知
     */
    async sendErrorNotification(error) {
        await this.sendNotification({
            type: 'error',
            title: '❌ 监控服务错误',
            message: `价格检查连续失败 ${error.errorCount} 次\n错误: ${error.error}\n上次成功: ${new Date(error.lastSuccess).toLocaleString('zh-CN')}`,
            data: error
        });
    }

    /**
     * 停止监控
     */
    stopMonitoring() {
        if (!this.isMonitoring) {
            console.log('⚠️  监控服务未在运行');
            return;
        }

        this.isMonitoring = false;
        
        // 清除定时器
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }
        
        if (this.keyRotationInterval) {
            clearInterval(this.keyRotationInterval);
            this.keyRotationInterval = null;
        }
        
        // 移除事件监听器
        if (this.vestingContract) {
            this.vestingContract.removeAllListeners();
        }
        
        console.log('⏹️  安全版价格监控服务已停止');
    }

    /**
     * 获取监控状态
     */
    getStatus() {
        const keyStats = this.keyManager.getKeyStatistics();
        
        return {
            isMonitoring: this.isMonitoring,
            errorCount: this.errorCount,
            lastSuccessfulCheck: this.lastSuccessfulCheck,
            checkInterval: this.config.checkInterval,
            contractAddress: process.env.NEXT_PUBLIC_HAOX_VESTING_ADDRESS_V2_FIXED,
            walletAddress: this.wallet ? this.wallet.address : null,
            keyManagement: {
                currentVersion: keyStats.currentVersion,
                totalEncryptedKeys: keyStats.totalEncryptedKeys,
                lastRotated: this.keyLastRotated
            }
        };
    }

    /**
     * 手动触发密钥轮换
     */
    async manualKeyRotation() {
        console.log('🔄 手动触发密钥轮换...');
        await this.rotateKeys();
    }

    /**
     * 验证密钥完整性
     */
    async validateKeys() {
        try {
            const password = await this.keyManager.getPasswordFromSources();
            return this.keyManager.validateKeyIntegrity(password);
        } catch (error) {
            console.error('❌ 密钥验证失败:', error);
            return false;
        }
    }
}

export default PriceMonitoringServiceSecure;
