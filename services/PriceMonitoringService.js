import { ethers } from 'ethers';
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config({ path: '.env.local' });

/**
 * HAOX价格监控服务
 * 自动检查价格条件，监听解锁事件，发送通知
 */
class PriceMonitoringService {
    constructor() {
        this.isMonitoring = false;
        this.monitoringInterval = null;
        this.eventListeners = [];
        
        // 初始化区块链连接
        this.provider = new ethers.JsonRpcProvider(process.env.NEXT_PUBLIC_BSC_RPC_URL);
        this.wallet = new ethers.Wallet(process.env.DEPLOYER_PRIVATE_KEY, this.provider);
        
        // 合约实例
        this.vestingContract = new ethers.Contract(
            process.env.NEXT_PUBLIC_HAOX_VESTING_ADDRESS_V2_FIXED,
            this.getVestingABI(),
            this.wallet
        );
        
        this.priceOracleContract = new ethers.Contract(
            process.env.NEXT_PUBLIC_HAOX_PRICE_ORACLE_ADDRESS,
            this.getPriceOracleABI(),
            this.provider
        );
        
        // 监控配置
        this.config = {
            checkInterval: 30000, // 30秒检查一次
            retryAttempts: 3,
            retryDelay: 5000,
            gasLimit: 500000,
            gasPrice: ethers.parseUnits('10', 'gwei'),
            notificationCooldown: 300000 // 5分钟通知冷却期
        };
        
        // 状态跟踪
        this.lastNotification = {};
        this.errorCount = 0;
        this.lastSuccessfulCheck = Date.now();
        
        console.log('🔍 价格监控服务初始化完成');
        console.log('合约地址:', process.env.NEXT_PUBLIC_HAOX_VESTING_ADDRESS_V2_FIXED);
    }

    // 获取Vesting合约ABI
    getVestingABI() {
        return [
            'function checkPriceCondition()',
            'function getUnlockProgress() view returns (uint256, uint256, uint256, bool, uint256, uint256)',
            'function currentRound() view returns (uint256)',
            'event PriceConditionMet(uint256 indexed roundNumber, uint256 price, uint256 timestamp)',
            'event RoundUnlocked(uint256 indexed roundNumber, uint256 triggerPrice, uint256 projectTokens, uint256 communityTokens, uint256 timestamp)',
            'event PriceConditionReset(uint256 indexed roundNumber, uint256 price, uint256 timestamp)',
            'event PriceChecked(uint256 indexed roundNumber, uint256 currentPrice, uint256 targetPrice, bool conditionMet, uint256 timestamp)'
        ];
    }

    // 获取价格预言机ABI
    getPriceOracleABI() {
        return [
            'function getLatestPrice() view returns (uint256)',
            'function getLastUpdateTime() view returns (uint256)'
        ];
    }

    // 启动监控
    async startMonitoring() {
        if (this.isMonitoring) {
            console.log('⚠️  监控服务已在运行');
            return;
        }

        try {
            // 验证合约连接
            await this.validateContracts();
            
            this.isMonitoring = true;
            console.log('🚀 启动价格监控服务...');
            
            // 设置事件监听器
            this.setupEventListeners();
            
            // 启动定期检查
            this.monitoringInterval = setInterval(async () => {
                await this.performPriceCheck();
            }, this.config.checkInterval);
            
            // 立即执行一次检查
            await this.performPriceCheck();
            
            console.log(`✅ 价格监控服务已启动 (检查间隔: ${this.config.checkInterval / 1000}秒)`);
            
        } catch (error) {
            console.error('❌ 监控服务启动失败:', error);
            this.isMonitoring = false;
            throw error;
        }
    }

    // 验证合约连接
    async validateContracts() {
        try {
            // 检查Vesting合约
            const currentRound = await this.vestingContract.currentRound();
            console.log('✅ Vesting合约连接正常，当前轮次:', currentRound.toString());
            
            // 检查价格预言机
            const latestPrice = await this.priceOracleContract.getLatestPrice();
            console.log('✅ 价格预言机连接正常，当前价格: $', (Number(latestPrice) / 100000000).toFixed(8));
            
            // 检查钱包余额
            const balance = await this.provider.getBalance(this.wallet.address);
            console.log('✅ 钱包余额:', ethers.formatEther(balance), 'BNB');
            
            if (balance < ethers.parseEther('0.01')) {
                console.warn('⚠️  钱包余额较低，可能影响交易执行');
            }
            
        } catch (error) {
            console.error('❌ 合约验证失败:', error);
            throw new Error(`合约连接失败: ${error.message}`);
        }
    }

    // 设置事件监听器
    setupEventListeners() {
        console.log('📡 设置事件监听器...');
        
        // 监听价格条件达成事件
        const priceConditionMetListener = this.vestingContract.on(
            'PriceConditionMet',
            async (roundNumber, price, timestamp, event) => {
                await this.handlePriceConditionMet({
                    roundNumber: Number(roundNumber),
                    price: Number(price),
                    timestamp: Number(timestamp),
                    txHash: event.transactionHash
                });
            }
        );

        // 监听轮次解锁事件
        const roundUnlockedListener = this.vestingContract.on(
            'RoundUnlocked',
            async (roundNumber, triggerPrice, projectTokens, communityTokens, timestamp, event) => {
                await this.handleRoundUnlocked({
                    roundNumber: Number(roundNumber),
                    triggerPrice: Number(triggerPrice),
                    projectTokens: Number(projectTokens),
                    communityTokens: Number(communityTokens),
                    timestamp: Number(timestamp),
                    txHash: event.transactionHash
                });
            }
        );

        // 监听价格条件重置事件
        const priceConditionResetListener = this.vestingContract.on(
            'PriceConditionReset',
            async (roundNumber, price, timestamp, event) => {
                await this.handlePriceConditionReset({
                    roundNumber: Number(roundNumber),
                    price: Number(price),
                    timestamp: Number(timestamp),
                    txHash: event.transactionHash
                });
            }
        );

        // 监听价格检查事件
        const priceCheckedListener = this.vestingContract.on(
            'PriceChecked',
            async (roundNumber, currentPrice, targetPrice, conditionMet, timestamp, event) => {
                await this.handlePriceChecked({
                    roundNumber: Number(roundNumber),
                    currentPrice: Number(currentPrice),
                    targetPrice: Number(targetPrice),
                    conditionMet,
                    timestamp: Number(timestamp),
                    txHash: event.transactionHash
                });
            }
        );

        this.eventListeners = [
            priceConditionMetListener,
            roundUnlockedListener,
            priceConditionResetListener,
            priceCheckedListener
        ];

        console.log('✅ 事件监听器设置完成');
    }

    // 执行价格检查
    async performPriceCheck() {
        try {
            console.log('🔍 执行价格检查...');
            
            // 获取当前状态
            const progress = await this.vestingContract.getUnlockProgress();
            const currentPrice = Number(progress[0]) / 100000000; // 转换为USD
            const nextRound = Number(progress[1]);
            const targetPrice = Number(progress[2]) / 100000000;
            const conditionMet = progress[3];
            const timeRemaining = Number(progress[4]);
            
            console.log(`📊 当前状态: 第${nextRound}轮, 价格 $${currentPrice.toFixed(8)}, 目标 $${targetPrice.toFixed(8)}`);
            
            if (nextRound > 31) {
                console.log('🎉 所有轮次已完成，停止监控');
                this.stopMonitoring();
                return;
            }
            
            // 调用合约检查价格条件
            const tx = await this.vestingContract.checkPriceCondition({
                gasLimit: this.config.gasLimit,
                gasPrice: this.config.gasPrice
            });
            
            console.log('📤 价格检查交易已发送:', tx.hash);
            
            // 等待交易确认
            const receipt = await tx.wait();
            console.log('✅ 价格检查完成, Gas使用:', receipt.gasUsed.toString());
            
            // 重置错误计数
            this.errorCount = 0;
            this.lastSuccessfulCheck = Date.now();
            
            // 发送状态通知
            await this.sendStatusNotification({
                type: 'price_check',
                roundNumber: nextRound,
                currentPrice,
                targetPrice,
                conditionMet,
                timeRemaining,
                txHash: tx.hash
            });
            
        } catch (error) {
            this.errorCount++;
            console.error(`❌ 价格检查失败 (${this.errorCount}/${this.config.retryAttempts}):`, error.message);
            
            // 如果连续失败次数过多，发送警报
            if (this.errorCount >= this.config.retryAttempts) {
                await this.sendErrorNotification({
                    type: 'price_check_failed',
                    error: error.message,
                    errorCount: this.errorCount,
                    lastSuccess: this.lastSuccessfulCheck
                });
                
                // 重置错误计数，避免重复发送警报
                this.errorCount = 0;
            }
        }
    }

    // 处理价格条件达成事件
    async handlePriceConditionMet(eventData) {
        const { roundNumber, price, timestamp, txHash } = eventData;
        const priceUSD = price / 100000000;
        
        console.log(`🎯 第${roundNumber}轮价格条件达成: $${priceUSD.toFixed(8)}`);
        
        await this.sendNotification({
            type: 'price_condition_met',
            title: '🎯 价格条件达成',
            message: `第${roundNumber}轮解锁价格条件已达成！\n价格: $${priceUSD.toFixed(8)}\n现在需要维持7天才能解锁。`,
            data: {
                roundNumber,
                price: priceUSD,
                timestamp: new Date(timestamp * 1000).toISOString(),
                txHash
            }
        });
    }

    // 处理轮次解锁事件
    async handleRoundUnlocked(eventData) {
        const { roundNumber, triggerPrice, projectTokens, communityTokens, timestamp, txHash } = eventData;
        const priceUSD = triggerPrice / 100000000;
        const projectHAOX = ethers.formatEther(projectTokens);
        const communityHAOX = ethers.formatEther(communityTokens);
        
        console.log(`🎉 第${roundNumber}轮解锁完成! 价格: $${priceUSD.toFixed(8)}`);
        
        await this.sendNotification({
            type: 'round_unlocked',
            title: '🎉 轮次解锁完成',
            message: `第${roundNumber}轮解锁成功！\n` +
                    `触发价格: $${priceUSD.toFixed(8)}\n` +
                    `项目代币: ${parseFloat(projectHAOX).toLocaleString()} HAOX\n` +
                    `社区代币: ${parseFloat(communityHAOX).toLocaleString()} HAOX`,
            data: {
                roundNumber,
                triggerPrice: priceUSD,
                projectTokens: projectHAOX,
                communityTokens: communityHAOX,
                timestamp: new Date(timestamp * 1000).toISOString(),
                txHash
            }
        });
    }

    // 处理价格条件重置事件
    async handlePriceConditionReset(eventData) {
        const { roundNumber, price, timestamp, txHash } = eventData;
        const priceUSD = price / 100000000;
        
        console.log(`⚠️  第${roundNumber}轮价格条件重置: $${priceUSD.toFixed(8)}`);
        
        await this.sendNotification({
            type: 'price_condition_reset',
            title: '⚠️ 价格条件重置',
            message: `第${roundNumber}轮价格跌破目标，维持期重置。\n当前价格: $${priceUSD.toFixed(8)}`,
            data: {
                roundNumber,
                price: priceUSD,
                timestamp: new Date(timestamp * 1000).toISOString(),
                txHash
            }
        });
    }

    // 处理价格检查事件
    async handlePriceChecked(eventData) {
        const { roundNumber, currentPrice, targetPrice, conditionMet, timestamp } = eventData;
        const currentUSD = currentPrice / 100000000;
        const targetUSD = targetPrice / 100000000;
        
        // 只记录日志，不发送通知（避免过于频繁）
        console.log(`📊 价格检查: R${roundNumber} $${currentUSD.toFixed(8)}/${targetUSD.toFixed(8)} ${conditionMet ? '✅' : '❌'}`);
    }

    // 发送通知
    async sendNotification(notification) {
        const { type, title, message, data } = notification;
        const now = Date.now();
        
        // 检查通知冷却期
        if (this.lastNotification[type] && 
            now - this.lastNotification[type] < this.config.notificationCooldown) {
            return;
        }
        
        this.lastNotification[type] = now;
        
        try {
            // 控制台输出
            console.log(`📢 ${title}`);
            console.log(message);
            
            // 这里可以集成其他通知方式：
            // - Telegram Bot
            // - Discord Webhook
            // - 邮件通知
            // - 短信通知
            
            // 示例：Telegram通知（需要配置）
            if (process.env.TELEGRAM_BOT_TOKEN && process.env.TELEGRAM_CHAT_ID) {
                await this.sendTelegramNotification(title, message, data);
            }
            
            // 示例：Discord通知（需要配置）
            if (process.env.DISCORD_WEBHOOK_URL) {
                await this.sendDiscordNotification(title, message, data);
            }
            
        } catch (error) {
            console.error('❌ 通知发送失败:', error);
        }
    }

    // 发送状态通知
    async sendStatusNotification(status) {
        // 定期状态报告（每小时一次）
        const hourlyKey = `status_${Math.floor(Date.now() / 3600000)}`;
        if (this.lastNotification[hourlyKey]) return;
        
        this.lastNotification[hourlyKey] = Date.now();
        
        const { roundNumber, currentPrice, targetPrice, conditionMet, timeRemaining } = status;
        
        console.log('📊 状态报告:');
        console.log(`   轮次: 第${roundNumber}轮`);
        console.log(`   价格: $${currentPrice.toFixed(8)} / $${targetPrice.toFixed(8)}`);
        console.log(`   条件: ${conditionMet ? '已达成' : '未达成'}`);
        if (conditionMet && timeRemaining > 0) {
            console.log(`   剩余: ${Math.floor(timeRemaining / 3600)}小时${Math.floor((timeRemaining % 3600) / 60)}分钟`);
        }
    }

    // 发送错误通知
    async sendErrorNotification(error) {
        await this.sendNotification({
            type: 'error',
            title: '❌ 监控服务错误',
            message: `价格检查连续失败 ${error.errorCount} 次\n错误: ${error.error}\n上次成功: ${new Date(error.lastSuccess).toLocaleString('zh-CN')}`,
            data: error
        });
    }

    // Telegram通知（示例）
    async sendTelegramNotification(title, message, data) {
        // 实现Telegram Bot通知
        // 需要安装 node-telegram-bot-api
    }

    // Discord通知（示例）
    async sendDiscordNotification(title, message, data) {
        // 实现Discord Webhook通知
    }

    // 停止监控
    stopMonitoring() {
        if (!this.isMonitoring) {
            console.log('⚠️  监控服务未在运行');
            return;
        }

        this.isMonitoring = false;
        
        // 清除定时器
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }
        
        // 移除事件监听器
        this.eventListeners.forEach(listener => {
            if (listener && typeof listener.removeAllListeners === 'function') {
                listener.removeAllListeners();
            }
        });
        this.eventListeners = [];
        
        console.log('⏹️  价格监控服务已停止');
    }

    // 获取监控状态
    getStatus() {
        return {
            isMonitoring: this.isMonitoring,
            errorCount: this.errorCount,
            lastSuccessfulCheck: this.lastSuccessfulCheck,
            checkInterval: this.config.checkInterval,
            contractAddress: process.env.NEXT_PUBLIC_HAOX_VESTING_ADDRESS_V2_FIXED,
            walletAddress: this.wallet.address
        };
    }
}

export default PriceMonitoringService;

// 如果直接运行此文件，启动监控服务
if (import.meta.url === `file://${process.argv[1]}`) {
    const monitor = new PriceMonitoringService();

    // 优雅关闭处理
    process.on('SIGINT', () => {
        console.log('\n🛑 收到停止信号，正在关闭监控服务...');
        monitor.stopMonitoring();
        process.exit(0);
    });

    process.on('SIGTERM', () => {
        console.log('\n🛑 收到终止信号，正在关闭监控服务...');
        monitor.stopMonitoring();
        process.exit(0);
    });

    // 启动监控
    monitor.startMonitoring().catch(error => {
        console.error('❌ 监控服务启动失败:', error);
        process.exit(1);
    });
}
