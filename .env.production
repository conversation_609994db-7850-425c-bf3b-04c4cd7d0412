# SocioMint 生产环境配置文件
# 请在部署前更新所有值

# 应用环境
NODE_ENV=production
NEXT_PUBLIC_APP_ENV=production
NEXT_PUBLIC_APP_VERSION=1.0.0

# 应用 URL (生产域名)
NEXT_PUBLIC_APP_URL=https://sociomint.app
NEXT_PUBLIC_API_URL=https://sociomint.app/api

# 私钥（生产环境需要更安全的密钥管理）
PRIVATE_KEY=YOUR_PRODUCTION_PRIVATE_KEY_HERE

# Supabase 配置（生产环境）
NEXT_PUBLIC_SUPABASE_URL=https://your-production-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_production_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_production_service_role_key_here

# 智能合约地址 (BSC测试网)
NEXT_PUBLIC_HAOX_TOKEN_ADDRESS=******************************************
NEXT_PUBLIC_HAOX_PRESALE_ADDRESS=******************************************
NEXT_PUBLIC_HAOX_INVITATION_ADDRESS=******************************************
NEXT_PUBLIC_HAOX_PRICE_ORACLE_ADDRESS=******************************************
NEXT_PUBLIC_HAOX_VESTING_ADDRESS=******************************************

# WalletConnect 配置
NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID=your_production_walletconnect_project_id

# 区块链配置
NEXT_PUBLIC_CHAIN_ID=97
NEXT_PUBLIC_NETWORK_NAME=BSC Testnet
NEXT_PUBLIC_RPC_URL=https://data-seed-prebsc-1-s1.binance.org:8545/
NEXT_PUBLIC_BLOCK_EXPLORER=https://testnet.bscscan.com

# Alchemy API Key（生产环境）
NEXT_PUBLIC_ALCHEMY_API_KEY=your_production_alchemy_api_key_here

# 社交平台 API 配置
TWITTER_API_KEY=your_production_twitter_api_key
TWITTER_API_SECRET=your_production_twitter_api_secret
TWITTER_BEARER_TOKEN=your_production_twitter_bearer_token

DISCORD_BOT_TOKEN=your_production_discord_bot_token
DISCORD_CLIENT_ID=your_production_discord_client_id
DISCORD_CLIENT_SECRET=your_production_discord_client_secret

TELEGRAM_BOT_TOKEN=your_production_telegram_bot_token
TELEGRAM_BOT_USERNAME=sociomint_bot
NEXT_PUBLIC_TELEGRAM_BOT_USERNAME=sociomint_bot
TELEGRAM_WEBHOOK_URL=https://sociomint.app/api/telegram/webhook

# 支付配置
ALIPAY_APP_ID=your_production_alipay_app_id
ALIPAY_PRIVATE_KEY=your_production_alipay_private_key
ALIPAY_PUBLIC_KEY=your_production_alipay_public_key

WECHAT_PAY_APP_ID=your_production_wechat_app_id
WECHAT_PAY_MCH_ID=your_production_wechat_mch_id
WECHAT_PAY_API_KEY=your_production_wechat_api_key

# 监控和分析
SENTRY_DSN=your_production_sentry_dsn
GOOGLE_ANALYTICS_ID=your_production_ga_id
MIXPANEL_TOKEN=your_production_mixpanel_token

# 邮件服务
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_production_email_password

# Redis 缓存（生产环境推荐）
REDIS_URL=redis://your-production-redis-url:6379
REDIS_PASSWORD=your_production_redis_password

# 安全配置
JWT_SECRET=7faa445517bb658e3286c4c8a8a030dc6abceb8caf7fe52409f50b4717615443dd1ad02edd74541069365502dd8ae6020215e3d3e857f1cee7bf5e7b2e1992fa
NEXTAUTH_SECRET=af907d9c8122b55084f848487b34a6bf264f230f71bf846444983502e15ef9d465f071fc7b3f9da4d91df1f1739999ecb6ab82a2fb92b327d3a10a4271ce85dc
NEXTAUTH_URL=https://sociomint.app
ENCRYPTION_KEY=1f0dbbcbe6b2231c8883a1c242f305bafb84131141c1c05738e69a103eb18801
KMS_MASTER_KEY=005f408fe761a31679c6c468ef8dd2342930c9ca856aba4adcd00055a960bf34
CORS_ORIGIN=https://sociomint.app,https://www.sociomint.app

# 文件存储
AWS_ACCESS_KEY_ID=your_production_aws_access_key
AWS_SECRET_ACCESS_KEY=your_production_aws_secret_key
AWS_S3_BUCKET=sociomint-production-assets
AWS_REGION=us-east-1

# 数据库备份
DATABASE_BACKUP_URL=your_production_backup_database_url

# 限流配置
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_WINDOW_MS=900000

# 日志配置
LOG_LEVEL=info
LOG_FILE_PATH=/var/log/sociomint/app.log

# 健康检查
HEALTH_CHECK_INTERVAL=30000
HEALTH_CHECK_TIMEOUT=5000

# 特性开关
FEATURE_SOCIAL_TASKS=true
FEATURE_MERCHANT_APPLICATIONS=true
FEATURE_P2P_TRADING=true
FEATURE_STAKING=false
FEATURE_NFT_MARKETPLACE=false
