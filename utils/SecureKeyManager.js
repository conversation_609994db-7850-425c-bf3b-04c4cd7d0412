import crypto from 'crypto';
import fs from 'fs';
import path from 'path';
import readline from 'readline';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * 安全密钥管理器
 * 实现AES-256-GCM加密算法，支持多种密钥源
 * 提供密钥版本管理和安全轮换机制
 */
class SecureKeyManager {
    constructor(options = {}) {
        this.algorithm = 'aes-256-gcm';
        this.keyDerivationAlgorithm = 'scrypt';
        this.keyLength = 32; // 256 bits
        this.ivLength = 16; // 128 bits
        this.saltLength = 32; // 256 bits
        this.tagLength = 16; // 128 bits
        
        // 配置选项
        this.options = {
            keyPath: options.keyPath || path.join(process.cwd(), '.keys'),
            keyRotationDays: options.keyRotationDays || 90,
            maxKeyVersions: options.maxKeyVersions || 5,
            scryptOptions: {
                N: 16384, // CPU/memory cost parameter
                r: 8,     // block size parameter
                p: 1      // parallelization parameter
            },
            ...options
        };
        
        this.ensureKeyDirectory();
    }

    /**
     * 确保密钥目录存在并设置正确权限
     */
    ensureKeyDirectory() {
        if (!fs.existsSync(this.options.keyPath)) {
            fs.mkdirSync(this.options.keyPath, { 
                mode: 0o700, 
                recursive: true 
            });
        }
        
        // 确保目录权限正确
        fs.chmodSync(this.options.keyPath, 0o700);
    }

    /**
     * 生成主密钥
     * @param {string} password - 主密码
     * @param {number} version - 密钥版本（可选）
     * @returns {Buffer} 生成的主密钥
     */
    generateMasterKey(password, version = null) {
        if (!password || password.length < 12) {
            throw new Error('Password must be at least 12 characters long');
        }

        const salt = crypto.randomBytes(this.saltLength);
        const currentVersion = version || this.getNextKeyVersion();
        
        const key = crypto.scryptSync(
            password, 
            salt, 
            this.keyLength,
            this.options.scryptOptions
        );
        
        const keyMetadata = {
            version: currentVersion,
            algorithm: this.keyDerivationAlgorithm,
            salt: salt.toString('hex'),
            keyLength: this.keyLength,
            scryptOptions: this.options.scryptOptions,
            createdAt: new Date().toISOString(),
            expiresAt: new Date(Date.now() + this.options.keyRotationDays * 24 * 60 * 60 * 1000).toISOString()
        };
        
        const keyFilePath = path.join(this.options.keyPath, `master_v${currentVersion}.key`);
        fs.writeFileSync(
            keyFilePath,
            JSON.stringify(keyMetadata, null, 2),
            { mode: 0o600 }
        );
        
        // 更新当前版本指针
        this.updateCurrentVersion(currentVersion);
        
        console.log(`✅ Master key v${currentVersion} generated successfully`);
        return key;
    }

    /**
     * 获取主密钥
     * @param {string} password - 主密码
     * @param {number} version - 密钥版本（可选，默认使用当前版本）
     * @returns {Buffer} 主密钥
     */
    getMasterKey(password, version = null) {
        const keyVersion = version || this.getCurrentVersion();
        const keyFilePath = path.join(this.options.keyPath, `master_v${keyVersion}.key`);
        
        if (!fs.existsSync(keyFilePath)) {
            throw new Error(`Master key version ${keyVersion} not found`);
        }
        
        const keyMetadata = JSON.parse(fs.readFileSync(keyFilePath, 'utf8'));
        
        // 检查密钥是否过期
        if (new Date() > new Date(keyMetadata.expiresAt)) {
            console.warn(`⚠️  Master key v${keyVersion} has expired`);
        }
        
        const salt = Buffer.from(keyMetadata.salt, 'hex');
        const key = crypto.scryptSync(
            password,
            salt,
            keyMetadata.keyLength,
            keyMetadata.scryptOptions
        );
        
        return key;
    }

    /**
     * 加密私钥
     * @param {string} privateKey - 要加密的私钥
     * @param {string} password - 主密码
     * @param {string} keyId - 密钥标识符
     * @returns {boolean} 加密成功返回true
     */
    encryptPrivateKey(privateKey, password, keyId = 'default') {
        if (!privateKey || !privateKey.startsWith('0x')) {
            throw new Error('Invalid private key format');
        }
        
        const masterKey = this.getMasterKey(password);
        const iv = crypto.randomBytes(this.ivLength);
        
        const cipher = crypto.createCipher(this.algorithm, masterKey, iv);
        
        let encrypted = cipher.update(privateKey, 'utf8', 'hex');
        encrypted += cipher.final('hex');
        
        const authTag = cipher.getAuthTag();
        
        const encryptedData = {
            keyId,
            algorithm: this.algorithm,
            encrypted,
            iv: iv.toString('hex'),
            authTag: authTag.toString('hex'),
            keyVersion: this.getCurrentVersion(),
            createdAt: new Date().toISOString(),
            lastAccessed: new Date().toISOString()
        };
        
        const encryptedFilePath = path.join(this.options.keyPath, `${keyId}.key.enc`);
        fs.writeFileSync(
            encryptedFilePath,
            JSON.stringify(encryptedData, null, 2),
            { mode: 0o600 }
        );
        
        console.log(`✅ Private key '${keyId}' encrypted successfully`);
        return true;
    }

    /**
     * 解密私钥
     * @param {string} password - 主密码
     * @param {string} keyId - 密钥标识符
     * @returns {string} 解密后的私钥
     */
    decryptPrivateKey(password, keyId = 'default') {
        const encryptedFilePath = path.join(this.options.keyPath, `${keyId}.key.enc`);
        
        if (!fs.existsSync(encryptedFilePath)) {
            throw new Error(`Encrypted key '${keyId}' not found`);
        }
        
        const encryptedData = JSON.parse(fs.readFileSync(encryptedFilePath, 'utf8'));
        
        // 更新最后访问时间
        encryptedData.lastAccessed = new Date().toISOString();
        fs.writeFileSync(
            encryptedFilePath,
            JSON.stringify(encryptedData, null, 2),
            { mode: 0o600 }
        );
        
        const masterKey = this.getMasterKey(password, encryptedData.keyVersion);
        const iv = Buffer.from(encryptedData.iv, 'hex');
        const authTag = Buffer.from(encryptedData.authTag, 'hex');
        
        const decipher = crypto.createDecipher(this.algorithm, masterKey, iv);
        decipher.setAuthTag(authTag);
        
        let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
        decrypted += decipher.final('utf8');
        
        return decrypted;
    }

    /**
     * 轮换密钥
     * @param {string} oldPassword - 旧密码
     * @param {string} newPassword - 新密码
     * @returns {number} 新密钥版本号
     */
    rotateKey(oldPassword, newPassword) {
        console.log('🔄 Starting key rotation...');
        
        // 获取所有加密的私钥
        const encryptedKeys = this.listEncryptedKeys();
        const decryptedKeys = {};
        
        // 使用旧密码解密所有私钥
        for (const keyId of encryptedKeys) {
            try {
                decryptedKeys[keyId] = this.decryptPrivateKey(oldPassword, keyId);
                console.log(`✅ Decrypted key: ${keyId}`);
            } catch (error) {
                console.error(`❌ Failed to decrypt key ${keyId}:`, error.message);
                throw error;
            }
        }
        
        // 生成新的主密钥
        const newVersion = this.generateMasterKey(newPassword);
        
        // 使用新密码重新加密所有私钥
        for (const [keyId, privateKey] of Object.entries(decryptedKeys)) {
            this.encryptPrivateKey(privateKey, newPassword, keyId);
            console.log(`✅ Re-encrypted key: ${keyId}`);
        }
        
        // 清理旧版本密钥（保留最近几个版本）
        this.cleanupOldKeys();
        
        console.log('✅ Key rotation completed successfully');
        return this.getCurrentVersion();
    }

    /**
     * 获取下一个密钥版本号
     * @returns {number} 下一个版本号
     */
    getNextKeyVersion() {
        const currentVersion = this.getCurrentVersion();
        return currentVersion + 1;
    }

    /**
     * 获取当前密钥版本号
     * @returns {number} 当前版本号
     */
    getCurrentVersion() {
        const versionFile = path.join(this.options.keyPath, 'current_version');
        
        if (!fs.existsSync(versionFile)) {
            return 1;
        }
        
        const version = parseInt(fs.readFileSync(versionFile, 'utf8').trim());
        return isNaN(version) ? 1 : version;
    }

    /**
     * 更新当前版本号
     * @param {number} version - 新版本号
     */
    updateCurrentVersion(version) {
        const versionFile = path.join(this.options.keyPath, 'current_version');
        fs.writeFileSync(versionFile, version.toString(), { mode: 0o600 });
    }

    /**
     * 列出所有加密的私钥
     * @returns {string[]} 密钥ID列表
     */
    listEncryptedKeys() {
        const files = fs.readdirSync(this.options.keyPath);
        return files
            .filter(file => file.endsWith('.key.enc'))
            .map(file => file.replace('.key.enc', ''));
    }

    /**
     * 检查是否存在加密的私钥
     * @param {string} keyId - 密钥标识符
     * @returns {boolean} 存在返回true
     */
    hasEncryptedKey(keyId = 'default') {
        const encryptedFilePath = path.join(this.options.keyPath, `${keyId}.key.enc`);
        return fs.existsSync(encryptedFilePath);
    }

    /**
     * 清理旧版本密钥
     */
    cleanupOldKeys() {
        const files = fs.readdirSync(this.options.keyPath);
        const keyFiles = files.filter(file => file.startsWith('master_v') && file.endsWith('.key'));
        
        if (keyFiles.length <= this.options.maxKeyVersions) {
            return;
        }
        
        // 按版本号排序
        const sortedFiles = keyFiles.sort((a, b) => {
            const versionA = parseInt(a.match(/master_v(\d+)\.key/)[1]);
            const versionB = parseInt(b.match(/master_v(\d+)\.key/)[1]);
            return versionA - versionB;
        });
        
        // 删除最旧的密钥文件
        const filesToDelete = sortedFiles.slice(0, sortedFiles.length - this.options.maxKeyVersions);
        
        for (const file of filesToDelete) {
            const filePath = path.join(this.options.keyPath, file);
            fs.unlinkSync(filePath);
            console.log(`🗑️  Deleted old key file: ${file}`);
        }
    }

    /**
     * 获取密钥统计信息
     * @returns {object} 统计信息
     */
    getKeyStatistics() {
        const encryptedKeys = this.listEncryptedKeys();
        const currentVersion = this.getCurrentVersion();
        
        const stats = {
            currentVersion,
            totalEncryptedKeys: encryptedKeys.length,
            encryptedKeys,
            keyVersions: []
        };
        
        // 获取所有版本信息
        const files = fs.readdirSync(this.options.keyPath);
        const keyFiles = files.filter(file => file.startsWith('master_v') && file.endsWith('.key'));
        
        for (const file of keyFiles) {
            const filePath = path.join(this.options.keyPath, file);
            const metadata = JSON.parse(fs.readFileSync(filePath, 'utf8'));
            stats.keyVersions.push({
                version: metadata.version,
                createdAt: metadata.createdAt,
                expiresAt: metadata.expiresAt,
                isExpired: new Date() > new Date(metadata.expiresAt)
            });
        }
        
        return stats;
    }

    /**
     * 验证密钥完整性
     * @param {string} password - 主密码
     * @returns {boolean} 验证通过返回true
     */
    validateKeyIntegrity(password) {
        try {
            const encryptedKeys = this.listEncryptedKeys();
            
            for (const keyId of encryptedKeys) {
                const decrypted = this.decryptPrivateKey(password, keyId);
                
                // 验证私钥格式
                if (!decrypted.startsWith('0x') || decrypted.length !== 66) {
                    throw new Error(`Invalid private key format for ${keyId}`);
                }
            }
            
            console.log('✅ Key integrity validation passed');
            return true;
        } catch (error) {
            console.error('❌ Key integrity validation failed:', error.message);
            return false;
        }
    }

    /**
     * 交互式密码输入
     * @param {string} prompt - 提示信息
     * @returns {Promise<string>} 输入的密码
     */
    async promptPassword(prompt = 'Enter password: ') {
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
        
        return new Promise((resolve) => {
            rl.question(prompt, (password) => {
                rl.close();
                resolve(password);
            });
        });
    }
}

    /**
     * 从多种源获取密码
     * @returns {Promise<string>} 密码
     */
    async getPasswordFromSources() {
        // 方案1: 从环境变量获取
        if (process.env.MASTER_PASSWORD) {
            console.log('🔑 Using password from environment variable');
            return process.env.MASTER_PASSWORD;
        }

        // 方案2: 从AWS KMS获取
        if (process.env.AWS_KMS_KEY_ID) {
            console.log('🔑 Using password from AWS KMS');
            return await this.getPasswordFromAWSKMS();
        }

        // 方案3: 从Azure Key Vault获取
        if (process.env.AZURE_KEY_VAULT_URL) {
            console.log('🔑 Using password from Azure Key Vault');
            return await this.getPasswordFromAzureKV();
        }

        // 方案4: 交互式输入（开发环境）
        if (process.env.NODE_ENV === 'development') {
            console.log('🔑 Using interactive password input');
            return await this.promptPassword('Enter master password: ');
        }

        throw new Error('No password source available. Please set MASTER_PASSWORD or configure KMS.');
    }

    /**
     * 从AWS KMS获取密码
     * @returns {Promise<string>} 密码
     */
    async getPasswordFromAWSKMS() {
        try {
            // 需要安装 @aws-sdk/client-kms
            const { KMSClient, DecryptCommand } = await import('@aws-sdk/client-kms');

            const client = new KMSClient({
                region: process.env.AWS_REGION || 'us-east-1'
            });

            const encryptedPassword = process.env.AWS_KMS_ENCRYPTED_PASSWORD;
            if (!encryptedPassword) {
                throw new Error('AWS_KMS_ENCRYPTED_PASSWORD not found');
            }

            const command = new DecryptCommand({
                CiphertextBlob: Buffer.from(encryptedPassword, 'base64')
            });

            const response = await client.send(command);
            return Buffer.from(response.Plaintext).toString('utf8');
        } catch (error) {
            console.error('❌ Failed to get password from AWS KMS:', error.message);
            throw error;
        }
    }

    /**
     * 从Azure Key Vault获取密码
     * @returns {Promise<string>} 密码
     */
    async getPasswordFromAzureKV() {
        try {
            // 需要安装 @azure/keyvault-secrets @azure/identity
            const { SecretClient } = await import('@azure/keyvault-secrets');
            const { DefaultAzureCredential } = await import('@azure/identity');

            const vaultUrl = process.env.AZURE_KEY_VAULT_URL;
            const secretName = process.env.AZURE_SECRET_NAME || 'master-password';

            const credential = new DefaultAzureCredential();
            const client = new SecretClient(vaultUrl, credential);

            const secret = await client.getSecret(secretName);
            return secret.value;
        } catch (error) {
            console.error('❌ Failed to get password from Azure Key Vault:', error.message);
            throw error;
        }
    }

    /**
     * 备份密钥到安全位置
     * @param {string} backupPath - 备份路径
     * @returns {boolean} 备份成功返回true
     */
    backupKeys(backupPath) {
        try {
            if (!fs.existsSync(backupPath)) {
                fs.mkdirSync(backupPath, { mode: 0o700, recursive: true });
            }

            const files = fs.readdirSync(this.options.keyPath);
            let backedUpFiles = 0;

            for (const file of files) {
                const sourcePath = path.join(this.options.keyPath, file);
                const destPath = path.join(backupPath, file);

                fs.copyFileSync(sourcePath, destPath);
                fs.chmodSync(destPath, 0o600);
                backedUpFiles++;
            }

            // 创建备份元数据
            const backupMetadata = {
                timestamp: new Date().toISOString(),
                filesCount: backedUpFiles,
                sourceDirectory: this.options.keyPath,
                backupDirectory: backupPath
            };

            fs.writeFileSync(
                path.join(backupPath, 'backup_metadata.json'),
                JSON.stringify(backupMetadata, null, 2),
                { mode: 0o600 }
            );

            console.log(`✅ Backed up ${backedUpFiles} key files to ${backupPath}`);
            return true;
        } catch (error) {
            console.error('❌ Backup failed:', error.message);
            return false;
        }
    }

    /**
     * 从备份恢复密钥
     * @param {string} backupPath - 备份路径
     * @returns {boolean} 恢复成功返回true
     */
    restoreKeys(backupPath) {
        try {
            const metadataPath = path.join(backupPath, 'backup_metadata.json');
            if (!fs.existsSync(metadataPath)) {
                throw new Error('Backup metadata not found');
            }

            const metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf8'));
            console.log(`🔄 Restoring backup from ${metadata.timestamp}`);

            const files = fs.readdirSync(backupPath);
            let restoredFiles = 0;

            for (const file of files) {
                if (file === 'backup_metadata.json') continue;

                const sourcePath = path.join(backupPath, file);
                const destPath = path.join(this.options.keyPath, file);

                fs.copyFileSync(sourcePath, destPath);
                fs.chmodSync(destPath, 0o600);
                restoredFiles++;
            }

            console.log(`✅ Restored ${restoredFiles} key files from backup`);
            return true;
        } catch (error) {
            console.error('❌ Restore failed:', error.message);
            return false;
        }
    }
}

export default SecureKeyManager;
